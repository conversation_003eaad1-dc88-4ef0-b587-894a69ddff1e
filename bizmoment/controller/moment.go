package controller

import (
	"context"

	"new-gitlab.xunlei.cn/vcproject/backends/baselib/logger"

	"new-gitlab.xunlei.cn/vcproject/backends/baselib/errcode"
	"new-gitlab.xunlei.cn/vcproject/backends/baselib/xlaccount"

	"new-gitlab.xunlei.cn/vcproject/backends/proto/api/bizmoment"
	"new-gitlab.xunlei.cn/vcproject/backends/proto/api/svcmoment"
	"new-gitlab.xunlei.cn/vcproject/backends/proto/svcmgr"
)

var (
	_ bizmoment.SServer = new(Controller)
)

type Controller struct {
	svcMomentClient svcmoment.SClient
}

func NewController() *Controller {
	svcmgr.InitServices()
	return &Controller{
		svcMomentClient: svcmgr.MomentClient(),
	}
}

// GetCharacterList 获取角色列表
func (c *Controller) GetCharacterList(ctx context.Context, req *bizmoment.GetCharacterListReq) (*bizmoment.GetCharacterListResp, error) {
	resp := &bizmoment.GetCharacterListResp{}

	authInfo := xlaccount.GetAuthInfo(ctx)
	svcReq := &svcmoment.GetCharacterListReq{
		Page:     req.GetPage(),
		PageSize: req.GetPageSize(),
		UserId:   authInfo.UserId,
	}

	svcResp, err := c.svcMomentClient.GetCharacterList(ctx, svcReq)
	if err != nil {
		errcode.FromError(err).SetTo(resp)
		return resp, nil
	}

	resp.Data = svcResp.Data
	errcode.ErrOK.SetTo(resp)
	return resp, nil
}

// PublishMoment 发布朋友圈
func (c *Controller) PublishMoment(ctx context.Context, req *bizmoment.PublishMomentReq) (*bizmoment.PublishMomentResp, error) {
	resp := &bizmoment.PublishMomentResp{}

	authInfo := xlaccount.GetAuthInfo(ctx)
	svcReq := &svcmoment.PublishMomentReq{
		UserId:         authInfo.UserId,
		CharacterId:    req.GetCharacterId(),
		OriginAudioUrl: req.GetOriginAudioUrl(),
		VoiceAudioUrl:  req.GetVoiceAudioUrl(),
		Content:        req.GetContent(),
		Duration:       req.GetDuration(),
	}

	svcResp, err := c.svcMomentClient.PublishMoment(ctx, svcReq)
	if err != nil {
		errcode.FromError(err).SetTo(resp)
		return resp, nil
	}

	resp.Data = svcResp.Data
	errcode.ErrOK.SetTo(resp)
	return resp, nil
}

// GetMyMoments 获取我发布的朋友圈列表
func (c *Controller) GetMyMoments(ctx context.Context, req *bizmoment.GetMyMomentsReq) (*bizmoment.GetMyMomentsResp, error) {
	resp := &bizmoment.GetMyMomentsResp{}

	authInfo := xlaccount.GetAuthInfo(ctx)
	svcReq := &svcmoment.GetMyMomentsReq{
		UserId:   authInfo.UserId,
		Page:     req.GetPage(),
		PageSize: req.GetPageSize(),
	}

	svcResp, err := c.svcMomentClient.GetMyMoments(ctx, svcReq)
	if err != nil {
		logger.Errorf("GetMyMoments error: %v", err)
		errcode.FromError(err).SetTo(resp)
		return resp, nil
	}

	resp.Data = svcResp.Data
	errcode.ErrOK.SetTo(resp)
	return resp, nil
}

// GetMomentsByIds 通过ID列表获取朋友圈
func (c *Controller) GetMomentsByIds(ctx context.Context, req *bizmoment.GetMomentsByIdsReq) (*bizmoment.GetMomentsByIdsResp, error) {
	resp := &bizmoment.GetMomentsByIdsResp{}

	authInfo := xlaccount.GetAuthInfo(ctx)
	svcReq := &svcmoment.GetMomentsByIdsReq{
		MomentIds: req.GetMomentIds(),
		UserId:    authInfo.UserId,
	}

	svcResp, err := c.svcMomentClient.GetMomentsByIds(ctx, svcReq)
	if err != nil {
		logger.Errorf("GetMomentsByIds error: %v", err)
		errcode.FromError(err).SetTo(resp)
		return resp, nil
	}

	resp.Data = svcResp.Data
	errcode.ErrOK.SetTo(resp)
	return resp, nil
}

// LikeMoment 点赞
func (c *Controller) LikeMoment(ctx context.Context, req *bizmoment.LikeMomentReq) (*bizmoment.LikeMomentResp, error) {
	resp := &bizmoment.LikeMomentResp{}

	authInfo := xlaccount.GetAuthInfo(ctx)
	svcReq := &svcmoment.LikeMomentReq{
		MomentId:    req.GetMomentId(),
		UserId:      authInfo.UserId,
		CharacterId: req.GetCharacterId(),
	}

	_, err := c.svcMomentClient.LikeMoment(ctx, svcReq)
	if err != nil {
		errcode.FromError(err).SetTo(resp)
		return resp, nil
	}

	errcode.ErrOK.SetTo(resp)
	return resp, nil
}

// UnlikeMoment 取消点赞
func (c *Controller) UnlikeMoment(ctx context.Context, req *bizmoment.UnlikeMomentReq) (*bizmoment.UnlikeMomentResp, error) {
	resp := &bizmoment.UnlikeMomentResp{}

	authInfo := xlaccount.GetAuthInfo(ctx)
	svcReq := &svcmoment.UnlikeMomentReq{
		MomentId: req.GetMomentId(),
		UserId:   authInfo.UserId,
	}

	_, err := c.svcMomentClient.UnlikeMoment(ctx, svcReq)
	if err != nil {
		errcode.FromError(err).SetTo(resp)
		return resp, nil
	}

	errcode.ErrOK.SetTo(resp)
	return resp, nil
}

// GetMyLikedMoments 获取我点赞的朋友圈列表
func (c *Controller) GetMyLikedMoments(ctx context.Context, req *bizmoment.GetMyLikedMomentsReq) (*bizmoment.GetMyLikedMomentsResp, error) {
	resp := &bizmoment.GetMyLikedMomentsResp{}

	authInfo := xlaccount.GetAuthInfo(ctx)
	svcReq := &svcmoment.GetMyLikedMomentsReq{
		UserId:   authInfo.UserId,
		Page:     req.GetPage(),
		PageSize: req.GetPageSize(),
	}

	svcResp, err := c.svcMomentClient.GetMyLikedMoments(ctx, svcReq)
	if err != nil {
		logger.Errorf("GetMyLikedMoments error: %v", err)
		errcode.FromError(err).SetTo(resp)
		return resp, nil
	}

	resp.Data = svcResp.Data
	errcode.ErrOK.SetTo(resp)
	return resp, nil
}

// GetMomentSquare 获取朋友圈广场
func (c *Controller) GetMomentSquare(ctx context.Context, req *bizmoment.GetMomentSquareReq) (*bizmoment.GetMomentSquareResp, error) {
	resp := &bizmoment.GetMomentSquareResp{}

	authInfo := xlaccount.GetAuthInfo(ctx)
	svcReq := &svcmoment.GetMomentSquareReq{
		Page:     req.GetPage(),
		PageSize: req.GetPageSize(),
		UserId:   authInfo.UserId,
	}

	svcResp, err := c.svcMomentClient.GetMomentSquare(ctx, svcReq)
	if err != nil {
		logger.Errorf("GetMomentSquare error: %v", err)
		errcode.FromError(err).SetTo(resp)
		return resp, nil
	}

	resp.Data = svcResp.Data
	errcode.ErrOK.SetTo(resp)
	return resp, nil
}

// UpdateUserCharacter 更新用户当前使用的角色
func (c *Controller) UpdateUserCharacter(ctx context.Context, req *bizmoment.UpdateUserCharacterReq) (*bizmoment.UpdateUserCharacterResp, error) {
	resp := &bizmoment.UpdateUserCharacterResp{}

	authInfo := xlaccount.GetAuthInfo(ctx)
	svcReq := &svcmoment.UpdateUserCharacterReq{
		UserId:      authInfo.UserId,
		CharacterId: req.GetCharacterId(),
	}

	_, err := c.svcMomentClient.UpdateUserCharacter(ctx, svcReq)
	if err != nil {
		errcode.FromError(err).SetTo(resp)
		return resp, nil
	}

	errcode.ErrOK.SetTo(resp)
	return resp, nil
}

// GetUserCharacter 获取用户当前使用的角色
func (c *Controller) GetUserCharacter(ctx context.Context, req *bizmoment.GetUserCharacterReq) (*bizmoment.GetUserCharacterResp, error) {
	resp := &bizmoment.GetUserCharacterResp{}

	authInfo := xlaccount.GetAuthInfo(ctx)
	svcReq := &svcmoment.GetUserCharacterReq{
		UserId: authInfo.UserId,
	}

	svcResp, err := c.svcMomentClient.GetUserCharacter(ctx, svcReq)
	if err != nil {
		errcode.FromError(err).SetTo(resp)
		return resp, nil
	}

	resp.Data = svcResp.Data
	errcode.ErrOK.SetTo(resp)
	return resp, nil
}

// 举报
func (c *Controller) Report(ctx context.Context, req *svcmoment.ReportReq) (*bizmoment.ReportResp, error) {
	logger.Infof("report req: %v", req)
	resp := &bizmoment.ReportResp{}
	errcode.ErrOK.SetTo(resp)
	return resp, nil
}
