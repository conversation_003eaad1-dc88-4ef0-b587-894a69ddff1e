package controller

import (
	"context"
	"testing"

	"github.com/stretchr/testify/assert"
	"new-gitlab.xunlei.cn/vcproject/backends/baselib/xlaccount"
	"new-gitlab.xunlei.cn/vcproject/backends/proto"
	"new-gitlab.xunlei.cn/vcproject/backends/proto/api/bizmoment"
	"new-gitlab.xunlei.cn/vcproject/backends/proto/svcmgr"
)

const (
	testUserID        = 50000
	testCharacterName = "测试角色"
	testAvatarURL     = "https://example.com/avatar.jpg"
)

func setupTestContext() context.Context {
	ctx := context.Background()
	// 添加认证信息
	ctx = xlaccount.SetAuthInfo(ctx, &proto.AuthInfo{
		UserId: testUserID,
	})
	return ctx
}

func TestMain(m *testing.M) {
	// 初始化服务
	svcmgr.InitServices()
	m.Run()
}

func TestGetCharacterList(t *testing.T) {
	// 创建控制器
	c := NewController()

	// 执行测试
	ctx := setupTestContext()
	req := &bizmoment.GetCharacterListReq{
		Page:     1,
		PageSize: 10,
	}

	resp, err := c.GetCharacterList(ctx, req)

	// 验证结果
	assert.NoError(t, err)
	assert.NotNil(t, resp)
	assert.Equal(t, int32(0), resp.Code)
}

func TestPublishMoment(t *testing.T) {
	// 创建控制器
	c := NewController()

	// 执行测试
	ctx := setupTestContext()
	req := &bizmoment.PublishMomentReq{
		CharacterId:    1,
		OriginAudioUrl: "https://example.com/origin.mp3",
		VoiceAudioUrl:  "https://example.com/voice.mp3",
		Content:        "这是一条测试朋友圈",
		Duration:       10,
	}

	resp, err := c.PublishMoment(ctx, req)

	// 验证结果
	assert.NoError(t, err)
	assert.NotNil(t, resp)
	assert.Equal(t, int32(0), resp.Code)
	assert.NotZero(t, resp.Data.MomentId)
}

func TestGetMyMoments(t *testing.T) {
	// 创建控制器
	c := NewController()

	// 先发布一条朋友圈
	ctx := setupTestContext()
	publishReq := &bizmoment.PublishMomentReq{
		CharacterId:    1,
		OriginAudioUrl: "https://example.com/origin.mp3",
		VoiceAudioUrl:  "https://example.com/voice.mp3",
		Content:        "这是一条测试朋友圈",
		Duration:       10,
	}
	_, err := c.PublishMoment(ctx, publishReq)
	assert.NoError(t, err)

	// 获取朋友圈列表
	req := &bizmoment.GetMyMomentsReq{
		Page:     1,
		PageSize: 10,
	}

	resp, err := c.GetMyMoments(ctx, req)

	// 验证结果
	assert.NoError(t, err)
	assert.NotNil(t, resp)
	assert.Equal(t, int32(0), resp.Code)
	if len(resp.Data.Moments) > 0 {
		assert.Equal(t, testUserID, resp.Data.Moments[0].UserId)
	}
}

func TestGetMomentsByIds(t *testing.T) {
	// 创建控制器
	c := NewController()

	// 先发布一条朋友圈
	ctx := setupTestContext()
	publishReq := &bizmoment.PublishMomentReq{
		CharacterId:    1,
		OriginAudioUrl: "https://example.com/origin.mp3",
		VoiceAudioUrl:  "https://example.com/voice.mp3",
		Content:        "这是一条测试朋友圈",
		Duration:       10,
	}
	publishResp, err := c.PublishMoment(ctx, publishReq)
	assert.NoError(t, err)
	momentID := publishResp.Data.MomentId

	// 通过ID获取朋友圈详情
	req := &bizmoment.GetMomentsByIdsReq{
		MomentIds: []int64{momentID},
	}

	resp, err := c.GetMomentsByIds(ctx, req)

	// 验证结果
	assert.NoError(t, err)
	assert.NotNil(t, resp)
	assert.Equal(t, int32(0), resp.Code)
	if len(resp.Data.Moments) > 0 {
		assert.Equal(t, momentID, resp.Data.Moments[0].Id)
		assert.Equal(t, testUserID, resp.Data.Moments[0].UserId)
	}
}

func TestLikeMoment(t *testing.T) {
	// 创建控制器
	c := NewController()

	// 先发布一条朋友圈
	ctx := setupTestContext()
	publishReq := &bizmoment.PublishMomentReq{
		CharacterId:    1,
		OriginAudioUrl: "https://example.com/origin.mp3",
		VoiceAudioUrl:  "https://example.com/voice.mp3",
		Content:        "这是一条测试朋友圈",
		Duration:       10,
	}
	publishResp, err := c.PublishMoment(ctx, publishReq)
	assert.NoError(t, err)
	momentID := publishResp.Data.MomentId

	// 点赞
	req := &bizmoment.LikeMomentReq{
		MomentId:    momentID,
		CharacterId: 1,
	}

	resp, err := c.LikeMoment(ctx, req)

	// 验证结果
	assert.NoError(t, err)
	assert.NotNil(t, resp)
	assert.Equal(t, int32(0), resp.Code)
}

func TestUnlikeMoment(t *testing.T) {
	// 创建控制器
	c := NewController()

	// 先发布一条朋友圈
	ctx := setupTestContext()
	publishReq := &bizmoment.PublishMomentReq{
		CharacterId:    1,
		OriginAudioUrl: "https://example.com/origin.mp3",
		VoiceAudioUrl:  "https://example.com/voice.mp3",
		Content:        "这是一条测试朋友圈",
		Duration:       10,
	}
	publishResp, err := c.PublishMoment(ctx, publishReq)
	assert.NoError(t, err)
	momentID := publishResp.Data.MomentId

	// 先点赞
	likeReq := &bizmoment.LikeMomentReq{
		MomentId:    momentID,
		CharacterId: 1,
	}
	_, err = c.LikeMoment(ctx, likeReq)
	assert.NoError(t, err)

	// 取消点赞
	req := &bizmoment.UnlikeMomentReq{
		MomentId: momentID,
	}

	resp, err := c.UnlikeMoment(ctx, req)

	// 验证结果
	assert.NoError(t, err)
	assert.NotNil(t, resp)
	assert.Equal(t, int32(0), resp.Code)
}

func TestGetMyLikedMoments(t *testing.T) {
	// 创建控制器
	c := NewController()

	// 先发布一条朋友圈并点赞
	ctx := setupTestContext()
	publishReq := &bizmoment.PublishMomentReq{
		CharacterId:    1,
		OriginAudioUrl: "https://example.com/origin.mp3",
		VoiceAudioUrl:  "https://example.com/voice.mp3",
		Content:        "这是一条测试朋友圈",
		Duration:       10,
	}
	publishResp, err := c.PublishMoment(ctx, publishReq)
	assert.NoError(t, err)
	momentID := publishResp.Data.MomentId

	// 点赞
	likeReq := &bizmoment.LikeMomentReq{
		MomentId:    momentID,
		CharacterId: 1,
	}
	_, err = c.LikeMoment(ctx, likeReq)
	assert.NoError(t, err)

	// 获取我点赞的朋友圈
	req := &bizmoment.GetMyLikedMomentsReq{
		Page:     1,
		PageSize: 10,
	}

	resp, err := c.GetMyLikedMoments(ctx, req)

	// 验证结果
	assert.NoError(t, err)
	assert.NotNil(t, resp)
	assert.Equal(t, int32(0), resp.Code)
}

func TestUpdateUserCharacter(t *testing.T) {
	// 创建控制器
	c := NewController()

	// 执行测试
	ctx := setupTestContext()
	req := &bizmoment.UpdateUserCharacterReq{
		CharacterId: 1,
	}

	resp, err := c.UpdateUserCharacter(ctx, req)

	// 验证结果
	assert.NoError(t, err)
	assert.NotNil(t, resp)
	assert.Equal(t, int32(0), resp.Code)
}

func TestGetUserCharacter(t *testing.T) {
	// 创建控制器
	c := NewController()

	// 先设置角色
	ctx := setupTestContext()
	updateReq := &bizmoment.UpdateUserCharacterReq{
		CharacterId: 1,
	}
	_, err := c.UpdateUserCharacter(ctx, updateReq)
	assert.NoError(t, err)

	// 获取角色信息
	req := &bizmoment.GetUserCharacterReq{}

	resp, err := c.GetUserCharacter(ctx, req)

	// 验证结果
	assert.NoError(t, err)
	assert.NotNil(t, resp)
	assert.Equal(t, int32(0), resp.Code)
	assert.Equal(t, int64(1), resp.Data.CharacterId)
}
