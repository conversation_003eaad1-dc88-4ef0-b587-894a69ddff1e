<template>
  <div class="voice-change-config-test">
    <el-card header="变声功能RVC配置测试">
      <div class="test-section">
        <h3>角色资源RVC配置测试</h3>
        
        <!-- 角色资源查询 -->
        <div class="query-section" style="margin-bottom: 30px;">
          <h4>查询角色资源配置</h4>
          <div style="display: flex; align-items: center; gap: 12px; margin-bottom: 15px;">
            <el-input
              v-model="characterAssetId"
              placeholder="请输入角色资源ID"
              style="width: 200px;"
              type="number"
            />
            <el-button 
              type="primary" 
              @click="queryCharacterAsset"
              :loading="querying"
            >
              查询配置
            </el-button>
          </div>
          
          <!-- 查询结果 -->
          <div v-if="characterAssetInfo" class="query-result">
            <h5>查询结果</h5>
            <el-descriptions :column="2" border size="small">
              <el-descriptions-item label="资源ID">
                {{ characterAssetInfo.ID }}
              </el-descriptions-item>
              <el-descriptions-item label="资源名称">
                {{ characterAssetInfo.name || '未设置' }}
              </el-descriptions-item>
              <el-descriptions-item label="角色ID">
                {{ characterAssetInfo.character_id }}
              </el-descriptions-item>
              <el-descriptions-item label="RVC配置">
                <el-tag :type="characterAssetInfo.reference_audio_use_rvc === 1 ? 'success' : 'warning'">
                  {{ characterAssetInfo.reference_audio_use_rvc === 1 ? '使用RVC' : '不使用RVC' }}
                  ({{ characterAssetInfo.reference_audio_use_rvc }})
                </el-tag>
              </el-descriptions-item>
              <el-descriptions-item label="参考音频URL">
                {{ characterAssetInfo.reference_audio_url || '未设置' }}
              </el-descriptions-item>
              <el-descriptions-item label="RVC模型名称">
                {{ characterAssetInfo.rvc_name || '未设置' }}
              </el-descriptions-item>
            </el-descriptions>
            
            <!-- 变声参数预测 -->
            <div style="margin-top: 15px;">
              <h5>变声参数预测</h5>
              <div style="padding: 12px; background: #f0f9ff; border-radius: 6px; border: 1px solid #bfdbfe;">
                <div style="margin-bottom: 8px;">
                  <strong>use_rvc 参数值：</strong>
                  <el-tag :type="characterAssetInfo.reference_audio_use_rvc === 1 ? 'success' : 'warning'">
                    {{ characterAssetInfo.reference_audio_use_rvc === 1 ? 'true' : 'false' }}
                  </el-tag>
                </div>
                <div style="font-size: 12px; color: #666;">
                  根据 reference_audio_use_rvc = {{ characterAssetInfo.reference_audio_use_rvc }}，
                  变声时将{{ characterAssetInfo.reference_audio_use_rvc === 1 ? '使用' : '不使用' }}RVC处理
                </div>
              </div>
            </div>
          </div>
        </div>
        
        <!-- 配置修改测试 -->
        <div class="modify-section" style="margin-bottom: 30px;">
          <h4>配置修改测试</h4>
          <div v-if="characterAssetInfo" style="margin-bottom: 15px;">
            <div style="margin-bottom: 12px;">
              <strong>当前配置：</strong>
              <el-tag :type="characterAssetInfo.reference_audio_use_rvc === 1 ? 'success' : 'warning'">
                reference_audio_use_rvc = {{ characterAssetInfo.reference_audio_use_rvc }}
              </el-tag>
            </div>
            
            <div style="display: flex; align-items: center; gap: 12px;">
              <el-radio-group v-model="newRvcConfig">
                <el-radio :value="1">使用RVC (1)</el-radio>
                <el-radio :value="0">不使用RVC (0)</el-radio>
              </el-radio-group>
              <el-button 
                type="warning" 
                @click="updateRvcConfig"
                :loading="updating"
                :disabled="newRvcConfig === characterAssetInfo.reference_audio_use_rvc"
              >
                更新配置
              </el-button>
            </div>
          </div>
          <div v-else style="color: #999; font-style: italic;">
            请先查询角色资源信息
          </div>
        </div>
        
        <!-- 测试日志 -->
        <div class="log-section">
          <h4>测试日志</h4>
          <div style="max-height: 300px; overflow-y: auto; padding: 12px; background: #f5f5f5; border-radius: 6px; font-family: monospace; font-size: 12px;">
            <div v-for="(log, index) in testLogs" :key="index" :style="{ color: log.type === 'error' ? '#f56c6c' : log.type === 'success' ? '#67c23a' : '#333' }">
              [{{ log.time }}] {{ log.message }}
            </div>
            <div v-if="testLogs.length === 0" style="color: #999; font-style: italic;">
              暂无日志
            </div>
          </div>
          <div style="margin-top: 8px;">
            <el-button size="small" @click="clearLogs">清空日志</el-button>
          </div>
        </div>
        
        <!-- 使用说明 -->
        <div class="instructions" style="margin-top: 30px;">
          <h4>使用说明</h4>
          <el-alert
            title="测试步骤"
            type="info"
            :closable="false"
            show-icon
          >
            <template #default>
              <ol style="margin: 0; padding-left: 20px;">
                <li>输入角色资源ID，点击"查询配置"获取当前配置</li>
                <li>查看"RVC配置"和"变声参数预测"结果</li>
                <li>可以修改配置进行测试（可选）</li>
                <li>在实际的剧本管理页面中测试变声功能</li>
                <li>查看控制台日志验证use_rvc参数是否正确</li>
              </ol>
            </template>
          </el-alert>
          
          <el-alert
            title="预期结果"
            type="success"
            :closable="false"
            show-icon
            style="margin-top: 12px;"
          >
            <template #default>
              <ul style="margin: 0; padding-left: 20px;">
                <li>reference_audio_use_rvc = 1 时，变声使用RVC处理</li>
                <li>reference_audio_use_rvc = 0 时，变声不使用RVC处理</li>
                <li>控制台输出正确的配置信息和use_rvc参数值</li>
              </ul>
            </template>
          </el-alert>
        </div>
      </div>
    </el-card>
  </div>
</template>

<script setup>
import { ref } from 'vue'
import { ElMessage } from 'element-plus'
import { findCharacterAssets, updateCharacterAssets } from '@/api/characters/characterAssets'

// 状态变量
const characterAssetId = ref('')
const characterAssetInfo = ref(null)
const newRvcConfig = ref(1)
const querying = ref(false)
const updating = ref(false)
const testLogs = ref([])

// 添加日志
const addLog = (message, type = 'info') => {
  const time = new Date().toLocaleTimeString()
  testLogs.value.push({ time, message, type })
}

// 清空日志
const clearLogs = () => {
  testLogs.value = []
}

// 查询角色资源配置
const queryCharacterAsset = async () => {
  if (!characterAssetId.value) {
    ElMessage.warning('请输入角色资源ID')
    return
  }

  querying.value = true
  addLog(`开始查询角色资源ID: ${characterAssetId.value}`)

  try {
    const response = await findCharacterAssets({
      ID: parseInt(characterAssetId.value)
    })

    addLog(`API响应: ${JSON.stringify(response)}`)

    if (!response.success) {
      throw new Error('API调用失败')
    }

    const asset = response.data.recharacterAssets
    if (!asset) {
      throw new Error('角色资源不存在')
    }

    characterAssetInfo.value = asset
    newRvcConfig.value = asset.reference_audio_use_rvc

    addLog(`查询成功: reference_audio_use_rvc = ${asset.reference_audio_use_rvc}`, 'success')
    addLog(`预测变声参数: use_rvc = ${asset.reference_audio_use_rvc === 1}`, 'success')

    ElMessage.success('查询成功')

  } catch (error) {
    addLog(`查询失败: ${error.message}`, 'error')
    ElMessage.error('查询失败：' + error.message)
    characterAssetInfo.value = null
  } finally {
    querying.value = false
  }
}

// 更新RVC配置
const updateRvcConfig = async () => {
  if (!characterAssetInfo.value) {
    ElMessage.warning('请先查询角色资源信息')
    return
  }

  updating.value = true
  addLog(`开始更新配置: reference_audio_use_rvc = ${newRvcConfig.value}`)

  try {
    const updateData = {
      ...characterAssetInfo.value,
      reference_audio_use_rvc: newRvcConfig.value
    }

    const response = await updateCharacterAssets(updateData)

    if (!response.success) {
      throw new Error('更新失败')
    }

    characterAssetInfo.value.reference_audio_use_rvc = newRvcConfig.value
    
    addLog(`更新成功: reference_audio_use_rvc = ${newRvcConfig.value}`, 'success')
    addLog(`新的变声参数: use_rvc = ${newRvcConfig.value === 1}`, 'success')

    ElMessage.success('配置更新成功')

  } catch (error) {
    addLog(`更新失败: ${error.message}`, 'error')
    ElMessage.error('更新失败：' + error.message)
  } finally {
    updating.value = false
  }
}
</script>

<style scoped>
.voice-change-config-test {
  padding: 20px;
}

.test-section {
  margin-bottom: 30px;
}

.query-result {
  margin-top: 15px;
  padding: 15px;
  background: #fafafa;
  border-radius: 8px;
  border: 1px solid #e0e0e0;
}

.query-result h5 {
  margin: 0 0 12px 0;
  color: #333;
}
</style>
