<template>
  <div class="recording-duration-test">
    <el-card header="录音时长显示测试">
      <div class="test-section">
        <h3>录音功能时长显示测试</h3>
        
        <!-- 录音控制区域 -->
        <div class="recording-controls" style="margin-bottom: 20px;">
          <el-button 
            v-if="!isRecording && !recordedBlob" 
            type="success" 
            :loading="recordingInitializing"
            @click="startRecording"
          >
            <el-icon><Microphone /></el-icon>
            开始录音
          </el-button>
          
          <el-button 
            v-if="isRecording" 
            type="danger" 
            @click="stopRecording"
          >
            <el-icon><VideoPause /></el-icon>
            停止录音
          </el-button>
          
          <div v-if="isRecording" class="recording-indicator" style="display: inline-flex; align-items: center; gap: 6px; margin-left: 12px;">
            <div class="recording-dot"></div>
            <span style="color: #f56c6c; font-size: 14px;">录音中... {{ recordingTime }}s</span>
          </div>
          
          <el-button 
            v-if="recordedBlob && !isRecording" 
            type="warning" 
            @click="resetRecording"
            style="margin-left: 12px;"
          >
            重新录音
          </el-button>
        </div>
        
        <!-- 录音预览区域 -->
        <div v-if="recordedBlob && recordingPreviewUrl" class="recording-preview" style="margin-bottom: 20px;">
          <h4>录音预览 - 时长显示测试</h4>
          
          <!-- 使用 AudioPlayer 组件 -->
          <div style="margin-bottom: 16px;">
            <h5>AudioPlayer 组件（推荐）</h5>
            <AudioPlayer 
              :src="recordingPreviewUrl" 
              :duration="recordingDuration * 1000"
              style="width: 100%; max-width: 400px;"
            />
            <div style="margin-top: 8px; font-size: 12px; color: #666;">
              录音时长: {{ formatDuration(recordingDuration) }} | 
              文件大小: {{ formatFileSize(recordedBlob?.size || 0) }}
            </div>
          </div>
          
          <!-- 原生 audio 标签对比 -->
          <div style="margin-bottom: 16px;">
            <h5>原生 Audio 标签（对比）</h5>
            <audio controls :src="recordingPreviewUrl" style="width: 100%; max-width: 400px;"></audio>
            <div style="margin-top: 8px; font-size: 12px; color: #666;">
              注意：原生 audio 标签可能显示时长为无限或不准确
            </div>
          </div>
          
          <!-- 详细信息 -->
          <div style="margin-top: 16px;">
            <h5>录音详细信息</h5>
            <el-descriptions :column="2" border size="small">
              <el-descriptions-item label="录音计时器时长">
                {{ formatDuration(recordingTime) }}
              </el-descriptions-item>
              <el-descriptions-item label="实际音频时长">
                {{ formatDuration(recordingDuration) }}
              </el-descriptions-item>
              <el-descriptions-item label="文件大小">
                {{ formatFileSize(recordedBlob?.size || 0) }}
              </el-descriptions-item>
              <el-descriptions-item label="文件类型">
                {{ recordedBlob?.type || 'unknown' }}
              </el-descriptions-item>
              <el-descriptions-item label="时长获取状态">
                {{ durationStatus }}
              </el-descriptions-item>
              <el-descriptions-item label="浏览器">
                {{ getBrowserInfo() }}
              </el-descriptions-item>
            </el-descriptions>
          </div>
        </div>
        
        <!-- 测试说明 -->
        <div class="test-info">
          <h4>测试说明</h4>
          <el-alert
            title="录音时长显示优化"
            type="info"
            :closable="false"
            show-icon
          >
            <template #default>
              <p><strong>测试目标：</strong></p>
              <ul>
                <li>验证 AudioPlayer 组件能正确显示录音时长</li>
                <li>对比原生 audio 标签的时长显示问题</li>
                <li>测试不同浏览器的兼容性</li>
                <li>验证时长获取的准确性和稳定性</li>
              </ul>
              <p><strong>预期结果：</strong></p>
              <ul>
                <li>AudioPlayer 显示准确的录音时长（如：0:15、1:23）</li>
                <li>录音计时器时长与实际音频时长基本一致</li>
                <li>在不同浏览器中都能正常显示</li>
                <li>时长获取过程稳定，无异常错误</li>
              </ul>
            </template>
          </el-alert>
        </div>
      </div>
    </el-card>
  </div>
</template>

<script setup>
import { ref, onMounted, onUnmounted } from 'vue'
import { ElMessage } from 'element-plus'
import { Microphone, VideoPause } from '@element-plus/icons-vue'
import AudioPlayer from '@/components/AudioPlayer/AudioPlayer.vue'

// 录音相关状态
const isRecording = ref(false)
const recordingInitializing = ref(false)
const recordedBlob = ref(null)
const recordingPreviewUrl = ref('')
const recordingTime = ref(0)
const recordingDuration = ref(0)
const mediaRecorder = ref(null)
const recordingStream = ref(null)
const recordingTimer = ref(null)
const durationStatus = ref('未开始')

// 开始录音
const startRecording = async () => {
  try {
    recordingInitializing.value = true
    durationStatus.value = '准备中...'
    
    const stream = await navigator.mediaDevices.getUserMedia({ 
      audio: {
        sampleRate: 44100,
        channelCount: 1,
        echoCancellation: true,
        noiseSuppression: true
      } 
    })
    
    recordingStream.value = stream
    
    const options = {
      mimeType: 'audio/webm;codecs=opus',
      audioBitsPerSecond: 128000
    }
    
    if (!MediaRecorder.isTypeSupported(options.mimeType)) {
      options.mimeType = 'audio/webm'
      if (!MediaRecorder.isTypeSupported(options.mimeType)) {
        options.mimeType = 'audio/mp4'
        if (!MediaRecorder.isTypeSupported(options.mimeType)) {
          delete options.mimeType
        }
      }
    }
    
    mediaRecorder.value = new MediaRecorder(stream, options)
    const chunks = []
    
    mediaRecorder.value.ondataavailable = (event) => {
      if (event.data.size > 0) {
        chunks.push(event.data)
      }
    }
    
    mediaRecorder.value.onstop = async () => {
      durationStatus.value = '获取时长中...'
      const blob = new Blob(chunks, { type: 'audio/webm' })
      recordedBlob.value = blob
      recordingPreviewUrl.value = URL.createObjectURL(blob)
      
      try {
        const duration = await getAudioDurationFromBlob(blob)
        recordingDuration.value = duration
        durationStatus.value = '获取成功'
        console.log('录音时长:', duration, '秒')
      } catch (error) {
        console.error('获取录音时长失败:', error)
        recordingDuration.value = recordingTime.value
        durationStatus.value = '获取失败，使用计时器时长'
      }
      
      if (recordingStream.value) {
        recordingStream.value.getTracks().forEach(track => track.stop())
        recordingStream.value = null
      }
    }
    
    mediaRecorder.value.start()
    isRecording.value = true
    recordingTime.value = 0
    durationStatus.value = '录音中...'
    
    recordingTimer.value = setInterval(() => {
      recordingTime.value++
    }, 1000)
    
    ElMessage.success('录音已开始')
    
  } catch (error) {
    console.error('录音启动失败:', error)
    durationStatus.value = '启动失败'
    if (error.name === 'NotAllowedError') {
      ElMessage.error('请允许访问麦克风权限')
    } else if (error.name === 'NotFoundError') {
      ElMessage.error('未找到麦克风设备')
    } else {
      ElMessage.error('录音启动失败：' + error.message)
    }
  } finally {
    recordingInitializing.value = false
  }
}

// 停止录音
const stopRecording = () => {
  if (mediaRecorder.value && isRecording.value) {
    mediaRecorder.value.stop()
    isRecording.value = false
    
    if (recordingTimer.value) {
      clearInterval(recordingTimer.value)
      recordingTimer.value = null
    }
    
    ElMessage.success('录音已停止')
  }
}

// 重新录音
const resetRecording = () => {
  if (recordingPreviewUrl.value) {
    URL.revokeObjectURL(recordingPreviewUrl.value)
    recordingPreviewUrl.value = ''
  }
  recordedBlob.value = null
  recordingTime.value = 0
  recordingDuration.value = 0
  durationStatus.value = '已重置'
  
  if (isRecording.value) {
    stopRecording()
  }
}

// 从Blob获取音频时长
const getAudioDurationFromBlob = (blob) => {
  return new Promise((resolve, reject) => {
    const audio = new Audio()
    const url = URL.createObjectURL(blob)
    
    const timeout = setTimeout(() => {
      URL.revokeObjectURL(url)
      reject(new Error('获取音频时长超时'))
    }, 5000)
    
    audio.addEventListener('loadedmetadata', () => {
      clearTimeout(timeout)
      URL.revokeObjectURL(url)
      
      if (audio.duration && !isNaN(audio.duration) && isFinite(audio.duration)) {
        resolve(audio.duration)
      } else {
        reject(new Error('音频时长无效'))
      }
    })
    
    audio.addEventListener('error', () => {
      clearTimeout(timeout)
      URL.revokeObjectURL(url)
      reject(new Error('无法加载音频文件'))
    })
    
    audio.src = url
    audio.load()
  })
}

// 格式化时长显示
const formatDuration = (seconds) => {
  if (!seconds || seconds <= 0) return '0:00'
  
  const minutes = Math.floor(seconds / 60)
  const remainingSeconds = Math.floor(seconds % 60)
  
  return `${minutes}:${remainingSeconds.toString().padStart(2, '0')}`
}

// 格式化文件大小显示
const formatFileSize = (bytes) => {
  if (!bytes || bytes <= 0) return '0 B'
  
  if (bytes < 1024) return `${bytes} B`
  if (bytes < 1024 * 1024) return `${(bytes / 1024).toFixed(1)} KB`
  return `${(bytes / (1024 * 1024)).toFixed(1)} MB`
}

// 获取浏览器信息
const getBrowserInfo = () => {
  const userAgent = navigator.userAgent
  if (userAgent.includes('Chrome')) return 'Chrome'
  if (userAgent.includes('Firefox')) return 'Firefox'
  if (userAgent.includes('Safari')) return 'Safari'
  if (userAgent.includes('Edge')) return 'Edge'
  return 'Unknown'
}

// 组件卸载时清理资源
onUnmounted(() => {
  if (recordingStream.value) {
    recordingStream.value.getTracks().forEach(track => track.stop())
  }
  if (recordingTimer.value) {
    clearInterval(recordingTimer.value)
  }
  if (recordingPreviewUrl.value) {
    URL.revokeObjectURL(recordingPreviewUrl.value)
  }
})
</script>

<style scoped>
.recording-duration-test {
  padding: 20px;
}

.test-section {
  margin-bottom: 30px;
}

.recording-controls {
  display: flex;
  align-items: center;
  gap: 12px;
  flex-wrap: wrap;
}

.recording-indicator {
  .recording-dot {
    width: 8px;
    height: 8px;
    background-color: #f56c6c;
    border-radius: 50%;
    animation: recording-pulse 1.5s ease-in-out infinite;
  }
}

@keyframes recording-pulse {
  0% {
    opacity: 1;
    transform: scale(1);
  }
  50% {
    opacity: 0.5;
    transform: scale(1.2);
  }
  100% {
    opacity: 1;
    transform: scale(1);
  }
}

.recording-preview {
  padding: 16px;
  background: #f0f9ff;
  border-radius: 8px;
  border: 1px solid #bfdbfe;
}

.test-info {
  margin-top: 20px;
}

.test-info ul {
  margin: 8px 0;
  padding-left: 20px;
}

.test-info li {
  margin: 4px 0;
}
</style>
