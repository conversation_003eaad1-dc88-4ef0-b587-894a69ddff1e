<template>
  <div class="audio-player-test">
    <el-card header="AudioPlayer 组件测试">
      <div class="test-section">
        <h3>AudioPlayer 时长显示测试</h3>
        
        <!-- 录音功能 -->
        <div class="recording-section" style="margin-bottom: 30px;">
          <h4>录音测试</h4>
          <div class="controls" style="margin-bottom: 15px;">
            <el-button 
              v-if="!isRecording && !recordedBlob" 
              type="success" 
              @click="startRecording"
            >
              开始录音
            </el-button>
            
            <el-button 
              v-if="isRecording" 
              type="danger" 
              @click="stopRecording"
            >
              停止录音 ({{ recordingTime }}s)
            </el-button>
            
            <el-button 
              v-if="recordedBlob" 
              type="warning" 
              @click="resetRecording"
            >
              重新录音
            </el-button>
          </div>
        </div>
        
        <!-- AudioPlayer 测试 -->
        <div v-if="recordedBlob" class="player-test-section">
          <h4>AudioPlayer 组件测试</h4>
          
          <!-- 不同参数的 AudioPlayer 测试 -->
          <div class="player-tests">
            <div class="test-item" style="margin-bottom: 20px; padding: 15px; border: 1px solid #ddd; border-radius: 8px;">
              <h5>测试1: 使用获取到的时长参数</h5>
              <div style="margin-bottom: 10px;">
                <strong>参数:</strong> duration={{ recordingDuration * 1000 }}ms
              </div>
              <AudioPlayer 
                :src="recordingPreviewUrl" 
                :duration="recordingDuration * 1000"
                @play="() => console.log('🎵 测试1 播放')"
                @error="(error) => console.error('❌ 测试1 错误:', error)"
              />
            </div>
            
            <div class="test-item" style="margin-bottom: 20px; padding: 15px; border: 1px solid #ddd; border-radius: 8px;">
              <h5>测试2: 使用固定时长参数</h5>
              <div style="margin-bottom: 10px;">
                <strong>参数:</strong> duration=5000ms (5秒)
              </div>
              <AudioPlayer 
                :src="recordingPreviewUrl" 
                :duration="5000"
                @play="() => console.log('🎵 测试2 播放')"
                @error="(error) => console.error('❌ 测试2 错误:', error)"
              />
            </div>
            
            <div class="test-item" style="margin-bottom: 20px; padding: 15px; border: 1px solid #ddd; border-radius: 8px;">
              <h5>测试3: 不传入时长参数</h5>
              <div style="margin-bottom: 10px;">
                <strong>参数:</strong> 无duration参数，依赖音频元数据
              </div>
              <AudioPlayer 
                :src="recordingPreviewUrl"
                @play="() => console.log('🎵 测试3 播放')"
                @error="(error) => console.error('❌ 测试3 错误:', error)"
              />
            </div>
            
            <div class="test-item" style="margin-bottom: 20px; padding: 15px; border: 1px solid #ddd; border-radius: 8px;">
              <h5>测试4: 原生 audio 标签对比</h5>
              <div style="margin-bottom: 10px;">
                <strong>说明:</strong> 原生audio标签，可能显示Infinity
              </div>
              <audio controls :src="recordingPreviewUrl" style="width: 100%; max-width: 400px;"></audio>
            </div>
          </div>
          
          <!-- 详细信息 -->
          <div class="debug-info" style="margin-top: 20px;">
            <h4>调试信息</h4>
            <el-descriptions :column="2" border size="small">
              <el-descriptions-item label="录音计时器时长">
                {{ recordingTime }}s
              </el-descriptions-item>
              <el-descriptions-item label="获取的实际时长">
                {{ recordingDuration }}s
              </el-descriptions-item>
              <el-descriptions-item label="时长毫秒值">
                {{ recordingDuration * 1000 }}ms
              </el-descriptions-item>
              <el-descriptions-item label="时长有效性">
                {{ recordingDuration > 0 && isFinite(recordingDuration) ? '✅ 有效' : '❌ 无效' }}
              </el-descriptions-item>
              <el-descriptions-item label="参数有效性">
                {{ (recordingDuration * 1000) > 0 && isFinite(recordingDuration * 1000) ? '✅ 有效' : '❌ 无效' }}
              </el-descriptions-item>
              <el-descriptions-item label="Blob类型">
                {{ recordedBlob?.type || 'unknown' }}
              </el-descriptions-item>
              <el-descriptions-item label="Blob大小">
                {{ formatFileSize(recordedBlob?.size || 0) }}
              </el-descriptions-item>
              <el-descriptions-item label="预览URL状态">
                {{ recordingPreviewUrl ? '✅ 已创建' : '❌ 未创建' }}
              </el-descriptions-item>
            </el-descriptions>
          </div>
          
          <!-- 手动测试按钮 -->
          <div class="manual-tests" style="margin-top: 20px;">
            <h4>手动测试</h4>
            <el-button @click="testAudioPlayerWithDifferentDurations">
              测试不同时长参数
            </el-button>
            <el-button @click="logAudioPlayerStates">
              输出AudioPlayer状态
            </el-button>
          </div>
        </div>
        
        <!-- 控制台提示 -->
        <div style="margin-top: 30px;">
          <el-alert
            title="请打开浏览器开发者工具查看详细的调试日志"
            type="info"
            :closable="false"
            show-icon
          />
        </div>
      </div>
    </el-card>
  </div>
</template>

<script setup>
import { ref } from 'vue'
import { ElMessage } from 'element-plus'
import AudioPlayer from '@/components/AudioPlayer/AudioPlayer.vue'

// 录音状态
const isRecording = ref(false)
const recordedBlob = ref(null)
const recordingPreviewUrl = ref('')
const recordingTime = ref(0)
const recordingDuration = ref(0)
const mediaRecorder = ref(null)
const recordingStream = ref(null)
const recordingTimer = ref(null)

// 开始录音
const startRecording = async () => {
  try {
    const stream = await navigator.mediaDevices.getUserMedia({ 
      audio: {
        sampleRate: 44100,
        channelCount: 1,
        echoCancellation: true,
        noiseSuppression: true
      } 
    })
    
    recordingStream.value = stream
    mediaRecorder.value = new MediaRecorder(stream, {
      mimeType: 'audio/webm;codecs=opus'
    })
    
    const chunks = []
    
    mediaRecorder.value.ondataavailable = (event) => {
      if (event.data.size > 0) {
        chunks.push(event.data)
      }
    }
    
    mediaRecorder.value.onstop = async () => {
      console.log('🛑 录音停止，开始处理录音数据')
      
      const blob = new Blob(chunks, { type: 'audio/webm' })
      recordedBlob.value = blob
      recordingPreviewUrl.value = URL.createObjectURL(blob)
      
      // 获取录音时长
      try {
        const duration = await getAudioDurationFromBlob(blob)
        recordingDuration.value = duration
        console.log('✅ 录音时长获取成功:', duration)
      } catch (error) {
        console.error('❌ 获取录音时长失败:', error)
        recordingDuration.value = recordingTime.value
      }
    }
    
    mediaRecorder.value.start()
    isRecording.value = true
    recordingTime.value = 0
    
    recordingTimer.value = setInterval(() => {
      recordingTime.value++
    }, 1000)
    
    ElMessage.success('录音已开始')
    
  } catch (error) {
    console.error('录音启动失败:', error)
    ElMessage.error('录音启动失败：' + error.message)
  }
}

// 停止录音
const stopRecording = () => {
  if (mediaRecorder.value && isRecording.value) {
    mediaRecorder.value.stop()
    isRecording.value = false
    
    if (recordingTimer.value) {
      clearInterval(recordingTimer.value)
      recordingTimer.value = null
    }
    
    if (recordingStream.value) {
      recordingStream.value.getTracks().forEach(track => track.stop())
      recordingStream.value = null
    }
    
    ElMessage.success('录音已停止')
  }
}

// 重新录音
const resetRecording = () => {
  if (recordingPreviewUrl.value) {
    URL.revokeObjectURL(recordingPreviewUrl.value)
    recordingPreviewUrl.value = ''
  }
  recordedBlob.value = null
  recordingTime.value = 0
  recordingDuration.value = 0
  
  if (isRecording.value) {
    stopRecording()
  }
}

// 获取音频时长
const getAudioDurationFromBlob = (blob) => {
  return new Promise((resolve, reject) => {
    const audio = new Audio()
    const url = URL.createObjectURL(blob)
    
    const timeout = setTimeout(() => {
      URL.revokeObjectURL(url)
      reject(new Error('获取音频时长超时'))
    }, 10000)
    
    audio.addEventListener('loadedmetadata', () => {
      clearTimeout(timeout)
      URL.revokeObjectURL(url)
      
      if (audio.duration && !isNaN(audio.duration) && isFinite(audio.duration) && audio.duration > 0) {
        resolve(audio.duration)
      } else {
        reject(new Error(`音频时长无效: ${audio.duration}`))
      }
    })
    
    audio.addEventListener('error', () => {
      clearTimeout(timeout)
      URL.revokeObjectURL(url)
      reject(new Error('无法加载音频文件'))
    })
    
    audio.src = url
    audio.load()
  })
}

// 测试不同时长参数
const testAudioPlayerWithDifferentDurations = () => {
  console.log('🧪 测试不同时长参数:')
  console.log('- 获取的时长:', recordingDuration.value, 's')
  console.log('- 毫秒值:', recordingDuration.value * 1000, 'ms')
  console.log('- 固定5秒:', 5000, 'ms')
  console.log('- 无参数: 依赖音频元数据')
}

// 输出AudioPlayer状态
const logAudioPlayerStates = () => {
  console.log('📊 AudioPlayer 状态信息:')
  console.log('- 录音Blob大小:', recordedBlob.value?.size, 'bytes')
  console.log('- 录音Blob类型:', recordedBlob.value?.type)
  console.log('- 预览URL:', recordingPreviewUrl.value)
  console.log('- 录音时长:', recordingDuration.value, 's')
  console.log('- 传入参数:', recordingDuration.value * 1000, 'ms')
}

// 格式化文件大小
const formatFileSize = (bytes) => {
  if (!bytes || bytes <= 0) return '0 B'
  
  if (bytes < 1024) return `${bytes} B`
  if (bytes < 1024 * 1024) return `${(bytes / 1024).toFixed(1)} KB`
  return `${(bytes / (1024 * 1024)).toFixed(1)} MB`
}
</script>

<style scoped>
.audio-player-test {
  padding: 20px;
}

.test-section {
  margin-bottom: 30px;
}

.controls {
  display: flex;
  align-items: center;
  gap: 12px;
  flex-wrap: wrap;
}

.player-tests {
  margin-top: 15px;
}

.test-item {
  background: #fafafa;
}

.test-item h5 {
  margin: 0 0 10px 0;
  color: #333;
}
</style>
