<template>
  <div class="recording-debug">
    <el-card header="录音时长调试工具">
      <div class="debug-section">
        <h3>录音时长获取调试</h3>
        
        <!-- 录音控制 -->
        <div class="recording-controls" style="margin-bottom: 20px;">
          <el-button 
            v-if="!isRecording && !recordedBlob" 
            type="success" 
            @click="startRecording"
          >
            开始录音
          </el-button>
          
          <el-button 
            v-if="isRecording" 
            type="danger" 
            @click="stopRecording"
          >
            停止录音 ({{ recordingTime }}s)
          </el-button>
          
          <el-button 
            v-if="recordedBlob" 
            type="warning" 
            @click="resetRecording"
          >
            重新录音
          </el-button>
        </div>
        
        <!-- 调试信息 -->
        <div v-if="recordedBlob" class="debug-info">
          <h4>调试信息</h4>
          
          <el-descriptions :column="2" border size="small">
            <el-descriptions-item label="录音计时器时长">
              {{ recordingTime }}s
            </el-descriptions-item>
            <el-descriptions-item label="获取的实际时长">
              {{ recordingDuration }}s
            </el-descriptions-item>
            <el-descriptions-item label="时长差异">
              {{ Math.abs(recordingDuration - recordingTime).toFixed(2) }}s
            </el-descriptions-item>
            <el-descriptions-item label="时长有效性">
              {{ recordingDuration > 0 && isFinite(recordingDuration) ? '✅ 有效' : '❌ 无效' }}
            </el-descriptions-item>
            <el-descriptions-item label="Blob大小">
              {{ formatFileSize(recordedBlob.size) }}
            </el-descriptions-item>
            <el-descriptions-item label="Blob类型">
              {{ recordedBlob.type }}
            </el-descriptions-item>
            <el-descriptions-item label="传给AudioPlayer的值">
              {{ recordingDuration * 1000 }}ms
            </el-descriptions-item>
            <el-descriptions-item label="获取状态">
              {{ durationStatus }}
            </el-descriptions-item>
          </el-descriptions>
          
          <!-- AudioPlayer 测试 -->
          <div style="margin-top: 20px;">
            <h5>AudioPlayer 组件测试</h5>
            <AudioPlayer 
              :src="recordingPreviewUrl" 
              :duration="recordingDuration * 1000"
              style="width: 100%; max-width: 400px;"
            />
          </div>
          
          <!-- 原生 audio 对比 -->
          <div style="margin-top: 20px;">
            <h5>原生 Audio 标签对比</h5>
            <audio controls :src="recordingPreviewUrl" style="width: 100%; max-width: 400px;"></audio>
          </div>
          
          <!-- 手动测试按钮 -->
          <div style="margin-top: 20px;">
            <h5>手动测试</h5>
            <el-button @click="testHtmlAudioMethod" :loading="testing.htmlAudio">
              测试 HTML Audio 方法
            </el-button>
            <el-button @click="testWebAudioMethod" :loading="testing.webAudio">
              测试 Web Audio API 方法
            </el-button>
            <el-button @click="testComprehensiveMethod" :loading="testing.comprehensive">
              测试综合方法
            </el-button>
          </div>
          
          <!-- 测试结果 -->
          <div v-if="testResults.length > 0" style="margin-top: 20px;">
            <h5>测试结果</h5>
            <el-table :data="testResults" size="small" border>
              <el-table-column prop="method" label="方法" width="150" />
              <el-table-column prop="duration" label="获取时长" width="100" />
              <el-table-column prop="status" label="状态" width="100">
                <template #default="scope">
                  <el-tag :type="scope.row.success ? 'success' : 'danger'">
                    {{ scope.row.success ? '成功' : '失败' }}
                  </el-tag>
                </template>
              </el-table-column>
              <el-table-column prop="error" label="错误信息" />
            </el-table>
          </div>
        </div>
        
        <!-- 日志输出 -->
        <div style="margin-top: 20px;">
          <h4>控制台日志</h4>
          <el-alert
            title="请打开浏览器开发者工具查看详细的调试日志"
            type="info"
            :closable="false"
            show-icon
          />
        </div>
      </div>
    </el-card>
  </div>
</template>

<script setup>
import { ref } from 'vue'
import { ElMessage } from 'element-plus'
import AudioPlayer from '@/components/AudioPlayer/AudioPlayer.vue'

// 录音状态
const isRecording = ref(false)
const recordedBlob = ref(null)
const recordingPreviewUrl = ref('')
const recordingTime = ref(0)
const recordingDuration = ref(0)
const mediaRecorder = ref(null)
const recordingStream = ref(null)
const recordingTimer = ref(null)
const durationStatus = ref('未开始')

// 测试状态
const testing = ref({
  htmlAudio: false,
  webAudio: false,
  comprehensive: false
})
const testResults = ref([])

// 开始录音
const startRecording = async () => {
  try {
    durationStatus.value = '录音中...'
    testResults.value = []
    
    const stream = await navigator.mediaDevices.getUserMedia({ 
      audio: {
        sampleRate: 44100,
        channelCount: 1,
        echoCancellation: true,
        noiseSuppression: true
      } 
    })
    
    recordingStream.value = stream
    mediaRecorder.value = new MediaRecorder(stream, {
      mimeType: 'audio/webm;codecs=opus'
    })
    
    const chunks = []
    
    mediaRecorder.value.ondataavailable = (event) => {
      if (event.data.size > 0) {
        chunks.push(event.data)
      }
    }
    
    mediaRecorder.value.onstop = async () => {
      console.log('🛑 录音停止，开始处理录音数据')
      
      const blob = new Blob(chunks, { type: 'audio/webm' })
      recordedBlob.value = blob
      recordingPreviewUrl.value = URL.createObjectURL(blob)
      
      durationStatus.value = '获取时长中...'
      
      // 自动测试所有方法
      await testAllMethods()
    }
    
    mediaRecorder.value.start()
    isRecording.value = true
    recordingTime.value = 0
    
    recordingTimer.value = setInterval(() => {
      recordingTime.value++
    }, 1000)
    
    ElMessage.success('录音已开始')
    
  } catch (error) {
    console.error('录音启动失败:', error)
    durationStatus.value = '启动失败'
    ElMessage.error('录音启动失败：' + error.message)
  }
}

// 停止录音
const stopRecording = () => {
  if (mediaRecorder.value && isRecording.value) {
    mediaRecorder.value.stop()
    isRecording.value = false
    
    if (recordingTimer.value) {
      clearInterval(recordingTimer.value)
      recordingTimer.value = null
    }
    
    if (recordingStream.value) {
      recordingStream.value.getTracks().forEach(track => track.stop())
      recordingStream.value = null
    }
    
    ElMessage.success('录音已停止')
  }
}

// 重新录音
const resetRecording = () => {
  if (recordingPreviewUrl.value) {
    URL.revokeObjectURL(recordingPreviewUrl.value)
    recordingPreviewUrl.value = ''
  }
  recordedBlob.value = null
  recordingTime.value = 0
  recordingDuration.value = 0
  durationStatus.value = '已重置'
  testResults.value = []
  
  if (isRecording.value) {
    stopRecording()
  }
}

// HTML Audio 方法
const getAudioDurationFromBlob = (blob) => {
  return new Promise((resolve, reject) => {
    console.log('🎵 HTML Audio 方法开始')
    
    const audio = new Audio()
    const url = URL.createObjectURL(blob)
    
    const timeout = setTimeout(() => {
      URL.revokeObjectURL(url)
      reject(new Error('超时'))
    }, 10000)
    
    audio.addEventListener('loadedmetadata', () => {
      clearTimeout(timeout)
      URL.revokeObjectURL(url)
      
      console.log('📊 HTML Audio 元数据:', {
        duration: audio.duration,
        readyState: audio.readyState
      })
      
      if (audio.duration && !isNaN(audio.duration) && isFinite(audio.duration) && audio.duration > 0) {
        resolve(audio.duration)
      } else {
        reject(new Error(`时长无效: ${audio.duration}`))
      }
    })
    
    audio.addEventListener('error', () => {
      clearTimeout(timeout)
      URL.revokeObjectURL(url)
      reject(new Error('加载失败'))
    })
    
    audio.src = url
    audio.load()
  })
}

// Web Audio API 方法
const getAudioDurationFromBlobWithWebAudio = async (blob) => {
  console.log('🎵 Web Audio API 方法开始')
  
  const arrayBuffer = await blob.arrayBuffer()
  const audioContext = new AudioContext()
  const audioBuffer = await audioContext.decodeAudioData(arrayBuffer)
  
  const duration = audioBuffer.duration
  console.log('📊 Web Audio API 结果:', duration)
  
  audioContext.close()
  return duration
}

// 综合方法
const getAudioDurationComprehensive = async (blob) => {
  console.log('🔍 综合方法开始')
  
  // 尝试 HTML Audio
  try {
    const duration = await getAudioDurationFromBlob(blob)
    if (duration > 0 && isFinite(duration)) {
      console.log('✅ HTML Audio 成功:', duration)
      return duration
    }
  } catch (error) {
    console.log('⚠️ HTML Audio 失败:', error.message)
  }
  
  // 尝试 Web Audio API
  try {
    const duration = await getAudioDurationFromBlobWithWebAudio(blob)
    if (duration > 0 && isFinite(duration)) {
      console.log('✅ Web Audio API 成功:', duration)
      return duration
    }
  } catch (error) {
    console.log('⚠️ Web Audio API 失败:', error.message)
  }
  
  // 使用录音计时器
  console.log('🔄 使用录音计时器:', recordingTime.value)
  return recordingTime.value
}

// 测试单个方法
const testHtmlAudioMethod = async () => {
  if (!recordedBlob.value) return
  
  testing.value.htmlAudio = true
  try {
    const duration = await getAudioDurationFromBlob(recordedBlob.value)
    testResults.value.push({
      method: 'HTML Audio',
      duration: duration + 's',
      success: true,
      error: ''
    })
  } catch (error) {
    testResults.value.push({
      method: 'HTML Audio',
      duration: '-',
      success: false,
      error: error.message
    })
  } finally {
    testing.value.htmlAudio = false
  }
}

const testWebAudioMethod = async () => {
  if (!recordedBlob.value) return
  
  testing.value.webAudio = true
  try {
    const duration = await getAudioDurationFromBlobWithWebAudio(recordedBlob.value)
    testResults.value.push({
      method: 'Web Audio API',
      duration: duration + 's',
      success: true,
      error: ''
    })
  } catch (error) {
    testResults.value.push({
      method: 'Web Audio API',
      duration: '-',
      success: false,
      error: error.message
    })
  } finally {
    testing.value.webAudio = false
  }
}

const testComprehensiveMethod = async () => {
  if (!recordedBlob.value) return
  
  testing.value.comprehensive = true
  try {
    const duration = await getAudioDurationComprehensive(recordedBlob.value)
    testResults.value.push({
      method: '综合方法',
      duration: duration + 's',
      success: true,
      error: ''
    })
    
    recordingDuration.value = duration
    durationStatus.value = '获取成功'
  } catch (error) {
    testResults.value.push({
      method: '综合方法',
      duration: '-',
      success: false,
      error: error.message
    })
    durationStatus.value = '获取失败'
  } finally {
    testing.value.comprehensive = false
  }
}

// 自动测试所有方法
const testAllMethods = async () => {
  await testHtmlAudioMethod()
  await testWebAudioMethod()
  await testComprehensiveMethod()
}

// 格式化文件大小
const formatFileSize = (bytes) => {
  if (!bytes || bytes <= 0) return '0 B'
  
  if (bytes < 1024) return `${bytes} B`
  if (bytes < 1024 * 1024) return `${(bytes / 1024).toFixed(1)} KB`
  return `${(bytes / (1024 * 1024)).toFixed(1)} MB`
}
</script>

<style scoped>
.recording-debug {
  padding: 20px;
}

.debug-section {
  margin-bottom: 30px;
}

.recording-controls {
  display: flex;
  align-items: center;
  gap: 12px;
  flex-wrap: wrap;
}

.debug-info {
  margin-top: 20px;
}
</style>
