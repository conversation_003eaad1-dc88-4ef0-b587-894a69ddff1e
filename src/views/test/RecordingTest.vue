<template>
  <div class="recording-test">
    <el-card header="录音功能测试">
      <div class="test-section">
        <h3>Web Audio API 录音测试</h3>
        
        <!-- 录音控制区域 -->
        <div class="recording-controls" style="margin-bottom: 20px;">
          <el-button 
            v-if="!isRecording && !recordedBlob" 
            type="success" 
            :loading="recordingInitializing"
            @click="startRecording"
          >
            <el-icon><Microphone /></el-icon>
            开始录音
          </el-button>
          
          <el-button 
            v-if="isRecording" 
            type="danger" 
            @click="stopRecording"
          >
            <el-icon><VideoPause /></el-icon>
            停止录音
          </el-button>
          
          <div v-if="isRecording" class="recording-indicator" style="display: inline-flex; align-items: center; gap: 6px; margin-left: 12px;">
            <div class="recording-dot"></div>
            <span style="color: #f56c6c; font-size: 14px;">录音中... {{ recordingTime }}s</span>
          </div>
          
          <el-button 
            v-if="recordedBlob && !isRecording" 
            type="warning" 
            @click="resetRecording"
            style="margin-left: 12px;"
          >
            重新录音
          </el-button>
        </div>
        
        <!-- 录音预览区域 -->
        <div v-if="recordedBlob && recordingPreviewUrl" class="recording-preview" style="margin-bottom: 20px;">
          <h4>录音预览</h4>
          <audio controls :src="recordingPreviewUrl" style="width: 100%; max-width: 400px;"></audio>
          <div style="margin-top: 10px;">
            <p>录音时长: {{ recordingTime }}秒</p>
            <p>文件大小: {{ (recordedBlob.size / 1024).toFixed(2) }} KB</p>
            <p>文件类型: {{ recordedBlob.type }}</p>
          </div>
        </div>
        
        <!-- 技术信息 -->
        <div class="tech-info">
          <h4>技术信息</h4>
          <el-descriptions :column="1" border>
            <el-descriptions-item label="浏览器支持">
              {{ browserSupport }}
            </el-descriptions-item>
            <el-descriptions-item label="支持的音频格式">
              {{ supportedFormats.join(', ') }}
            </el-descriptions-item>
            <el-descriptions-item label="推荐配置">
              采样率: 44.1kHz, 声道: 单声道, 比特率: 128kbps
            </el-descriptions-item>
          </el-descriptions>
        </div>
      </div>
    </el-card>
  </div>
</template>

<script setup>
import { ref, computed, onMounted, onUnmounted } from 'vue'
import { ElMessage } from 'element-plus'
import { Microphone, VideoPause } from '@element-plus/icons-vue'

// 录音相关状态
const isRecording = ref(false)
const recordingInitializing = ref(false)
const recordedBlob = ref(null)
const recordingPreviewUrl = ref('')
const recordingTime = ref(0)
const mediaRecorder = ref(null)
const recordingStream = ref(null)
const recordingTimer = ref(null)

// 浏览器支持检测
const browserSupport = computed(() => {
  if (!navigator.mediaDevices || !navigator.mediaDevices.getUserMedia) {
    return '不支持 - 缺少 MediaDevices API'
  }
  if (!window.MediaRecorder) {
    return '不支持 - 缺少 MediaRecorder API'
  }
  return '支持'
})

// 支持的音频格式
const supportedFormats = ref([])

// 检测支持的音频格式
const checkSupportedFormats = () => {
  const formats = [
    'audio/webm;codecs=opus',
    'audio/webm',
    'audio/mp4',
    'audio/ogg;codecs=opus',
    'audio/wav'
  ]
  
  supportedFormats.value = formats.filter(format => 
    MediaRecorder.isTypeSupported && MediaRecorder.isTypeSupported(format)
  )
}

// 开始录音
const startRecording = async () => {
  try {
    recordingInitializing.value = true
    
    // 请求麦克风权限
    const stream = await navigator.mediaDevices.getUserMedia({ 
      audio: {
        sampleRate: 44100,
        channelCount: 1,
        echoCancellation: true,
        noiseSuppression: true
      } 
    })
    
    recordingStream.value = stream
    
    // 创建MediaRecorder
    const options = {
      mimeType: 'audio/webm;codecs=opus',
      audioBitsPerSecond: 128000
    }
    
    // 检查浏览器支持的格式
    if (!MediaRecorder.isTypeSupported(options.mimeType)) {
      options.mimeType = 'audio/webm'
      if (!MediaRecorder.isTypeSupported(options.mimeType)) {
        options.mimeType = 'audio/mp4'
        if (!MediaRecorder.isTypeSupported(options.mimeType)) {
          delete options.mimeType
        }
      }
    }
    
    mediaRecorder.value = new MediaRecorder(stream, options)
    const chunks = []
    
    mediaRecorder.value.ondataavailable = (event) => {
      if (event.data.size > 0) {
        chunks.push(event.data)
      }
    }
    
    mediaRecorder.value.onstop = () => {
      const blob = new Blob(chunks, { type: 'audio/webm' })
      recordedBlob.value = blob
      recordingPreviewUrl.value = URL.createObjectURL(blob)
      
      // 停止所有音轨
      if (recordingStream.value) {
        recordingStream.value.getTracks().forEach(track => track.stop())
        recordingStream.value = null
      }
    }
    
    // 开始录音
    mediaRecorder.value.start()
    isRecording.value = true
    recordingTime.value = 0
    
    // 开始计时
    recordingTimer.value = setInterval(() => {
      recordingTime.value++
    }, 1000)
    
    ElMessage.success('录音已开始')
    
  } catch (error) {
    console.error('录音启动失败:', error)
    if (error.name === 'NotAllowedError') {
      ElMessage.error('请允许访问麦克风权限')
    } else if (error.name === 'NotFoundError') {
      ElMessage.error('未找到麦克风设备')
    } else {
      ElMessage.error('录音启动失败：' + error.message)
    }
  } finally {
    recordingInitializing.value = false
  }
}

// 停止录音
const stopRecording = () => {
  if (mediaRecorder.value && isRecording.value) {
    mediaRecorder.value.stop()
    isRecording.value = false
    
    // 清除计时器
    if (recordingTimer.value) {
      clearInterval(recordingTimer.value)
      recordingTimer.value = null
    }
    
    ElMessage.success('录音已停止')
  }
}

// 重新录音
const resetRecording = () => {
  // 清理之前的录音数据
  if (recordingPreviewUrl.value) {
    URL.revokeObjectURL(recordingPreviewUrl.value)
    recordingPreviewUrl.value = ''
  }
  recordedBlob.value = null
  recordingTime.value = 0
  
  // 如果正在录音，先停止
  if (isRecording.value) {
    stopRecording()
  }
}

// 组件挂载时检测支持的格式
onMounted(() => {
  checkSupportedFormats()
})

// 组件卸载时清理资源
onUnmounted(() => {
  if (recordingStream.value) {
    recordingStream.value.getTracks().forEach(track => track.stop())
  }
  if (recordingTimer.value) {
    clearInterval(recordingTimer.value)
  }
  if (recordingPreviewUrl.value) {
    URL.revokeObjectURL(recordingPreviewUrl.value)
  }
})
</script>

<style scoped>
.recording-test {
  padding: 20px;
}

.test-section {
  margin-bottom: 30px;
}

.recording-controls {
  display: flex;
  align-items: center;
  gap: 12px;
  flex-wrap: wrap;
}

.recording-indicator {
  .recording-dot {
    width: 8px;
    height: 8px;
    background-color: #f56c6c;
    border-radius: 50%;
    animation: recording-pulse 1.5s ease-in-out infinite;
  }
}

@keyframes recording-pulse {
  0% {
    opacity: 1;
    transform: scale(1);
  }
  50% {
    opacity: 0.5;
    transform: scale(1.2);
  }
  100% {
    opacity: 1;
    transform: scale(1);
  }
}

.recording-preview {
  padding: 16px;
  background: #f0f9ff;
  border-radius: 8px;
  border: 1px solid #bfdbfe;
}

.tech-info {
  margin-top: 20px;
}
</style>
