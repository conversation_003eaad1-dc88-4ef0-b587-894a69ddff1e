<template>
  <div class="batch-create-container">
    <div class="page-header">
      <div class="header-left">
        <h2>批量创建剧本</h2>
        <div class="stats-info">
          <span class="stat-item">剧本数量: {{ scripts.length }}</span>
          <span class="stat-item">总话题数: {{ getTotalTopicsCount() }}</span>
          <span class="stat-item">总台词数: {{ getTotalLinesCount() }}</span>
        </div>
      </div>
      <div class="header-actions">
        <el-button @click="addScript">添加剧本</el-button>
        <el-button type="warning" @click="clearAllScripts">清空所有</el-button>
        <el-button type="primary" :loading="submitLoading" @click="submitBatch">批量提交</el-button>
      </div>
    </div>

    <div class="scripts-list">
      <el-card v-for="(script, index) in scripts" :key="index" class="script-card">
        <template #header>
          <div class="card-header">
            <div class="card-title" @click="script.collapsed = !script.collapsed" style="cursor: pointer; flex: 1;">
              <el-button
                :icon="script.collapsed ? 'ArrowRight' : 'ArrowDown'"
                link
                @click.stop="script.collapsed = !script.collapsed"
                style="margin-right: 8px;"
              />
              <span>剧本 {{ index + 1 }}</span>
              <span v-if="script.title" class="script-title-preview">- {{ script.title }}</span>
            </div>
            <div class="card-actions">
              <el-button type="danger" link @click.stop="removeScript(index)">删除</el-button>
            </div>
          </div>
        </template>

        <div v-show="!script.collapsed">
          <el-form :model="script" :rules="scriptRules" :ref="el => setScriptFormRef(el, index)" label-width="120px">
            <!-- 基本信息 -->
            <div class="form-section">
              <h4>基本信息</h4>
              <el-form-item label="剧本标题" prop="title">
                <el-input v-model="script.title" placeholder="请输入剧本标题" />
              </el-form-item>

              <el-form-item label="作者ID" prop="author_id">
                <el-input-number v-model="script.author_id" :min="1" placeholder="请输入作者ID" style="width: 100%" />
              </el-form-item>

              <!-- 封面 -->
              <el-form-item label="封面" prop="cover">
                <div class="cover-upload">
                  <el-row :gutter="8">
                    <el-col :span="10">
                      <el-input
                        v-model="script.cover"
                        placeholder="请输入封面地址"
                        @blur="onCoverUrlBlur(index)"
                      />
                    </el-col>
                    <el-col :span="8">
                      <el-select
                        v-model="script.selectedCoverId"
                        placeholder="选择已有封面"
                        filterable
                        clearable
                        @change="onCoverSelect(index)"
                        style="width: 100%"
                      >
                        <el-option
                          v-for="cover in covers"
                          :key="cover.id"
                          :label="`${cover.description || '无描述'} (ID:${cover.id})`"
                          :value="cover.id"
                        >
                          <div style="display: flex; align-items: center; gap: 8px;">
                            <el-image
                              :src="cover.url"
                              fit="cover"
                              style="width: 30px; height: 30px; border-radius: 4px; flex-shrink: 0;"
                            />
                            <div style="flex: 1; min-width: 0;">
                              <div style="font-size: 14px; overflow: hidden; text-overflow: ellipsis; white-space: nowrap;">
                                {{ cover.description || '无描述' }}
                              </div>
                              <div style="font-size: 12px; color: #909399;">ID: {{ cover.id }}</div>
                            </div>
                          </div>
                        </el-option>
                      </el-select>
                    </el-col>
                    <el-col :span="6">
                      <el-upload
                        :auto-upload="false"
                        :show-file-list="false"
                        :on-change="(file) => handleCoverUpload(file, index)"
                        accept="image/*"
                        style="width: 100%"
                      >
                        <el-button type="primary" :loading="script.coverUploading" style="width: 100%">
                          {{ script.coverUploading ? '上传中...' : '上传封面' }}
                        </el-button>
                      </el-upload>
                    </el-col>
                  </el-row>

                  <div v-if="script.cover" class="cover-preview">
                    <el-image
                      :src="getCoverDisplayUrl(script.cover)"
                      :preview-src-list="[getCoverDisplayUrl(script.cover)]"
                      fit="cover"
                      style="width: 80px; height: 80px; border-radius: 6px; margin-top: 8px;"
                      :preview-teleported="true"
                    />
                  </div>
                </div>
              </el-form-item>

              <el-form-item label="主题色" prop="theme_color">
                <div class="theme-color-input">
                  <el-color-picker
                    v-model="script.theme_color"
                    show-alpha
                    :predefine="predefineColors"
                    style="margin-right: 10px;"
                  />
                  <el-input
                    v-model="script.theme_color"
                    placeholder="请选择或输入主题色"
                    style="flex: 1;"
                  />
                </div>
              </el-form-item>

              <!-- 背景音乐 -->
              <el-form-item label="音乐地址" prop="bgm_url">
                <div style="display: flex; gap: 8px; align-items: center;">
                  <el-input v-model="script.bgm_url" placeholder="请输入背景音乐地址" style="flex: 1;" />
                  <el-upload
                    :auto-upload="false"
                    :show-file-list="false"
                    :on-change="(file) => handleAudioUpload(file, index)"
                    accept="audio/*"
                  >
                    <el-button type="primary" :loading="script.audioUploading">
                      {{ script.audioUploading ? '上传中...' : '上传音频' }}
                    </el-button>
                  </el-upload>
                </div>
                <div v-if="script.bgm_url" class="bgm-preview">
                  <audio
                    :src="script.bgm_url"
                    preload="metadata"
                    style="width: 100%; margin-top: 10px;"
                    controls
                    @loadedmetadata="(event) => onAudioLoaded(event, index)"
                  ></audio>
                </div>
              </el-form-item>

              <el-form-item label="音乐时长" prop="bgm_duration">
                <el-input-number v-model="script.bgm_duration" :min="0" placeholder="请输入背景音乐时长(秒)" style="width: 100%" />
              </el-form-item>
            </div>

            <!-- 角色资源 -->
            <div class="form-section">
              <h4>角色资源</h4>
              <div v-for="(asset, assetIndex) in script.character_assets" :key="assetIndex" class="character-asset-item">
                <el-row :gutter="8">
                  <el-col :span="8">
                    <el-select
                      v-model="asset.character_id"
                      placeholder="选择角色"
                      filterable
                      @change="onCharacterChange(asset, index)"
                      style="width: 100%"
                    >
                      <el-option
                        v-for="character in getAvailableCharacters(index, assetIndex)"
                        :key="character.id"
                        :label="character.name"
                        :value="character.id"
                      />
                    </el-select>
                  </el-col>
                  <el-col :span="8">
                    <el-select
                      v-model="asset.character_asset_id"
                      placeholder="选择角色资源"
                      filterable
                      :disabled="!asset.character_id"
                      style="width: 100%"
                    >
                      <el-option
                        v-for="assetItem in asset.availableAssets"
                        :key="assetItem.id"
                        :label="assetItem.name"
                        :value="assetItem.id"
                      />
                    </el-select>
                  </el-col>
                  <el-col :span="8">
                    <el-button type="danger" @click="removeCharacterAsset(index, assetIndex)">删除</el-button>
                  </el-col>
                </el-row>
                <div v-if="isCharacterDuplicated(index, assetIndex)" class="character-warning">
                  <el-alert
                    title="该角色已被选择，请选择其他角色"
                    type="warning"
                    :closable="false"
                    show-icon
                    style="margin-top: 8px;"
                  />
                </div>
              </div>
              <el-button type="primary" link @click="addCharacterAsset(index)">添加角色资源</el-button>
            </div>

            <!-- 话题 -->
            <div class="form-section">
              <h4>话题</h4>
              <el-form-item label="选择话题">
                <el-select
                  v-model="script.selected_topics"
                  multiple
                  filterable
                  placeholder="请选择话题"
                  style="width: 100%"
                  @change="onTopicChange(index)"
                >
                  <el-option
                    v-for="topic in topics"
                    :key="topic.id"
                    :label="topic.name"
                    :value="topic.name"
                  >
                    <span>{{ topic.name }}</span>
                    <span style="float: right; color: #8492a6; font-size: 13px;">ID: {{ topic.id }}</span>
                  </el-option>
                  <el-option v-if="topics.length === 0" disabled value="" label="暂无话题数据" />
                </el-select>
              </el-form-item>

              <el-form-item label="自定义话题">
                <div style="display: flex; gap: 8px;">
                  <el-input
                    v-model="script.custom_topic"
                    placeholder="输入自定义话题名称，按回车添加"
                    style="flex: 1;"
                    @keyup.enter="addCustomTopic(index)"
                  />
                  <el-button type="primary" @click="addCustomTopic(index)">添加</el-button>
                </div>
              </el-form-item>

              <div v-if="script.topic_names.length > 0">
                <div style="margin-bottom: 8px; font-size: 14px; color: #606266;">已选话题：</div>
                <el-tag
                  v-for="(topic, topicIndex) in script.topic_names"
                  :key="topicIndex"
                  closable
                  @close="removeTopicName(index, topicIndex)"
                  style="margin-right: 8px; margin-bottom: 8px;"
                >
                  {{ topic }}
                </el-tag>
              </div>
            </div>

            <!-- 台词列表 -->
            <div class="form-section">
              <h4>台词列表</h4>
              <div v-for="(line, lineIndex) in script.lines" :key="lineIndex" class="line-item">
                <el-row :gutter="8">
                  <el-col :span="12">
                    <el-input
                      v-model="line.content"
                      placeholder="请输入台词内容"
                      type="textarea"
                      :rows="2"
                    />
                  </el-col>
                  <el-col :span="4">
                    <el-select
                      v-model="line.character_id"
                      placeholder="选择角色"
                      filterable
                      @change="onLineCharacterChange(line, index)"
                      style="width: 100%"
                    >
                      <el-option
                        v-for="asset in script.character_assets.filter(a => a.character_id)"
                        :key="asset.character_id"
                        :label="getCharacterName(asset.character_id)"
                        :value="asset.character_id"
                      />
                    </el-select>
                  </el-col>
                  <el-col :span="4">
                    <el-select
                      v-model="line.character_asset_id"
                      placeholder="角色资源"
                      filterable
                      :disabled="!line.character_id"
                      style="width: 100%"
                    >
                      <el-option
                        v-for="asset in getAvailableAssetsForCharacter(line.character_id, index)"
                        :key="asset.id"
                        :label="asset.name"
                        :value="asset.id"
                      />
                    </el-select>
                  </el-col>
                  <el-col :span="4">
                    <el-button type="danger" @click="removeLine(index, lineIndex)">删除</el-button>
                  </el-col>
                </el-row>
              </div>
              <el-button type="primary" link @click="addLine(index)">添加台词</el-button>
            </div>
          </el-form>
        </div>
      </el-card>
    </div>

    <div v-if="scripts.length === 0" class="empty-state">
      <el-empty description="暂无剧本，点击添加剧本开始创建">
        <el-button type="primary" @click="addScript">添加剧本</el-button>
      </el-empty>
    </div>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted, onBeforeUnmount, watch, computed } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { batchCreateScript } from '@/api/scripts/scripts'
import { getCharacterList } from '@/api/characters/characters'
import { getCharacterAssetsList } from '@/api/characters/characterAssets'
import { getTopicList } from '@/api/scripts/topics'
import { getCoverList } from '@/api/scripts/covers'
import { getUserInfoByCreationUserId } from '@/api/users/user'
import { uploadToOSS, checkFileSize } from '@/utils/oss'
import { extractThemeColorFromImage } from '@/utils/format'
import { useUserStore } from '@/pinia/modules/user'

defineOptions({
  name: 'BatchCreateScript'
})

// 用户状态管理
const userStore = useUserStore()

// 计算当前用户是否为创作者角色（角色ID：111）
const isCreator = computed(() => {
  return userStore.userInfo?.authority?.authorityId === 111
})

// 数据持久化键名
const STORAGE_KEY = 'batch_create_scripts_data'

// 预定义颜色
const predefineColors = ref([
  '#ff4500',
  '#ff8c00',
  '#ffd700',
  '#90ee90',
  '#00ced1',
  '#1e90ff',
  '#c71585',
  '#ff1493',
  '#00ff00',
  '#ffff00',
  '#ff00ff',
  '#00ffff',
  '#000000',
  '#ffffff',
  '#808080'
])

// 响应式数据
const scripts = ref([])
const characters = ref([])
const topics = ref([])
const covers = ref([])
const submitLoading = ref(false)
const scriptFormRefs = ref([])

// 设置表单引用
const setScriptFormRef = (el, index) => {
  if (el) {
    scriptFormRefs.value[index] = el
  }
}

// 表单验证规则
const scriptRules = reactive({
  title: [{ required: true, message: '请输入剧本标题', trigger: 'blur' }],
  cover: [{ required: true, message: '请输入封面地址', trigger: 'blur' }],
  author_id: [{ required: true, message: '请输入作者ID', trigger: 'blur' }]
})

// 创建默认剧本对象
const createDefaultScript = (authorId = 100000) => ({
  title: '',
  cover: '',
  author_id: authorId, // 作者ID，可以通过参数传入
  bgm_url: '',
  bgm_duration: 0,
  theme_color: '',
  character_assets: [],
  selected_topics: [], // 选中的话题
  topic_names: [], // 话题名称数组
  lines: [],
  custom_topic: '', // 自定义话题输入框
  collapsed: false, // 折叠状态
  selectedCoverId: null, // 选中的封面ID
  coverUploading: false,
  audioUploading: false
})

// 创建默认角色资源对象
const createDefaultCharacterAsset = () => ({
  character_id: null,
  character_asset_id: null,
  availableAssets: []
})

// 创建默认台词对象
const createDefaultLine = () => ({
  content: '',
  sort: 0,
  character_id: null,
  character_asset_id: null
})

// 保存数据到localStorage
const saveDataToStorage = () => {
  try {
    const dataToSave = {
      scripts: scripts.value,
      timestamp: Date.now()
    }
    localStorage.setItem(STORAGE_KEY, JSON.stringify(dataToSave))
    console.log('数据已保存到localStorage')
  } catch (error) {
    console.warn('保存数据失败:', error)
  }
}

// 从localStorage恢复数据
const loadDataFromStorage = () => {
  try {
    const savedData = localStorage.getItem(STORAGE_KEY)
    if (savedData) {
      const parsedData = JSON.parse(savedData)
      // 检查数据是否过期（24小时）
      const isExpired = Date.now() - parsedData.timestamp > 24 * 60 * 60 * 1000

      if (!isExpired && parsedData.scripts && parsedData.scripts.length > 0) {
        scripts.value = parsedData.scripts
        console.log('从localStorage恢复数据:', scripts.value.length, '个剧本')
        // 显示恢复提示
        setTimeout(() => {
          ElMessage.success(`已恢复 ${scripts.value.length} 个未提交的剧本数据`)
        }, 1000)
        return true
      }
    }
  } catch (error) {
    console.warn('恢复数据失败:', error)
  }
  return false
}

// 清除localStorage数据
const clearStorageData = () => {
  try {
    localStorage.removeItem(STORAGE_KEY)
    console.log('已清除localStorage数据')
  } catch (error) {
    console.warn('清除数据失败:', error)
  }
}

// 获取创作者关联的业务用户信息
const getCreatorUserInfo = async () => {
  if (!isCreator.value) {
    console.log('当前用户不是创作者角色，跳过获取用户信息')
    return null
  }

  try {
    console.log('=== 创作者角色检测到，开始获取关联的业务用户信息 ===')
    const res = await getUserInfoByCreationUserId()

    if (res.code === 0 && res.data) {
      console.log('获取创作者关联用户信息成功:', res.data)
      return res.data
    } else {
      console.warn('获取创作者关联用户信息失败:', res.msg || '未知错误')
      ElMessage.warning('获取关联用户信息失败，请手动填写作者ID')
      return null
    }
  } catch (error) {
    console.error('获取创作者关联用户信息异常:', error)
    ElMessage.warning('获取关联用户信息失败，请手动填写作者ID')
    return null
  }
}

// 监听数据变化，自动保存
watch(scripts, () => {
  saveDataToStorage()
}, { deep: true })

// 初始化数据
onMounted(async () => {
  console.log('=== 批量创建页面初始化 ===')

  // 加载基础数据（角色、话题、封面）
  await loadCharacters()
  await loadTopics()
  await loadCovers()

  console.log('=== 初始化完成 ===')
  console.log('角色数量:', characters.value.length)
  console.log('话题数量:', topics.value.length)
  console.log('封面数量:', covers.value.length)

  // 检测创作者角色并获取关联用户信息
  let defaultAuthorId = 100000 // 默认作者ID
  if (isCreator.value) {
    console.log('检测到创作者角色，尝试获取关联的业务用户信息')
    const creatorUserInfo = await getCreatorUserInfo()
    if (creatorUserInfo && creatorUserInfo.user_id) {
      defaultAuthorId = creatorUserInfo.user_id
      console.log('创作者关联用户ID:', defaultAuthorId)
      ElMessage.success(`已自动填充作者ID: ${defaultAuthorId}`)
    }
  }

  // 尝试从localStorage恢复数据
  const hasRestoredData = loadDataFromStorage()

  // 如果没有恢复到数据，添加一个默认剧本
  if (!hasRestoredData) {
    addScript(defaultAuthorId)
    console.log('添加默认剧本，作者ID:', defaultAuthorId)
  } else {
    console.log('数据恢复成功，当前剧本数量:', scripts.value.length)
    // 如果是创作者角色且恢复了数据，需要更新所有剧本的作者ID
    if (isCreator.value && defaultAuthorId !== 100000) {
      scripts.value.forEach(script => {
        if (script.author_id === 100000) { // 只更新默认值
          script.author_id = defaultAuthorId
        }
      })
      console.log('已更新恢复数据中的作者ID')
    }
  }
})

// 页面卸载前保存数据
onBeforeUnmount(() => {
  saveDataToStorage()
})

// 加载角色列表
const loadCharacters = async () => {
  try {
    const res = await getCharacterList({ page: 1, pageSize: 1000 })
    if (res.code === 0) {
      characters.value = res.data.list || []
    }
  } catch (error) {
    ElMessage.error('加载角色列表失败')
  }
}

// 加载话题列表
const loadTopics = async () => {
  try {
    console.log('=== 开始加载话题列表 ===')
    const res = await getTopicList({ page: 1, pageSize: 1000 })
    if (res.code === 0) {
      topics.value = res.data.list || []
      console.log('话题列表加载成功:', topics.value.length, '个话题')
      console.log('话题数据示例:', topics.value.slice(0, 3))
    } else {
      console.error('话题列表加载失败:', res)
      ElMessage.error('加载话题列表失败')
    }
  } catch (error) {
    console.error('话题列表加载异常:', error)
    ElMessage.error('加载话题列表失败')
  }
}

// 加载封面列表
const loadCovers = async () => {
  try {
    console.log('=== 开始加载封面列表 ===')
    const res = await getCoverList({ page: 1, pageSize: 1000, status: 1 }) // 只获取正常状态的封面
    if (res.code === 0) {
      covers.value = res.data.list || []
      console.log('封面列表加载成功:', covers.value.length, '个封面')
      console.log('封面数据示例:', covers.value.slice(0, 3))
    } else {
      console.error('封面列表加载失败:', res)
      ElMessage.error('加载封面列表失败')
    }
  } catch (error) {
    console.error('封面列表加载异常:', error)
    ElMessage.error('加载封面列表失败')
  }
}

// 获取总话题数
const getTotalTopicsCount = () => {
  return scripts.value.reduce((total, script) => {
    return total + (script.topic_names ? script.topic_names.length : 0)
  }, 0)
}

// 获取总台词数
const getTotalLinesCount = () => {
  return scripts.value.reduce((total, script) => {
    return total + (script.lines ? script.lines.length : 0)
  }, 0)
}

// 添加剧本
const addScript = (authorId) => {
  // 如果是创作者角色且没有传入作者ID，尝试获取当前创作者的用户ID
  if (isCreator.value && !authorId) {
    // 从现有剧本中获取作者ID（如果有的话）
    if (scripts.value.length > 0 && scripts.value[0].author_id !== 100000) {
      authorId = scripts.value[0].author_id
    } else {
      // 异步获取创作者用户信息
      getCreatorUserInfo().then(creatorUserInfo => {
        if (creatorUserInfo && creatorUserInfo.user_id) {
          // 更新最新添加的剧本的作者ID
          const lastScript = scripts.value[scripts.value.length - 1]
          if (lastScript && lastScript.author_id === 100000) {
            lastScript.author_id = creatorUserInfo.user_id
            console.log('已更新新添加剧本的作者ID:', creatorUserInfo.user_id)
          }
        }
      })
    }
  }

  scripts.value.push(createDefaultScript(authorId))
}

// 删除剧本
const removeScript = (index) => {
  ElMessageBox.confirm('确定要删除这个剧本吗？', '提示', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    type: 'warning'
  }).then(() => {
    scripts.value.splice(index, 1)
    scriptFormRefs.value.splice(index, 1)
  })
}

// 清空所有剧本
const clearAllScripts = async () => {
  ElMessageBox.confirm('确定要清空所有剧本数据吗？此操作不可恢复！', '警告', {
    confirmButtonText: '确定清空',
    cancelButtonText: '取消',
    type: 'warning'
  }).then(async () => {
    scripts.value = []
    scriptFormRefs.value = []
    // 清除localStorage数据
    clearStorageData()

    // 获取默认作者ID
    let defaultAuthorId = 100000
    if (isCreator.value) {
      const creatorUserInfo = await getCreatorUserInfo()
      if (creatorUserInfo && creatorUserInfo.user_id) {
        defaultAuthorId = creatorUserInfo.user_id
      }
    }

    // 添加一个新的空剧本
    addScript(defaultAuthorId)
    ElMessage.success('已清空所有数据')
  })
}

// 添加角色资源
const addCharacterAsset = (scriptIndex) => {
  scripts.value[scriptIndex].character_assets.push(createDefaultCharacterAsset())
}

// 删除角色资源
const removeCharacterAsset = (scriptIndex, assetIndex) => {
  scripts.value[scriptIndex].character_assets.splice(assetIndex, 1)
}

// 获取可用的角色列表（排除已选择的角色）
const getAvailableCharacters = (scriptIndex, currentAssetIndex) => {
  const script = scripts.value[scriptIndex]
  const selectedCharacterIds = script.character_assets
    .map((asset, index) => index !== currentAssetIndex ? asset.character_id : null)
    .filter(id => id !== null)

  return characters.value.filter(character =>
    !selectedCharacterIds.includes(character.id)
  )
}

// 检查角色是否重复
const isCharacterDuplicated = (scriptIndex, currentAssetIndex) => {
  const script = scripts.value[scriptIndex]
  const currentAsset = script.character_assets[currentAssetIndex]

  if (!currentAsset.character_id) return false

  const duplicateCount = script.character_assets.filter((asset, index) =>
    index !== currentAssetIndex && asset.character_id === currentAsset.character_id
  ).length

  return duplicateCount > 0
}

// 角色变更时加载对应的角色资源
const onCharacterChange = async (asset, scriptIndex) => {
  if (!asset.character_id) {
    asset.availableAssets = []
    asset.character_asset_id = null
    return
  }

  // 检查是否有重复角色
  const script = scripts.value[scriptIndex]
  const duplicateAssets = script.character_assets.filter(a =>
    a !== asset && a.character_id === asset.character_id
  )

  if (duplicateAssets.length > 0) {
    ElMessage.warning('该角色已被选择，请选择其他角色')
    // 不清空选择，让用户看到警告提示
  }

  try {
    const res = await getCharacterAssetsList({
      character_id: asset.character_id,
      preset_status: 1, // 只获取已预设的角色资源
      page: 1,
      pageSize: 1000
    })
    if (res.code === 0) {
      asset.availableAssets = res.data.list || []

      // 如果只有一个角色资源，自动选中
      if (asset.availableAssets.length === 1) {
        asset.character_asset_id = asset.availableAssets[0].id
        console.log('自动选中唯一角色资源:', asset.availableAssets[0])
      } else {
        asset.character_asset_id = null
      }
    }
  } catch (error) {
    console.error('加载角色资源失败:', error)
    ElMessage.error('加载角色资源失败')
  }
}

// 话题选择变化
const onTopicChange = (scriptIndex) => {
  const script = scripts.value[scriptIndex]
  console.log('=== 话题选择变化 ===', {
    scriptIndex,
    selectedTopics: script.selected_topics,
    availableTopics: topics.value.length
  })

  // 重新构建话题名称数组：先清空，再添加选中的话题
  script.topic_names = [...script.selected_topics]

  console.log('更新后的话题名称:', script.topic_names)
}

// 添加自定义话题
const addCustomTopic = (scriptIndex) => {
  const script = scripts.value[scriptIndex]
  const customTopic = script.custom_topic.trim()

  if (customTopic) {
    console.log('=== 添加自定义话题 ===', { scriptIndex, customTopic })

    // 检查是否已存在
    if (!script.topic_names.includes(customTopic)) {
      script.topic_names.push(customTopic)
      console.log('自定义话题添加成功:', script.topic_names)
    } else {
      ElMessage.warning('该话题已存在')
    }

    // 清空输入框
    script.custom_topic = ''
  }
}

// 删除话题名称
const removeTopicName = (scriptIndex, topicIndex) => {
  const script = scripts.value[scriptIndex]
  const removedTopic = script.topic_names[topicIndex]

  // 从话题名称数组中删除
  script.topic_names.splice(topicIndex, 1)

  // 如果是从选中话题中删除的，也要从selected_topics中移除
  const selectedIndex = script.selected_topics.indexOf(removedTopic)
  if (selectedIndex > -1) {
    script.selected_topics.splice(selectedIndex, 1)
  }

  console.log('删除话题:', removedTopic, '剩余话题:', script.topic_names)
}

// 添加台词
const addLine = (scriptIndex) => {
  scripts.value[scriptIndex].lines.push(createDefaultLine())
}

// 删除台词
const removeLine = (scriptIndex, lineIndex) => {
  scripts.value[scriptIndex].lines.splice(lineIndex, 1)
}

// 台词角色变化
const onLineCharacterChange = (line, scriptIndex) => {
  // 当角色变化时，重置角色资源选择
  line.character_asset_id = null

  // 如果该角色只有一个资源，自动选中
  const script = scripts.value[scriptIndex]
  const characterAsset = script.character_assets.find(asset => asset.character_id === line.character_id)
  if (characterAsset && characterAsset.availableAssets.length === 1) {
    line.character_asset_id = characterAsset.availableAssets[0].id
  }
}

// 获取角色名称
const getCharacterName = (characterId) => {
  const character = characters.value.find(c => c.id === characterId)
  return character ? character.name : `角色${characterId}`
}

// 获取角色可用资源
const getAvailableAssetsForCharacter = (characterId, scriptIndex) => {
  if (!characterId) return []

  const script = scripts.value[scriptIndex]
  const characterAsset = script.character_assets.find(asset => asset.character_id === characterId)
  return characterAsset ? characterAsset.availableAssets : []
}

// 获取封面显示URL（处理URI和完整URL）
const getCoverDisplayUrl = (coverPath) => {
  if (!coverPath) return ''

  // 如果已经是完整URL，直接返回
  if (coverPath.startsWith('http://') || coverPath.startsWith('https://')) {
    return coverPath
  }

  // 如果是URI路径，需要拼接域名
  // 这里需要根据实际的图片服务域名进行配置
  const imageBaseUrl = import.meta.env.VITE_IMAGE_BASE_URL || 'https://your-image-domain.com'
  return `${imageBaseUrl}${coverPath.startsWith('/') ? '' : '/'}${coverPath}`
}

// 封面地址失焦处理
const onCoverUrlBlur = (index) => {
  const script = scripts.value[index]
  if (script.cover) {
    // 处理URI格式
    try {
      const url = new URL(script.cover)
      script.cover = url.pathname
    } catch (error) {
      // 如果不是完整URL，保持原样
      console.log('封面地址不是完整URL，保持原样:', script.cover)
    }
  }
}

// 封面选择处理
const onCoverSelect = (index) => {
  const script = scripts.value[index]
  const selectedCover = covers.value.find(cover => cover.id === script.selectedCoverId)
  if (selectedCover) {
    // 提取URI部分
    try {
      const url = new URL(selectedCover.url)
      script.cover = url.pathname
    } catch (error) {
      script.cover = selectedCover.url
    }
    script.theme_color = selectedCover.theme_color || ''
    console.log('选择封面:', selectedCover)
  }
}

// 处理封面上传
const handleCoverUpload = async (file, index) => {
  const script = scripts.value[index]
  try {
    script.coverUploading = true

    // 检查文件大小
    checkFileSize(file.raw, 10) // 最大10MB

    // 上传到OSS
    const result = await uploadToOSS(file.raw, 'image', 'scripts/covers')

    // 更新表单数据 - 只保留URI部分
    try {
      const url = new URL(result.url)
      script.cover = url.pathname
    } catch (error) {
      // 如果不是完整URL，保持原样
      script.cover = result.url
    }

    // 提取主题色 - 使用完整URL进行提取
    try {
      const themeColor = await extractThemeColorFromImage(result.url)
      script.theme_color = themeColor
      ElMessage.success('封面上传成功，已自动提取主题色')
    } catch (colorError) {
      console.warn('主题色提取失败:', colorError)
      ElMessage.success('封面上传成功')
    }

  } catch (error) {
    ElMessage.error(error.message || '上传失败')
  } finally {
    script.coverUploading = false
  }
}

// 处理音频上传
const handleAudioUpload = async (file, index) => {
  const script = scripts.value[index]
  try {
    script.audioUploading = true

    // 检查文件大小
    checkFileSize(file.raw, 50) // 最大50MB

    // 上传到OSS
    const result = await uploadToOSS(file.raw, 'audio', 'scripts/bgm')

    // 更新表单数据
    script.bgm_url = result.url

    ElMessage.success('音频上传成功')

  } catch (error) {
    ElMessage.error(error.message || '上传失败')
  } finally {
    script.audioUploading = false
  }
}

// 音频加载完成事件
const onAudioLoaded = (event, index) => {
  const audio = event.target
  const script = scripts.value[index]
  if (audio.duration && !isNaN(audio.duration) && isFinite(audio.duration)) {
    // 自动填充音频时长（秒）
    script.bgm_duration = Math.round(audio.duration)
  }
}

// 验证单个剧本
const validateScript = async (index) => {
  const formRef = scriptFormRefs.value[index]
  if (!formRef) {
    ElMessage.error(`剧本${index + 1}表单引用不存在`)
    return false
  }

  try {
    await formRef.validate()
    return true
  } catch (error) {
    ElMessage.error(`剧本${index + 1}验证失败，请检查必填项`)
    return false
  }
}

// 批量提交
const submitBatch = async () => {
  if (scripts.value.length === 0) {
    ElMessage.warning('请至少添加一个剧本')
    return
  }

  // 验证所有剧本
  for (let i = 0; i < scripts.value.length; i++) {
    const isValid = await validateScript(i)
    if (!isValid) {
      return
    }
  }

  try {
    submitLoading.value = true

    // 构建提交数据
    const submitData = {
      scripts: scripts.value.map((script, index) => {
        const topicNames = script.topic_names.filter(name => name && name.trim())
        console.log(`剧本${index + 1}话题数据:`, {
          selected_topics: script.selected_topics,
          topic_names: script.topic_names,
          filtered_topic_names: topicNames
        })

        return {
          title: script.title.trim(),
          cover: script.cover.trim(),
          author_id: script.author_id,
          character_assets: script.character_assets.filter(asset =>
            asset.character_id && asset.character_asset_id
          ).map(asset => ({
            character_id: asset.character_id,
            character_asset_id: asset.character_asset_id
          })),
          topic_names: topicNames,
          lines: script.lines.filter(line =>
            line.content.trim() && line.character_id
          ).map(line => ({
            content: line.content.trim(),
            sort: line.sort || 0,
            character_id: line.character_id,
            character_asset_id: line.character_asset_id || 0
          })),
          bgm_url: script.bgm_url.trim(),
          bgm_duration: script.bgm_duration || 0,
          theme_color: script.theme_color.trim()
        }
      })
    }

    console.log('=== 批量创建剧本提交数据 ===', submitData)

    const res = await batchCreateScript(submitData)
    if (res.code === 0) {
      const result = res.data
      ElMessage.success(
        `批量创建完成！成功：${result.success_count}个，失败：${result.fail_count}个`
      )

      if (result.fail_count > 0 && result.errors?.length > 0) {
        console.error('创建失败的剧本:', result.errors)
        ElMessage.warning('部分剧本创建失败，请查看控制台了解详情')
      }

      // 成功后清空表单，重新开始
      if (result.success_count > 0) {
        // 立即清除localStorage数据
        clearStorageData()
        setTimeout(() => {
          // 清空所有剧本数据
          scripts.value = []
          // 添加一个新的空剧本
          addScript()
          ElMessage.success('页面已重置，可以继续创建新的剧本')
        }, 2000)
      }
    }
  } catch (error) {
    console.error('批量创建剧本失败:', error)
    ElMessage.error('批量创建失败')
  } finally {
    submitLoading.value = false
  }
}
</script>

<style scoped>
.batch-create-container {
  padding: 20px;
  max-width: 1200px;
  margin: 0 auto;
}

.page-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 20px;
  padding: 20px;
  background: white;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.header-left h2 {
  margin: 0 0 10px 0;
  color: #303133;
}

.stats-info {
  display: flex;
  gap: 20px;
  font-size: 14px;
  color: #606266;
}

.stat-item {
  padding: 4px 8px;
  background: #f5f7fa;
  border-radius: 4px;
}

.header-actions {
  display: flex;
  gap: 10px;
}

.scripts-list {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.script-card {
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.card-title {
  display: flex;
  align-items: center;
  font-weight: 500;
  user-select: none;
  transition: background-color 0.2s ease;
  padding: 4px 8px;
  border-radius: 4px;
  margin: -4px -8px;
}

.card-title:hover {
  background-color: rgba(64, 158, 255, 0.1);
}

.script-title-preview {
  color: #909399;
  font-weight: normal;
  margin-left: 8px;
}

.form-section {
  margin-bottom: 20px;
  padding: 16px;
  background: #fafafa;
  border-radius: 6px;
  border-left: 4px solid #409eff;
}

.form-section h4 {
  margin: 0 0 16px 0;
  color: #409eff;
  font-size: 16px;
}

.cover-upload {
  width: 100%;
}

.cover-preview {
  margin-top: 10px;
}

.bgm-preview {
  margin-top: 10px;
}

.theme-color-input {
  display: flex;
  align-items: center;
  gap: 10px;
  width: 100%;
}

.character-asset-item {
  margin-bottom: 12px;
  padding: 12px;
  background: white;
  border-radius: 6px;
  border: 1px solid #e4e7ed;
}

.character-warning {
  margin-top: 8px;
}

.line-item {
  margin-bottom: 12px;
  padding: 12px;
  background: white;
  border-radius: 6px;
  border: 1px solid #e4e7ed;
}

.empty-state {
  text-align: center;
  padding: 60px 20px;
  background: white;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

/* 响应式设计 */
@media (max-width: 768px) {
  .batch-create-container {
    padding: 10px;
  }

  .page-header {
    flex-direction: column;
    gap: 16px;
    align-items: stretch;
  }

  .stats-info {
    flex-direction: column;
    gap: 8px;
  }

  .header-actions {
    justify-content: center;
  }
}
</style>
