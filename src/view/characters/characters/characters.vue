<template>
  <div>
    <div class="gva-search-box">
      <el-form ref="elSearchFormRef" :inline="true" :model="searchInfo" class="demo-form-inline" :rules="searchRule" @keyup.enter="onSubmit">

        <template v-if="showAllQuery">
          <el-form-item label="角色名称" prop="name">
            <el-input v-model="searchInfo.name" placeholder="请输入角色名称" clearable />
          </el-form-item>
          <el-form-item label="是否推荐" prop="is_recommended">
            <el-select v-model="searchInfo.is_recommended" placeholder="请选择是否推荐" clearable>
              <el-option label="推荐" :value="true" />
              <el-option label="不推荐" :value="false" />
            </el-select>
          </el-form-item>
          <el-form-item label="所属IP" prop="ip_id">
            <el-select v-model="searchInfo.ip_id" placeholder="请选择IP" clearable>
              <el-option
                v-for="item in ipList"
                :key="item.id"
                :label="item.name"
                :value="item.id"
              />
            </el-select>
          </el-form-item>
        </template>

        <el-form-item>
          <el-button type="primary" icon="search" @click="onSubmit">查询</el-button>
          <el-button icon="refresh" @click="onReset">重置</el-button>
          <el-button link type="primary" icon="arrow-down" @click="showAllQuery=true" v-if="!showAllQuery">展开</el-button>
          <el-button link type="primary" icon="arrow-up" @click="showAllQuery=false" v-else>收起</el-button>
        </el-form-item>
      </el-form>
    </div>
    <div class="gva-table-box">
        <div class="gva-btn-list">
            <el-button  type="primary" icon="plus" @click="openDialog()">新增</el-button>
            <el-button  icon="delete" style="margin-left: 10px;" :disabled="!multipleSelection.length" @click="onDelete">下架</el-button>
        </div>
        <el-table
        ref="multipleTable"
        style="width: 100%"
        tooltip-effect="dark"
        :data="tableData"
        row-key="id"
        @selection-change="handleSelectionChange"
        >
        <el-table-column type="selection" width="55" />
        
            <el-table-column align="left" label="角色ID" prop="id" width="120" />

            <el-table-column align="left" label="角色名称" prop="name" width="120" />

            <el-table-column align="left" label="角色模型pth" prop="model_pth" width="120">
              <template #default="scope">
                <el-tooltip
                  :content="scope.row.model_pth"
                  placement="top"
                  :show-after="500"
                  :hide-after="2000"
                >
                  <span class="ellipsis">{{ scope.row.model_pth }}</span>
                </el-tooltip>
                <el-button
                  v-if="scope.row.model_pth"
                  type="primary"
                  link
                  @click="copyModelPth(scope.row.model_pth)"
                >
                  <el-icon><CopyDocument /></el-icon>
                </el-button>
              </template>
            </el-table-column> 
          
            <el-table-column align="left" label="角色模型index" prop="model_idx" width="120">
              <template #default="scope">
                <el-tooltip
                  :content="scope.row.model_idx"
                  placement="top"
                  :show-after="500"
                  :hide-after="2000"
                >
                  <span class="ellipsis">{{ scope.row.model_idx }}</span>
                </el-tooltip>
                 <el-button
                  v-if="scope.row.model_idx"
                  type="primary"
                  link
                  @click="copyModelPth(scope.row.model_idx)"
                  >
                  <el-icon><CopyDocument /></el-icon>
                </el-button>
              </template>
            </el-table-column> 

            <el-table-column align="left" label="角色排序" prop="sort" width="120" />

            <el-table-column align="left" label="角色所属IP" prop="ip_id" width="120" />

            <el-table-column align="left" label="IP名称" prop="ip_name" width="120" />

            <el-table-column align="left" label="角色配音次数" prop="dubbing_count" width="120" />

            <el-table-column align="left" label="是否推荐" prop="is_recommended" width="120" >
              <template #default="scope">
                <div class="recommend-cell">
                  <!-- 推荐星标 -->
                  <el-icon
                    v-if="scope.row.is_recommended"
                    class="recommend-star"
                    :size="16"
                  >
                    <StarFilled />
                  </el-icon>

                  <!-- 下拉选择器 -->
                  <el-select
                    v-model="scope.row.is_recommended"
                    placeholder="请选择"
                    size="small"
                    @change="(value) => handleRecommendChange(scope.row, value)"
                    :loading="scope.row.recommendLoading"
                  >
                    <el-option label="推荐" :value="true" />
                    <el-option label="不推荐" :value="false" />
                  </el-select>
                </div>
              </template>
             </el-table-column>
            <el-table-column align="left" label="试听音频" prop="preview_audio" width="240">
              <template #default="scope">
                <audio 
                  v-if="scope.row.preview_audio"
                  controls
                  :src="scope.row.preview_audio"
                  style="width: 100%; height: 28px;"
                >
                  您的浏览器不支持音频播放
                </audio>
                <span v-else>-</span>
              </template>
            </el-table-column>
            <el-table-column align="left" label="状态" prop="status" width="120" >
              <template #default="scope">
                {{ scope.row.status === 1 ? '上架' : '下架' }}
              </template>
             </el-table-column>
            <el-table-column align="left" label="创建时间" prop="created_at" width="180" >
              <template #default="scope">
                {{ formatDate(scope.row.created_at) }}
              </template>
            </el-table-column>

            <el-table-column align="left" label="更新时间" prop="updated_at" width="180">
              <template #default="scope">
                {{ formatDate(scope.row.updated_at) }}
              </template>
            </el-table-column>

        <el-table-column align="left" label="操作" fixed="right" :min-width="appStore.operateMinWith">
            <template #default="scope">
            <el-button  type="primary" link class="table-button" @click="getDetails(scope.row)"><el-icon style="margin-right: 5px"><InfoFilled /></el-icon>资源列表</el-button>
            <el-button  type="primary" link icon="edit" class="table-button" @click="updateCharacterFunc(scope.row)">编辑</el-button>
            <el-button   type="primary" link icon="delete" @click="deleteRow(scope.row)">下架</el-button>
            </template>
        </el-table-column>
        </el-table>
        <div class="gva-pagination">
            <el-pagination
            layout="total, sizes, prev, pager, next, jumper"
            :current-page="page"
            :page-size="pageSize"
            :page-sizes="[20, 30, 50, 100]"
            :total="total"
            @current-change="handleCurrentChange"
            @size-change="handleSizeChange"
            />
        </div>
    </div>
    <el-drawer destroy-on-close :size="appStore.drawerSize" v-model="dialogFormVisible" :show-close="false" :before-close="closeDialog">
       <template #header>
              <div class="flex justify-between items-center">
                <span class="text-lg">{{type==='create'?'新增':'编辑'}}</span>
                <div>
                  <el-button :loading="btnLoading" type="primary" @click="enterDialog">确 定</el-button>
                  <el-button @click="closeDialog">取 消</el-button>
                </div>
              </div>
            </template>

          <el-form :model="formData" label-position="top" ref="elFormRef" :rules="rule" label-width="80px">
        <el-form-item label="角色ID:" prop="id" v-if="type === 'update'">
         <el-input 
             v-model.number="formData.id" 
             :clearable="true" 
             placeholder="请输入角色ID" 
             :disabled="type === 'update'"
             v-if="type === 'update'"
          />
</el-form-item>
            <el-form-item label="角色名称:" prop="name">
    <el-input v-model="formData.name" :clearable="true" placeholder="请输入角色名称" />
</el-form-item>
            <el-form-item label="角色模型pth:" prop="model_pth">
    <el-input v-model.number="formData.model_pth" :clearable="true" placeholder="请输入角色模型pth" />
  </el-form-item>
            <el-form-item label="角色模型index:" prop="model_idx">
    <el-input v-model.number="formData.model_idx" :clearable="true" placeholder="请输入角色模型index" />
</el-form-item>
            <el-form-item label="角色排序:" prop="sort">
    <el-input v-model.number="formData.sort" :clearable="true" placeholder="请输入角色排序" />
</el-form-item>
            <el-form-item label="角色所属IP:" prop="ip_id">
              <el-select 
      v-model="formData.ip_id" 
      placeholder="请选择IP"
      @change="handleIpChange"
    >
    <el-option
        v-for="item in ipList"
        :key="item.id"
        :label="item.name"
        :value="item.id"
      />
    </el-select>
</el-form-item>
            <el-form-item label="IP名称:" prop="ip_name">
    <el-input v-model="formData.ip_name" :clearable="true" placeholder="请输入IP名称" />
</el-form-item>
            <el-form-item label="角色配音次数:" prop="dubbing_count">
    <el-input v-model.number="formData.dubbing_count" :clearable="true" placeholder="请输入角色配音次数" />
</el-form-item>
            <el-form-item label="是否推荐:" prop="is_recommended">
    <el-switch
        v-model="formData.is_recommended"
        active-text="推荐"
        inactive-text="不推荐"
        :active-value="true"
        :inactive-value="false"
    />
</el-form-item>
<el-form-item label="试听音频" prop="preview_audio">
      <el-upload
        class="audio-uploader"
        :auto-upload="false"
        :show-file-list="false"
        :on-change="(file) => handleFileChange(file, 'preview_audio')"
        accept="audio/*"
      >
      <template v-if="!formData.preview_audio">
      <el-button type="primary">上传音频</el-button>
      </template>
      <template v-else>
      <!-- 播放器 -->
      <audio :src="formData.preview_audio" controls class="audio-preview" />
      <!-- 重新上传按钮 -->
      <el-button type="primary" class="mt-2">重新上传</el-button>
      </template>
      </el-upload>
    </el-form-item>
<el-form-item label="状态（1:上架，2:下架）:" prop="status">
    <el-select v-model="formData.status" placeholder="请选择状态"
        :clearable="true"
        :default-first-option="true"
    >
       <el-option label="上架" :value="1" />
       <el-option label="下架" :value="2" />
  </el-select>
</el-form-item>
<el-form-item label="tts引擎:" prop="tts_engine">
    <el-select v-model="formData.tts_engine" placeholder="请选择tts引擎"
        :clearable="true"
        :default-first-option="true"
    >
       <el-option label="llasa" :value="llasa" />
       <el-option label="fish_speech" :value="fish_speech" />
  </el-select>
</el-form-item>
          </el-form>
    </el-drawer>
    <el-drawer destroy-on-close :size="appStore.drawerSize" v-model="detailShow" :show-close="true" :before-close="closeDetailShow" title="查看">
            <el-descriptions :column="1" border>
                    <el-descriptions-item label="角色ID">
    {{ detailFrom.id }}
</el-descriptions-item>
                    <el-descriptions-item label="角色名称">
    {{ detailFrom.name }}
</el-descriptions-item>
                    <el-descriptions-item label="角色模型pth">
    {{ detailFrom.model_pth }}
</el-descriptions-item>
<el-descriptions-item label="角色模型index">
    {{ detailFrom.model_idx }}
</el-descriptions-item>
                    <el-descriptions-item label="角色排序">
    {{ detailFrom.sort }}
</el-descriptions-item>
                    <el-descriptions-item label="角色所属IP">
    {{ detailFrom.ip_id }}
</el-descriptions-item>
                    <el-descriptions-item label="IP名称">
    {{ detailFrom.ip_name }}
</el-descriptions-item>
                    <el-descriptions-item label="角色配音次数">
    {{ detailFrom.dubbing_count }}
</el-descriptions-item>
                    <el-descriptions-item label="是否推荐">
    {{ detailFrom.is_recommended ? '推荐' : '不推荐' }}
</el-descriptions-item>
<el-descriptions-item label="状态（1:上架，2:下架）">
    {{ detailFrom.status }}
</el-descriptions-item>
                    <el-descriptions-item label="创建时间">
    {{ detailFrom.created_at }}
</el-descriptions-item>
                    <el-descriptions-item label="更新时间">
    {{ detailFrom.updated_at }}
</el-descriptions-item>
            </el-descriptions>
        </el-drawer>

<!-- 角色资源列表弹窗 -->
<el-dialog
  v-model="assetsDialogVisible"
  title="角色资源列表"
  width="90%"
  :before-close="closeAssetsDialog"
  class="assets-dialog"
>
 <!-- 添加新增按钮 -->
 <div class="gva-btn-list" style="margin-bottom: 15px;">
    <el-button type="primary" icon="plus" @click="openAssetsDialog">新增资源</el-button>
</div>
<el-table :data="assetsList" style="width: 100%" class="assets-table">
    <!-- 基础信息列 -->
    <el-table-column align="left" label="基础信息" width="200" fixed="left">
      <template #default="scope">
        <div class="basic-info">
          <div class="info-row">
            <span class="label">ID:</span>
            <span class="value">{{ scope.row.id }}</span>
          </div>
          <div class="info-row">
            <span class="label">类型:</span>
            <el-tag :type="getTypeTagType(scope.row.type)" size="small">{{ scope.row.type }}</el-tag>
          </div>
          <div class="info-row">
            <span class="label">名称:</span>
            <span class="value" :title="scope.row.name">{{ scope.row.name || '-' }}</span>
          </div>
          <div class="info-row">
            <span class="label">状态:</span>
            <el-tag :type="scope.row.status === 1 ? 'success' : 'danger'" size="small">
              {{ scope.row.status === 1 ? '上架' : '下架' }}
            </el-tag>
          </div>
        </div>
      </template>
    </el-table-column>

    <!-- 视觉资源列 -->
    <el-table-column align="left" label="视觉资源" width="260">
      <template #default="scope">
        <div class="visual-resources">
          <div class="resource-item">
            <span class="resource-label">头像:</span>
            <el-image
              v-if="scope.row.avatar_url"
              style="width: 40px; height: 40px; border-radius: 4px;"
              :src="scope.row.avatar_url"
              :preview-src-list="[scope.row.avatar_url]"
              fit="cover"
              :preview-teleported="true"
            />
            <span v-else class="no-resource">无图片资源</span>
          </div>
          <div class="resource-item">
            <span class="resource-label">背景:</span>
            <div class="background-preview">
              <el-image
                v-if="scope.row.background_url"
                style="width: 60px; height: 40px; border-radius: 4px;"
                :src="scope.row.background_url"
                :preview-src-list="[scope.row.background_url]"
                fit="cover"
                :preview-teleported="true"
              />
              <span v-else class="no-resource">无图片资源</span>
              <div v-if="scope.row.bg_theme_color" class="theme-color">
                <div
                  class="color-block"
                  :style="{ backgroundColor: scope.row.bg_theme_color }"
                  :title="scope.row.bg_theme_color"
                ></div>
              </div>
            </div>
          </div>
          <div class="resource-item">
            <span class="resource-label">动态:</span>
            <div class="animated-preview">
              <el-image
                v-if="scope.row.animated_url"
                style="width: 60px; height: 40px; border-radius: 4px;"
                :src="scope.row.animated_url"
                :preview-src-list="[scope.row.animated_url]"
                fit="cover"
                :preview-teleported="true"
              />
              <span v-else class="no-resource">无图片资源</span>
              <div v-if="scope.row.animated_theme_color" class="theme-color">
                <div
                  class="color-block"
                  :style="{ backgroundColor: scope.row.animated_theme_color }"
                  :title="scope.row.animated_theme_color"
                ></div>
              </div>
            </div>
          </div>
        </div>
      </template>
    </el-table-column>

    <!-- 音频配置列 -->
    <el-table-column align="left" label="音频配置" width="400">
      <template #default="scope">
        <div class="audio-config">
          <div class="config-item">
            <span class="config-label">引导音频:</span>
            <audio
              v-if="scope.row.sample_audio"
              controls
              :src="scope.row.sample_audio"
              style="width: 300px; height: 28px;"
            >
              您的浏览器不支持音频播放
            </audio>
            <span v-else class="no-resource">无音频资源</span>
          </div>
          <div class="config-item">
            <span class="config-label">引导文本:</span>
            <el-tooltip :content="scope.row.sample_audio_text" placement="top" :disabled="!scope.row.sample_audio_text">
              <span class="text-content">{{ scope.row.sample_audio_text || '-' }}</span>
            </el-tooltip>
          </div>
          <div class="config-item">
            <span class="config-label">TTS引擎:</span>
            <el-tag size="small" type="info">{{ scope.row.tts_engine || '-' }}</el-tag>
          </div>
        </div>
      </template>
    </el-table-column>

    <!-- RVC配置列 -->
    <el-table-column align="left" label="RVC配置" width="220">
      <template #default="scope">
        <div class="rvc-config">
          <div class="config-item-inline">
            <span class="config-label-inline">参考音频:</span>
            <el-tag :type="scope.row.reference_audio_use_rvc === 1 ? 'success' : 'info'" size="small">
              {{ scope.row.reference_audio_use_rvc === 1 ? '使用RVC' : '不使用' }}
            </el-tag>
          </div>
          <div class="config-item-inline">
            <span class="config-label-inline">用户配音:</span>
            <el-tag :type="scope.row.user_dubbing_use_rvc === 1 ? 'success' : 'info'" size="small">
              {{ scope.row.user_dubbing_use_rvc === 1 ? '使用RVC' : '不使用' }}
            </el-tag>
          </div>
          <div class="config-item-inline">
            <span class="config-label-inline">默认资源:</span>
            <el-tag :type="scope.row.is_default ? 'warning' : 'info'" size="small">
              {{ scope.row.is_default ? '是' : '否' }}
            </el-tag>
          </div>
        </div>
      </template>
    </el-table-column>

    <!-- 预设状态列 -->
    <el-table-column align="left" label="预设状态" width="120">
      <template #default="scope">
        <div class="preset-status">
          <el-tag
            :type="scope.row.preset_status === 1 ? 'success' : 'warning'"
            size="small"
            :icon="scope.row.preset_status === 1 ? Check : Clock"
          >
            {{ scope.row.preset_status === 1 ? '已预设' : '未预设' }}
          </el-tag>
        </div>
      </template>
    </el-table-column>

    <!-- 时间信息列 -->
    <el-table-column align="left" label="时间信息" width="160">
      <template #default="scope">
        <div class="time-info">
          <div class="time-item">
            <span class="time-label">创建:</span>
            <span class="time-value">{{ formatTimestamp(scope.row.created_at) }}</span>
          </div>
          <div class="time-item">
            <span class="time-label">更新:</span>
            <span class="time-value">{{ formatTimestamp(scope.row.updated_at) }}</span>
          </div>
        </div>
      </template>
    </el-table-column>

    <!-- 操作列 -->
    <el-table-column align="left" label="操作" width="300" fixed="right">
      <template #default="scope">
        <div class="action-buttons">
          <!-- 预设操作组 -->
          <div class="action-group">
            <span class="group-label">预设:</span>
            <el-button
              :type="scope.row.preset_status === 1 ? 'success' : 'primary'"
              :icon="scope.row.preset_status === 1 ? Check : Plus"
              size="small"
              :disabled="scope.row.preset_status === 1"
              @click="handleAddPreset(scope.row)"
              class="preset-btn"
              :class="{ 'preset-completed': scope.row.preset_status === 1 }"
            >
              {{ scope.row.preset_status === 1 ? '已预设' : '预设' }}
            </el-button>
            <el-button
              type="primary"
              :icon="View"
              size="small"
              @click="handleGetPreset(scope.row)"
            >
              查看
            </el-button>
            <el-button
              type="danger"
              :icon="Delete"
              size="small"
              @click="handleDeletePreset(scope.row)"
            >
              下架
            </el-button>
          </div>

          <!-- 模型操作组 -->
          <div class="action-group">
            <span class="group-label">模型:</span>
            <el-button
              type="primary"
              :icon="Plus"
              size="small"
              @click="handleAddModel(scope.row)"
            >
              添加
            </el-button>
            <el-button
              type="primary"
              :icon="View"
              size="small"
              @click="handleGetModel(scope.row)"
            >
              查看
            </el-button>
            <el-button
              type="danger"
              :icon="Delete"
              size="small"
              @click="handleDeleteModel(scope.row)"
            >
              下架
            </el-button>
          </div>

          <!-- 其他操作组 -->
          <div class="action-group">
            <span class="group-label">其他:</span>
            <el-button
              type="warning"
              :icon="Microphone"
              size="small"
              @click="handlePreviewVoice(scope.row)"
            >
              预览
            </el-button>
            <el-button
              type="primary"
              :icon="Edit"
              size="small"
              @click="handleEditAssets(scope.row)"
            >
              编辑
            </el-button>
            <el-button
              v-if="scope.row.status === 1"
              type="danger"
              :icon="Delete"
              size="small"
              @click="handleDeleteAssets(scope.row)"
            >
              下架
            </el-button>
          </div>
        </div>
      </template>
    </el-table-column>
  </el-table>
  <div class="gva-pagination">
    <el-pagination
      layout="total, sizes, prev, pager, next, jumper"
      :current-page="assetsPage"
      :page-size="assetsPageSize"
      :page-sizes="[10, 30, 50, 100]"
      :total="assetsTotal"
      @current-change="handleAssetsCurrentChange"
      @size-change="handleAssetsSizeChange"
    />
  </div>
</el-dialog>

<!-- 添加新增资源弹窗 -->
<el-dialog
  v-model="addAssetsDialogVisible"
  :title="isEdit ? '编辑资源' : '新增资源'"
  width="600px"
  :before-close="closeAddAssetsDialog"
>
  <el-form 
    :model="assetsFormData" 
    label-position="top" 
    ref="assetsFormRef" 
    :rules="assetsFormRules"
  >
    <el-form-item label="资源包类型" prop="type">
      <el-select v-model="assetsFormData.type" placeholder="请选择资源包类型">
        <el-option label="普通" value="normal" />
        <el-option label="高兴" value="happy" />
        <el-option label="悲伤" value="sad" />
        <el-option label="激动" value="excited" />
      </el-select>
    </el-form-item>
    
    <el-form-item label="资源包名称" prop="name">
      <el-input v-model="assetsFormData.name" placeholder="请输入资源包名称" />
    </el-form-item>
    
    <el-form-item label="角色头像" prop="avatar_url">
      <el-upload
        class="avatar-uploader"
        :auto-upload="false"
        :show-file-list="false"
        :on-change="(file) => handleFileChange(file, 'avatar')"
        accept="image/*"
      >
        <img v-if="assetsFormData.avatar_url" :src="assetsFormData.avatar_url" class="avatar" />
        <el-icon v-else class="avatar-uploader-icon"><Plus /></el-icon>
      </el-upload>
    </el-form-item>
    
    <el-form-item label="角色背景图" prop="background_url">
      <el-upload
        class="background-uploader"
        :auto-upload="false"
        :show-file-list="false"
        :on-change="(file) => handleFileChange(file, 'background')"
        accept="image/*"
      >
        <img v-if="assetsFormData.background_url" :src="assetsFormData.background_url" class="background" />
        <el-icon v-else class="background-uploader-icon"><Plus /></el-icon>
      </el-upload>
    </el-form-item>

    <el-form-item label="背景图主题色" prop="bg_theme_color">
      <div style="display: flex; align-items: center; gap: 8px;">
        <el-color-picker v-model="assetsFormData.bg_theme_color" />
        <el-input v-model="assetsFormData.bg_theme_color" :clearable="true" placeholder="请输入背景图主题色" style="flex: 1;" />
      </div>
    </el-form-item>

    <el-form-item label="角色动态图" prop="animated_url">
      <el-upload
        class="dynamic-uploader"
        :auto-upload="false"
        :show-file-list="false"
        :on-change="(file) => handleFileChange(file, 'dynamic')"
        accept="image/*"
      >
        <img v-if="assetsFormData.animated_url" :src="assetsFormData.animated_url" class="dynamic" />
        <el-icon v-else class="dynamic-uploader-icon"><Plus /></el-icon>
      </el-upload>
    </el-form-item>

    <el-form-item label="动态图主题色" prop="animated_theme_color">
      <div style="display: flex; align-items: center; gap: 8px;">
        <el-color-picker v-model="assetsFormData.animated_theme_color" />
        <el-input v-model="assetsFormData.animated_theme_color" :clearable="true" placeholder="请输入动态图主题色" style="flex: 1;" />
      </div>
    </el-form-item>
    <el-form-item label="参考音频使用rvc:" prop="reference_audio_use_rvc">
      <el-select v-model="assetsFormData.reference_audio_use_rvc" placeholder="参考音频是否使用rvc">
        <el-option label="使用" :value="1" />
        <el-option label="不使用" :value="2" />
      </el-select>
    </el-form-item>
    <el-form-item label="用户配音使用rvc:" prop="user_dubbing_use_rvc">
      <el-select v-model="assetsFormData.user_dubbing_use_rvc" placeholder="用户配音是否使用rvc">
        <el-option label="使用" :value="1" />
        <el-option label="不使用" :value="2" />
      </el-select>
    </el-form-item>
    <el-form-item label="tts引擎" prop="tts_engine">
      <el-select v-model="assetsFormData.tts_engine" placeholder="选择tts引擎">
        <el-option label="llasa" :value="llasa" />
        <el-option label="fish_speech" :value="fish_speech" />
      </el-select>
    </el-form-item>
    <el-form-item label="引导音频" prop="sample_audio">
      <el-upload
        class="audio-uploader"
        :auto-upload="false"
        :show-file-list="false"
        :on-change="(file) => handleFileChange(file, 'audio')"
        accept="audio/*"
      >
      <template v-if="!assetsFormData.sample_audio">
      <el-button type="primary">上传音频</el-button>
      </template>
      <template v-else>
      <!-- 播放器 -->
      <audio :src="assetsFormData.sample_audio" controls class="audio-preview" />
      <!-- 重新上传按钮 -->
      <el-button type="primary" class="mt-2">重新上传</el-button>
      </template>
      <!--
        <el-button type="primary" v-if="!assetsFormData.sample_audio">上传音频</el-button>
        <audio v-else :src="assetsFormData.sample_audio" controls></audio>
      -->
      </el-upload>
    </el-form-item>
    
    <el-form-item label="引导文本" prop="sample_audio_text">
      <el-input 
        v-model="assetsFormData.sample_audio_text" 
        type="textarea" 
        :rows="3"
        placeholder="请输入引导文本"
      />
    </el-form-item>
    
    <el-form-item label="是否默认" prop="is_default">
      <el-switch v-model="assetsFormData.is_default" />
    </el-form-item>
    <el-form-item label="排序" prop="sort">
      <el-input-number  
          v-model="assetsFormData.sort" 
          :min="0"
          :precision="0"
          placeholder="请输入排序值"
      />
    </el-form-item>
    <el-form-item label="状态" prop="status">
      <el-select v-model="assetsFormData.status" placeholder="请选择状态">
        <el-option label="上架" :value="1" />
        <el-option label="下架" :value="2" />
      </el-select>
    </el-form-item>
  </el-form>
  
  <template #footer>
    <div class="dialog-footer">
      <el-button @click="closeAddAssetsDialog">取 消</el-button>
      <el-button type="primary" :loading="submitLoading" @click="submitAssetsForm">确 定</el-button>
    </div>
  </template>
</el-dialog>


<!-- 预览变声的弹出窗口 -->>
<el-dialog
  v-model="previewDialogVisible"
  title="预览变声"
  width="800px"
  :before-close="closePreviewDialog"
>
  <div class="preview-container">
    <!-- 原始音频 -->
    <div class="audio-section">
      <h4>引导音频</h4>
      <!--
      <div 
        class="audio-drop-zone"
        @dragover.prevent
        @drop.prevent="handlePreviewAudioDrop"
      >
        <audio 
          v-if="previewData.localAudioUrl"
          controls
          :src="previewData.localAudioUrl"
          style="width: 100%; margin: 10px 0;"
        >
          您的浏览器不支持音频播放
        </audio>
        <div v-else class="drop-hint">
          拖拽音频文件到这里
        </div>
      </div>
      -->
      <audio 
        v-if="previewData.sample_audio"
        controls
        :src="previewData.sample_audio"
        style="width: 100%; margin: 10px 0;">
        您的浏览器不支持音频播放
      </audio>
      <div class="text-content">
        <h4>引导文本</h4>
        <el-input
          v-model="previewData.sample_audio_text"
          type="textarea"
          :rows="3"
          placeholder="引导文本内容"
          readonly
        />
      </div>
    </div>

    <!-- 变声部分 -->
    <div class="voice-section">
      <h4>变声文本</h4>
      <el-input
        v-model="previewData.vc_text"
        type="textarea"
        :rows="3"
        placeholder="请输入要变声的文本"
      />
      <h4>本地变声音频</h4>
      <div 
        class="audio-drop-zone"
        @dragover.prevent
        @drop.prevent="handleLocalPreviewAudioDrop"
      >
        <audio 
          v-if="previewData.localPreviewAudioUrl"
          controls
          :src="previewData.localPreviewAudioUrl"
          style="width: 100%; margin: 10px 0;"
        >
          您的浏览器不支持音频播放
        </audio>
        <div v-else class="drop-hint">
          拖拽音频文件到这里
        </div>
      </div>

      <div class="button-group" style="margin-top: 15px; display: flex; align-items: center; gap: 10px;">
        <el-button 
          type="primary" 
          :loading="previewLoading"
          @click="handleTTS"
        >
          TTS
        </el-button>
        <el-select v-model="previewData.tts_engine" placeholder="请选择" style="width: 120px;">
          <el-option label="llasa" :value="llasa" />
          <el-option label="coqui引导音频" :value="coqui_reference" />
          <el-option label="coqui内置引导" :value="coqui_speaker" />
          <el-option label="Fish Speech" :value="fish_speech" />
        </el-select>
        <el-select v-model="previewData.tts_lang"
        v-if="previewData.tts_engine !== 'llasa' && previewData.tts_engine !== 'fish_speech'"
        placeholder="请选择" 
        style="width: 120px;">
          <el-option label="zh" :value="lang_zh" />
          <el-option label="en" :value="lang_en" />
          <el-option label="ja" :value="lang_ja" />
        </el-select>
        <el-select v-model="previewData.speaker"
        v-if="previewData.tts_engine === 'coqui_speaker'" 
        placeholder="请选择" 
        style="width: 120px;">
          <el-option
            v-for="item in speakerOptions"
            :key="item"
            :label="item"
            :value="item"
          />
        </el-select>
        <el-button 
          type="success" 
          :loading="voiceChangeLoading"
          @click="handleVoiceChange"
        >
          变声
        </el-button>
        <el-button 
          type="success" 
          :loading="voiceChangeLoading"
          @click="handleLocalVoiceChange"
        >
          本地音频变声
        </el-button>
        <el-select v-model="previewData.use_rvc" placeholder="请选择" style="width: 120px;">
          <el-option label="使用RVC" :value="true" />
          <el-option label="不使用RVC" :value="false" />
        </el-select>
      </div>
      <!-- TTS后的音频播放器 -->
      <div v-if="previewData.transformed_audio" class="tts-audio">
        <h4>TTS结果:</h4>
        <audio 
          controls
          :src="previewData.transformed_audio"
          style="width: 100%; margin: 10px 0;"
        >
          您的浏览器不支持音频播放
        </audio>
      </div>
       <!-- 变声后的音频播放器 -->
       <div v-if="previewData.rvc_audio" class="rvc-audio">
        <h4>变声结果:</h4>
        <audio 
          controls
          :src="previewData.rvc_audio"
          style="width: 100%; margin: 10px 0;"
        >
          您的浏览器不支持音频播放
        </audio>
      </div>
    </div>
  </div>
</el-dialog>

<!--展示preset预设详情-->
<el-dialog
  v-model="presetDialogVisible"
  title="预设详情"
  width="900px"
  :before-close="closePresetDialog"
>
  <el-descriptions 
    :column="1" 
    border
    label-width="200px"
  >
    <el-descriptions-item label="预设名称">
      {{ presetData.preset_name }}
    </el-descriptions-item>
    <el-descriptions-item label="RVC模型名称">
      {{ presetData.rvc_name }}
    </el-descriptions-item>
    <el-descriptions-item label="种子名称">
      {{ presetData.seed_name }}
    </el-descriptions-item>
    <el-descriptions-item label="参考音频URL">
      {{ presetData.reference_audio_url }}
    </el-descriptions-item>
    <el-descriptions-item label="F0方法">
      {{ presetData.f0_method }}
    </el-descriptions-item>
    <el-descriptions-item label="F0音调调整">
      {{ presetData.f0_up_key }}
    </el-descriptions-item>
    <el-descriptions-item label="保护系数">
      {{ presetData.protect }}
    </el-descriptions-item>
    <el-descriptions-item label="RMS混合率">
      {{ presetData.rms_mix_rate }}
    </el-descriptions-item>
    <el-descriptions-item label="扩散步数">
      {{ presetData.diffusion_steps }}
    </el-descriptions-item>
    <el-descriptions-item label="用户配音是否使用RVC">
      {{ presetData.cascaded_use_rvc ? '是' : '否' }}
    </el-descriptions-item>
    <el-descriptions-item label="F0条件">
      {{ presetData.f0_condition ? '是' : '否' }}
    </el-descriptions-item>
    <el-descriptions-item label="自动F0调整">
      {{ presetData.auto_f0_adjust ? '是' : '否' }}
    </el-descriptions-item>
    <el-descriptions-item label="半音偏移">
      {{ presetData.semi_tone_shift }}
    </el-descriptions-item>
  </el-descriptions>
</el-dialog>


<!--展示rvcModel详情-->
<el-dialog
  v-model="rvcModelDialogVisible"
  title="模型详情"
  width="1000px"
  :before-close="closeRvcModelDialog"
>
  <el-descriptions 
    :column="1" 
    border
    label-width="220px"
  >
    <el-descriptions-item label="rvcName">
      {{ rvcModelData.rvc_name }}
    </el-descriptions-item>
    <el-descriptions-item label="netg_path">
      {{ rvcModelData.netg_path }}
    </el-descriptions-item>
    <el-descriptions-item label="netg_url">
      {{ rvcModelData.netg_url }}
    </el-descriptions-item>
    <el-descriptions-item label="netg_file_size">
      {{ rvcModelData.netg_file_size }}
    </el-descriptions-item>
    <el-descriptions-item label="netg_sha256">
      {{ rvcModelData.netg_sha256 }}
    </el-descriptions-item>
    <el-descriptions-item label="index_path">
      {{ rvcModelData.index_path }}
    </el-descriptions-item>
    <el-descriptions-item label="index_url">
      {{ rvcModelData.index_url }}
    </el-descriptions-item>
    <el-descriptions-item label="index_file_size">
      {{ rvcModelData.index_file_size }}
    </el-descriptions-item>
    <el-descriptions-item label="index_sha256">
      {{ rvcModelData.index_sha256 }}
    </el-descriptions-item>
    <el-descriptions-item label="tgt_sr">
      {{ rvcModelData.tgt_sr }}
    </el-descriptions-item>
    <el-descriptions-item label="if_f0">
      {{ rvcModelData.if_f0 }}
    </el-descriptions-item>
    <el-descriptions-item label="version">
      {{ rvcModelData.version }}
    </el-descriptions-item>
  </el-descriptions>
</el-dialog>

</div>
</template>

<script setup>
import {
  createCharacter,
  deleteCharacter,
  deleteCharacterByIds,
  updateCharacter,
  findCharacter,
  getCharacterList,
  setCharacterRecommended
} from '@/api/characters/characters'

import {
  getCharacterAssetsList,
  getPreset,
  addPreset,
  deletePreset,
  addModel,
  deleteModel,
  getModel,
  llasa_tts,
  coqui_tts,
  coqui_tts_by_voice,
  coqui_tts_speakers,
  fish_speech_tts,
  createCharacterAssets,
  findCharacterAssets,
  updateCharacterAssets,
  deleteCharacterAssets,
  updatePresetStatus
} from '@/api/characters/characterAssets'

import { getAllIps } from '@/api/ips/ip'

import { getPresets, loadModel, inferRvc } from '@/api/rvc/rvc'
import {
  CopyDocument,
  Check,
  Plus,
  View,
  Delete,
  Microphone,
  Edit,
  Clock,
  StarFilled,
  InfoFilled
} from '@element-plus/icons-vue'

// 全量引入格式化工具 请按需保留
import { formatDate, formatBoolean, formatTimestamp, extractThemeColorFromImage } from '@/utils/format'
import { uploadToOSS, checkFileSize } from '@/utils/oss'  
import { ElMessage, ElMessageBox } from 'element-plus'
import { ref, reactive, onMounted, watch } from 'vue'
import { useAppStore } from "@/pinia"
import { v4 as uuidv4 } from 'uuid'
import { ElLoading } from 'element-plus'

defineOptions({
    name: 'Character'
})

// 定义tts engine类型
const llasa = 'llasa'
const coqui_reference = 'coqui_reference'
const coqui_speaker = 'coqui_speaker' 
const fish_speech = 'fish_speech'

const lang_zh = 'zh'
const lang_en = 'en'
const lang_ja = 'ja'


// 提交按钮loading
const btnLoading = ref(false)
const appStore = useAppStore()

// 控制更多查询条件显示/隐藏状态
const showAllQuery = ref(true)

// 自动化生成的字典（可能为空）以及字段
const formData = ref({
            id: undefined,
            name: '',
            model_pth: undefined,
            model_idx: undefined,
            sort: undefined,
            ip_id: undefined,
            ip_name: '',
            dubbing_count: undefined,
            is_recommended: false,
            preview_audio: '',  // 设置为空字符串而不是undefined
            status: undefined,
            created_at: undefined,
            updated_at: undefined,
        })



// 验证规则
const rule = reactive({
})

const searchRule = reactive({
  CreatedAt: [
    { validator: (rule, value, callback) => {
      if (searchInfo.value.startCreatedAt && !searchInfo.value.endCreatedAt) {
        callback(new Error('请填写结束日期'))
      } else if (!searchInfo.value.startCreatedAt && searchInfo.value.endCreatedAt) {
        callback(new Error('请填写开始日期'))
      } else if (searchInfo.value.startCreatedAt && searchInfo.value.endCreatedAt && (searchInfo.value.startCreatedAt.getTime() === searchInfo.value.endCreatedAt.getTime() || searchInfo.value.startCreatedAt.getTime() > searchInfo.value.endCreatedAt.getTime())) {
        callback(new Error('开始日期应当早于结束日期'))
      } else {
        callback()
      }
    }, trigger: 'change' }
  ],
})

const elFormRef = ref()
const elSearchFormRef = ref()

// =========== 表格控制部分 ===========
const page = ref(1)
const total = ref(0)
const pageSize = ref(20)
const tableData = ref([])
const searchInfo = ref({})
// 重置
const onReset = () => {
  searchInfo.value = {}
  getTableData()
}

// 搜索
const onSubmit = () => {
  elSearchFormRef.value?.validate(async(valid) => {
    if (!valid) return
    page.value = 1
    getTableData()
  })
}

// 分页
const handleSizeChange = (val) => {
  pageSize.value = val
  getTableData()
}

// 修改页面容量
const handleCurrentChange = (val) => {
  page.value = val
  getTableData()
}

// 查询
const getTableData = async() => {
  const table = await getCharacterList({ page: page.value, pageSize: pageSize.value, ...searchInfo.value })
  if (table.code === 0) {
    tableData.value = table.data.list
    total.value = table.data.total
    page.value = table.data.page
    pageSize.value = table.data.pageSize
  }
}

getTableData()

// ============== 表格控制部分结束 ===============

// 获取需要的字典 可能为空 按需保留
const setOptions = async () =>{
  await getIpOptions()
}

// 获取需要的字典 可能为空 按需保留
setOptions()


// 多选数据
const multipleSelection = ref([])
// 多选
const handleSelectionChange = (val) => {
    multipleSelection.value = val
}

// 下架行
const deleteRow = (row) => {
    ElMessageBox.confirm('确定要下架吗?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
    }).then(() => {
            deleteCharacterFunc(row)
        })
    }

// 多选下架
const onDelete = async() => {
  ElMessageBox.confirm('确定要下架吗?', '提示', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    type: 'warning'
  }).then(async() => {
      const ids = []
      if (multipleSelection.value.length === 0) {
        ElMessage({
          type: 'warning',
          message: '请选择要下架的数据'
        })
        return
      }
      multipleSelection.value &&
        multipleSelection.value.map(item => {
          ids.push(item.id)
        })
      const res = await deleteCharacterByIds({ ids })
      if (res.code === 0) {
        ElMessage({
          type: 'success',
          message: '下架成功'
        })
        if (tableData.value.length === ids.length && page.value > 1) {
          page.value--
        }
        getTableData()
      }
      })
    }

// 行为控制标记（弹窗内部需要增还是改）
const type = ref('')

// 更新行
const updateCharacterFunc = async(row) => {
    const res = await findCharacter({ id: row.id })
    type.value = 'update'
    if (res.code === 0) {
        formData.value = res.data
        dialogFormVisible.value = true
    }
}


// 下架行
const deleteCharacterFunc = async (row) => {
    const res = await deleteCharacter({ id: row.id })
    if (res.code === 0) {
        ElMessage({
                type: 'success',
                message: '下架成功'
            })
            if (tableData.value.length === 1 && page.value > 1) {
            page.value--
        }
        getTableData()
    }
}

// 弹窗控制标记
const dialogFormVisible = ref(false)

// 打开弹窗
const openDialog = () => {
    type.value = 'create'
    formData.value = {
      name: '',
      model_pth: undefined,
      model_idx: undefined,
      sort: 0,
      ip_id: undefined,
      ip_name: '',
      dubbing_count: 0,
      is_recommended: false,  // 默认设置为不推荐
      preview_audio: '',  // 设置为空字符串而不是undefined
      status: 1  // 默认设置为上架状态
    }
    dialogFormVisible.value = true
}

// 关闭弹窗
const closeDialog = () => {
    dialogFormVisible.value = false
    formData.value = {
        id: undefined,
        name: '',
        model_pth: undefined,
        model_idx: undefined,
        sort: undefined,
        ip_id: undefined,
        ip_name: '',
        dubbing_count: undefined,
        is_recommended: false,
        preview_audio: '',  // 设置为空字符串而不是undefined
        status: undefined,
        created_at: undefined,
        updated_at: undefined,
        }
}
// 弹窗确定
const enterDialog = async () => {
     btnLoading.value = true
     elFormRef.value?.validate( async (valid) => {
             if (!valid) return btnLoading.value = false
              let res
              switch (type.value) {
                case 'create':
                  res = await createCharacter(formData.value)
                  break
                case 'update':
                  res = await updateCharacter(formData.value)
                  break
                default:
                  res = await createCharacter(formData.value)
                  break
              }
              btnLoading.value = false
              if (res.code === 0) {
                ElMessage({
                  type: 'success',
                  message: '创建/更改成功'
                })
                closeDialog()
                getTableData()
              } else {
                ElMessage({
                  type: 'error',
                  message: res.msg || '操作失败'
                })
              }
      })
}

const detailFrom = ref({})


// 打开详情
const getDetails = async (row) => {
  currentCharacterId.value = row.id
  // 保存当前角色的 model_pth 和 model_idx
  currentCharacterModel.value = {
    model_pth: row.model_pth,
    model_idx: row.model_idx
  }
  assetsPage.value = 1
  await getAssetsList()
  assetsDialogVisible.value = true
}

// 添加新的 ref 来存储当前角色的模型信息
const currentCharacterModel = ref({
  model_pth: null,
  model_idx: null
})

// 获取资源列表
const getAssetsList = async () => {
  const res = await getCharacterAssetsList({ 
    page: assetsPage.value, 
    pageSize: assetsPageSize.value,
    character_id: currentCharacterId.value 
  })
  if (res.code === 0) {
    assetsList.value = res.data.list
    assetsTotal.value = res.data.total
  }
}

// 处理资源列表分页
const handleAssetsCurrentChange = (val) => {
  assetsPage.value = val
  getAssetsList()
}

// 处理资源列表每页条数变化
const handleAssetsSizeChange = (val) => {
  assetsPageSize.value = val
  getAssetsList()
}

// 关闭资源列表弹窗
const closeAssetsDialog = () => {
  assetsDialogVisible.value = false
  assetsList.value = []
  currentCharacterId.value = null
  currentCharacterModel.value = {
    model_pth: null,
    model_idx: null
  }
  assetsPage.value = 1
  assetsPageSize.value = 10
  assetsTotal.value = 0
}


// 角色资源列表相关
const assetsDialogVisible = ref(false)
const assetsList = ref([])
const currentCharacterId = ref(null)
const assetsPage = ref(1)
const assetsPageSize = ref(10)
const assetsTotal = ref(0)

// 处理添加预设
const handleAddPreset = async (row) => {
  if (!row.sample_audio) {
    ElMessage.warning('该资源没有引导音频')
    return
  }
  const referenceAudioUseRvc = row.reference_audio_use_rvc === 1 
  const userDubbingUseRvc = row.user_dubbing_use_rvc === 1 
  const useRVC = referenceAudioUseRvc || userDubbingUseRvc
  const env =  import.meta.env.VITE_APP_ENV === 'production' ? 'prod' : 'test'
  const presetName = import.meta.env.VITE_APP_ENV === 'production' ? `${row.character_id}_${row.id}` : `test_${row.character_id}_${row.id}`
  const rvcName = import.meta.env.VITE_APP_ENV === 'production' ? `rvcmodel_${row.id}` :  `test_rvcmodel_${row.id}`
  // 设置加载状态
  row.presetLoading = true
  try {
    console.log('useRVC:', useRVC, "referenceAudioUseRvc = ", referenceAudioUseRvc, "userDubbingUseRvc = ", userDubbingUseRvc)
    // 如果是使用rvc，检查rvc是否已经设置model
    if (useRVC) {
      const res = await getModel({
        env: env,
        rvc_name: rvcName
      })
      if (res.ret !== 1) {
        ElMessage.error('model数据不存在，' + (res.msg || '未知原因'))
        return
      }
    }
    const res = await addPreset({
      preset_name: presetName,
      reference_audio_url: row.sample_audio,
      prompt_text: row.prompt_text,
      seed_name: 'whisper_small',
      cascaded_use_rvc: userDubbingUseRvc,
      rvc_name: rvcName
    })
    if (res.ret === 1) {
      ElMessage.success('添加预设成功')
      const result = await updatePresetStatus({
        id: row.id,
        preset_status: 1
      })
      if (result.code === 0) {
         ElMessage.success('更新预设状态成功')
         // 刷新该条数据
         const updatedRes = await findCharacterAssets({id: row.id})
         if (updatedRes.code === 0) {
           // 更新列表中的对应行数据
           const index = assetsList.value.findIndex(item => item.id === row.id)
           if (index !== -1) {
             assetsList.value[index] = updatedRes.data
           }
         }
      }else{
        ElMessage.error(result.msg || '更新预设状态失败')
      }
    } else {
      ElMessage.error(res.msg || '添加预设失败')
    }
  } catch (error) {
    ElMessage.error('添加预设失败: ' + (error.msg || '未知原因'))
  } finally {
    row.presetLoading = false
  }
}



// 处理更新预设
const handleDeletePreset = async (row) => {
  ElMessageBox.confirm(
    '确定要下架该预设吗？',
    '提示',
    {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning'
    }
  ).then(async () => {
      const presetName = import.meta.env.VITE_APP_ENV === 'production' ? `${row.character_id}_${row.id}` : `test_${row.character_id}_${row.id}`
      // 设置加载状态
      row.presetLoading = true
      try {
        let res = await deletePreset({
          preset_name: presetName
        })
        if (res.ret !== 1) {
          ElMessage.error('下架preset失败，' + (res.msg || '未知原因'))
          return
        }
        // 先更新数据库状态
        const result = await updatePresetStatus({
            id: row.id,
            preset_status: 2
        })
        if (result.code !== 0 ) {
          ElMessage.error("更新preset_status状态失败")
          return
        }
        const updatedRes = await findCharacterAssets({id: row.id})
        if (updatedRes.code === 0) {
          const index = assetsList.value.findIndex(item => item.id === row.id)
          if (index !== -1) {
            assetsList.value[index] = updatedRes.data
          }
        }
      } catch (error) {
        ElMessage.error('添加预设失败: ' + (error.msg || '未知原因'))
      } finally {
        row.presetLoading = false
      }
    }).catch(() => {
    // 用户取消下架操作
  })
}

// 处理添加模型数据预设
const handleAddModel = async (row) => {
  let loadingInstance = null
  const mdoelExist = Boolean(currentCharacterModel.value.model_idx &&
                  currentCharacterModel.value.model_pth  &&  
                  currentCharacterModel.value.model_idx?.length > 0 && 
                  currentCharacterModel.value.model_pth?.length > 0)
  if (mdoelExist === false) {
     ElMessage.error("模型数据不存在")
     return
  }
  const rvcName = import.meta.env.VITE_APP_ENV === 'production' ? `rvcmodel_${row.id}` :  `test_rvcmodel_${row.id}`
  // 设置加载状态
  row.presetLoading = true
  loadingInstance = ElLoading.service({ fullscreen: true, text: '正在添加模型数据，请稍候...' })
  try {
    let res = await addModel({
      rvc_name: rvcName,
      netg_url: currentCharacterModel.value.model_pth,
      index_url: currentCharacterModel.value.model_idx
    })
    if (res.ret !== 1) {
       ElMessage.error('添加模型数据失败' + (res.msg || '未知原因'))
       return
    }
    ElMessage.success('添加模型数据成功！')
  } catch (error) {
    ElMessage.error('添加模型数据失败: ' + (error.msg || '未知原因'))
  } finally {
    row.presetLoading = false
    if (loadingInstance) loadingInstance.close()
  }
}


// 处理添加模型数据预设
const handleDeleteModel = async (row) => {
  let loadingInstance = null
  ElMessageBox.confirm(
    '确定要下架模型数据吗？',
    '提示',
    {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning'
    }
  ).then(async () => {
      const modelName = import.meta.env.VITE_APP_ENV === 'production' ? `rvcmodel_${row.id}` :  `test_rvcmodel_${row.id}`
      // 设置加载状态
      row.presetLoading = true
      loadingInstance = ElLoading.service({ fullscreen: true, text: '正在下架模型数据，请稍候...' })
      try {
        let res = await deleteModel({
          rvc_name: modelName,
        })
        if (res.ret !== 1) {
          ElMessage.error('下架模型数据失败，' + (res.msg || '未知原因'))
          return
        }
        ElMessage.success('下架模型数据成功！')
      } catch (error) {
        ElMessage.error('下架模型数据失败: ' + (error.msg || '未知原因'))
      } finally {
        row.presetLoading = false
        if (loadingInstance) loadingInstance.close()
      }
    }).catch(() => {
      
    })
}


const rvcModelDialogVisible = ref(false)
const rvcModelData = ref({})

// 处理更新预设
const handleGetModel = async (row) => {
  const env =  import.meta.env.VITE_APP_ENV === 'production' ? 'prod' : 'test'
  const rvcName = import.meta.env.VITE_APP_ENV === 'production' ? `rvcmodel_${row.id}` : `test_rvcmodel_${row.id}`
  // 设置加载状态
  try {
    const res = await getModel({
      env: env,
      rvc_name: rvcName
    })
    if (res.ret !== 1) {
       ElMessage.error('model数据不存在，' + (res.msg || '未知原因'))
       return
    }
    rvcModelData.value = res.data
    rvcModelDialogVisible.value = true
  } catch (error) {
    ElMessage.error('获取model失败: ' + (error.msg || '未知原因'))
  } finally {
    
  }
}

const closeRvcModelDialog = () => {
  rvcModelDialogVisible.value = false
  rvcModelData.value = {}
}


const presetDialogVisible = ref(false)
const presetData = ref({})

// 处理更新预设
const handleGetPreset = async (row) => {
  const env =  import.meta.env.VITE_APP_ENV === 'production' ? 'prod' : 'test'
  const presetName = import.meta.env.VITE_APP_ENV === 'production' ? `${row.character_id}_${row.id}` : `test_${row.character_id}_${row.id}`
  // 设置加载状态
  try {
    const res = await getPreset({
      env: env,
      preset_name: presetName
    })
    if (res.ret !== 1) {
       ElMessage.error('preset不存在，' + (res.msg || '未知原因'))
       return
    }
    presetData.value = res.data
    presetDialogVisible.value = true
  } catch (error) {
    ElMessage.error('获取preset失败: ' + (error.msg || '未知原因'))
  } finally {
    
  }
}

const closePresetDialog = () => {
  presetDialogVisible.value = false
  presetData.value = {}
}

const isEdit = ref(false)
const currentEditId = ref(null)

// 处理编辑角色资源
const handleEditAssets = async(row) => {
  isEdit.value = true
  currentEditId.value = row.id
  const res = await findCharacterAssets({id: row.id})
  console.log("res result:", res)
  if (res.code === 0){
    assetsFormData.value = res.data
  }
  addAssetsDialogVisible.value = true
}

const handleDeleteAssets = (row) => {
  ElMessageBox.confirm(
    '确定要下架该资源吗？',
    '提示',
    {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning'
    }
  ).then(async () => {
    try {
      const res = await deleteCharacterAssets({ id: row.id })
      if (res.code === 0) {
        ElMessage.success('下架成功')
        getAssetsList() // 刷新列表
      } else {
        ElMessage.error(res.msg || '下架失败')
      }
    } catch (error) {
      ElMessage.error('下架失败')
    }
  }).catch(() => {
    // 用户取消下架操作
  })
}

// TTS预览处理
const previewDialogVisible = ref(false)
const previewLoading = ref(false)
const speakerOptions = ref([])
const previewData = ref({
  character_id: 0,
  row_id: 0,
  sample_audio: '',
  sample_audio_text: '',
  preview_text: '',
  transformed_audio: '',
  rvc_audio: '',
  local_sample_audio: '',
  localAudioUrl: '',
  localPreviewAudioUrl: '',
  vc_text: '',
  use_rvc: true,
  tts_engine: 'llasa',
  tts_lang: 'zh',
  speaker: '',
})

const handlePreviewAudioDrop = (event) => {
  const files = event.dataTransfer.files
  if (files.length > 0) {
    const file = files[0]
    // 检查文件类型
    if (!file.type.startsWith('audio/')) {
      ElMessage.error('请上传音频文件')
      return
    }
    
    // 创建本地URL
    const localUrl = URL.createObjectURL(file)
    // 更新预览数据
    previewData.value.localAudioUrl = localUrl
    // 保持原有的 sample_audio 不变，用于其他功能
    previewData.value.sample_audio = localUrl
  }
}

const handleLocalPreviewAudioDrop = (event) => {
  const files = event.dataTransfer.files
  if (files.length > 0) {
    const file = files[0]
    // 检查文件类型
    if (!file.type.startsWith('audio/')) {
      ElMessage.error('请上传音频文件')
      return
    }
    
    // 创建本地URL
    const localUrl = URL.createObjectURL(file)
    // 更新预览数据
    previewData.value.localPreviewAudioUrl = localUrl
    // 保持原有的 sample_audio 不变，用于其他功能
    previewData.value.local_sample_audio = localUrl
  }
}

// 处理预览变声
const handlePreviewVoice = (row) => {
  if (!row.sample_audio) {
    ElMessage.warning('该资源没有引导音频')
    return
  }
  
  // 初始化预览数据
  previewData.value = {
    character_id: row.character_id,
    row_id: row.id,
    sample_audio: row.sample_audio,
    sample_audio_text: row.sample_audio_text || '',
    use_rvc: true,
    tts_engine: 'llasa',
    tts_lang: 'zh',
  }
  
  previewDialogVisible.value = true
}

// 关闭预览弹窗
const closePreviewDialog = () => {
  // 如果存在本地URL，释放它
  if (previewData.value.localAudioUrl) {
    URL.revokeObjectURL(previewData.value.localAudioUrl)
  }
  if (previewData.value.localPreviewAudioUrl) {
    URL.revokeObjectURL(previewData.value.localPreviewAudioUrl)
  }
  previewDialogVisible.value = false
  previewData.value = {
    character_id: 0, 
    row_id: 0,
    sample_audio: '',
    sample_audio_text: '',
    preview_text: '',
    transformed_audio: '',
    rvc_audio: '',
    local_sample_audio: '',
    use_rvc: true,
  }
}

// 获取引导音频时长
const getAudioDuration = (audioUrl) => {
  return new Promise((resolve) => {
    const audio = new Audio(audioUrl)
    audio.addEventListener('loadedmetadata', () => {
      resolve(audio.duration)
    })
    audio.addEventListener('error', () => {
      resolve(0)
    })
  })
}

// 处理变声转换
const handleTTS = async () => {
  if (!previewData.value.sample_audio) {
    ElMessage.warning('引导音频为空')
    return
  }
  if (!previewData.value.vc_text) {
    ElMessage.warning('请输入要变声的文本')
    return
  }
  if (previewData.value.tts_engine === 'llasa' && !previewData.value.sample_audio_text) {
    ElMessage.warning('请输入引导文本')
    return
  }
  if (previewData.value.tts_engine === 'coqui_speaker' && !previewData.value.speaker) {
    ElMessage.warning('选择speaker')
    return
  }
  //检查引导音频时长
  const duration = await getAudioDuration(previewData.value.sample_audio)
  console.log('duration:', duration)
  if (duration > 20) {
    ElMessageBox.alert(
      '引导音频时长超过20秒，可能会影响变声效果',
      '提示',
      {
        confirmButtonText: '知道了',
        type: 'warning'
      }
    )
    return  // 直接返回，不执行任何操作
  }
  if (previewData.value.tts_engine === 'llasa'){
      await processLlasaTTS()
  } else if (previewData.value.tts_engine === 'fish_speech') {
      await processFishSpeechTTS()
  } else {
      await processCoquiTTS()
  }
}

const processLlasaTTS = async () => {
  previewLoading.value = true
  try {
     // 如果存在本地音频URL，需要先上传到OSS
    const res = await llasa_tts({
      refer_audio_url: previewData.value.sample_audio,
      prompt_text: previewData.value.sample_audio_text,
      text: previewData.value.vc_text,
    })
    
    if (res.code === 0) {
      previewData.value.transformed_audio = res.data.url
      ElMessage.success('TTS成功')
    } else {
      ElMessage.error(res.msg || 'TTS失败')
    }
  } catch (error) {
    ElMessage.error('TTS失败' + (error.msg || '未知错误'))
  } finally {
    previewLoading.value = false
  }
}

const processFishSpeechTTS = async () => {
  previewLoading.value = true
  try {
     // 如果存在本地音频URL，需要先上传到OSS
    const res = await fish_speech_tts({
      refer_audio_url: previewData.value.sample_audio,
      prompt_text: previewData.value.sample_audio_text,
      text: previewData.value.vc_text,
    })
    
    if (res.code === 0) {
      previewData.value.transformed_audio = res.data.url
      ElMessage.success('TTS成功')
    } else {
      ElMessage.error(res.msg || 'TTS失败')
    }
  } catch (error) {
    ElMessage.error('TTS失败' + (error.msg || '未知错误'))
  } finally {
    previewLoading.value = false
  }
}

const processCoquiTTS = async () => {
  previewLoading.value = true
  try {
     // 如果存在本地音频URL，需要先上传到OSS
    let res = undefined
    if (previewData.value.tts_engine === 'coqui_reference') {
      res = await coqui_tts({
        refer_audio_url: previewData.value.sample_audio,
        text: previewData.value.vc_text,
        lang: previewData.value.tts_lang,
      })
    }else{
      res = await coqui_tts_by_voice({
        speaker: previewData.value.speaker,
        text: previewData.value.vc_text,
        lang: previewData.value.tts_lang,
      })
    } 
    
    if (res.code === 0) {
      previewData.value.transformed_audio = res.data.url
      ElMessage.success('TTS成功')
    } else {
      ElMessage.error(res.msg || 'TTS失败')
    }
  } catch (error) {
    ElMessage.error('TTS失败' + (error.msg || '未知错误'))
  } finally {
    previewLoading.value = false
  }
}

// 处理变声
const voiceChangeLoading = ref(false)


const handleLocalVoiceChange = async () => {
  if (!previewData.value.localPreviewAudioUrl) {
    ElMessage.error('本地变声音频为空')
    return
  }
  voiceChangeLoading.value = true
  try {
    // 从本地URL获取文件
    const response = await fetch(previewData.value.localPreviewAudioUrl)
    const blob = await response.blob()
    // 创建文件对象
    const file = new File([blob], 'audio.wav', { type: 'audio/wav' })
    
    // 上传到OSS
    const result = await uploadToOSS(file, 'audio', 'characters/refer_audio/local_preview')
    console.log('processTTS uploadToOSS url:', result.url)
    // 更新sample_audio为OSS的URL
    previewData.value.transformed_audio = result.url
    await handleVoiceChange()
  } catch (error) {
    ElMessage.error('处理本地音频变声失败' + (error.msg || '未知错误'))
  }finally {
     voiceChangeLoading.value = false
  }
}

const handleVoiceChange = async () => {
  if (!previewData.value.transformed_audio) {
    ElMessage.warning('请先进行 TTS 转换')
    return
  }
  voiceChangeLoading.value = true
  try {
    const env =  import.meta.env.VITE_APP_ENV === 'production' ? 'prod' : 'test'
    const preset_name = import.meta.env.VITE_APP_ENV === 'production' ?  `${previewData.value.character_id}_${previewData.value.row_id}` : `test_${previewData.value.character_id}_${previewData.value.row_id}`
    console.log('preset_name:', preset_name)
    const timestamp = Date.now()
    const uuid = uuidv4()
    const request_id = `${timestamp}-${uuid}`
    const mid = `${timestamp}`
    // 先获取preset
    const presetRes = await getPreset({
      env: env,
      preset_name: preset_name
    })
    console.log('返回结果:', presetRes)
    if (presetRes.ret !== 1) {
       throw new Error('获取presets失败')
    }
    // 从预设数据中找到对应的配置
    if (!presetRes.data?.rvc_name ) {
      throw new Error('rvc_name为空')
    }
    if (!presetRes.data?.reference_audio_url) {
      throw new Error('reference_audio_url为空')
    }
    const use_rvc = previewData.value.use_rvc
    console.log('handleVoiceChange use_rvc=', use_rvc)
    if (use_rvc) {
      const loadModelRes =   await loadModel({
        rvc_name: presetRes.data?.rvc_name,
        seed_name: 'whisper_small',
        refer_audio_url: presetRes.data?.reference_audio_url,
        request_id: request_id,
        mid:  mid
      }) 
      if (loadModelRes.ret !== 1) {
        throw new Error('加载预设失败')
      }
    }
    // 从 transformed_audio URL 获取音频文件
    const response = await fetch(previewData.value.transformed_audio)
    const blob = await response.blob()
    // 创建文件对象
    const file = new File([blob], 'transformed_audio.wav', { type: 'audio/wav'})
    
    // 上传到 OSS
    const result = await uploadToOSS(file, 'audio', 'characters/audio/preview')
    const audioUrl = result.url
    if (!audioUrl) {
       throw new Error('上传音频文件失败')
    }
    const rvcRes = await inferRvc(
       {
          rvc_name: presetRes.data?.rvc_name,
          seed_name: 'whisper_small', 
          reference_audio_url: presetRes.data?.reference_audio_url,
          request_id: request_id,
          mid:  mid,
          source_audio_url: audioUrl,
          cascaded_use_rvc: use_rvc 
       }
    )
    if (rvcRes.ret !== 1) {
       throw new Error('调用变声失败')
    }
    previewData.value.rvc_audio = rvcRes.data.final_output_url
  } catch (error) {
    ElMessage.error('变声失败：' + (error.message || '未知错误'))
  } finally {
    voiceChangeLoading.value = false
  }
}

// 新加资源列表
const addAssetsDialogVisible = ref(false)
const submitLoading = ref(false)
const assetsFormRef = ref(null)
const assetsFormData = ref({
  type: '',
  name: '',
  avatar_url: '',
  background_url: '',
  bg_theme_color: '',
  animated_url: '',
  animated_theme_color: '',
  sample_audio: '',
  sample_audio_text: '',
  is_default: false,
  sort: 0,
  status: 1,
  reference_audio_use_rvc: undefined
})

// 表单验证规则
const assetsFormRules = {
  type: [{ required: true, message: '请选择资源包类型', trigger: 'change' }],
  name: [{ required: true, message: '请输入资源包名称', trigger: 'blur' }],
  avatar_url: [{ required: true, message: '请上传角色头像', trigger: 'change' }],
  background_url: [{ required: true, message: '请上传角色背景图', trigger: 'change' }],
  sample_audio: [{ required: true, message: '请上传引导音频', trigger: 'change' }],
  sample_audio_text: [{ required: true, message: '请输入引导文本', trigger: 'blur' }]
}

// 打开新增资源弹窗
const openAssetsDialog = () => {
  isEdit.value = false
  currentEditId.value = null
  addAssetsDialogVisible.value = true
  assetsFormData.value = {
    type: '',
    name: '',
    avatar_url: '',
    background_url: '',
    bg_theme_color: '',
    animated_url: '',
    animated_theme_color: '',
    sample_audio: '',
    sample_audio_text: '',
    is_default: false,
    sort: 0,
    status: 1,
    reference_audio_use_rvc: undefined
  }
}

// 关闭新增资源弹窗
const closeAddAssetsDialog = () => {
  addAssetsDialogVisible.value = false
  isEdit.value = false
  currentEditId.value = null
  assetsFormRef.value?.resetFields()
}

let isUploading = false
// 文件上传处理
const handleFileChange = async (file, type) => {
  if (isUploading) {
    ElMessage.warning('正在上传中，请稍候...')
    return
  }
  let loading = null
  isUploading = true
  try {
    // 检查文件大小
    const maxSize = type === 'audio' ? 20 : 10 // 音频最大 10MB，图片最大 2MB
    checkFileSize(file.raw, maxSize)
    
    // 显示上传中状态
    loading = ElMessage({
      message: '文件上传中...',
      duration: 0,
      showClose: true
    })

    let sub_path = 'data'
    let biz_type = 'data'
    switch (type) {
      case 'avatar': 
         sub_path = 'characters/avatar'
         biz_type = 'image'
         break
      case 'preview_audio':
        sub_path = 'characters/preview_audio'
        biz_type = 'audio'
        break
      case 'background':
         sub_path = 'characters/background'
         biz_type = 'image'
         break
      case 'dynamic':
        sub_path = 'characters/dynamic'
        biz_type = 'image'
        break
      case 'audio':
        sub_path = 'characters/audio'
        biz_type = 'audio'
        break
    }
    
    // 上传到 OSS
    const result = await uploadToOSS(file.raw, biz_type, sub_path)
    
    // 更新表单数据
    switch (type) {
      case 'avatar':
        assetsFormData.value.avatar_url = result.url
        break
      case 'background':
        assetsFormData.value.background_url = result.url
        // 提取背景图主题色
        try {
          const themeColor = await extractThemeColorFromImage(result.url)
          assetsFormData.value.bg_theme_color = themeColor
          ElMessage.success('上传成功，已自动提取主题色')
        } catch (colorError) {
          console.warn('主题色提取失败:', colorError)
          ElMessage.success('上传成功')
        }
        return // 提前返回，避免重复显示成功消息
      case 'dynamic':
        assetsFormData.value.animated_url = result.url
        // 提取动态图主题色
        try {
          const themeColor = await extractThemeColorFromImage(result.url)
          assetsFormData.value.animated_theme_color = themeColor
          ElMessage.success('上传成功，已自动提取主题色')
        } catch (colorError) {
          console.warn('主题色提取失败:', colorError)
          ElMessage.success('上传成功')
        }
        return // 提前返回，避免重复显示成功消息
      case 'audio':
        assetsFormData.value.sample_audio = result.url
        break
      case 'preview_audio':
        formData.value.preview_audio = result.url
        break
    }

    ElMessage.success('上传成功')
    
  } catch (error) {
    ElMessage.error(error.message || '上传失败')
  } finally {
    isUploading = false
    // 确保在任何情况下都关闭 loading 消息
    if (loading) {
      loading.close()
    }
  }
}

// 提交表单时检查文件是否已上传
const submitAssetsForm = async () => {
  if (!assetsFormRef.value) return
  
  await assetsFormRef.value.validate(async (valid) => {
    if (!valid) return
    
    // 检查必填文件是否已上传
    if (!assetsFormData.value.avatar_url) {
      ElMessage.warning('请上传角色头像')
      return
    }
    if (!assetsFormData.value.sample_audio) {
      ElMessage.warning('请上传引导音频')
      return
    }
    
    submitLoading.value = true
    try {
      let res
      if (isEdit.value) {
        res = await updateCharacterAssets({
          id: currentEditId.value,
          ...assetsFormData.value,
          character_id: currentCharacterId.value,
          preset_status: 2
        })
      }else{
        res = await createCharacterAssets({
          ...assetsFormData.value,
          character_id: currentCharacterId.value,
          preset_status: 2
        })
      }
      if (res.code === 0) {
        ElMessage.success(isEdit.value ? '编辑成功' : '添加成功')
        closeAddAssetsDialog()
        getAssetsList() // 刷新列表
      } else {
        ElMessage.error(res.msg ||  (isEdit.value ? '编辑失败' : '添加失败'))
      }
    } catch (error) {
      ElMessage.error(isEdit.value ? '编辑失败' : '添加失败')
    } finally {
      submitLoading.value = false
    }
  })
}

// 加载ip列表相关
const ipList = ref([])

const getIpOptions = async () => {
  const res = await getAllIps({ page: 1, pageSize: 1000 })
  if (res.code === 0) {
    ipList.value = res.data.list
  }
}

const handleIpChange = (ipId) => {
  const selectedIp = ipList.value.find(ip => ip.id === ipId)
  if (selectedIp) {
    formData.value.ip_name = selectedIp.name
  }
}

// 处理推荐状态下拉列表变更
const handleRecommendChange = async (row, newValue) => {
  console.log('handleRecommendChange called', { row: row.id, newValue })

  // 获取原始值（在v-model更新之前）
  const originalValue = row.is_recommended === newValue ? !newValue : row.is_recommended

  // 如果值没有变化，直接返回
  if (originalValue === newValue) {
    console.log('值没有变化，跳过操作')
    return
  }

  row.recommendLoading = true

  try {
    console.log('调用API设置推荐状态', { id: row.id, is_recommended: newValue })
    const res = await setCharacterRecommended({
      id: row.id,
      is_recommended: newValue
    })
    console.log('API响应', res)

    if (res.code === 0) {
      ElMessage.success(`${newValue ? '设为推荐' : '设为不推荐'}成功`)
    } else {
      // 操作失败，恢复原值
      row.is_recommended = originalValue
      ElMessage.error(res.msg || '操作失败')
    }
  } catch (error) {
    console.error('API调用失败', error)
    // 操作失败，恢复原值
    row.is_recommended = originalValue
    ElMessage.error('操作失败: ' + (error.message || '未知错误'))
  } finally {
    row.recommendLoading = false
  }
}



onMounted(() => {
  getIpOptions()
})

// 添加复制功能
const copyModelPth = async (text) => {
  try {
    await navigator.clipboard.writeText(text)
    ElMessage.success('复制成功')
  } catch (err) {
    ElMessage.error('复制失败')
  }
}

// 获取类型标签颜色
const getTypeTagType = (type) => {
  const typeMap = {
    'normal': '',
    'happy': 'success',
    'sad': 'info',
    'excited': 'warning'
  }
  return typeMap[type] || ''
}

const getSpeakers = async () => {
  try {
    const res = await coqui_tts_speakers()
    speakerOptions.value = res.data.list || []
  } catch (error) {
    console.error('获取 Coqui 说话人列表失败:', error)
  }
}

const lastTtsEngine = ref('')

watch(() => previewData.value.tts_engine, async (newVal, oldVal) => {
  if (newVal === coqui_speaker && oldVal !== coqui_speaker) {
    // 只在切换到 coqui_speaker 时调用
    await getSpeakers()
  }
  lastTtsEngine.value = newVal
})
</script>

<style scoped>
.avatar-uploader,
.background-uploader,
.dynamic-uploader {
  border: 1px dashed #d9d9d9;
  border-radius: 6px;
  cursor: pointer;
  position: relative;
  overflow: hidden;
}

.avatar-uploader:hover,
.background-uploader:hover,
.dynamic-uploader:hover {
  border-color: #409EFF;
}

.avatar-uploader-icon,
.background-uploader-icon,
.dynamic-uploader-icon {
  font-size: 28px;
  color: #8c939d;
  width: 100px;
  height: 100px;
  text-align: center;
  line-height: 100px;
}

.avatar {
  width: 100px;
  height: 100px;
  display: block;
}

.background {
  width: 200px;
  height: 200px;
  display: block;
}

.dynamic {
  width: 200px;
  height: 200px;
  display: block;
}

.audio-uploader {
  margin-top: 10px;
}

.audio-drop-zone {
  border: 2px dashed #d9d9d9;
  border-radius: 4px;
  padding: 10px;
  text-align: center;
  cursor: pointer;
  transition: border-color 0.3s;
  min-height: 100px;
  display: flex;
  align-items: center;
  justify-content: center;
  margin: 10px 0;
}

.audio-drop-zone:hover {
  border-color: #409EFF;
}

.drop-hint {
  color: #909399;
  font-size: 14px;
}

/* 推荐状态单元格样式 */
.recommend-cell {
    display: flex;
  align-items: center;
  gap: 8px;
}
.model-pth-cell {
  display: flex;
  align-items: center;
  gap: 8px;
}

/* 推荐星标样式 */
.recommend-star {
  color: #f39c12;
  flex-shrink: 0;
}

.ellipsis {
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  flex: 1;
}

:deep(.el-descriptions__content) {
  white-space: pre-wrap;
  word-break: break-all;
  word-wrap: break-word;
}

/* 资源列表弹框样式 */
.assets-dialog {
  .el-dialog__body {
    padding: 10px 20px;
  }
}

.assets-table {
  .el-table__cell {
    padding: 8px 0;
  }
}

/* 基础信息样式 */
.basic-info {
  .info-row {
    display: flex;
    align-items: center;
    margin-bottom: 4px;
    font-size: 12px;

    .label {
      color: #909399;
      width: 35px;
      flex-shrink: 0;
    }

    .value {
      color: #303133;
      flex: 1;
      overflow: hidden;
      text-overflow: ellipsis;
      white-space: nowrap;
    }
  }
}

/* 视觉资源样式 */
.visual-resources {
  .resource-item {
    display: flex;
    align-items: center;
    margin-bottom: 8px;
    font-size: 12px;

    .resource-label {
      color: #909399;
      width: 40px;
      flex-shrink: 0;
    }

    .no-resource {
      color: #C0C4CC;
      font-style: italic;
      font-size: 12px;
    }
  }

  .background-preview,
  .animated-preview {
    display: flex;
    align-items: center;
    gap: 8px;

    .theme-color {
      .color-block {
        width: 16px;
        height: 16px;
        border-radius: 2px;
        border: 1px solid #ddd;
        cursor: pointer;
      }
    }
  }
}

/* 音频配置样式 */
.audio-config {
  .config-item {
    margin-bottom: 8px;
    font-size: 12px;

    .config-label {
      color: #909399;
      display: block;
      margin-bottom: 4px;
    }

    .text-content {
      color: #303133;
      display: block;
      max-width: 300px;
      overflow: hidden;
      text-overflow: ellipsis;
      white-space: nowrap;
      cursor: pointer;
      line-height: 1.4;
    }

    .no-resource {
      color: #C0C4CC;
      font-style: italic;
      font-size: 11px;
    }
  }
}

/* RVC配置样式 */
.rvc-config {
  .config-item {
    margin-bottom: 8px;
    font-size: 12px;

    .config-label {
      color: #909399;
      display: block;
      margin-bottom: 4px;
    }
  }

  .config-item-inline {
    display: flex;
    align-items: center;
    margin-bottom: 6px;
    font-size: 12px;
    gap: 6px;

    .config-label-inline {
      color: #909399;
      white-space: nowrap;
      flex-shrink: 0;
      min-width: 60px;
      font-size: 11px;
    }
  }
}

/* 预设状态样式 */
.preset-status {
  display: flex;
  justify-content: center;
}

/* 时间信息样式 */
.time-info {
  .time-item {
    margin-bottom: 4px;
    font-size: 11px;

    .time-label {
      color: #909399;
      display: inline-block;
      width: 35px;
    }

    .time-value {
      color: #606266;
    }
  }
}

/* 操作按钮样式 */
.action-buttons {
  .action-group {
    display: flex;
    align-items: center;
    margin-bottom: 8px;
    gap: 4px;

    .group-label {
      color: #909399;
      font-size: 11px;
      width: 35px;
      flex-shrink: 0;
    }

    .el-button {
      margin-left: 0;
      margin-right: 4px;

      &:last-child {
        margin-right: 0;
      }
    }
  }
}

/* 预设按钮特殊样式 */
.preset-btn.preset-completed {
  background-color: #f0f9ff;
  border-color: #67c23a;
  color: #67c23a;
  cursor: not-allowed;

  &:hover {
    background-color: #f0f9ff;
    border-color: #67c23a;
    color: #67c23a;
  }
}
</style>
