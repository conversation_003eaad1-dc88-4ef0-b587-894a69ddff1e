<template>
  <div class="audio-player" v-if="src">
    <div class="player-controls">
      <el-button 
        :icon="isPlaying ? VideoPause : VideoPlay" 
        circle 
        size="small"
        @click="togglePlay"
        :loading="loading"
      />
      <div class="progress-container">
        <span class="time">{{ formatTime(currentTime) }}</span>
        <el-slider
          v-model="progress"
          :max="100"
          :show-tooltip="false"
          @change="onProgressChange"
          class="progress-slider"
        />
        <span class="time">{{ formatTime(displayDuration) }}</span>
      </div>
      <el-button 
        :icon="isMuted ? Mute : Microphone" 
        circle 
        size="small"
        @click="toggleMute"
      />
    </div>
    <audio
      ref="audioRef"
      :src="src"
      preload="metadata"
      @loadedmetadata="onLoadedMetadata"
      @timeupdate="onTimeUpdate"
      @ended="onEnded"
      @error="onError"
      style="display: none;"
    />

    <!-- 错误提示 -->
    <div v-if="error" class="error-message">
      <el-alert
        :title="error"
        type="error"
        :closable="false"
        show-icon
        size="small"
      />
    </div>
  </div>
  <span v-else>-</span>
</template>

<script setup>
import { ref, computed, watch, onUnmounted } from 'vue'
import { VideoPlay, VideoPause, Microphone, Mute } from '@element-plus/icons-vue'

const props = defineProps({
  src: {
    type: String,
    default: ''
  },
  autoplay: {
    type: Boolean,
    default: false
  },
  duration: {
    type: Number,
    default: 0
  }
})

const emit = defineEmits(['error', 'play', 'pause', 'ended'])

const audioRef = ref(null)
const isPlaying = ref(false)
const isMuted = ref(false)
const loading = ref(false)
const currentTime = ref(0)
const audioDuration = ref(0)
const progress = ref(0)
const error = ref('')

// 计算显示的时长（优先使用音频实际时长，其次使用传入的duration）
const displayDuration = computed(() => {
  // 检查音频实际时长是否有效
  if (audioDuration.value > 0 && isFinite(audioDuration.value) && !isNaN(audioDuration.value)) {
    return audioDuration.value
  }

  // 使用传入的duration参数
  if (props.duration > 0 && isFinite(props.duration) && !isNaN(props.duration)) {
    return props.duration / 1000 // 传入的duration是毫秒，需要转换为秒
  }

  return 0
})

// 格式化时间
const formatTime = (seconds) => {
  if (!seconds || isNaN(seconds)) return '00:00'
  const mins = Math.floor(seconds / 60)
  const secs = Math.floor(seconds % 60)
  return `${mins.toString().padStart(2, '0')}:${secs.toString().padStart(2, '0')}`
}

// 播放/暂停
const togglePlay = async () => {
  if (!audioRef.value) return

  try {
    loading.value = true
    error.value = ''
    if (isPlaying.value) {
      audioRef.value.pause()
      emit('pause')
    } else {
      await audioRef.value.play()
      emit('play')
    }
  } catch (err) {
    console.error('播放失败:', err)
    error.value = '播放失败：' + err.message
    emit('error', err)
  } finally {
    loading.value = false
  }
}

// 静音/取消静音
const toggleMute = () => {
  if (!audioRef.value) return
  audioRef.value.muted = !audioRef.value.muted
  isMuted.value = audioRef.value.muted
}

// 进度条改变
const onProgressChange = (value) => {
  if (!audioRef.value || !displayDuration.value) return
  const newTime = (value / 100) * displayDuration.value
  audioRef.value.currentTime = newTime
}

// 音频元数据加载完成
const onLoadedMetadata = () => {
  if (audioRef.value) {
    const audioDur = audioRef.value.duration

    // 检查音频时长是否有效，如果无效则不设置 audioDuration
    // 这样 displayDuration 会使用传入的 props.duration
    if (audioDur && !isNaN(audioDur) && isFinite(audioDur) && audioDur > 0) {
      audioDuration.value = audioDur
    }
    // 如果音频元数据时长无效，不设置 audioDuration.value，让 displayDuration 使用 props.duration

    if (props.autoplay) {
      togglePlay()
    }
  }
}

// 时间更新
const onTimeUpdate = () => {
  if (audioRef.value) {
    currentTime.value = audioRef.value.currentTime
    if (displayDuration.value > 0) {
      progress.value = (currentTime.value / displayDuration.value) * 100
    }
  }
}

// 播放结束
const onEnded = () => {
  isPlaying.value = false
  progress.value = 0
  currentTime.value = 0
  emit('ended')
}

// 播放错误
const onError = (event) => {
  console.error('音频播放错误:', event)
  loading.value = false
  isPlaying.value = false
  error.value = '音频加载失败，请检查音频文件是否存在'
  emit('error', event)
}

// 监听播放状态
watch(() => audioRef.value, (audio) => {
  if (audio) {
    audio.addEventListener('play', () => {
      isPlaying.value = true
    })
    audio.addEventListener('pause', () => {
      isPlaying.value = false
    })
  }
})

// 组件卸载时停止播放
onUnmounted(() => {
  if (audioRef.value) {
    audioRef.value.pause()
  }
})
</script>

<style scoped>
.audio-player {
  display: flex;
  align-items: center;
  width: 100%;
  min-width: 280px;
}

.player-controls {
  display: flex;
  align-items: center;
  gap: 8px;
  width: 100%;
}

.progress-container {
  display: flex;
  align-items: center;
  gap: 6px;
  flex: 1;
  min-width: 160px;
  max-width: 160px;
}

.progress-slider {
  flex: 1;
  margin: 0 4px;
  min-width: 80px;
}

.time {
  font-size: 11px;
  color: #666;
  white-space: nowrap;
  min-width: 32px;
  text-align: center;
}

:deep(.el-slider__runway) {
  height: 4px;
}

:deep(.el-slider__button) {
  width: 12px;
  height: 12px;
}

.error-message {
  margin-top: 8px;
}
</style>
