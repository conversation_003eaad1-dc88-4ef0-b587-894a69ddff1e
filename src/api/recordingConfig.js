import service from '@/utils/request'

// 获取录音配置
export const getRecordingConfig = (data) => {
  return service({
    url: '/recordingConfig/getRecordingConfig',
    method: 'post',
    data
  })
}

// 根据台词ID获取录音配置
export const getRecordingConfigByLineId = (lineId, scene = 'dubbing') => {
  return service({
    url: '/recordingConfig/getRecordingConfigByLineId',
    method: 'get',
    params: {
      line_id: lineId,
      scene: scene
    }
  })
}
