package controller

import (
	"context"
	"new-gitlab.xunlei.cn/vcproject/backends/baselib/xlaccount"

	"new-gitlab.xunlei.cn/vcproject/backends/baselib/errcode"
	"new-gitlab.xunlei.cn/vcproject/backends/proto/api/bizscript"
	"new-gitlab.xunlei.cn/vcproject/backends/proto/api/svcscript"
	"new-gitlab.xunlei.cn/vcproject/backends/proto/svcmgr"
)

var (
	_ bizscript.SServer = new(Controller)
)

type Controller struct{}

func NewController() *Controller {
	svcmgr.InitServices()
	return &Controller{}
}

// GetCharacterList 获取角色列表
func (c *Controller) GetCharacterList(ctx context.Context, req *bizscript.GetCharacterListReq) (*bizscript.GetCharacterListResp, error) {
	resp := &bizscript.GetCharacterListResp{}

	if err := req.Validate(); err != nil {
		errcode.ErrorParam.SetTo(resp)
		return resp, nil
	}

	svcReq := &svcscript.GetCharacterListReq{
		Page:     req.Page,
		PageSize: req.PageSize,
		IpId:     req.IpId,
	}

	svcResp, err := svcmgr.ScriptClient().GetCharacterList(ctx, svcReq)
	if err != nil {
		errResp := errcode.FromError(err)
		resp.Code = errResp.Code
		resp.Msg = errResp.Msg
		return resp, nil
	}

	errcode.ErrOK.SetTo(resp, svcResp.Data)
	return resp, nil
}

// SearchCharacters 搜索角色
func (c *Controller) SearchCharacters(ctx context.Context, req *bizscript.SearchCharactersReq) (*bizscript.SearchCharactersResp, error) {
	resp := &bizscript.SearchCharactersResp{}
	if err := req.Validate(); err != nil {
		errcode.ErrorParam.SetTo(resp)
		return resp, nil
	}

	svcReq := &svcscript.SearchCharactersReq{
		Keyword:  req.Keyword,
		Page:     req.Page,
		PageSize: req.PageSize,
	}

	svcResp, err := svcmgr.ScriptClient().SearchCharacters(ctx, svcReq)
	if err != nil {
		errResp := errcode.FromError(err)
		resp.Code = errResp.Code
		resp.Msg = errResp.Msg
		return resp, nil
	}

	errcode.ErrOK.SetTo(resp, svcResp.Data)
	return resp, nil
}

// SetUserCharacter 设置用户角色
func (c *Controller) SetUserCharacter(ctx context.Context, req *bizscript.SetUserCharacterReq) (*bizscript.SetUserCharacterResp, error) {
	resp := &bizscript.SetUserCharacterResp{}
	if err := req.Validate(); err != nil {
		errcode.ErrorParam.SetTo(resp)
		return resp, nil
	}

	authInfo := xlaccount.GetAuthInfo(ctx)
	if authInfo == nil || authInfo.UserId == 0 {
		errcode.ErrUserNotFound.SetTo(resp)
		return resp, nil
	}

	svcReq := &svcscript.SetUserCharacterReq{
		CharacterId:      req.CharacterId,
		UserId:           authInfo.UserId,
		CharacterAssetId: req.CharacterAssetId,
	}

	_, err := svcmgr.ScriptClient().SetUserCharacter(ctx, svcReq)

	if err != nil {
		errcode.FromError(err).SetTo(resp)
		return resp, nil
	}

	errcode.ErrOK.SetTo(resp)
	return resp, nil
}

// GetUserCharacters 获取用户角色
func (c *Controller) GetUserCharacters(ctx context.Context, req *bizscript.GetUserCharactersReq) (*bizscript.GetUserCharactersResp, error) {
	resp := &bizscript.GetUserCharactersResp{}
	if err := req.Validate(); err != nil {
		errcode.ErrorParam.SetTo(resp)
		return resp, nil
	}

	authInfo := xlaccount.GetAuthInfo(ctx)
	if authInfo == nil {
		errcode.ErrUserNotFound.SetTo(resp)
		return resp, nil
	}

	svcReq := &svcscript.GetUserCharactersReq{
		UserId: authInfo.UserId,
	}

	svcResp, err := svcmgr.ScriptClient().GetUserCharacters(ctx, svcReq)
	if err != nil {
		errcode.FromError(err).SetTo(resp)
		return resp, nil
	}

	errcode.ErrOK.SetTo(resp, svcResp.Data)
	return resp, nil
}

// BatchCheckCharacterAvailability 批量检查角色可用性
func (c *Controller) BatchCheckCharacterAvailability(ctx context.Context, req *bizscript.BatchCheckCharacterAvailabilityReq) (*bizscript.BatchCheckCharacterAvailabilityResp, error) {
	resp := &bizscript.BatchCheckCharacterAvailabilityResp{}
	if err := req.Validate(); err != nil {
		errcode.ErrorParam.SetTo(resp)
		return resp, nil
	}

	svcReq := &svcscript.BatchCheckCharacterAvailabilityReq{
		CharacterIds: req.CharacterIds,
	}

	svcResp, err := svcmgr.ScriptClient().BatchCheckCharacterAvailability(ctx, svcReq)
	if err != nil {
		errcode.FromError(err).SetTo(resp)
		return resp, nil
	}

	errcode.ErrOK.SetTo(resp, svcResp.Data)
	return resp, nil
}

// GetTopicList 获取话题列表
func (c *Controller) GetTopicList(ctx context.Context, req *bizscript.GetTopicListReq) (*bizscript.GetTopicListResp, error) {
	resp := &bizscript.GetTopicListResp{}
	if err := req.Validate(); err != nil {
		errcode.ErrorParam.SetTo(resp)
		return resp, nil
	}

	svcReq := &svcscript.GetTopicListReq{
		Page:     req.Page,
		PageSize: req.PageSize,
		Scene:    req.Scene,
	}

	svcResp, err := svcmgr.ScriptClient().GetTopicList(ctx, svcReq)
	if err != nil {
		errcode.FromError(err).SetTo(resp)
		return resp, nil
	}

	errcode.ErrOK.SetTo(resp, svcResp.Data)
	return resp, nil
}

// CreateScript 创建剧本
func (c *Controller) CreateScript(ctx context.Context, req *bizscript.CreateScriptReq) (*bizscript.CreateScriptResp, error) {
	resp := &bizscript.CreateScriptResp{}
	if err := req.Validate(); err != nil {
		errcode.ErrorParam.SetTo(resp)
		return resp, nil
	}

	authInfo := xlaccount.GetAuthInfo(ctx)
	if authInfo == nil || authInfo.UserId == 0 {
		errcode.ErrUserNotFound.SetTo(resp)
		return resp, nil
	}

	svcReq := &svcscript.CreateScriptReq{
		Title:             req.Title,
		Cover:             req.Cover,
		CharacterAssetIds: req.CharacterAssetIds,
		TopicNames:        req.TopicNames,
		Lines:             req.Lines,
		AuthorId:          authInfo.UserId,
		BgmUrl:            req.BgmUrl,
		BgmDuration:       req.BgmDuration,
	}

	svcResp, err := svcmgr.ScriptClient().CreateScript(ctx, svcReq)
	if err != nil {
		errcode.FromError(err).SetTo(resp)
		return resp, nil
	}

	errcode.ErrOK.SetTo(resp, svcResp.Data)
	return resp, nil
}

func (c *Controller) DeleteScript(ctx context.Context, req *bizscript.DeleteScriptReq) (*bizscript.DeleteScriptResp, error) {
	resp := &bizscript.DeleteScriptResp{}

	if err := req.Validate(); err != nil {
		errcode.ErrorParam.SetTo(resp)
		return resp, nil
	}

	authInfo := xlaccount.GetAuthInfo(ctx)
	if authInfo == nil || authInfo.UserId == 0 {
		errcode.ErrUserNotFound.SetTo(resp)
		return resp, nil
	}

	svcReq := &svcscript.DeleteScriptReq{ScriptId: req.ScriptId}
	_, err := svcmgr.ScriptClient().DeleteScript(ctx, svcReq)
	if err != nil {
		errcode.FromError(err).SetTo(resp)
		return resp, nil
	}

	errcode.ErrOK.SetTo(resp)
	return resp, nil
}

// GetScriptList 获取剧本列表
func (c *Controller) GetScriptList(ctx context.Context, req *bizscript.GetScriptListReq) (*bizscript.GetScriptListResp, error) {
	resp := &bizscript.GetScriptListResp{}
	if err := req.Validate(); err != nil {
		errcode.ErrorParam.SetTo(resp)
		return resp, nil
	}

	authInfo := xlaccount.GetAuthInfo(ctx)
	if authInfo == nil {
		errcode.ErrUserNotFound.SetTo(resp)
		return resp, nil
	}

	svcReq := &svcscript.GetScriptListReq{
		TopicId:       req.TopicId,
		Page:          req.Page,
		PageSize:      req.PageSize,
		UserId:        authInfo.UserId,
		IsAggregation: req.IsAggregation,
	}

	svcResp, err := svcmgr.ScriptClient().GetScriptList(ctx, svcReq)
	if err != nil {
		errcode.FromError(err).SetTo(resp)
		return resp, nil
	}

	errcode.ErrOK.SetTo(resp, svcResp.Data)
	return resp, nil
}

// GetScriptDetail 获取剧本详情
func (c *Controller) GetScriptDetail(ctx context.Context, req *bizscript.GetScriptDetailReq) (*bizscript.GetScriptDetailResp, error) {
	resp := &bizscript.GetScriptDetailResp{}
	if err := req.Validate(); err != nil {
		errcode.ErrorParam.SetTo(resp)
		return resp, nil
	}

	authInfo := xlaccount.GetAuthInfo(ctx)
	if authInfo == nil {
		errcode.ErrUserNotFound.SetTo(resp)
		return resp, nil
	}

	svcReq := &svcscript.GetScriptDetailReq{
		ScriptId: req.ScriptId,
		UserId:   authInfo.UserId,
	}

	svcResp, err := svcmgr.ScriptClient().GetScriptDetail(ctx, svcReq)
	if err != nil {
		errcode.FromError(err).SetTo(resp)
		return resp, nil
	}

	errcode.ErrOK.SetTo(resp, svcResp.Data)
	return resp, nil
}

// SearchScripts 搜索剧本
func (c *Controller) Search(ctx context.Context, req *bizscript.SearchReq) (*bizscript.SearchResp, error) {
	resp := &bizscript.SearchResp{}
	if err := req.Validate(); err != nil {
		errcode.ErrorParam.SetTo(resp)
		return resp, nil
	}

	authInfo := xlaccount.GetAuthInfo(ctx)
	if authInfo == nil {
		errcode.ErrUserNotFound.SetTo(resp)
		return resp, nil
	}

	svcReq := &svcscript.SearchReq{
		Keyword:    req.Keyword,
		SearchType: req.SearchType,
		Page:       req.Page,
		PageSize:   req.PageSize,
		UserId:     authInfo.UserId,
	}

	svcResp, err := svcmgr.ScriptClient().Search(ctx, svcReq)
	if err != nil {
		errcode.FromError(err).SetTo(resp)
		return resp, nil
	}

	errcode.ErrOK.SetTo(resp, svcResp.Data)
	return resp, nil
}

// ShareScript 分享剧本
func (c *Controller) ShareScript(ctx context.Context, req *bizscript.ShareScriptReq) (*bizscript.ShareScriptResp, error) {
	resp := &bizscript.ShareScriptResp{}
	if err := req.Validate(); err != nil {
		errcode.ErrorParam.SetTo(resp)
		return resp, nil
	}

	authInfo := xlaccount.GetAuthInfo(ctx)
	if authInfo == nil {
		errcode.ErrUserNotFound.SetTo(resp)
		return resp, nil
	}

	svcReq := &svcscript.ShareScriptReq{
		ScriptId: req.ScriptId,
		Platform: req.Platform,
		UserId:   authInfo.UserId,
	}

	_, err := svcmgr.ScriptClient().ShareScript(ctx, svcReq)
	if err != nil {
		errcode.FromError(err).SetTo(resp)
		return resp, nil
	}

	errcode.ErrOK.SetTo(resp)
	return resp, nil
}

func (c *Controller) GetLineDubbingSimple(ctx context.Context, req *bizscript.GetLineDubbingSimpleReq) (*bizscript.GetLineDubbingSimpleResp, error) {
	resp := &bizscript.GetLineDubbingSimpleResp{}
	if err := req.Validate(); err != nil {
		errcode.ErrorParam.SetTo(resp)
		return resp, nil
	}

	authInfo := xlaccount.GetAuthInfo(ctx)
	if authInfo == nil {
		errcode.ErrUserNotFound.SetTo(resp)
		return resp, nil
	}

	svcReq := &svcscript.GetLineDubbingsSimpleReq{
		ScriptId:        req.ScriptId,
		UserId:          authInfo.UserId,
		DubbingRecordId: req.DubbingRecordId,
	}

	svcResp, err := svcmgr.ScriptClient().GetLineDubbingsSimple(ctx, svcReq)
	if err != nil {
		errcode.FromError(err).SetTo(resp)
		return resp, nil
	}
	errcode.ErrOK.SetTo(resp, svcResp.Data)
	return resp, nil
}

// GetLineDubbings 获取台词配音列表
func (c *Controller) GetLineDubbingAll(ctx context.Context, req *bizscript.GetLineDubbingAllReq) (*bizscript.GetLineDubbingAllResp, error) {
	resp := &bizscript.GetLineDubbingAllResp{}
	if err := req.Validate(); err != nil {
		errcode.ErrorParam.SetTo(resp)
		return resp, nil
	}

	authInfo := xlaccount.GetAuthInfo(ctx)
	if authInfo == nil {
		errcode.ErrUserNotFound.SetTo(resp)
		return resp, nil
	}

	svcReq := &svcscript.GetLineDubbingsReq{
		ScriptId: req.ScriptId,
		LineId:   req.LineId,
		Page:     req.Page,
		PageSize: req.PageSize,
		UserId:   authInfo.UserId,
	}

	svcResp, err := svcmgr.ScriptClient().GetLineDubbings(ctx, svcReq)
	if err != nil {
		errcode.FromError(err).SetTo(resp)
		return resp, nil
	}
	errcode.ErrOK.SetTo(resp, svcResp.Data)
	return resp, nil
}

// 获取剧本台词列表首条配音
func (c *Controller) GetScriptFirstDubbing(ctx context.Context, req *bizscript.GetScriptFirstDubbingReq) (*bizscript.GetScriptFirstDubbingResp, error) {
	resp := &bizscript.GetScriptFirstDubbingResp{}
	if err := req.Validate(); err != nil {
		errcode.ErrorParam.SetTo(resp)
		return resp, nil
	}

	authInfo := xlaccount.GetAuthInfo(ctx)
	if authInfo == nil {
		errcode.ErrUserNotFound.SetTo(resp)
		return resp, nil
	}

	svcReq := &svcscript.GetScriptFirstDubbingReq{
		ScriptId: req.ScriptId,
		UserId:   authInfo.UserId,
	}

	svcResp, err := svcmgr.ScriptClient().GetScriptFirstDubbing(ctx, svcReq)
	if err != nil {
		errcode.FromError(err).SetTo(resp)
		return resp, nil
	}
	errcode.ErrOK.SetTo(resp, svcResp.Data)
	return resp, nil
}

// 批量获取剧本台词列表首条配音
func (c *Controller) BatchGetScriptFirstDubbing(ctx context.Context, req *bizscript.BatchGetScriptFirstDubbingReq) (*bizscript.BatchGetScriptFirstDubbingResp, error) {
	resp := &bizscript.BatchGetScriptFirstDubbingResp{}
	if err := req.Validate(); err != nil {
		errcode.ErrorParam.SetTo(resp)
		return resp, nil
	}

	authInfo := xlaccount.GetAuthInfo(ctx)
	if authInfo == nil {
		errcode.ErrUserNotFound.SetTo(resp)
		return resp, nil
	}

	svcReq := &svcscript.BatchGetScriptFirstDubbingReq{
		ScriptIds: req.ScriptIds,
		UserId:    authInfo.UserId,
	}

	svcResp, err := svcmgr.ScriptClient().BatchGetScriptFirstDubbing(ctx, svcReq)
	if err != nil {
		errcode.FromError(err).SetTo(resp)
		return resp, nil
	}
	errcode.ErrOK.SetTo(resp, svcResp.Data)
	return resp, nil
}

// CreateDubbing 创建配音
func (c *Controller) CreateDubbing(ctx context.Context, req *bizscript.CreateDubbingReq) (*bizscript.CreateDubbingResp, error) {
	resp := &bizscript.CreateDubbingResp{}
	if err := req.Validate(); err != nil {
		errcode.ErrorParam.SetTo(resp)
		return resp, nil
	}

	authInfo := xlaccount.GetAuthInfo(ctx)
	if authInfo == nil || authInfo.UserId == 0 {
		errcode.ErrUserNotFound.SetTo(resp)
		return resp, nil
	}

	svcReq := &svcscript.CreateDubbingReq{
		UserId:       authInfo.UserId,
		ScriptId:     req.ScriptId,
		LineDubbings: req.LineDubbings,
	}

	svcResp, err := svcmgr.ScriptClient().CreateDubbing(ctx, svcReq)
	if err != nil {
		errcode.FromError(err).SetTo(resp)
		return resp, nil
	}

	errcode.ErrOK.SetTo(resp, svcResp.Data)
	return resp, nil
}

// DeleteDubbingRecord 删除配音记录
func (c *Controller) DeleteDubbingRecord(ctx context.Context, req *bizscript.DeleteDubbingRecordReq) (*bizscript.DeleteDubbingRecordResp, error) {
	resp := &bizscript.DeleteDubbingRecordResp{}
	if err := req.Validate(); err != nil {
		errcode.ErrorParam.SetTo(resp)
		return resp, nil
	}

	authInfo := xlaccount.GetAuthInfo(ctx)
	if authInfo == nil || authInfo.UserId == 0 {
		errcode.ErrUserNotFound.SetTo(resp)
		return resp, nil
	}

	svcReq := &svcscript.DeleteDubbingRecordReq{
		DubbingRecordId: req.DubbingRecordId,
		UserId:          authInfo.UserId,
	}

	_, err := svcmgr.ScriptClient().DeleteDubbingRecord(ctx, svcReq)
	if err != nil {
		errcode.FromError(err).SetTo(resp)
		return resp, nil
	}

	errcode.ErrOK.SetTo(resp)
	return resp, nil
}

// GetCommentList 获取评论列表
func (c *Controller) GetCommentList(ctx context.Context, req *bizscript.GetCommentListReq) (*bizscript.GetCommentListResp, error) {
	resp := &bizscript.GetCommentListResp{}
	if err := req.Validate(); err != nil {
		errcode.ErrorParam.SetTo(resp)
		return resp, nil
	}

	authInfo := xlaccount.GetAuthInfo(ctx)
	if authInfo == nil {
		errcode.ErrUserNotFound.SetTo(resp)
		return resp, nil
	}

	svcReq := &svcscript.GetCommentListReq{
		CommentType: req.CommentType,
		ScriptId:    req.ScriptId,
		DubbingId:   req.DubbingId,
		ParentId:    req.ParentId,
		Page:        req.Page,
		PageSize:    req.PageSize,
		UserId:      authInfo.UserId,
	}

	svcResp, err := svcmgr.ScriptClient().GetCommentList(ctx, svcReq)
	if err != nil {
		errcode.FromError(err).SetTo(resp)
		return resp, nil
	}

	errcode.ErrOK.SetTo(resp, svcResp.Data)
	return resp, nil
}

func (c *Controller) GetCommentReplies(ctx context.Context, req *bizscript.GetCommentRepliesReq) (*bizscript.GetCommentRepliesResp, error) {
	resp := &bizscript.GetCommentRepliesResp{}
	if err := req.Validate(); err != nil {
		errcode.ErrorParam.SetTo(resp)
		return resp, nil
	}

	authInfo := xlaccount.GetAuthInfo(ctx)
	if authInfo == nil {
		errcode.ErrUserNotFound.SetTo(resp)
		return resp, nil
	}

	svcReq := &svcscript.GetCommentRepliesReq{
		ParentId: req.ParentId,
		UserId:   authInfo.UserId,
		Page:     req.Page,
		PageSize: req.PageSize,
	}
	svcResp, err := svcmgr.ScriptClient().GetCommentReplies(ctx, svcReq)
	if err != nil {
		errcode.FromError(err).SetTo(resp)
		return resp, nil
	}

	errcode.ErrOK.SetTo(resp, svcResp.Data)
	return resp, nil
}

// CreateComment 创建评论
func (c *Controller) CreateComment(ctx context.Context, req *bizscript.CreateCommentReq) (*bizscript.CreateCommentResp, error) {
	resp := &bizscript.CreateCommentResp{}
	if err := req.Validate(); err != nil {
		errcode.ErrorParam.SetTo(resp)
		return resp, nil
	}

	authInfo := xlaccount.GetAuthInfo(ctx)
	if authInfo == nil || authInfo.UserId == 0 {
		errcode.ErrUserNotFound.SetTo(resp)
		return resp, nil
	}

	svcReq := &svcscript.CreateCommentReq{
		CommentType:           req.CommentType,
		ParentId:              req.ParentId,
		ScriptId:              req.ScriptId,
		DubbingId:             req.DubbingId,
		CharacterId:           req.CharacterId,
		CharacterAssetId:      req.CharacterAssetId,
		ContentType:           req.ContentType,
		Content:               req.Content,
		VoiceUrl:              req.VoiceUrl,
		VoiceDuration:         req.VoiceDuration,
		UserId:                authInfo.UserId,
		OriginalVoiceDuration: req.OriginalVoiceDuration,
		OriginalVoiceUrl:      req.OriginalVoiceUrl,
		SvcVoiceDuration:      req.SvcVoiceDuration,
		SvcVoiceUrl:           req.SvcVoiceUrl,
	}

	svcResp, err := svcmgr.ScriptClient().CreateComment(ctx, svcReq)
	if err != nil {
		errcode.FromError(err).SetTo(resp)
		return resp, nil
	}

	errcode.ErrOK.SetTo(resp, svcResp.Data)
	return resp, nil
}

// SetCommentTop 设置评论置顶状态
func (c *Controller) SetCommentTop(ctx context.Context, req *bizscript.SetCommentTopReq) (*bizscript.SetCommentTopResp, error) {
	resp := &bizscript.SetCommentTopResp{}
	if err := req.Validate(); err != nil {
		errcode.ErrorParam.SetTo(resp)
		return resp, nil
	}

	authInfo := xlaccount.GetAuthInfo(ctx)
	if authInfo == nil || authInfo.UserId == 0 {
		errcode.ErrUserNotFound.SetTo(resp)
		return resp, nil
	}

	svcReq := &svcscript.SetCommentTopReq{
		CommentId: req.CommentId,
		UserId:    authInfo.UserId,
		IsTop:     req.IsTop,
	}

	_, err := svcmgr.ScriptClient().SetCommentTop(ctx, svcReq)
	if err != nil {
		errcode.FromError(err).SetTo(resp)
		return resp, nil
	}

	errcode.ErrOK.SetTo(resp)
	return resp, nil
}

// DeleteComment 删除评论
func (c *Controller) DeleteComment(ctx context.Context, req *bizscript.DeleteCommentReq) (*bizscript.DeleteCommentResp, error) {
	resp := &bizscript.DeleteCommentResp{}
	if err := req.Validate(); err != nil {
		errcode.ErrorParam.SetTo(resp)
		return resp, nil
	}

	authInfo := xlaccount.GetAuthInfo(ctx)
	if authInfo == nil || authInfo.UserId == 0 {
		errcode.ErrUserNotFound.SetTo(resp)
		return resp, nil
	}

	svcReq := &svcscript.DeleteCommentReq{
		CommentId: req.CommentId,
		UserId:    authInfo.UserId,
	}

	_, err := svcmgr.ScriptClient().DeleteComment(ctx, svcReq)
	if err != nil {
		errcode.FromError(err).SetTo(resp)
		return resp, nil
	}

	errcode.ErrOK.SetTo(resp)
	return resp, nil
}

// GetCommentAsr 获取评论ASR结果
func (c *Controller) GetCommentAsr(ctx context.Context, req *bizscript.GetCommentASRReq) (*bizscript.GetCommentASRResp, error) {
	resp := &bizscript.GetCommentASRResp{}

	if err := req.Validate(); err != nil {
		errcode.ErrorParam.SetTo(resp)
		return resp, nil
	}

	authInfo := xlaccount.GetAuthInfo(ctx)
	if authInfo == nil {
		errcode.ErrUserNotFound.SetTo(resp)
		return resp, nil
	}

	svcReq := &svcscript.GetCommentASRReq{
		CommentId: req.CommentId,
		UserId:    authInfo.UserId,
	}
	svcResp, err := svcmgr.ScriptClient().GetCommentASR(ctx, svcReq)
	if err != nil {
		errcode.FromError(err).SetTo(resp)
		return resp, nil
	}
	errcode.ErrOK.SetTo(resp, svcResp.Data)
	return resp, nil
}

// Like 点赞
func (c *Controller) Like(ctx context.Context, req *bizscript.LikeReq) (*bizscript.LikeResp, error) {
	resp := &bizscript.LikeResp{}
	if err := req.Validate(); err != nil {
		errcode.ErrorParam.SetTo(resp)
		return resp, nil
	}

	authInfo := xlaccount.GetAuthInfo(ctx)
	if authInfo == nil || authInfo.UserId == 0 {
		errcode.ErrUserNotFound.SetTo(resp)
		return resp, nil
	}

	svcReq := &svcscript.LikeReq{
		LikeType: req.LikeType,
		TargetId: req.TargetId,
		UserId:   authInfo.UserId,
	}

	_, err := svcmgr.ScriptClient().Like(ctx, svcReq)
	if err != nil {
		errcode.FromError(err).SetTo(resp)
		return resp, nil
	}

	errcode.ErrOK.SetTo(resp)
	return resp, nil
}

// Unlike 取消点赞
func (c *Controller) Unlike(ctx context.Context, req *bizscript.UnlikeReq) (*bizscript.UnlikeResp, error) {
	resp := &bizscript.UnlikeResp{}
	if err := req.Validate(); err != nil {
		errcode.ErrorParam.SetTo(resp)
		return resp, nil
	}

	authInfo := xlaccount.GetAuthInfo(ctx)
	if authInfo == nil || authInfo.UserId == 0 {
		errcode.ErrUserNotFound.SetTo(resp)
		return resp, nil
	}

	svcReq := &svcscript.UnlikeReq{
		LikeType: req.LikeType,
		TargetId: req.TargetId,
		UserId:   authInfo.UserId,
	}

	_, err := svcmgr.ScriptClient().Unlike(ctx, svcReq)
	if err != nil {
		errcode.FromError(err).SetTo(resp)
		return resp, nil
	}

	errcode.ErrOK.SetTo(resp)
	return resp, nil
}

// GetReportReasons 获取举报原因列表
func (c *Controller) GetReportReasons(ctx context.Context, req *bizscript.GetReportReasonsReq) (*bizscript.GetReportReasonsResp, error) {
	resp := &bizscript.GetReportReasonsResp{}
	if err := req.Validate(); err != nil {
		errcode.ErrorParam.SetTo(resp)
		return resp, nil
	}

	svcReq := &svcscript.GetReportReasonsReq{
		ReportType: req.ReportType,
	}

	svcResp, err := svcmgr.ScriptClient().GetReportReasons(ctx, svcReq)
	if err != nil {
		errcode.FromError(err).SetTo(resp)
		return resp, nil
	}

	errcode.ErrOK.SetTo(resp, svcResp.Data)
	return resp, nil
}

// GetIPList 获取IP列表
func (c *Controller) GetIPList(ctx context.Context, req *bizscript.GetIPListReq) (*bizscript.GetIPListResp, error) {
	resp := &bizscript.GetIPListResp{}
	if err := req.Validate(); err != nil {
		errcode.ErrorParam.SetTo(resp)
		return resp, nil
	}

	svcReq := &svcscript.GetIPListReq{
		Page:     req.Page,
		PageSize: req.PageSize,
	}

	svcResp, err := svcmgr.ScriptClient().GetIPList(ctx, svcReq)
	if err != nil {
		errcode.FromError(err).SetTo(resp)
		return resp, nil
	}

	// 设置响应数据
	errcode.ErrOK.SetTo(resp, svcResp.Data)
	return resp, nil
}

// Report 举报内容
func (c *Controller) Report(ctx context.Context, req *bizscript.ReportReq) (*bizscript.ReportResp, error) {
	resp := &bizscript.ReportResp{}
	if err := req.Validate(); err != nil {
		errcode.ErrorParam.SetTo(resp)
		return resp, nil
	}

	authInfo := xlaccount.GetAuthInfo(ctx)
	if authInfo == nil || authInfo.UserId == 0 {
		errcode.ErrUserNotFound.SetTo(resp)
		return resp, nil
	}

	svcReq := &svcscript.ReportReq{
		ReportType: req.ReportType,
		TargetId:   req.TargetId,
		ReasonId:   req.ReasonId,
		UserId:     authInfo.UserId,
	}

	_, err := svcmgr.ScriptClient().Report(ctx, svcReq)
	if err != nil {
		errcode.FromError(err).SetTo(resp)
		return resp, nil
	}

	errcode.ErrOK.SetTo(resp)
	return resp, nil
}

// GetCoverList 获取封面列表
func (c *Controller) GetCoverList(ctx context.Context, req *bizscript.GetCoverListReq) (*bizscript.GetCoverListResp, error) {
	resp := &bizscript.GetCoverListResp{}
	if err := req.Validate(); err != nil {
		errcode.ErrorParam.SetTo(resp)
		return resp, nil
	}

	svcReq := &svcscript.GetCoverListReq{
		Page:     req.Page,
		PageSize: req.PageSize,
		Type:     req.Type,
	}

	svcResp, err := svcmgr.ScriptClient().GetCoverList(ctx, svcReq)
	if err != nil {
		errcode.FromError(err).SetTo(resp)
		return resp, nil
	}

	errcode.ErrOK.SetTo(resp, svcResp.Data)
	return resp, nil
}

// GetUserScriptLists 获取用户剧本列表
func (c *Controller) GetUserScriptLists(ctx context.Context, req *bizscript.GetUserScriptListsReq) (*bizscript.GetUserScriptListsResp, error) {
	resp := &bizscript.GetUserScriptListsResp{}
	if err := req.Validate(); err != nil {
		errcode.ErrorParam.SetTo(resp)
		return resp, nil
	}

	authInfo := xlaccount.GetAuthInfo(ctx)
	if authInfo == nil {
		errcode.ErrUserNotFound.SetTo(resp)
		return resp, nil
	}

	userId := authInfo.UserId
	if req.UserId > 0 {
		userId = req.UserId
	}

	svcReq := &svcscript.GetUserScriptListsReq{
		UserId:   userId,
		ListType: req.ListType,
		Page:     req.Page,
		PageSize: req.PageSize,
	}

	svcResp, err := svcmgr.ScriptClient().GetUserScriptLists(ctx, svcReq)
	if err != nil {
		errcode.FromError(err).SetTo(resp)
		return resp, nil
	}

	errcode.ErrOK.SetTo(resp, svcResp.Data)
	return resp, nil
}

// GetUserStats 获取用户统计数据
func (c *Controller) GetUserStats(ctx context.Context, req *bizscript.GetUserStatsReq) (*bizscript.GetUserStatsResp, error) {
	resp := &bizscript.GetUserStatsResp{}

	authInfo := xlaccount.GetAuthInfo(ctx)
	if authInfo == nil {
		errcode.ErrUserNotFound.SetTo(resp)
		return resp, nil
	}

	userId := authInfo.UserId
	if req.UserId > 0 {
		userId = req.UserId
	}

	svcReq := &svcscript.GetUserStatsReq{
		UserId: userId,
	}

	svcResp, err := svcmgr.ScriptClient().GetUserStats(ctx, svcReq)
	if err != nil {
		errcode.FromError(err).SetTo(resp)
		return resp, nil
	}

	errcode.ErrOK.SetTo(resp, svcResp.Data)
	return resp, nil
}

// GenerateScriptLines 生成剧本台词
func (c *Controller) GenerateScriptLines(ctx context.Context, req *bizscript.GenerateScriptLinesReq) (*bizscript.GenerateScriptLinesResp, error) {
	resp := &bizscript.GenerateScriptLinesResp{}
	if err := req.Validate(); err != nil {
		errcode.ErrorParam.SetTo(resp)
		return resp, nil
	}

	svcReq := &svcscript.GenerateScriptLinesReq{
		CharacterIds: req.CharacterIds,
	}

	svcResp, err := svcmgr.ScriptClient().GenerateScriptLines(ctx, svcReq)
	if err != nil {
		errcode.FromError(err).SetTo(resp)
		return resp, nil
	}

	respData := &bizscript.GenerateScriptLinesRespData{
		Lines:      svcResp.Data.Lines,
		Characters: svcResp.Data.Characters,
	}

	errcode.ErrOK.SetTo(resp, respData)
	return resp, nil
}

// 批量获取点赞数
func (c *Controller) BatchGetLikes(ctx context.Context, req *bizscript.BatchGetLikesReq) (*bizscript.BatchGetLikesResp, error) {
	resp := &bizscript.BatchGetLikesResp{}
	if err := req.Validate(); err != nil {
		errcode.ErrorParam.SetTo(resp)
		return resp, nil
	}

	authInfo := xlaccount.GetAuthInfo(ctx)
	if authInfo == nil {
		errcode.ErrUserNotFound.SetTo(resp)
		return resp, nil
	}

	svcReq := &svcscript.BatchGetLikesReq{
		UserId:    authInfo.UserId,
		LikeType:  req.LikeType,
		TargetIds: req.TargetIds,
	}

	svcResp, err := svcmgr.ScriptClient().BatchGetLikes(ctx, svcReq)
	if err != nil {
		errcode.FromError(err).SetTo(resp)
		return resp, nil
	}

	errcode.ErrOK.SetTo(resp, svcResp.Data)
	return resp, nil
}

// DeleteDubbing 删除配音
func (c *Controller) DeleteDubbing(ctx context.Context, req *bizscript.DeleteDubbingReq) (*bizscript.DeleteDubbingResp, error) {
	resp := &bizscript.DeleteDubbingResp{}
	if err := req.Validate(); err != nil {
		errcode.ErrorParam.SetTo(resp)
		return resp, nil
	}

	authInfo := xlaccount.GetAuthInfo(ctx)
	if authInfo == nil || authInfo.UserId == 0 {
		errcode.ErrUserNotFound.SetTo(resp)
		return resp, nil
	}

	svcReq := &svcscript.DeleteDubbingReq{
		DubbingId: req.DubbingId,
		UserId:    authInfo.UserId,
	}

	_, err := svcmgr.ScriptClient().DeleteDubbing(ctx, svcReq)
	if err != nil {
		errcode.FromError(err).SetTo(resp)
		return resp, nil
	}

	errcode.ErrOK.SetTo(resp)
	return resp, nil
}

// SetDubbingTop 设置配音置顶状态
func (c *Controller) SetDubbingTop(ctx context.Context, req *bizscript.SetDubbingTopReq) (*bizscript.SetDubbingTopResp, error) {
	resp := &bizscript.SetDubbingTopResp{}
	if err := req.Validate(); err != nil {
		errcode.ErrorParam.SetTo(resp)
		return resp, nil
	}

	authInfo := xlaccount.GetAuthInfo(ctx)
	if authInfo == nil || authInfo.UserId == 0 {
		errcode.ErrUserNotFound.SetTo(resp)
		return resp, nil
	}

	svcReq := &svcscript.SetDubbingTopReq{
		DubbingId: req.DubbingId,
		UserId:    authInfo.UserId,
		IsTop:     req.IsTop,
	}

	_, err := svcmgr.ScriptClient().SetDubbingTop(ctx, svcReq)
	if err != nil {
		errcode.FromError(err).SetTo(resp)
		return resp, nil
	}

	errcode.ErrOK.SetTo(resp)
	return resp, nil
}
