package controller

import (
	"context"
	"encoding/json"

	"new-gitlab.xunlei.cn/vcproject/backends/baselib/errcode"
	"new-gitlab.xunlei.cn/vcproject/backends/baselib/logger"
	"new-gitlab.xunlei.cn/vcproject/backends/baselib/server/interceptor/bizcontext"
	"new-gitlab.xunlei.cn/vcproject/backends/baselib/util"
	"new-gitlab.xunlei.cn/vcproject/backends/baselib/xlaccount"
	"new-gitlab.xunlei.cn/vcproject/backends/proto/api/bizstat"
	"new-gitlab.xunlei.cn/vcproject/backends/proto/api/svcstat"
	"new-gitlab.xunlei.cn/vcproject/backends/proto/consts"
	"new-gitlab.xunlei.cn/vcproject/backends/proto/report"
	"new-gitlab.xunlei.cn/vcproject/backends/proto/svcmgr"
)

var (
	_ bizstat.SServer = &Controller{}
)

type Controller struct {
}

func NewController() *Controller {
	svcmgr.InitServices()
	return &Controller{}
}

func (c *Controller) PushEvent(ctx context.Context, req *svcstat.EventPushReq) (resp *bizstat.EventPushResp, err error) {
	resp = &bizstat.EventPushResp{}
	errcode.ErrOK.SetTo(resp)
	if err = req.ValidateAll(); err != nil {
		errcode.ErrorParam.SetTo(resp)
		return resp, nil
	}
	authInfo := xlaccount.GetAuthInfo(ctx)
	baseParam := bizcontext.GetBaseContext(ctx)
	userid := authInfo.UserId
	dt := baseParam.GetDt()
	appVersion := baseParam.GetAv()
	packageName := baseParam.GetApp()
	reprotEvents := make([]*svcstat.EventItem, 0, len(req.List))
	retIDs := make([]string, 0, len(req.List))
	for _, eventItem := range req.List {
		if (eventItem.EventType == report.XlDataEventTypeTrack || len(eventItem.EventType) == 0) && len(eventItem.EventName) == 0 {
			continue
		}
		extData := make(map[string]interface{}, 0)
		if len(eventItem.ExtData) > 0 {
			tmpErr := json.Unmarshal([]byte(eventItem.ExtData), &extData)
			if tmpErr != nil {
				logger.Errorf("PushEvent err=%+v", tmpErr)
				continue
			}
		}
		if dt == int32(consts.BaseDevTypeAndroid) {
			extData["@platform"] = consts.BaseDevTypeAndroid.String()
		} else if dt == int32(consts.BaseDevTypeIos) {
			extData["@platform"] = consts.BaseDevTypeIos.String()
		} else {
			extData["@platform"] = consts.BaseDevTypeH5.String()
		}
		extData["channel"] = baseParam.GetCh()
		extData["dt"] = dt
		extData["package_name"] = packageName
		extData["app_version"] = appVersion
		// 兼容字段
		extData["app"] = packageName
		extData["av"] = appVersion
		extData["ch"] = baseParam.GetCh()
		extData["did"] = baseParam.GetDid()
		extData["bd"] = baseParam.GetBd()
		extData["os"] = baseParam.GetOs()
		ip := authInfo.Ip
		if len(ip) == 0 {
			ip = baseParam.GetIp()
		}
		reportEvent := &svcstat.EventItem{
			UserId:     userid,
			DistinctId: baseParam.GetDid(),
			EventName:  eventItem.EventName,
			EventType:  eventItem.EventType,
			ClientIp:   ip,
			ReportTime: eventItem.ReportTime,
			ExtData:    util.JsonStr(extData),
		}
		reprotEvents = append(reprotEvents, reportEvent)
		retIDs = append(retIDs, eventItem.Id)
	}
	svcReq := &svcstat.EventPushReq{List: reprotEvents}
	_, tmpErr := svcmgr.StatClient().EventPush(ctx, svcReq)
	if tmpErr != nil {
		logger.Errorf("PushEvent err=%+v", tmpErr)
		errcode.ErrorInternal.SetTo(resp)
		return
	}
	resp = &bizstat.EventPushResp{
		Data: &bizstat.EventPushRespData{
			List: retIDs,
		},
	}
	return
}
