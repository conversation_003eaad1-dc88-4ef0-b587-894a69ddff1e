package grpccli

import (
	"context"
	"fmt"

	"google.golang.org/grpc"
	"google.golang.org/protobuf/encoding/protojson"
	"google.golang.org/protobuf/proto"
	"new-gitlab.xunlei.cn/vcproject/backends/baselib/httpcli"
	"new-gitlab.xunlei.cn/vcproject/backends/baselib/logger"
)

func devToHttp(ctx context.Context, method string, req, reply interface{}, cc *grpc.ClientConn, invoker grpc.UnaryInvoker, opts ...grpc.CallOption) error {
	var grpcToHttpMap = map[string]string{
		"/vc.svcscript.s/AdminGetScriptList": "/admin/script/list",
		// 其他映射...
	}

	httpPath, ok := grpcToHttpMap[method]
	if !ok {
		return fmt.Errorf("no HTTP mapping found for method: %s", method)
	}

	reqData, err := protojson.Marshal(req.(proto.Message))
	if err != nil {
		logger.Errorf("marshal error: %v", err)
		return err
	}

	logger.Debugf("POST https://api-test.voicelives.com%s", httpPath)
	resp, err := httpcli.PostJsonRawData("https://api-test.voicelives.com"+httpPath, nil, reqData)
	if err != nil {
		logger.Errorf("http error: %v", err)
		return err
	}

	err = protojson.Unmarshal([]byte(resp), reply.(proto.Message))
	if err != nil {
		logger.Errorf("unmarshal error: %v", err)
		return err
	}
	return nil
}
