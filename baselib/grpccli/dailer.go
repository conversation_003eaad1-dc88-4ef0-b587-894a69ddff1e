package grpccli

import (
	"context"
	"net"

	"new-gitlab.xunlei.cn/vcproject/backends/baselib/logger"
)

func HostsDialer(hosts map[string]string) func(context.Context, string) (net.Conn, error) {
	return func(ctx context.Context, target string) (net.Conn, error) {
		var addr = target
		if hostAddr, ok := hosts[target]; ok {
			addr = hostAddr
			logger.Infof("Dailer use addr %s for addr %s", addr, addr)
		} else {
			logger.Infof("dailer use default addr for addr %s", addr)
		}
		conn, err := net.Dial("tcp", addr)
		if err != nil {
			logger.Warnf("error dail %s for addr %s error %v", addr, target, err)
		}
		return conn, err
	}
}
