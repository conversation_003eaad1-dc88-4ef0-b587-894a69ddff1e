package grpccli

import (
	"context"
	"new-gitlab.xunlei.cn/vcproject/backends/baselib/logger"
	"new-gitlab.xunlei.cn/vcproject/backends/baselib/server/interceptor"
	"strconv"
	"strings"

	gpm "github.com/grpc-ecosystem/go-grpc-middleware"
	"google.golang.org/grpc"
	"new-gitlab.xunlei.cn/vcproject/backends/baselib/metric"
	"new-gitlab.xunlei.cn/vcproject/backends/baselib/server/env"
	"new-gitlab.xunlei.cn/vcproject/backends/baselib/tracer"
	"new-gitlab.xunlei.cn/vcproject/backends/baselib/util"
	"new-gitlab.xunlei.cn/vcproject/backends/proto/api/common"
)

var (
	_ = gpm.ChainUnaryClient
)

func NewLocalConn() (*grpc.ClientConn, error) {
	var devClientInterceptor grpc.UnaryClientInterceptor
	if env.IsLocal() {
		logger.Infof("env is local")
		devClientInterceptor = devToHttp
	}
	return grpc.Dial(
		"127.0.0.1:9002",
		grpc.WithInsecure(),
		grpc.WithContextDialer(HostsDialer(nil)),
		grpc.WithUnaryInterceptor(
			gpm.ChainUnaryClient(
				util.FilterEmptyUnaryClientInterceptor(
					metric.UnaryClientInterceptor(),
					callLog,
					ClientErrorCodeMetric,
					devClientInterceptor,
					interceptor.BizAuthInfoInterceptor,
				)...,
			)),
		tracer.WithGRPCClientStatsHandler(),
	)
}

func ClientErrorCodeMetric(ctx context.Context, method string, req, reply interface{}, cc *grpc.ClientConn, invoker grpc.UnaryInvoker, opts ...grpc.CallOption) error {
	err := invoker(ctx, method, req, reply, cc, opts...)
	var errcode int32 = -1
	svcResp, ok := reply.(interface {
		GetBase() *common.SvcBaseResp
	})
	if ok && svcResp != nil && svcResp.GetBase() != nil {
		errcode = svcResp.GetBase().GetCode()
	}
	var errorMsg = "0"
	if err != nil {
		errorMsg = "-1"
	}
	sp := strings.Split(method, "/")
	metric.CounterWithLabels("grpc_svc_client_errcode", map[string]string{
		"grpc_service": sp[1],
		"grpc_method":  method,
		"errcode":      strconv.FormatInt(int64(errcode), 10),
		"error_msg":    errorMsg,
	}).Inc()

	return err
}

func NewRemoteConn(serverName string) (*grpc.ClientConn, error) {
	return grpc.Dial(
		serverName,
		grpc.WithInsecure(),
		grpc.WithContextDialer(HostsDialer(nil)),
		grpc.WithUnaryInterceptor(
			gpm.ChainUnaryClient(
				util.FilterEmptyUnaryClientInterceptor(
					metric.UnaryClientInterceptor(),
					callLog,
				)...,
			)),
		//grpc.WithStreamInterceptor(),
	)
}
