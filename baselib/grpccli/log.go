package grpccli

import (
	"context"
	"strconv"
	"time"

	"github.com/pborman/uuid"
	"google.golang.org/grpc"
	"google.golang.org/grpc/metadata"
	"new-gitlab.xunlei.cn/vcproject/backends/baselib/logger"
	"new-gitlab.xunlei.cn/vcproject/backends/baselib/server/env"
	"new-gitlab.xunlei.cn/vcproject/backends/baselib/server/interceptor/bizcontext"
	"new-gitlab.xunlei.cn/vcproject/backends/baselib/server/usermd"
	"new-gitlab.xunlei.cn/vcproject/backends/baselib/tracer"
	"new-gitlab.xunlei.cn/vcproject/backends/baselib/util"
)

var (
	noCallLogMap = map[string]bool{
		"/vc.svcaccount.s/AccountCheck":   true,
		"/vc.svcaccount.s/AccountInfo":    true,
		"/vc.svcaccount.s/CheckUserBlock": true,
		"/vc.svcaccount.s/RegisterInfo":   true,
		"/vc.svcaccount.s/CoinLogs":       true,
		"/vc.svcaccount.s/ScoreLogs":      true,
	}

	closeClientLog bool
)

func init() {
	if !env.IsProd() {
		noCallLogMap = map[string]bool{}
	}
}

func SetCloseClientLog() {
	closeClientLog = true
}

func callLog(ctx context.Context, method string, req, reply interface{}, cc *grpc.ClientConn, invoker grpc.UnaryInvoker, opts ...grpc.CallOption) error {
	span := tracer.SpanFromContext(ctx)
	if !tracer.IsSpanValid(span) {
		ctx, span = tracer.NewSpan(ctx, "ClientLog")
		defer span.End()
	}
	traceId := tracer.GetTraceID(span)
	if !span.SpanContext().HasTraceID() && traceId == "" {
		md, ok := metadata.FromIncomingContext(ctx)
		if ok {
			ms, ok := md[logger.VcTraceKey]
			if ok && len(ms) > 0 {
				traceId = ms[0]
			}
		}
		if traceId == "" {
			traceId = uuid.NewUUID().String()
		}
	}
	mid := usermd.GetUserIdFromContext(ctx)
	//lang := usermd.GetLangFromContext(ctx)
	//countryCode := usermd.GetCountryCodeFromContext(ctx)
	//region := usermd.GetRegionFromContext(ctx)
	baseStr := bizcontext.GetBaseContextStr(ctx)
	ctx = metadata.NewOutgoingContext(ctx, metadata.New(map[string]string{
		logger.VcTraceKey:         traceId,
		usermd.GrpcUserContextKey: strconv.FormatInt(mid, 10),
		//usermd.LangContextKey:     lang,
		//usermd.UserRegionContextKey:  strconv.FormatInt(int64(region), 10),
		//usermd.UserCountryContextKey: countryCode,
		bizcontext.GetBaseContextKey(): baseStr,
	}))
	st := time.Now()
	err := invoker(ctx, method, req, reply, cc, opts...)
	if !closeClientLog && (!env.IsProd() || !noCallLogMap[method]) {
		logger.Infof("rpc call %s fromUserid %d timeCost %d ms error %v req %s resp %s", method, mid, time.Since(st).Milliseconds(), err,
			util.JsonStr(req), util.JsonStr(reply))
	}
	return err
}

func CallLog(ctx context.Context, method string, req, reply interface{}, cc *grpc.ClientConn, invoker grpc.UnaryInvoker, opts ...grpc.CallOption) error {
	return callLog(ctx, method, req, reply, cc, invoker, opts...)
}
