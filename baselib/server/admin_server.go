package server

import (
	"runtime"

	gpm "github.com/grpc-ecosystem/go-grpc-middleware"
	"google.golang.org/grpc"
	"new-gitlab.xunlei.cn/vcproject/backends/baselib/config"
	"new-gitlab.xunlei.cn/vcproject/backends/baselib/errcode"
	"new-gitlab.xunlei.cn/vcproject/backends/baselib/httpcli"
	"new-gitlab.xunlei.cn/vcproject/backends/baselib/metric"
	"new-gitlab.xunlei.cn/vcproject/backends/baselib/server/env"
	"new-gitlab.xunlei.cn/vcproject/backends/baselib/server/interceptor"
	"new-gitlab.xunlei.cn/vcproject/backends/baselib/util"
	"new-gitlab.xunlei.cn/vcproject/backends/proto/api/common"
)

func NewAdminServer(conf *config.ServerConfig) *Server {
	runtime.GOMAXPROCS(runtime.NumCPU())
	server := &Server{
		config:     conf,
		grpcServer: nil,
	}

	// 创建gRPC服务
	svr := grpc.NewServer(
		grpc.ChainUnaryInterceptor(
			// 注册链路跟踪和监控
			gpm.ChainUnaryServer(
				util.FilterEmptyUnaryServerInterceptor(
					interceptor.RecoverWithRespFunc(func(stack string) interface{} {
						msg := "内部错误"
						if !env.IsProd() {
							msg = stack
						}
						return &common.BizBaseResp{
							Code: errcode.ErrorInternal.Code,
							Msg:  msg,
						}
					}),
					interceptor.BizServerLog,
					//jaeger.ServerInterceptor(),
					metric.UnaryServerInterceptor(),
					//interceptor.AdminAuthInterceptor,
				)...,
			),
		),
		//grpc.StreamInterceptor(ctx.GRPCStreamServerInterceptor()),
	)
	server.grpcServer = svr
	server.initResources()
	//goi18n.Init(config.TranslateDir, &language.English, nil)
	httpcli.Init()
	return server
}
