package usermd

import (
	"context"
	"strconv"

	"google.golang.org/grpc/metadata"
)

const (
	UserContextKey     = "user_key"
	GrpcUserContextKey = "from_user"
	//LangContextKey        = "lang"
	//UserRegionContextKey  = "fumi-region"
	//UserCountryContextKey = "country_code"
)

type UserContext struct {
	UserId int64
}

func SetUserContext(ctx context.Context, userContext *UserContext) context.Context {
	return context.WithValue(ctx, UserContextKey, userContext)
}

func GetUserContext(ctx context.Context) *UserContext {
	if u, ok := ctx.Value(UserContextKey).(*UserContext); ok && u != nil {
		return u
	}
	return nil
}

func GetUserIdFromContext(ctx context.Context) int64 {
	if u, ok := ctx.Value(UserContextKey).(*UserContext); ok && u != nil {
		return u.UserId
	}
	md, ok := metadata.FromIncomingContext(ctx)
	if ok {
		s, ok := md[GrpcUserContextKey]
		if ok && len(s) > 0 {
			userid, _ := strconv.ParseInt(s[0], 10, 64)
			return userid
		}
	}
	return 0
}

// func GetRegionFromContext(ctx context.Context) int32 {
// 	if u, ok := ctx.Value(UserContextKey).(*UserContext); ok && u != nil && u.Region != 0 {
// 		return u.Region
// 	}
// 	md, ok := metadata.FromIncomingContext(ctx)
// 	if ok {
// 		l, ok := md[UserRegionContextKey]
// 		if ok && len(l) > 0 && l[0] != "" {
// 			regionId, _ := strconv.ParseInt(l[0], 10, 64)
// 			return int32(regionId) // regionId
// 		}
// 	}
// 	return 0
// }

// func GetCountryCodeFromContext(ctx context.Context) (lang string) {
// 	if u, ok := ctx.Value(UserContextKey).(*UserContext); ok && u != nil && u.CountryCode != "" {
// 		return u.CountryCode
// 	}
// 	md, ok := metadata.FromIncomingContext(ctx)
// 	if ok {
// 		c, _ok := md[UserCountryContextKey]
// 		if _ok && len(c) > 0 && c[0] != "" {
// 			return c[0]
// 		}
// 	}
// 	return
// }
