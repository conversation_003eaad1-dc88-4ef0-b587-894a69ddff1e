package server

import (
	"net"
	"runtime"
	"time"

	"new-gitlab.xunlei.cn/vcproject/backends/baselib/alioss"
	"new-gitlab.xunlei.cn/vcproject/backends/baselib/config"
	"new-gitlab.xunlei.cn/vcproject/backends/baselib/logger"
	"new-gitlab.xunlei.cn/vcproject/backends/baselib/server/env"
	"new-gitlab.xunlei.cn/vcproject/backends/baselib/server/interceptor"
	"new-gitlab.xunlei.cn/vcproject/backends/baselib/tracer"

	gpm "github.com/grpc-ecosystem/go-grpc-middleware"
	"google.golang.org/grpc"
	"new-gitlab.xunlei.cn/vcproject/backends/baselib/cache"
	"new-gitlab.xunlei.cn/vcproject/backends/baselib/database"
	mongodb "new-gitlab.xunlei.cn/vcproject/backends/baselib/database/mongo"
	"new-gitlab.xunlei.cn/vcproject/backends/baselib/errcode"
	"new-gitlab.xunlei.cn/vcproject/backends/baselib/httpcli"
	"new-gitlab.xunlei.cn/vcproject/backends/baselib/metric"
	"new-gitlab.xunlei.cn/vcproject/backends/baselib/util"
	"new-gitlab.xunlei.cn/vcproject/backends/proto/api/common"
)

type Server struct {
	config     *config.ServerConfig
	grpcServer *grpc.Server
}

func NewBizServer(conf *config.ServerConfig) *Server {
	runtime.GOMAXPROCS(runtime.NumCPU())
	server := &Server{
		config:     conf,
		grpcServer: nil,
	}

	// 创建gRPC服务
	svr := grpc.NewServer(
		grpc.ChainUnaryInterceptor(
			// 注册链路跟踪和监控
			gpm.ChainUnaryServer(
				util.FilterEmptyUnaryServerInterceptor(
					interceptor.HeaderToContextInterceptor(),
					interceptor.RecoverWithRespFunc(func(stack string) interface{} {
						msg := "internal server error"
						if !env.IsProd() {
							msg = stack
						}
						return &common.BizBaseResp{
							Code: errcode.ErrorInternal.Code,
							Msg:  msg,
						}
					}),
					//bizcontext.WriteSvcContext,
					interceptor.BizServerLog,
					//jaeger.ServerInterceptor(),
					metric.UnaryServerInterceptor(),
					interceptor.BizErrcodeMetric,
					interceptor.AuthInterceptor,
					//interceptor.ServiceMaintenanceInterceptor,
					//goi18n.TranslateRespondInterceptor,
				)...,
			),
		),
		tracer.GRPCServerStatsHandler(),
	)
	server.grpcServer = svr
	server.initResources()
	//interceptor.Init(conf.Interceptor)
	//goi18n.Init(config.TranslateDir, &language.English, nil)

	httpcli.Init()
	return server
}

func NewSvcServer(conf *config.ServerConfig) *Server {
	runtime.GOMAXPROCS(runtime.NumCPU())
	server := &Server{
		config:     conf,
		grpcServer: nil,
	}

	// 创建gRPC服务
	svr := grpc.NewServer(
		grpc.ChainUnaryInterceptor(
			// 注册链路跟踪和监控
			gpm.ChainUnaryServer(
				util.FilterEmptyUnaryServerInterceptor(
					interceptor.SvcAuthInfoInterceptor,
					interceptor.RecoverWithRespFunc(func(stack string) interface{} {
						msg := "internal server error"
						if !env.IsProd() {
							msg = stack
						}
						return &common.SvcCommonResp{
							Base: &common.SvcBaseResp{
								Code: errcode.ErrorInternal.Code,
								Msg:  msg,
							},
						}
					}),
					interceptor.SvcServerLog,
					//jaeger.ServerInterceptor(),
					metric.UnaryServerInterceptor(),
					interceptor.SvcErrcodeMetric,
					//goi18n.TranslateRespondInterceptor,
				)...,
			),
		),
		tracer.GRPCServerStatsHandler(),
	)
	server.grpcServer = svr
	server.initResources()
	//goi18n.Init(config.TranslateDir, &language.English, nil)
	//interceptor.Init(conf.Interceptor)
	httpcli.Init()
	return server
}

// func NewBizMsgServer(conf *config.ServerConfig) *Server {
// 	runtime.GOMAXPROCS(runtime.NumCPU())
// 	server := &Server{
// 		config:     conf,
// 		grpcServer: nil,
// 	}

// 	// 创建gRPC服务
// 	svr := grpc.NewServer(
// 		grpc.ChainUnaryInterceptor(
// 			// 注册链路跟踪和监控
// 			gpm.ChainUnaryServer(
// 				util.FilterEmptyUnaryServerInterceptor(
// 					interceptor.RecoverWithRespFunc(func(stack string) interface{} {
// 						msg := "internal server error"
// 						if !env.IsProd() {
// 							msg = stack
// 						}
// 						return &bizmsgchan.HandleMsgRsp{
// 							Result:  1,
// 							Msg: msg,
// 						}
// 					}),
// 					interceptor.RecoverWithResp(&common.SvcCommonResp{
// 						Base: errcode.ErrorInternal.ToSvcBaseRespMsg("error panic"),
// 					}),
// 					interceptor.BizServerLog,
// 					//jaeger.ServerInterceptor(),
// 					metric.UnaryServerInterceptor(),
// 					BizMsgChanErrorCode,
// 				)...,
// 			),
// 		),
// 		//grpc.StreamInterceptor(ctx.GRPCStreamServerInterceptor()),
// 	)
// 	server.grpcServer = svr
// 	server.initResources()
// 	//interceptor.Init(conf.Interceptor)
// 	httpcli.Init()
// 	return server
// }

// func BizMsgChanErrorCode(ctx context.Context, req interface{}, info *grpc.UnaryServerInfo, handler grpc.UnaryHandler) (interface{}, error) {
// 	resp, err := handler(ctx, req)
// 	var errcode int32 = -1
// 	svcResp, ok := resp.(interface {
// 		GetResult() int32
// 		GetMessage() string
// 	})
// 	if ok && svcResp != nil {
// 		errcode = svcResp.GetResult()
// 	}
// 	sp := strings.Split(info.FullMethod, "/")
// 	metric.CounterWithLabels("grpc_svc_server_errcode", map[string]string{
// 		"grpc_service": sp[1],
// 		"grpc_method":  info.FullMethod,
// 		"errcode":      strconv.FormatInt(int64(errcode), 10),
// 	}).Inc()

// 	return resp, err
// }

// 注册gRPC服务实现
func (server *Server) RegisterServices(f func(s *grpc.Server)) {
	f(server.grpcServer)
}

// 启动服务
func (server *Server) Start() {
	addr := server.config.Addr
	ls, err := net.Listen("tcp", addr)
	if err != nil {
		logger.Errorf("Listen error: %v\n", err)
		return
	}
	logger.Infof("Started Application at %v, listen on %v", time.Now().Format("January 2, 2006 at 3:04pm (MST)"), addr)
	server.grpcServer.Serve(ls)
}

func (server *Server) GracefulStop() {
	if server.grpcServer != nil {
		server.grpcServer.GracefulStop()
	}
}

func (server *Server) initResources() {
	logger.InitLoggerWitchLevel(server.config.Logger, server.config.LogLevel)
	// i18n路径固定，不可修改,通过configMap挂载
	//jaegerConf := server.config.Jaeger
	//jaeger.InitJaeger(jaegerConf.ServiceName, jaegerConf.Host, jaegerConf.SamplerParam)
	metric.InitPrometheus(server.grpcServer, server.config.Prometheus.Addr)
	database.InitMysql(server.config.Mysql)
	database.InitSlaveMysql(server.config.SlaveMysql)
	//database.InitClickhouse(server.config.Clickhouse)
	mongodb.InitMongoDB(server.config.Mongodb)
	cache.InitRedis(server.config.Redis)
	alioss.InitAliOss(server.config.AliOss)
	config.InitAsrCofig(server.config.Asr)
	//awss3.InitS3(server.config)
}
