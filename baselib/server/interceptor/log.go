package interceptor

import (
	"context"
	"fmt"
	"reflect"
	"strconv"
	"time"

	"github.com/pborman/uuid"
	"google.golang.org/grpc"
	"google.golang.org/grpc/codes"
	"google.golang.org/grpc/metadata"
	"google.golang.org/grpc/status"
	"google.golang.org/protobuf/encoding/protojson"
	"google.golang.org/protobuf/proto"
	"new-gitlab.xunlei.cn/vcproject/backends/baselib/errcode"
	"new-gitlab.xunlei.cn/vcproject/backends/baselib/logger"
	"new-gitlab.xunlei.cn/vcproject/backends/baselib/server/env"
	"new-gitlab.xunlei.cn/vcproject/backends/baselib/server/interceptor/bizcontext"
	"new-gitlab.xunlei.cn/vcproject/backends/baselib/server/usermd"
	"new-gitlab.xunlei.cn/vcproject/backends/baselib/tracer"
	"new-gitlab.xunlei.cn/vcproject/backends/proto/api/common"
)

var (
	protoJsonMarshalOption = protojson.MarshalOptions{
		Multiline:       false,
		Indent:          "",
		AllowPartial:    false,
		UseProtoNames:   true,
		UseEnumNumbers:  false,
		EmitUnpopulated: false,
		Resolver:        nil,
	}
	closeClientLog bool
)

func SetCloseServerLog() {
	closeClientLog = true
}

var (
	noCallLogMap = map[string]bool{}
)

func BizServerLog(ctx context.Context, req interface{}, info *grpc.UnaryServerInfo, handler grpc.UnaryHandler) (interface{}, error) {
	span := tracer.SpanFromContext(ctx)
	if !tracer.IsSpanValid(span) {
		ctx, span = tracer.NewSpan(ctx, "BizServerLog")
		defer span.End()
	}
	traceId := tracer.GetTraceID(span)
	goId := logger.GoID()
	logger.Infof("span=%+v tranceId=%s goId=%+v", span, traceId, goId)
	if span.SpanContext().HasTraceID() && traceId != "" {
		traceId2 := logger.GetTrace(goId)
		logger.Infof("span=%+v tranceId=%s goId=%+v traceId2=%s", span, traceId, goId, traceId2)
		if traceId2 != traceId {
			logger.SetTrace(goId, traceId)
		}
	} else {
		md, ok := metadata.FromIncomingContext(ctx)
		if ok {
			ms, ok := md[logger.VcTraceKey]
			if ok && len(ms) > 0 {
				traceId = ms[0]
			}
			newTraceId := ""
			transparent, ok := md[logger.VcTraceKey]
			if ok && len(transparent) > 0 {
				newTraceId = transparent[0]
			}
			logger.Infof("newTranceId=%s", newTraceId)
		}
		if traceId == "" {
			traceId = uuid.NewUUID().String()
		}
		logger.Infof("span=%+v tranceId=%s goId=%+v traceId=%s", span, traceId, goId, traceId)
		logger.SetTrace(goId, traceId)
	}
	var (
		ip          string
		did         string
		packageName string
	)
	baseParam := bizcontext.GetBaseContext(ctx)
	if baseParam != nil {
		did = baseParam.Did
		packageName = baseParam.App
	}
	st := time.Now()
	reqData, _ := protoJsonMarshalOption.Marshal(req.(proto.Message))
	tracer.AddSpanEvents(span, "message", map[string]string{
		"req": string(reqData),
	})

	resp, err := handler(ctx, req)
	timeCost := time.Since(st)
	if err != nil && timeCost.Milliseconds() < 3000 { // 返回小于2s 不算超时算 算用户取消,其它得还是算超时，业务目前报内容错误
		s, ok := status.FromError(err)
		if ok && s.Code() == codes.Canceled {
			if resp == nil || reflect.ValueOf(resp).IsNil() {
				resp = errcode.ErrorCanceled
			}
			bizResp, ok := resp.(interface {
				GetCode() int32
				GetMsg() string
			})
			if ok && bizResp != nil {
				val := reflect.ValueOf(resp).Elem()
				v1 := val.FieldByName("Code")
				if v1.CanSet() {
					v1.SetInt(int64(errcode.ErrorCanceled.Code))
				}
				v2 := val.FieldByName("Msg")
				if v2.CanSet() {
					v2.SetString(errcode.ErrorCanceled.Msg)
				}
			}
		}
	}

	respData, _ := protoJsonMarshalOption.Marshal(resp.(proto.Message))
	tracer.AddSpanEvents(span, "message", map[string]string{
		"resp": string(respData),
	})
	if md, ok := metadata.FromIncomingContext(ctx); ok {
		ip = getClientIP(md)
	}
	userid := usermd.GetUserIdFromContext(ctx)
	tracer.AddSpanTags(span, map[string]string{
		"userid": fmt.Sprintf("%d", userid),
		"ip":     ip,
		"packge": packageName,
		"did":    did,
	})

	if !closeClientLog && (!env.IsProd() || !noCallLogMap[info.FullMethod]) {
		// timeCost := time.Since(st)
		logger.Infof("request-log fromUserid %d method:%s,timeCost %dms,error:%v,req:%s,resp:%s", userid, info.FullMethod, timeCost.Milliseconds(), err, string(reqData), string(respData))
		if timeCost >= time.Millisecond*500 {
			logger.Infof("long_time request-log fromUserid %d method:%s,timeCost %dms,error:%v,req:%s,resp:%s", userid, info.FullMethod, timeCost.Milliseconds(), err, string(reqData), string(respData))
		}
	}

	logger.DeleteTrace(goId)
	return resp, err
}

func SvcServerLog(ctx context.Context, req interface{}, info *grpc.UnaryServerInfo, handler grpc.UnaryHandler) (interface{}, error) {
	span := tracer.SpanFromContext(ctx)
	if !tracer.IsSpanValid(span) {
		ctx, span = tracer.NewSpan(ctx, "SvcServerLog")
		defer span.End()
	}
	traceId := tracer.GetTraceID(span)
	goId := logger.GoID()
	if span.SpanContext().HasTraceID() && traceId != "" {
		traceId2 := logger.GetTrace(goId)
		if traceId2 != traceId {
			logger.SetTrace(goId, traceId)
		}
	} else {
		md, ok := metadata.FromIncomingContext(ctx)
		if ok {
			ms, ok := md[logger.VcTraceKey]
			if ok && len(ms) > 0 {
				traceId = ms[0]
			}
		}
		if traceId == "" {
			traceId = uuid.NewUUID().String()
		}
		logger.SetTrace(goId, traceId)
	}
	var (
		ip          string
		did         string
		packageName string
	)
	baseParam := bizcontext.GetBaseContext(ctx)
	if baseParam != nil {
		did = baseParam.Did
		packageName = baseParam.App
	}
	userid := usermd.GetUserIdFromContext(ctx)
	st := time.Now()
	//ctx = usermd.SetUserContext(ctx, &usermd.UserContext{UserId: 0})
	reqData, _ := protoJsonMarshalOption.Marshal(req.(proto.Message))
	tracer.AddSpanEvents(span, "message", map[string]string{
		"req": string(reqData),
	})

	resp, err := handler(ctx, req)
	timeCost := time.Since(st)
	if err != nil && timeCost.Milliseconds() < 3000 { // 返回小于2s 不算超时算 算用户取消,其它得还是算超时，业务目前报内容错误
		s, ok := status.FromError(err)
		if ok && s.Code() == codes.Canceled {
			// 判断是否定义了 *common.SvcBaseResp
			svcResp, ok := resp.(interface {
				GetBase() *common.SvcBaseResp
			})
			if ok && svcResp != nil {
				val := reflect.ValueOf(resp).Elem()
				if val.IsValid() && !val.IsZero() {
					v := val.FieldByName("Base")
					if v.CanSet() {
						v.Set(reflect.ValueOf(&common.SvcBaseResp{
							Code: errcode.ErrorCanceled.Code,
							Msg:  errcode.ErrorCanceled.Msg,
						}))
					}
				}
			}
		}
	}

	respData, _ := protoJsonMarshalOption.Marshal(resp.(proto.Message))
	tracer.AddSpanEvents(span, "message", map[string]string{
		"resp": string(respData),
	})
	if md, ok := metadata.FromIncomingContext(ctx); ok {
		ip = getClientIP(md)
	}
	spanTags := map[string]string{
		"userid": strconv.FormatInt(userid, 10),
		"ip":     ip,
		"packge": packageName,
		"did":    did,
	}
	tracer.AddSpanTags(span, spanTags)
	if !closeClientLog && (!env.IsProd() || !noCallLogMap[info.FullMethod]) {
		logger.Infof("request-log fromUserid %d method:%s,timeCost %dms,error:%v,req:%s,resp:%s", userid, info.FullMethod, timeCost.Milliseconds(), err, string(reqData), string(respData))
		if timeCost >= time.Millisecond*500 {
			logger.Infof("long_time request-log fromUserid %d method:%s,timeCost %dms,error:%v,req:%s,resp:%s", userid, info.FullMethod, timeCost.Milliseconds(), err, string(reqData), string(respData))
		}
	}

	logger.DeleteTrace(goId)
	return resp, err
}

func setTraceID(data proto.Message, traceID string) {
	if data == nil {
		return
	}

	// 获取 data 的反射值
	v := reflect.ValueOf(data)

	// 确保 data 是一个指针并且非 nil
	if v.Kind() != reflect.Ptr || v.IsNil() {
		return
	}

	// 获取指针指向的实际值
	elem := v.Elem()

	// 确保实际值是一个结构体
	if elem.Kind() != reflect.Struct {
		return
	}

	// 查找 traceId 字段
	traceIdField := elem.FieldByName("TraceId")

	// 确保 traceId 字段存在且可写
	if !traceIdField.IsValid() || !traceIdField.CanSet() {
		return
	}

	// 确保 traceId 字段是一个字符串
	if traceIdField.Kind() != reflect.String {
		return
	}

	// 设置 traceId 字段的值
	traceIdField.SetString(traceID)
}
