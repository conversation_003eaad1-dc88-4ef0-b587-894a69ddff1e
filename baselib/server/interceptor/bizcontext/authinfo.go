package bizcontext

import (
	"context"
	"encoding/base64"
	"encoding/json"
	"new-gitlab.xunlei.cn/vcproject/backends/proto"

	"google.golang.org/grpc/metadata"
	"new-gitlab.xunlei.cn/vcproject/backends/baselib/logger"
)

const vcBizAuthInfoKey = "vc_biz_auth_info"

func GetBizAuthInfoKey() string {
	return vcBizAuthInfoKey
}

func GetBizAuthInfo(ctx context.Context) *proto.AuthInfo {
	if md, ok := metadata.FromIncomingContext(ctx); ok {
		if data := md.Get(vcBizAuthInfoKey); len(data) > 0 && data[0] != "" {
			decoded, err := base64.StdEncoding.DecodeString(data[0])
			if err != nil {
				logger.Errorf("Error decoding:%v", err)
				return nil
			}
			var result = new(proto.AuthInfo)
			if err = json.Unmarshal(decoded, &result); err != nil {
				logger.Errorf("parse base data err:%v", err)
			}
			return result
		}
	}
	return nil
}
