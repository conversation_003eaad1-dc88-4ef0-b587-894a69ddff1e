package bizcontext

import (
	"context"
	"encoding/base64"
	"encoding/json"
	"google.golang.org/grpc"
	"google.golang.org/grpc/metadata"
	"new-gitlab.xunlei.cn/vcproject/backends/baselib/logger"
	"new-gitlab.xunlei.cn/vcproject/backends/baselib/server/usermd"
	"new-gitlab.xunlei.cn/vcproject/backends/baselib/util"
	"new-gitlab.xunlei.cn/vcproject/backends/proto/api/common"
	"strings"
)

const vcBizBaseKey = "vc_biz_base"

func GetBaseContextKey() string {
	return vcBizBaseKey
}

func GetBaseContextStr(ctx context.Context) string {
	md, ok := metadata.FromIncomingContext(ctx)
	if ok {
		c, _ok := md[vcBizBaseKey]
		if _ok && len(c) > 0 && c[0] != "" {
			return c[0]
		}
	}
	return ""
}

func GetBaseContext(ctx context.Context) *common.BaseParam {
	if md, ok := metadata.FromIncomingContext(ctx); ok {
		if data := md.Get(vcBizBaseKey); len(data) > 0 && data[0] != "" {
			decoded, err := base64.StdEncoding.DecodeString(data[0])
			if err != nil {
				logger.Errorf("Error decoding:%v", err)
				return nil
			}
			var result = new(common.BaseParam)
			if err = json.Unmarshal(decoded, &result); err != nil {
				logger.Errorf("parse base data err:%v", err)
			}
			return result
		}
	}
	return nil
}

func WriteSvcContext(ctx context.Context, req interface{}, info *grpc.UnaryServerInfo, handler grpc.UnaryHandler) (resp interface{}, err error) {
	type GetBase interface {
		GetBase() *common.BaseParam
	}
	var baseData *common.BaseParam

	// 在最顶层拦截器设置usermd.UserContext才有用
	userCtx := usermd.GetUserContext(ctx)
	if userCtx == nil {
		userCtx = &usermd.UserContext{}
		ctx = usermd.SetUserContext(ctx, userCtx)
	}

	if baseInt, ok := req.(GetBase); ok {
		baseData = baseInt.GetBase()
	}
	if baseData == nil {
		logger.Errorf("req:%#v not GetBase interface", req)
		return handler(ctx, req)
	}
	if md, ok := metadata.FromIncomingContext(ctx); ok {
		md.Set(vcBizBaseKey, base64.StdEncoding.EncodeToString([]byte(util.JsonStr(baseData))))
		ctx = metadata.NewIncomingContext(ctx, md.Copy())
	}

	return handler(ctx, req)
}

func GetUserAgentFromCtx(ctx context.Context) (ua string) {
	md, ok := metadata.FromIncomingContext(ctx)
	if ok {
		for k, v := range md {
			if strings.ToLower(k) == "user-agent" && len(v) > 0 {
				parts := strings.Split(v[0], ",")
				if len(parts) > 0 {
					ua = parts[0]
				}
			}
		}
	}
	return
}
