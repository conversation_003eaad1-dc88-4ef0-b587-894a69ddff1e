package interceptor

import (
	"context"
	"fmt"
	"runtime"

	"google.golang.org/grpc"
	"google.golang.org/protobuf/proto"
	"new-gitlab.xunlei.cn/vcproject/backends/baselib/dingding"
	"new-gitlab.xunlei.cn/vcproject/backends/baselib/errcode"
	"new-gitlab.xunlei.cn/vcproject/backends/baselib/logger"
	"new-gitlab.xunlei.cn/vcproject/backends/baselib/server/env"
	"new-gitlab.xunlei.cn/vcproject/backends/proto/api/common"
)

func Recover(ctx context.Context, req interface{}, info *grpc.UnaryServerInfo, handler grpc.UnaryHandler) (resp interface{}, err error) {
	defer func() {
		if r := recover(); r != nil {
			printStack()
			resp = &common.SvcCommonResp{
				Base: &common.SvcBaseResp{
					Code: errcode.ErrorInternal.Code,
					Msg:  errcode.ErrorInternal.Msg,
				},
			}
		}
	}()
	resp, err = handler(ctx, req)
	return
}

func RecoverWithResp(errorResp interface{}) func(ctx context.Context, req interface{}, info *grpc.UnaryServerInfo, handler grpc.UnaryHandler) (resp interface{}, err error) {
	return func(ctx context.Context, req interface{}, info *grpc.UnaryServerInfo, handler grpc.UnaryHandler) (resp interface{}, err error) {
		defer func() {
			if r := recover(); r != nil {
				logger.Errorf("panic reason:%v", r)
				msg := printStack()
				notifyPanic(info.FullMethod, req, msg, r)
				resp = errorResp
			}
		}()
		resp, err = handler(ctx, req)
		return
	}
}

func RecoverWithRespFunc(f func(stack string) interface{}) func(ctx context.Context, req interface{}, info *grpc.UnaryServerInfo, handler grpc.UnaryHandler) (resp interface{}, err error) {
	return func(ctx context.Context, req interface{}, info *grpc.UnaryServerInfo, handler grpc.UnaryHandler) (resp interface{}, err error) {
		defer func() {
			if r := recover(); r != nil {
				logger.Errorf("panic reason:%v", r)
				msg := printStack()
				resp = f(msg)
				notifyPanic(info.FullMethod, req, msg, r)
			}
		}()
		resp, err = handler(ctx, req)

		return
	}
}

func printStack() string {
	var buf [4096]byte
	n := runtime.Stack(buf[:], false)
	stackMsg := string(buf[:n])
	logger.Errorf(stackMsg)
	return stackMsg
}

func notifyPanic(method string, req interface{}, msg string, err any) {
	reqData, _ := protoJsonMarshalOption.Marshal(req.(proto.Message))
	trace := logger.GetCurrentTrace()
	go dingding.RobotClient().Send(dingding.DingDingMsg{
		MsgType: dingding.DINGDING_MSG_TYPE_TEXT,
		Text: dingding.Text{
			Content: fmt.Sprintf("panic错误: %v\n\n\n", err) +
				"trace:" + trace + "\n\n" +
				"method " + method + "\n\n" +
				" host " + env.GetHostName() + "\n\n" +
				"env " + string(env.GetEnvironment()) + "\n\n" +
				"req: " + string(reqData) + "\n\n" + msg,
		},
	})
}
