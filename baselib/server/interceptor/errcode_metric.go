package interceptor

import (
	"context"
	"encoding/base64"
	"errors"
	"strconv"
	"strings"

	"google.golang.org/grpc"
	"google.golang.org/grpc/metadata"
	"google.golang.org/protobuf/proto"
	"new-gitlab.xunlei.cn/vcproject/backends/baselib/dingding"
	"new-gitlab.xunlei.cn/vcproject/backends/baselib/errcode"
	"new-gitlab.xunlei.cn/vcproject/backends/baselib/logger"
	"new-gitlab.xunlei.cn/vcproject/backends/baselib/metric"
	"new-gitlab.xunlei.cn/vcproject/backends/baselib/server/env"
	"new-gitlab.xunlei.cn/vcproject/backends/baselib/server/interceptor/bizcontext"
	"new-gitlab.xunlei.cn/vcproject/backends/baselib/server/usermd"
	"new-gitlab.xunlei.cn/vcproject/backends/baselib/util"
	"new-gitlab.xunlei.cn/vcproject/backends/proto/api/common"
)

func filterDingdingMsg(msg string) (isReturn bool) {
	if msg == "rpc error: code = Canceled desc = context canceled" || msg == "context canceled" {
		return true
	}
	return
}

func SvcErrcodeMetric(ctx context.Context, req interface{}, info *grpc.UnaryServerInfo, handler grpc.UnaryHandler) (interface{}, error) {
	resp, err := handler(ctx, req)

	var eCode int32 = -1
	var msg string

	// 兼容直接错误返回: return nil, err;
	if err != nil {
		var ve *errcode.Error
		if errors.As(err, &ve) {
			eCode = ve.Code
			msg = ve.Msg
		}
	}

	// 原svcResp返回判断
	svcResp, ok := resp.(interface {
		GetBase() *common.SvcBaseResp
	})
	if ok && svcResp != nil && svcResp.GetBase() != nil {
		baseResp := svcResp.GetBase()
		eCode = baseResp.Code
		msg = baseResp.Msg
	}

	sp := strings.Split(info.FullMethod, "/")
	metric.CounterWithLabels("grpc_svc_server_errcode", map[string]string{
		"grpc_service": sp[1],
		"grpc_method":  info.FullMethod,
		"errcode":      strconv.FormatInt(int64(eCode), 10),
	}).Inc()

	notifyErrorCode(eCode, info.FullMethod, msg, req, resp)
	return resp, err
}

func BizErrcodeMetric(ctx context.Context, req interface{}, info *grpc.UnaryServerInfo, handler grpc.UnaryHandler) (interface{}, error) {
	resp, err := handler(ctx, req)
	var errcode int32 = -1
	msg := ""
	svcResp, ok := resp.(interface {
		GetCode() int32
		GetMsg() string
	})
	if ok && svcResp != nil {
		errcode = svcResp.GetCode()
		msg = svcResp.GetMsg()
	} else {
		res, ok := resp.(interface {
			GetResult() int32
		})
		if ok && res != nil {
			errcode = res.GetResult()
		}
	}
	sp := strings.Split(info.FullMethod, "/")
	metric.CounterWithLabels("grpc_svc_server_errcode", map[string]string{
		"grpc_service": sp[1],
		"grpc_method":  info.FullMethod,
		"errcode":      strconv.FormatInt(int64(errcode), 10),
	}).Inc()
	notifyErrorCode(errcode, info.FullMethod, msg, req, resp)
	return resp, err
}

// 在 interceptor 包中添加新的拦截器
// baselib/server/interceptor/header.go
func HeaderToContextInterceptor() grpc.UnaryServerInterceptor {
	return func(ctx context.Context, req interface{}, info *grpc.UnaryServerInfo, handler grpc.UnaryHandler) (interface{}, error) {
		// 从 metadata 中获取 header
		md, ok := metadata.FromIncomingContext(ctx)
		if !ok {
			md = metadata.New(map[string]string{})
		}
		dt, _ := strconv.ParseInt(getFirstValue(md, "dt"), 10, 32)
		nt, _ := strconv.ParseInt(getFirstValue(md, "nt"), 10, 32)
		ts, _ := strconv.ParseInt(getFirstValue(md, "ts"), 10, 64)
		logger.Infof("md: %v", md)
		baseParam := &common.BaseParam{
			// 从 header 中获取各种参数
			App:  getFirstValue(md, "app"),
			Av:   getFirstValue(md, "av"),
			Dt:   int32(dt),
			Did:  getFirstValue(md, "did"),
			Nt:   int32(nt),
			Ch:   getFirstValue(md, "ch"),
			Md:   getFirstValue(md, "md"),
			Os:   getFirstValue(md, "os"),
			Ts:   ts,
			Ip:   getFirstValue(md, "x-forwarded-for"),
			Imei: getFirstValue(md, "imei"),
			Oaid: getFirstValue(md, "oaid"),
			Bd:   getFirstValue(md, "bd"),
			Idfa: getFirstValue(md, "idfa"),
			Vpn:  getFirstValue(md, "vpn"),
			Ua:   getFirstValue(md, "User-Agent"),
		}
		// 在最顶层拦截器设置usermd.UserContext才有用
		userCtx := usermd.GetUserContext(ctx)
		if userCtx == nil {
			userCtx = &usermd.UserContext{}
			ctx = usermd.SetUserContext(ctx, userCtx)
		}
		if md, ok := metadata.FromIncomingContext(ctx); ok {
			md.Set(bizcontext.GetBaseContextKey(), base64.StdEncoding.EncodeToString([]byte(util.JsonStr(baseParam))))
			ctx = metadata.NewIncomingContext(ctx, md.Copy())
		}
		return handler(ctx, req)
	}
}

// 辅助函数：获取 metadata 中的第一个值
func getFirstValue(md metadata.MD, key string) string {
	if values := md.Get(key); len(values) > 0 {
		return values[0]
	}
	return ""
}

func notifyErrorCode(code int32, method, msg string, req, resp interface{}) {
	switch code {
	case errcode.ErrorInternal.Code:
	default:
		return
	}
	if filterDingdingMsg(msg) {
		return
	}
	trace := logger.GetCurrentTrace()
	go func() {
		reqData, _ := protoJsonMarshalOption.Marshal(req.(proto.Message))
		dingding.RobotClient().Send(dingding.DingDingMsg{
			MsgType: dingding.DINGDING_MSG_TYPE_TEXT,
			Text: dingding.Text{
				Content: "内部错误 \n\n" +
					"host " + env.GetHostName() + "\n\n" +
					"env " + string(env.GetEnvironment()) + "\n\n" +
					"method " + method + "\n\n" +
					"trace:" + trace + "\n\n" +
					"req: " + string(reqData) + "\n\n" +
					"resp:" + util.JsonStr(resp) + "\n\n",
			},
		})
	}()
}
