package interceptor

import (
	"context"
	"encoding/base64"
	"errors"
	"net"
	"strconv"
	"strings"

	"google.golang.org/grpc"
	"google.golang.org/grpc/codes"
	"google.golang.org/grpc/metadata"
	"google.golang.org/grpc/status"
	"new-gitlab.xunlei.cn/vcproject/backends/baselib/errcode"
	"new-gitlab.xunlei.cn/vcproject/backends/baselib/logger"
	"new-gitlab.xunlei.cn/vcproject/backends/baselib/server/env"
	"new-gitlab.xunlei.cn/vcproject/backends/baselib/server/interceptor/bizcontext"
	"new-gitlab.xunlei.cn/vcproject/backends/baselib/server/usermd"
	"new-gitlab.xunlei.cn/vcproject/backends/baselib/util"
	"new-gitlab.xunlei.cn/vcproject/backends/baselib/xlaccount"
	"new-gitlab.xunlei.cn/vcproject/backends/proto"
)

const (
	HKeyIPForwarded = "x-forwarded-for"
	HKeyIPProxy     = "proxy-client-ip"
	HKeyIPWlProxy   = "wl-proxy-client-ip"
)

var (
	whiteIps = []string{
		"***************", // office
		"***********",     // office backup
		"*************",   //Hongkong
		"***********",     //korean Seoul
		"***************", //Indonesia
		"***********",     // pod ip
		"*************",   // office vpn
		"**************",  //小川vpn
	}
)

// AuthInterceptor 登录权限拦截器,弱校验是需要获取用户信息，没有用户信息不返回错误
func AuthInterceptor(ctx context.Context, req interface{}, info *grpc.UnaryServerInfo, handler grpc.UnaryHandler) (interface{}, error) {
	// 获取base参数
	baseParam := bizcontext.GetBaseContext(ctx)
	if env.IsTest() {
		logger.Debugf("GetBaseContext baseParam=%+v", util.JsonStr(baseParam))
	}
	userCtx := usermd.GetUserContext(ctx)
	if userCtx == nil {
		userCtx = &usermd.UserContext{}
		ctx = usermd.SetUserContext(ctx, userCtx)
	}
	fullMethod := info.FullMethod
	isWeakAuth := xlaccount.IsWeakAuthorize(info.FullMethod)
	logger.Debugf("fullMethod=%v, isWeakAuth=%v", fullMethod, isWeakAuth)

	md, ok := metadata.FromIncomingContext(ctx)
	if ok {
		if !env.IsProd() { //上线的时候把这个注释放出dd
			from, ok := md["from"]
			if ok && from[0] == "postman" {
				testCtx := setTestUser(ctx)
				if testCtx == nil {
					return errcode.ErrorParam.ToBizBaseResp()
				}
				return handler(testCtx, req)
			}
		} else {
			ip := xlaccount.GetIpFromCtx(ctx)
			logger.Debugf("CheckAuthInterceptor ip=%s whiteIp%+v", ip, whiteIps)
			if util.InStringSlice(whiteIps, ip) || isIPv4StartsWith10Dot4(ip) {
				tmpMd := xlaccount.GetMdMap(ctx)
				authToken := tmpMd[xlaccount.HeaderAuthKey]
				if len(authToken) == 0 {
					authToken = tmpMd[strings.ToLower(xlaccount.HeaderAuthKey)]
				}
				// 设置一个万能的token
				sp := strings.Split(authToken, " ")
				logger.Debugf("CheckAuthInterceptor ip= %s, sp=%+v", ip, sp)
				if len(sp) == 1 && sp[0] == "*******************************************************************************************************************************************.t1NWhp53xzw4nrz_tn5S1sP0HgCLBnaQtdsLoDGxlU4==" {
					testCtx := setTestUser(ctx)
					if testCtx == nil {
						return errcode.ErrorParam.ToBizBaseResp()
					}
					return handler(testCtx, req)
				}
			}
		}
	}

	// 1. 非白名单方法，非若校验方法走以下逻辑
	if !isWeakAuth {
		authInfo, err := xlaccount.CheckAuthTokenWithValidator(ctx)
		logger.Debugf("CheckAuthInterceptor authInfo=%+v", authInfo)
		// 1.1鉴权有问题返回相应错误
		if err != nil || authInfo == nil {
			logger.Errorf("CheckAuthInterceptor err: %+v", err)
			// 根据具体错误类型返回不同的错误码
			if err != nil {
				// 如果是token为空的情况，返回更明确的错误码
				if errors.Is(err, errcode.ErrTokenRequired) {
					return errcode.ErrTokenMissing.ToBizBaseResp()
				}
				// 尝试将error转换为errcode.Error类型
				//if errCodeErr := errcode.FromError(err); errCodeErr != nil {
				//	return errCodeErr.ToBizBaseResp()
				//}
				// 如果转换失败，返回通用未授权错误
				return errcode.ErrUnauthorized.ToBizBaseResp()
			}
			// authInfo为nil但err为nil的情况，返回未授权
			return errcode.ErrUnauthorized.ToBizBaseResp()
		}
		// 1.2 用户授权没问题，业务系统继续校验
		checkResult, err := xlaccount.CheckAccount(ctx, fullMethod, baseParam, authInfo)
		logger.Debugf("CheckAuthInterceptor checkResult=%+v", checkResult)
		if err != nil {
			s, ok := status.FromError(err)
			if !ok {
				return errcode.ErrorInternal.ToBizBaseResp()
			}
			if s.Code() == codes.Canceled {
				return errcode.ErrorCanceled.ToBizBaseResp()
			}
			return errcode.ErrorInternal.ToBizBaseResp()
		}
		if ok, resp := checkResult.ErrorResp(); !ok {
			return resp, nil
		}
		// 1.3 业务系统查不到用户，提示注册
		if checkResult.IsAccountNull() {
			if xlaccount.IsRequiredSignup(fullMethod) {
				return errcode.ErrUserNotFound.ToBizBaseResp()
			}
		} else {
			// @todo 用户封禁，用户注销等业务逻辑处理
		}
		// 1.4 设置用户信息给下游服务使用
		authInfo.UserId = checkResult.UserId
		authInfo.Gender = checkResult.Gender
		ctx = xlaccount.SetAuthInfo(ctx, authInfo)
		logger.Debugf("CheckAuthInterceptor final authInfo=%+v", authInfo)
		logger.Debugf("CheckAuthInterceptor GetAuthInfo:%v", xlaccount.GetAuthInfo(ctx))
		//userCtx.UserId = checkResult.UserId

	} else {
		for {
			authInfo, err := xlaccount.CheckAuthTokenWithValidator(ctx)
			if err != nil {
				break
			}
			checkResult, err := xlaccount.CheckAccount(ctx, fullMethod, baseParam, authInfo)
			if err != nil {
				logger.Warnf("error %v", err)
				break
			}

			authInfo.UserId = checkResult.UserId
			authInfo.Gender = checkResult.Gender
			authInfo.Channel = checkResult.Channel
			authInfo.RegTime = checkResult.RegTime
			ctx = xlaccount.SetAuthInfo(ctx, authInfo)
			break
		}
	}
	return handler(ctx, req)
}

func isIPv4StartsWith10Dot4(ipStr string) bool {
	ip := net.ParseIP(ipStr)
	if ip == nil {
		return false
	}
	ipv4 := ip.To4()
	if ipv4 == nil {
		return false
	}
	return ipv4[0] == 10 && ipv4[1] == 4
}

func getClientIP(md metadata.MD) string {
	keys := []string{
		HKeyIPForwarded,
		HKeyIPProxy,
		HKeyIPWlProxy,
	}
	for _, key := range keys {
		mdVal, ok := md[key]
		if ok {
			if len(mdVal[0]) > 0 {
				ips := strings.Split(mdVal[0], ",")
				if len(ips) > 0 {
					return ips[0]
				}
			}
		}
	}
	return ""
}

func setTestUser(ctx context.Context) context.Context {
	md, ok := metadata.FromIncomingContext(ctx)
	if ok {
		userIdMd, ok := md["user_id"]
		if ok {
			var (
				sex     int64
				regTime int64
			)
			if sexStr, ok := md["gender"]; ok {
				sex, _ = strconv.ParseInt(sexStr[0], 10, 64)
			}
			ip := getClientIP(md)
			userId, _ := strconv.ParseInt(userIdMd[0], 10, 64)
			ctx = usermd.SetUserContext(ctx, &usermd.UserContext{
				UserId: userId,
			})
			return xlaccount.SetAuthInfo(ctx, &proto.AuthInfo{
				UserId:  userId,
				Gender:  int32(sex),
				Ip:      ip,
				RegTime: regTime,
			})

		}
	}
	return nil
}

func SvcAuthInfoInterceptor(ctx context.Context, req interface{}, info *grpc.UnaryServerInfo, handler grpc.UnaryHandler) (interface{}, error) {
	authInfo := bizcontext.GetBizAuthInfo(ctx)
	ctx = xlaccount.SetAuthInfo(ctx, authInfo)
	return handler(ctx, req)
}

func BizAuthInfoInterceptor(ctx context.Context, method string, req interface{}, reply interface{}, cc *grpc.ClientConn, invoker grpc.UnaryInvoker, opts ...grpc.CallOption) error {
	md, _ := metadata.FromOutgoingContext(ctx)
	authInfo := xlaccount.GetAuthInfo(ctx)
	v := base64.StdEncoding.EncodeToString([]byte(util.JsonStr(authInfo)))
	md = metadata.Join(md, metadata.Pairs(bizcontext.GetBizAuthInfoKey(), v))
	ctx = metadata.NewOutgoingContext(ctx, md)
	return invoker(ctx, method, req, reply, cc, opts...)
}
