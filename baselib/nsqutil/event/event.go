package event

import (
	"encoding/json"
	"fmt"
	"strings"

	"github.com/pborman/uuid"
	"new-gitlab.xunlei.cn/vcproject/backends/baselib/util"
)

type OType string
type EType string

type Event interface {
	String() string
	GetId() string
	GetOType() OType
	GetEType() EType
	GetData() []byte
	ExtractEventData(data interface{}) (err error)
}

// event中的mid是触发该事件的用户id，oid是触发事件对象id
// case1: otype=post, etype=create mid=发帖人的mid, oid是帖子id
// case2: otype=user, etype=attention mid=发起关注的用户mid, oid是被关注用户的mid
type BaseEvent struct {
	// 事件ID，业务可以选择根据这个做消息幂等处理逻辑
	Id    string          `json:"id"`
	OType OType           `json:"otype"`
	EType EType           `json:"etype"`
	Data  json.RawMessage `json:"data,omitempty"`
}

func (e *BaseEvent) String() string {
	return fmt.Sprintf("id: %s, otype:%s, etype:%s, data: %s",
		e.Id, e.OType, e.EType, string(e.Data))
}

func (e *BaseEvent) GetId() string {
	return e.Id
}

func (e *BaseEvent) GetOType() OType {
	return e.OType
}

func (e *BaseEvent) GetEType() EType {
	return e.EType
}

func (e *BaseEvent) GetData() []byte {
	return e.Data
}

// data must be ptr type
func (e *BaseEvent) ExtractEventData(data interface{}) (err error) {
	return util.Unmarshal(e.Data, data)
}

func GenBaseEvent(otype OType, etype EType, data interface{}) (e *BaseEvent) {
	d, _ := util.Marshal(data)
	e = &BaseEvent{
		Id:    genMsgId(),
		OType: otype,
		EType: etype,
		Data:  d,
	}
	return
}

func genMsgId() string {
	return strings.Replace(uuid.New(), "-", "", -1)
}
