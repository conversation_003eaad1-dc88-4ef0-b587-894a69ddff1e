package event

import (
	"encoding/json"
	"runtime"
	"time"

	"github.com/nsqio/go-nsq"
	"new-gitlab.xunlei.cn/vcproject/backends/baselib/dingding"
	"new-gitlab.xunlei.cn/vcproject/backends/baselib/logger"
	"new-gitlab.xunlei.cn/vcproject/backends/baselib/server/env"
)

type Handler interface {
	HandleMessage(e Event) error
}

var handlers = make(map[OType]Handler)

func RegisterHandler(oType OType, h Handler) (err error) {
	if _, ok := handlers[oType]; ok {
		return nil
	}
	handlers[oType] = h
	return
}

func EventProc(message *nsq.Message) (err error) {
	e := &BaseEvent{}
	err = json.Unmarshal(message.Body, e)
	if err != nil {
		logger.Errorf("error unmarshal msg %v %s", err, string(message.Body))
		return err
	}
	err = func() error {
		defer func() {
			err := recover()
			if err != nil {
				buf := make([]byte, 10240)
				n := runtime.Stack(buf, false)
				buf = buf[:n]
				logger.Errorf("subscriber crash: %v\n%s", err, string(buf))
				time.Sleep(time.Second)
				go dingding.RobotClient().Send(dingding.DingDingMsg{
					MsgType: dingding.DINGDING_MSG_TYPE_TEXT,
					Text: dingding.Text{
						Content: "host " + env.GetServiceName() + "\n\n\n" +
							"env " + string(env.GetEnvironment()) + "\n\n\n" +
							string(buf),
					},
				})
			}
		}()
		h := handlers[e.GetOType()]
		if h == nil {
			return nil
		}
		st := time.Now()
		err = h.HandleMessage(e)
		if err == nil {
			logger.Debugf("rec event:{%v} timeCost %d ms error %v", e.String(), time.Since(st).Milliseconds(), err)
		} else {
			EventErrcodeDingding(e, err)
		}
		return err
	}()
	if err != nil {
		logger.Errorf("process event fail, data %s, error: %+v", e.String(), err)
		return err
	}
	return nil
}

func EventErrcodeDingding(e Event, err error) {
	dingding.RobotClient().Send(dingding.DingDingMsg{
		MsgType: dingding.DINGDING_MSG_TYPE_TEXT,
		Text: dingding.Text{
			Content: "event 内部错误 \n\n" +
				"host " + env.GetHostName() + "\n\n" +
				"env " + string(env.GetEnvironment()) + "\n\n" +
				"req: " + e.String() + "\n\n" +
				"err:" + err.Error(),
		},
	})
}
