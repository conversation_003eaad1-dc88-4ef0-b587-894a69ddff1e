package nsqutil

import (
	"context"
	"fmt"
	"os"
	"testing"
	"time"

	"github.com/nsqio/go-nsq"
	"new-gitlab.xunlei.cn/vcproject/backends/baselib/config"
	"new-gitlab.xunlei.cn/vcproject/backends/baselib/logger"
	"new-gitlab.xunlei.cn/vcproject/backends/baselib/server/env"
)

// nsq消费
type nsqConsumer struct{}

func TestMain(m *testing.M) {
	logger.InitLoggerWitchLevel("/dev/stdout", "debug")

	defer logger.Sync()
	os.Setenv("env", "local")
	env.ResetEnv()
	m.Run()
}

func (c *nsqConsumer) HandleMessage(message *nsq.Message) error {
	// data := &tracingmq.TraceMqData{}
	// json.Unmarshal(message.Body, data)
	// tracing.BuildContextByCarrier(data.Carriers, "mq_consumer", data.Topic)
	fmt.Printf("message body: %v\n", string(message.Body))
	return nil
}

// 构造一个NsqConsumer
func newConsumerTest() error {
	return NewConsumer(&config.NsqConfig{
		Lookupds: []string{"10.10.45.71:4161"},
	}, 0)
}

// 构造一个NsqProducer
func newProducerTest() error {
	return NewProducer(&config.NsqConfig{
		Lookupds: []string{"10.10.45.71:4161"},
	})
}

func TestSubscribe(t *testing.T) {
	newConsumerTest()

	var (
		topic   = "hello"
		channel = "world"
		handler = &nsqConsumer{}
	)
	if _, err := Subscribe(topic, channel, handler); err != nil {
		t.Errorf("Subscribe err:%v", err)
	}
}

func TestPublish(t *testing.T) {
	newConsumerTest()
	newProducerTest()

	var (
		topic   = "hello"
		channel = "world"
		handler = &nsqConsumer{}
	)
	if err := PublishJSON(context.Background(), topic, "hello, nsq!"); err != nil {
		t.Errorf("Publish err:%v", err)
	}
	if _, err := Subscribe(topic, channel, handler); err != nil {
		t.Errorf("Subscribe err:%v", err)
	}
	time.Sleep(time.Second)
}
