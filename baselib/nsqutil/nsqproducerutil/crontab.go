/*
 * @Author: genobili <EMAIL>
 * @Date: 2025-03-25 20:34:49
 * @LastEditors: genobili <EMAIL>
 * @LastEditTime: 2025-03-25 20:35:04
 * @FilePath: /backends/baselib/nsqutil/nsqproducerutil/crontab.go
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
 */
package nsqproducerutil

import (
	"time"

	"new-gitlab.xunlei.cn/vcproject/backends/baselib/logger"
)

// 定时刷新nsqd client配置
type OrderCrontab struct {
	interval uint32
	ProBussi *ProducerBussi
}

func NewOrderCrontab(time uint32, cli *ProducerBussi) *OrderCrontab {
	cron := &OrderCrontab{
		interval: time,
		ProBussi: cli,
	}
	return cron
}

func (cron *OrderCrontab) process() {
	for {

		time.Sleep(time.Duration(cron.interval) * time.Second)
		cron.reload()
	}
}

func (cron *OrderCrontab) Start() {
	go cron.process()
}

// 该方法只处理新增的节点,失败节点的删除逻辑 由 ProducerBussi 自己处理
func (cron *OrderCrontab) reload() error {
	if cron.ProBussi == nil {
		logger.Logger().Error("[OrderCrontab] probussi is nil")
		return nil
	}
	return cron.ProBussi.FlushNsqd()
}
