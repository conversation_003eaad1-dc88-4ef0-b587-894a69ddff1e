package signature

import (
	"crypto/md5"
	"crypto/sha256"
	"encoding/hex"
	"fmt"
	"golang.org/x/text/cases"
	"golang.org/x/text/language"
	"net/http"
	"sort"
	"strconv"
	"strings"
	"time"

	"new-gitlab.xunlei.cn/vcproject/backends/baselib/config"
	"new-gitlab.xunlei.cn/vcproject/backends/baselib/errcode"
	"new-gitlab.xunlei.cn/vcproject/backends/baselib/logger"
	"new-gitlab.xunlei.cn/vcproject/backends/proto/api/common"
)

const (
	// 默认配置
	DefaultTimeWindow = 300           // 5分钟
	DefaultAlgorithm  = "HMAC-SHA256" // 当前实现兼容客户端：SHA256 -> MD5
)

// HTTPSignatureVerifier HTTP 专用签名验证器
type HTTPSignatureVerifier struct {
	config *config.SignatureConfig
}

// NewHTTPSignatureVerifier 创建 HTTP 签名验证器
func NewHTTPSignatureVerifier(conf *config.SignatureConfig) *HTTPSignatureVerifier {
	if conf == nil {
		conf = &config.SignatureConfig{
			Enable:     false,
			TimeWindow: DefaultTimeWindow,
			Algorithm:  DefaultAlgorithm,
		}
	}
	return &HTTPSignatureVerifier{config: conf}
}

// VerifyHTTPRequest 验证 HTTP 请求签名
// headers: HTTP 请求头
// body: 原始请求体
func (h *HTTPSignatureVerifier) VerifyHTTPRequest(headers http.Header, body []byte) error {
	if !h.config.Enable {
		return nil
	}

	// 从 headers 提取签名参数
	baseParam, err := h.extractBaseParamFromHeaders(headers)
	if err != nil {
		return err
	}
	logger.Infof("VerifyHTTPRequest, baseParam: %v", baseParam)

	// 验证必需字段
	if err := h.validateRequiredFields(baseParam); err != nil {
		return err
	}

	// 验证时间戳
	if err := h.verifyTimestamp(baseParam.GetTs()); err != nil {
		return err
	}

	// 获取签名密钥
	platform := h.getPlatformFromDevType(baseParam.GetDt())
	secret, err := h.getSecret(platform)
	if err != nil {
		return err
	}

	// 计算期望签名
	expectedSignature, err := h.computeHTTPSignature(baseParam, body, secret)
	if err != nil {
		return err
	}

	// 验证签名
	actualSignature := baseParam.GetSignature()
	if actualSignature != expectedSignature {
		logger.Warnf("HTTP signature verification failed, expected: %s, actual: %s, platform: %s",
			expectedSignature, actualSignature, platform)
		return errcode.ErrSignatureInvalid
	}

	logger.Debugf("HTTP signature verification success for platform: %s", platform)
	return nil
}

// extractBaseParamFromHeaders 从 HTTP headers 提取 BaseParam
func (h *HTTPSignatureVerifier) extractBaseParamFromHeaders(headers http.Header) (*common.BaseParam, error) {
	// 获取必要字段
	signature := h.getHeaderValue(headers, "signature")
	if signature == "" {
		return nil, errcode.ErrSignatureMissing
	}

	tsStr := h.getHeaderValue(headers, "ts")
	if tsStr == "" {
		return nil, errcode.ErrTimestampMissing
	}

	ts, err := strconv.ParseInt(tsStr, 10, 64)
	if err != nil {
		return nil, errcode.ErrTimestampInvalid
	}

	// 获取其他字段
	dtStr := h.getHeaderValue(headers, "dt")
	dt, _ := strconv.ParseInt(dtStr, 10, 32)

	ntStr := h.getHeaderValue(headers, "nt")
	nt, _ := strconv.ParseInt(ntStr, 10, 32)

	return &common.BaseParam{
		App:       h.getHeaderValue(headers, "app"),
		Av:        h.getHeaderValue(headers, "av"),
		Dt:        int32(dt),
		Did:       h.getHeaderValue(headers, "did"),
		Nt:        int32(nt),
		Ch:        h.getHeaderValue(headers, "ch"),
		Md:        h.getHeaderValue(headers, "md"),
		Os:        h.getHeaderValue(headers, "os"),
		Ts:        ts,
		Ip:        h.getHeaderValue(headers, "x-forwarded-for"),
		Imei:      h.getHeaderValue(headers, "imei"),
		Oaid:      h.getHeaderValue(headers, "oaid"),
		Bd:        h.getHeaderValue(headers, "bd"),
		Idfa:      h.getHeaderValue(headers, "idfa"),
		Vpn:       h.getHeaderValue(headers, "vpn"),
		Ua:        h.getHeaderValue(headers, "user-agent"),
		Smid:      h.getHeaderValue(headers, "smid"),
		Aid:       h.getHeaderValue(headers, "aid"),
		Signature: signature,
	}, nil
}

// getHeaderValue 获取 header 值，支持多种格式
func (h *HTTPSignatureVerifier) getHeaderValue(headers http.Header, key string) string {
	// 尝试不同的 header 格式
	variations := []string{
		key,
		cases.Lower(language.Und).String(key),
		cases.Upper(language.Und).String(key),
		cases.Title(language.Und).String(key),
		"x-" + key,
		"X-" + cases.Title(language.Und).String(key),
	}

	for _, variation := range variations {
		if value := headers.Get(variation); value != "" {
			return value
		}
	}

	return ""
}

// validateRequiredFields 验证必需字段
func (h *HTTPSignatureVerifier) validateRequiredFields(baseParam *common.BaseParam) error {
	if baseParam.GetSignature() == "" {
		return errcode.ErrSignatureMissing
	}
	if baseParam.GetTs() <= 0 {
		return errcode.ErrTimestampMissing
	}
	if baseParam.GetApp() == "" {
		return errcode.ErrSignatureMissing
	}
	if baseParam.GetDid() == "" {
		return errcode.ErrSignatureMissing
	}
	return nil
}

// verifyTimestamp 验证时间戳
func (h *HTTPSignatureVerifier) verifyTimestamp(timestamp int64) error {
	// 转换为秒（如果是毫秒时间戳）
	timestampSec := timestamp
	if timestamp > 1e12 { // 毫秒时间戳
		timestampSec = timestamp / 1000
	}

	now := time.Now().Unix()
	timeWindow := h.config.TimeWindow
	if timeWindow <= 0 {
		timeWindow = DefaultTimeWindow
	}

	if timestampSec < now-timeWindow || timestampSec > now+timeWindow {
		logger.Warnf("timestamp expired, now: %d, timestamp: %d, window: %d",
			now, timestampSec, timeWindow)
		return errcode.ErrTimestampExpired
	}

	return nil
}

// computeHTTPSignature 计算 HTTP 签名
func (h *HTTPSignatureVerifier) computeHTTPSignature(baseParam *common.BaseParam, body []byte, secret string) (string, error) {
	// 构建 baseParams 字符串
	baseParams := make(map[string]string)

	// 添加标准字段（按字母顺序）
	baseParams["app"] = baseParam.GetApp()
	baseParams["av"] = baseParam.GetAv()
	baseParams["dt"] = strconv.Itoa(int(baseParam.GetDt()))
	baseParams["did"] = baseParam.GetDid()
	baseParams["smid"] = baseParam.GetSmid()
	baseParams["aid"] = baseParam.GetAid()
	baseParams["oaid"] = baseParam.GetOaid()
	baseParams["imei"] = baseParam.GetImei()
	baseParams["nt"] = strconv.Itoa(int(baseParam.GetNt()))
	baseParams["vpn"] = baseParam.GetVpn()
	baseParams["ch"] = baseParam.GetCh()
	baseParams["bd"] = baseParam.GetBd()
	baseParams["md"] = baseParam.GetMd()
	baseParams["os"] = baseParam.GetOs()
	baseParams["ts"] = strconv.FormatInt(baseParam.GetTs(), 10)

	// 构建参数字符串
	baseParamsString := h.buildParamsString(baseParams)

	// 构建完整的签名字符串
	signString := baseParamsString + string(body) + secret

	logger.Debugf("HTTP signature string: %s", signString)

	// 计算签名：SHA256 -> MD5
	sha256Hash := sha256.Sum256([]byte(signString))
	md5Hash := md5.Sum([]byte(hex.EncodeToString(sha256Hash[:])))

	return hex.EncodeToString(md5Hash[:]), nil
}

// buildParamsString 构建参数字符串
func (h *HTTPSignatureVerifier) buildParamsString(params map[string]string) string {
	// 获取所有键并排序
	keys := make([]string, 0, len(params))
	for key := range params {
		keys = append(keys, key)
	}
	sort.Strings(keys)

	// 构建参数字符串
	var parts []string
	for _, key := range keys {
		parts = append(parts, fmt.Sprintf("%s=%s", key, params[key]))
	}

	return strings.Join(parts, "&")
}

// getPlatformFromDevType 根据设备类型获取平台
func (h *HTTPSignatureVerifier) getPlatformFromDevType(devType int32) string {
	switch devType {
	case 1:
		return "android"
	case 2:
		return "ios"
	case 3:
		return "web"
	default:
		return "android" // 默认
	}
}

// getSecret 获取签名密钥
func (h *HTTPSignatureVerifier) getSecret(platform string) (string, error) {
	// 优先使用平台特定密钥
	if platform != "" && h.config.PlatformSecrets != nil {
		if secret, exists := h.config.PlatformSecrets[platform]; exists && secret != "" {
			return secret, nil
		}
	}

	// 使用默认密钥
	if h.config.DefaultSecret != "" {
		return h.config.DefaultSecret, nil
	}

	return "", errcode.ErrSignatureConfigError
}

// IsEnabled 检查签名验证是否启用
func (h *HTTPSignatureVerifier) IsEnabled() bool {
	return h.config != nil && h.config.Enable
}
