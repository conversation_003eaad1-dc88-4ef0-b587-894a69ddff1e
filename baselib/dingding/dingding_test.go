package dingding

import (
	"fmt"
	"testing"
)

func TestDingdingProd(t *testing.T) {
	c := DingdingClient{
		Secret:  "SEC19ec2441e3a20d294ca17c39269f751efb04b5fb1255fc5b9f28fb5f0cf6d84f",
		BaseUrl: "https://oapi.dingtalk.com/robot/send?access_token=15c6c987d588c9cade51602d7d0a85aff0fb6f14526ce1ee77febd565e53e1a6",
	}

	err := c.Send(DingDingMsg{
		MsgType: DINGDING_MSG_TYPE_TEXT,
		Text: Text{
			Content: "测试使用",
		},
	})

	fmt.Println(err)
}

func TestDingMarkdown(t *testing.T) {

	DingdingClient{
		Secret:  "SEC30cde7ca6ced3d718fab0a9bb3ed6fa00dd7d88f222fb3c358783f7ee81385f8",
		BaseUrl: "https://oapi.dingtalk.com/robot/send?access_token=8281635c9b0a90843c53fddebab535863dbdebcbe40f59f8c713bbfd470a17c2",
	}.Send(DingDingMsg{
		MsgType: DINGDING_MSG_TYPE_TEXT,
		Text: Text{
			Content: fmt.Sprintf("method:%s\nuri:\n%s\n\nheader:\n%v", "Get", "5RylnX0npEzAKb_lB7j-Lf1C1RVHPsxYXja_jaQ5-VHr9I-87y6yVFvrABZU26GyE_zFnp-jUZjf1xwDfZOnUmrX1fqjAQEYHJjbSaksCcm5pk24B8QEb_tyyOZW0ErfdioSXEPF-ymSyj9Dy2RwRqOXsKsywDtiF5GiYVTs2TrvN11Bh1FzVWArwK9OXhr2uCfauEcKI2Cz9NOqKtEiBrgMaKP6Ikwa6WQII7Tf3tQhqggaBuLkTiC_rCyTyP6KlDF1XVWjBc1hoRHIg4J1gVFxjfMBRwjv6tXiAV8OUWnBMIrYnbioQcD-Y9UmgGASVJAo3xqAGlWahCYhxvWSkXKG9AgmtZnd_yOkD4c6GxW8Fz7PWP8q9vHoEw0m9p7bfD7vSysbGknmQaSLu9hGpPQ9xeLqubpQz0qb2uJswnsJhDjvlRn015scPaRYfzeMAwhXF4hNu3aOS_z61X2nLd6omMxzIhlt-42wkr7fC1aimYFpkViGFjy2D4SPuxSNveJ_d1tAg02nmThCYLingQ",
				`{
				"Accept-Encoding": ["gzip"],
				"Sec-Websocket-Extensions": ["permessage-deflate"],
				"Sec-Websocket-Key": ["+uAs0jl7YKBxrh2sGhuESA=="],
				"Sec-Websocket-Version": ["13"],
				"User-Agent": ["okhttp/4.12.0"],
				"X-Forwarded-For": ["************"],
				"X-Forwarded-Host": ["conn.talkconnect.net"],
				"X-Forwarded-Port": ["80"],
				"X-Forwarded-Proto": ["https"],
				"X-Forwarded-Scheme": ["https"],
				"X-Original-Forwarded-For": ["************"],
				"X-Real-Ip": ["************"],
				"X-Request-Id": ["ab185e49fae699996709e83aebf0d458"],
				"X-Scheme": ["https"]
			}`),
		},
	})
}
