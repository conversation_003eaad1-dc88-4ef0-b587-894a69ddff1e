package tracer

import (
	"context"

	"go.opentelemetry.io/otel"
	"go.opentelemetry.io/otel/attribute"
	sdktrace "go.opentelemetry.io/otel/sdk/trace"
	"go.opentelemetry.io/otel/trace"
)

func NewSpan(ctx context.Context, name string, opts ...trace.SpanStartOption) (context.Context, trace.Span) {
	return otel.Tracer("").Start(ctx, name, opts...)
}

func SpanFromContext(ctx context.Context) trace.Span {
	return trace.SpanFromContext(ctx)
}

func AddSpanTags(span trace.Span, tags map[string]string) {
	list := make([]attribute.KeyValue, len(tags))

	var i int
	for k, v := range tags {
		list[i] = attribute.Key(k).String(v)
		i++
	}

	span.SetAttributes(list...)
}

func AddSpanEvents(span trace.Span, name string, events map[string]string) {
	list := make([]trace.EventOption, len(events))

	var i int
	for k, v := range events {
		list[i] = trace.WithAttributes(attribute.Key(k).String(v))
		i++
	}

	span.AddEvent(name, list...)
}

func GetTraceID(span trace.Span) string {
	return span.SpanContext().TraceID().String()
}

func GetSpanID(span trace.Span) string {
	return span.SpanContext().SpanID().String()
}

func IsTracerProviderSet() bool {
	tp := otel.GetTracerProvider()
	switch tp.(type) {
	case *sdktrace.TracerProvider:
		return true

	default:
		return false
	}
}

func IsSpanValid(span trace.Span) bool {
	ctx := span.SpanContext()
	return span.IsRecording() && ctx.HasTraceID() && ctx.HasSpanID()
}
