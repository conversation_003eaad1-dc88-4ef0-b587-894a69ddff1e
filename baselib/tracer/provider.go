package tracer

import (
	"context"
	"fmt"
	"log/slog"
	"os"

	"go.opentelemetry.io/otel"
	"go.opentelemetry.io/otel/exporters/otlp/otlptrace/otlptracegrpc"
	"go.opentelemetry.io/otel/propagation"
	"go.opentelemetry.io/otel/sdk/resource"
	sdktrace "go.opentelemetry.io/otel/sdk/trace"
	semconv "go.opentelemetry.io/otel/semconv/v1.10.0"
	"go.opentelemetry.io/otel/trace"
	"go.opentelemetry.io/otel/trace/noop"
	"google.golang.org/grpc/credentials"
)

type (
	ProviderConfig struct {
		Disabled       bool
		Endpoint       string
		ServiceName    string
		ServiceVersion string
		Environment    string
		IsCompression  bool
		Creds          credentials.TransportCredentials
		SampleRate     float64
	}

	Provider struct {
		provider trace.TracerProvider
	}
)

func NewProvider(ctx context.Context, cfg ProviderConfig) (*Provider, error) {
	if cfg.Disabled {
		return &Provider{provider: noop.NewTracerProvider()}, nil
	}

	// exporter
	opts := []otlptracegrpc.Option{
		otlptracegrpc.WithEndpoint(cfg.Endpoint),
	}

	if cfg.IsCompression {
		opts = append(opts, otlptracegrpc.WithCompressor("gzip"))
	}

	if cfg.Creds != nil {
		opts = append(opts, otlptracegrpc.WithTLSCredentials(cfg.Creds))
	} else {
		opts = append(opts, otlptracegrpc.WithInsecure())
	}

	exp, err := otlptracegrpc.New(ctx, opts...)
	if err != nil {
		return nil, fmt.Errorf("failed to new OTLP exporter: %w", err)
	}

	// resource
	hostName, _ := os.Hostname()
	rsc, err := resource.New(
		ctx,
		resource.WithFromEnv(),
		resource.WithProcess(),
		resource.WithTelemetrySDK(),
		resource.WithHost(),
		resource.WithAttributes(
			semconv.ServiceNameKey.String(cfg.ServiceName),
			semconv.ServiceVersionKey.String(cfg.ServiceVersion),
			semconv.DeploymentEnvironmentKey.String(cfg.Environment),
			semconv.HostNameKey.String(hostName),
		),
	)
	if err != nil {
		return nil, fmt.Errorf("failed to new resource: %w", err)
	}
	var sampler sdktrace.Sampler
	if cfg.SampleRate > 0 && cfg.SampleRate < 1 {
		sampler = sdktrace.ParentBased(sdktrace.TraceIDRatioBased(cfg.SampleRate))
		slog.Info("env:", cfg.Environment, "using trace sampler:", cfg.SampleRate)
	} else {
		sampler = sdktrace.AlwaysSample()
		slog.Info("env:", cfg.Environment, "using trace sampler always")
	}
	prv := sdktrace.NewTracerProvider(
		sdktrace.WithSampler(sampler),
		sdktrace.WithBatcher(exp),
		sdktrace.WithResource(rsc),
	)

	otel.SetTracerProvider(prv)
	otel.SetTextMapPropagator(propagation.NewCompositeTextMapPropagator(
		propagation.TraceContext{},
		propagation.Baggage{},
	))

	return &Provider{provider: prv}, nil
}

func (p *Provider) Close(ctx context.Context) error {
	if prv, ok := p.provider.(*sdktrace.TracerProvider); ok {
		if err := prv.Shutdown(ctx); err != nil {
			return fmt.Errorf("failed to shutdown trace provider: %w", err)
		}
	}

	return nil
}
