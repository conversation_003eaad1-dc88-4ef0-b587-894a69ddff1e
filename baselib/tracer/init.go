package tracer

import (
	"context"

	"new-gitlab.xunlei.cn/vcproject/backends/baselib/config"
	"new-gitlab.xunlei.cn/vcproject/backends/baselib/logger"
	"new-gitlab.xunlei.cn/vcproject/backends/baselib/server/env"
)

func initTracer(ctx context.Context, config *config.JaegerConfig) (provider *Provider, err error) {
	var serviceName string
	if config.ServiceName != "" {
		serviceName = config.ServiceName
	} else {
		serviceName = env.GetServiceName()
	}
	provider, err = NewProvider(ctx, ProviderConfig{
		Endpoint:       config.Endpoint, // Jaeger / 阿里云 的 OTEL 协议的 gRPC 端口
		ServiceName:    serviceName,
		ServiceVersion: "v0.0.1",
		Environment:    string(env.GetEnvironment()),
		SampleRate:     config.SampleRate,
	})
	if err != nil {
		logger.Errorf("Failed to create trace provider: %v", err)
	}
	return
}

func InitTracer(ctx context.Context, config *config.JaegerConfig) (provider *Provider, err error) {
	return initTracer(ctx, config)
}

func InitWithCloser(ctx context.Context, config *config.JaegerConfig) (close func(), _ error) {
	if config != nil && config.Enable {
		provider, err := initTracer(ctx, config)
		if err != nil {
			logger.Errorf("Failed to init tracer: %v", err)
			return
		}
		return func() {
			provider.Close(ctx)
		}, nil
	}
	return func() {}, nil
}
