package cache

import (
	"strings"

	"github.com/prometheus/client_golang/prometheus"
	"github.com/redis/go-redis/v9"
	"go.uber.org/zap"
	"new-gitlab.xunlei.cn/vcproject/backends/baselib/cache/redis_hook"
	"new-gitlab.xunlei.cn/vcproject/backends/baselib/cache/rediscollector"
	"new-gitlab.xunlei.cn/vcproject/backends/baselib/config"
	"new-gitlab.xunlei.cn/vcproject/backends/baselib/logger"
	"new-gitlab.xunlei.cn/vcproject/backends/baselib/metric"
	"new-gitlab.xunlei.cn/vcproject/backends/baselib/server/env"
)

var (
	redisClients map[string]*redis.Client
)

func GetRedisClient(name string) *redis.Client {
	return redisClients[name]
}

func InitRedis(configs []*config.RedisConfig) {
	if len(configs) == 0 {
		logger.Warnf("no redis config")
		return
	}
	redisClients = make(map[string]*redis.Client)
	for _, config := range configs {
		if len(config.Name) == 0 {
			config.Name = "default"
		}
		addr := config.Addr
		if len(strings.Split(config.Addr, ":")) == 1 {
			addr = addr + ":6379"
		}
		client := redis.NewClient(&redis.Options{
			Addr:     addr,
			Password: config.Password,
			DB:       config.Db,
			PoolSize: 512,
		})
		client.AddHook(redis_hook.CollectorHook{
			Addr: addr,
			Name: config.Name,
		})

		if client != nil {
			redisClients[config.Name] = client
			if !env.IsLocal() {
				metric.MustRegister(
					rediscollector.NewDBStatsCollector(client,
						prometheus.Labels(metric.ConstLabel().
							With("name", config.Name).With("addr", addr),
						),
					),
				)
			}
			logger.Info("Redis连接成功", zap.Any("name", config.Name), zap.Any("addr", config.Addr))
		} else {
			logger.Panic("Redis连接异常", zap.Any("config", config))
			return
		}
	}
}
