package rediscollector

import (
	"github.com/prometheus/client_golang/prometheus"
	"github.com/redis/go-redis/v9"
)

type RedisPoolStats interface {
	PoolStats() *redis.PoolStats
}

type dbStatsCollector struct {
	db RedisPoolStats

	totalConns *prometheus.Desc
	idleConns  *prometheus.Desc
	staleConns *prometheus.Desc
}

// NewDBStatsCollector returns a collector that exports metrics about the given *sql.DB.
// See https://golang.org/pkg/database/sql/#DBStats for more information on stats.
func NewDBStatsCollector(db RedisPoolStats, labels prometheus.Labels) prometheus.Collector {
	fqName := func(name string) string {
		return "go_redis_" + name
	}
	return &dbStatsCollector{
		db: db,
		totalConns: prometheus.NewDesc(
			fqName("total_connections"),
			"number of total connections in the pool",
			nil,
			labels,
		),
		idleConns: prometheus.NewDesc(
			fqName("idle_connections"),
			"number of idle connections in the pool",
			nil,
			labels,
		),
		staleConns: prometheus.NewDesc(
			fqName("stale_connections"),
			"number of stale connections removed from the pool",
			nil,
			labels,
		),
	}
}

// Describe implements Collector.
func (c *dbStatsCollector) Describe(ch chan<- *prometheus.Desc) {
	ch <- c.totalConns
	ch <- c.idleConns
	ch <- c.staleConns
}

// Collect implements Collector.
func (c *dbStatsCollector) Collect(ch chan<- prometheus.Metric) {
	stats := c.db.PoolStats()
	ch <- prometheus.MustNewConstMetric(c.totalConns, prometheus.GaugeValue, float64(stats.TotalConns))
	ch <- prometheus.MustNewConstMetric(c.idleConns, prometheus.GaugeValue, float64(stats.IdleConns))
	ch <- prometheus.MustNewConstMetric(c.staleConns, prometheus.GaugeValue, float64(stats.StaleConns))
}
