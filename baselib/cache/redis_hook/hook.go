package redis_hook

import (
	"context"
	"fmt"
	"runtime"
	"strings"

	"github.com/redis/go-redis/v9"
	"new-gitlab.xunlei.cn/vcproject/backends/baselib/metric"
)

func getCallerFrame(skip int) (frame runtime.Frame, ok bool) {
	const skipOffset = 1 // skip getCallerFrame and Callers

	pc := make([]uintptr, 1)
	numFrames := runtime.Callers(skip+skipOffset, pc)
	if numFrames < 1 {
		return
	}

	frame, _ = runtime.CallersFrames(pc).Next()
	return frame, frame.PC != 0
}

var _ redis.Hook = new(CollectorHook)

type CollectorHook struct {
	Addr string
	Name string
}

func getShortFileName(name string) string {
	index := strings.Index(name, "backends")
	if index == -1 {
		return name
	}
	return name[index+len("backends"):]
}

func (c CollectorHook) ProcessHook(next redis.ProcessHook) redis.ProcessHook {
	return func(ctx context.Context, cmd redis.Cmder) error {
		var s string
		f, ok := getCallerFrame(5)
		if !ok {
			s = "unknown"
		} else {
			s = fmt.Sprintf("%s:%d", getShortFileName(f.File), f.Line)
		}
		metric.CounterWithLabels("redis_call", map[string]string{
			"postion": s,
			"method":  cmd.Name(),
			"addr":    c.Addr,
			"name":    c.Name,
		}).Inc()
		return next(ctx, cmd)
	}
}

func (c CollectorHook) ProcessPipelineHook(next redis.ProcessPipelineHook) redis.ProcessPipelineHook {
	return func(ctx context.Context, cmds []redis.Cmder) error {
		var s string
		f, ok := getCallerFrame(7)
		if !ok {
			s = "unknown"
		} else {
			s = fmt.Sprintf("%s:%d", getShortFileName(f.File), f.Line)
		}
		metric.CounterWithLabels("redis_call", map[string]string{
			"postion": s,
			"method":  "pipeline",
			"addr":    c.Addr,
			"name":    c.Name,
		}).Inc()
		return next(ctx, cmds)
	}
}

func (c CollectorHook) DialHook(next redis.DialHook) redis.DialHook {
	return next
}
