package lock

import (
	"context"
	"fmt"
	"testing"
	"time"

	"github.com/redis/go-redis/v9"
	"new-gitlab.xunlei.cn/vcproject/backends/baselib/logger"
)

func Test_LockDoJob(t *testing.T) {
	logger.InitLogger("/dev/stdout")
	client := redis.NewClient(&redis.Options{
		Addr:     "r-wz9krkbev65z76g28n.redis.rds.aliyuncs.com:6379",
		Password: "F2dFjBk869ou",
	})
	err := client.Ping(context.Background()).Err()
	if err != nil {
		panic(err)
	}

	go NewLockDoJob(client, "lock_test", func() {
		logger.Infof("lock1")
	}, nil).Start()
	NewLockDoJob(client, "lock_test", func() {
		logger.Infof("lock2")
	}, nil).Start()
}

func Test_lockdo(t *testing.T) {
	logger.InitLogger("/dev/stdout")
	client := redis.NewClient(&redis.Options{
		Addr:     "r-wz9krkbev65z76g28n.redis.rds.aliyuncs.com:6379",
		Password: "F2dFjBk869ou",
	})
	err := client.Ping(context.Background()).Err()
	if err != nil {
		panic(err)
	}
	go LockDo(client, "lock_test", time.Second, func(input context.Context) error {
		select {
		case <-input.Done():
			logger.Infof("done %v", err)
			return fmt.Errorf("done")
		default:

		}
		logger.Infof("log")
		return nil
	})
	LockDo(client, "lock_test", time.Second, func(input context.Context) error {
		select {
		case <-input.Done():
			logger.Infof("done %v", err)
			return fmt.Errorf("done")
		default:

		}
		logger.Infof("log1")
		return nil
	})

}
