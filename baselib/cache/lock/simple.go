package lock

import (
	"context"
	"time"

	"github.com/redis/go-redis/v9"
	"new-gitlab.xunlei.cn/vcproject/backends/baselib/logger"
)

type SimpleLock struct {
	client *redis.Client
	ctx    context.Context
	key    string
	ttl    time.Duration
}

func NewSimpleLock(client *redis.Client, key string, ttl time.Duration) (lock *SimpleLock, err error) {
	ok, err := client.SetNX(context.Background(), key, "1", ttl).Result()
	if err != nil {
		return nil, err
	}
	if !ok {
		return nil, ErrNotObtained
	}
	return &SimpleLock{
		client: client,
		key:    key,
		ttl:    ttl,
	}, nil
}

func (c *SimpleLock) Release() (err error) {
	err = c.client.Del(context.Background(), c.key).Err()
	if err != nil {
		logger.Errorf("error %v", err)
	}
	return
}
