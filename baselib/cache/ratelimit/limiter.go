package ratelimit

import (
	"context"
	"fmt"
	"time"

	"github.com/go-redis/redis_rate/v10"
	"github.com/redis/go-redis/v9"
)

type RateLimiterInterface interface {
	Allow(ctx context.Context, key string, limit int, window time.Duration) (bool, int, error)
}

type RateLimiter struct {
	limiter *redis_rate.Limiter
}

func NewRateLimiter(redisClient *redis.Client) RateLimiterInterface {
	return &RateLimiter{
		limiter: redis_rate.NewLimiter(redisClient),
	}
}

// Allow 使用 go-redis/redis_rate 实现分布式限流
// key: 频控key，limit:次数上限，window:窗口时长
// 返回(是否允许, 当前计数, 错误)
func (r *RateLimiter) Allow(ctx context.Context, key string, limit int, window time.Duration) (bool, int, error) {
	redisKey := fmt.Sprintf("ratelimit:%s", key)
	res, err := r.limiter.Allow(ctx, redisKey, redis_rate.Limit{
		Period: window,
		Rate:   limit,
		Burst:  limit, // 一般与Rate一致即可
	})
	if err != nil {
		return false, 0, err
	}
	return res.Allowed > 0, int(res.Remaining), nil
}
