package xlaccount

import (
	"context"
	"encoding/base64"
	"encoding/json"
	"errors"
	"strings"

	"google.golang.org/grpc/metadata"
	"new-gitlab.xunlei.cn/vcproject/backends/baselib/errcode"
	"new-gitlab.xunlei.cn/vcproject/backends/proto"
	"new-gitlab.xunlei.cn/vcproject/backends/proto/api/common"
	"new-gitlab.xunlei.cn/vcproject/backends/proto/consts"
)

const (
	HeaderAuthKey          = "Authorization"
	HeaderUnderageTokenKey = "UnderageToken"
	HeaderIPKey            = "x-forwarded-for"
	BearerType             = "Bearer"
	BasicType              = "Basic"

	TestJwtSecret = "7NkWJGrwiRCQtwhgL2iJRsGIw_FIywJkbq9I7i94wrQ"
	ProdJwtSecret = "slkjf%#ftu56*^##dfGLG6997#$%BCX++**#@NMNN"
)

var (
	notRequiredSignupMethods = []string{
		"/vc.bizaccount.s/Test",
		"/vc.bizaccount.s/AccountSignUp",
		"/vc.bizaccount.s/GetVerificationCode",
	}
	logoutMethod   = "/vc.bizaccount.s/AccountSignOut"
	underageMethod = "/vc.bizaccount.s/AccountUnderage"
	weakAuthMethod = map[string]bool{
		"/vc.bizaccount.s/Test":                           true,
		"/vc.bizaccount.s/GetVerificationCode":            true,
		"/vc.bizaccount.s/AccountSignUp":                  true,
		"/vc.bizaccount.s/AccountSignIn":                  true,
		"/vc.bizaccount.s/RefreshToken":                   true,
		"/vc.bizaccount.s/GetUserInfo":                    true,
		"/vc.bizmisc.s/GetAliObjectId":                    true,
		"/vc.bizmisc.s/GenerateAliOssSignature":           true,
		"/vc.bizmoment.s/GetMomentSquare":                 true,
		"/vc.bizmoment.s/GetMomentsByIds":                 true,
		"/vc.bizscript.s/GetTopicList":                    true,
		"/vc.bizscript.s/GetScriptList":                   true,
		"/vc.bizscript.s/GetScriptFirstDubbing":           true,
		"/vc.bizscript.s/GetLineDubbingSimple":            true,
		"/vc.bizscript.s/GetLineDubbingAll":               true,
		"/vc.bizscript.s/Search":                          true,
		"/vc.bizscript.s/GetCommentList":                  true,
		"/vc.bizscript.s/GetCommentReplies":               true,
		"/vc.bizscript.s/GetReportReasons":                true,
		"/vc.bizscript.s/GetScriptDetail":                 true,
		"/vc.bizscript.s/BatchGetScriptFirstDubbing":      true,
		"/vc.bizscript.s/BatchCheckCharacterAvailability": true,
		"/vc.bizscript.s/GetUserScriptLists":              true,
		"/vc.bizscript.s/GetUserStats":                    true,
		"/vc.bizmisc.s/CheckVersion":                      true,
		"/vc.bizstat.s/PushEvent":                         true,
		"/vc.bizmisc.s/GetOfficialApkUrl":                 true,
	}
)

type tokenInfo struct {
	Issuer string `json:"iss"`
}

var xlAuth = &struct {
}{}

func IsRequiredSignup(name string) bool {
	for _, item := range notRequiredSignupMethods {
		if item == name {
			return false
		}
	}
	return true
}

func IsWeakAuthorize(name string) bool {
	return weakAuthMethod[name]
}

func IsLogoutRequest(name string) bool {
	if name == logoutMethod {
		return true
	}
	return false
}

func SetAuthInfo(ctx context.Context, authInfo *proto.AuthInfo) context.Context {
	return context.WithValue(ctx, proto.AuthInfo{}, authInfo)
}

func GetAuthInfo(ctx context.Context) (authInfo *proto.AuthInfo) {
	authInfo, ok := ctx.Value(proto.AuthInfo{}).(*proto.AuthInfo)
	if !ok {
		authInfo = &proto.AuthInfo{}
	}
	return
}

//func CheckAuthToken(ctx context.Context) (authInfo *proto.AuthInfo, err error) {
//	md := getMdMap(ctx)
//	var authToken string
//	authToken = md[HeaderAuthKey]
//	if authToken == "" {
//		authToken = md[strings.ToLower(HeaderAuthKey)]
//		if authToken == "" {
//			err = errors.New("authorization is required")
//			logger.Errorf("error %v", err)
//			return
//		}
//	}
//	sp := strings.Split(authToken, " ")
//	if len(sp) < 2 {
//		err = errors.New("invalid authorization")
//		logger.Errorf("error %v authToken=%v", err, authToken)
//		return
//	}
//
//	authInfo = &proto.AuthInfo{
//		Type:  strings.TrimSpace(sp[0]), //Bearer
//		Token: strings.TrimSpace(sp[1]), //Token
//		Ip:    getIPFromMD(md),
//	}
//
//	switch authInfo.Type {
//	case BearerType:
//		var claim = make(jwt.MapClaims)
//		claim, err = verifyToken(authInfo.Token)
//		if err != nil {
//			logger.Errorf("error %v", err)
//			return
//		}
//		if userIdStr, ok := claim["user_id"]; ok {
//			if userId, ok := userIdStr.(float64); ok {
//				authInfo.UserId = int64(userId)
//			}
//		}
//
//		authInfo.Issuer = consts.AccessModePhone
//		if issuer, ok := claim["iss"]; ok {
//			iss := issuer.(string)
//			if strings.Contains(iss, "apple") {
//				authInfo.Issuer = consts.AccessModeApple
//			}
//		}
//		if email, ok := claim["email"]; ok {
//			authInfo.Email = email.(string)
//		}
//		return
//	default:
//		err = errors.New("invalid authorization")
//		logger.Errorf("error %v", err)
//		return
//	}
//}

func GetMdMap(ctx context.Context) map[string]string {
	return getMdMap(ctx)
}

func GetToken(mds map[string]string) string {
	if token := mds[HeaderAuthKey]; token != "" {
		return token
	}
	if token := mds[strings.ToLower(HeaderAuthKey)]; token != "" {
		return token
	}
	return ""
}

func getMdMap(ctx context.Context) map[string]string {
	mds := make(map[string]string)
	md, ok := metadata.FromIncomingContext(ctx)
	if !ok {
		return mds
	}
	for k, v := range md {
		if len(v) > 0 {
			//mds[strings.ToLower(k)] = v[0]
			mds[k] = v[0]
		}
	}
	return mds
}

func GetIpFromCtx(ctx context.Context) string {
	mds := getMdMap(ctx)
	return getIPFromMD(mds)
}

func getIPFromMD(mds map[string]string) string {
	if ip := mds[HeaderIPKey]; ip != "" {
		parts := strings.Split(ip, ",")
		if len(parts) > 0 {
			return parts[0]
		}
	}
	return ""
}

type CheckAccountResult struct {
	BaseResp      *common.SvcBaseResp
	UserId        int64
	Mid           int64
	Gender        int32
	Status        consts.UserStatus
	Underage      bool
	UnderageToken string
	Channel       string
	RegTime       int64
}

func (r *CheckAccountResult) ErrorResp() (bool, *common.BizBaseResp) {
	if r.BaseResp == nil || errcode.ErrOK.Code == r.BaseResp.Code {
		return true, nil
	}
	return false, &common.BizBaseResp{
		Code: r.BaseResp.Code,
		Msg:  r.BaseResp.Msg,
	}
}

func (r *CheckAccountResult) IsAccountNull() bool {
	return r.UserId == 0
}

func (r *CheckAccountResult) IsAccountCanceled() bool {
	return r.Status == consts.UserStatusCancel
}

func (r *CheckAccountResult) IsAccountBlocked() bool {
	return r.Status == consts.UserStatusBlock
}

func (r *CheckAccountResult) IsUnderage(name string) bool {
	if r.Underage && name != underageMethod {
		return true
	}
	return false
}

type BaseParamRequest interface {
	GetBase() *common.BaseParam
}

func CheckAccount(ctx context.Context, fullMethod string, baseParam *common.BaseParam, authInfo *proto.AuthInfo) (result *CheckAccountResult, err error) {
	if authInfo == nil || authInfo.UserId == 0 {
		err = errors.New("authInfo is nil")
		return
	}
	// params := &svcaccount.AccountCheckReq{
	// 	Xluid: authInfo.Xluid,
	// 	Token: &svcaccount.AuthToken{
	// 		St:   authInfo.Expiry,
	// 		Et:   authInfo.IssuedAt,
	// 		Hash: authInfo.Token,
	// 	},
	// 	UserId: 0,
	// 	Base:   baseParam,
	// 	Method: fullMethod,
	// }
	// resp, err := svcmgr.AccountClient().AccountCheck(ctx, params)
	// if err != nil {
	// 	logger.Warnf("AccountCheck error: %v", err)
	// 	return
	// }

	result = &CheckAccountResult{
		BaseResp: &common.SvcBaseResp{
			Code: 0,
			Msg:  "success",
		},
		UserId: authInfo.UserId,
		Gender: authInfo.Gender,
		// UserId:        resp.UserId,
		// Gender:           resp.Gender,
		// Status:        enums.UserStatus(resp.Status),
		// Underage:      resp.Underage,
		// UnderageToken: resp.UnderageToken,
		// Channel:       resp.Channel,
		// RegTime:       resp.RegTime,
	}
	return
}

func GetIssuerFromBearToken(token string) (string, error) {
	sp := strings.Split(token, ".")
	if len(sp) != 3 {
		return "", errors.New("invalid token length")
	}

	bs, err := base64.RawURLEncoding.DecodeString(sp[1])
	if err != nil {
		return "", errors.New("invalid token data")
	}

	var info tokenInfo
	if err = json.Unmarshal(bs, &info); err != nil {
		return "", errors.New("invalid token data")
	}

	return strings.TrimRight(info.Issuer, "/"), nil
}

func remoteDeleteUser(xluid string) (err error) {
	return
}

func DeleteUser(xluid string) (err error) {
	// if env.IsProd() {
	return remoteDeleteUser(xluid)
	// }
	// _, err = svcmgr.BaseUserClient().MgrDeleteUser(context.Background(), &pbcommon.CommonId{Id: xluid})
	// if err != nil {
	// 	logger.Errorf("DeleteUser xluid %v err %v", xluid, err)
	// }

	//return
}

//func verifyToken(tkStr string) (map[string]interface{}, error) {
//	jwtSecret := ""
//	if env.IsProd() {
//		jwtSecret = ProdJwtSecret
//	} else {
//		jwtSecret = TestJwtSecret
//	}
//	token, err := jwt.Parse(tkStr, func(token *jwt.Token) (interface{}, error) {
//		return []byte(jwtSecret), nil
//	})
//	if err != nil {
//		logger.Errorf("verifyToken err %v jwtSecret=%s env=%s tkStr=%s token=%+v", err, jwtSecret, env.GetEnvironment(), tkStr, util.JsonStr(token))
//		var vErr *jwt.ValidationError
//		if errors.As(err, &vErr) {
//			if vErr.Errors == jwt.ValidationErrorExpired {
//				return nil, err
//			}
//		}
//		return nil, err
//	}
//
//	if !token.Valid {
//		return nil, err
//	}
//
//	return token.Claims.(jwt.MapClaims), nil
//
//}
