package xlaccount

import (
	"context"
	"crypto/sha256"
	"errors"
	"fmt"
	"strings"
	"time"

	"new-gitlab.xunlei.cn/vcproject/backends/baselib/server/interceptor/bizcontext"
	"new-gitlab.xunlei.cn/vcproject/backends/proto"

	"github.com/golang-jwt/jwt/v4"
	"new-gitlab.xunlei.cn/vcproject/backends/baselib/errcode"
	"new-gitlab.xunlei.cn/vcproject/backends/baselib/logger"
	"new-gitlab.xunlei.cn/vcproject/backends/baselib/server/env"
	"new-gitlab.xunlei.cn/vcproject/backends/proto/api/svcaccount"
	"new-gitlab.xunlei.cn/vcproject/backends/proto/consts"
)

// JWTClaims 与 account 服务保持一致
type JWTClaims struct {
	UserId      int64            `json:"user_id"`
	DeviceId    string           `json:"device_id"`
	TokenType   consts.TokenType `json:"token_type"`
	Platform    int32            `json:"platform"`
	Fingerprint string           `json:"fingerprint"`
	jwt.RegisteredClaims
}

// TokenConfig 配置与 account 服务保持一致
type TokenConfig struct {
	jwtSecret       string
	AccessTokenTTL  time.Duration
	RefreshTokenTTL time.Duration
	ClockSkew       time.Duration
	MaxRefreshTime  time.Duration
}

var defaultConfig = TokenConfig{
	AccessTokenTTL:  consts.AccessTokenExpire,
	RefreshTokenTTL: consts.RefreshTokenExpire,
	ClockSkew:       consts.ClockSkew,
	MaxRefreshTime:  consts.MaxRefreshTime,
}

// TokenValidator 定义token验证器接口
type TokenValidator interface {
	VerifyToken(ctx context.Context, req *svcaccount.VerifyTokenReq) (*svcaccount.VerifyTokenResp, error)
}

var (
	// 全局token验证器，通过依赖注入设置
	globalTokenValidator TokenValidator
)

// SetTokenValidator 设置全局token验证器（依赖注入）
func SetTokenValidator(validator TokenValidator) {
	globalTokenValidator = validator
}

// GetTokenValidator 获取全局token验证器
func GetTokenValidator() TokenValidator {
	return globalTokenValidator
}

func init() {
	if env.IsProd() {
		defaultConfig.jwtSecret = ProdJwtSecret
	} else {
		defaultConfig.jwtSecret = TestJwtSecret
	}
}

func generateDeviceFingerprint(deviceId string, platform int32) string {
	if deviceId == "" {
		return ""
	}

	// 组合多个因素生成更安全的设备指纹
	h := sha256.New()
	h.Write([]byte(fmt.Sprintf("%s_%d_%s", deviceId, platform, defaultConfig.jwtSecret)))
	return fmt.Sprintf("%x", h.Sum(nil))
}

func verifyToken(ctx context.Context, tokenString string) (*JWTClaims, error) {
	if tokenString == "" {
		return nil, errcode.ErrTokenRequired
	}

	token, err := jwt.ParseWithClaims(tokenString, &JWTClaims{}, func(token *jwt.Token) (interface{}, error) {
		if _, ok := token.Method.(*jwt.SigningMethodHMAC); !ok {
			return nil, errcode.ErrInvalidSigningMethod.WithMessage(fmt.Sprintf("unexpected signing method: %v", token.Header["alg"]))
		}
		return []byte(defaultConfig.jwtSecret), nil
	})

	if err != nil {
		var ve *jwt.ValidationError
		if errors.As(err, &ve) {
			if ve.Errors&jwt.ValidationErrorExpired != 0 {
				return nil, errcode.ErrTokenExpired
			}
		}
		return nil, errcode.ErrInvalidToken.WithMessage(err.Error())
	}

	claims, ok := token.Claims.(*JWTClaims)
	if !ok || !token.Valid {
		return nil, errcode.ErrInvalidToken
	}

	// 验证设备指纹
	currentFingerprint := generateDeviceFingerprint(claims.DeviceId, claims.Platform)
	if claims.Fingerprint != currentFingerprint {
		logger.Warnf("device mismatch detected: user_id=%d, device_id=%s", claims.UserId, claims.DeviceId)
		return nil, errcode.ErrDeviceFingerprint
	}

	return claims, nil
}

// CheckAuthTokenWithValidator 使用注入的验证器进行token验证
func CheckAuthTokenWithValidator(ctx context.Context) (*proto.AuthInfo, error) {
	validator := GetTokenValidator()
	if validator == nil {
		logger.Warn("no token validator set, fallback to local verification")
		// 没有验证器时，降级到本地验证
		return CheckAuthToken(ctx)
	}

	md := getMdMap(ctx)
	authToken := GetToken(md)
	if authToken == "" {
		return nil, errcode.ErrTokenRequired
	}

	sp := strings.Split(authToken, " ")
	if len(sp) != 2 || sp[0] != BearerType {
		return nil, errcode.ErrInvalidToken
	}

	// 获取请求上下文信息
	base := bizcontext.GetBaseContext(ctx)
	var deviceId, userAgent string
	var platform int32

	if base != nil {
		deviceId = base.GetDid()
		userAgent = base.GetUa()
		platform = base.GetDt()
	}

	// 调用注入的验证器进行完整验证（包括黑名单检查）
	resp, err := validator.VerifyToken(ctx, &svcaccount.VerifyTokenReq{
		Token:     sp[1],
		DeviceId:  deviceId,
		Platform:  platform,
		UserAgent: userAgent,
	})

	if err != nil {
		logger.Warnf("token validator failed: %v, fallback to local verification", err)
		// 验证器调用失败时的降级策略：使用本地验证
		return CheckAuthToken(ctx)
	}

	// 检查验证结果
	if resp.Data == nil || !resp.Data.IsValid {
		reason := "token validation failed"
		if resp.Data != nil && resp.Data.Reason != "" {
			reason = resp.Data.Reason
		}
		return nil, errcode.ErrTokenInvalid.WithMessage(reason)
	}

	// 验证成功，返回AuthInfo
	return &proto.AuthInfo{
		Type:     BearerType,
		Token:    sp[1],
		UserId:   resp.Data.UserId,
		DeviceId: resp.Data.DeviceId,
		Platform: resp.Data.Platform,
	}, nil
}

// CheckAuthToken 对外提供的验证接口
func CheckAuthToken(ctx context.Context) (*proto.AuthInfo, error) {
	md := getMdMap(ctx)
	authToken := GetToken(md)
	if authToken == "" {
		return nil, errcode.ErrTokenRequired
	}

	sp := strings.Split(authToken, " ")
	if len(sp) != 2 || sp[0] != BearerType {
		return nil, errcode.ErrInvalidToken
	}

	// 1. 验证token
	claims, err := verifyToken(ctx, sp[1])
	if err != nil {
		return nil, err
	}

	// 2. 验证当前请求的设备ID（如果需要）
	base := bizcontext.GetBaseContext(ctx)
	if base.GetDid() != "" && claims.DeviceId != base.GetDid() {
		logger.Warnf("device mismatch in interceptor: token_device=%s, request_device=%s, user_id=%d",
			claims.DeviceId, base.GetDid(), claims.UserId)
		return nil, errcode.ErrDeviceMismatch
	}

	// 3. 返回验证信息
	return &proto.AuthInfo{
		Type:     BearerType,
		Token:    sp[1],
		UserId:   claims.UserId,
		DeviceId: claims.DeviceId,
		Platform: claims.Platform,
	}, nil
}
