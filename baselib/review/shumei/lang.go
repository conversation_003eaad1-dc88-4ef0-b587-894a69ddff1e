package shumei

import (
	"fmt"
	"new-gitlab.xunlei.cn/vcproject/backends/baselib/server/env"
	"strings"
)

// 国家对应数美语言
var countryLang = map[string]string{
	"CN": "zh", // 中文
	"ID": "id", // 印尼语
	"US": "en", // 英文
	"BR": "pt", // 葡萄牙语
}

// eventID 前缀
var countryPrefix = map[string]string{
	"CN": "id", // 中文 配置也用默认的印尼
	"ID": "id", // 印尼国家
	"BR": "pt", // 巴西
}

var defaultPrefix = "id" // 目前先以印尼国家为默认值

func getEventIdPrefixAndLang(country string) (prefix string, lang string) {
	cc := strings.ToUpper(country)
	if v, ok := countryLang[cc]; ok {
		lang = v
	}
	if v, ok := countryPrefix[cc]; ok {
		prefix = v
	}
	if len(prefix) == 0 {
		prefix = defaultPrefix
	}
	return
}

func GetMultEventIdAndLang(country string, eventId string) (string, string) {
	prefix, lang := getEventIdPrefixAndLang(country)
	return fmt.Sprintf("%s_%s", prefix, eventId), lang
}

func imageSupportLang(lang string) string {
	switch lang {
	case "zh", //中文
		"en", //英文
		"ar": //阿拉伯语
		return lang
	default:
		return "en"
	}
}

func textSupportLang(lang string) string {
	switch lang {
	case "zh", //zh：中文
		"en", // en：英文
		"ar", // ar：阿拉伯语
		"hi", // hi：印地语
		"es", // es：西班牙语
		"fr", // fr：法语
		"ru", // ru：俄语
		"pt", // pt：葡萄牙语
		"id", // id：印尼语
		"de", // de：德语
		"ja", // ja：日语
		"tr", // tr：土耳其语
		"vi", // vi：越南语
		"it", // it：意大利语
		"th", // th：泰语
		"tl", // tl：菲律宾语
		"ko", // ko：韩语
		"ms": // ms：马来语
		return lang
	default:
		return "auto" //auto：自动识别语种类型
	}
}

func audioSupportLang(lang string) string {
	switch lang {
	case "zh", //zh：中文
		"en", // en：英文
		"ar", // ar：阿拉伯语
		"hi", // hi：印地语
		"es", // es：西班牙语
		"fr", // fr：法语
		"ru", // ru：俄语
		"pt", // pt：葡萄牙语
		"id", // id：印尼语
		"de", // de：德语
		"ja", // ja：日语
		"tr", // tr：土耳其语
		"vi", // vi：越南语
		"it", // it：意大利语
		"th", // th：泰语
		"tl", // tl：菲律宾语
		"ko", // ko：韩语
		"ms": // ms：马来语
		return lang
	default:
		if env.IsProd() {
			return "id" // 找不到印尼
		} else {
			return "zh"
		}

	}
}

func audioStreamSupportLang(lang string) string {
	switch lang {
	case "zh", //zh：中文
		"en", // en：英文
		"ar", // ar：阿拉伯语
		"hi", // hi：印地语
		"es", // es：西班牙语
		"fr", // fr：法语
		"ru", // ru：俄语
		"pt", // pt：葡萄牙语
		"id", // id：印尼语
		"de", // de：德语
		"ja", // ja：日语
		"tr", // tr：土耳其语
		"vi", // vi：越南语
		"it", // it：意大利语
		"th", // th：泰语
		"tl", // tl：菲律宾语
		"ko", // ko：韩语
		"ms": // ms：马来语
		return lang
	default:
		if env.IsProd() {
			return "id" // 找不到印尼
		} else {
			return "zh"
		}
	}
}

func videoStreamSupportLang(lang string) string {
	switch lang {
	case "zh", //中文
		"en", //英文
		"ar": //阿拉伯语
		return lang
	default:
		if env.IsProd() {
			return "en"
		} else {
			return "zh"
		}

	}
}
