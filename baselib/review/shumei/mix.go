package shumei

import (
	"fmt"
	"new-gitlab.xunlei.cn/vcproject/backends/baselib/logger"
	"new-gitlab.xunlei.cn/vcproject/backends/baselib/review"
	"new-gitlab.xunlei.cn/vcproject/backends/proto/consts"
	"strings"
)

type MixCallbackData struct {
	Moid int64
}

type MixBody struct {
	BaseReq
	EventId       string           `json:"eventId"`
	Data          MixData          `json:"data"`
	Callback      string           `json:"callback,omitempty"`
	CallbackParam *MixCallbackData `json:"passThrough,omitempty"`
}

type MixData struct {
	BtId     string       `json:"btId"`
	TokenId  string       `json:"tokenId"`
	Contents []MixContent `json:"contents"`
}

type MixContent struct {
	DataType string `json:"dataType"` //"video","image","text"
	Content  string `json:"content"`  //检测数据类型是文本时传入原始文本数据，检测数据类型是其他时传入数据url
	BtId     string `json:"btId"`
	TxtType  string `json:"txtType"` //文本检测
	ImgType  string `json:"imgType"` //图片检测
	Lang     string `json:"lang"`
}

type MomentResultShumei struct {
	Code      int    `json:"code"`
	Message   string `json:"message"`
	RequestId string `json:"requestId"`
}

// 图片传参：
// 1 appId：default
// 2 eventId：
// umine_头像：id_avatar
// umine_相册：id_album
// umine_私聊：id_message
// umine_语音房封面：id_room_cover
// 3 type：POLITY_EROTIC_VIOLENT_IMGTEXTRISK_QRCODE_ADVERT
// 4 lang：按需传
// https://help.ishumei.com/docs/tj/media/documentV1/developDoc/#%E6%8E%A5%E5%8F%A3%E8%AF%B4%E6%98%8E

func MixScan(userid int64, texts, imgUrls []string, btId, bizType, country, lang string, callbackData *MixCallbackData) (result *MomentResultShumei, err error) {
	// if isClosedReview() {
	// 	return AuditResultShumei{
	// 		RiskLevel: RiskLevelPass,
	// 	}, nil
	// }
	if len(texts) == 0 && len(imgUrls) == 0 {
		err = review.ErrReviewContentEmpty
		return
	}

	eventId, lg := GetMultEventIdAndLang(country, EventIdMoment)
	params := MixBody{
		BaseReq:       GetBaseReq(),
		EventId:       eventId,
		Callback:      consts.GetApiUrl(mixCallbackUri),
		CallbackParam: callbackData,
	}

	if lg != "" {
		lang = lg
	}

	for i, text := range texts {
		if strings.TrimSpace(text) == "" {
			continue
		}
		params.Data.Contents = append(params.Data.Contents, MixContent{
			DataType: "text",
			Content:  text,
			TxtType:  "TEXTRISK",
			Lang:     textSupportLang("zh"),
			BtId:     fmt.Sprintf("%d-%s-text-%d", userid, btId, i),
		})
	}

	for i, url := range imgUrls {
		if strings.TrimSpace(url) == "" {
			continue
		}
		params.Data.Contents = append(params.Data.Contents, MixContent{
			DataType: "image",
			Content:  url,
			ImgType:  "POLITY_EROTIC_VIOLENT_IMGTEXTRISK_QRCODE_ADVERT",
			Lang:     imageSupportLang(lang),
			BtId:     fmt.Sprintf("%d-%s-image-%d", userid, btId, i),
		})
	}
	params.Data = MixData{
		BtId:     fmt.Sprintf("%d-%s", userid, btId),
		TokenId:  fmt.Sprintf("%d", userid),
		Contents: params.Data.Contents,
	}

	response := &MomentResultShumei{}
	err = auditClient.AuditCheck(review.ContentMix, 5, params, response, bizType)
	if err != nil {
		return
	}
	logger.Infof("MixScan AuditCheck req: %v,resp: %v,err:%v", params, response, err)
	return &MomentResultShumei{
		Code:      response.Code,
		Message:   response.Message,
		RequestId: response.RequestId,
	}, nil
}
