package shumei

import (
	"fmt"
	"strings"

	"new-gitlab.xunlei.cn/vcproject/backends/baselib/logger"
	"new-gitlab.xunlei.cn/vcproject/backends/baselib/review"
	"new-gitlab.xunlei.cn/vcproject/backends/proto/consts"
)

const (
	EventIdAudioMessage = "message" //私密语音聊天
	EventIdAudioSign    = "audio"   //语音签名
	EventIdDubbing      = "audio"   //智能音频
	EventIdAudioComment = "audio"   //智能音频
)

type AudioRiskType int

const (
	AudioRiskTypeNormal         AudioRiskType = 0   // 正常
	AudioRiskTypePolicy         AudioRiskType = 100 // 涉政
	AudioRiskTypeTerrorism      AudioRiskType = 110 // 暴恐
	AudioRiskTypeNationalAnthem AudioRiskType = 120 // 国歌
	AudioRiskTypePorn           AudioRiskType = 200 // ⾊情
	AudioRiskTypeAbuse          AudioRiskType = 210 // 辱骂
	AudioRiskTypeJiaochuan      AudioRiskType = 250 //娇喘
	AudioRiskTypeLeader         AudioRiskType = 260 //⼀号领导声纹
	AudioRiskTypeRenshengAttr   AudioRiskType = 270 //⼈声属性
	AudioRiskTypeForbidSong     AudioRiskType = 280 //违禁歌曲
	AudioRiskTypeAd             AudioRiskType = 300 //⼴告
	AudioRiskTypePour           AudioRiskType = 400 //灌⽔
	AudioRiskTypeNoMean         AudioRiskType = 500 //⽆意义
	AudioRiskTypeForbid         AudioRiskType = 600 //违禁
	AudioRiskTypeOther          AudioRiskType = 700 //其他
	AudioRiskTypeBlockAccount   AudioRiskType = 720 //⿊账号
	AudioRiskTypeBlockIp        AudioRiskType = 730 //⿊IP
	AudioRiskTypeDangerAccount  AudioRiskType = 800 //⾼危账号
	AudioRiskTypeDefine         AudioRiskType = 900 //⾃定义

)

type AudioBody struct {
	BaseReq
	Type          string    `json:"type"`
	BtId          string    `json:"btId"`
	Data          AudioData `json:"data"`
	Callback      string    `json:"callback,omitempty"`
	CallbackParam *Origin   `json:"callbackParam,omitempty"`
}

type AudioData struct {
	Url            string         `json:"url"`
	Lang           string         `json:"lang"`
	Content        string         `json:"content,omitempty"`
	FormatInfo     FormatInfoData `json:"formatInfo"`
	AudioName      string         `json:"audioName"`
	TokenId        string         `json:"tokenId"`
	Channel        string         `json:"channel"`
	ReturnAllText  bool           `json:"returnAllText"`
	Nickname       string         `json:"nickname"`
	Timestamp      int            `json:"timestamp"`
	Room           string         `json:"room"`
	ReceiveTokenId string         `json:"receiveTokenId"`
	Ip             string         `json:"ip"`
}

type CallbackParamData struct {
}

type FormatInfoData struct {
	Format string `json:"format"`
	Rate   int    `json:"rate"`
	Track  int    `json:"track"`
}

type RespAudio struct {
	Code      Code   `json:"code"`
	Message   string `json:"message"`
	RequestId string `json:"requestId"`
	BtId      string `json:"btId"`
}

type RespAudioCallback struct {
	Code          Code              `json:"code"`
	Message       string            `json:"message"`
	RequestId     string            `json:"requestId"`
	BtId          string            `json:"btId"`
	AudioText     string            `json:"audioText"`
	AudioTime     int               `json:"audioTime"`
	Labels        string            `json:"labels"`
	RiskLevel     RiskLevel         `json:"riskLevel"`
	Detail        []ImageDetailData `json:"detail"`
	Gender        GenderData        `json:"gender"`
	Tags          []TagData         `json:"tags"`
	CallbackParam Origin            `json:"callbackParam"`
}

type ImageDetailData struct {
	AudioStarttime   int           `json:"audioStarttime"`
	AudioEndtime     int           `json:"audioEndtime"`
	AudioUrl         string        `json:"audioUrl"`
	AudioText        string        `json:"audioText"`
	RiskLevel        RiskLevel     `json:"riskLevel"`
	RiskType         AudioRiskType `json:"riskType"`
	AudioMatchedItem string        `json:"audioMatchedItem"`
	Description      string        `json:"description"`
}

type GenderData struct {
	Label      string `json:"label"`
	Confidence int    `json:"confidence"`
}

type TagData struct {
	Label      string `json:"label"`
	Confidence int    `json:"confidence"`
}

// 自动查询结果 req
type AutoAudioResultReq struct {
	AccessKey string `json:"accessKey"`
	BtId      string `json:"btId"`
}

// 自动查询结果 resp
type AutoAudioResultResp struct {
	Code           Code                  `json:"code"`
	Message        string                `json:"message"`
	RequestId      string                `json:"requestId"`
	BtId           string                `json:"btId"`
	AudioText      string                `json:"audioText"`
	AudioTime      int                   `json:"audioTime"`
	Labels         string                `json:"labels"`
	RiskLevel      RiskLevel             `json:"riskLevel"`
	Detail         []AutoImageDetailData `json:"detail"`
	Gender         GenderData            `json:"gender"`
	Language       []LanguageData        `json:"language"`
	IsSing         int                   `json:"isSing"`
	Tags           []TagData             `json:"tags"`
	BusinessLabels []BusinessLabelData   `json:"businessLabels"`
}

type AutoImageDetailData struct {
	RequestId string `json:"requestId"`
	ImageDetailData
}

type LanguageData struct {
	Label      int `json:"label"`
	Confidence int `json:"confidence"`
}

type BusinessLabelData struct {
	BusinessLabel1      string `json:"businessLabel1"`
	BusinessLabel2      string `json:"businessLabel2"`
	BusinessLabel3      string `json:"businessLabel3"`
	BusinessDescription string `json:"businessDescription"`
}

func GetAudioChannelAndType(bizType string, sex int32, level int32) (channel, typ string, err error) {
	typ = "POLITY_EROTIC_ADVERT_BAN_VIOLENT_DIRTY_MOAN_ANTHEN_AUDIOPOLITICAL_BANEDAUDIO"
	switch bizType {
	case review.BizTypeChatAudio:
		channel = EventIdAudioMessage
	case review.BizTypeSignVoice:
		channel = EventIdAudioSign
	case review.BizTypeScriptDubbingAudio:
		channel = EventIdDubbing
	case review.BizTypeScriptCommentAudio:
		channel = EventIdAudioComment
	default:
		err = review.ErrReviewTypeBad
		return
	}
	return
}

// 音频文件传参：
// 1 appId：
// 默认应用：default
// 2 eventId：
// umine_私聊：id_message
// umine_签名：id_sign
// 3 type：POLITY_EROTIC_ADVERT_BAN_VIOLENT_DIRTY_MOAN
// 4 lang：按需传，英语传en，印尼语传id
// https://help.ishumei.com/docs/tj/audio/newest/developDoc/

func AudioScan(userid int64, peerid int64, sex int32, level int32, btId, audioUrl, bizType, ip string, origin *Origin) (result AuditResultShumei, err error) {
	if isClosedReview() {
		return AuditResultShumei{
			RiskLevel: RiskLevelPass,
		}, nil
	}

	if strings.TrimSpace(audioUrl) == "" || len(btId) == 0 {
		err = review.ErrReviewContentEmpty
		return
	}
	if origin != nil {
		origin.BizType = bizType
	}

	channel, typ, err := GetAudioChannelAndType(bizType, sex, level)
	if err != nil {
		return
	}
	params := AudioBody{
		BaseReq:       GetBaseReq(),
		BtId:          btId,
		Type:          typ,
		Callback:      consts.GetApiUrl(callbackUri),
		CallbackParam: origin,
	}
	params.Data = AudioData{
		TokenId:        GetTokenId(userid),
		Url:            audioUrl,
		Lang:           audioSupportLang("zh"),
		Channel:        channel,
		ReceiveTokenId: GetTokenId(peerid),
		Ip:             ip,
	}

	response := &RespAudio{}
	err = auditClient.AuditCheck(review.ContentVoice, 3, params, response, bizType)
	if err != nil {
		return
	}
	if response.Code != CodeSuccess {
		err = fmt.Errorf("code:%v,message:%v,requestId:%v", response.Code, response.Message, response.RequestId)
		logger.Errorf("shumei audio err:%v", err)

		// 如果是重复提交错误(1902)，只记录日志，不返回错误，避免影响业务流程
		if response.Code == CodeParamError && strings.Contains(response.Message, "duplicate submit") {
			logger.Warnf("shumei audio duplicate submit, ignore error: code:%v, message:%v, requestId:%v",
				response.Code, response.Message, response.RequestId)
			// 返回审核中状态，让业务正常流转
			return AuditResultShumei{
				RiskLevel: RiskLevelReview,
				RequestId: response.RequestId,
				BtId:      btId,
			}, nil
		}
		return
	}

	return AuditResultShumei{
		RiskLevel: RiskLevelReview,
		RequestId: response.RequestId,
		BtId:      response.BtId,
	}, nil
}

func AutoAudioResult(btId string) (response *AutoAudioResultResp, err error) {
	if len(btId) == 0 {
		err = review.ErrReviewContentEmpty
		return
	}
	params := AutoAudioResultReq{
		AccessKey: accessKey,
		BtId:      btId,
	}

	response = &AutoAudioResultResp{}
	err = auditClient.AuditCheck(review.ContentVoiceResult, 1, params, response, "auto_audio_result")
	if err != nil {
		return
	}

	return
}
