package shumei

import (
	"fmt"

	"new-gitlab.xunlei.cn/vcproject/backends/baselib/logger"
	"new-gitlab.xunlei.cn/vcproject/backends/baselib/review"
)

const (
	TextEventIdNickName      = "NICKNAME"     // 昵称
	TextEventIdIm            = "message"      // 私聊
	TextEventIdTextSign      = "profile"      // 个性签名
	TextEventIdRoomChat      = "room_message" // 房间公屏消息
	TextEventIdRoomTitle     = "room_title"   // 语音房房间title
	TextEventIdRoomNotice    = "room_notice"  // 语音房房间公告
	TextEventIdMoment        = "text"         // 朋友圈内容
	TextEventIdScriptTitle   = "TITLE"        // 剧本标题
	TextEventIdScriptLine    = "text"         // 剧本台词
	TextEventIdScriptComment = "comment"      // 剧本评论
	TextEventIdScriptTopic   = "text"         // 剧本话题
)

// 请求 body
type TextBody struct {
	BaseReq
	Type    string   `json:"type"`
	EventId string   `json:"eventId"`
	Data    TextData `json:"data"`
}

type TextData struct {
	Text     string        `json:"text"`
	TokenId  string        `json:"tokenId"`
	Nickname string        `json:"nickname"`
	Ip       string        `json:"ip,omitempty"`
	DeviceId string        `json:"deviceId"`
	Extra    TextExtraData `json:"extra"`
	Lang     string        `json:"lang"`
}

// resp body
type RespText struct {
	Code               Code                     `json:"code"`
	Message            string                   `json:"message"`
	RequestId          string                   `json:"requestId"`
	RiskLevel          RiskLevel                `json:"riskLevel"`
	RiskLabel1         string                   `json:"riskLabel1"`
	RiskLabel2         string                   `json:"riskLabel2"`
	RiskLabel3         string                   `json:"riskLabel3"`
	RiskDescription    string                   `json:"riskDescription"`
	RiskDetail         TextRiskDetailData       `json:"riskDetail"`
	Auxlnfo            TextAuxlnfoData          `json:"auxlnfo"`
	TokenLabels        TextTokenLabelsData      `json:"tokenLabels"`
	AllLabels          []TextAllLabelsData      `json:"allLabels"`
	BusinessLabels     []TextBusinessLabelsData `json:"businessLabels"`
	TokenProfileLabels []TokenProfileLabelData  `json:"tokenProfileLabels"`
	TokenRiskLabels    []TokenRiskLabelData     `json:"tokenRiskLabels"`
	LangResult         LangResultData           `json:"langResult"`
}

type TextRiskDetailData struct {
	MatchedLists []MatchedListData `json:"matchedLists"`
	RiskSegments []RiskSegmentData `json:"riskSegments"`
}

type TextAuxlnfoData struct {
	FilteredText     string              `json:"filteredText"`
	PassThrough      Origin              `json:"passThrough"`
	ContactResult    []ContactResultData `json:"contactResult"`
	UnauthorizedType string              `json:"unauthorizedType"`
}

type TextTokenLabelsData struct {
	UGCAccountRisk TextUGCAccountRiskData `json:"UGC_account_risk"`
}

type TextAllLabelsData struct {
	RiskLabel1      string             `json:"riskLabel1"`
	RiskLabel2      string             `json:"riskLabel2"`
	RiskLabel3      string             `json:"riskLabel3"`
	RiskDescription string             `json:"riskDescription"`
	Probability     float64            `json:"probability"`
	RiskDetail      TextRiskDetailData `json:"riskDetail"`
	RiskLevel       string             `json:"riskLevel"`
}

type TextBusinessLabelsData struct {
	BusinessLabel1      string  `json:"businessLabel1"`
	BusinessLabel2      string  `json:"businessLabel2"`
	BusinessLabel3      string  `json:"businessLabel3"`
	BusinessDescription float64 `json:"businessDescription"`
	Probability         float64 `json:"probability"`
}

type TokenProfileLabelData struct {
	Label1      string `json:"label1"`
	Label2      string `json:"label2"`
	Label3      string `json:"label3"`
	Description string `json:"description"`
	Timestamp   int    `json:"timestamp"`
}

type TokenRiskLabelData struct {
	Label1      string `json:"label1"`
	Label2      string `json:"label2"`
	Label3      string `json:"label3"`
	Description string `json:"description"`
	Timestamp   int    `json:"timestamp"`
}

type LangResultData struct {
	DetectedLang string `json:"detectedLang"`
}

type TextUGCAccountRiskData struct {
	SexyRiskTokenid float64 `json:"sexy_risk_tokenid"`
}

type ContactResultData struct {
	ContactType   int    `json:"contactType"`
	ContactString string `json:"contactString"`
}

func GetTextEventIdAndType(bizType string, sex int32, level int32) (eventId, typ string, err error) {
	typ = "TEXTRISK_TEXTMINOR"
	switch bizType {
	case review.BizTypeChat:
		eventId = TextEventIdIm
	case review.BizTypeRoomChat:
		eventId = TextEventIdRoomChat
	case review.BizTypeNickname:
		eventId = TextEventIdNickName
	case review.BizTypeLoveWords:
		eventId = TextEventIdTextSign
	case review.BizTypeRoomTitle:
		eventId = TextEventIdRoomTitle
	case review.BizTypeRoomNotice:
		eventId = TextEventIdRoomNotice
	case review.BizTypeMoment:
		eventId = TextEventIdMoment
	case review.BizTypeScriptTitle:
		eventId = TextEventIdScriptTitle
	case review.BizTypeScriptLine:
		eventId = TextEventIdScriptLine
	case review.BizTypeScriptCommentText:
		eventId = TextEventIdScriptComment
	case review.BizTypeScriptTopic:
		eventId = TextEventIdScriptTopic
	default:
		err = review.ErrReviewTypeBad
		return
	}
	return
}

// 文本传参：
// 1 appId：default
// 2 eventId：
// umine_私聊：id_message
// umine_个性签名：id_profile
// umine_语音房消息：id_room_message
// umine_语音房名称：id_room_title
// umine_语音房公告：id_room_notice
// 3 type：TEXTRISK
// 4 lang：如果区分不了语种传auto；能区分语种，按需传，如英语传en
// https://help.ishumei.com/docs/tj/text/newest/developDoc

func TextScan(userid, peerid int64, sex, level int32, text string, bizType, ip string, origin *Origin, isContext bool) (result AuditResultShumei, err error) {
	if isClosedReview() {
		return AuditResultShumei{
			RiskLevel: RiskLevelPass,
		}, nil
	}
	if len(text) == 0 {
		err = review.ErrReviewContentEmpty
		return
	}

	eventId, typ, err := GetTextEventIdAndType(bizType, sex, level)
	if err != nil {
		return
	}
	params := TextBody{
		BaseReq: GetBaseReq(),
		EventId: eventId,
		Type:    typ,
	}

	if level > 4 {
		level = 4
	}
	tokenId := GetTokenId(userid)
	if isContext {
		tokenId += "-misc"
	}
	params.Data = TextData{
		Text:    text,
		TokenId: tokenId,
		Ip:      ip,
		Extra: TextExtraData{
			Sex:            GetGender(sex),
			Level:          level,
			PassThrough:    origin,
			ReceiveTokenId: GetTokenId(peerid),
		},
		Lang: textSupportLang("zh"),
	}

	if eventId == TextEventIdNickName ||
		eventId == TextEventIdTextSign {
		params.Data.Extra.ReceiveTokenId = fmt.Sprintf("%d", peerid)
	}

	response := &RespText{}
	err = auditClient.AuditCheck(review.ContentText, 2, params, response, bizType)
	if err != nil {
		return
	}

	if response.Code != CodeSuccess {
		err = fmt.Errorf("code:%v,message:%v,requestId:%v", response.Code, response.Message, response.RequestId)
		logger.Errorf("shumei text err:%v", err)
		return
	}

	var allLabels []AllLabelsData
	for _, label := range response.AllLabels {
		allLabels = append(allLabels, AllLabelsData{
			RiskLabel1:      label.RiskLabel1,
			RiskLabel2:      label.RiskLabel2,
			RiskLabel3:      label.RiskLabel3,
			RiskDescription: label.RiskDescription,
			Probability:     label.Probability,
		})
	}

	return AuditResultShumei{
		RiskLevel:       response.RiskLevel,
		RequestId:       response.RequestId,
		RiskLabel1:      response.RiskLabel1,
		RiskLabel2:      response.RiskLabel2,
		RiskLabel3:      response.RiskLabel3,
		RiskDescription: response.RiskDescription,
		AllLabels:       allLabels,
	}, nil
}
