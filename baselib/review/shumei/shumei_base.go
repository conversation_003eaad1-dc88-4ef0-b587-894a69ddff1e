package shumei

import (
	"encoding/json"
	"fmt"
	"strconv"
	"time"

	"new-gitlab.xunlei.cn/vcproject/backends/baselib/httpcli"
	"new-gitlab.xunlei.cn/vcproject/backends/baselib/logger"
	"new-gitlab.xunlei.cn/vcproject/backends/baselib/metric"
	"new-gitlab.xunlei.cn/vcproject/backends/baselib/review"
	"new-gitlab.xunlei.cn/vcproject/backends/baselib/server/env"
	"new-gitlab.xunlei.cn/vcproject/backends/baselib/util"
	"new-gitlab.xunlei.cn/vcproject/backends/proto/api/common"
	"new-gitlab.xunlei.cn/vcproject/backends/proto/consts"
)

const (
	accessKey             = "6cNxVueKNokosBS9rDqx"                    // "YG6LegHP4el2bI4D3JXJ"
	callbackUri           = "/biznotify/shumei/audio_callback"        // 私信会话
	audioStreamCallback   = "/biznotify/shumei/audio_stream_callback" // 语音房音频流
	videoStreamCallback   = "/biznotify/shumei/video_stream_callback" // 打电话视频流
	mixCallbackUri        = "/biznotify/shumei/mix_callback"          // 混合内容的回调
	multiImageCallbackUri = "/biznotify/shumei/multi_image_callback"  // 多图片的
)

const (
	TokenIdOffset = 0 //int64(5687456)   // 不做偏移给 userid
)

type RiskLevel string

const (
	RiskLevelPass   RiskLevel = "PASS"
	RiskLevelReview RiskLevel = "REVIEW"
	RiskLevelReject RiskLevel = "REJECT"
	RiskLevelVerify RiskLevel = "VERIFY"
)

func (s RiskLevel) IsPass() bool {
	return s == RiskLevelPass
}

func (s RiskLevel) IsReview() bool {
	return s == RiskLevelReview
}

func (s RiskLevel) IsReject() bool {
	return s == RiskLevelReject
}

func (s RiskLevel) IsVerify() bool {
	return s == RiskLevelVerify
}

type Origin struct {
	SeqId   int64  `json:"seqid"`
	Content string `json:"content"`
	BizType string `json:"biz_type"`
}

type Code int

const (
	CodeSuccess      Code = 1100 // 成功
	CodeQPSLimit     Code = 1901 // QPS超限
	CodeParamError   Code = 1902 // 参数不合法
	CodeServiceFail  Code = 1903 // 服务失败
	CodeDownloadFail Code = 1911 // 图⽚片下载失败
	CodeNoBalance    Code = 9100 // 余额不足
	CodeNoPermission Code = 9101 // 无权限操作
)

type CodeError int

const (
	CodeErrorInvalidImageFormat CodeError = iota + 1 //Invalid Image Format
)

type TextExtraData struct {
	Sex            int     `json:"sex"`
	Level          int32   `json:"level"`
	ReceiveTokenId string  `json:"receiveTokenId"`
	PassThrough    *Origin `json:"passThrough"`
}

type ExtraData struct {
	PassThrough *Origin `json:"passThrough"`
}

type MatchedListData struct {
	Name  string     `json:"name"`
	Words []WordData `json:"words"`
}

type RiskSegmentData struct {
	Segment  string `json:"segment"`
	Position []int  `json:"position"`
}

type WordData struct {
	Word     string `json:"word"`
	Position []int  `json:"position"`
}

type AuditResultShumei struct {
	RiskLevel       RiskLevel
	RequestId       string
	BtId            string
	RiskLabel1      string
	RiskLabel2      string
	RiskLabel3      string
	RiskDescription string
	AllLabels       []AllLabelsData
	Code            CodeError
}

type AllLabelsData struct {
	RiskLabel1      string             `json:"riskLabel1"`
	RiskLabel2      string             `json:"riskLabel2"`
	RiskLabel3      string             `json:"riskLabel3"`
	RiskDescription string             `json:"riskDescription"`
	Probability     float64            `json:"probability"`
	RiskDetail      TextRiskDetailData `json:"riskDetail"`
	RiskLevel       string             `json:"riskLevel"`
}

type AuditClient struct {
	AppId                string
	TextUrl              string
	ImageUrl             string
	VoiceUrl             string
	VoiceResultUrl       string
	MixUrl               string
	MultiImages          string
	PhonePortraitUrl     string
	SkynetUrl            string
	AudioStreamUrl       string
	FinishAudioStreamUrl string
	VideoStreamUrl       string
	FinishVideoStreamUrl string
}

func (ar AuditResultShumei) Result() (auditResult common.AuditResult) {
	if ar.RiskLevel.IsPass() {
		auditResult = common.AuditResult_pass
	} else if ar.RiskLevel.IsReject() {
		auditResult = common.AuditResult_reject
	} else if ar.RiskLevel.IsReview() {
		auditResult = common.AuditResult_review
	} else {
		auditResult = common.AuditResult_unknown
	}
	return
}

var auditClient = AuditClient{
	AppId:            "voicelives",
	TextUrl:          "http://api-text-sh.fengkongcloud.com/text/v4",
	ImageUrl:         "http://api-img-sh.fengkongcloud.com/image/v4", //同步单张
	VoiceUrl:         "http://api-audio-sh.fengkongcloud.com/v2/saas/anti_fraud/audio",
	VoiceResultUrl:   "http://api-audio-sh.fengkongcloud.com/v2/saas/anti_fraud/query_audio",
	PhonePortraitUrl: "http://api-tianxiang-sh.fengkongcloud.com/tianxiang/v4", // 暂时不接
	SkynetUrl:        "http://api-skynet-sh.fengkongcloud.com/v4/event",
	MixUrl:           "http://api-media-gg.fengkongcloud.com/media/v1",
	MultiImages:      "http://api-img-sh.fengkongcloud.com/images/v4",
	// 音频流审核
	AudioStreamUrl:       "http://api-audiostream-sh.fengkongcloud.com/v2/saas/anti_fraud/audiostream",        //"http://api-audiostream-sh.fengkongcloud.com/audiostream/v4",
	FinishAudioStreamUrl: "http://api-audiostream-sh.fengkongcloud.com/v2/saas/anti_fraud/finish_audiostream", //"http://api-audiostream-sh.fengkongcloud.com/finish_audiostream/v4",
	// 视频流审核
	VideoStreamUrl:       "http://api-videostream-sh.fengkongcloud.com/videostream/v4",
	FinishVideoStreamUrl: "http://api-videostream-sh.fengkongcloud.com/finish_videostream/v4",
}

func GetAuditClient() AuditClient {
	return auditClient
}

func GetTokenId(userid int64) string {
	if !env.IsProd() {
		return fmt.Sprintf("test-%d", userid+TokenIdOffset)
	}
	return fmt.Sprintf("%d", userid+TokenIdOffset)
}

func GetGender(gender int32) int {
	switch gender {
	case consts.GenderFemale:
		return 2
	case consts.GenderMale:
		return 1
	}
	return 0
}

func isClosedReview() bool {
	return review.GetRiskConfig().CloseShumei
}

type BaseReq struct {
	AccessKey string `json:"accessKey"`
	AppId     string `json:"appId"`
}

func GetBaseReq() BaseReq {
	return BaseReq{
		AppId:     auditClient.AppId,
		AccessKey: accessKey}
}

func (client AuditClient) AuditCheck(typ review.ContentType, timeout int64, params interface{}, response interface{}, bizType string) (err error) {
	apiUrl := client.getUrl(typ)
	if len(apiUrl) == 0 {
		err = review.ErrReviewTypeBad
		return
	}
	logger.Debugf("ShumeiCheck post url:%v,params:%v", apiUrl, util.JsonStr(params))

	st := time.Now()

	resp, err := httpcli.PostJsonWithTimeout(apiUrl, nil, timeout, params)
	duration := time.Since(st)

	// 获取API方法名
	apiMethod := client.getAPIMethodName(typ)

	// 上报第三方API监控指标
	metric.ShumeiAPIHistogram("shumei", apiMethod, bizType, duration, err)

	if err != nil {
		logger.Warnf("ShumeiCheck post err: %v,params:%v", err, util.JsonStr(params))
		return
	}

	cost := duration.Milliseconds()
	if cost > 500 {
		logger.Warnf("ShumeiCheck contentType:%v,cost:%v,resp:%v", typ, cost, resp)
	}

	// 保留原有的监控指标以兼容现有面板
	metric.HistogramLabelsWithBuckets("shumei_time", map[string]string{
		"typ":      strconv.FormatInt(int64(typ), 10),
		"url":      apiUrl,
		"biz_type": bizType,
	}, []float64{10, 50, 100, 200, 300, 400, 500, 600, 700, 800, 900, 1000, 1200, 1400, 1600, 1800, 2000, 3000, 4000}).Observe(float64(cost))

	err = json.Unmarshal([]byte(resp), &response)
	if err != nil {
		logger.Warnf("ShumeiCheck json err: %v, resp: %v", err, resp)
		return
	}
	return
}

func (client AuditClient) getUrl(typ review.ContentType) string {
	switch typ {
	case review.ContentText:
		return client.TextUrl
	case review.ContentImage:
		return client.ImageUrl
	case review.ContentVoice:
		return client.VoiceUrl
	case review.ContentVoiceResult:
		return client.VoiceResultUrl
	case review.PhonePortrait:
		return client.PhonePortraitUrl
	case review.ContentSkynet:
		return client.SkynetUrl
	case review.ContentAudioStream:
		return client.AudioStreamUrl
	case review.ContentFinishAudioStream:
		return client.FinishAudioStreamUrl
	case review.ContentVideoStream:
		return client.VideoStreamUrl
	case review.ContentFinishVideoStream:
		return client.FinishVideoStreamUrl
	case review.ContentMix:
		return client.MixUrl
	case review.ContentMultiImages:
		return client.MultiImages
	}
	return ""
}

// getAPIMethodName 获取API方法名用于监控标签
func (client AuditClient) getAPIMethodName(typ review.ContentType) string {
	switch typ {
	case review.ContentText:
		return "text_scan"
	case review.ContentImage:
		return "image_scan"
	case review.ContentVoice:
		return "audio_scan"
	case review.ContentVoiceResult:
		return "audio_query"
	case review.PhonePortrait:
		return "phone_portrait"
	case review.ContentSkynet:
		return "skynet"
	case review.ContentAudioStream:
		return "audio_stream"
	case review.ContentFinishAudioStream:
		return "finish_audio_stream"
	case review.ContentVideoStream:
		return "video_stream"
	case review.ContentFinishVideoStream:
		return "finish_video_stream"
	case review.ContentMix:
		return "mix_scan"
	case review.ContentMultiImages:
		return "multi_image_scan"
	}
	return "unknown"
}
