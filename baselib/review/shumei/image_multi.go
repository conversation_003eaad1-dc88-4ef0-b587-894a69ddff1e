package shumei

import (
	"fmt"
	"strings"

	"new-gitlab.xunlei.cn/vcproject/backends/baselib/logger"
	"new-gitlab.xunlei.cn/vcproject/backends/baselib/review"
	"new-gitlab.xunlei.cn/vcproject/backends/proto/consts"

	"new-gitlab.xunlei.cn/vcproject/backends/proto/api/svcreview"
)

type MultiImageBody struct {
	BaseReq
	EventId   string         `json:"eventId"`
	ImageType string         `json:"type"`
	Data      MultiImageData `json:"data"`
	Callback  string         `json:"callback,omitempty"`
}

type MultiImageData struct {
	TokenId  string                          `json:"tokenId"`
	Contents []ImageContent                  `json:"imgs"`
	Lang     string                          `json:"lang"`
	Extra    svcreview.ShumeiMultiImageExtra `json:"extra"`
}
type ImageContent struct {
	BtId string `json:"btId"`
	Url  string `json:"img"`
}

func MultiImageScan(userid int64, imgUrls []string, btId int64, bizType, country, lang string) (result *MomentImagesResult, err error) {

	if len(imgUrls) == 0 {
		err = review.ErrReviewContentEmpty
		return
	}

	params := &MultiImageBody{
		BaseReq:   GetBaseReq(),
		EventId:   "id_album",
		ImageType: "POLITY_EROTIC_VIOLENT_IMGTEXTRISK_QRCODE_ADVERT",
		Callback:  consts.GetApiUrl(multiImageCallbackUri),
		Data: MultiImageData{
			TokenId: fmt.Sprintf("%d", userid),
			Lang:    lang,
			Extra: svcreview.ShumeiMultiImageExtra{
				PassThrough: &svcreview.ReviewMultiImageCallbackData{
					BizType: bizType,
					Id:      btId,
					Userid:  userid,
				},
			},
		},
	}

	for i, url := range imgUrls {
		if strings.TrimSpace(url) == "" {
			continue
		}
		params.Data.Contents = append(params.Data.Contents, ImageContent{
			Url:  url,
			BtId: fmt.Sprintf("%d-%d-%s-image-%d", userid, btId, bizType, i),
		})
	}

	response := &MomentImagesResult{}
	err = auditClient.AuditCheck(review.ContentMultiImages, 5, params, response, bizType)
	if err != nil {
		return
	}
	logger.Infof("MultiImageScan AuditCheck req: %v,resp: %v,err:%v", params, response, err)
	return &MomentImagesResult{
		Code:    response.Code,
		Message: response.Message,
	}, nil
}

type MomentImagesResult struct {
	Code       int      `json:"code"`
	Message    string   `json:"message"`
	RequestIds []ReqIds `json:"requestIds"`
}

type ReqIds struct {
	BtId      string `json:"btId"`
	RequestId string `json:"requestId"`
}
