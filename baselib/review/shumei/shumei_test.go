package shumei

import (
	"fmt"
	"testing"
	"time"

	"new-gitlab.xunlei.cn/vcproject/backends/baselib/alioss"
	"new-gitlab.xunlei.cn/vcproject/backends/baselib/logger"
	"new-gitlab.xunlei.cn/vcproject/backends/baselib/review"
	"new-gitlab.xunlei.cn/vcproject/backends/baselib/util"
)

// go test -v  -run TestText
func TestText(t *testing.T) {
	logger.InitLogger("/dev/stdout")
	st := time.Now()
	result, err := TextScan(5462307, 5462307, 1, 0, "可以的美女！怎么能找到你", review.BizTypeRoomChat, "", nil, false)
	t.Logf("%d", time.Since(st).Milliseconds())
	if err != nil {
		t.Logf(err.<PERSON><PERSON>r())
		return
	}
	t.Logf("%v", util.JsonStr(result))
}

// go test -v  -run TestImage
func TestImage(t *testing.T) {
	logger.InitLogger("/dev/stdout")
	//url := util.FullAvatarUrl("1000000000004752")
	url := alioss.FullMomentImageUrl("4209") // 1006402

	st := time.Now()
	result, err := ImageScan(1, 1, 0, 0, url, review.BizTypeChatImage, "", nil)
	t.Logf("%d", time.Since(st).Milliseconds())
	if err != nil {
		t.Logf(err.Error())
		return
	}
	t.Logf("%v", util.JsonStr(result))
}

// go test -v  -run TestAudio
func TestAudio(t *testing.T) {
	logger.InitLogger("/dev/stdout")
	st := time.Now()
	id := "1000000000001149"
	bizType := review.BizTypeChatAudio
	btId := fmt.Sprintf("%s_%s", bizType, id)
	url := alioss.FullChatAudioUrl(id)
	result, err := AudioScan(1, 2, 1, 0, btId, url, review.BizTypeChatAudio, "", nil)
	t.Logf("%d", time.Since(st).Milliseconds())
	if err != nil {
		t.Logf(err.Error())
		return
	}
	t.Logf("%v", util.JsonStr(result))

	for i := 0; i < 4; i++ {
		time.Sleep(5 * time.Second)
		resp, err := AutoAudioResult(btId)
		if err != nil {
			t.Logf(err.Error())
			continue
		}
		if resp.Code == CodeSuccess {
			t.Logf("%v", util.JsonStr(resp))
			break
		}
	}

}

func TestImageReciveTokenId(t *testing.T) {
	logger.InitLogger("/dev/stdout")
	//url := util.FullAvatarUrl("1000000000004752")
	url := alioss.FullMomentImageUrl("4209") // 1006402

	st := time.Now()
	result, err := ImageScan(1, 2, 0, 0, url, review.BizTypeChatImage, "", nil)
	t.Logf("%d", time.Since(st).Milliseconds())
	if err != nil {
		t.Logf(err.Error())
		return
	}
	t.Logf("%v", util.JsonStr(result))
}

func TestVideoStream(t *testing.T) {
	logger.InitLogger("/dev/stdout")
	// VideoLiveScan(642715, review.BizTypeVideoLive, "AGORA", "", "videodrv9dryhshdl8g", "", "")
}

func TestCustomShumeiPolicyTextAd(t *testing.T) {
	logger.InitLogger("/dev/stdout")
	defer logger.Sync()

	for i := 0; i <= 10; i++ {
		result, err := TextScan(5462307, 5462307, 1, 0, "爱聊", review.BizTypeChat, "", nil, false)
		if err != nil {
			logger.Errorf(err.Error())
		} else {
			logger.Infof(util.JsonStr(result))
		}
	}
	fmt.Println("ok")
}

func TestCustomShumeiPolicyText(t *testing.T) {
	logger.InitLogger("/dev/stdout")
	defer logger.Sync()

	for i := 0; i <= 10; i++ {
		result, err := TextScan(5462307, 5462307, 1, 0, "淘宝", review.BizTypeChat, "", nil, false)
		if err != nil {
			logger.Errorf(err.Error())
		} else {
			logger.Infof(util.JsonStr(result))
		}
	}
	fmt.Println("ok")
}

func TestAdText(t *testing.T) {
	logger.InitLogger("/dev/stdout")
	defer logger.Sync()

	result, err := TextScan(22, 5462307, 1, 0, "15396246589", review.BizTypeChat, "", nil, false)
	if err != nil {
		logger.Errorf(err.Error())
	} else {
		logger.Infof(util.JsonStr(result))
	}
	fmt.Println("ok")
}

func TestCustomShumeiPolicyTextMultUser(t *testing.T) {
	logger.InitLogger("/dev/stdout")
	defer logger.Sync()

	var userids = []int64{1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11}
	for _, userid := range userids {
		result, err := TextScan(642686, userid, 1, 0, "哈哈", review.BizTypeChat, "", nil, false)
		if err != nil {
			logger.Errorf(err.Error())
		} else {
			logger.Infof(util.JsonStr(result))
		}
	}

	fmt.Println("ok")
}

func TestCustomShumeiPolicyImageMultUser(t *testing.T) {
	logger.InitLogger("/dev/stdout")
	defer logger.Sync()

	var userids = []int64{1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11}
	for _, userid := range userids {
		result, err := ImageScan(23, userid, 0, 0, "https://tests3.boychat.net/chat/1000830", review.BizTypeChatImage, "", nil)
		if err != nil {
			logger.Errorf(err.Error())
		} else {
			logger.Infof(util.JsonStr(result))
		}
		time.Sleep(1 * time.Second)
	}

	fmt.Println("ok")
}
