package review

import (
	"errors"
)

var (
	ErrReviewContentEmpty = errors.New("review content empty")
	ErrReviewTypeBad      = errors.New("review type is not support")
)

type BizType = string

const (
	BizTypeNickname   BizType = "nickname"       //昵称
	BizTypeSignVoice  BizType = "sign_audio"     //语音签名
	BizTypeLoveWords  BizType = "sign"           //爱情宣言
	BizTypeAlbum      BizType = "album_image"    //相册
	BizTypeAvatar     BizType = "avatar"         //头像
	BizTypeMoment     BizType = "moment"         //动态
	BizTypeComment    BizType = "comment"        //动态评论
	BizTypeChat       BizType = "im_chat"        //IM即时聊天文本
	BizTypeChatImage  BizType = "im_image"       //IM即时聊天图片
	BizTypeChatAudio  BizType = "im_audio"       //IM即时聊天语音
	BizTypeChatVideo  BizType = "im_video"       //IM即时聊天语音 (目前没有相关业务)
	BizTypeVideoLive  BizType = "1v1_video_live" //1v1视频聊天
	BizTypeAudioLive  BizType = "1v1_audio_live" //1v1语音聊天
	BizTypeCommonMsg  BizType = "phrase"         //聊天常用语
	BizTypeRoomTitle  BizType = "room_title"     //房间名称
	BizTypeRoomNotice BizType = "room_notice"    //房间公告
	BizTypeRoomCover  BizType = "room_cover"     //房间封面
	BizTypeRoomBg     BizType = "room_bg"        //房间背景
	BizTypeRoomChat   BizType = "room_chat"      //房间公屏
	BizTypeRoomAudio  BizType = "room_audio"     //房间语音流

	BizTypeScriptTitle        BizType = "script_title"         //剧本标题
	BizTypeScriptCover        BizType = "script_cover"         //剧本封面
	BizTypeScriptLine         BizType = "script_line"          //剧本台词
	BizTypeScriptTopic        BizType = "script_topic"         //剧本话题
	BizTypeScriptDubbingAudio BizType = "script_dubbing_audio" //剧本配音语音
	BizTypeScriptDubbingText  BizType = "script_dubbing_text"  //剧本配音文本
	BizTypeScriptCommentAudio BizType = "script_comment_audio" //剧本语音评论
	BizTypeScriptCommentText  BizType = "script_comment_text"  //剧本文本评论
)

type ContentType int

const (
	ContentText      ContentType = 1
	ContentImage     ContentType = 2
	ContentVoice     ContentType = 3
	ContentVideo     ContentType = 4
	ContentTextImage ContentType = 5
	ContentTextVideo ContentType = 6
	ContentTextVoice ContentType = 7

	ContentVoiceCall   ContentType = 8
	ContentVideoCall   ContentType = 9
	ContentVoiceResult ContentType = 10

	PhonePortrait            ContentType = 11 //风险手机号画像
	ContentAudioStream       ContentType = 12 // 音频流审核
	ContentFinishAudioStream ContentType = 13 // 结束音频流审核
	ContentVideoStream       ContentType = 14 // 视频流审核
	ContentFinishVideoStream ContentType = 15 // 结束视频流审核
	ContentMix               ContentType = 17 //混合审核
	ContentMultiImages       ContentType = 18 //多图片审核
	ContentSkynet            ContentType = 100
)

type Suggestion string

func (s Suggestion) IsPass() bool {
	return s == SuggestionPass
}

func (s Suggestion) IsReview() bool {
	return s == SuggestionReview
}

func (s Suggestion) IsReject() bool {
	return s == SuggestionReject
}

func (s Suggestion) String() string {
	return string(s)
}

func (s Suggestion) IsEmpty() bool {
	return s == ""
}

type Origin struct {
	SeqId   int64  `json:"seqid"`
	Content string `json:"content"`
	DataId  string `json:"dataId"`
}

type ReviewResult struct {
	Suggestion Suggestion
	Label      string
}

const (
	SuggestionUnknown Suggestion = "unknown"
	SuggestionPass    Suggestion = "pass"
	SuggestionReview  Suggestion = "review"
	SuggestionReject  Suggestion = "reject"
)

func (s Suggestion) IsMoreWorse(cur Suggestion) bool {
	if cur.IsEmpty() {
		return true
	} else if cur.IsPass() {
		if s.IsReject() || s.IsReview() {
			return true
		}
	} else if cur.IsReview() {
		if s.IsReject() {
			return true
		}
	}
	return false
}

func Init() {
	InitRiskConf()
}
