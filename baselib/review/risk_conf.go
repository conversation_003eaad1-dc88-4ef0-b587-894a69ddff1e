package review

import (
	"flag"
	"fmt"
	"github.com/spf13/viper"
	"new-gitlab.xunlei.cn/vcproject/backends/baselib/logger"
	"new-gitlab.xunlei.cn/vcproject/backends/baselib/util"
)

var (
	riskConfig = RiskConfig{}
	Conf       string
)

func GetRiskConfig() *RiskConfig {
	return &riskConfig
}

func init() {
	flag.StringVar(&Conf, "config", "deploy/risk.yaml", "config file")
}

func InitRiskConf() {
	flag.Parse()
	viper.SetConfigType("yaml")
	viper.SetConfigFile(Conf)
	err := viper.ReadInConfig()
	if err != nil {
		panic(fmt.Errorf("error read config %s error %v", Conf, err))
	}
	err = viper.Unmarshal(&riskConfig)
	if err != nil {
		panic(fmt.Errorf("errpr viper.Unnarshal %v", err))
	}
	logger.Infof("load risk config init success %v ", util.JsonStr(riskConfig))
}

type RiskConfig struct {
	CloseShumei bool     `yaml:"close_shumei" mapstructure:"close_shumei"`
	WhiteApps   []string `yaml:"white_apps" mapstructure:"white_apps"`
	WhiteUid    []int64  `yaml:"white_uid" mapstructure:"white_uid"`
}
