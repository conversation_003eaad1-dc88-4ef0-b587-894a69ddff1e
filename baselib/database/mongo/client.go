package mongo

import (
	"context"
	"fmt"
	"reflect"
	"sync"
	"time"

	"go.mongodb.org/mongo-driver/mongo"
)

var (
	client         *Client
	initMetricOnce sync.Once
	mongoKey       = "bba67d378f1125b8e4821709fe7ba337"

	promMetricsPath                  = "/mongo_metric"
	clientMetricKeyLatency string    = "mongo_client_command_latency"
	clientMetricKeyError   string    = "mongo_client_command_error"
	clientMetricBucket     []float64 = []float64{5, 15, 30, 50, 100, 300, 600, 1000, 2500, 5000, 10000}
)

func GetMongoDBClient() *Client {
	return client
}

type Client struct {
	conn         *mongo.Client
	pool         chan bool
	timeout      time.Duration
	metricTarget string
}

// Client get mongo driver client
func (f *Client) Client() *mongo.Client {
	return f.conn
}

// Timeout get configed timeout
func (f *Client) Timeout() time.Duration {
	return f.timeout
}

func (f *Client) SetTimeout(timeout time.Duration) {
	f.timeout = timeout
}

func (f *Client) ScanCursor(ctx context.Context, cursor *mongo.Cursor, result interface{}) (err error) {
	defer func() {
		err1 := cursor.Close(ctx)
		if err == nil && err1 != nil {
			err = err1
		}
	}()

	resultv := reflect.ValueOf(result)
	if resultv.Kind() != reflect.Ptr || resultv.Elem().Kind() != reflect.Slice {
		err = fmt.Errorf("result argument must be a slice address")
		return
	}
	slicev := resultv.Elem()
	slicev = slicev.Slice(0, slicev.Cap())
	elemt := slicev.Type().Elem()

	i := 0
	for ; cursor.Next(ctx); i++ {
		if slicev.Len() == i {
			// slice长度耗尽时，通过append触发slice_grow
			// 并将slice的len扩大到与cap相同，保证新增长的空间可用index索引
			elemp := reflect.New(elemt)
			err = cursor.Decode(elemp.Interface())
			if err != nil {
				return
			}
			slicev = reflect.Append(slicev, elemp.Elem())
			slicev = slicev.Slice(0, slicev.Cap())
		} else {
			// slice长度未耗尽时通过index索引使用剩余空间
			err = cursor.Decode(slicev.Index(i).Addr().Interface())
			if err != nil {
				return
			}
		}
	}
	// 将slice恢复为真正的长度
	resultv.Elem().Set(slicev.Slice(0, i))

	err = cursor.Err()
	if err != nil {
		return
	}
	return
}

// NewCollectionWrapper get collection operation wrapper, with a default waitPoolTimeout valued one second.
func (f *Client) NewCollectionWrapper(database, collection string) CollectionWrapper {
	return &collectionWrapper{
		client:     f,
		database:   database,
		collection: collection,
	}
}
