package mongo

import (
	"context"
	"runtime"
	"strings"
	"time"

	"github.com/pkg/errors"
	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/mongo"
	"go.mongodb.org/mongo-driver/mongo/options"
	"new-gitlab.xunlei.cn/vcproject/backends/baselib/logger"
	"new-gitlab.xunlei.cn/vcproject/backends/baselib/metric"
)

// CollectionWrapper declares a wrapper of mongo collection operators.
type CollectionWrapper interface {

	// GenSortBson translate sort keys like [-_id, cnt, +ut] to bson.D
	GenSortBson(sort []string) (result bson.D)

	// Find
	Find(ctx context.Context, filter interface{}, result interface{}, sort []string, skip, limit int64, opts ...*options.FindOptions) (err error)

	// FindOne
	FindOne(ctx context.Context, filter interface{}, result interface{}, sort []string, skip int64, opts ...*options.FindOneOptions) (has bool, err error)

	// FindID
	FindID(ctx context.Context, ID interface{}, result interface{}, opts ...*options.FindOneOptions) (has bool, err error)

	// FindOneAndUpdate
	FindOneAndUpdate(ctx context.Context, filter, update, result interface{}, sort []string, upsert, returnNew bool, opts ...*options.FindOneAndUpdateOptions) (has bool, err error)

	// FindOneAndReplace
	FindOneAndReplace(ctx context.Context, filter, replacement, result interface{}, sort []string, upsert, returnNew bool, opts ...*options.FindOneAndReplaceOptions) (has bool, err error)

	// FindOneAndDelete
	FindOneAndDelete(ctx context.Context, filter, result interface{}, sort []string, opts ...*options.FindOneAndDeleteOptions) (has bool, err error)

	// InsertOne
	InsertOne(ctx context.Context, document interface{}, opts ...*options.InsertOneOptions) (insertedID interface{}, err error)

	// InsertMany
	InsertMany(ctx context.Context, document []interface{}, opts ...*options.InsertManyOptions) (insertedIDs []interface{}, err error)

	// UpdateOne
	UpdateOne(ctx context.Context, filter, update interface{}, upsert bool, opts ...*options.UpdateOptions) (result *UpdateResult, err error)

	// UpdateID
	UpdateID(ctx context.Context, ID, update interface{}, upsert bool, opts ...*options.UpdateOptions) (result *UpdateResult, err error)

	// UpdateMany
	UpdateMany(ctx context.Context, filter, update interface{}, upsert bool, opts ...*options.UpdateOptions) (result *UpdateResult, err error)

	// Counts
	Count(ctx context.Context, filter interface{}, skip, limit int64, opts ...*options.CountOptions) (count int64, err error)

	// EstimatedCount，For a fast count of the documents in the collection
	EstimatedCount(ctx context.Context, opts ...*options.EstimatedDocumentCountOptions) (count int64, err error)

	// DeleteOne
	DeleteOne(ctx context.Context, filter interface{}, opts ...*options.DeleteOptions) (has bool, err error)

	// DeleteID
	DeleteID(ctx context.Context, ID interface{}, opts ...*options.DeleteOptions) (has bool, err error)

	// DeleteMany
	DeleteMany(ctx context.Context, filter interface{}, opts ...*options.DeleteOptions) (deletedCnt int64, err error)

	// Distinct
	Distinct(ctx context.Context, filedName string, filter interface{}, opts ...*options.DistinctOptions) (result []interface{}, err error)

	// BulkWrite
	BulkWrite(ctx context.Context, models []mongo.WriteModel, opts ...*options.BulkWriteOptions) (result *BulkWriteResult, err error)

	// Aggregate
	// pipeline: a slice of aggragate commands
	// result: a slice address
	Aggregate(ctx context.Context, pipeline, result interface{}, opts ...*options.AggregateOptions) (err error)

	/*UseSession provides the ability of transaction.
	* fn is a closure, all the action in it should use SessionContext as context param.
	* Use GenSessionWrapper() is a good idea to simple your code.
	 */
	UseSession(ctx context.Context, fn func(mongo.SessionContext) error, opts ...*options.SessionOptions) (err error)

	FindScan(ctx context.Context, m bson.M, findOption *options.FindOptions, f func(decoder CurDecoder) error) (err error)
}

// UpdateResult is the result type returned from UpdateOne, UpdateMany, and ReplaceOne operations.
type UpdateResult struct {
	MatchedCount  int64       // The number of documents matched by the filter.
	ModifiedCount int64       // The number of documents modified by the operation.
	UpsertedCount int64       // The number of documents upserted by the operation.
	UpsertedID    interface{} // The _id field of the upserted document, or nil if no upsert was done.
}

// BulkWriteResult is the result type returned by a BulkWrite operation.
type BulkWriteResult struct {
	InsertedCount int64                 // The number of documents inserted.
	MatchedCount  int64                 // The number of documents matched by filters in update and replace operations.
	ModifiedCount int64                 // The number of documents modified by update and replace operations.
	DeletedCount  int64                 // The number of documents deleted.
	UpsertedCount int64                 // The number of documents upserted by update and replace operations.
	UpsertedIDs   map[int64]interface{} // A map of operation index to the _id of each upserted document.
}

var _ CollectionWrapper = &collectionWrapper{}

type collectionWrapper struct {
	client     *Client
	database   string
	collection string
}

type commandMetricInfo struct {
	st         time.Time
	callerName string
}

func (c *collectionWrapper) startMetric() commandMetricInfo {
	defer c.recover()
	pc := make([]uintptr, 2)
	runtime.Callers(2, pc)
	caller := runtime.FuncForPC(pc[0])
	if caller == nil {
		return commandMetricInfo{}
	}

	callerName := caller.Name()
	if idx := strings.LastIndex(callerName, "."); idx > 0 {
		callerName = callerName[idx+1:]
	}
	return commandMetricInfo{
		st:         time.Now(),
		callerName: callerName,
	}
}

func (c *collectionWrapper) endMetric(info commandMetricInfo, err error) {
	defer c.recover()
	if info == (commandMetricInfo{}) {
		return
	}
	if err != nil {
		metric.CounterWithLabels(clientMetricKeyError, map[string]string{
			"target":     c.client.metricTarget,
			"command":    info.callerName,
			"db":         c.database,
			"collection": c.collection,
		}).Inc()
	}
	metric.HistogramLabels(clientMetricKeyLatency, map[string]string{
		"target":     c.client.metricTarget,
		"command":    info.callerName,
		"db":         c.database,
		"collection": c.collection,
	}).Observe(float64(time.Since(info.st).Milliseconds()))
}

func (c *collectionWrapper) recover() {
	err := recover()
	if err != nil {
		buf := make([]byte, 10240)
		runtime.Stack(buf, false)
		logger.Errorf("panic: %v,\n%s", err, string(buf))
	}
}

func (c *collectionWrapper) GenSortBson(sort []string) (result bson.D) {
	result = bson.D{}
	for _, v := range sort {
		if strings.HasPrefix(v, "-") {
			v = strings.TrimPrefix(v, "-")
			result = append(result, bson.E{Key: v, Value: -1})
		} else {
			v = strings.TrimPrefix(v, "+")
			result = append(result, bson.E{Key: v, Value: 1})
		}
	}
	return
}

// GenSessionWrapper wraps the common Start/Commit/Abort Transaction code
// The wrapper will commit the transcation if logic()'s error is nil, otherwise abort it.
// Operations in the logic() must use mongo.SessionContext as ctx parameter.
/* example:
logic := func(ctx mongo.SessionContext) (err error) {
	_, err = col.InsertOne(ctx, bson.M{"key": 1})
	if err != nil {
		// transaction will be aborted if err!=nil
		return
	}
	// transaction will be commited if err==nil
	return
}
wrapper := col.GenSessionWrapper(logic)
err = col.UseSession(context.TODO(), wrapper)
if err != nil {
	return
}
*/
func (c *collectionWrapper) GenSessionWrapper(logic func(mongo.SessionContext) error) (wrapper func(mongo.SessionContext) error) {
	wrapper = func(ctx mongo.SessionContext) (err error) {
		err = ctx.StartTransaction()
		if err != nil {
			err = errors.Wrap(err, "start transaction")
			return
		}
		defer func() {
			if err != nil {
				err1 := ctx.AbortTransaction(ctx)
				if err1 != nil {
					err = errors.Wrapf(err, "abort transaction: %v", err1)
					return
				}
			} else {
				err = ctx.CommitTransaction(ctx)
				if err != nil {
					err = errors.Wrap(err, "commit transaction")
					return
				}
			}
		}()
		err = logic(ctx)
		if err != nil {
			err = errors.WithMessage(err, "do wrapper logic")
			return
		}
		return
	}
	return
}

func (c *collectionWrapper) Find(ctx context.Context, filter interface{}, result interface{},
	sort []string, skip, limit int64, opts ...*options.FindOptions) (err error) {

	metric := c.startMetric()
	defer func() {
		c.endMetric(metric, err)
	}()

	opt := options.Find().SetSkip(skip).SetLimit(limit)
	if len(sort) > 0 {
		opt.SetSort(c.GenSortBson(sort))
	}
	opts = append(opts, opt)

	conn := c.client.Client()
	if ctx == nil {
		ctx = context.Background()
	}
	ctx, cancel := context.WithTimeout(ctx, c.client.Timeout())
	defer cancel()

	cursor, err := conn.Database(c.database).Collection(c.collection).Find(ctx, filter, opts...)
	if err != nil {
		return
	}
	err = c.client.ScanCursor(ctx, cursor, result)
	if err != nil {
		return
	}
	return
}

func (c *collectionWrapper) FindOne(ctx context.Context, filter interface{}, result interface{},
	sort []string, skip int64, opts ...*options.FindOneOptions) (has bool, err error) {

	metric := c.startMetric()
	defer func() {
		c.endMetric(metric, err)
	}()

	return c.findOne(ctx, filter, result, sort, skip, opts...)
}

func (c *collectionWrapper) findOne(ctx context.Context, filter interface{}, result interface{},
	sort []string, skip int64, opts ...*options.FindOneOptions) (has bool, err error) {

	opt := options.FindOne().SetSkip(skip)
	if len(sort) > 0 {
		opt.SetSort(c.GenSortBson(sort))
	}
	opts = append(opts, opt)

	conn := c.client.Client()
	if ctx == nil {
		ctx = context.Background()
	}
	ctx, cancel := context.WithTimeout(ctx, c.client.Timeout())
	defer cancel()

	err = conn.Database(c.database).Collection(c.collection).FindOne(ctx, filter, opts...).Decode(result)
	if err == mongo.ErrNoDocuments {
		has, err = false, nil
		return
	}
	if err != nil {
		return
	}
	has = true
	return
}

func (c *collectionWrapper) FindID(ctx context.Context, ID interface{}, result interface{},
	opts ...*options.FindOneOptions) (has bool, err error) {

	metric := c.startMetric()
	defer func() {
		c.endMetric(metric, err)
	}()

	filter := bson.M{"_id": ID}
	return c.findOne(ctx, filter, result, nil, 0, opts...)
}

func (c *collectionWrapper) FindOneAndUpdate(ctx context.Context, filter, update, result interface{},
	sort []string, upsert, returnNew bool, opts ...*options.FindOneAndUpdateOptions) (has bool, err error) {

	metric := c.startMetric()
	defer func() {
		c.endMetric(metric, err)
	}()

	opt := options.FindOneAndUpdate()
	if len(sort) > 0 {
		opt.SetSort(c.GenSortBson(sort))
	}
	opt.SetUpsert(upsert)
	if returnNew {
		opt.SetReturnDocument(options.After)
	} else {
		opt.SetReturnDocument(options.Before)
	}
	opts = append(opts, opt)

	conn := c.client.Client()
	if ctx == nil {
		ctx = context.Background()
	}
	ctx, cancel := context.WithTimeout(ctx, c.client.Timeout())
	defer cancel()

	err = conn.Database(c.database).Collection(c.collection).FindOneAndUpdate(ctx, filter, update, opts...).Decode(result)
	if err == mongo.ErrNoDocuments {
		has, err = false, nil
		return
	}
	if err != nil {
		return
	}
	has = true
	return
}

func (c *collectionWrapper) FindOneAndReplace(ctx context.Context, filter, replacement, result interface{},
	sort []string, upsert, returnNew bool, opts ...*options.FindOneAndReplaceOptions) (has bool, err error) {

	metric := c.startMetric()
	defer func() {
		c.endMetric(metric, err)
	}()

	opt := options.FindOneAndReplace()
	if len(sort) > 0 {
		opt.SetSort(c.GenSortBson(sort))
	}
	opt.SetUpsert(upsert)
	if returnNew {
		opt.SetReturnDocument(options.After)
	} else {
		opt.SetReturnDocument(options.Before)
	}
	opts = append(opts, opt)

	conn := c.client.Client()
	if ctx == nil {
		ctx = context.Background()
	}
	ctx, cancel := context.WithTimeout(ctx, c.client.Timeout())
	defer cancel()

	err = conn.Database(c.database).Collection(c.collection).FindOneAndReplace(ctx, filter, replacement, opts...).Decode(result)
	if err == mongo.ErrNoDocuments {
		has, err = false, nil
		return
	}
	if err != nil {
		return
	}
	has = true
	return
}

func (c *collectionWrapper) FindOneAndDelete(ctx context.Context, filter, result interface{},
	sort []string, opts ...*options.FindOneAndDeleteOptions) (has bool, err error) {

	metric := c.startMetric()
	defer func() {
		c.endMetric(metric, err)
	}()

	opt := options.FindOneAndDelete()
	if len(sort) > 0 {
		opt.SetSort(c.GenSortBson(sort))
	}
	opts = append(opts, opt)

	conn := c.client.Client()
	if ctx == nil {
		ctx = context.Background()
	}
	ctx, cancel := context.WithTimeout(ctx, c.client.Timeout())
	defer cancel()

	err = conn.Database(c.database).Collection(c.collection).FindOneAndDelete(ctx, filter, opts...).Decode(result)
	if err == mongo.ErrNoDocuments {
		has, err = false, nil
		return
	}
	if err != nil {
		return
	}
	has = true
	return
}

func (c *collectionWrapper) InsertOne(ctx context.Context, document interface{},
	opts ...*options.InsertOneOptions) (insertedID interface{}, err error) {

	metric := c.startMetric()
	defer func() {
		c.endMetric(metric, err)
	}()

	conn := c.client.Client()
	if ctx == nil {
		ctx = context.Background()
	}
	ctx, cancel := context.WithTimeout(ctx, c.client.Timeout())
	defer cancel()

	result, err := conn.Database(c.database).Collection(c.collection).InsertOne(ctx, document, opts...)
	if err != nil {
		return
	}
	insertedID = result.InsertedID
	return
}

func (c *collectionWrapper) InsertMany(ctx context.Context, document []interface{},
	opts ...*options.InsertManyOptions) (insertedIDs []interface{}, err error) {

	metric := c.startMetric()
	defer func() {
		c.endMetric(metric, err)
	}()

	conn := c.client.Client()
	if ctx == nil {
		ctx = context.Background()
	}
	ctx, cancel := context.WithTimeout(ctx, c.client.Timeout())
	defer cancel()

	result, err := conn.Database(c.database).Collection(c.collection).InsertMany(ctx, document, opts...)
	if err != nil {
		return
	}
	insertedIDs = result.InsertedIDs
	return
}

func (c *collectionWrapper) UpdateOne(ctx context.Context, filter, update interface{},
	upsert bool, opts ...*options.UpdateOptions) (result *UpdateResult, err error) {

	metric := c.startMetric()
	defer func() {
		c.endMetric(metric, err)
	}()

	return c.updateOne(ctx, filter, update, upsert, opts...)
}

func (c *collectionWrapper) UpdateID(ctx context.Context, ID, update interface{},
	upsert bool, opts ...*options.UpdateOptions) (result *UpdateResult, err error) {

	metric := c.startMetric()
	defer func() {
		c.endMetric(metric, err)
	}()

	filter := bson.M{"_id": ID}
	return c.updateOne(ctx, filter, update, upsert, opts...)
}

func (c *collectionWrapper) updateOne(ctx context.Context, filter, update interface{},
	upsert bool, opts ...*options.UpdateOptions) (result *UpdateResult, err error) {
	opt := options.Update()
	opt.SetUpsert(upsert)
	opts = append(opts, opt)

	conn := c.client.Client()
	if ctx == nil {
		ctx = context.Background()
	}
	ctx, cancel := context.WithTimeout(ctx, c.client.Timeout())
	defer cancel()

	resultOri, err := conn.Database(c.database).Collection(c.collection).UpdateOne(ctx, filter, update, opts...)
	if err != nil {
		return
	}

	result = &UpdateResult{
		MatchedCount:  resultOri.MatchedCount,
		ModifiedCount: resultOri.ModifiedCount,
		UpsertedCount: resultOri.UpsertedCount,
		UpsertedID:    resultOri.UpsertedID,
	}
	return
}

func (c *collectionWrapper) UpdateMany(ctx context.Context, filter, update interface{},
	upsert bool, opts ...*options.UpdateOptions) (result *UpdateResult, err error) {

	metric := c.startMetric()
	defer func() {
		c.endMetric(metric, err)
	}()

	opt := options.Update()
	opt.SetUpsert(upsert)
	opts = append(opts, opt)

	conn := c.client.Client()
	if ctx == nil {
		ctx = context.Background()
	}
	ctx, cancel := context.WithTimeout(ctx, c.client.Timeout())
	defer cancel()

	resultOri, err := conn.Database(c.database).Collection(c.collection).UpdateMany(ctx, filter, update, opts...)
	if err != nil {
		return
	}

	result = &UpdateResult{
		MatchedCount:  resultOri.MatchedCount,
		ModifiedCount: resultOri.ModifiedCount,
		UpsertedCount: resultOri.UpsertedCount,
		UpsertedID:    resultOri.UpsertedID,
	}
	return
}

func (c *collectionWrapper) Count(ctx context.Context, filter interface{}, skip, limit int64,
	opts ...*options.CountOptions) (count int64, err error) {

	metric := c.startMetric()
	defer func() {
		c.endMetric(metric, err)
	}()

	opt := options.Count().SetSkip(skip)
	if limit > 0 {
		opt.SetLimit(limit)
	}
	opts = append(opts, opt)

	conn := c.client.Client()
	if ctx == nil {
		ctx = context.Background()
	}
	ctx, cancel := context.WithTimeout(ctx, c.client.Timeout())
	defer cancel()

	count, err = conn.Database(c.database).Collection(c.collection).CountDocuments(ctx, filter, opts...)
	if err != nil {
		return
	}
	return
}

func (c *collectionWrapper) EstimatedCount(ctx context.Context, opts ...*options.EstimatedDocumentCountOptions) (count int64, err error) {

	metric := c.startMetric()
	defer func() {
		c.endMetric(metric, err)
	}()

	conn := c.client.Client()
	if ctx == nil {
		ctx = context.Background()
	}
	ctx, cancel := context.WithTimeout(ctx, c.client.Timeout())
	defer cancel()

	count, err = conn.Database(c.database).Collection(c.collection).EstimatedDocumentCount(ctx, opts...)
	if err != nil {
		return
	}
	return
}

func (c *collectionWrapper) deleteOne(ctx context.Context, filter interface{},
	opts ...*options.DeleteOptions) (has bool, err error) {
	conn := c.client.Client()
	if ctx == nil {
		ctx = context.Background()
	}
	ctx, cancel := context.WithTimeout(ctx, c.client.Timeout())
	defer cancel()

	result, err := conn.Database(c.database).Collection(c.collection).DeleteOne(ctx, filter, opts...)
	if err != nil {
		return
	}
	has = result.DeletedCount > 0
	return
}

func (c *collectionWrapper) DeleteOne(ctx context.Context, filter interface{},
	opts ...*options.DeleteOptions) (has bool, err error) {

	metric := c.startMetric()
	defer func() {
		c.endMetric(metric, err)
	}()

	return c.deleteOne(ctx, filter, opts...)
}

func (c *collectionWrapper) DeleteID(ctx context.Context, ID interface{},
	opts ...*options.DeleteOptions) (has bool, err error) {

	metric := c.startMetric()
	defer func() {
		c.endMetric(metric, err)
	}()

	filter := bson.M{"_id": ID}
	return c.deleteOne(ctx, filter, opts...)
}

func (c *collectionWrapper) DeleteMany(ctx context.Context, filter interface{},
	opts ...*options.DeleteOptions) (deletedCnt int64, err error) {

	metric := c.startMetric()
	defer func() {
		c.endMetric(metric, err)
	}()

	conn := c.client.Client()
	if ctx == nil {
		ctx = context.Background()
	}
	ctx, cancel := context.WithTimeout(ctx, c.client.Timeout())
	defer cancel()

	result, err := conn.Database(c.database).Collection(c.collection).DeleteMany(ctx, filter, opts...)
	if err != nil {
		return
	}
	deletedCnt = result.DeletedCount
	return
}

func (c *collectionWrapper) Distinct(ctx context.Context, filedName string, filter interface{},
	opts ...*options.DistinctOptions) (result []interface{}, err error) {

	metric := c.startMetric()
	defer func() {
		c.endMetric(metric, err)
	}()

	conn := c.client.Client()
	if ctx == nil {
		ctx = context.Background()
	}
	ctx, cancel := context.WithTimeout(ctx, c.client.Timeout())
	defer cancel()

	result, err = conn.Database(c.database).Collection(c.collection).Distinct(ctx, filedName, filter, opts...)
	if err != nil {
		return
	}
	return
}

func (c *collectionWrapper) Aggregate(ctx context.Context, pipeline, result interface{},
	opts ...*options.AggregateOptions) (err error) {

	conn := c.client.Client()
	if ctx == nil {
		ctx = context.Background()
	}
	ctx, cancel := context.WithTimeout(ctx, c.client.Timeout())
	defer cancel()

	cursor, err := conn.Database(c.database).Collection(c.collection).Aggregate(ctx, pipeline, opts...)
	if err != nil {
		return
	}
	err = c.client.ScanCursor(ctx, cursor, result)
	if err != nil {
		return
	}
	return
}

func (c *collectionWrapper) UseSession(ctx context.Context, fn func(mongo.SessionContext) error,
	opts ...*options.SessionOptions) (err error) {

	metric := c.startMetric()
	defer func() {
		c.endMetric(metric, err)
	}()

	conn := c.client.Client()
	if ctx == nil {
		ctx = context.Background()
	}
	ctx, cancel := context.WithTimeout(ctx, c.client.Timeout())
	defer cancel()

	err = conn.UseSessionWithOptions(ctx, options.MergeSessionOptions(opts...), fn)
	if err != nil {
		return
	}
	return
}

func (c *collectionWrapper) BulkWrite(ctx context.Context, models []mongo.WriteModel,
	opts ...*options.BulkWriteOptions) (result *BulkWriteResult, err error) {

	metric := c.startMetric()
	defer func() {
		c.endMetric(metric, err)
	}()

	conn := c.client.Client()
	if ctx == nil {
		ctx = context.Background()
	}
	ctx, cancel := context.WithTimeout(ctx, c.client.Timeout())
	defer cancel()

	oriResult, err := conn.Database(c.database).Collection(c.collection).BulkWrite(ctx, models, options.MergeBulkWriteOptions(opts...))
	if err != nil {
		return
	}

	result = &BulkWriteResult{
		InsertedCount: oriResult.InsertedCount,
		MatchedCount:  oriResult.MatchedCount,
		ModifiedCount: oriResult.ModifiedCount,
		DeletedCount:  oriResult.DeletedCount,
		UpsertedCount: oriResult.UpsertedCount,
		UpsertedIDs:   oriResult.UpsertedIDs,
	}
	return
}

type CurDecoder interface {
	Decode(val interface{}) error
}

func (c *collectionWrapper) FindScan(ctx context.Context, m bson.M, findOption *options.FindOptions,
	f func(decoder CurDecoder) error) (err error) {
	cursor, err := c.client.Client().Database(c.database).Collection(c.collection).
		Find(context.Background(), m, findOption)
	if err != nil {
		logger.Errorf("error %v", err)
		return
	}
	defer func() {
		err = cursor.Close(context.Background())
		if err != nil {
			logger.Errorf("error %v", err)
		}
	}()
	for cursor.Next(context.Background()) {
		err = f(cursor)
		if err != nil {
			logger.Errorf("error %v", err)
			return err
		}
	}

	err = cursor.Err()
	if err != nil {
		logger.Errorf("error %v", err)
		return err
	}
	//err = cursor.Close(context.Background())
	//if err != nil {
	//	logger.Errorf("error %v", err)
	//	return err
	//}
	return
}
