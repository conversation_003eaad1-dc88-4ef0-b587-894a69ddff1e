package database

import (
	"time"

	"github.com/prometheus/client_golang/prometheus/collectors"
	"go.uber.org/zap"
	"gorm.io/driver/mysql"
	"gorm.io/gorm"
	"moul.io/zapgorm2"
	"new-gitlab.xunlei.cn/vcproject/backends/baselib/config"
	"new-gitlab.xunlei.cn/vcproject/backends/baselib/logger"
	"new-gitlab.xunlei.cn/vcproject/backends/baselib/metric"
)

var (
	slaveMysqlDBs = make(map[string]*gorm.DB)
)

func InitSlaveMysql(configs []*config.MysqlConfig) {
	if len(configs) == 0 {
		logger.Warnf("InitSlaveMysql no mysql configs")
		return
	}
	for _, config := range configs {
		gormConfig := mysql.Config{
			DSN:                       config.Dsn(), // DSN data source name
			DefaultStringSize:         191,          // string 类型字段的默认长度
			DisableDatetimePrecision:  true,         // 禁用 datetime 精度，MySQL 5.6 之前的数据库不支持
			DontSupportRenameIndex:    true,         // 重命名索引时采用删除并新建的方式，MySQL 5.7 之前的数据库和 MariaDB 不支持重命名索引
			DontSupportRenameColumn:   true,         // 用 `change` 重命名列，MySQL 8 之前的数据库和 MariaDB 不支持重命名列
			SkipInitializeWithVersion: false,        // 根据版本自动配置
		}
		l := zapgorm2.New(logger.Logger().Named("gorm").With(zap.String("db", config.DbName)))
		c := gormOptions(l)
		if config.DbName == "new_stats" {
			c.PrepareStmt = false
		}
		if db, err := gorm.Open(mysql.New(gormConfig), c); err == nil {
			sqlDB, _ := db.DB()
			if config.MaxIdleConns > 0 {
				sqlDB.SetMaxIdleConns(config.MaxIdleConns)
			}
			if config.MaxOpenConns > 0 {
				sqlDB.SetMaxOpenConns(config.MaxOpenConns)
			}
			sqlDB.SetConnMaxIdleTime(time.Minute * 20)
			if config.Name == "" {
				config.Name = config.DbName
			}

			metric.MustRegister(collectors.NewDBStatsCollector(sqlDB, config.Name+"_slave_"+config.Host))

			slaveMysqlDBs[config.Name] = db
			logger.Info("Mysql连接从库成功", zap.Any("name", config.Name), zap.Any("db-name", config.DbName), zap.Any("dsn", config.Dsn()))
		} else {
			logger.Panicf("Mysql连接从库异常 name=%s dsn=%s err=%+v", config.Name, config.Dsn(), zap.Any("err", err))
			return
		}
	}
}

// 连接只读库
func GetMysqlSlaveDB(name string) *gorm.DB {
	if db, ok := slaveMysqlDBs[name]; ok {
		return db
	}
	return nil
}

// 连接只读库
type SlaveBaseDao interface {
	SlaveReady() *gorm.DB
}
