package msgchan

import (
	"context"
	"fmt"
	"strconv"
	"time"

	"github.com/hashicorp/go-uuid"
	"golang.org/x/exp/rand"
	"new-gitlab.xunlei.cn/vcproject/backends/baselib/config"
	"new-gitlab.xunlei.cn/vcproject/backends/baselib/grpccli"
	"new-gitlab.xunlei.cn/vcproject/backends/baselib/logger"
	"new-gitlab.xunlei.cn/vcproject/backends/baselib/metric"
	"new-gitlab.xunlei.cn/vcproject/backends/baselib/server/env"
	"new-gitlab.xunlei.cn/vcproject/backends/baselib/util"
	"new-gitlab.xunlei.cn/vcproject/backends/proto/api/basemsgcallin"
	"new-gitlab.xunlei.cn/vcproject/backends/proto/api/common"
)

const (
	RetOK           = 0      //成功
	RetInvalidParam = 180400 // 参数错误
	RetSignError    = 180401 // 签名错误

	devPlatFormId = "1004"
	devSecret     = "fsnq7vFSF9xffs"

	testPlatformId = "4"
	testSecret     = "B8n37v4Xq95cfs"

	onlinePlatformId = "4"
	onlineSecret     = "B8n37v4Xq95cfs"
)

const (
	PublishTypeSingleUser = "1"
	PublishTypeTopic      = "2"
)

var msgChan = &struct {
	config *config.MsgChanConfig
	//httpClient  basemsgcallin.SClient
	localClient basemsgcallin.SClient
	signature   *Signature
}{}

type TokenData struct {
	Userid int64  `json:"userid"`
	Devid  string `json:"devid"`
	Data   string `json:"data"`
}

type ExtendData struct {
	Scheme    string            `json:"scheme"`
	BaseParam *common.BaseParam `json:"base_param"`
}

func Init() error {
	var conf *config.MsgChanConfig
	switch {
	case env.IsDev():
		conf = &config.MsgChanConfig{
			PlatformId: devPlatFormId,
			Secret:     devSecret,
			Duration:   600,
		}
	case env.IsTest() || env.IsLocal():
		conf = &config.MsgChanConfig{
			PlatformId: testPlatformId,
			Secret:     testSecret,
			Duration:   600,
		}
	case env.IsProd():
		conf = &config.MsgChanConfig{
			PlatformId: onlinePlatformId,
			Secret:     onlineSecret,
			Duration:   600,
		}
	}
	if conf == nil {
		logger.Panicf("no found msgchan config")
	}
	logger.Infof("msgchan conf %s", util.JsonStr(conf))

	msgChan.config = conf
	msgChan.signature = NewSignature(conf.Secret, time.Duration(conf.Duration)*time.Second)
	msgChan.signature.SetUserReflect(true)

	localConn, err := grpccli.NewLocalConn()
	if err != nil {
		logger.Errorf("error %v", err)
		return err
	}
	msgChan.localClient = basemsgcallin.NewSClient(localConn)
	logger.Infof("msgchan init local client success")

	return nil
}

func GetTokenData(msgData *TokenData) (data *basemsgcallin.MsgTokenData, err error) {
	req, err := makeGetTokenSignReq(msgData)
	if err != nil {
		logger.Errorf("GetToken error %v", err)
		return
	}
	var (
		resp *basemsgcallin.GetMsgConnTokenRsp
	)
	resp, err = msgChan.localClient.GetMsgConnToken(context.Background(), req)
	if err != nil {
		logger.Errorf("GetToken error %v", err)
		return
	}
	if resp.GetBase().GetCode() == RetOK {
		data = resp.Data
	} else {
		logger.Errorf("GetToken error %v", resp.GetBase().GetMsg())
		err = fmt.Errorf("error resp %s", resp.GetBase().GetMsg())
	}
	return
}

func PublishRawMsg(userid int64, excludeDevId string, data string) error {
	return publishRawMsg(userid, "", excludeDevId, data)
}

func PublishRawMsgToDid(userid int64, devId string, data string) error {
	return publishRawMsg(userid, devId, "", data)
}

func publishRawMsg(userid int64, devId, excludeDevId string, data string) error {
	nonceStr, err := uuid.GenerateUUID()
	if err != nil {
		nonceStr = fmt.Sprintf("%d%d", time.Now().UnixNano(), rand.Int63n(100000000)+100000000)
	}
	target := &basemsgcallin.PublishTargetInfo{
		UserId:           strconv.FormatInt(userid, 10),
		DeviceId:         devId,
		Topic:            "",
		ExcludeDeviceIds: nil,
	}
	if excludeDevId != "" {
		target.ExcludeDeviceIds = append(target.ExcludeDeviceIds, excludeDevId)
	}
	req := &basemsgcallin.PublishMsgReq{
		PlatformId: msgChan.config.PlatformId,
		TimeStamp:  fmt.Sprintf("%d", time.Now().Unix()),
		NonceStr:   nonceStr,
		Sign:       "",
		Payload:    data,
		Type:       PublishTypeSingleUser,
		TargetJson: util.JsonStr(target),
	}
	var resp *basemsgcallin.PublishMsgRsp
	defer func() {
		label := map[string]string{
			"result": "-1",
			"error":  "",
		}
		if err != nil {
			label["result"] = err.Error()
		}
		if resp != nil {
			label["result"] = fmt.Sprintf("%d", resp.GetBase().GetCode())
		}
		metric.CounterWithLabels("msgchan", label).Inc()
	}()
	sign, err := msgChan.signature.Sign(req)
	if err != nil {
		logger.Errorf("makePublishMsgSignReq error %v, %v", err, util.JsonStr(req))
		return err
	}
	req.Sign = sign
	resp, err = msgChan.localClient.PublishMsg(context.Background(), req)
	if err != nil {
		logger.Errorf("PublishMsg error %v", err)
		return err
	}
	if resp.GetBase().GetCode() != RetOK {
		logger.Errorf("PublishMsg error %v", resp.GetBase().GetMsg())
		return fmt.Errorf("error resp.Result %v", resp.GetBase().GetCode())
	}
	return err
}

func makeGetTokenSignReq(msgData *TokenData) (req *basemsgcallin.GetMsgConnTokenReq, err error) {
	nonceStr, err := uuid.GenerateUUID()
	if err != nil {
		nonceStr = fmt.Sprintf("%d%d", time.Now().UnixNano(), rand.Int63n(100000000)+100000000)
	}
	extras := msgData.Data
	req = &basemsgcallin.GetMsgConnTokenReq{
		PlatformId: msgChan.config.PlatformId,
		TimeStamp:  fmt.Sprintf("%d", time.Now().Unix()),
		NonceStr:   nonceStr,
		UserId:     fmt.Sprintf("%d", msgData.Userid),
		DeviceId:   msgData.Devid,
		ExtendData: string(extras),
	}

	sign, err := msgChan.signature.Sign(req)
	if err != nil {
		logger.Errorf("makeGetTokenSignReq error %v, %v", err, util.JsonStr(req))
		return
	}
	req.Sign = sign
	return
}

func Subscribe(userid int64, topic string) (err error) {
	req := &basemsgcallin.SubscribeTopicReq{
		PlatformId: msgChan.config.PlatformId,
		TimeStamp:  fmt.Sprintf("%d", time.Now().Unix()),
		NonceStr:   util.UUID(),
		Sign:       "",
		UserId:     strconv.FormatInt(userid, 10),
		DeviceId:   "",
		Topics:     util.JsonStr([]string{topic}),
	}
	sign, err := msgChan.signature.Sign(req)
	if err != nil {
		logger.Errorf("makePublishMsgSignReq error %v, %v", err, util.JsonStr(req))
		return err
	}
	req.Sign = sign

	resp, err := msgChan.localClient.SubscribeTopic(context.Background(), req)
	if err != nil {
		logger.Errorf("PublishMsg error %v", err)
		return err
	}
	if resp.GetBase().GetCode() != RetOK {
		logger.Errorf("PublishMsg error %v", resp.GetBase().GetMsg())
		if resp.GetBase().GetCode() == 180403 {
			// 长连接不存在
			return nil
		}
		return fmt.Errorf("error resp.Result %v", resp.GetBase().GetCode())
	}
	return err
}

func UnSubscribe(userid int64, topic string) (err error) {
	req := &basemsgcallin.UnSubscribeTopicReq{
		PlatformId: msgChan.config.PlatformId,
		TimeStamp:  fmt.Sprintf("%d", time.Now().Unix()),
		NonceStr:   util.UUID(),
		Sign:       "",
		UserId:     strconv.FormatInt(userid, 10),
		DeviceId:   "",
		Topics:     util.JsonStr([]string{topic}),
	}
	sign, err := msgChan.signature.Sign(req)
	if err != nil {
		logger.Errorf("makePublishMsgSignReq error %v, %v", err, util.JsonStr(req))
		return err
	}
	req.Sign = sign

	resp, err := msgChan.localClient.UnSubscribeTopic(context.Background(), req)
	if err != nil {
		logger.Errorf("PublishMsg error %v", err)
		return err
	}
	if resp.GetBase().GetCode() != RetOK {
		logger.Errorf("PublishMsg error %v", resp.GetBase().GetMsg())
		return fmt.Errorf("error resp.Result %v", resp.GetBase().GetCode())
	}
	return err
}

func UserTopics(userid int64) (topics []string, err error) {
	req := &basemsgcallin.UsersTopicsReq{
		PlatformId: msgChan.config.PlatformId,
		TimeStamp:  fmt.Sprintf("%d", time.Now().Unix()),
		NonceStr:   util.UUID(),
		Sign:       "",
		UserDevices: util.JsonStr([]basemsgcallin.UserDevice{
			{
				UserId:   strconv.FormatInt(userid, 10),
				DeviceId: "",
			},
		}),
	}
	sign, err := msgChan.signature.Sign(req)
	if err != nil {
		logger.Errorf("makePublishMsgSignReq error %v, %v", err, util.JsonStr(req))
		return
	}
	req.Sign = sign

	resp, err := msgChan.localClient.UsersTopics(context.Background(), req)
	if err != nil {
		logger.Errorf("PublishMsg error %v", err)
		return
	}
	if resp.GetBase().GetCode() != RetOK {
		logger.Errorf("PublishMsg error %v", resp.GetBase().GetMsg())
		err = fmt.Errorf("error resp.Result %v", resp.GetBase().GetCode())
		return
	}
	if resp.Data == nil {
		err = fmt.Errorf("error resp.Data nil")
		return
	}
	userTopic := resp.Data.UserTopics[strconv.FormatInt(userid, 10)]
	if userTopic == nil {
		return nil, nil
	}
	var filterMap = map[string]bool{}
	for _, data := range userTopic.List {
		for _, topic := range data.Topics {
			if !filterMap[topic] {
				topics = append(topics, topic)
				filterMap[topic] = true
			}
		}
	}
	return
}

func Broadcast(topic string, data string) (err error) {
	req := &basemsgcallin.PublishMsgReq{
		PlatformId: msgChan.config.PlatformId,
		TimeStamp:  fmt.Sprintf("%d", time.Now().Unix()),
		NonceStr:   util.UUID(),
		Sign:       "",
		Payload:    data,
		Type:       PublishTypeTopic,
		TargetJson: util.JsonStr(basemsgcallin.PublishTargetInfo{
			UserId:           "",
			DeviceId:         "",
			ExcludeDeviceIds: nil,
			Topic:            topic,
			IncludeUserIds:   nil,
			ExcludeUserIds:   nil,
			BroadcastRate:    0,
			LimitList:        nil,
		}),
	}
	var resp *basemsgcallin.PublishMsgRsp
	defer func() {
		label := map[string]string{
			"result": "-1",
			"error":  "",
		}
		if err != nil {
			label["result"] = err.Error()
		}
		if resp != nil {
			label["result"] = fmt.Sprintf("%d", resp.GetBase().GetCode())
		}
		metric.CounterWithLabels("msgchan_publish", label).Inc()
	}()
	sign, err := msgChan.signature.Sign(req)
	if err != nil {
		logger.Errorf("makePublishMsgSignReq error %v, %v", err, util.JsonStr(req))
		return err
	}
	req.Sign = sign
	resp, err = msgChan.localClient.PublishMsg(context.Background(), req)
	if err != nil {
		logger.Errorf("PublishMsg error %v", err)
		return err
	}
	if resp.GetBase().GetCode() != RetOK {
		logger.Errorf("PublishMsg error %v", resp.GetBase().GetMsg())
		return fmt.Errorf("error resp.Result %v", resp.GetBase().GetCode())
	}
	return err
}
