package alioss

import (
	"net/url"
	"new-gitlab.xunlei.cn/vcproject/backends/baselib/logger"
	"new-gitlab.xunlei.cn/vcproject/backends/baselib/server/env"
	"new-gitlab.xunlei.cn/vcproject/backends/proto/consts"
	"strings"
)

func CDNPhotoUrl(key string) string {
	if strings.HasPrefix(key, "http") {
		return key
	}
	key = strings.TrimPrefix(key, "/")
	return "https://" + consts.CDNImageDomain + "/" + key
}

func FullAvatarUrl(id string) string {
	return fullImageUrl(consts.MediaBizAvatar, id, false)
}

func FullAvatarPrivateUrl(id string) string {
	return fullImageUrl(consts.MediaBizAvatar, id, true)
}

func FullPhotoUrl(id string) string {
	return fullImageUrl(consts.MediaBizPhoto, id, false)
}

func FullPhotoPrivateUrl(id string) string {
	return fullImageUrl(consts.MediaBizPhoto, id, true)
}

func FullSignVoiceUrl(id string) string {
	return fullAudioUrl(consts.MediaBizSignVoice, id, false)
}

func FullSignVoicePrivateUrl(id string) string {
	return fullAudioUrl(consts.MediaBizSignVoice, id, true)
}

func FullCertImageUrl(id string) string {
	return fullImageUrl(consts.MediaBizCert, id, true)
}

func FullMomentImageUrl(id string) string {
	return fullImageUrl(consts.MediaBizMoment, id, false)
}

func FullMomentImagePrivateUrl(id string) string {
	return fullImageUrl(consts.MediaBizMoment, id, true)
}

func FullMomentVideoUrl(id string) string {
	return fullVideoUrl(consts.MediaBizMoment, id, false)
}

//func FullMomentVideoSnapshotUrl(id string) string {
//	return fullVideoSnapshotUrl(consts.MediaBizMoment, id, false)
//}
//
//func FullMomentVideoSnapshotPrivateUrl(id string) string {
//	return fullVideoSnapshotUrl(consts.MediaBizMoment, id, true)
//}

func FullMomentVideoPrivateUrl(id string) string {
	return fullVideoUrl(consts.MediaBizMoment, id, true)
}

func FullChatImageUrl(id string) string {
	return fullImageUrl(consts.MediaBizChat, id, true)
}

func FullBannerImageUrl(id string) string {
	return fullImageUrl(consts.MediaBizBanner, id, false)
}

func FullChatAudioUrl(id string) string {
	return fullAudioUrl(consts.MediaBizChat, id, true)
}

func FullChatVideoUrl(id string) string {
	return fullVideoUrl(consts.MediaBizChat, id, true)
}

func FullEmojisUrl(id string) string {
	return fullImageUrl(consts.MediaBizEmojis, id, false)
}

func FullGiftIconUrl(id string) string {
	return fullImageUrl(consts.MediaBizGift, id, false)
}

func FullGiftStyleUrl(id string) string {
	return fullVideoUrl(consts.MediaBizGift, id, false)
}

func FullActivityImageUrl(id string) string {
	return fullImageUrl(consts.MediaBizActivity, id, false)
}

func FullTaskImageUrl(id string) string {
	if strings.HasPrefix(id, "http") {
		return id
	}
	return fullImageUrl(consts.MediaBizTask, id, false)
}

func FullVoiceRoomBgUrl(id string) string {
	return fullImageUrl(consts.MediaBizVoiceBackground, id, false)
}

func FullVoiceRoomBgPrivateUrl(id string) string {
	return fullImageUrl(consts.MediaBizVoiceBackground, id, true)
}

func FullVoiceRoomCoverUrl(id string) string {
	return fullImageUrl(consts.MediaBizVoiceRoomCover, id, false)
}

func FullVoiceRoomCoverPrivateUrl(id string) string {
	return fullImageUrl(consts.MediaBizVoiceRoomCover, id, true)
}

// 内部送审头像缩略图
func FullReviewAvatarThumbnailPrivateUrl(id string) string {
	return fullReviewThumbnailPrivateUrl(consts.MediaBizAvatar, id, 500)
}

// 内部送审相册缩略图
func FullReviewPhotoThumbnailPrivateUrl(id string) string {
	return fullReviewThumbnailPrivateUrl(consts.MediaBizPhoto, id, 500)
}

// 内部送审动态缩略图
func FullReviewMomentThumbnailPrivateUrl(id string) string {
	return fullReviewThumbnailPrivateUrl(consts.MediaBizMoment, id, 500)
}

// 送审chat缩略图 (专属打招呼||im 聊天图片)
func FullReviewChatThumbnailPrivateUrl(id string) string {
	return fullReviewThumbnailPrivateUrl(consts.MediaBizChat, id, 500)
}

// 阿里云真人认证缩略图
func FullRealfaceThumbnailPrivateUrl(id string) string {
	return fullReviewThumbnailPrivateUrl(consts.MediaBizAvatar, id, 1000)
}

// 送审缩略图
func fullReviewThumbnailPrivateUrl(biz consts.MediaBiz, id string, size int) string {
	if strings.HasPrefix(id, "http") {
		return id
	}
	idLen := len(id)
	if idLen == 0 {
		return ""
	}
	return GetOssBucketImageThumbnailPrivateUrl(biz, id, size)
}

func fullImageUrl(biz consts.MediaBiz, id string, private bool) string {
	if strings.HasPrefix(id, "http") {
		return id
	}
	idLen := len(id)
	if idLen == 0 {
		return ""
	}
	if !private && !env.IsProd() {
		private = true
	}
	return GetOssBucketMediaUrl(consts.MediaTypeImage, biz, id)
}

func fullVideoUrl(biz consts.MediaBiz, id string, private bool) string {
	idLen := len(id)
	if idLen == 0 {
		return ""
	}
	if !private && !env.IsProd() {
		private = true
	}
	return GetOssBucketMediaUrl(consts.MediaTypeVideo, biz, id)
}

//func fullVideoSnapshotUrl(biz consts.MediaBiz, id string, private bool) string {
//	idLen := len(id)
//	if idLen == 0 {
//		return ""
//	}
//	if !private && !env.IsProd() {
//		private = true
//	}
//	return alioss.GetOssBucketVideoSnapshotUrl(consts.MediaTypeVideo, biz, id)
//}

func fullAudioUrl(biz consts.MediaBiz, id string, private bool) string {
	idLen := len(id)
	if idLen == 0 {
		return ""
	}
	if !private && !env.IsProd() {
		private = true
	}
	return GetOssBucketMediaUrl(consts.MediaTypeAudio, biz, id)
}

//func fullEffectUrl(biz consts.MediaBiz, id string, private bool) string {
//	idLen := len(id)
//	if idLen == 0 {
//		return ""
//	}
//	if !private && !env.IsProd() {
//		private = true
//	}
//	return alioss.GetOssBucketMediaUrl(consts.MediaTypeEffect, biz, id)
//}

func GetMediaUrl(typ consts.MediaType, biz consts.MediaBiz, id string) string {
	switch typ {
	case consts.MediaTypeImage:
		return fullImageUrl(biz, id, false)
	case consts.MediaTypeVideo:
		return fullVideoUrl(biz, id, false)
	case consts.MediaTypeAudio:
		return fullAudioUrl(biz, id, false)
		//case consts.MediaTypeEffect:
		//	return fullEffectUrl(biz, id, false)
	}
	return ""
}

func GetMediaUrlId(url string) string { //拿到纯id
	if len(url) > 0 {
		url = url[strings.LastIndex(url, "/")+1:]
		splits := strings.Split(url, ".")
		if len(splits) > 1 {
			return splits[0]
		}
		return url
	}
	return ""
}

func ParseMediaIdFromUrl(u string) string {
	pu, err := url.Parse(u)
	if err != nil {
		logger.Errorf("error %v parse url %s", err, u)
		return ""
	}
	return pu.Path[strings.LastIndex(pu.Path, "/")+1:]
}
