package alioss

import (
	"fmt"
	"new-gitlab.xunlei.cn/vcproject/backends/baselib/config"
	"new-gitlab.xunlei.cn/vcproject/backends/baselib/logger"
	"new-gitlab.xunlei.cn/vcproject/backends/baselib/util"
	"new-gitlab.xunlei.cn/vcproject/backends/proto/consts"
	"strings"
)

var (
	ossConfigs = make(map[string]*config.AliOssConfig)
)

func InitAliOss(aliOssConfigs []*config.AliOssConfig) (err error) {
	logger.Infof("InitAliOss: %v", util.JsonStr(aliOssConfigs))
	for _, param := range aliOssConfigs {
		ossConfigs[param.Name] = param
	}
	return nil
}

func GetAliOssConfig(name consts.MediaType) *config.AliOssConfig {
	return ossConfigs[name.String()]
}

func GetCDNDomain(typ consts.MediaType) string {
	if cfg, ok := ossConfigs[typ.String()]; ok {
		return cfg.Cdn
	}
	return ""
}

func FillCharacterAvatarUrl(uri string) string {
	if uri == "" {
		return ""
	}
	if strings.HasPrefix(uri, "http://") || strings.HasPrefix(uri, "https://") {
		return uri
	}
	return fmt.Sprintf("%s%s", GetCDNDomain(consts.MediaTypeImage), uri)
}

func FillImageUrl(uri string) string {
	if uri == "" {
		return ""
	}
	if strings.HasPrefix(uri, "http://") || strings.HasPrefix(uri, "https://") {
		return uri
	}
	return fmt.Sprintf("%s%s", GetCDNDomain(consts.MediaTypeImage), uri)
}

func FillAudioUrl(uri string) string {
	if uri == "" {
		return ""
	}
	if strings.HasPrefix(uri, "http://") || strings.HasPrefix(uri, "https://") {
		return uri
	}
	return fmt.Sprintf("%s%s", GetCDNDomain(consts.MediaTypeAudio), uri)
}

func RemoveDomainFromUrl(url string) string {
	cdnDomain := GetCDNDomain(consts.MediaTypeImage)
	if strings.HasPrefix(url, cdnDomain) {
		return strings.TrimPrefix(url, cdnDomain)
	}
	return url
}

func GetOssBucketMediaUrl(typ consts.MediaType, biz consts.MediaBiz, id string) string {
	return fmt.Sprintf("%s/%s/%s", GetCDNDomain(typ), biz.Dir, id)
}

// 审核缩略图
func GetOssBucketImageThumbnailPrivateUrl(biz consts.MediaBiz, id string, size int) string {
	typ := consts.MediaTypeImage
	return fmt.Sprintf("%s/%s/%s", GetCDNDomain(typ), biz.Dir, id)
}
