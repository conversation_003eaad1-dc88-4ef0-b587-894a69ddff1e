package alioss

import (
	"testing"
)

const (
	AccessKeyId     = "LTAI5tR6zjHnmpuTQAU5AGdk"
	AccessKeySecret = "******************************"
	EndPoint        = "https://oss-cn-shenzhen.aliyuncs.com"
	BucketName      = "vcproject-test"
	Prefix          = "speech_to_text/202504/9/"
)

func TestListObject(t *testing.T) {
	objectKeys, err := ListObject(AccessKeyId, AccessKeySecret, EndPoint, BucketName, Prefix, 2)
	if err != nil {
		t.<PERSON><PERSON><PERSON>("failed to list objects: %v", err)
	}
	t.Logf("objectKeys: %v", objectKeys)
}
