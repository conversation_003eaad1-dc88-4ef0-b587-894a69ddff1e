package alioss

import (
	"fmt"

	"github.com/aliyun/aliyun-oss-go-sdk/oss"
)

func ListObject(accessKeyId, accessKeySecret, endPoint, bucketName, prefix string, maxKeys int) (objectKeys []string, err error) {
	// 创建 OSS 客户端
	client, err := oss.New(endPoint, accessKeyId, accessKeySecret)
	if err != nil {
		return nil, fmt.Errorf("failed to create OSS client: %v", err)
	}
	// 获取存储桶
	bucket, err := client.Bucket(bucketName)
	if err != nil {
		return nil, err
	}
	if maxKeys <= 0 {
		maxKeys = 500
	}
	// 列出所有对象
	marker := ""
	for {
		result, err := bucket.ListObjectsV2(oss.Prefix(prefix), oss.MaxKeys(maxKeys), oss.ContinuationToken(marker))
		if err != nil {
			return nil, fmt.E<PERSON><PERSON>("failed to list objects: %v", err)
		}

		for _, object := range result.Objects {
			objectKeys = append(objectKeys, object.Key)
		}

		if !result.IsTruncated {
			break
		}
		marker = result.NextContinuationToken
	}
	return objectKeys, nil
}
