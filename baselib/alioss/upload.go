package alioss

import (
	"bytes"
	"fmt"

	"github.com/aliyun/aliyun-oss-go-sdk/oss"
)

func UploadBinaryFile(accessKeyId, accessKeySecret, endPoint, bucketName, objectName string, data []byte) error {
	// 创建 OSS 客户端
	client, err := oss.New(endPoint, accessKeyId, accessKeySecret)
	if err != nil {
		return fmt.Errorf("failed to create OSS client: %v", err)
	}

	// 获取存储桶
	bucket, err := client.Bucket(bucketName)
	if err != nil {
		return err
	}
	// 上传二进制数据
	err = bucket.PutObject(objectName, bytes.NewReader(data))
	if err != nil {
		return err
	}
	return nil
}
