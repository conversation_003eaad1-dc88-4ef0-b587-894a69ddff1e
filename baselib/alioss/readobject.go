package alioss

import (
	"bytes"
	"fmt"
	"io"

	"github.com/aliyun/aliyun-oss-go-sdk/oss"
)

func ReadObject(accessKeyId, accessKeySecret, endPoint, bucketName, objectName string) (data []byte, err error) {
	client, err := oss.New(endPoint, accessKeyId, accessKeySecret)
	if err != nil {
		return nil, fmt.Errorf("failed to create OSS client: %v", err)
	}
	// 获取存储桶
	bucket, err := client.Bucket(bucketName)
	if err != nil {
		return nil, err
	}
	body, err := bucket.GetObject(objectName)
	if err != nil {
		return nil, fmt.Errorf("failed to get object: %v", err)
	}
	defer body.Close()
	// 读取内容到内存
	buf := new(bytes.Buffer)
	if _, err := io.Copy(buf, body); err != nil {
		return nil, fmt.E<PERSON>rf("failed to read object: %v", err)
	}
	return buf.Bytes(), nil
}
