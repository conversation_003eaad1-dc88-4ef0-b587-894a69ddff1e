package ginserver

import (
	"fmt"
	"net/http"
	"runtime"

	"github.com/gin-gonic/gin"
	"new-gitlab.xunlei.cn/vcproject/backends/baselib/dingding"
	"new-gitlab.xunlei.cn/vcproject/backends/baselib/logger"
	"new-gitlab.xunlei.cn/vcproject/backends/baselib/server/env"
	"new-gitlab.xunlei.cn/vcproject/backends/baselib/util"
)

// StartWithServer 初始化并返回 HTTP 服务器实例
func StartWithServer() *http.Server {
	if env.IsProd() {
		gin.SetMode(gin.ReleaseMode)
	}

	g := gin.Default()
	g.NoRoute(noRouterHandler)
	g.NoMethod(noMethod)
	g.Use(Recover, GinLog)
	initRouter(g)

	srv := &http.Server{
		Addr:    ":9000",
		Handler: g,
	}

	// 在新的 goroutine 中启动服务器
	go func() {
		if err := srv.ListenAndServe(); err != nil && err != http.ErrServerClosed {
			logger.Fatalf("listen: %s\n", err)
		}
	}()

	return srv
}

func noRouterHandler(c *gin.Context) {
	defer util.Recover()
	path := c.Request.URL.Path
	method := c.Request.Method
	dingding.RobotClient().Send(dingding.DingDingMsg{
		MsgType: dingding.DINGDING_MSG_TYPE_TEXT,
		Text: dingding.Text{
			Content: env.GetServiceName() + " " + fmt.Sprintf("method %s path %s 未找到", method, path),
		},
		Link:     dingding.Link{},
		Markdown: dingding.Markdown{},
		At:       dingding.At{},
	})
}

func noMethod(c *gin.Context) {
	defer util.Recover()
	path := c.Request.URL.Path
	method := c.Request.Method
	dingding.RobotClient().Send(dingding.DingDingMsg{
		MsgType: dingding.DINGDING_MSG_TYPE_TEXT,
		Text: dingding.Text{
			Content: env.GetServiceName() + " " + fmt.Sprintf("method %s path %s 未找到", method, path),
		},
		Link:     dingding.Link{},
		Markdown: dingding.Markdown{},
		At:       dingding.At{},
	})
}

func Recover(c *gin.Context) {
	defer func() {
		if r := recover(); r != nil {
			buf := make([]byte, 10240)
			runtime.Stack(buf, false)
			logger.Errorf("panic: %v,\n%s", r, string(buf))
			c.Data(http.StatusInternalServerError, "text/html;charset=utf-8", buf)
		}
	}()
	c.Next()
}
