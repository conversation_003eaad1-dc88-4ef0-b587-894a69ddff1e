package ginserver

import (
	"bytes"
	"io"
	"strconv"
	"time"

	"github.com/gin-gonic/gin"
	"github.com/pborman/uuid"
	"new-gitlab.xunlei.cn/vcproject/backends/baselib/logger"
	"new-gitlab.xunlei.cn/vcproject/backends/baselib/metric"
)

func GinLog(c *gin.Context) {
	st := time.Now()
	uid := uuid.NewUUID().String()
	logger.SetCurrentTrace(uid)
	defer logger.DeleteCurrentTrace()
	w := &responseBodyWriter{body: &bytes.Buffer{}, ResponseWriter: c.Writer}
	c.Writer = w
	req, err := c.GetRawData()
	if err != nil {
		return
	}
	c.Request.Body = io.NopCloser(bytes.NewBuffer(req))
	c.Next()
	resp := w.body.String()
	path := c.Request.URL.Path
	url := c.Request.URL.String()
	logger.Infof("respStatus %d path: %s method:%s content-type:%s reqUrl:%s reqData: %s resp:%s", w.Status(), path,
		c.Request.Method, c.Request.Header.Get("Content-Type"),
		url,
		string(req), resp)
	var status = strconv.FormatInt(int64(w.status), 10)
	metric.CounterWithLabels("http_request", map[string]string{
		"method": c.Request.Method,
		"path":   path,
		"status": status,
	}).Inc()
	metric.HistogramLabelsWithBuckets("http_request_bucket", map[string]string{
		"method": c.Request.Method,
		"path":   path,
		"status": status,
	}, []float64{0, 10, 50, 100, 200, 300, 400, 500, 750, 1000, 2000}).Observe(time.Since(st).Seconds() * 1000)
}

type responseBodyWriter struct {
	gin.ResponseWriter
	status int
	body   *bytes.Buffer
}

func (r *responseBodyWriter) Write(b []byte) (int, error) {
	r.body.Write(b)
	return r.ResponseWriter.Write(b)
}

func (r *responseBodyWriter) WriteHeader(statusCode int) {
	r.status = statusCode
	r.ResponseWriter.WriteHeader(statusCode)
}
