package config

import (
	"flag"
	"fmt"

	"github.com/spf13/viper"
	"new-gitlab.xunlei.cn/vcproject/backends/baselib/server/env"
)

var (
	serverConfig = ServerConfig{}

	Conf         string
	TranslateDir string
)

func init() {
	flag.StringVar(&Conf, "conf", "deploy/config.yml", "config file")
	flag.StringVar(&TranslateDir, "translate", "configs/i18n", "translate dir")
}

func GetServerConfig() *ServerConfig {
	return &serverConfig
}

func Init() {

	if env.IsTest() || env.IsProd() {
		initRemote()
		return
	}
	// 保证本地测试服务启动无需调整
	initLocal()
}

func initLocal() {
	flag.Parse()
	viper.SetConfigType("yaml")
	viper.SetConfigFile(Conf)
	checkAndPanic("addr", viper.BindEnv("addr", "MODULES_XLSOA_SERVER_CONTEXT_ADDR"))
	checkAndPanic("prometheus.addr", viper.BindEnv("prometheus.addr", "MODULES_XLSOA_ENVIRONMENT_PROMETHEUS_LISTEN_ADDR"))
	checkAndPanic("prometheus.metrics_path", viper.BindEnv("prometheus.metrics_path", "MODULES_XLSOA_ENVIRONMENT_PROMETHEUS_PATH"))
	err := viper.ReadInConfig()
	if err != nil {
		panic(fmt.Errorf("error read config %s error %v", Conf, err))
	}
	err = viper.Unmarshal(&serverConfig)
	if err != nil {
		panic(fmt.Errorf("errpr viper.Unnarshal %v", err))
	}
}

func initRemote() {
	checkAndPanic("addr", viper.BindEnv("addr", "MODULES_XLSOA_SERVER_CONTEXT_ADDR"))
	checkAndPanic("prometheus.addr", viper.BindEnv("prometheus.addr", "MODULES_XLSOA_ENVIRONMENT_PROMETHEUS_LISTEN_ADDR"))
	checkAndPanic("prometheus.metrics_path", viper.BindEnv("prometheus.metrics_path", "MODULES_XLSOA_ENVIRONMENT_PROMETHEUS_PATH"))
	flag.Parse()
	viper.SetConfigType("yaml")
	viper.SetConfigFile(Conf)
	err := viper.ReadInConfig()
	if err != nil {
		panic(fmt.Errorf("error read config %s error %v", Conf, err))
	}
	InitVipers()
	err = viper.Unmarshal(&serverConfig)
	if err != nil {
		panic(fmt.Errorf("errpr viper.Unnarshal %v", err))
	}
	serverConfig.FilterConfigWithLabels()
}

func checkAndPanic(msg string, err error) {
	if err != nil {
		panic(fmt.Sprintf("error "+msg+" :%v", err))
	}
}

type ServerConfig struct {
	Name              string             `yaml:"name"`
	Addr              string             `yaml:"addr"`
	Logger            string             `yaml:"logger"`
	LogLevel          string             `yaml:"log-level" mapstructure:"log-level"`
	Zap               *ZapConfig         `yaml:"zap"`
	Prometheus        PrometheusConfig   `yaml:"prometheus"`
	Jaeger            *JaegerConfig      `yaml:"jaeger"`
	Mysql             []*MysqlConfig     `yaml:"mysql"`
	SlaveMysql        []*MysqlConfig     `yaml:"slave_mysql" mapstructure:"slave_mysql"`
	Clickhouse        []ClickhouseConfig `yaml:"clickhouse" mapstructure:"clickhouse"`
	Redis             []*RedisConfig     `yaml:"redis"`
	Mongodb           *MongodbConfig     `yaml:"mongodb"`
	Nsq               NsqConfig          `yaml:"nsq"`
	ConfigLabels      []string           `mapstructure:"config_labels" yaml:"config_labels"`
	ConfigSlaveLabels []string           `mapstructure:"config_slave_labels" yaml:"config_slave_labels"`
	Aws               []*S3Config        `yaml:"aws" mapstructure:"aws"`
	AliOss            []*AliOssConfig    `yaml:"ali_oss" mapstructure:"ali_oss"`
	Core              *CoreConfig        `yaml:"core"`
	ConsulAddr        string             `yaml:"consul_addr" mapstructure:"consul_addr"`
	JwtSecret         string             `yaml:"jwt_secret" mapstructure:"jwt_secret"`
	SmsConfig         SmsConfig          `yaml:"sms" mapstructure:"sms"`
	Asr               *AsrConfig         `yaml:"asr" mapstructure:"asr"`
	Signature         *SignatureConfig   `yaml:"signature" mapstructure:"signature"`
}

type S3Config struct {
	Name         string `json:"name" yaml:"name" mapstructure:"name"`
	AccessKey    string `json:"access_key" yaml:"access_key" mapstructure:"access_key"`
	SecrectKey   string `json:"secret_key" yaml:"secret_key" mapstructure:"secret_key"`
	Region       string `json:"region" yaml:"region" mapstructure:"region"`
	Distribution string `json:"distribution" yaml:"distribution" mapstructure:"distribution"`
	ExpSeconds   int    `json:"exp_seconds" yaml:"exp_seconds" mapstructure:"exp_seconds"`
	BucketName   string `json:"bucket_name" yaml:"bucket_name" mapstructure:"bucket_name"`
	Cdn          string `json:"cdn" yaml:"cdn" mapstructure:"cdn"`
	RoleArn      string `json:"role_arn" yaml:"role_arn" mapstructure:"role_arn"`
	SessionName  string `json:"session_name" yaml:"session_name" mapstructure:"session_name"`
}

type PrometheusConfig struct {
	Addr        string `yaml:"addr"`
	MetricsPath string `yaml:"metrics_path"`
}

type JaegerConfig struct {
	ServiceName string  `yaml:"service_name"`
	Endpoint    string  `yaml:"endpoint"`
	Enable      bool    `yaml:"enable"`
	SampleRate  float64 `yaml:"sample_rate" mapstructure:"sample_rate"`
}

type CoreConfig struct {
	Domain *DomainConfig `yaml:"domain"`
	Agora  *AgoraConfig  `yaml:"agora"`
}

type DomainConfig struct {
	App string     `yaml:"app"`
	H5  string     `yaml:"h5"`
	Cdn *CdnDomain `yaml:"cdn"`
}

type CdnDomain struct {
	Image string `yaml:"image"`
	Video string `yaml:"video"`
	Voice string `yaml:"voice"`
	Data  string `yaml:"data"`
}

type AgoraConfig struct {
	Appid         string `yaml:"appid"`
	Secrect       string `yaml:"secrect"`
	RestfulKey    string `yaml:"restful_key" mapstructure:"restful_key"`
	RestfulSecret string `yaml:"restful_secret" mapstructure:"restful_secret"`
	RecordSecret  string `yaml:"record_secret" mapstructure:"record_secret"`
	ChannelSecret string `yaml:"channel_secret" mapstructure:"channel_secret"`
}

// SmsConfig 存储阿里云SMS配置信息
type SmsConfig struct {
	AccessKeyID     string `json:"access_key_id" yaml:"access_key_id" mapstructure:"access_key_id"`
	AccessKeySecret string `json:"access_key_secret" yaml:"access_key_secret" mapstructure:"access_key_secret"`
	SignName        string `json:"sign_name" yaml:"sign_name" mapstructure:"sign_name"`
	TemplateCode    string `json:"template_code" yaml:"template_code" mapstructure:"template_code"`
}

type AliOssConfig struct {
	Name            string `json:"name" yaml:"name" mapstructure:"name"`
	AccessKeyId     string `json:"access_key_id" yaml:"access_key_id" mapstructure:"access_key_id"`
	AccessKeySecret string `json:"access_key_secret" yaml:"access_key_secret" mapstructure:"access_key_secret"`
	Endpoint        string `json:"endpoint" yaml:"endpoint" mapstructure:"endpoint"`
	Bucket          string `json:"bucket" yaml:"bucket" mapstructure:"bucket"`
	Cdn             string `json:"cdn" yaml:"cdn" mapstructure:"cdn"`
	UrlExpire       int64  `json:"url_expire" yaml:"url_expire" mapstructure:"url_expire"`
}

// FilterConfigWithLabels 根据标签过滤某些配置
func (s *ServerConfig) FilterConfigWithLabels() {
	var listToMap = func(list []string) map[string]struct{} {
		result := make(map[string]struct{})
		for _, item := range list {
			result[item] = struct{}{}
		}
		return result
	}

	var (
		mysqlDBs      []*MysqlConfig
		slaveMysqlDBs []*MysqlConfig
		redisList     []*RedisConfig
	)

	labels, err := ParseLabels(s.ConfigLabels)
	if err != nil {
		fmt.Printf("ParseLabels err:%v\n", err)
		return
	}

	for scheme, items := range labels {
		switch scheme {
		case Mysql:
			var itemMap = listToMap(items)
			for _, db := range s.Mysql {
				if _, ok := itemMap[db.DbName]; ok {
					mysqlDBs = append(mysqlDBs, db)
				}
			}
		case Redis:
			var itemMap = listToMap(items)
			for _, db := range s.Redis {
				if _, ok := itemMap[db.Name]; ok {
					redisList = append(redisList, db)
				}
			}
		}
	}

	slaveLabels, err := ParseLabels(s.ConfigSlaveLabels)
	if err != nil {
		fmt.Printf("ParseLabels err:%v\n", err)
		return
	}

	for scheme, items := range slaveLabels {
		switch scheme {
		case Mysql:
			var itemMap = listToMap(items)
			for _, db := range s.SlaveMysql {
				if _, ok := itemMap[db.DbName]; ok {
					slaveMysqlDBs = append(slaveMysqlDBs, db)
				}
			}
		}
	}
	s.Mysql = mysqlDBs
	s.SlaveMysql = slaveMysqlDBs
	s.Redis = redisList
}

// SignatureConfig 接口签名验证配置（核心配置，通过config.yaml管理）
type SignatureConfig struct {
	Enable              bool              `yaml:"enable" mapstructure:"enable"`                               // 是否启用签名验证
	DefaultSecret       string            `yaml:"default_secret" mapstructure:"default_secret"`               // 默认签名密钥
	PlatformSecrets     map[string]string `yaml:"platform_secrets" mapstructure:"platform_secrets"`           // 平台特定密钥配置
	TimeWindow          int64             `yaml:"time_window" mapstructure:"time_window"`                     // 时间窗口（秒），默认300秒
	RequiredFields      []string          `yaml:"required_fields" mapstructure:"required_fields"`             // 必须包含的签名字段
	ExcludeFields       []string          `yaml:"exclude_fields" mapstructure:"exclude_fields"`               // 签名时排除的字段
	Algorithm           string            `yaml:"algorithm" mapstructure:"algorithm"`                         // 签名算法，默认HMAC-SHA256
	SkipMethods         []string          `yaml:"skip_methods" mapstructure:"skip_methods"`                   // 跳过签名验证的方法列表（兼容配置，建议迁移到Consul）
	SkipServicePrefixes []string          `yaml:"skip_service_prefixes" mapstructure:"skip_service_prefixes"` // 跳过签名验证的服务前缀列表（兼容配置，建议迁移到Consul）
	UniversalSignature  string            `yaml:"universal_signature" mapstructure:"universal_signature"`     // 万能签名（兼容配置，建议迁移到Consul）
}

// SignatureRulesConfig 签名验证规则配置（动态配置，通过Consul管理）
type SignatureRulesConfig struct {
	SkipMethods         []string `yaml:"skip_methods" json:"skip_methods"`                   // 跳过签名验证的方法列表
	SkipServicePrefixes []string `yaml:"skip_service_prefixes" json:"skip_service_prefixes"` // 跳过签名验证的服务前缀列表
	UniversalSignature  string   `yaml:"universal_signature" json:"universal_signature"`     // 万能签名，当请求头包含此签名时直接跳过验证
}
