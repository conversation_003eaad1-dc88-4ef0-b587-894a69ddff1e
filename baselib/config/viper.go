package config

import (
	"fmt"
	"os"
	"path/filepath"
	"strings"

	"github.com/spf13/viper"
)

var pathDir = "configs"

// var configPaths = []string{
// 	filepath.Join(pathDir, "mysql.yaml"),
// 	filepath.Join(pathDir, "redis.yaml"),
// 	filepath.Join(pathDir, "nsq.yaml"),
// 	filepath.Join(pathDir, "mongo.yaml"),
// 	filepath.Join(pathDir, "jaeger.yaml"),
// 	filepath.Join(pathDir, "core.yaml"),
// 	filepath.Join(pathDir, "aws.yaml"),
// }

func InitVipers() {
	// 获取当前目录
	currentDir, err := os.Getwd()
	if err != nil {
		fmt.Printf("get current directory error: %v\n", err)
		return
	}
	currentDir = filepath.Join(currentDir, pathDir)
	fmt.Printf("InitVipers currentDir=%s \n", currentDir)
	// 扫描当前目录下的文件
	files, err := os.ReadDir(currentDir)
	if err != nil {
		fmt.Printf("read directory error: %v currentDir=%s\n", err, currentDir)
		return
	}
	// 遍历所有文件
	for _, file := range files {
		if file.IsDir() {
			continue
		}
		filename := file.Name()
		// 检查文件扩展名
		if strings.HasSuffix(filename, ".yaml") || strings.HasSuffix(filename, ".yml") {
			filePath := filepath.Join(pathDir, filename)
			viper.SetConfigFile(filePath)
			if err = viper.MergeInConfig(); err != nil {
				fmt.Printf("init config viper err:%v, config name:%s\n", err, filePath)
				continue // 继续处理下一个文件，而不是直接返回
			}
			fmt.Printf("loaded config file: %s\n", filename)
		}
	}
}
