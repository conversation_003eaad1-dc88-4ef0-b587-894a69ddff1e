package httpcli

import (
	"context"
	"errors"
	"net/http"
	"sync"
	"time"
)

// HTTPClientPool HTTP客户端连接池
type HTTPClientPool struct {
	// 配置选项
	config PoolConfig

	// 客户端连接池
	clients chan *http.Client

	// 保护共享资源
	mu sync.RWMutex

	// 连接池状态
	closed bool
}

// PoolConfig 连接池配置
type PoolConfig struct {
	// 最大连接数
	MaxSize int

	// 初始连接数
	InitialSize int

	// 连接超时时间
	ConnTimeout time.Duration

	// 空闲超时时间
	IdleTimeout time.Duration

	// 每个Host的最大连接数
	MaxConnsPerHost int

	// 自定义Transport配置
	Transport *http.Transport
}

// DefaultPoolConfig 默认连接池配置
var DefaultPoolConfig = PoolConfig{
	MaxSize:         100,
	InitialSize:     10,
	ConnTimeout:     5 * time.Second,
	IdleTimeout:     60 * time.Second,
	MaxConnsPerHost: 10,
	Transport: &http.Transport{
		MaxIdleConns:        100,
		MaxIdleConnsPerHost: 10,
		IdleConnTimeout:     90 * time.Second,
		DisableKeepAlives:   false,
		DisableCompression:  false,
	},
}

// NewHTTPClientPool 创建新的HTTP客户端连接池
func NewHTTPClientPool(config *PoolConfig) (*HTTPClientPool, error) {
	if config == nil {
		config = &DefaultPoolConfig
	}
	if config.Transport == nil {
		config.Transport = &http.Transport{
			MaxIdleConns:        100,
			MaxIdleConnsPerHost: 10,
			IdleConnTimeout:     90 * time.Second,
			DisableKeepAlives:   false,
			DisableCompression:  false,
		}
	}
	pool := &HTTPClientPool{
		config:  *config,
		clients: make(chan *http.Client, config.MaxSize),
		closed:  false,
	}

	// 初始化连接池
	for i := 0; i < config.InitialSize; i++ {
		client := pool.createClient()
		pool.clients <- client
	}

	return pool, nil
}

// createClient 创建新的HTTP客户端
func (p *HTTPClientPool) createClient() *http.Client {
	return &http.Client{
		Transport: p.config.Transport,
		Timeout:   p.config.ConnTimeout,
	}
}

// GetClient 从连接池获取客户端
func (p *HTTPClientPool) GetClient(ctx context.Context) (*http.Client, error) {
	if p.closed {
		return nil, ErrPoolClosed
	}

	select {
	case client := <-p.clients:
		return client, nil
	case <-ctx.Done():
		return nil, ctx.Err()
	default:
		// 如果没有可用的客户端且未达到最大连接数，创建新的客户端
		p.mu.Lock()
		defer p.mu.Unlock()

		if len(p.clients) < p.config.MaxSize {
			client := p.createClient()
			return client, nil
		}

		// 等待可用的客户端
		select {
		case client := <-p.clients:
			return client, nil
		case <-ctx.Done():
			return nil, ctx.Err()
		}
	}
}

// ReleaseClient 释放客户端回连接池
func (p *HTTPClientPool) ReleaseClient(client *http.Client) error {
	if p.closed {
		return ErrPoolClosed
	}

	select {
	case p.clients <- client:
		return nil
	default:
		// 如果连接池已满，直接丢弃
		return nil
	}
}

// Close 关闭连接池
func (p *HTTPClientPool) Close() error {
	p.mu.Lock()
	defer p.mu.Unlock()

	if p.closed {
		return ErrPoolClosed
	}

	p.closed = true
	close(p.clients)
	return nil
}

// 定义错误
var (
	ErrPoolClosed = errors.New("connection pool is closed")
)
