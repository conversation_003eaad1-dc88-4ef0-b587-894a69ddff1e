package httpcli

import (
	"bytes"
	"context"
	"encoding/json"
	"io"
	"net/http"
	"time"
)

// HTTPClient HTTP客户端封装
type HTTPClient struct {
	pool *HTTPClientPool
}

// NewHTTPClient 创建新的HTTP客户端
func NewHTTPClient(config *PoolConfig) (*HTTPClient, error) {
	pool, err := NewHTTPClientPool(config)
	if err != nil {
		return nil, err
	}

	return &HTTPClient{pool: pool}, nil
}

// Request HTTP请求选项
type Request struct {
	Method  string
	URL     string
	Headers map[string]string
	Body    interface{}
	Timeout time.Duration
}

// Response HTTP响应
type Response struct {
	StatusCode int
	Headers    http.Header
	Body       []byte
}

// Do 执行HTTP请求
func (c *HTTPClient) Do(ctx context.Context, req *Request) (*Response, error) {
	if req.Timeout > 0 {
		var cancel context.CancelFunc
		ctx, cancel = context.WithTimeout(ctx, req.Timeout)
		defer cancel()
	}

	// 获取客户端
	client, err := c.pool.GetClient(ctx)
	if err != nil {
		return nil, err
	}
	defer c.pool.ReleaseClient(client)

	// 准备请求体
	var bodyReader io.Reader
	if req.Body != nil {
		var bodyBytes []byte
		switch v := req.Body.(type) {
		case []byte:
			bodyBytes = v
		case string:
			bodyBytes = []byte(v)
		default:
			bodyBytes, err = json.Marshal(v)
			if err != nil {
				return nil, err
			}
		}
		bodyReader = bytes.NewReader(bodyBytes)
	}
	// 创建请求
	httpReq, err := http.NewRequestWithContext(ctx, req.Method, req.URL, bodyReader)
	if err != nil {
		return nil, err
	}
	// 设置请求头
	for k, v := range req.Headers {
		httpReq.Header.Set(k, v)
	}
	// 如果是JSON请求，设置Content-Type
	if req.Body != nil {
		httpReq.Header.Set("Content-Type", "application/json")
	}
	// 执行请求
	resp, err := client.Do(httpReq)
	if err != nil {
		return nil, err
	}
	defer resp.Body.Close()
	// 读取响应体
	body, err := io.ReadAll(resp.Body)
	if err != nil {
		return nil, err
	}
	return &Response{
		StatusCode: resp.StatusCode,
		Headers:    resp.Header,
		Body:       body,
	}, nil
}

// Get 发送GET请求
func (c *HTTPClient) Get(ctx context.Context, url string, headers map[string]string, timeout time.Duration) (*Response, error) {
	return c.Do(ctx, &Request{
		Method:  http.MethodGet,
		URL:     url,
		Headers: headers,
		Timeout: timeout,
	})
}

// Post 发送POST请求
func (c *HTTPClient) Post(ctx context.Context, url string, headers map[string]string, body interface{}, timeout time.Duration) (*Response, error) {
	return c.Do(ctx, &Request{
		Method:  http.MethodPost,
		URL:     url,
		Headers: headers,
		Body:    body,
		Timeout: timeout,
	})
}
