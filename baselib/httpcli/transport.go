package httpcli

import (
	"net/http"
	"time"
)

var (
	Transport = &http.Transport{
		Proxy:                  nil,
		DialContext:            nil,
		Dial:                   nil,
		DialTLSContext:         nil,
		DialTLS:                nil,
		TLSClientConfig:        nil,
		TLSHandshakeTimeout:    0,
		Disable<PERSON><PERSON>Alives:      false,
		DisableCompression:     false,
		MaxIdleConns:           1024,
		MaxIdleConnsPerHost:    128,
		MaxConnsPerHost:        0,
		IdleConnTimeout:        time.Minute * 8,
		ResponseHeaderTimeout:  0,
		ExpectContinueTimeout:  0,
		TLSNextProto:           nil,
		ProxyConnectHeader:     nil,
		GetProxyConnectHeader:  nil,
		MaxResponseHeaderBytes: 0,
		WriteBufferSize:        0,
		ReadBufferSize:         0,
		ForceAttemptHTTP2:      false,
	}
)
