package httpcli

import (
	"bytes"
	"fmt"
	"io"
	"net/http"
	"net/url"
	"testing"
	"time"

	"new-gitlab.xunlei.cn/vcproject/backends/baselib/logger"
	"new-gitlab.xunlei.cn/vcproject/backends/baselib/util"
)

func TestTimeout(t *testing.T) {
	logger.InitLogger("/dev/stdout")
	defer logger.Sync()
	GetTe("https://www.baidu.com", nil, nil)
}

func GetTe(originUrl string, headers, data map[string]string) (body string, err error) {
	u, err := url.Parse(originUrl)
	if err != nil {
		logger.Errorf("error parse url %s error %v", originUrl, err)
		return "", err
	}
	values, err := url.ParseQuery(u.RawQuery)
	if err != nil {
		logger.Errorf("error parse query param %s %v", u.RawQuery, err)
		return "", err
	}
	for k, v := range data {
		values.Set(k, v)
	}
	u.RawQuery = values.Encode()

	req, err := http.NewRequest(http.MethodGet, u.String(), bytes.NewBufferString(util.JsonStr(data)))
	if err != nil {
		logger.Errorf("error %v", err)
		return "", err
	}
	req.Header.Set("Content-Type", "application/json")
	for k, v := range headers {
		req.Header.Set(k, v)
	}
	client := http.Client{
		Transport: Transport,
		Timeout:   time.Millisecond,
	}
	resp, err := client.Do(req)
	if err != nil {
		logger.Errorf("error %v", err)
		return "", err
	}
	defer resp.Body.Close()
	if resp.StatusCode != http.StatusOK {
		logger.Errorf("error status %s", resp.Status)
		return "", fmt.Errorf("http status %d", resp.StatusCode)
	}
	c, err := io.ReadAll(resp.Body)
	if err != nil {
		logger.Errorf("error %v", err)
		return "", err
	}
	return string(c), nil
}
