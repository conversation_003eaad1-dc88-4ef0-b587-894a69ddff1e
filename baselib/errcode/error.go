package errcode

import (
	"errors"
	"fmt"
	"reflect"
	"strconv"

	"google.golang.org/genproto/googleapis/rpc/errdetails"
	"google.golang.org/grpc/codes"
	"google.golang.org/grpc/status"
	"google.golang.org/protobuf/types/known/anypb"
	"new-gitlab.xunlei.cn/vcproject/backends/proto/api/common"
)

type SvcBaseResp interface {
	GetBase() *common.SvcBaseResp
}

type Error struct {
	Code int32  `json:"code"`
	Msg  string `json:"msg"`
}

func (e *Error) Error() string {
	return fmt.Sprintf("code=%d, message=%s", e.Code, e.Msg)
}

func (e *Error) GRPCStatus() *status.Status {
	st := status.New(codes.Unknown, e.Msg)
	st, err := st.WithDetails(&errdetails.ErrorInfo{
		Reason:   fmt.Sprint(e.Code),
		Metadata: map[string]string{"message": e.Msg},
	})
	if err != nil {
		return status.New(codes.Unknown, e.Msg)
	}
	return st
}

func New(code int32, message string) *Error {
	return &Error{
		Code: code,
		Msg:  message,
	}
}

func (e *Error) ToBizBaseResp() (*common.BizBaseResp, error) {
	var data *anypb.Any
	return &common.BizBaseResp{
		Code: e.Code,
		Msg:  e.Msg,
		Data: data,
	}, nil
}

func (e *Error) WithError(err error) *Error {
	return &Error{
		Code: e.Code,
		Msg:  fmt.Sprintf("%s: %v", e.Msg, err),
	}
}

func (e *Error) WithMessage(msg string) *Error {
	return &Error{
		Code: e.Code,
		Msg:  msg,
	}
}

func FromError(err error) *Error {
	var e *Error
	if errors.As(err, &e) {
		return e
	}

	st, ok := status.FromError(err)
	if !ok {
		return ErrorInternal.WithMessage(err.Error())
	}

	details := st.Details()
	if len(details) == 0 {
		return ErrorInternal.WithMessage(st.Message())
	}

	info, ok := details[0].(*errdetails.ErrorInfo)
	if !ok {
		return ErrorInternal.WithMessage(st.Message())
	}

	code, _ := strconv.Atoi(info.Reason)
	return New(int32(code), info.Metadata["message"])
}

func (e *Error) SetTo(resp interface{}, data ...interface{}) {
	if resp == nil {
		return
	}
	v := reflect.ValueOf(resp).Elem()
	v.FieldByName("Code").SetInt(int64(e.Code))
	v.FieldByName("Msg").SetString(e.Msg)
	if len(data) > 0 && data[0] != nil {
		v.FieldByName("Data").Set(reflect.ValueOf(data[0]))
	}
}

func (e *Error) SetRespWithMsg(resp interface{}, errMsg string, data ...interface{}) {
	if resp == nil {
		return
	}
	msg := e.Msg
	if len(errMsg) > 0 {
		msg = errMsg
	}
	v := reflect.ValueOf(resp).Elem()
	v.FieldByName("Code").SetInt(int64(e.Code))
	v.FieldByName("Msg").SetString(msg)
	if len(data) > 0 && data[0] != nil {
		v.FieldByName("Data").Set(reflect.ValueOf(data[0]))
	}
}

func (e *Error) ToSvcBaseResp() *common.SvcBaseResp {
	return &common.SvcBaseResp{
		Code: e.Code,
		Msg:  e.Msg,
	}
}

func IsOk(s SvcBaseResp) bool {
	return ErrOK.EqualResp(s)
}

func NotOk(s SvcBaseResp) bool {
	return !ErrOK.EqualResp(s)
}

func (e *Error) EqualResp(rsp SvcBaseResp) bool {
	return rsp.GetBase() != nil && e.Code == rsp.GetBase().GetCode()
}
