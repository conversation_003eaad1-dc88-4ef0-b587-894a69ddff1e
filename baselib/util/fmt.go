package util

import (
	"fmt"
	"strings"
)

// FormatAmount 将分（*100）的单位格式化为元，保留两位小数
// 例如输入 123456 返回 "1234.56"
func FormatAmount(amountInCents int64) string {
	if amountInCents == 0 {
		return "0"
	}

	amountInYuan := float64(amountInCents) / 100.0
	str := fmt.Sprintf("%.2f", amountInYuan)

	// 去掉结尾的 .00（如果存在）
	if strings.HasSuffix(str, ".00") {
		return strings.TrimSuffix(str, ".00")
	}
	return str
}
