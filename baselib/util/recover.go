package util

import (
	"runtime"

	"new-gitlab.xunlei.cn/vcproject/backends/baselib/dingding"
	"new-gitlab.xunlei.cn/vcproject/backends/baselib/logger"
	"new-gitlab.xunlei.cn/vcproject/backends/baselib/server/env"
)

// 避免野生goroutine 滥用
func RecoverFunc(f func() error) {
	defer func() {
		err := recover()
		if err != nil {
			buf := make([]byte, 10240)
			n := runtime.Stack(buf, false)
			buf = buf[:n]
			logger.Errorf("panic: %v,\n%s", err, string(buf))
		}
	}()
	f()
}

func RecoverNotSendDingding() {
	defer func() {
		err := recover()
		if err != nil {
			buf := make([]byte, 10240)
			n := runtime.Stack(buf, false)
			buf = buf[:n]
			logger.Errorf("panic: %v,\n%s", err, string(buf))
		}
	}()
}

func Recover() {
	err := recover()
	if err != nil {
		buf := make([]byte, 10240)
		runtime.Stack(buf, false)
		logger.Errorf("panic: %v,\n%s", err, string(buf))
		go dingding.RobotClient().Send(dingding.DingDingMsg{
			MsgType: dingding.DINGDING_MSG_TYPE_TEXT,
			Text: dingding.Text{
				Content: "host " + env.GetServiceName() + "\n\n\n" +
					"env " + string(env.GetEnvironment()) + "\n\n\n" +
					string(buf),
			},
		})
	}
}
