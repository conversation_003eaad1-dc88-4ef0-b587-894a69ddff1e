package util

import (
	"bytes"
	"crypto/aes"
	"crypto/cipher"
	"crypto/md5"
	crand "crypto/rand"
	"crypto/sha1"
	"crypto/sha256"
	"encoding/base64"
	"encoding/binary"
	"encoding/hex"
	"encoding/json"
	"errors"
	"fmt"
	"hash/crc32"
	"hash/fnv"
	"io"
	"math/rand"
	"strconv"
	"strings"
	"time"
	"unsafe"

	"github.com/google/uuid"
	"google.golang.org/protobuf/encoding/protojson"
	"google.golang.org/protobuf/proto"
	"new-gitlab.xunlei.cn/vcproject/backends/proto/consts"
)

const (
	numbersAndLetters = "0123456789abcdefghijklmnopqrstuvwxyz"
	memberAesKey      = "dv2qOj0CAvd*#rVGFoJxbgO$#n9Y0751"
)

func UUID() string {
	return uuid.New().String()
}

func TimeUUID() string {
	return fmt.Sprintf("%s-%s", UUID(), IntToCode(NowTimeNano()/1000))
}

func NowTimeNano() int64 {
	return time.Now().UnixNano()
}

func NowTimeMillis() int64 {
	return time.Now().UnixMilli()
}

func NowTime() int64 {
	return time.Now().Unix()
}

func NowYmd() string {
	return time.Now().Format(consts.TimeFormatYMD)
}

func NowYmdTZ(loc *time.Location) string {
	// @todo TimeFormatYMD2 后续改成 TimeFormatYMD
	if loc != nil {
		return time.Now().In(loc).Format(consts.TimeFormatYMD2)
	}
	return time.Now().Format(consts.TimeFormatYMD2)
}

func NowYmdH() string {
	return time.Now().Format(consts.TimeFormatYMDH)
}

func NowYmdHTZ(loc *time.Location) string {
	if loc != nil {
		return time.Now().In(loc).Format(consts.TimeFormatYMDH)
	}
	return NowYmdH()

}

func YesterdayYmd() string {
	return time.Now().Add(time.Hour * -24).Format(consts.TimeFormatYMD)
}

func NowYmdhms() string {
	return time.Now().Format(consts.TimeFormatYMDHMS)
}

func NowYmdhms2() string {
	return time.Now().Format(consts.TimeFormatYMDHMS2)
}

func NowWeekDate(now time.Time) string {
	if now.IsZero() {
		now = time.Now()
	}
	year, week := now.ISOWeek()
	return GetWeekDate(year, week)
}

func GetWeekDate(year, week int) string {
	format := "%d-00-%d"
	if week < 10 {
		format = "%d-00-0%d"
	}
	return fmt.Sprintf(format, year, week)
}

func GetTodayStartTimestamp() int64 {
	currentTime := time.Now()
	return time.Date(currentTime.Year(), currentTime.Month(), currentTime.Day(), 0, 0, 0, 0, currentTime.Location()).UnixMilli()
}

func GetTodayStartTimestampWithTimeZone(tz string) int64 {
	currentTime := TimeZone(time.Now(), tz)
	return time.Date(currentTime.Year(), currentTime.Month(), currentTime.Day(), 0, 0, 0, 0, currentTime.Location()).UnixMilli()
}

func GetMonthEndTimestamp(t time.Time, zone string) int64 {
	// Get the year and month
	year, month, _ := t.Date()

	// Get the number of days in the month
	daysInMonth := time.Date(year, month+1, 0, 0, 0, 0, 0, time.UTC).Day()

	// Create a new time object for the last day of the month
	lastDay := time.Date(year, month, daysInMonth, 23, 59, 59, 0, time.FixedZone(zone, 0))

	// Convert the time object to a timestamp
	timestamp := lastDay.UnixMilli()

	return timestamp
}

func TransferTimestampToString(timestamp int64, tz string) string {

	t := time.UnixMilli(timestamp)

	// set timezone
	loc, err := time.LoadLocation(tz)
	if err != nil {
		fmt.Println(err)
		return ""
	}
	t = t.In(loc)

	// format time to string

	return t.Format("2006-01-02 15:04:05")
}

func Md5(bs []byte) string {
	has := md5.Sum(bs)
	return fmt.Sprintf("%x", has)
}

func Md5String(data string) string {
	if len(data) > 0 {
		has := md5.Sum([]byte(data))
		return fmt.Sprintf("%x", has)
	}
	return ""
}

func JsonStr(v interface{}) string {
	b, _ := json.Marshal(v)
	return string(b)
}

func JsonBytes(v interface{}) []byte {
	b, _ := json.Marshal(v)
	return b
}

func ProtoJsonStr(v proto.Message) string {
	b, _ := protojson.Marshal(v)
	return string(b)
}

func IntToCode(num int64) string {
	result := ""
	size := int64(len(numbersAndLetters))
	for num != 0 {
		rem := num % 36
		result = string(numbersAndLetters[rem]) + result
		num = num / size
	}
	return result
}

func GetUsercode() string {
	ts := NowTimeNano()/1000 - 1641960000000000
	return strings.ToUpper(IntToCode(ts))
}

func CheckYmdDate(ymd string) (year, month, day int, err error) {
	layout := consts.TimeFormatYMD
	if strings.Count(ymd, "-") == 0 {
		layout = consts.TimeFormatYMD2
	}
	date, err := time.Parse(layout, ymd)
	if err != nil {
		return
	}
	year = date.Year()
	month = int(date.Month())
	day = date.Day()
	return
}

func GetAgeByBirthday(birthday string) (age int, err error) {
	y, month, day, err := CheckYmdDate(birthday)
	if err != nil {
		return
	}
	now := time.Now()
	ny := now.Year()
	nm := int(now.Month())
	age = ny - y
	if age > 0 {
		if month > nm || (month == nm && day > now.Day()) {
			age = age - 1
		}
	}
	return
}

func GetConstellation(ymd string) (year, constellation int32, err error) {
	y, month, day, err := CheckYmdDate(ymd)
	if err != nil {
		return
	}
	year = int32(y)
	if age, _ := GetAgeByBirthday(ymd); age < 18 || age > 60 {
		year = 0
	}
	switch {
	case month <= 0, month >= 13, day <= 0, day >= 32:
		constellation = 0
	case month == 1 && day >= 20, month == 2 && day <= 18:
		constellation = 1
	case month == 2 && day >= 19, month == 3 && day <= 20:
		constellation = 2
	case month == 3 && day >= 21, month == 4 && day <= 19:
		constellation = 3
	case month == 4 && day >= 20, month == 5 && day <= 20:
		constellation = 4
	case month == 5 && day >= 21, month == 6 && day <= 21:
		constellation = 5
	case month == 6 && day >= 22, month == 7 && day <= 22:
		constellation = 6
	case month == 7 && day >= 23, month == 8 && day <= 22:
		constellation = 7
	case month == 8 && day >= 23, month == 9 && day <= 22:
		constellation = 8
	case month == 9 && day >= 23, month == 10 && day <= 22:
		constellation = 9
	case month == 10 && day >= 23, month == 11 && day <= 21:
		constellation = 10
	case month == 11 && day >= 22, month == 12 && day <= 21:
		constellation = 11
	case month == 12 && day >= 22, month == 1 && day <= 19:
		constellation = 12
	}
	return
}

func RandInt64(start, end int64) int64 {
	return rand.Int63n(end-start) + start
}

func RandInt32(start, end int32) int32 {
	return rand.Int31n(end-start) + start
}

func RandString(size int) string {
	bytes := make([]byte, size)
	for i := 0; i < size; i++ {
		b := rand.Intn(10) + 48
		bytes[i] = byte(b)
	}
	return string(bytes)
}

func RandNumberString(size int) string {
	bytes := make([]byte, size)
	for i := 0; i < size; i++ {
		b := rand.Intn(10) + 48
		bytes[i] = byte(b)
	}
	return string(bytes)
}

func GeneratePassword(size int) (err error, res string) {
	if size < 8 {
		err = fmt.Errorf("Password must be at least 8 characters")
		return
	}
	numLen := size / 4
	symbolLen := rand.Intn(size/4-1) + 1
	uppercaseLen := size / 4
	lowercaseLen := size - numLen - symbolLen - uppercaseLen
	var bytes []byte
	// 生成数字
	for i := 0; i < numLen; i++ {
		c := rand.Intn(10) + '0'
		bytes = append(bytes, byte(c))
	}
	// 生成特殊字符
	for i := 0; i < symbolLen; i++ {
		symbols := []byte(",./\\-=_+;':\"[]{}")
		bytes = append(bytes, symbols[rand.Intn(len(symbols))])
	}
	// 生成大写字母
	for i := 0; i < uppercaseLen; i++ {
		c := rand.Intn(26) + 'A'
		bytes = append(bytes, byte(c))
	}
	// 生成小写字母
	for i := 0; i < lowercaseLen; i++ {
		c := rand.Intn(26) + 'a'
		bytes = append(bytes, byte(c))
	}
	// 随机打乱
	rand.Shuffle(len(bytes), func(i, j int) {
		bytes[i], bytes[j] = bytes[j], bytes[i]
	})
	return nil, string(bytes)
}

func RandLetterString(size int) string {
	bytes := make([]byte, size)
	for i := 0; i < size; i++ {
		b := rand.Intn(26) + 65
		bytes[i] = byte(b)
	}
	return string(bytes)
}

func GetHashCode(s string) int64 {
	return int64(crc32.ChecksumIEEE([]byte(s)))
}

func PKCS7Padding(ciphertext []byte, blockSize int) []byte {
	padding := blockSize - len(ciphertext)%blockSize
	padtext := bytes.Repeat([]byte{byte(padding)}, padding)
	return append(ciphertext, padtext...)
}

func PKCS7UnPadding(origData []byte) []byte {
	length := len(origData)
	unpadding := int(origData[length-1])
	return origData[:(length - unpadding)]
}

// AES加密,CBC
func AesEncrypt(origData, key []byte) ([]byte, error) {
	block, err := aes.NewCipher(key)
	if err != nil {
		return nil, err
	}
	blockSize := block.BlockSize()
	origData = PKCS7Padding(origData, blockSize)
	blockMode := cipher.NewCBCEncrypter(block, key[:blockSize])
	crypted := make([]byte, len(origData))
	blockMode.CryptBlocks(crypted, origData)
	return crypted, nil
}

// AES解密
func AesDecrypt(crypted, key []byte) ([]byte, error) {
	block, err := aes.NewCipher(key)
	if err != nil {
		return nil, err
	}
	blockSize := block.BlockSize()
	blockMode := cipher.NewCBCDecrypter(block, key[:blockSize])
	origData := make([]byte, len(crypted))
	blockMode.CryptBlocks(origData, crypted)
	origData = PKCS7UnPadding(origData)
	return origData, nil
}

func hashBytes(key string) (hash []byte) {
	h := sha1.New()
	io.WriteString(h, key)
	hashStr := hex.EncodeToString(h.Sum(nil))
	hash = []byte(hashStr)[:32]
	return
}

func Encrypt(plainText string, key string) (cipherText string, err error) {
	var block cipher.Block
	keyBytes := hashBytes(key)
	plainTextBytes := []byte(plainText)
	block, err = aes.NewCipher(keyBytes)
	if err != nil {
		return
	}

	cipherTextBytes := make([]byte, aes.BlockSize+len(plainTextBytes))
	iv := cipherTextBytes[:aes.BlockSize]
	if _, err = io.ReadFull(crand.Reader, iv); err != nil {
		return
	}

	stream := cipher.NewCFBEncrypter(block, iv)
	stream.XORKeyStream(cipherTextBytes[aes.BlockSize:], plainTextBytes)
	cipherText = "crypt-" + hex.EncodeToString(cipherTextBytes)
	return
}

func Decrypt(cipherText string, key string) (plainText string, err error) {
	if len(cipherText) == 0 || len(cipherText) < 6 || cipherText[:6] != "crypt-" {
		err = errors.New("Illegal ciphertext")
		return
	}
	cipherText = string(cipherText[6:])
	var block cipher.Block
	keyBytes := hashBytes(key)
	cipherTextBytes, _ := hex.DecodeString(cipherText)
	block, err = aes.NewCipher(keyBytes)
	if err != nil {
		return
	}

	if len(cipherTextBytes) < aes.BlockSize {
		err = errors.New("Ciphertext too short")
		return
	}

	iv := cipherTextBytes[:aes.BlockSize]
	cipherTextBytes = cipherTextBytes[aes.BlockSize:]
	stream := cipher.NewCFBDecrypter(block, iv)

	plainTextBytes := make([]byte, len(cipherTextBytes))
	stream.XORKeyStream(plainTextBytes, cipherTextBytes)
	plainText = string(plainTextBytes)
	return
}

func MemberEncrypt(data string) string {
	if len(data) > 0 {
		encrypted, err := AesEncrypt([]byte(data), []byte(memberAesKey))
		if err == nil {
			return base64.StdEncoding.EncodeToString(encrypted)
		}
	}
	return data
}

func MemberDecrypt(data string) string {
	if len(data) > 0 {
		encrypted, err := base64.StdEncoding.DecodeString(data)
		if err != nil {
			return data
		}
		tempBytes, err := AesDecrypt(encrypted, []byte(memberAesKey))
		if err == nil {
			return string(tempBytes)
		}
	}
	return data
}

func MemberIdcardMd5(code string) string {
	return Md5([]byte(fmt.Sprintf("%s:%s", memberAesKey, code)))
}

// implementate zero-copy convert
func StringToBytes(s string) []byte {
	return *(*[]byte)(unsafe.Pointer(&s))
}

func BytesToString(b []byte) string {
	return *(*string)(unsafe.Pointer(&b))
}

func Int64ToBytes(v int64) []byte {
	bs := make([]byte, 8)
	binary.BigEndian.PutUint64(bs, uint64(v))
	return bs
}

func EncryptSha256(s string) string {
	hash := sha256.New()
	hash.Write([]byte(s))
	sum := hash.Sum(nil)
	return hex.EncodeToString(sum)
}

func GetThunderH5PartnerIDAndAv(ver string) (partnerID, av string) {
	datas := strings.Split(ver, ":")
	if len(datas) == 2 {
		av = datas[0]
		partnerID = datas[1]
		return
	}
	av = ver
	return
}

func GetRandomString(n int) string {
	randBytes := make([]byte, n/2)
	rand.Read(randBytes)
	return fmt.Sprintf("%x", randBytes)
}

func StringInArray(v string, slice []string) bool {
	for _, s := range slice {
		if s == v {
			return true
		}
	}
	return false
}

func Int32InArray(v int32, slice []int32) bool {
	for _, s := range slice {
		if s == v {
			return true
		}
	}
	return false
}

func HashString(s string) uint32 {
	h := fnv.New32a()
	h.Write([]byte(s))
	return h.Sum32()
}

func HashStringToInt64(s string) int64 {
	h := fnv.New32a()
	h.Write([]byte(s))
	return int64(h.Sum32())
}

func RemoveTrailingZeros(str string) string {

	if dotIndex := strings.IndexByte(str, '.'); dotIndex == -1 {
		return str
	}
	str = strings.TrimRight(str, "0")
	if str[len(str)-1] == '.' {
		str = str[:len(str)-1]
	}

	return str
}

func StrArrayToInt64Array(strArray []string) []int64 {
	result := make([]int64, 0, len(strArray))
	for _, s := range strArray {
		int64Value, err := strconv.ParseInt(strings.TrimSpace(s), 10, 64)
		if err != nil {
			continue
		}
		result = append(result, int64Value)
	}
	return result
}

func RemoveDuplicates(arr []int64) []int64 {
	seen := make(map[int64]bool)
	var result []int64

	for _, v := range arr {
		if !seen[v] {
			seen[v] = true
			result = append(result, v)
		}
	}

	return result
}

func RemoveDupString(arr []string) []string {
	seen := make(map[string]bool)
	var result []string

	for _, v := range arr {
		if !seen[v] {
			seen[v] = true
			result = append(result, v)
		}
	}

	return result
}

func TimeZone(t time.Time, tz string) time.Time {
	loc, err := time.LoadLocation(tz)
	if err != nil {
		return t
	}
	return t.In(loc)
}

// MaskPhoneNumber 对手机号进行脱敏处理，将中间4位替换为星号(*)
// 例如：13812345678 -> 138****5678
func MaskPhoneNumber(phone string) string {
	if len(phone) != 11 {
		return phone // 非标准11位手机号，不做处理
	}
	return phone[:3] + "****" + phone[7:]
}

// GenerateUniqueCode 将 int64 转换为 base36 字符串，并添加时间戳确保唯一性
func GenerateUniqueCode(id int64) string {
	// 组合时间戳的低32位和输入id，确保唯一性
	timestamp := NowTimeNano() & 0xFFFFFFFF
	uniqueNum := (timestamp << 32) | (id & 0xFFFFFFFF)
	return IntToCode(uniqueNum)
}
