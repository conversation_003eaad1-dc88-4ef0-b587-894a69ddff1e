package util

import (
	"errors"
	"fmt"
	"image"
	_ "image/gif"
	_ "image/jpeg"
	_ "image/png"
	"io"
	"net/http"
	"os"
	"sort"
	"strings"
	"time"

	"github.com/EdlinOrg/prominentcolor"
	"github.com/lucasb-eyer/go-colorful"
	"github.com/sirupsen/logrus"
)

// ColorInfo 存储颜色信息，包括RGB值、HSL值和权重
type ColorInfo struct {
	Hex        string  // 十六进制颜色值
	Hue        float64 // 色相 [0..360]
	Saturation float64 // 饱和度 [0..1]
	Lightness  float64 // 亮度 [0..1]
	Weight     int     // 权重（颜色在图像中的占比）
}

// ThemeColorOptions 定义主题色提取的选项
type ThemeColorOptions struct {
	MinSaturation float64 // 最小饱和度 [0..1]
	MaxSaturation float64 // 最大饱和度 [0..1]
	MinLightness  float64 // 最小亮度 [0..1]
	MaxLightness  float64 // 最大亮度 [0..1]
	K             int     // 提取的颜色数量
	UseCropping   bool    // 是否使用中心裁剪
	ResizeWidth   uint    // 调整大小的宽度
}

// DefaultThemeColorOptions 返回默认的主题色提取选项
func DefaultThemeColorOptions() ThemeColorOptions {
	return ThemeColorOptions{
		MinSaturation: 0.5, // 提高最小饱和度阈值，过滤掉中低饱和度的颜色
		MaxSaturation: 1.0,
		MinLightness:  0.3,  // 稍微提高最小亮度，避免颜色过暗
		MaxLightness:  0.7,  // 降低最大亮度，避免颜色过亮而失去饱和感
		K:             7,    // 增加提取的颜色数量，提高找到高饱和度颜色的概率
		UseCropping:   true, // 使用中心裁剪
		ResizeWidth:   120,  // 增加处理图像的尺寸，保留更多细节
	}
}

// VibrancyThemeColorOptions 返回高饱和度的主题色提取选项
func VibrancyThemeColorOptions() ThemeColorOptions {
	return ThemeColorOptions{
		MinSaturation: 0.7, // 设置非常高的最小饱和度阈值
		MaxSaturation: 1.0,
		MinLightness:  0.4,   // 提高最小亮度，确保颜色明亮
		MaxLightness:  0.6,   // 降低最大亮度，避免颜色过亮而失去饱和感
		K:             10,    // 提取更多颜色，增加找到理想颜色的概率
		UseCropping:   false, // 不使用裁剪，考虑整个图像
		ResizeWidth:   160,   // 使用更大的处理尺寸，保留更多细节
	}
}

// ExtractThemeColor 从图片路径提取主题色（支持URL和本地文件路径）
func ExtractThemeColor(imagePath string, options ThemeColorOptions) (string, []ColorInfo, error) {
	// 检查是否为URL
	if strings.HasPrefix(imagePath, "http://") || strings.HasPrefix(imagePath, "https://") {
		return ExtractThemeColorFromURL(imagePath, options)
	}
	// 否则作为本地文件路径处理
	return ExtractThemeColorFromPath(imagePath, options)
}

// ExtractThemeColorFromURL 从URL下载图片并提取主题色
func ExtractThemeColorFromURL(imageURL string, options ThemeColorOptions) (string, []ColorInfo, error) {
	// 设置HTTP客户端，超时时间为10秒
	client := &http.Client{
		Timeout: 10 * time.Second,
	}

	// 发起HTTP请求
	resp, err := client.Get(imageURL)
	if err != nil {
		return "", nil, fmt.Errorf("下载图片失败: %v", err)
	}
	defer resp.Body.Close()

	// 检查HTTP状态码
	if resp.StatusCode != http.StatusOK {
		return "", nil, fmt.Errorf("下载图片失败，HTTP状态码: %d", resp.StatusCode)
	}

	// 从响应体中解码图片
	return ExtractThemeColorFromReader(resp.Body, options)
}

// ExtractThemeColorFromPath 从本地文件路径读取图片并提取主题色
func ExtractThemeColorFromPath(imagePath string, options ThemeColorOptions) (string, []ColorInfo, error) {
	// 打开本地文件
	file, err := os.Open(imagePath)
	if err != nil {
		return "", nil, fmt.Errorf("打开图片文件失败: %v", err)
	}
	defer file.Close()

	// 从文件中提取主题色
	return ExtractThemeColorFromReader(file, options)
}

// ExtractThemeColorFromReader 从io.Reader中提取主题色
func ExtractThemeColorFromReader(reader io.Reader, options ThemeColorOptions) (string, []ColorInfo, error) {
	// 解码图片
	img, _, err := image.Decode(reader)
	if err != nil {
		return "", nil, fmt.Errorf("解码图片失败: %v", err)
	}

	// 设置prominentcolor参数
	// 使用位运算设置参数
	arguments := prominentcolor.ArgumentDefault // 默认使用Kmeans++和中值

	// 如果不使用裁剪，设置ArgumentNoCropping位
	if !options.UseCropping {
		arguments |= prominentcolor.ArgumentNoCropping
	}

	// 使用KmeansWithAll函数提取颜色
	colors, err := prominentcolor.KmeansWithAll(
		options.K,                        // K值
		img,                              // 图片
		arguments,                        // 参数
		options.ResizeWidth,              // 调整大小的宽度
		prominentcolor.GetDefaultMasks(), // 使用默认的背景掩码
	)
	if err != nil {
		return "", nil, fmt.Errorf("提取颜色失败: %v", err)
	}

	// 转换为ColorInfo并过滤
	colorInfos := make([]ColorInfo, 0, len(colors))
	for _, c := range colors {
		// 将RGB转换为HSL
		cf, err := colorful.Hex("#" + c.AsString())
		if err != nil {
			logrus.Warnf("颜色转换失败: %v", err)
			continue
		}

		h, s, l := cf.Hsl()

		// 根据饱和度和亮度过滤颜色
		if s >= options.MinSaturation && s <= options.MaxSaturation &&
			l >= options.MinLightness && l <= options.MaxLightness {
			colorInfos = append(colorInfos, ColorInfo{
				Hex:        "#" + c.AsString(),
				Hue:        h,
				Saturation: s,
				Lightness:  l,
				Weight:     c.Cnt,
			})
		}
	}

	// 如果没有颜色满足条件，放宽条件再试一次
	if len(colorInfos) == 0 {
		for _, c := range colors {
			cf, err := colorful.Hex("#" + c.AsString())
			if err != nil {
				continue
			}

			h, s, l := cf.Hsl()
			colorInfos = append(colorInfos, ColorInfo{
				Hex:        "#" + c.AsString(),
				Hue:        h,
				Saturation: s,
				Lightness:  l,
				Weight:     c.Cnt,
			})
		}
	}

	// 如果仍然没有颜色，返回错误
	if len(colorInfos) == 0 {
		return "", nil, errors.New("没有找到合适的主题色")
	}

	// 按饱和度和权重的组合排序
	sort.Slice(colorInfos, func(i, j int) bool {
		// 饱和度差异较大时，优先选择饱和度高的
		if colorInfos[i].Saturation-colorInfos[j].Saturation > 0.2 {
			return true
		} else if colorInfos[j].Saturation-colorInfos[i].Saturation > 0.2 {
			return false
		}
		// 饱和度相近时，按权重排序
		return colorInfos[i].Weight > colorInfos[j].Weight
	})

	// 返回排序后的第一个颜色作为主题色
	return colorInfos[0].Hex, colorInfos, nil
}
