package util

import (
	"crypto/rand"
	"fmt"
	"math/big"
	"time"
)

const (
	randStrLen = 11

	TimeFormatStr = "20060102150405"
)

type TOrderType int32

type ItuOrderID struct {
	orderType TOrderType
	tm        time.Time //初始化时，timeZone=Local
	randStr   string
	//totalLength int
}

func (t TOrderType) String() string {
	return fmt.Sprintf("%08d", t)
}

func (o *ItuOrderID) String() string {
	//orderID格式：类型（8位）+ 年月日时分秒毫秒（17位）+ 随机串（11位）	共36位
	//例如：000000102019101115151600101234567891
	//注意：使用utc时间（0时区）对应的月份分表
	return o.orderType.String() + o.tm.UTC().Format(TimeFormatStr) +
		fmt.Sprintf("%03d", o.tm.Nanosecond()/int(time.Millisecond)) + o.randStr
}

func NewOrderIDByTime(t TOrderType, tm time.Time) *ItuOrderID {
	return &ItuOrderID{tm: tm, orderType: t, randStr: randString(randStrLen)}
}

func NewOrderID(t TOrderType) *ItuOrderID {
	return NewOrderIDByTime(t, time.Now())
}

func randString(n int) string {
	rands := ""
	for j := 0; j < n/4; j++ {
		randnum, _ := rand.Int(rand.Reader, big.NewInt(9999))
		rands = rands + fmt.Sprintf("%0*d", 4, randnum)
	}
	if n%4 != 0 {
		var number int64
		number = 1
		for i := 0; i < n%4; i++ {
			number = number * 10
		}
		randnum, _ := rand.Int(rand.Reader, big.NewInt(number-1))
		rands = rands + fmt.Sprintf("%0*d", n%4, randnum)
	}
	return rands
}
