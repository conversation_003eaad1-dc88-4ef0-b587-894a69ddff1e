package util

import (
	"net/url"
	"strings"
)

// ExtractURI 获取URI path + query //fragment
func ExtractURI(rawURL string) (string, error) {
	parsed, err := url.Parse(strings.TrimSpace(rawURL))
	if err != nil {
		return "", err
	}
	uri := parsed.RequestURI()
	if parsed.Fragment != "" {
		uri += "#" + parsed.Fragment
	}
	return uri, nil
}

// ExtractPath 只提取 URL 的 path 部分
func ExtractPath(raw string) string {
	raw = strings.TrimSpace(raw)
	if raw == "" {
		return ""
	}

	if u, err := url.Parse(raw); err == nil {
		return u.Path
	}

	// fallback: 如果不能解析就简单做前缀清理
	if i := strings.Index(raw, "?"); i != -1 {
		raw = raw[:i]
	}
	if i := strings.Index(raw, "#"); i != -1 {
		raw = raw[:i]
	}
	return raw
}
