package util

import "new-gitlab.xunlei.cn/vcproject/backends/proto/consts"

func GetReportGender(gender int32) string {
	if gender == 1 {
		return "male"
	}
	if gender == 2 {
		return "female"
	}
	return "unknown"
}

func GetOs(dt int32) string {
	if dt == 1 {
		return "android"
	}
	if dt == 2 {
		return "ios"
	}
	if dt == 3 {
		return "web"
	}
	return ""
}

func GetIssuer(issuer string) string {
	if issuer == consts.AccessModePhone {
		return "phone"
	}
	if issuer == consts.AccessModeWechat {
		return "wechat"
	}
	if issuer == consts.AccessModeApple {
		return "apple"
	}
	return "phone"
}
