package util

import (
	"github.com/mozillazg/go-pinyin"
	"strings"
	"unicode"
)

// ConvertToPinyin 将中文转换为拼音，非中文原样保留
func ConvertToPinyin(s string) string {
	args := pinyin.NewArgs()
	args.Style = pinyin.Normal

	var result strings.Builder
	for _, r := range s {
		if unicode.Is(unicode.Han, r) {
			// 是汉字，转为拼音
			py := pinyin.SinglePinyin(r, args)
			if len(py) > 0 {
				result.WriteString(py[0])
			}
		} else {
			// 非汉字，原样添加
			result.WriteRune(r)
		}
	}
	return result.String()
}
