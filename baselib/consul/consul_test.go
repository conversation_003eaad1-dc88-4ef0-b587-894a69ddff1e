package consul

import (
	"testing"

	"gopkg.in/yaml.v3"
)

type SubStruct struct {
	Count int    `yaml:"count"`
	Nick  string `yaml:"nick"`
}

type TestStruct struct {
	Name string      `yaml:"name"`
	Age  int         `yaml:"age"`
	List []SubStruct `yaml:"list"`
}

func TestConsulPut(t *testing.T) {
	Init("os-consul.voicelives.net", "local/test1/test.yaml")
	test := TestStruct{
		Name: "test",
		Age:  18,
		List: []SubStruct{
			{
				Count: 1,
				Nick:  "232rwrqwr",
			},
			{
				Count: 100,
				Nick:  "垃圾发健身房啦刷卡机法师",
			},
		},
	}
	byteVal, _ := yaml.Marshal(test)
	t.Logf("byteVal=%s", string(byteVal))
	err := PutKv("local/test2/test2.yaml", string(byteVal))
	if err != nil {
		t.<PERSON>("err=%+v", err)
	}
}
