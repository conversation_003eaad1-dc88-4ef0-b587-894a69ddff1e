package consul

import (
	"encoding/json"
	"fmt"
	"reflect"
	"sync"

	"github.com/go-playground/validator"
	"github.com/hashicorp/consul/api"
	"github.com/hashicorp/consul/api/watch"
	"new-gitlab.xunlei.cn/vcproject/backends/baselib/logger"

	"gopkg.in/yaml.v3"
)

const (
	EmptyPrefix = ""
)

var (
	Address string
	Prefix  string
	KV      *api.KV // KV is used to manipulate the K/V API
	Client  *api.Client
)

// 自定义前缀，这样就可以复用配置了
func WithPrefix(prefix string) Consul {
	return Consul{prefixPtr: &prefix}
}

func (m Consul) WithPrefix(prefix string) Consul {
	m.prefixPtr = &prefix
	return m
}

func WithLocker(locker sync.Locker) Consul {
	var m Consul
	return m.WithLocker(locker)
}

func (m Consul) WithLocker(locker sync.Locker) Consul {
	m.locker = locker
	return m
}

func GetValue(key string) (value []byte) {
	return getValue(key, EmptyPrefix, defaultConsul)
}

var (
	cacheMap  = map[string]*api.KVPair{} //once写一次，并发读没问题
	cacheOnce sync.Map
	infoOnce  sync.Map
)

func getOncer(m *sync.Map, k string) *sync.Once {
	oncev, _ := m.LoadOrStore(k, &sync.Once{})
	return oncev.(*sync.Once)
}

// 返回完整的key:ck(complete key)，给外面用
func getValue(key, prefix string, lo Consul) (value []byte) {
	k := getCompleteKey(key, prefix, lo)
	getOncer(&cacheOnce, k).Do(func() { //首次大家都阻塞，直到写成
		pair, _, _ := KV.Get(k, nil) //失败时pair是nil
		cacheMap[k] = pair
		if pair != nil {
			logger.Infof("key=%s value=%s consul request value ok", k, string(pair.Value))
		}
	})
	pair := cacheMap[k]

	if pair == nil {
		logger.Panic("consul has't key")
	}
	return pair.Value
}

func (m Consul) GetValue(key string) (value []byte) {
	return getValue(key, EmptyPrefix, m)
}

func getCompleteKey(key, prefix string, lo Consul) string {
	prefixParam := Prefix
	if len(prefix) > 0 {
		prefixParam = prefix
	}
	if lo.prefixPtr != nil {
		prefixParam = *lo.prefixPtr
	}
	return fmt.Sprintf("%s/%s", prefixParam, key)
}

type Unmarshal func(data []byte, v interface{}) error

func getJson(key, prefix string, i interface{}, lo Consul, unmarshal Unmarshal) {
	t := reflect.TypeOf(i)

	switch t.Kind() {
	case reflect.Ptr:
		value := getValue(key, prefix, lo)
		if err := unmarshal(value, i); err != nil {
			logger.Panicf("key=%s bs=%+v err=%v consul value invalid", getCompleteKey(key, prefix, lo), string(value), err)
		}
		if err := valiVa(lo, i); err != nil {
			logger.Panicf("key=%s  value =i err=%v vali failed", getCompleteKey(key, prefix, lo), i, err)
		}
		getOncer(&infoOnce, getCompleteKey(key, prefix, lo)).Do(func() {
		})
		return
	case reflect.Func:
		if t.NumIn() != 1 {
			logger.Panicf("key=%s numIn:%d != 1", getCompleteKey(key, prefix, lo), t.NumIn())
		}
		v := reflect.ValueOf(i)

		in0Type := t.In(0)
		in0Ptr := reflect.New(in0Type).Interface()
		getJson(key, prefix, in0Ptr, lo, unmarshal)
		v.Call([]reflect.Value{reflect.ValueOf(in0Ptr).Elem()})
		return
	default:
		logger.Panicf("key=%s type=%s invalie value kind=%+v", getCompleteKey(key, prefix, lo), t.String(), t.Kind())
	}
}

// 如果对值不关心，只想要用这个值去执行一个函数，例如用于初始化日志，那么第二个参数就传入有一个入参的函数吧
func GetJson(key string, i interface{}) {
	getJson(key, EmptyPrefix, i, defaultConsul, json.Unmarshal)
}

func GetJsonByPrefix(key, prefix string, i interface{}) {
	getJson(key, prefix, i, defaultConsul, json.Unmarshal)
}

func GetYaml(key string, i interface{}) {
	getJson(key, EmptyPrefix, i, defaultConsul, yaml.Unmarshal)
}

func GetYamlByPrefix(key, prefix string, i interface{}) {
	getJson(key, prefix, i, defaultConsul, yaml.Unmarshal)
}

// 成员用指针，不为nil时候才使用对应功能
type Consul struct {
	valiTypePtr *int
	tag         string
	prefixPtr   *string
	locker      sync.Locker
}

var defaultConsul Consul //默认的

const (
	valiStruct = 1
	valiVar    = 2
)

func newInt(i int) *int {
	return &i
}

func (m Consul) ValiStruct() Consul {
	m.valiTypePtr = newInt(valiStruct)
	return m
}

func (m Consul) ValiVar(tag string) Consul {
	m.valiTypePtr = newInt(valiVar)
	m.tag = tag
	return m
}

func ValiStruct() Consul {
	return Consul{valiTypePtr: newInt(valiStruct)}
}
func ValiVar(tag string) Consul {
	return Consul{
		valiTypePtr: newInt(valiVar),
		tag:         tag,
	}
}
func (m Consul) GetJson(key string, i interface{}) {
	getJson(key, EmptyPrefix, i, m, json.Unmarshal)
}

func (m Consul) GetYaml(key string, i interface{}) {
	getJson(key, EmptyPrefix, i, m, yaml.Unmarshal)
}

func valiVa(lo Consul, i interface{}) error {
	if lo.valiTypePtr == nil {
		return nil
	}
	switch *lo.valiTypePtr {
	case valiStruct:
		return vali.Struct(i)
	case valiVar:
		return vali.Var(i, lo.tag)
	default:
		return fmt.Errorf("invalid m.valiType:%d", lo.valiTypePtr)
	}
}

var vali = validator.New()

func (m Consul) WatchJson(key string, ptr interface{}, handler func()) {
	watchJson(key, EmptyPrefix, ptr, handler, m, json.Unmarshal)
}
func (m Consul) WatchYaml(key string, ptr interface{}, handler func()) {
	watchJson(key, EmptyPrefix, ptr, handler, m, yaml.Unmarshal)
}

func WatchJson(key string, ptr interface{}, handler func()) {
	watchJson(key, EmptyPrefix, ptr, handler, defaultConsul, json.Unmarshal)
}

func WatchJsonByPrefix(key, prefix string, ptr interface{}, handler func()) {
	watchJson(key, prefix, ptr, handler, defaultConsul, json.Unmarshal)
}

func WatchYaml(key string, ptr interface{}, handler func()) {
	watchJson(key, EmptyPrefix, ptr, handler, defaultConsul, yaml.Unmarshal)
}

func WatchYamlByPrefix(key, prefix string, ptr interface{}, handler func()) {
	watchJson(key, prefix, ptr, handler, defaultConsul, yaml.Unmarshal)
}

func watchJson(key, prefix string, ptr interface{}, handler func(), lo Consul, unmarshal Unmarshal) {
	tmpPrefix := Prefix
	if lo.prefixPtr != nil {
		tmpPrefix = *lo.prefixPtr
	}
	if len(prefix) > 0 {
		tmpPrefix = prefix
	}

	plan, err := watch.Parse(map[string]interface{}{
		"type": "key",
		"key":  fmt.Sprintf("%s/%s", tmpPrefix, key),
	})
	if err != nil {
		logger.Panicf("key=%s err=%+v consul watch parse failed", key, err)
	}
	getJson(key, tmpPrefix, ptr, lo, unmarshal)
	if handler != nil {
		handler()
	}
	rt := reflect.TypeOf(ptr)
	plan.Handler = func(idx uint64, raw interface{}) {
		if lo.locker != nil {
			lo.locker.Lock() //避免竞争, 例如map并发修改会panic
			defer lo.locker.Unlock()
		}
		var value []byte
		defer func() {
			//避免对外部造成影响
			if r := recover(); r != nil {
				logger.Errorf("key=%s panic=%+v", key, r)
			}
		}()
		if kv, ok := raw.(*api.KVPair); ok && kv != nil {
			value = kv.Value
			tmp := reflect.New(rt.Elem()).Interface() //先在临时变量上修改, 没问题再设置, 如同nginx -s reload
			if err := unmarshal(value, tmp); err != nil {
				logger.Errorf("bs=%s value_old=%+v err=%+v consul watch unmarshal json failed", string(value), reflect.ValueOf(ptr).Elem().Interface(), err)
				return
			}
			if err := valiVa(lo, tmp); err != nil {
				logger.Errorf("bs=%s value_old=%+v value_new=%+v err=%+v vali failed", string(value), reflect.ValueOf(ptr).Elem().Interface(), tmp, err)
				return
			}
			reflect.ValueOf(ptr).Elem().Set(reflect.ValueOf(tmp).Elem())
			logger.Errorf("bs=%s value_old=%+v value_set_default=%+v consul watch value ok ", string(value), reflect.ValueOf(ptr).Elem().Interface(), tmp)
			if handler != nil {
				handler() //启动时会起个线程执行一次，发生修改后回调
			}
		} else {
			logger.Errorf("idx=%+v raw=%+v consul watch invalid raw", idx, raw)
		}
	}
	go plan.Run(Address)
}

func (m Consul) WatchJsonVarious(key string, i interface{}) {
	watchJsonVarious(key, i, m, json.Unmarshal)
}

func (m Consul) WatchYamlVarious(key string, i interface{}) {
	watchJsonVarious(key, i, m, yaml.Unmarshal)
}

// 只关心修改后函数的执行
// consul监控key对应的value的变化，然后调用函数handler(value)
func WatchJsonVarious(key string, i interface{}) {
	watchJsonVarious(key, i, defaultConsul, json.Unmarshal)
}

func WatchYamlVarious(key string, i interface{}) {
	watchJsonVarious(key, i, defaultConsul, yaml.Unmarshal)
}

func watchJsonVarious(key string, i interface{}, lo Consul, unmarshal Unmarshal) {
	t := reflect.TypeOf(i)
	switch t.Kind() {
	case reflect.Ptr:
		watchJson(key, EmptyPrefix, i, nil, lo, unmarshal)
	case reflect.Func:
		if t.NumIn() != 1 {
			logger.Panicf("key=%s type=%s numIn:%d != 1", key, t.String(), t.NumIn())
		}
		v := reflect.ValueOf(i)

		in0Type := t.In(0)
		in0Ptr := reflect.New(in0Type).Interface() //为了避免再写一遍WatchJson而采用的偷懒做法
		watchJson(key, EmptyPrefix, in0Ptr, func() {
			v.Call([]reflect.Value{reflect.ValueOf(in0Ptr).Elem()})
		}, lo, unmarshal)
	}
}

func Init(address string, prefix string) {
	Address = address
	Prefix = prefix
	consulClient, err := api.NewClient(&api.Config{Address: address})
	if err != nil {
		logger.Panicf("address=%s prefix=%s err=%+v consul connect failed", address, prefix, err)
	}
	Client = consulClient
	KV = consulClient.KV()
	logger.Infof("address=%s prefix=%s consul connect ok", address, prefix)
}

func PutKv(prefix, values string) (err error) {
	_, err = KV.Put(&api.KVPair{Key: prefix, Value: []byte(values)}, nil)
	return err
}

func ListByPrefix(prefix string) (result map[string][]byte, err error) {
	fullPrefix := getCompleteKey(prefix, EmptyPrefix, defaultConsul)
	pairs, _, err := KV.List(fullPrefix, nil)
	if err != nil {
		return nil, err
	}
	result = make(map[string][]byte)
	for _, kv := range pairs {
		if kv != nil {
			result[kv.Key] = kv.Value
		}
	}
	return
}
