package metric

import (
	"net/http"
	"os"

	grpcprometheus "github.com/grpc-ecosystem/go-grpc-prometheus"
	"github.com/prometheus/client_golang/prometheus"
	"github.com/prometheus/client_golang/prometheus/collectors"
	"github.com/prometheus/client_golang/prometheus/promhttp"
	"google.golang.org/grpc"
	"new-gitlab.xunlei.cn/vcproject/backends/baselib/logger"
)

var (
	reg             = prometheus.NewRegistry()
	serverMetrics   = grpcprometheus.NewServerMetrics()
	clientMetrics   = grpcprometheus.NewClientMetrics()
	promMetricsPath = "/grpc_metric"
	PromHttpAddr    = ""
	pid             int
)

func init() {
	pid = os.Getpid()
}

func MustRegister(collector ...prometheus.Collector) {
	reg.Must<PERSON><PERSON><PERSON>(collector...)
}

func InitPrometheus(server *grpc.Server, promAddr string) {
	if server != nil {
		serverMetrics.InitializeMetrics(server)
	}
	serverMetrics.EnableHandlingTimeHistogram(grpcprometheus.WithHistogramBuckets([]float64{.005, .01, .025, .05, .060, .070, .080, .090, .1, .25, .5, 1, 2.5, 5, 10}))
	clientMetrics.EnableClientHandlingTimeHistogram()

	PromHttpAddr = promAddr
	if PromHttpAddr == "" {
		PromHttpAddr = "0.0.0.0:9100"
	}
	logger.Infof("PromHttpAddr %v", PromHttpAddr)
	reg.MustRegister(serverMetrics, clientMetrics)
	reg.MustRegister(collectors.NewBuildInfoCollector())
	reg.MustRegister(collectors.NewGoCollector())
	reg.MustRegister(collectors.NewProcessCollector(collectors.ProcessCollectorOpts{
		PidFn: func() (int, error) {
			return pid, nil
		},
		Namespace:    "",
		ReportErrors: false,
	}))
	http.Handle(promMetricsPath, promhttp.HandlerFor(reg, promhttp.HandlerOpts{}))
	//http.HandleFunc(promMetricsPath, promhttp.Handler().ServeHTTP)
	go func() {
		err := http.ListenAndServe(PromHttpAddr, nil)
		if err != nil {
			logger.Errorf("error start promohttpaddr %v", err)
		}
	}()
}

func UnaryClientInterceptor() grpc.UnaryClientInterceptor {
	return clientMetrics.UnaryClientInterceptor()
}

func UnaryServerInterceptor() grpc.UnaryServerInterceptor {
	return serverMetrics.UnaryServerInterceptor()
}
