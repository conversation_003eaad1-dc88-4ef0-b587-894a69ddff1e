package metric

import (
	"context"
	"fmt"
	"sort"
	"strconv"
	"strings"
	"time"

	"github.com/prometheus/client_golang/prometheus"
	"github.com/redis/go-redis/v9"
	"new-gitlab.xunlei.cn/vcproject/backends/baselib/logger"
	"new-gitlab.xunlei.cn/vcproject/backends/baselib/server/env"
)

/*
  pv,uv指标的上报会依赖，redis, 各个业务按则需要手动调用此初始化按钮，传入redis实例
*/

var (
	//redisClient *redis.Client

	ctx          = context.Background()
	metricReport *MetricReport
)

type MetricReport struct {
	bizPvNamesKey string
	bizUvNamesKey string
	redisClient   *redis.Client
}

func InitPvUvReport(rdsClient *redis.Client, svcName string) {
	if rdsClient == nil || len(svcName) == 0 {
		logger.Panicf("redisClient is nil or svcName empty")
	}
	bizPvMembersKey := "metrics:pv:biznames:key:%s"
	bizUvMembersKey := "metrics:uv:biznames:key:%s"
	metricReport = &MetricReport{
		redisClient:   rdsClient,
		bizPvNamesKey: fmt.Sprintf(bizPvMembersKey, svcName),
		bizUvNamesKey: fmt.Sprintf(bizUvMembersKey, svcName),
	}
	go metricReport.statJob()
}

func ReportPv(biz string, pvBy int64, labels prometheus.Labels, date ...string) (err error) {
	if len(biz) == 0 {
		return fmt.Errorf("biz empty")
	}
	if strings.Contains(biz, ":") {
		return fmt.Errorf("biz conn't contain colon")
	}
	keys := make([]string, 0, len(labels))
	if len(labels) > 0 {
		for k, v := range labels {
			keys = append(keys, k)
			if strings.Contains(k, ":") || strings.Contains(v, ":") {
				return fmt.Errorf("labels conn't contain colon")
			}
		}
	}
	// 排序 key
	sort.Strings(keys)
	day := getToday()
	if len(date) > 0 {
		day = date[0]
	}
	pvKey := fmt.Sprintf("pv:%s:%s", biz, day)
	defer func() {
		metricReport.redisClient.Expire(ctx, pvKey, time.Hour*24)
	}()
	fieldKey := ""
	if len(keys) > 0 {
		for _, k := range keys {
			val, ok := labels[k]
			if !ok {
				return fmt.Errorf("%s not found label", k)
			}
			if len(fieldKey) == 0 {
				fieldKey = val
			} else {
				fieldKey = fieldKey + fmt.Sprintf(":%s", val)
			}
		}
		// INCR PV
		if err := metricReport.redisClient.HIncrBy(ctx, pvKey, fieldKey, pvBy).Err(); err != nil {
			return err
		}
		// 缓存此biz值
		_, err = metricReport.redisClient.HSet(ctx, metricReport.bizPvNamesKey, biz, strings.Join(keys, ":")).Result()
	} else {
		// INCR PV
		if err := metricReport.redisClient.IncrBy(ctx, pvKey, pvBy).Err(); err != nil {
			return err
		}
		// 缓存此biz值
		_, err = metricReport.redisClient.HSet(ctx, metricReport.bizPvNamesKey, biz, "").Result()
	}
	return
}

func ReportUv(biz, uvTag string, labels prometheus.Labels, date ...string) (err error) {
	if len(biz) == 0 {
		return fmt.Errorf("biz empty")
	}
	if strings.Contains(biz, ":") {
		return fmt.Errorf("biz conn't contain colon")
	}
	keys := make([]string, 0, len(labels))
	if len(labels) > 0 {
		for k, v := range labels {
			keys = append(keys, k)
			if strings.Contains(k, ":") || strings.Contains(v, ":") {
				return fmt.Errorf("labels conn't contain colon")
			}
		}
	}
	// 排序 key
	sort.Strings(keys)
	day := getToday()
	if len(date) > 0 {
		day = date[0]
	}
	// 缓存此biz值
	uvKey := fmt.Sprintf("uv:%s:%s", biz, day)
	defer func() {
		metricReport.redisClient.Expire(ctx, uvKey, time.Hour*24)
	}()
	// PFADD UV
	if len(labels) > 0 {
		fieldKey := ""
		for _, k := range keys {
			val, ok := labels[k]
			if !ok {
				return fmt.Errorf("%s not found label", k)
			}
			if len(fieldKey) == 0 {
				fieldKey = val
			} else {
				fieldKey = fieldKey + fmt.Sprintf(":%s", val)
			}
		}
		fieldKey = fmt.Sprintf("%s:%s_%s", fieldKey, day, biz)
		if err := metricReport.redisClient.PFAdd(ctx, fieldKey, uvTag).Err(); err != nil {
			return err
		}
		metricReport.redisClient.Expire(ctx, fieldKey, time.Hour*24)
		metricReport.redisClient.SAdd(ctx, uvKey, fieldKey)
		// 缓存此biz值
		_, err = metricReport.redisClient.HSet(ctx, metricReport.bizUvNamesKey, biz, strings.Join(keys, ":")).Result()
	} else {
		if err := metricReport.redisClient.PFAdd(ctx, uvKey, uvTag).Err(); err != nil {
			return err
		}
		_, err = metricReport.redisClient.HSet(ctx, metricReport.bizUvNamesKey, biz, "").Result()
	}
	return
}

func getToday() string {
	return time.Now().Format("20060102")
}

func (s *MetricReport) statJob() {
	for {
		time.Sleep(time.Second * 15)
		s.reportMetrics()
	}
}

func (s *MetricReport) reportMetrics() {
	bizPvSet, err := s.redisClient.HGetAll(ctx, s.bizPvNamesKey).Result()
	if err != nil && err != redis.Nil {
		logger.Errorf("err=%+v", err)
		return
	}
	day := getToday()
	for biz, labels := range bizPvSet {
		metricName := fmt.Sprintf("%s_pv", strings.ReplaceAll(biz, ".", "_"))
		pvKey := fmt.Sprintf("pv:%s:%s", biz, day)
		if len(labels) > 0 {
			labelArr := strings.Split(labels, ":")
			vals, err := s.redisClient.HGetAll(ctx, pvKey).Result()
			if err != nil && err != redis.Nil {
				logger.Errorf("err=%+v", err)
			} else {
				for labelVals, val := range vals {
					labelValArr := strings.Split(labelVals, ":")
					if len(labelValArr) == len(labelArr) {
						pv, _ := strconv.ParseInt(val, 10, 64)
						prometheusLabels := prometheus.Labels{}
						for i := range labelArr {
							prometheusLabels[labelArr[i]] = labelValArr[i]
						}
						if env.IsLocal() {
							logger.Infof("metricName=%s pv=%+v labels=%+v", metricName, pv, prometheusLabels)
						} else {
							GaugeLabels(metricName, prometheusLabels).Set(float64(pv))
						}
					}
				}
			}
		} else {
			val, err := s.redisClient.Get(ctx, pvKey).Result()
			if err != nil && err != redis.Nil {
				logger.Errorf("err=%+v", err)
			} else {
				pv, _ := strconv.ParseInt(val, 10, 64)
				if env.IsLocal() {
					logger.Infof("metricName=%s pv=%+v no labels", metricName, pv)
				} else {
					Gauge(metricName).Set(float64(pv))
				}
			}
		}
	}
	// report uv
	bizUvSet, err := s.redisClient.HGetAll(ctx, s.bizUvNamesKey).Result()
	if err != nil && err != redis.Nil {
		logger.Errorf("err=%+v", err)
		return
	}
	for biz, labels := range bizUvSet {
		metricName := fmt.Sprintf("%s_uv", strings.ReplaceAll(biz, ".", "_"))
		uvKey := fmt.Sprintf("uv:%s:%s", biz, day)
		if len(labels) > 0 {
			labelArr := strings.Split(labels, ":")
			vals, err := s.redisClient.SMembers(ctx, uvKey).Result()
			if err != nil && err != redis.Nil {
				logger.Errorf("err=%+v", err)
			} else {
				for _, labelVals := range vals {
					idx := strings.LastIndex(labelVals, ":")
					//logger.Infof("idx=%+v", idx)
					if idx != -1 {
						labelVals = labelVals[:idx]
						//logger.Infof("idx=%+v labelVals=%s", idx, labelVals)
						labelValArr := strings.Split(labelVals, ":")
						if len(labelValArr) == len(labelArr) {
							pfCountKey := fmt.Sprintf("%s:%s_%s", labelVals, day, biz)
							uv, _ := s.redisClient.PFCount(ctx, pfCountKey).Result()
							prometheusLabels := prometheus.Labels{}
							for i := range labelArr {
								prometheusLabels[labelValArr[i]] = labelValArr[i]
							}
							if env.IsLocal() {
								logger.Infof("metricName=%s uv=%+v labels=%+v", metricName, uv, prometheusLabels)
							} else {
								GaugeLabels(metricName, prometheusLabels).Set(float64(uv))
							}
						}
					}
				}
			}
		} else {
			uv, err := s.redisClient.PFCount(ctx, uvKey).Result()
			if err != nil && err != redis.Nil {
				logger.Errorf("err=%+v", err)
			} else {
				if env.IsLocal() {
					logger.Infof("metricName=%s uv=%+v no labels", metricName, uv)
				} else {
					Gauge(metricName).Set(float64(uv))
				}
			}
		}
	}
}
