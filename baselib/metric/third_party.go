package metric

import (
	"time"

	"github.com/prometheus/client_golang/prometheus"
	"new-gitlab.xunlei.cn/vcproject/backends/baselib/server/env"
)

// ThirdPartyAPIMonitor 第三方API监控器
type ThirdPartyAPIMonitor struct {
	histogram *prometheus.HistogramVec
}

// NewThirdPartyAPIMonitor 创建第三方API监控器
func NewThirdPartyAPIMonitor() *ThirdPartyAPIMonitor {
	histogram := prometheus.NewHistogramVec(
		prometheus.HistogramOpts{
			Name: "third_party_api_duration_seconds",
			Help: "Third party API call duration in seconds",
			// 针对数美API优化的桶设置：10ms到10s
			Buckets: []float64{0.01, 0.05, 0.1, 0.25, 0.5, 1, 2.5, 5, 10},
			ConstLabels: prometheus.Labels{
				"pod":     env.GetHostName(),
				"service": env.GetServiceName(),
			},
		},
		[]string{"api_name", "method", "status", "biz_type"},
	)

	MustReg<PERSON>(histogram)
	return &ThirdPartyAPIMonitor{histogram: histogram}
}

// ObserveAPI 记录API调用
func (m *ThirdPartyAPIMonitor) ObserveAPI(apiName, method, bizType string, duration time.Duration, err error) {
	status := "success"
	if err != nil {
		status = "error"
	}

	m.histogram.WithLabelValues(apiName, method, status, bizType).Observe(duration.Seconds())
}

// 全局实例
var shumeiAPIMonitor = NewThirdPartyAPIMonitor()

// ShumeiAPIHistogram 数美API响应时间监控
func ShumeiAPIHistogram(apiName, method, bizType string, duration time.Duration, err error) {
	shumeiAPIMonitor.ObserveAPI(apiName, method, bizType, duration, err)
}
