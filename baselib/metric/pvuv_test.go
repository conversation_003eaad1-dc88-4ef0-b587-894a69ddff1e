package metric

import (
	"os"
	"testing"

	"github.com/prometheus/client_golang/prometheus"
	"github.com/redis/go-redis/v9"
	"new-gitlab.xunlei.cn/vcproject/backends/baselib/logger"
)

func initConfig() {
	//logger.InitLoggerWitchLevel("/dev/stdout", "debug")
	// cfg := &config.ServerConfig{
	// 	Mysql: []*config.MysqlConfig{
	// 		{
	// 			Host:     "rm-wz97c0uyj88s5j70oqo.mysql.rds.aliyuncs.com",
	// 			Port:     3306,
	// 			User:     "root",
	// 			Password: "R4b3e5YPdpPs",
	// 			DbName:   "vc_user",
	// 		},
	// 	},
	// 	Redis: []*config.RedisConfig{
	// 		{
	// 			Name:     "vc_user",
	// 			Db:       0,
	// 			Addr:     "r-wz980q7vcxubgri2ekpd.redis.rds.aliyuncs.com",
	// 			Password: "h67DzoxYAr7B",
	// 		},
	// 	},
	// 	Logger:   "/dev/stdout",
	// 	LogLevel: "debug",
	// }
	logger.InitLoggerWitchLevel("/dev/stdout", "debug")
	//cache.InitRedis(cfg.Redis)
}

func TestMain(m *testing.M) {
	initConfig()
	rdsClient := redis.NewClient(&redis.Options{
		Addr:     "r-wz980q7vcxubgri2ekpd.redis.rds.aliyuncs.com:6379",
		Password: "h67DzoxYAr7B",
	})
	//rdsCllient := cache.GetRedisClient("vc_user")
	InitPvUvReport(rdsClient, "")
	// 测试初始化
	code := m.Run()
	// 清理资源
	logger.Sync()
	// 可以添加其他清理代码
	os.Exit(code)
}

func TestReportPv(t *testing.T) {
	err := ReportPv("new_register", 1, prometheus.Labels{"register_type": "phone", "platform": "ios"})
	if err != nil {
		t.Errorf(err.Error())
	}
	err = ReportPv("new_register", 1, prometheus.Labels{"register_type": "wechat", "platform": "android"})
	if err != nil {
		t.Errorf(err.Error())
	}
	err = ReportPv("new_register", 1, prometheus.Labels{"register_type": "phone", "platform": "android"})
	if err != nil {
		t.Errorf(err.Error())
	}
}

func TestReportPvNoLabels(t *testing.T) {
	err := ReportPv("new_register2", 1, prometheus.Labels{})
	if err != nil {
		t.Errorf(err.Error())
	}
	err = ReportPv("new_register2", 1, prometheus.Labels{})
	if err != nil {
		t.Errorf(err.Error())
	}
}

func TestReportUv(t *testing.T) {
	err := ReportUv("new_register", "1101", prometheus.Labels{"register_type": "wechat", "platform": "android"})
	if err != nil {
		t.Errorf(err.Error())
	}
	err = ReportUv("new_register", "1102", prometheus.Labels{"register_type": "phone", "platform": "ios"})
	if err != nil {
		t.Errorf(err.Error())
	}
}

func TestReportUvNoLabels(t *testing.T) {
	err := ReportUv("new_register2", "1201", prometheus.Labels{})
	if err != nil {
		t.Errorf(err.Error())
	}
	err = ReportUv("new_register2", "1202", prometheus.Labels{})
	if err != nil {
		t.Errorf(err.Error())
	}
}

func TestReportPvUv(t *testing.T) {
	//reportMetrics()
}
