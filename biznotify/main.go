package main

import (
	"context"
	"os"
	"os/signal"
	"syscall"
	"time"

	"new-gitlab.xunlei.cn/vcproject/backends/baselib/config"
	"new-gitlab.xunlei.cn/vcproject/backends/baselib/ginserver"
	"new-gitlab.xunlei.cn/vcproject/backends/baselib/logger"
	"new-gitlab.xunlei.cn/vcproject/backends/baselib/metric"
	"new-gitlab.xunlei.cn/vcproject/backends/baselib/util"
	"new-gitlab.xunlei.cn/vcproject/backends/biznotify/controller"
)

func main() {
	config.Init()
	conf := config.GetServerConfig()
	logger.InitLoggerWitchLevel(conf.Logger, conf.LogLevel)
	logger.Infof("biznotify start conf=%+v", util.JsonStr(conf))

	metric.InitPrometheus(nil, "")
	controller.Init()
	// 获取HTTP服务实例
	srv := ginserver.StartWithServer()
	// 创建用于接收系统信号的通道
	quit := make(chan os.Signal, 1)
	signal.Notify(quit, syscall.SIGINT, syscall.SIGTERM)

	// 等待中断信号
	<-quit
	logger.Info("Shutting down server...")
	// 创建一个带超时的上下文
	ctx, cancel := context.WithTimeout(context.Background(), 10*time.Second)
	defer cancel()
	// 优雅关闭服务器
	if err := srv.Shutdown(ctx); err != nil {
		logger.Errorf("Server forced to shutdown: %v", err)
	}
	logger.Info("Server exiting")
}
