package controller

import (
	"encoding/json"
	"net/http"
	"time"

	"github.com/gin-gonic/gin"
	"new-gitlab.xunlei.cn/vcproject/backends/baselib/errcode"
	"new-gitlab.xunlei.cn/vcproject/backends/baselib/ginserver"
	"new-gitlab.xunlei.cn/vcproject/backends/baselib/httpcli"
	"new-gitlab.xunlei.cn/vcproject/backends/baselib/logger"
	"new-gitlab.xunlei.cn/vcproject/backends/baselib/server/env"
	"new-gitlab.xunlei.cn/vcproject/backends/baselib/util"
	"new-gitlab.xunlei.cn/vcproject/backends/proto/api/vcxxjob"
	"new-gitlab.xunlei.cn/vcproject/backends/proto/svcmgr"
)

const (
	ProdXKey = "tts-prod-7e7ab06e-d514-47ef-9084-76fa3778ab1f=="
	TestXKey = "tts-d19ab5a4-5ff3-4cc7-b478-9fccaeb0c149=="
)

type UpdateTTSStatusReq struct {
	TaskId           string `json:"task_id"`
	Status           string `json:"status"`
	TtsAudioUrl      string `json:"tts_audio_url"`
	TtsAudioDuration int32  `json:"tts_audio_duration"`
}

type TTSController struct {
}

func newTTSController() *TTSController {
	return &TTSController{}
}

func (c TTSController) Name() string {
	return "tts"
}

func (c TTSController) Routes() []ginserver.ControllerRoute {
	return []ginserver.ControllerRoute{
		{
			Method: ginserver.GET,
			Name:   "v1/preview_audios", // biznotify/tts/v1/preview_audios
			Func:   c.PreviewAudios,
		},
		{
			Method: ginserver.POST,
			Name:   "v1/tts", // biznotify/tts/v1/tts
			Func:   c.TTS,
		},
		{
			Method: ginserver.POST,
			Name:   "v1/update_tts_status", // biznotify/tts/v1/update_tts_status
			Func:   c.UpdateTTSStatus,
		},
	}
}

func (c TTSController) fail(ctx *gin.Context, msg string) {
	ctx.JSON(http.StatusOK, gin.H{
		"code": errcode.ErrNotOK.Code,
		"msg":  msg,
	})
}

func (c TTSController) success(ctx *gin.Context, data interface{}) {
	body := gin.H{
		"code": 0,
		"msg":  "success",
	}
	if data != nil {
		body["data"] = data
	}
	ctx.JSON(http.StatusOK, body)
}

func (c TTSController) PreviewAudios(ctx *gin.Context) {
	if env.IsProd() {
		c.success(ctx, nil)
		return
	}
	// 发送 GET 请求到指定 URL
	url := "http://14.103.229.186:50007/v1/preview_audios"
	headers := map[string]string{
		"Content-Type": "application/json",
	}
	body, err := httpcli.Get(url, headers, nil)
	if err != nil {
		logger.Errorf("PreviewAudios request failed: %v", err)
		c.fail(ctx, "failed")
		return
	}
	resp := map[string]interface{}{}
	err = json.Unmarshal([]byte(body), &resp)
	if err != nil {
		logger.Errorf("PreviewAudios Unmarshal failed: %v", err)
		c.fail(ctx, "failed")
		return
	}
	ctx.JSON(http.StatusOK, resp)
}

func (c TTSController) TTS(ctx *gin.Context) {
	if env.IsProd() {
		c.success(ctx, nil)
		return
	}
	// 从请求体中获取数据
	var requestData map[string]interface{}
	if err := ctx.ShouldBindJSON(&requestData); err != nil {
		logger.Errorf("TTS bind JSON failed: %v", err)
		c.fail(ctx, "failed")
		return
	}

	url := "http://14.103.229.186:50007/v1/tts"
	headers := map[string]string{
		"Content-Type": "application/json",
	}
	// 发送 POST 请求转发数据
	body, err := httpcli.PostJson(url, headers, requestData, time.Duration(180))
	if err != nil {
		logger.Errorf("TTS request failed: %v", err)
		c.fail(ctx, "failed")
		return
	}
	resp := map[string]interface{}{}
	err = json.Unmarshal([]byte(body), &resp)
	if err != nil {
		logger.Errorf("PreviewAudios Unmarshal failed: %v", err)
		c.fail(ctx, "failed")
		return
	}
	ctx.JSON(http.StatusOK, resp)
}

func (c TTSController) UpdateTTSStatus(ctx *gin.Context) {
	// get x-key from header
	xkey := ctx.GetHeader("x-key")
	if len(xkey) == 0 {
		xkey = ctx.GetHeader("X-Key")
	}
	if env.IsProd() {
		if xkey != ProdXKey {
			c.fail(ctx, "xkey error")
			return
		}
	} else {
		if xkey != TestXKey {
			c.fail(ctx, "xkey error")
			return
		}
	}
	// 从请求体中获取数据
	requestData := UpdateTTSStatusReq{}
	if err := ctx.ShouldBindJSON(&requestData); err != nil {
		logger.Errorf("TTS bind JSON failed: %v", err)
		c.fail(ctx, err.Error())
		return
	}
	logger.Infof("requestData = %+v", util.JsonStr(requestData))
	resp, err := svcmgr.VcxxjobClient().UpdateVcTask(ctx, &vcxxjob.UpdateVcTaskReq{
		TaskId:           requestData.TaskId,
		Status:           requestData.Status,
		TtsAudioUrl:      requestData.TtsAudioUrl,
		TtsAudioDuration: requestData.TtsAudioDuration,
	})
	if err != nil || !errcode.IsOk(resp) {
		logger.Errorf("UpdateVcTask failed: %v, resp", err, util.JsonStr(resp))
		c.fail(ctx, err.Error())
		return
	}
	c.success(ctx, nil)
}
