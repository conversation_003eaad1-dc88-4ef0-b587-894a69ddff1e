package controller

import (
	"io"
	"net/http"

	"github.com/gin-gonic/gin"
	"new-gitlab.xunlei.cn/vcproject/backends/baselib/errcode"
	"new-gitlab.xunlei.cn/vcproject/backends/baselib/ginserver"
	"new-gitlab.xunlei.cn/vcproject/backends/baselib/logger"
	"new-gitlab.xunlei.cn/vcproject/backends/baselib/util"
	"new-gitlab.xunlei.cn/vcproject/backends/proto/api/svcreview"
	"new-gitlab.xunlei.cn/vcproject/backends/proto/svcmgr"
)

type ShumeiController struct {
}

func newShumeiController() *ShumeiController {
	return &ShumeiController{}
}

func (c ShumeiController) Name() string {
	return "shumei"
}

func (c <PERSON><PERSON>Controller) Routes() []ginserver.ControllerRoute {
	return []ginserver.ControllerRoute{
		{
			Method: ginserver.POST,
			Name:   "audio_callback", // biznotify/shumei/audio_callback
			Func:   c.AudioCallback,
		},
		{
			Method: ginserver.POST,
			Name:   "audio_stream_callback", // biznotify/shumei/audio_stream_callback
			Func:   c.AudioStreamCallback,
		},
		{
			Method: ginserver.POST,
			Name:   "video_stream_callback", // biznotify/shumei/video_stream_callback
			Func:   c.VideoStreamCallback,
		},
		{
			Method: ginserver.POST,
			Name:   "mix_callback", // biznotify/shumei/mix_callback
			Func:   c.MixCallback,
		},
		{
			Method: ginserver.POST,
			Name:   "multi_image_callback", // biznotify/shumei/multi_image_callback
			Func:   c.MultiImageCallback,
		},
	}
}

func (c ShumeiController) fail(ctx *gin.Context) {
	ctx.JSON(http.StatusOK, gin.H{
		"code": errcode.ErrOK.Code,
		"msg":  "fail",
	})
}

func (c ShumeiController) success(ctx *gin.Context) {
	ctx.JSON(http.StatusOK, gin.H{
		"code": 0,
		"msg":  "success",
	})
}

func (c ShumeiController) MultiImageCallback(ctx *gin.Context) {
	defer ctx.Request.Body.Close()
	content, err := io.ReadAll(ctx.Request.Body)
	if err != nil {
		logger.Errorf("shumei mix callback body:%v", util.JsonStr(ctx.Request.Body))
		c.fail(ctx)
		return
	}
	logger.Debugf("content=%+v", content)
	// req := &svcreview.ShumeiMultiImageCallbackReq{}
	// err = util.Unmarshal(content, req)
	// if err != nil {
	// 	logger.Errorf("shumei mix callback body:%v,err:%v", string(content), err)
	// 	c.fail(ctx)
	// 	return
	// }

	// logger.Infof("shumei mix callback:%v", req)

	// callbackResp, err := svcmgr.ReviewClient().ShumeiMultiImageCallback(ctx, req)
	// if err != nil || !errcode.IsOk(callbackResp) {
	// 	logger.Errorf("error shumei mix callback fail,req:%#v,resp:%#v,err:%v", req, util.JsonStr(callbackResp), err)
	// 	c.fail(ctx)
	// 	return
	// }
	c.success(ctx)
}

func (c ShumeiController) MixCallback(ctx *gin.Context) {
	defer ctx.Request.Body.Close()
	content, err := io.ReadAll(ctx.Request.Body)
	if err != nil {
		logger.Errorf("shumei mix callback body:%v", util.JsonStr(ctx.Request.Body))
		c.fail(ctx)
		return
	}
	logger.Debugf("content=%+v", content)
	// req := &svcreview.ShumeiMixCallbackReq{}
	// err = util.Unmarshal(content, req)
	// if err != nil {
	// 	logger.Errorf("shumei mix callback body:%v,err:%v", string(content), err)
	// 	c.fail(ctx)
	// 	return
	// }

	// logger.Infof("shumei mix callback:%v", req)

	// callbackResp, err := svcmgr.ReviewClient().ShumeiMixCallback(ctx, req)
	// if err != nil || !errcode.IsOk(callbackResp) {
	// 	logger.Errorf("error shumei mix callback fail,req:%#v,resp:%#v,err:%v", req, util.JsonStr(callbackResp), err)
	// 	c.fail(ctx)
	// 	return
	// }
	c.success(ctx)
}

func (c ShumeiController) AudioCallback(ctx *gin.Context) {
	defer ctx.Request.Body.Close()
	content, err := io.ReadAll(ctx.Request.Body)
	if err != nil {
		logger.Errorf("shumei audio callback body:%v", util.JsonStr(ctx.Request.Body))
		c.fail(ctx)
		return
	}
	logger.Debugf("content=%+v", content)
	req := &svcreview.ShumeiAudioCallbackReq{}
	err = util.Unmarshal(content, req)
	if err != nil {
		logger.Errorf("shumei audio callback body:%v,err:%v", string(content), err)
		c.fail(ctx)
		return
	}
	logger.Infof("shumei audio callback:%v", req)
	callbackResp, err := svcmgr.ReviewClient().ShumeiAudioCallback(ctx, req)
	if err != nil || !errcode.IsOk(callbackResp) {
		logger.Errorf("error shumei audio callback fail,req:%#v,resp:%#v,err:%v", req, util.JsonStr(callbackResp), err)
		c.fail(ctx)
		return
	}
	c.success(ctx)
}

func (c ShumeiController) AudioStreamCallback(ctx *gin.Context) {
	defer ctx.Request.Body.Close()
	content, err := io.ReadAll(ctx.Request.Body)
	if err != nil {
		logger.Errorf("shumei audio stream callback body:%v", util.JsonStr(ctx.Request.Body))
		c.fail(ctx)
		return
	}
	logger.Debugf("content=%+v", content)
	// resp, err := svcmgr.ReviewClient().ShumeiAudioStreamCallback(context.Background(), &svcreview.ShumeiAudioStreamCallbackReq{
	// 	Body: string(content),
	// })

	// if err != nil {
	// 	logger.Errorf("AudioStreamCallback err %v,body %v", err, string(content))
	// 	c.fail(ctx)
	// 	return
	// }

	// if errcode.NotOk(resp) {
	// 	logger.Errorf("AudioStreamCallback resp %v, body %v", util.JsonStr(resp), string(content))
	// 	c.fail(ctx)
	// 	return
	// }
	c.success(ctx)
}

func (c ShumeiController) VideoStreamCallback(ctx *gin.Context) {
	defer ctx.Request.Body.Close()
	content, err := io.ReadAll(ctx.Request.Body)
	if err != nil {
		logger.Errorf("shumei video live callback body:%v", util.JsonStr(ctx.Request.Body))
		c.fail(ctx)
		return
	}
	logger.Debugf("content=%+v", content)
	// req := &svcreview.ShumeiVideoLiveCallbackReq{}
	// err = util.Unmarshal(content, req)
	// if err != nil {
	// 	logger.Errorf("shumei video callback body:%v,err:%v", string(content), err)
	// 	c.fail(ctx)
	// 	return
	// }

	// logger.Infof("shumei video stream callback:%v", req)

	// callbackResp, err := svcmgr.ReviewClient().ShumeiVideoStreamCallback(ctx, req)
	// if err != nil || !errcode.IsOk(callbackResp) {
	// 	logger.Errorf("error shumei video callback fail,req:%#v,resp:%#v,err:%v", req, util.JsonStr(callbackResp), err)
	// 	c.fail(ctx)
	// 	return
	// }
	c.success(ctx)
}
