package event

import (
	"context"

	"new-gitlab.xunlei.cn/vcproject/backends/baselib/logger"
	"new-gitlab.xunlei.cn/vcproject/backends/baselib/nsqutil"
	"new-gitlab.xunlei.cn/vcproject/backends/baselib/nsqutil/event"
)

type TTSLineInputData struct {
	ReferAudioUrl    string `json:"refer_audio_url"`    // 试听音频地址
	PromptText       string `json:"prompt_text"`        // 提示文本
	LineID           int64  `json:"line_id"`            // 台词ID
	LineText         string `json:"text"`               // 台词文本
	CharacterID      int64  `json:"character_id"`       // 角色ID
	CharacterAssetID int64  `json:"character_asset_id"` // 角色素材ID
	UseVc            bool   `json:"use_vc"`             // 是否使用变声
	UseRvc           bool   `json:"use_rvc"`            //是否使用rvc
	ScriptID         int64  `json:"script_id"`          //剧本ID
	TTSEngine        string `json:"tts_engine"`         // tts引擎
	TaskId           string `json:"task_id"`            // 任务id
	//ReferenceAudioUseRvc bool   `json:"reference_audio_use_rvc"` //生成参考音频是否使用rvc
}

type RvcInputData struct {
	LineID           int64  `json:"line_id"`            // 台词ID
	CharacterID      int64  `json:"character_id"`       // 角色ID
	CharacterAssetID int64  `json:"character_asset_id"` // 角色素材ID
	ScriptID         int64  `json:"script_id"`          //剧本ID
	TtsAudioUrl      string `json:"tts_audio_url"`      // tts变声的结果
	TtsAudioDuration int64  `json:"tts_audio_duration"` // tts变声的结果时长，毫秒
	UseRvc           bool   `json:"use_rvc"`            // 是否使用rvc变声
	TaskId           string `json:"task_id"`
}

type TTSLineOutputData struct {
	LineID          int64  `json:"line_id"`          // 台词ID
	DubbingURL      string `json:"dubbing_url"`      // 配音地址
	DubbingDuration int32  `json:"dubbing_duration"` // 配音时长（毫秒）
}

// 输入事件
func GenTTSLineInputEvent(eType event.EType, data interface{}) (e *event.BaseEvent) {
	return event.GenBaseEvent(OTypeTTSInput, eType, data)
}

func PushTTSLineInput(data TTSLineInputData) (err error) {
	err = nsqutil.PublishJSON(context.Background(), TopicTTS, GenTTSLineInputEvent(ETypeTTSInput, data))
	if err != nil {
		logger.Errorf("NsqEvent PushTTSLineInput err: %v", err)
		return err
	}
	return
}

// 输出事件
func GenTTSLineOutputEvent(eType event.EType, data interface{}) (e *event.BaseEvent) {
	return event.GenBaseEvent(OTypeTTSOutput, eType, data)
}

func PushTTSLineOutput(data TTSLineOutputData) (err error) {
	err = nsqutil.PublishJSON(context.Background(), TopicTTS, GenTTSLineOutputEvent(ETypeTTSOutput, data))
	if err != nil {
		logger.Errorf("NsqEvent PushTTSLineOutput err: %v", err)
		return err
	}
	return
}

// vc变声输入事件
func GenVcInputEvent(eType event.EType, data interface{}) (e *event.BaseEvent) {
	return event.GenBaseEvent(OTypeTTSInput, eType, data)
}

func PushVcLineInput(data RvcInputData) (err error) {
	err = nsqutil.PublishJSON(context.Background(), TopicTTS, GenVcInputEvent(ETypeRvcInput, data))
	if err != nil {
		logger.Errorf("NsqEvent PushVcLineInput err: %v", err)
		return err
	}
	return
}
