// Code generated by protoc-gen-validate. DO NOT EDIT.
// source: svcreview.proto

package svcreview

import (
	"bytes"
	"errors"
	"fmt"
	"net"
	"net/mail"
	"net/url"
	"regexp"
	"sort"
	"strings"
	"time"
	"unicode/utf8"

	"google.golang.org/protobuf/types/known/anypb"
)

// ensure the imports are used
var (
	_ = bytes.MinRead
	_ = errors.New("")
	_ = fmt.Print
	_ = utf8.UTFMax
	_ = (*regexp.Regexp)(nil)
	_ = (*strings.Reader)(nil)
	_ = net.IPv4len
	_ = time.Duration(0)
	_ = (*url.URL)(nil)
	_ = (*mail.Address)(nil)
	_ = anypb.Any{}
	_ = sort.Sort
)

// Validate checks the field values on ReviewImage with the rules defined in
// the proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *ReviewImage) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on ReviewImage with the rules defined in
// the proto definition for this message. If any rules are violated, the
// result is a list of violation errors wrapped in ReviewImageMultiError, or
// nil if none found.
func (m *ReviewImage) ValidateAll() error {
	return m.validate(true)
}

func (m *ReviewImage) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Id

	// no validation rules for Url

	if len(errors) > 0 {
		return ReviewImageMultiError(errors)
	}

	return nil
}

// ReviewImageMultiError is an error wrapping multiple validation errors
// returned by ReviewImage.ValidateAll() if the designated constraints aren't met.
type ReviewImageMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m ReviewImageMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m ReviewImageMultiError) AllErrors() []error { return m }

// ReviewImageValidationError is the validation error returned by
// ReviewImage.Validate if the designated constraints aren't met.
type ReviewImageValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e ReviewImageValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e ReviewImageValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e ReviewImageValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e ReviewImageValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e ReviewImageValidationError) ErrorName() string { return "ReviewImageValidationError" }

// Error satisfies the builtin error interface
func (e ReviewImageValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sReviewImage.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = ReviewImageValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = ReviewImageValidationError{}

// Validate checks the field values on ReviewVoice with the rules defined in
// the proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *ReviewVoice) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on ReviewVoice with the rules defined in
// the proto definition for this message. If any rules are violated, the
// result is a list of violation errors wrapped in ReviewVoiceMultiError, or
// nil if none found.
func (m *ReviewVoice) ValidateAll() error {
	return m.validate(true)
}

func (m *ReviewVoice) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Id

	// no validation rules for Sec

	// no validation rules for Url

	if len(errors) > 0 {
		return ReviewVoiceMultiError(errors)
	}

	return nil
}

// ReviewVoiceMultiError is an error wrapping multiple validation errors
// returned by ReviewVoice.ValidateAll() if the designated constraints aren't met.
type ReviewVoiceMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m ReviewVoiceMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m ReviewVoiceMultiError) AllErrors() []error { return m }

// ReviewVoiceValidationError is the validation error returned by
// ReviewVoice.Validate if the designated constraints aren't met.
type ReviewVoiceValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e ReviewVoiceValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e ReviewVoiceValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e ReviewVoiceValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e ReviewVoiceValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e ReviewVoiceValidationError) ErrorName() string { return "ReviewVoiceValidationError" }

// Error satisfies the builtin error interface
func (e ReviewVoiceValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sReviewVoice.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = ReviewVoiceValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = ReviewVoiceValidationError{}

// Validate checks the field values on ReviewVideo with the rules defined in
// the proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *ReviewVideo) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on ReviewVideo with the rules defined in
// the proto definition for this message. If any rules are violated, the
// result is a list of violation errors wrapped in ReviewVideoMultiError, or
// nil if none found.
func (m *ReviewVideo) ValidateAll() error {
	return m.validate(true)
}

func (m *ReviewVideo) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Id

	// no validation rules for Sec

	// no validation rules for Url

	if len(errors) > 0 {
		return ReviewVideoMultiError(errors)
	}

	return nil
}

// ReviewVideoMultiError is an error wrapping multiple validation errors
// returned by ReviewVideo.ValidateAll() if the designated constraints aren't met.
type ReviewVideoMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m ReviewVideoMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m ReviewVideoMultiError) AllErrors() []error { return m }

// ReviewVideoValidationError is the validation error returned by
// ReviewVideo.Validate if the designated constraints aren't met.
type ReviewVideoValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e ReviewVideoValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e ReviewVideoValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e ReviewVideoValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e ReviewVideoValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e ReviewVideoValidationError) ErrorName() string { return "ReviewVideoValidationError" }

// Error satisfies the builtin error interface
func (e ReviewVideoValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sReviewVideo.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = ReviewVideoValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = ReviewVideoValidationError{}

// Validate checks the field values on ShumeiAudioCallbackReq with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *ShumeiAudioCallbackReq) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on ShumeiAudioCallbackReq with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// ShumeiAudioCallbackReqMultiError, or nil if none found.
func (m *ShumeiAudioCallbackReq) ValidateAll() error {
	return m.validate(true)
}

func (m *ShumeiAudioCallbackReq) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Code

	// no validation rules for Message

	// no validation rules for RequestId

	// no validation rules for BtId

	// no validation rules for AudioText

	// no validation rules for AudioTime

	// no validation rules for Labels

	// no validation rules for RiskLevel

	for idx, item := range m.GetDetail() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, ShumeiAudioCallbackReqValidationError{
						field:  fmt.Sprintf("Detail[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, ShumeiAudioCallbackReqValidationError{
						field:  fmt.Sprintf("Detail[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return ShumeiAudioCallbackReqValidationError{
					field:  fmt.Sprintf("Detail[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	if all {
		switch v := interface{}(m.GetGender()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, ShumeiAudioCallbackReqValidationError{
					field:  "Gender",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, ShumeiAudioCallbackReqValidationError{
					field:  "Gender",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetGender()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return ShumeiAudioCallbackReqValidationError{
				field:  "Gender",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	for idx, item := range m.GetTags() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, ShumeiAudioCallbackReqValidationError{
						field:  fmt.Sprintf("Tags[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, ShumeiAudioCallbackReqValidationError{
						field:  fmt.Sprintf("Tags[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return ShumeiAudioCallbackReqValidationError{
					field:  fmt.Sprintf("Tags[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	if all {
		switch v := interface{}(m.GetCallbackParam()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, ShumeiAudioCallbackReqValidationError{
					field:  "CallbackParam",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, ShumeiAudioCallbackReqValidationError{
					field:  "CallbackParam",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetCallbackParam()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return ShumeiAudioCallbackReqValidationError{
				field:  "CallbackParam",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return ShumeiAudioCallbackReqMultiError(errors)
	}

	return nil
}

// ShumeiAudioCallbackReqMultiError is an error wrapping multiple validation
// errors returned by ShumeiAudioCallbackReq.ValidateAll() if the designated
// constraints aren't met.
type ShumeiAudioCallbackReqMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m ShumeiAudioCallbackReqMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m ShumeiAudioCallbackReqMultiError) AllErrors() []error { return m }

// ShumeiAudioCallbackReqValidationError is the validation error returned by
// ShumeiAudioCallbackReq.Validate if the designated constraints aren't met.
type ShumeiAudioCallbackReqValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e ShumeiAudioCallbackReqValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e ShumeiAudioCallbackReqValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e ShumeiAudioCallbackReqValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e ShumeiAudioCallbackReqValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e ShumeiAudioCallbackReqValidationError) ErrorName() string {
	return "ShumeiAudioCallbackReqValidationError"
}

// Error satisfies the builtin error interface
func (e ShumeiAudioCallbackReqValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sShumeiAudioCallbackReq.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = ShumeiAudioCallbackReqValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = ShumeiAudioCallbackReqValidationError{}

// Validate checks the field values on ShumeiAudioDetail with the rules defined
// in the proto definition for this message. If any rules are violated, the
// first error encountered is returned, or nil if there are no violations.
func (m *ShumeiAudioDetail) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on ShumeiAudioDetail with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// ShumeiAudioDetailMultiError, or nil if none found.
func (m *ShumeiAudioDetail) ValidateAll() error {
	return m.validate(true)
}

func (m *ShumeiAudioDetail) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for AudioStarttime

	// no validation rules for AudioEndtime

	// no validation rules for AudioUrl

	// no validation rules for AudioText

	// no validation rules for RiskLevel

	// no validation rules for RiskType

	// no validation rules for AudioMatchedItem

	// no validation rules for Description

	if len(errors) > 0 {
		return ShumeiAudioDetailMultiError(errors)
	}

	return nil
}

// ShumeiAudioDetailMultiError is an error wrapping multiple validation errors
// returned by ShumeiAudioDetail.ValidateAll() if the designated constraints
// aren't met.
type ShumeiAudioDetailMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m ShumeiAudioDetailMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m ShumeiAudioDetailMultiError) AllErrors() []error { return m }

// ShumeiAudioDetailValidationError is the validation error returned by
// ShumeiAudioDetail.Validate if the designated constraints aren't met.
type ShumeiAudioDetailValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e ShumeiAudioDetailValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e ShumeiAudioDetailValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e ShumeiAudioDetailValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e ShumeiAudioDetailValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e ShumeiAudioDetailValidationError) ErrorName() string {
	return "ShumeiAudioDetailValidationError"
}

// Error satisfies the builtin error interface
func (e ShumeiAudioDetailValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sShumeiAudioDetail.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = ShumeiAudioDetailValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = ShumeiAudioDetailValidationError{}

// Validate checks the field values on ShumeiAudioGender with the rules defined
// in the proto definition for this message. If any rules are violated, the
// first error encountered is returned, or nil if there are no violations.
func (m *ShumeiAudioGender) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on ShumeiAudioGender with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// ShumeiAudioGenderMultiError, or nil if none found.
func (m *ShumeiAudioGender) ValidateAll() error {
	return m.validate(true)
}

func (m *ShumeiAudioGender) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Label

	// no validation rules for Confidence

	if len(errors) > 0 {
		return ShumeiAudioGenderMultiError(errors)
	}

	return nil
}

// ShumeiAudioGenderMultiError is an error wrapping multiple validation errors
// returned by ShumeiAudioGender.ValidateAll() if the designated constraints
// aren't met.
type ShumeiAudioGenderMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m ShumeiAudioGenderMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m ShumeiAudioGenderMultiError) AllErrors() []error { return m }

// ShumeiAudioGenderValidationError is the validation error returned by
// ShumeiAudioGender.Validate if the designated constraints aren't met.
type ShumeiAudioGenderValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e ShumeiAudioGenderValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e ShumeiAudioGenderValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e ShumeiAudioGenderValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e ShumeiAudioGenderValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e ShumeiAudioGenderValidationError) ErrorName() string {
	return "ShumeiAudioGenderValidationError"
}

// Error satisfies the builtin error interface
func (e ShumeiAudioGenderValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sShumeiAudioGender.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = ShumeiAudioGenderValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = ShumeiAudioGenderValidationError{}

// Validate checks the field values on ShumeiAudioTags with the rules defined
// in the proto definition for this message. If any rules are violated, the
// first error encountered is returned, or nil if there are no violations.
func (m *ShumeiAudioTags) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on ShumeiAudioTags with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// ShumeiAudioTagsMultiError, or nil if none found.
func (m *ShumeiAudioTags) ValidateAll() error {
	return m.validate(true)
}

func (m *ShumeiAudioTags) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Label

	// no validation rules for Confidence

	if len(errors) > 0 {
		return ShumeiAudioTagsMultiError(errors)
	}

	return nil
}

// ShumeiAudioTagsMultiError is an error wrapping multiple validation errors
// returned by ShumeiAudioTags.ValidateAll() if the designated constraints
// aren't met.
type ShumeiAudioTagsMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m ShumeiAudioTagsMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m ShumeiAudioTagsMultiError) AllErrors() []error { return m }

// ShumeiAudioTagsValidationError is the validation error returned by
// ShumeiAudioTags.Validate if the designated constraints aren't met.
type ShumeiAudioTagsValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e ShumeiAudioTagsValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e ShumeiAudioTagsValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e ShumeiAudioTagsValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e ShumeiAudioTagsValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e ShumeiAudioTagsValidationError) ErrorName() string { return "ShumeiAudioTagsValidationError" }

// Error satisfies the builtin error interface
func (e ShumeiAudioTagsValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sShumeiAudioTags.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = ShumeiAudioTagsValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = ShumeiAudioTagsValidationError{}

// Validate checks the field values on ReviewCallbackOrigin with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *ReviewCallbackOrigin) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on ReviewCallbackOrigin with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// ReviewCallbackOriginMultiError, or nil if none found.
func (m *ReviewCallbackOrigin) ValidateAll() error {
	return m.validate(true)
}

func (m *ReviewCallbackOrigin) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Seqid

	// no validation rules for Content

	// no validation rules for BizType

	if len(errors) > 0 {
		return ReviewCallbackOriginMultiError(errors)
	}

	return nil
}

// ReviewCallbackOriginMultiError is an error wrapping multiple validation
// errors returned by ReviewCallbackOrigin.ValidateAll() if the designated
// constraints aren't met.
type ReviewCallbackOriginMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m ReviewCallbackOriginMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m ReviewCallbackOriginMultiError) AllErrors() []error { return m }

// ReviewCallbackOriginValidationError is the validation error returned by
// ReviewCallbackOrigin.Validate if the designated constraints aren't met.
type ReviewCallbackOriginValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e ReviewCallbackOriginValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e ReviewCallbackOriginValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e ReviewCallbackOriginValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e ReviewCallbackOriginValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e ReviewCallbackOriginValidationError) ErrorName() string {
	return "ReviewCallbackOriginValidationError"
}

// Error satisfies the builtin error interface
func (e ReviewCallbackOriginValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sReviewCallbackOrigin.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = ReviewCallbackOriginValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = ReviewCallbackOriginValidationError{}

// Validate checks the field values on ReviewCommonResp with the rules defined
// in the proto definition for this message. If any rules are violated, the
// first error encountered is returned, or nil if there are no violations.
func (m *ReviewCommonResp) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on ReviewCommonResp with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// ReviewCommonRespMultiError, or nil if none found.
func (m *ReviewCommonResp) ValidateAll() error {
	return m.validate(true)
}

func (m *ReviewCommonResp) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetBase()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, ReviewCommonRespValidationError{
					field:  "Base",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, ReviewCommonRespValidationError{
					field:  "Base",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetBase()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return ReviewCommonRespValidationError{
				field:  "Base",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for Result

	// no validation rules for Reason

	if all {
		switch v := interface{}(m.GetDetail()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, ReviewCommonRespValidationError{
					field:  "Detail",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, ReviewCommonRespValidationError{
					field:  "Detail",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetDetail()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return ReviewCommonRespValidationError{
				field:  "Detail",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return ReviewCommonRespMultiError(errors)
	}

	return nil
}

// ReviewCommonRespMultiError is an error wrapping multiple validation errors
// returned by ReviewCommonResp.ValidateAll() if the designated constraints
// aren't met.
type ReviewCommonRespMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m ReviewCommonRespMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m ReviewCommonRespMultiError) AllErrors() []error { return m }

// ReviewCommonRespValidationError is the validation error returned by
// ReviewCommonResp.Validate if the designated constraints aren't met.
type ReviewCommonRespValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e ReviewCommonRespValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e ReviewCommonRespValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e ReviewCommonRespValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e ReviewCommonRespValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e ReviewCommonRespValidationError) ErrorName() string { return "ReviewCommonRespValidationError" }

// Error satisfies the builtin error interface
func (e ReviewCommonRespValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sReviewCommonResp.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = ReviewCommonRespValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = ReviewCommonRespValidationError{}

// Validate checks the field values on ReviewResultDetail with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *ReviewResultDetail) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on ReviewResultDetail with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// ReviewResultDetailMultiError, or nil if none found.
func (m *ReviewResultDetail) ValidateAll() error {
	return m.validate(true)
}

func (m *ReviewResultDetail) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Result

	if all {
		switch v := interface{}(m.GetText()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, ReviewResultDetailValidationError{
					field:  "Text",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, ReviewResultDetailValidationError{
					field:  "Text",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetText()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return ReviewResultDetailValidationError{
				field:  "Text",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	for idx, item := range m.GetImage() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, ReviewResultDetailValidationError{
						field:  fmt.Sprintf("Image[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, ReviewResultDetailValidationError{
						field:  fmt.Sprintf("Image[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return ReviewResultDetailValidationError{
					field:  fmt.Sprintf("Image[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	if all {
		switch v := interface{}(m.GetVoice()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, ReviewResultDetailValidationError{
					field:  "Voice",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, ReviewResultDetailValidationError{
					field:  "Voice",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetVoice()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return ReviewResultDetailValidationError{
				field:  "Voice",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetVideo()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, ReviewResultDetailValidationError{
					field:  "Video",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, ReviewResultDetailValidationError{
					field:  "Video",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetVideo()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return ReviewResultDetailValidationError{
				field:  "Video",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for NoneedPushTips

	if len(errors) > 0 {
		return ReviewResultDetailMultiError(errors)
	}

	return nil
}

// ReviewResultDetailMultiError is an error wrapping multiple validation errors
// returned by ReviewResultDetail.ValidateAll() if the designated constraints
// aren't met.
type ReviewResultDetailMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m ReviewResultDetailMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m ReviewResultDetailMultiError) AllErrors() []error { return m }

// ReviewResultDetailValidationError is the validation error returned by
// ReviewResultDetail.Validate if the designated constraints aren't met.
type ReviewResultDetailValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e ReviewResultDetailValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e ReviewResultDetailValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e ReviewResultDetailValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e ReviewResultDetailValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e ReviewResultDetailValidationError) ErrorName() string {
	return "ReviewResultDetailValidationError"
}

// Error satisfies the builtin error interface
func (e ReviewResultDetailValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sReviewResultDetail.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = ReviewResultDetailValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = ReviewResultDetailValidationError{}

// Validate checks the field values on ReviewImageResult with the rules defined
// in the proto definition for this message. If any rules are violated, the
// first error encountered is returned, or nil if there are no violations.
func (m *ReviewImageResult) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on ReviewImageResult with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// ReviewImageResultMultiError, or nil if none found.
func (m *ReviewImageResult) ValidateAll() error {
	return m.validate(true)
}

func (m *ReviewImageResult) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetImage()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, ReviewImageResultValidationError{
					field:  "Image",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, ReviewImageResultValidationError{
					field:  "Image",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetImage()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return ReviewImageResultValidationError{
				field:  "Image",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for Result

	// no validation rules for Reason

	if len(errors) > 0 {
		return ReviewImageResultMultiError(errors)
	}

	return nil
}

// ReviewImageResultMultiError is an error wrapping multiple validation errors
// returned by ReviewImageResult.ValidateAll() if the designated constraints
// aren't met.
type ReviewImageResultMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m ReviewImageResultMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m ReviewImageResultMultiError) AllErrors() []error { return m }

// ReviewImageResultValidationError is the validation error returned by
// ReviewImageResult.Validate if the designated constraints aren't met.
type ReviewImageResultValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e ReviewImageResultValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e ReviewImageResultValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e ReviewImageResultValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e ReviewImageResultValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e ReviewImageResultValidationError) ErrorName() string {
	return "ReviewImageResultValidationError"
}

// Error satisfies the builtin error interface
func (e ReviewImageResultValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sReviewImageResult.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = ReviewImageResultValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = ReviewImageResultValidationError{}

// Validate checks the field values on ReviewVoiceResult with the rules defined
// in the proto definition for this message. If any rules are violated, the
// first error encountered is returned, or nil if there are no violations.
func (m *ReviewVoiceResult) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on ReviewVoiceResult with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// ReviewVoiceResultMultiError, or nil if none found.
func (m *ReviewVoiceResult) ValidateAll() error {
	return m.validate(true)
}

func (m *ReviewVoiceResult) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetVoice()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, ReviewVoiceResultValidationError{
					field:  "Voice",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, ReviewVoiceResultValidationError{
					field:  "Voice",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetVoice()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return ReviewVoiceResultValidationError{
				field:  "Voice",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for Result

	// no validation rules for Reason

	if len(errors) > 0 {
		return ReviewVoiceResultMultiError(errors)
	}

	return nil
}

// ReviewVoiceResultMultiError is an error wrapping multiple validation errors
// returned by ReviewVoiceResult.ValidateAll() if the designated constraints
// aren't met.
type ReviewVoiceResultMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m ReviewVoiceResultMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m ReviewVoiceResultMultiError) AllErrors() []error { return m }

// ReviewVoiceResultValidationError is the validation error returned by
// ReviewVoiceResult.Validate if the designated constraints aren't met.
type ReviewVoiceResultValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e ReviewVoiceResultValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e ReviewVoiceResultValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e ReviewVoiceResultValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e ReviewVoiceResultValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e ReviewVoiceResultValidationError) ErrorName() string {
	return "ReviewVoiceResultValidationError"
}

// Error satisfies the builtin error interface
func (e ReviewVoiceResultValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sReviewVoiceResult.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = ReviewVoiceResultValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = ReviewVoiceResultValidationError{}

// Validate checks the field values on ReviewVideoResult with the rules defined
// in the proto definition for this message. If any rules are violated, the
// first error encountered is returned, or nil if there are no violations.
func (m *ReviewVideoResult) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on ReviewVideoResult with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// ReviewVideoResultMultiError, or nil if none found.
func (m *ReviewVideoResult) ValidateAll() error {
	return m.validate(true)
}

func (m *ReviewVideoResult) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetVideo()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, ReviewVideoResultValidationError{
					field:  "Video",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, ReviewVideoResultValidationError{
					field:  "Video",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetVideo()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return ReviewVideoResultValidationError{
				field:  "Video",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for Result

	// no validation rules for Reason

	if len(errors) > 0 {
		return ReviewVideoResultMultiError(errors)
	}

	return nil
}

// ReviewVideoResultMultiError is an error wrapping multiple validation errors
// returned by ReviewVideoResult.ValidateAll() if the designated constraints
// aren't met.
type ReviewVideoResultMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m ReviewVideoResultMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m ReviewVideoResultMultiError) AllErrors() []error { return m }

// ReviewVideoResultValidationError is the validation error returned by
// ReviewVideoResult.Validate if the designated constraints aren't met.
type ReviewVideoResultValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e ReviewVideoResultValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e ReviewVideoResultValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e ReviewVideoResultValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e ReviewVideoResultValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e ReviewVideoResultValidationError) ErrorName() string {
	return "ReviewVideoResultValidationError"
}

// Error satisfies the builtin error interface
func (e ReviewVideoResultValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sReviewVideoResult.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = ReviewVideoResultValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = ReviewVideoResultValidationError{}

// Validate checks the field values on ReviewTextResult with the rules defined
// in the proto definition for this message. If any rules are violated, the
// first error encountered is returned, or nil if there are no violations.
func (m *ReviewTextResult) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on ReviewTextResult with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// ReviewTextResultMultiError, or nil if none found.
func (m *ReviewTextResult) ValidateAll() error {
	return m.validate(true)
}

func (m *ReviewTextResult) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Text

	// no validation rules for Result

	// no validation rules for Reason

	if len(errors) > 0 {
		return ReviewTextResultMultiError(errors)
	}

	return nil
}

// ReviewTextResultMultiError is an error wrapping multiple validation errors
// returned by ReviewTextResult.ValidateAll() if the designated constraints
// aren't met.
type ReviewTextResultMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m ReviewTextResultMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m ReviewTextResultMultiError) AllErrors() []error { return m }

// ReviewTextResultValidationError is the validation error returned by
// ReviewTextResult.Validate if the designated constraints aren't met.
type ReviewTextResultValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e ReviewTextResultValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e ReviewTextResultValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e ReviewTextResultValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e ReviewTextResultValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e ReviewTextResultValidationError) ErrorName() string { return "ReviewTextResultValidationError" }

// Error satisfies the builtin error interface
func (e ReviewTextResultValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sReviewTextResult.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = ReviewTextResultValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = ReviewTextResultValidationError{}

// Validate checks the field values on ShumeiMixCallbackReq with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *ShumeiMixCallbackReq) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on ShumeiMixCallbackReq with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// ShumeiMixCallbackReqMultiError, or nil if none found.
func (m *ShumeiMixCallbackReq) ValidateAll() error {
	return m.validate(true)
}

func (m *ShumeiMixCallbackReq) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for BtId

	// no validation rules for Code

	// no validation rules for RequestId

	// no validation rules for RiskLevel

	// no validation rules for ResultType

	if all {
		switch v := interface{}(m.GetDetails()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, ShumeiMixCallbackReqValidationError{
					field:  "Details",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, ShumeiMixCallbackReqValidationError{
					field:  "Details",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetDetails()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return ShumeiMixCallbackReqValidationError{
				field:  "Details",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetPassThrough()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, ShumeiMixCallbackReqValidationError{
					field:  "PassThrough",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, ShumeiMixCallbackReqValidationError{
					field:  "PassThrough",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetPassThrough()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return ShumeiMixCallbackReqValidationError{
				field:  "PassThrough",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return ShumeiMixCallbackReqMultiError(errors)
	}

	return nil
}

// ShumeiMixCallbackReqMultiError is an error wrapping multiple validation
// errors returned by ShumeiMixCallbackReq.ValidateAll() if the designated
// constraints aren't met.
type ShumeiMixCallbackReqMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m ShumeiMixCallbackReqMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m ShumeiMixCallbackReqMultiError) AllErrors() []error { return m }

// ShumeiMixCallbackReqValidationError is the validation error returned by
// ShumeiMixCallbackReq.Validate if the designated constraints aren't met.
type ShumeiMixCallbackReqValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e ShumeiMixCallbackReqValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e ShumeiMixCallbackReqValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e ShumeiMixCallbackReqValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e ShumeiMixCallbackReqValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e ShumeiMixCallbackReqValidationError) ErrorName() string {
	return "ShumeiMixCallbackReqValidationError"
}

// Error satisfies the builtin error interface
func (e ShumeiMixCallbackReqValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sShumeiMixCallbackReq.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = ShumeiMixCallbackReqValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = ShumeiMixCallbackReqValidationError{}

// Validate checks the field values on ShumeiMixDetail with the rules defined
// in the proto definition for this message. If any rules are violated, the
// first error encountered is returned, or nil if there are no violations.
func (m *ShumeiMixDetail) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on ShumeiMixDetail with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// ShumeiMixDetailMultiError, or nil if none found.
func (m *ShumeiMixDetail) ValidateAll() error {
	return m.validate(true)
}

func (m *ShumeiMixDetail) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	for idx, item := range m.GetTexts() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, ShumeiMixDetailValidationError{
						field:  fmt.Sprintf("Texts[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, ShumeiMixDetailValidationError{
						field:  fmt.Sprintf("Texts[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return ShumeiMixDetailValidationError{
					field:  fmt.Sprintf("Texts[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	for idx, item := range m.GetImages() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, ShumeiMixDetailValidationError{
						field:  fmt.Sprintf("Images[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, ShumeiMixDetailValidationError{
						field:  fmt.Sprintf("Images[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return ShumeiMixDetailValidationError{
					field:  fmt.Sprintf("Images[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	if len(errors) > 0 {
		return ShumeiMixDetailMultiError(errors)
	}

	return nil
}

// ShumeiMixDetailMultiError is an error wrapping multiple validation errors
// returned by ShumeiMixDetail.ValidateAll() if the designated constraints
// aren't met.
type ShumeiMixDetailMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m ShumeiMixDetailMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m ShumeiMixDetailMultiError) AllErrors() []error { return m }

// ShumeiMixDetailValidationError is the validation error returned by
// ShumeiMixDetail.Validate if the designated constraints aren't met.
type ShumeiMixDetailValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e ShumeiMixDetailValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e ShumeiMixDetailValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e ShumeiMixDetailValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e ShumeiMixDetailValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e ShumeiMixDetailValidationError) ErrorName() string { return "ShumeiMixDetailValidationError" }

// Error satisfies the builtin error interface
func (e ShumeiMixDetailValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sShumeiMixDetail.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = ShumeiMixDetailValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = ShumeiMixDetailValidationError{}

// Validate checks the field values on ShumeiMixTextDetail with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *ShumeiMixTextDetail) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on ShumeiMixTextDetail with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// ShumeiMixTextDetailMultiError, or nil if none found.
func (m *ShumeiMixTextDetail) ValidateAll() error {
	return m.validate(true)
}

func (m *ShumeiMixTextDetail) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Code

	// no validation rules for Message

	// no validation rules for RequestId

	// no validation rules for DataId

	// no validation rules for BtId

	// no validation rules for RiskLevel

	if len(errors) > 0 {
		return ShumeiMixTextDetailMultiError(errors)
	}

	return nil
}

// ShumeiMixTextDetailMultiError is an error wrapping multiple validation
// errors returned by ShumeiMixTextDetail.ValidateAll() if the designated
// constraints aren't met.
type ShumeiMixTextDetailMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m ShumeiMixTextDetailMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m ShumeiMixTextDetailMultiError) AllErrors() []error { return m }

// ShumeiMixTextDetailValidationError is the validation error returned by
// ShumeiMixTextDetail.Validate if the designated constraints aren't met.
type ShumeiMixTextDetailValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e ShumeiMixTextDetailValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e ShumeiMixTextDetailValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e ShumeiMixTextDetailValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e ShumeiMixTextDetailValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e ShumeiMixTextDetailValidationError) ErrorName() string {
	return "ShumeiMixTextDetailValidationError"
}

// Error satisfies the builtin error interface
func (e ShumeiMixTextDetailValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sShumeiMixTextDetail.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = ShumeiMixTextDetailValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = ShumeiMixTextDetailValidationError{}

// Validate checks the field values on ShumeiMixImgDetail with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *ShumeiMixImgDetail) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on ShumeiMixImgDetail with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// ShumeiMixImgDetailMultiError, or nil if none found.
func (m *ShumeiMixImgDetail) ValidateAll() error {
	return m.validate(true)
}

func (m *ShumeiMixImgDetail) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Code

	// no validation rules for Message

	// no validation rules for RequestId

	// no validation rules for DataId

	// no validation rules for BtId

	// no validation rules for RiskLevel

	if len(errors) > 0 {
		return ShumeiMixImgDetailMultiError(errors)
	}

	return nil
}

// ShumeiMixImgDetailMultiError is an error wrapping multiple validation errors
// returned by ShumeiMixImgDetail.ValidateAll() if the designated constraints
// aren't met.
type ShumeiMixImgDetailMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m ShumeiMixImgDetailMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m ShumeiMixImgDetailMultiError) AllErrors() []error { return m }

// ShumeiMixImgDetailValidationError is the validation error returned by
// ShumeiMixImgDetail.Validate if the designated constraints aren't met.
type ShumeiMixImgDetailValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e ShumeiMixImgDetailValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e ShumeiMixImgDetailValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e ShumeiMixImgDetailValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e ShumeiMixImgDetailValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e ShumeiMixImgDetailValidationError) ErrorName() string {
	return "ShumeiMixImgDetailValidationError"
}

// Error satisfies the builtin error interface
func (e ShumeiMixImgDetailValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sShumeiMixImgDetail.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = ShumeiMixImgDetailValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = ShumeiMixImgDetailValidationError{}

// Validate checks the field values on ReviewCallbackData with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *ReviewCallbackData) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on ReviewCallbackData with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// ReviewCallbackDataMultiError, or nil if none found.
func (m *ReviewCallbackData) ValidateAll() error {
	return m.validate(true)
}

func (m *ReviewCallbackData) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Id

	// no validation rules for BizType

	if len(errors) > 0 {
		return ReviewCallbackDataMultiError(errors)
	}

	return nil
}

// ReviewCallbackDataMultiError is an error wrapping multiple validation errors
// returned by ReviewCallbackData.ValidateAll() if the designated constraints
// aren't met.
type ReviewCallbackDataMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m ReviewCallbackDataMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m ReviewCallbackDataMultiError) AllErrors() []error { return m }

// ReviewCallbackDataValidationError is the validation error returned by
// ReviewCallbackData.Validate if the designated constraints aren't met.
type ReviewCallbackDataValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e ReviewCallbackDataValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e ReviewCallbackDataValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e ReviewCallbackDataValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e ReviewCallbackDataValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e ReviewCallbackDataValidationError) ErrorName() string {
	return "ReviewCallbackDataValidationError"
}

// Error satisfies the builtin error interface
func (e ReviewCallbackDataValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sReviewCallbackData.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = ReviewCallbackDataValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = ReviewCallbackDataValidationError{}

// Validate checks the field values on ShumeiMultiImageCallbackReq with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *ShumeiMultiImageCallbackReq) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on ShumeiMultiImageCallbackReq with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// ShumeiMultiImageCallbackReqMultiError, or nil if none found.
func (m *ShumeiMultiImageCallbackReq) ValidateAll() error {
	return m.validate(true)
}

func (m *ShumeiMultiImageCallbackReq) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for BtId

	// no validation rules for Code

	// no validation rules for RequestId

	for idx, item := range m.GetImgs() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, ShumeiMultiImageCallbackReqValidationError{
						field:  fmt.Sprintf("Imgs[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, ShumeiMultiImageCallbackReqValidationError{
						field:  fmt.Sprintf("Imgs[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return ShumeiMultiImageCallbackReqValidationError{
					field:  fmt.Sprintf("Imgs[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	if all {
		switch v := interface{}(m.GetAuxInfo()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, ShumeiMultiImageCallbackReqValidationError{
					field:  "AuxInfo",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, ShumeiMultiImageCallbackReqValidationError{
					field:  "AuxInfo",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetAuxInfo()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return ShumeiMultiImageCallbackReqValidationError{
				field:  "AuxInfo",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return ShumeiMultiImageCallbackReqMultiError(errors)
	}

	return nil
}

// ShumeiMultiImageCallbackReqMultiError is an error wrapping multiple
// validation errors returned by ShumeiMultiImageCallbackReq.ValidateAll() if
// the designated constraints aren't met.
type ShumeiMultiImageCallbackReqMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m ShumeiMultiImageCallbackReqMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m ShumeiMultiImageCallbackReqMultiError) AllErrors() []error { return m }

// ShumeiMultiImageCallbackReqValidationError is the validation error returned
// by ShumeiMultiImageCallbackReq.Validate if the designated constraints
// aren't met.
type ShumeiMultiImageCallbackReqValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e ShumeiMultiImageCallbackReqValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e ShumeiMultiImageCallbackReqValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e ShumeiMultiImageCallbackReqValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e ShumeiMultiImageCallbackReqValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e ShumeiMultiImageCallbackReqValidationError) ErrorName() string {
	return "ShumeiMultiImageCallbackReqValidationError"
}

// Error satisfies the builtin error interface
func (e ShumeiMultiImageCallbackReqValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sShumeiMultiImageCallbackReq.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = ShumeiMultiImageCallbackReqValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = ShumeiMultiImageCallbackReqValidationError{}

// Validate checks the field values on ShumeiMultiImage with the rules defined
// in the proto definition for this message. If any rules are violated, the
// first error encountered is returned, or nil if there are no violations.
func (m *ShumeiMultiImage) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on ShumeiMultiImage with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// ShumeiMultiImageMultiError, or nil if none found.
func (m *ShumeiMultiImage) ValidateAll() error {
	return m.validate(true)
}

func (m *ShumeiMultiImage) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for BtId

	// no validation rules for RiskLevel

	// no validation rules for Code

	if len(errors) > 0 {
		return ShumeiMultiImageMultiError(errors)
	}

	return nil
}

// ShumeiMultiImageMultiError is an error wrapping multiple validation errors
// returned by ShumeiMultiImage.ValidateAll() if the designated constraints
// aren't met.
type ShumeiMultiImageMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m ShumeiMultiImageMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m ShumeiMultiImageMultiError) AllErrors() []error { return m }

// ShumeiMultiImageValidationError is the validation error returned by
// ShumeiMultiImage.Validate if the designated constraints aren't met.
type ShumeiMultiImageValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e ShumeiMultiImageValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e ShumeiMultiImageValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e ShumeiMultiImageValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e ShumeiMultiImageValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e ShumeiMultiImageValidationError) ErrorName() string { return "ShumeiMultiImageValidationError" }

// Error satisfies the builtin error interface
func (e ShumeiMultiImageValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sShumeiMultiImage.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = ShumeiMultiImageValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = ShumeiMultiImageValidationError{}

// Validate checks the field values on ShumeiMultiImageExtra with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *ShumeiMultiImageExtra) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on ShumeiMultiImageExtra with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// ShumeiMultiImageExtraMultiError, or nil if none found.
func (m *ShumeiMultiImageExtra) ValidateAll() error {
	return m.validate(true)
}

func (m *ShumeiMultiImageExtra) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetPassThrough()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, ShumeiMultiImageExtraValidationError{
					field:  "PassThrough",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, ShumeiMultiImageExtraValidationError{
					field:  "PassThrough",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetPassThrough()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return ShumeiMultiImageExtraValidationError{
				field:  "PassThrough",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return ShumeiMultiImageExtraMultiError(errors)
	}

	return nil
}

// ShumeiMultiImageExtraMultiError is an error wrapping multiple validation
// errors returned by ShumeiMultiImageExtra.ValidateAll() if the designated
// constraints aren't met.
type ShumeiMultiImageExtraMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m ShumeiMultiImageExtraMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m ShumeiMultiImageExtraMultiError) AllErrors() []error { return m }

// ShumeiMultiImageExtraValidationError is the validation error returned by
// ShumeiMultiImageExtra.Validate if the designated constraints aren't met.
type ShumeiMultiImageExtraValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e ShumeiMultiImageExtraValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e ShumeiMultiImageExtraValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e ShumeiMultiImageExtraValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e ShumeiMultiImageExtraValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e ShumeiMultiImageExtraValidationError) ErrorName() string {
	return "ShumeiMultiImageExtraValidationError"
}

// Error satisfies the builtin error interface
func (e ShumeiMultiImageExtraValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sShumeiMultiImageExtra.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = ShumeiMultiImageExtraValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = ShumeiMultiImageExtraValidationError{}

// Validate checks the field values on ReviewMultiImageCallbackData with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *ReviewMultiImageCallbackData) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on ReviewMultiImageCallbackData with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// ReviewMultiImageCallbackDataMultiError, or nil if none found.
func (m *ReviewMultiImageCallbackData) ValidateAll() error {
	return m.validate(true)
}

func (m *ReviewMultiImageCallbackData) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Id

	// no validation rules for BizType

	// no validation rules for Userid

	// no validation rules for Sex

	if len(errors) > 0 {
		return ReviewMultiImageCallbackDataMultiError(errors)
	}

	return nil
}

// ReviewMultiImageCallbackDataMultiError is an error wrapping multiple
// validation errors returned by ReviewMultiImageCallbackData.ValidateAll() if
// the designated constraints aren't met.
type ReviewMultiImageCallbackDataMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m ReviewMultiImageCallbackDataMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m ReviewMultiImageCallbackDataMultiError) AllErrors() []error { return m }

// ReviewMultiImageCallbackDataValidationError is the validation error returned
// by ReviewMultiImageCallbackData.Validate if the designated constraints
// aren't met.
type ReviewMultiImageCallbackDataValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e ReviewMultiImageCallbackDataValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e ReviewMultiImageCallbackDataValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e ReviewMultiImageCallbackDataValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e ReviewMultiImageCallbackDataValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e ReviewMultiImageCallbackDataValidationError) ErrorName() string {
	return "ReviewMultiImageCallbackDataValidationError"
}

// Error satisfies the builtin error interface
func (e ReviewMultiImageCallbackDataValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sReviewMultiImageCallbackData.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = ReviewMultiImageCallbackDataValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = ReviewMultiImageCallbackDataValidationError{}

// Validate checks the field values on ReviewMemberInfoCallbackData with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *ReviewMemberInfoCallbackData) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on ReviewMemberInfoCallbackData with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// ReviewMemberInfoCallbackDataMultiError, or nil if none found.
func (m *ReviewMemberInfoCallbackData) ValidateAll() error {
	return m.validate(true)
}

func (m *ReviewMemberInfoCallbackData) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Userid

	// no validation rules for Id

	// no validation rules for Type

	if len(errors) > 0 {
		return ReviewMemberInfoCallbackDataMultiError(errors)
	}

	return nil
}

// ReviewMemberInfoCallbackDataMultiError is an error wrapping multiple
// validation errors returned by ReviewMemberInfoCallbackData.ValidateAll() if
// the designated constraints aren't met.
type ReviewMemberInfoCallbackDataMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m ReviewMemberInfoCallbackDataMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m ReviewMemberInfoCallbackDataMultiError) AllErrors() []error { return m }

// ReviewMemberInfoCallbackDataValidationError is the validation error returned
// by ReviewMemberInfoCallbackData.Validate if the designated constraints
// aren't met.
type ReviewMemberInfoCallbackDataValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e ReviewMemberInfoCallbackDataValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e ReviewMemberInfoCallbackDataValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e ReviewMemberInfoCallbackDataValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e ReviewMemberInfoCallbackDataValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e ReviewMemberInfoCallbackDataValidationError) ErrorName() string {
	return "ReviewMemberInfoCallbackDataValidationError"
}

// Error satisfies the builtin error interface
func (e ReviewMemberInfoCallbackDataValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sReviewMemberInfoCallbackData.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = ReviewMemberInfoCallbackDataValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = ReviewMemberInfoCallbackDataValidationError{}

// Validate checks the field values on ReviewMemberInfoReq with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *ReviewMemberInfoReq) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on ReviewMemberInfoReq with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// ReviewMemberInfoReqMultiError, or nil if none found.
func (m *ReviewMemberInfoReq) ValidateAll() error {
	return m.validate(true)
}

func (m *ReviewMemberInfoReq) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Userid

	// no validation rules for Sex

	// no validation rules for BizType

	for idx, item := range m.GetImages() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, ReviewMemberInfoReqValidationError{
						field:  fmt.Sprintf("Images[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, ReviewMemberInfoReqValidationError{
						field:  fmt.Sprintf("Images[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return ReviewMemberInfoReqValidationError{
					field:  fmt.Sprintf("Images[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	if all {
		switch v := interface{}(m.GetVoice()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, ReviewMemberInfoReqValidationError{
					field:  "Voice",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, ReviewMemberInfoReqValidationError{
					field:  "Voice",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetVoice()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return ReviewMemberInfoReqValidationError{
				field:  "Voice",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetCallbackData()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, ReviewMemberInfoReqValidationError{
					field:  "CallbackData",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, ReviewMemberInfoReqValidationError{
					field:  "CallbackData",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetCallbackData()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return ReviewMemberInfoReqValidationError{
				field:  "CallbackData",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for Xluid

	// no validation rules for Country

	// no validation rules for Lang

	if len(errors) > 0 {
		return ReviewMemberInfoReqMultiError(errors)
	}

	return nil
}

// ReviewMemberInfoReqMultiError is an error wrapping multiple validation
// errors returned by ReviewMemberInfoReq.ValidateAll() if the designated
// constraints aren't met.
type ReviewMemberInfoReqMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m ReviewMemberInfoReqMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m ReviewMemberInfoReqMultiError) AllErrors() []error { return m }

// ReviewMemberInfoReqValidationError is the validation error returned by
// ReviewMemberInfoReq.Validate if the designated constraints aren't met.
type ReviewMemberInfoReqValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e ReviewMemberInfoReqValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e ReviewMemberInfoReqValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e ReviewMemberInfoReqValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e ReviewMemberInfoReqValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e ReviewMemberInfoReqValidationError) ErrorName() string {
	return "ReviewMemberInfoReqValidationError"
}

// Error satisfies the builtin error interface
func (e ReviewMemberInfoReqValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sReviewMemberInfoReq.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = ReviewMemberInfoReqValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = ReviewMemberInfoReqValidationError{}

// Validate checks the field values on ReviewMomentReq with the rules defined
// in the proto definition for this message. If any rules are violated, the
// first error encountered is returned, or nil if there are no violations.
func (m *ReviewMomentReq) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on ReviewMomentReq with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// ReviewMomentReqMultiError, or nil if none found.
func (m *ReviewMomentReq) ValidateAll() error {
	return m.validate(true)
}

func (m *ReviewMomentReq) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Userid

	// no validation rules for Moid

	// no validation rules for Text

	if all {
		switch v := interface{}(m.GetVoice()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, ReviewMomentReqValidationError{
					field:  "Voice",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, ReviewMomentReqValidationError{
					field:  "Voice",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetVoice()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return ReviewMomentReqValidationError{
				field:  "Voice",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetVideo()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, ReviewMomentReqValidationError{
					field:  "Video",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, ReviewMomentReqValidationError{
					field:  "Video",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetVideo()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return ReviewMomentReqValidationError{
				field:  "Video",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetCallbackData()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, ReviewMomentReqValidationError{
					field:  "CallbackData",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, ReviewMomentReqValidationError{
					field:  "CallbackData",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetCallbackData()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return ReviewMomentReqValidationError{
				field:  "CallbackData",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for Country

	// no validation rules for Lang

	if len(errors) > 0 {
		return ReviewMomentReqMultiError(errors)
	}

	return nil
}

// ReviewMomentReqMultiError is an error wrapping multiple validation errors
// returned by ReviewMomentReq.ValidateAll() if the designated constraints
// aren't met.
type ReviewMomentReqMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m ReviewMomentReqMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m ReviewMomentReqMultiError) AllErrors() []error { return m }

// ReviewMomentReqValidationError is the validation error returned by
// ReviewMomentReq.Validate if the designated constraints aren't met.
type ReviewMomentReqValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e ReviewMomentReqValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e ReviewMomentReqValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e ReviewMomentReqValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e ReviewMomentReqValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e ReviewMomentReqValidationError) ErrorName() string { return "ReviewMomentReqValidationError" }

// Error satisfies the builtin error interface
func (e ReviewMomentReqValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sReviewMomentReq.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = ReviewMomentReqValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = ReviewMomentReqValidationError{}

// Validate checks the field values on ReviewUserCommonMsgReq with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *ReviewUserCommonMsgReq) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on ReviewUserCommonMsgReq with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// ReviewUserCommonMsgReqMultiError, or nil if none found.
func (m *ReviewUserCommonMsgReq) ValidateAll() error {
	return m.validate(true)
}

func (m *ReviewUserCommonMsgReq) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Userid

	// no validation rules for Id

	// no validation rules for Text

	if all {
		switch v := interface{}(m.GetCallbackData()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, ReviewUserCommonMsgReqValidationError{
					field:  "CallbackData",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, ReviewUserCommonMsgReqValidationError{
					field:  "CallbackData",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetCallbackData()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return ReviewUserCommonMsgReqValidationError{
				field:  "CallbackData",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return ReviewUserCommonMsgReqMultiError(errors)
	}

	return nil
}

// ReviewUserCommonMsgReqMultiError is an error wrapping multiple validation
// errors returned by ReviewUserCommonMsgReq.ValidateAll() if the designated
// constraints aren't met.
type ReviewUserCommonMsgReqMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m ReviewUserCommonMsgReqMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m ReviewUserCommonMsgReqMultiError) AllErrors() []error { return m }

// ReviewUserCommonMsgReqValidationError is the validation error returned by
// ReviewUserCommonMsgReq.Validate if the designated constraints aren't met.
type ReviewUserCommonMsgReqValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e ReviewUserCommonMsgReqValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e ReviewUserCommonMsgReqValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e ReviewUserCommonMsgReqValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e ReviewUserCommonMsgReqValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e ReviewUserCommonMsgReqValidationError) ErrorName() string {
	return "ReviewUserCommonMsgReqValidationError"
}

// Error satisfies the builtin error interface
func (e ReviewUserCommonMsgReqValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sReviewUserCommonMsgReq.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = ReviewUserCommonMsgReqValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = ReviewUserCommonMsgReqValidationError{}

// Validate checks the field values on ReviewUserCommonMsgCallbackData with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *ReviewUserCommonMsgCallbackData) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on ReviewUserCommonMsgCallbackData with
// the rules defined in the proto definition for this message. If any rules
// are violated, the result is a list of violation errors wrapped in
// ReviewUserCommonMsgCallbackDataMultiError, or nil if none found.
func (m *ReviewUserCommonMsgCallbackData) ValidateAll() error {
	return m.validate(true)
}

func (m *ReviewUserCommonMsgCallbackData) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Userid

	// no validation rules for Id

	if len(errors) > 0 {
		return ReviewUserCommonMsgCallbackDataMultiError(errors)
	}

	return nil
}

// ReviewUserCommonMsgCallbackDataMultiError is an error wrapping multiple
// validation errors returned by ReviewUserCommonMsgCallbackData.ValidateAll()
// if the designated constraints aren't met.
type ReviewUserCommonMsgCallbackDataMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m ReviewUserCommonMsgCallbackDataMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m ReviewUserCommonMsgCallbackDataMultiError) AllErrors() []error { return m }

// ReviewUserCommonMsgCallbackDataValidationError is the validation error
// returned by ReviewUserCommonMsgCallbackData.Validate if the designated
// constraints aren't met.
type ReviewUserCommonMsgCallbackDataValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e ReviewUserCommonMsgCallbackDataValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e ReviewUserCommonMsgCallbackDataValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e ReviewUserCommonMsgCallbackDataValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e ReviewUserCommonMsgCallbackDataValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e ReviewUserCommonMsgCallbackDataValidationError) ErrorName() string {
	return "ReviewUserCommonMsgCallbackDataValidationError"
}

// Error satisfies the builtin error interface
func (e ReviewUserCommonMsgCallbackDataValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sReviewUserCommonMsgCallbackData.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = ReviewUserCommonMsgCallbackDataValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = ReviewUserCommonMsgCallbackDataValidationError{}

// Validate checks the field values on ReviewCallbackReq with the rules defined
// in the proto definition for this message. If any rules are violated, the
// first error encountered is returned, or nil if there are no violations.
func (m *ReviewCallbackReq) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on ReviewCallbackReq with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// ReviewCallbackReqMultiError, or nil if none found.
func (m *ReviewCallbackReq) ValidateAll() error {
	return m.validate(true)
}

func (m *ReviewCallbackReq) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for AppId

	// no validation rules for BizType

	// no validation rules for Auditor

	// no validation rules for CaseId

	// no validation rules for ContentType

	// no validation rules for DataId

	// no validation rules for Suggestion

	// no validation rules for Label

	// no validation rules for LabelKey

	// no validation rules for LabelCode

	if all {
		switch v := interface{}(m.GetOrigin()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, ReviewCallbackReqValidationError{
					field:  "Origin",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, ReviewCallbackReqValidationError{
					field:  "Origin",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetOrigin()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return ReviewCallbackReqValidationError{
				field:  "Origin",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for Secret

	if len(errors) > 0 {
		return ReviewCallbackReqMultiError(errors)
	}

	return nil
}

// ReviewCallbackReqMultiError is an error wrapping multiple validation errors
// returned by ReviewCallbackReq.ValidateAll() if the designated constraints
// aren't met.
type ReviewCallbackReqMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m ReviewCallbackReqMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m ReviewCallbackReqMultiError) AllErrors() []error { return m }

// ReviewCallbackReqValidationError is the validation error returned by
// ReviewCallbackReq.Validate if the designated constraints aren't met.
type ReviewCallbackReqValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e ReviewCallbackReqValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e ReviewCallbackReqValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e ReviewCallbackReqValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e ReviewCallbackReqValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e ReviewCallbackReqValidationError) ErrorName() string {
	return "ReviewCallbackReqValidationError"
}

// Error satisfies the builtin error interface
func (e ReviewCallbackReqValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sReviewCallbackReq.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = ReviewCallbackReqValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = ReviewCallbackReqValidationError{}

// Validate checks the field values on ReviewRoomInfoCallbackData with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *ReviewRoomInfoCallbackData) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on ReviewRoomInfoCallbackData with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// ReviewRoomInfoCallbackDataMultiError, or nil if none found.
func (m *ReviewRoomInfoCallbackData) ValidateAll() error {
	return m.validate(true)
}

func (m *ReviewRoomInfoCallbackData) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Roomid

	// no validation rules for Id

	// no validation rules for Type

	if len(errors) > 0 {
		return ReviewRoomInfoCallbackDataMultiError(errors)
	}

	return nil
}

// ReviewRoomInfoCallbackDataMultiError is an error wrapping multiple
// validation errors returned by ReviewRoomInfoCallbackData.ValidateAll() if
// the designated constraints aren't met.
type ReviewRoomInfoCallbackDataMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m ReviewRoomInfoCallbackDataMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m ReviewRoomInfoCallbackDataMultiError) AllErrors() []error { return m }

// ReviewRoomInfoCallbackDataValidationError is the validation error returned
// by ReviewRoomInfoCallbackData.Validate if the designated constraints aren't met.
type ReviewRoomInfoCallbackDataValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e ReviewRoomInfoCallbackDataValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e ReviewRoomInfoCallbackDataValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e ReviewRoomInfoCallbackDataValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e ReviewRoomInfoCallbackDataValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e ReviewRoomInfoCallbackDataValidationError) ErrorName() string {
	return "ReviewRoomInfoCallbackDataValidationError"
}

// Error satisfies the builtin error interface
func (e ReviewRoomInfoCallbackDataValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sReviewRoomInfoCallbackData.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = ReviewRoomInfoCallbackDataValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = ReviewRoomInfoCallbackDataValidationError{}

// Validate checks the field values on ReviewRoomInfoReq with the rules defined
// in the proto definition for this message. If any rules are violated, the
// first error encountered is returned, or nil if there are no violations.
func (m *ReviewRoomInfoReq) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on ReviewRoomInfoReq with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// ReviewRoomInfoReqMultiError, or nil if none found.
func (m *ReviewRoomInfoReq) ValidateAll() error {
	return m.validate(true)
}

func (m *ReviewRoomInfoReq) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Roomid

	// no validation rules for Userid

	// no validation rules for BizType

	// no validation rules for Text

	if all {
		switch v := interface{}(m.GetImage()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, ReviewRoomInfoReqValidationError{
					field:  "Image",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, ReviewRoomInfoReqValidationError{
					field:  "Image",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetImage()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return ReviewRoomInfoReqValidationError{
				field:  "Image",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetCallbackData()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, ReviewRoomInfoReqValidationError{
					field:  "CallbackData",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, ReviewRoomInfoReqValidationError{
					field:  "CallbackData",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetCallbackData()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return ReviewRoomInfoReqValidationError{
				field:  "CallbackData",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for Country

	// no validation rules for Lang

	if len(errors) > 0 {
		return ReviewRoomInfoReqMultiError(errors)
	}

	return nil
}

// ReviewRoomInfoReqMultiError is an error wrapping multiple validation errors
// returned by ReviewRoomInfoReq.ValidateAll() if the designated constraints
// aren't met.
type ReviewRoomInfoReqMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m ReviewRoomInfoReqMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m ReviewRoomInfoReqMultiError) AllErrors() []error { return m }

// ReviewRoomInfoReqValidationError is the validation error returned by
// ReviewRoomInfoReq.Validate if the designated constraints aren't met.
type ReviewRoomInfoReqValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e ReviewRoomInfoReqValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e ReviewRoomInfoReqValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e ReviewRoomInfoReqValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e ReviewRoomInfoReqValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e ReviewRoomInfoReqValidationError) ErrorName() string {
	return "ReviewRoomInfoReqValidationError"
}

// Error satisfies the builtin error interface
func (e ReviewRoomInfoReqValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sReviewRoomInfoReq.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = ReviewRoomInfoReqValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = ReviewRoomInfoReqValidationError{}

// Validate checks the field values on ReviewRoomInfoResp with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *ReviewRoomInfoResp) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on ReviewRoomInfoResp with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// ReviewRoomInfoRespMultiError, or nil if none found.
func (m *ReviewRoomInfoResp) ValidateAll() error {
	return m.validate(true)
}

func (m *ReviewRoomInfoResp) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetBase()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, ReviewRoomInfoRespValidationError{
					field:  "Base",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, ReviewRoomInfoRespValidationError{
					field:  "Base",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetBase()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return ReviewRoomInfoRespValidationError{
				field:  "Base",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for Result

	// no validation rules for Reason

	if len(errors) > 0 {
		return ReviewRoomInfoRespMultiError(errors)
	}

	return nil
}

// ReviewRoomInfoRespMultiError is an error wrapping multiple validation errors
// returned by ReviewRoomInfoResp.ValidateAll() if the designated constraints
// aren't met.
type ReviewRoomInfoRespMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m ReviewRoomInfoRespMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m ReviewRoomInfoRespMultiError) AllErrors() []error { return m }

// ReviewRoomInfoRespValidationError is the validation error returned by
// ReviewRoomInfoResp.Validate if the designated constraints aren't met.
type ReviewRoomInfoRespValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e ReviewRoomInfoRespValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e ReviewRoomInfoRespValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e ReviewRoomInfoRespValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e ReviewRoomInfoRespValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e ReviewRoomInfoRespValidationError) ErrorName() string {
	return "ReviewRoomInfoRespValidationError"
}

// Error satisfies the builtin error interface
func (e ReviewRoomInfoRespValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sReviewRoomInfoResp.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = ReviewRoomInfoRespValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = ReviewRoomInfoRespValidationError{}

// Validate checks the field values on ReviewRoomChatReq with the rules defined
// in the proto definition for this message. If any rules are violated, the
// first error encountered is returned, or nil if there are no violations.
func (m *ReviewRoomChatReq) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on ReviewRoomChatReq with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// ReviewRoomChatReqMultiError, or nil if none found.
func (m *ReviewRoomChatReq) ValidateAll() error {
	return m.validate(true)
}

func (m *ReviewRoomChatReq) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Userid

	// no validation rules for Roomid

	// no validation rules for Sid

	// no validation rules for SubType

	// no validation rules for Content

	// no validation rules for Ip

	// no validation rules for Country

	// no validation rules for Lang

	if all {
		switch v := interface{}(m.GetBase()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, ReviewRoomChatReqValidationError{
					field:  "Base",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, ReviewRoomChatReqValidationError{
					field:  "Base",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetBase()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return ReviewRoomChatReqValidationError{
				field:  "Base",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return ReviewRoomChatReqMultiError(errors)
	}

	return nil
}

// ReviewRoomChatReqMultiError is an error wrapping multiple validation errors
// returned by ReviewRoomChatReq.ValidateAll() if the designated constraints
// aren't met.
type ReviewRoomChatReqMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m ReviewRoomChatReqMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m ReviewRoomChatReqMultiError) AllErrors() []error { return m }

// ReviewRoomChatReqValidationError is the validation error returned by
// ReviewRoomChatReq.Validate if the designated constraints aren't met.
type ReviewRoomChatReqValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e ReviewRoomChatReqValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e ReviewRoomChatReqValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e ReviewRoomChatReqValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e ReviewRoomChatReqValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e ReviewRoomChatReqValidationError) ErrorName() string {
	return "ReviewRoomChatReqValidationError"
}

// Error satisfies the builtin error interface
func (e ReviewRoomChatReqValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sReviewRoomChatReq.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = ReviewRoomChatReqValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = ReviewRoomChatReqValidationError{}

// Validate checks the field values on ReviewRoomChatResp with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *ReviewRoomChatResp) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on ReviewRoomChatResp with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// ReviewRoomChatRespMultiError, or nil if none found.
func (m *ReviewRoomChatResp) ValidateAll() error {
	return m.validate(true)
}

func (m *ReviewRoomChatResp) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetBase()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, ReviewRoomChatRespValidationError{
					field:  "Base",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, ReviewRoomChatRespValidationError{
					field:  "Base",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetBase()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return ReviewRoomChatRespValidationError{
				field:  "Base",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for Result

	// no validation rules for Reason

	if len(errors) > 0 {
		return ReviewRoomChatRespMultiError(errors)
	}

	return nil
}

// ReviewRoomChatRespMultiError is an error wrapping multiple validation errors
// returned by ReviewRoomChatResp.ValidateAll() if the designated constraints
// aren't met.
type ReviewRoomChatRespMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m ReviewRoomChatRespMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m ReviewRoomChatRespMultiError) AllErrors() []error { return m }

// ReviewRoomChatRespValidationError is the validation error returned by
// ReviewRoomChatResp.Validate if the designated constraints aren't met.
type ReviewRoomChatRespValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e ReviewRoomChatRespValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e ReviewRoomChatRespValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e ReviewRoomChatRespValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e ReviewRoomChatRespValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e ReviewRoomChatRespValidationError) ErrorName() string {
	return "ReviewRoomChatRespValidationError"
}

// Error satisfies the builtin error interface
func (e ReviewRoomChatRespValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sReviewRoomChatResp.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = ReviewRoomChatRespValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = ReviewRoomChatRespValidationError{}

// Validate checks the field values on ReviewChatCallbackData with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *ReviewChatCallbackData) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on ReviewChatCallbackData with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// ReviewChatCallbackDataMultiError, or nil if none found.
func (m *ReviewChatCallbackData) ValidateAll() error {
	return m.validate(true)
}

func (m *ReviewChatCallbackData) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Sid

	// no validation rules for Msgid

	if len(errors) > 0 {
		return ReviewChatCallbackDataMultiError(errors)
	}

	return nil
}

// ReviewChatCallbackDataMultiError is an error wrapping multiple validation
// errors returned by ReviewChatCallbackData.ValidateAll() if the designated
// constraints aren't met.
type ReviewChatCallbackDataMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m ReviewChatCallbackDataMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m ReviewChatCallbackDataMultiError) AllErrors() []error { return m }

// ReviewChatCallbackDataValidationError is the validation error returned by
// ReviewChatCallbackData.Validate if the designated constraints aren't met.
type ReviewChatCallbackDataValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e ReviewChatCallbackDataValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e ReviewChatCallbackDataValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e ReviewChatCallbackDataValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e ReviewChatCallbackDataValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e ReviewChatCallbackDataValidationError) ErrorName() string {
	return "ReviewChatCallbackDataValidationError"
}

// Error satisfies the builtin error interface
func (e ReviewChatCallbackDataValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sReviewChatCallbackData.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = ReviewChatCallbackDataValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = ReviewChatCallbackDataValidationError{}

// Validate checks the field values on ReviewChatReq with the rules defined in
// the proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *ReviewChatReq) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on ReviewChatReq with the rules defined
// in the proto definition for this message. If any rules are violated, the
// result is a list of violation errors wrapped in ReviewChatReqMultiError, or
// nil if none found.
func (m *ReviewChatReq) ValidateAll() error {
	return m.validate(true)
}

func (m *ReviewChatReq) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Userid

	// no validation rules for Sid

	// no validation rules for MsgId

	// no validation rules for Text

	if all {
		switch v := interface{}(m.GetImage()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, ReviewChatReqValidationError{
					field:  "Image",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, ReviewChatReqValidationError{
					field:  "Image",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetImage()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return ReviewChatReqValidationError{
				field:  "Image",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetVoice()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, ReviewChatReqValidationError{
					field:  "Voice",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, ReviewChatReqValidationError{
					field:  "Voice",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetVoice()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return ReviewChatReqValidationError{
				field:  "Voice",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetVideo()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, ReviewChatReqValidationError{
					field:  "Video",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, ReviewChatReqValidationError{
					field:  "Video",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetVideo()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return ReviewChatReqValidationError{
				field:  "Video",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetCallbackData()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, ReviewChatReqValidationError{
					field:  "CallbackData",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, ReviewChatReqValidationError{
					field:  "CallbackData",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetCallbackData()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return ReviewChatReqValidationError{
				field:  "CallbackData",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for Sex

	// no validation rules for SweetRate

	// no validation rules for ETag

	// no validation rules for PeerId

	// no validation rules for Ip

	// no validation rules for Country

	// no validation rules for Lang

	// no validation rules for Count

	if all {
		switch v := interface{}(m.GetBase()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, ReviewChatReqValidationError{
					field:  "Base",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, ReviewChatReqValidationError{
					field:  "Base",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetBase()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return ReviewChatReqValidationError{
				field:  "Base",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return ReviewChatReqMultiError(errors)
	}

	return nil
}

// ReviewChatReqMultiError is an error wrapping multiple validation errors
// returned by ReviewChatReq.ValidateAll() if the designated constraints
// aren't met.
type ReviewChatReqMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m ReviewChatReqMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m ReviewChatReqMultiError) AllErrors() []error { return m }

// ReviewChatReqValidationError is the validation error returned by
// ReviewChatReq.Validate if the designated constraints aren't met.
type ReviewChatReqValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e ReviewChatReqValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e ReviewChatReqValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e ReviewChatReqValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e ReviewChatReqValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e ReviewChatReqValidationError) ErrorName() string { return "ReviewChatReqValidationError" }

// Error satisfies the builtin error interface
func (e ReviewChatReqValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sReviewChatReq.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = ReviewChatReqValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = ReviewChatReqValidationError{}

// Validate checks the field values on ReviewChatResp with the rules defined in
// the proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *ReviewChatResp) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on ReviewChatResp with the rules defined
// in the proto definition for this message. If any rules are violated, the
// result is a list of violation errors wrapped in ReviewChatRespMultiError,
// or nil if none found.
func (m *ReviewChatResp) ValidateAll() error {
	return m.validate(true)
}

func (m *ReviewChatResp) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetBase()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, ReviewChatRespValidationError{
					field:  "Base",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, ReviewChatRespValidationError{
					field:  "Base",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetBase()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return ReviewChatRespValidationError{
				field:  "Base",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for Sync

	// no validation rules for Result

	// no validation rules for Reason

	// no validation rules for SubResult

	if len(errors) > 0 {
		return ReviewChatRespMultiError(errors)
	}

	return nil
}

// ReviewChatRespMultiError is an error wrapping multiple validation errors
// returned by ReviewChatResp.ValidateAll() if the designated constraints
// aren't met.
type ReviewChatRespMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m ReviewChatRespMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m ReviewChatRespMultiError) AllErrors() []error { return m }

// ReviewChatRespValidationError is the validation error returned by
// ReviewChatResp.Validate if the designated constraints aren't met.
type ReviewChatRespValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e ReviewChatRespValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e ReviewChatRespValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e ReviewChatRespValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e ReviewChatRespValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e ReviewChatRespValidationError) ErrorName() string { return "ReviewChatRespValidationError" }

// Error satisfies the builtin error interface
func (e ReviewChatRespValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sReviewChatResp.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = ReviewChatRespValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = ReviewChatRespValidationError{}

// Validate checks the field values on ReviewScriptReq with the rules defined
// in the proto definition for this message. If any rules are violated, the
// first error encountered is returned, or nil if there are no violations.
func (m *ReviewScriptReq) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on ReviewScriptReq with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// ReviewScriptReqMultiError, or nil if none found.
func (m *ReviewScriptReq) ValidateAll() error {
	return m.validate(true)
}

func (m *ReviewScriptReq) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for UserId

	// no validation rules for Id

	// no validation rules for BizType

	if all {
		switch v := interface{}(m.GetVoice()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, ReviewScriptReqValidationError{
					field:  "Voice",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, ReviewScriptReqValidationError{
					field:  "Voice",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetVoice()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return ReviewScriptReqValidationError{
				field:  "Voice",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetCallbackData()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, ReviewScriptReqValidationError{
					field:  "CallbackData",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, ReviewScriptReqValidationError{
					field:  "CallbackData",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetCallbackData()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return ReviewScriptReqValidationError{
				field:  "CallbackData",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return ReviewScriptReqMultiError(errors)
	}

	return nil
}

// ReviewScriptReqMultiError is an error wrapping multiple validation errors
// returned by ReviewScriptReq.ValidateAll() if the designated constraints
// aren't met.
type ReviewScriptReqMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m ReviewScriptReqMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m ReviewScriptReqMultiError) AllErrors() []error { return m }

// ReviewScriptReqValidationError is the validation error returned by
// ReviewScriptReq.Validate if the designated constraints aren't met.
type ReviewScriptReqValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e ReviewScriptReqValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e ReviewScriptReqValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e ReviewScriptReqValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e ReviewScriptReqValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e ReviewScriptReqValidationError) ErrorName() string { return "ReviewScriptReqValidationError" }

// Error satisfies the builtin error interface
func (e ReviewScriptReqValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sReviewScriptReq.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = ReviewScriptReqValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = ReviewScriptReqValidationError{}

// Validate checks the field values on ReviewScriptCallbackData with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *ReviewScriptCallbackData) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on ReviewScriptCallbackData with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// ReviewScriptCallbackDataMultiError, or nil if none found.
func (m *ReviewScriptCallbackData) ValidateAll() error {
	return m.validate(true)
}

func (m *ReviewScriptCallbackData) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Userid

	// no validation rules for Id

	// no validation rules for Type

	// no validation rules for Result

	if len(errors) > 0 {
		return ReviewScriptCallbackDataMultiError(errors)
	}

	return nil
}

// ReviewScriptCallbackDataMultiError is an error wrapping multiple validation
// errors returned by ReviewScriptCallbackData.ValidateAll() if the designated
// constraints aren't met.
type ReviewScriptCallbackDataMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m ReviewScriptCallbackDataMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m ReviewScriptCallbackDataMultiError) AllErrors() []error { return m }

// ReviewScriptCallbackDataValidationError is the validation error returned by
// ReviewScriptCallbackData.Validate if the designated constraints aren't met.
type ReviewScriptCallbackDataValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e ReviewScriptCallbackDataValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e ReviewScriptCallbackDataValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e ReviewScriptCallbackDataValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e ReviewScriptCallbackDataValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e ReviewScriptCallbackDataValidationError) ErrorName() string {
	return "ReviewScriptCallbackDataValidationError"
}

// Error satisfies the builtin error interface
func (e ReviewScriptCallbackDataValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sReviewScriptCallbackData.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = ReviewScriptCallbackDataValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = ReviewScriptCallbackDataValidationError{}

// Validate checks the field values on GetReviewLogReq with the rules defined
// in the proto definition for this message. If any rules are violated, the
// first error encountered is returned, or nil if there are no violations.
func (m *GetReviewLogReq) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GetReviewLogReq with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// GetReviewLogReqMultiError, or nil if none found.
func (m *GetReviewLogReq) ValidateAll() error {
	return m.validate(true)
}

func (m *GetReviewLogReq) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Userid

	// no validation rules for BizType

	// no validation rules for BizId

	if len(errors) > 0 {
		return GetReviewLogReqMultiError(errors)
	}

	return nil
}

// GetReviewLogReqMultiError is an error wrapping multiple validation errors
// returned by GetReviewLogReq.ValidateAll() if the designated constraints
// aren't met.
type GetReviewLogReqMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetReviewLogReqMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetReviewLogReqMultiError) AllErrors() []error { return m }

// GetReviewLogReqValidationError is the validation error returned by
// GetReviewLogReq.Validate if the designated constraints aren't met.
type GetReviewLogReqValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetReviewLogReqValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetReviewLogReqValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetReviewLogReqValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetReviewLogReqValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetReviewLogReqValidationError) ErrorName() string { return "GetReviewLogReqValidationError" }

// Error satisfies the builtin error interface
func (e GetReviewLogReqValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetReviewLogReq.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetReviewLogReqValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetReviewLogReqValidationError{}

// Validate checks the field values on GetReviewLogResp with the rules defined
// in the proto definition for this message. If any rules are violated, the
// first error encountered is returned, or nil if there are no violations.
func (m *GetReviewLogResp) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GetReviewLogResp with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// GetReviewLogRespMultiError, or nil if none found.
func (m *GetReviewLogResp) ValidateAll() error {
	return m.validate(true)
}

func (m *GetReviewLogResp) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetBase()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetReviewLogRespValidationError{
					field:  "Base",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetReviewLogRespValidationError{
					field:  "Base",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetBase()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetReviewLogRespValidationError{
				field:  "Base",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetLog()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetReviewLogRespValidationError{
					field:  "Log",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetReviewLogRespValidationError{
					field:  "Log",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetLog()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetReviewLogRespValidationError{
				field:  "Log",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return GetReviewLogRespMultiError(errors)
	}

	return nil
}

// GetReviewLogRespMultiError is an error wrapping multiple validation errors
// returned by GetReviewLogResp.ValidateAll() if the designated constraints
// aren't met.
type GetReviewLogRespMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetReviewLogRespMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetReviewLogRespMultiError) AllErrors() []error { return m }

// GetReviewLogRespValidationError is the validation error returned by
// GetReviewLogResp.Validate if the designated constraints aren't met.
type GetReviewLogRespValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetReviewLogRespValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetReviewLogRespValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetReviewLogRespValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetReviewLogRespValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetReviewLogRespValidationError) ErrorName() string { return "GetReviewLogRespValidationError" }

// Error satisfies the builtin error interface
func (e GetReviewLogRespValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetReviewLogResp.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetReviewLogRespValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetReviewLogRespValidationError{}

// Validate checks the field values on GetReviewLogsReq with the rules defined
// in the proto definition for this message. If any rules are violated, the
// first error encountered is returned, or nil if there are no violations.
func (m *GetReviewLogsReq) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GetReviewLogsReq with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// GetReviewLogsReqMultiError, or nil if none found.
func (m *GetReviewLogsReq) ValidateAll() error {
	return m.validate(true)
}

func (m *GetReviewLogsReq) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Userid

	// no validation rules for BizType

	// no validation rules for RiskLevel

	// no validation rules for Page

	// no validation rules for PageSize

	if len(errors) > 0 {
		return GetReviewLogsReqMultiError(errors)
	}

	return nil
}

// GetReviewLogsReqMultiError is an error wrapping multiple validation errors
// returned by GetReviewLogsReq.ValidateAll() if the designated constraints
// aren't met.
type GetReviewLogsReqMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetReviewLogsReqMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetReviewLogsReqMultiError) AllErrors() []error { return m }

// GetReviewLogsReqValidationError is the validation error returned by
// GetReviewLogsReq.Validate if the designated constraints aren't met.
type GetReviewLogsReqValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetReviewLogsReqValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetReviewLogsReqValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetReviewLogsReqValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetReviewLogsReqValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetReviewLogsReqValidationError) ErrorName() string { return "GetReviewLogsReqValidationError" }

// Error satisfies the builtin error interface
func (e GetReviewLogsReqValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetReviewLogsReq.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetReviewLogsReqValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetReviewLogsReqValidationError{}

// Validate checks the field values on GetReviewLogsResp with the rules defined
// in the proto definition for this message. If any rules are violated, the
// first error encountered is returned, or nil if there are no violations.
func (m *GetReviewLogsResp) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GetReviewLogsResp with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// GetReviewLogsRespMultiError, or nil if none found.
func (m *GetReviewLogsResp) ValidateAll() error {
	return m.validate(true)
}

func (m *GetReviewLogsResp) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetBase()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetReviewLogsRespValidationError{
					field:  "Base",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetReviewLogsRespValidationError{
					field:  "Base",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetBase()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetReviewLogsRespValidationError{
				field:  "Base",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	for idx, item := range m.GetLogs() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, GetReviewLogsRespValidationError{
						field:  fmt.Sprintf("Logs[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, GetReviewLogsRespValidationError{
						field:  fmt.Sprintf("Logs[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return GetReviewLogsRespValidationError{
					field:  fmt.Sprintf("Logs[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	// no validation rules for Total

	if len(errors) > 0 {
		return GetReviewLogsRespMultiError(errors)
	}

	return nil
}

// GetReviewLogsRespMultiError is an error wrapping multiple validation errors
// returned by GetReviewLogsResp.ValidateAll() if the designated constraints
// aren't met.
type GetReviewLogsRespMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetReviewLogsRespMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetReviewLogsRespMultiError) AllErrors() []error { return m }

// GetReviewLogsRespValidationError is the validation error returned by
// GetReviewLogsResp.Validate if the designated constraints aren't met.
type GetReviewLogsRespValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetReviewLogsRespValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetReviewLogsRespValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetReviewLogsRespValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetReviewLogsRespValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetReviewLogsRespValidationError) ErrorName() string {
	return "GetReviewLogsRespValidationError"
}

// Error satisfies the builtin error interface
func (e GetReviewLogsRespValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetReviewLogsResp.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetReviewLogsRespValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetReviewLogsRespValidationError{}

// Validate checks the field values on ReviewLogInfo with the rules defined in
// the proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *ReviewLogInfo) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on ReviewLogInfo with the rules defined
// in the proto definition for this message. If any rules are violated, the
// result is a list of violation errors wrapped in ReviewLogInfoMultiError, or
// nil if none found.
func (m *ReviewLogInfo) ValidateAll() error {
	return m.validate(true)
}

func (m *ReviewLogInfo) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Id

	// no validation rules for Userid

	// no validation rules for BizType

	// no validation rules for BizId

	// no validation rules for Data

	// no validation rules for Status

	// no validation rules for RequestId

	// no validation rules for RiskReason

	// no validation rules for RiskLabels

	// no validation rules for RiskLevel

	// no validation rules for AuditDetail

	// no validation rules for Ct

	// no validation rules for Ut

	if len(errors) > 0 {
		return ReviewLogInfoMultiError(errors)
	}

	return nil
}

// ReviewLogInfoMultiError is an error wrapping multiple validation errors
// returned by ReviewLogInfo.ValidateAll() if the designated constraints
// aren't met.
type ReviewLogInfoMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m ReviewLogInfoMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m ReviewLogInfoMultiError) AllErrors() []error { return m }

// ReviewLogInfoValidationError is the validation error returned by
// ReviewLogInfo.Validate if the designated constraints aren't met.
type ReviewLogInfoValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e ReviewLogInfoValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e ReviewLogInfoValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e ReviewLogInfoValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e ReviewLogInfoValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e ReviewLogInfoValidationError) ErrorName() string { return "ReviewLogInfoValidationError" }

// Error satisfies the builtin error interface
func (e ReviewLogInfoValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sReviewLogInfo.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = ReviewLogInfoValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = ReviewLogInfoValidationError{}
