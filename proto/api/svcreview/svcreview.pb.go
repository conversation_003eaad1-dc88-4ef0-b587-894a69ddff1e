// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.36.6
// 	protoc        v5.29.3
// source: svcreview.proto

package svcreview

import (
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	common "new-gitlab.xunlei.cn/vcproject/backends/proto/api/common"
	reflect "reflect"
	sync "sync"
	unsafe "unsafe"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type ReviewBizType int32

const (
	ReviewBizType_unknown            ReviewBizType = 0
	ReviewBizType_UserNickname       ReviewBizType = 1 // 用户昵称
	ReviewBizType_UserAvatar         ReviewBizType = 2 // 用户头像
	ReviewBizType_UserSignVoice      ReviewBizType = 3 // 用户签名语音
	ReviewBizType_UserTextSign       ReviewBizType = 4 // 用户签名文字
	ReviewBizType_UserAlbum          ReviewBizType = 5 // 用户相册
	ReviewBizType_Moment             ReviewBizType = 6
	ReviewBizType_Comment            ReviewBizType = 7
	ReviewBizType_RoomTitle          ReviewBizType = 8
	ReviewBizType_RoomNotice         ReviewBizType = 9
	ReviewBizType_RoomCover          ReviewBizType = 10
	ReviewBizType_RoomBg             ReviewBizType = 11
	ReviewBizType_ScriptTitle        ReviewBizType = 12 //剧本标题
	ReviewBizType_ScriptCover        ReviewBizType = 13 //剧本封面
	ReviewBizType_ScriptLine         ReviewBizType = 14 //剧本台词
	ReviewBizType_ScriptTopic        ReviewBizType = 15 //剧本话题
	ReviewBizType_ScriptDubbingText  ReviewBizType = 16 //剧本配音文本
	ReviewBizType_ScriptDubbingVoice ReviewBizType = 17 //剧本配音语音
	ReviewBizType_ScriptCommentText  ReviewBizType = 18 //剧本评论文本
	ReviewBizType_ScriptCommentVoice ReviewBizType = 19 //剧本评论语音
)

// Enum value maps for ReviewBizType.
var (
	ReviewBizType_name = map[int32]string{
		0:  "unknown",
		1:  "UserNickname",
		2:  "UserAvatar",
		3:  "UserSignVoice",
		4:  "UserTextSign",
		5:  "UserAlbum",
		6:  "Moment",
		7:  "Comment",
		8:  "RoomTitle",
		9:  "RoomNotice",
		10: "RoomCover",
		11: "RoomBg",
		12: "ScriptTitle",
		13: "ScriptCover",
		14: "ScriptLine",
		15: "ScriptTopic",
		16: "ScriptDubbingText",
		17: "ScriptDubbingVoice",
		18: "ScriptCommentText",
		19: "ScriptCommentVoice",
	}
	ReviewBizType_value = map[string]int32{
		"unknown":            0,
		"UserNickname":       1,
		"UserAvatar":         2,
		"UserSignVoice":      3,
		"UserTextSign":       4,
		"UserAlbum":          5,
		"Moment":             6,
		"Comment":            7,
		"RoomTitle":          8,
		"RoomNotice":         9,
		"RoomCover":          10,
		"RoomBg":             11,
		"ScriptTitle":        12,
		"ScriptCover":        13,
		"ScriptLine":         14,
		"ScriptTopic":        15,
		"ScriptDubbingText":  16,
		"ScriptDubbingVoice": 17,
		"ScriptCommentText":  18,
		"ScriptCommentVoice": 19,
	}
)

func (x ReviewBizType) Enum() *ReviewBizType {
	p := new(ReviewBizType)
	*p = x
	return p
}

func (x ReviewBizType) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (ReviewBizType) Descriptor() protoreflect.EnumDescriptor {
	return file_svcreview_proto_enumTypes[0].Descriptor()
}

func (ReviewBizType) Type() protoreflect.EnumType {
	return &file_svcreview_proto_enumTypes[0]
}

func (x ReviewBizType) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use ReviewBizType.Descriptor instead.
func (ReviewBizType) EnumDescriptor() ([]byte, []int) {
	return file_svcreview_proto_rawDescGZIP(), []int{0}
}

type ReviewMemberType int32

const (
	ReviewMemberType_review_unknown ReviewMemberType = 0
	ReviewMemberType_avatar         ReviewMemberType = 1 // 1头像
	ReviewMemberType_album          ReviewMemberType = 2 // 2相册
	ReviewMemberType_other          ReviewMemberType = 3 // 3其它字段
)

// Enum value maps for ReviewMemberType.
var (
	ReviewMemberType_name = map[int32]string{
		0: "review_unknown",
		1: "avatar",
		2: "album",
		3: "other",
	}
	ReviewMemberType_value = map[string]int32{
		"review_unknown": 0,
		"avatar":         1,
		"album":          2,
		"other":          3,
	}
)

func (x ReviewMemberType) Enum() *ReviewMemberType {
	p := new(ReviewMemberType)
	*p = x
	return p
}

func (x ReviewMemberType) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (ReviewMemberType) Descriptor() protoreflect.EnumDescriptor {
	return file_svcreview_proto_enumTypes[1].Descriptor()
}

func (ReviewMemberType) Type() protoreflect.EnumType {
	return &file_svcreview_proto_enumTypes[1]
}

func (x ReviewMemberType) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use ReviewMemberType.Descriptor instead.
func (ReviewMemberType) EnumDescriptor() ([]byte, []int) {
	return file_svcreview_proto_rawDescGZIP(), []int{1}
}

// 审核结果
type AuditResult int32

const (
	// 默认，不使用
	AuditResult_audit_result_unknown AuditResult = 0
	// 通过
	AuditResult_pass AuditResult = 1
	// 拒绝
	AuditResult_reject AuditResult = 2
	// 审核中
	AuditResult_review AuditResult = 3
	// 伪发送 (命中了拒绝并且一级标签是广告)
	AuditResult_fake_send AuditResult = 4
)

// Enum value maps for AuditResult.
var (
	AuditResult_name = map[int32]string{
		0: "audit_result_unknown",
		1: "pass",
		2: "reject",
		3: "review",
		4: "fake_send",
	}
	AuditResult_value = map[string]int32{
		"audit_result_unknown": 0,
		"pass":                 1,
		"reject":               2,
		"review":               3,
		"fake_send":            4,
	}
)

func (x AuditResult) Enum() *AuditResult {
	p := new(AuditResult)
	*p = x
	return p
}

func (x AuditResult) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (AuditResult) Descriptor() protoreflect.EnumDescriptor {
	return file_svcreview_proto_enumTypes[2].Descriptor()
}

func (AuditResult) Type() protoreflect.EnumType {
	return &file_svcreview_proto_enumTypes[2]
}

func (x AuditResult) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use AuditResult.Descriptor instead.
func (AuditResult) EnumDescriptor() ([]byte, []int) {
	return file_svcreview_proto_rawDescGZIP(), []int{2}
}

type AuditSubResult int32

const (
	// 默认，不使用
	AuditSubResult_audit_sub_unknown AuditSubResult = 0
	// 联系方式拦截 男用户亲密的等级过低
	AuditSubResult_audit_sub_male_lianxifangshi AuditSubResult = 1
)

// Enum value maps for AuditSubResult.
var (
	AuditSubResult_name = map[int32]string{
		0: "audit_sub_unknown",
		1: "audit_sub_male_lianxifangshi",
	}
	AuditSubResult_value = map[string]int32{
		"audit_sub_unknown":            0,
		"audit_sub_male_lianxifangshi": 1,
	}
)

func (x AuditSubResult) Enum() *AuditSubResult {
	p := new(AuditSubResult)
	*p = x
	return p
}

func (x AuditSubResult) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (AuditSubResult) Descriptor() protoreflect.EnumDescriptor {
	return file_svcreview_proto_enumTypes[3].Descriptor()
}

func (AuditSubResult) Type() protoreflect.EnumType {
	return &file_svcreview_proto_enumTypes[3]
}

func (x AuditSubResult) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use AuditSubResult.Descriptor instead.
func (AuditSubResult) EnumDescriptor() ([]byte, []int) {
	return file_svcreview_proto_rawDescGZIP(), []int{3}
}

// shield 结果
type ShieldResult int32

const (
	ShieldResult_shield_unknown ShieldResult = 0 // 默认，不使用
	ShieldResult_shield_pass    ShieldResult = 1 //通过
	ShieldResult_shield_reject  ShieldResult = 2 //拒绝
	ShieldResult_shield_review  ShieldResult = 3 //
)

// Enum value maps for ShieldResult.
var (
	ShieldResult_name = map[int32]string{
		0: "shield_unknown",
		1: "shield_pass",
		2: "shield_reject",
		3: "shield_review",
	}
	ShieldResult_value = map[string]int32{
		"shield_unknown": 0,
		"shield_pass":    1,
		"shield_reject":  2,
		"shield_review":  3,
	}
)

func (x ShieldResult) Enum() *ShieldResult {
	p := new(ShieldResult)
	*p = x
	return p
}

func (x ShieldResult) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (ShieldResult) Descriptor() protoreflect.EnumDescriptor {
	return file_svcreview_proto_enumTypes[4].Descriptor()
}

func (ShieldResult) Type() protoreflect.EnumType {
	return &file_svcreview_proto_enumTypes[4]
}

func (x ShieldResult) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use ShieldResult.Descriptor instead.
func (ShieldResult) EnumDescriptor() ([]byte, []int) {
	return file_svcreview_proto_rawDescGZIP(), []int{4}
}

type ReviewImage struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Id            string                 `protobuf:"bytes,1,opt,name=id,proto3" json:"id,omitempty"`
	Url           string                 `protobuf:"bytes,2,opt,name=url,proto3" json:"url,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *ReviewImage) Reset() {
	*x = ReviewImage{}
	mi := &file_svcreview_proto_msgTypes[0]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ReviewImage) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ReviewImage) ProtoMessage() {}

func (x *ReviewImage) ProtoReflect() protoreflect.Message {
	mi := &file_svcreview_proto_msgTypes[0]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ReviewImage.ProtoReflect.Descriptor instead.
func (*ReviewImage) Descriptor() ([]byte, []int) {
	return file_svcreview_proto_rawDescGZIP(), []int{0}
}

func (x *ReviewImage) GetId() string {
	if x != nil {
		return x.Id
	}
	return ""
}

func (x *ReviewImage) GetUrl() string {
	if x != nil {
		return x.Url
	}
	return ""
}

type ReviewVoice struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Id            string                 `protobuf:"bytes,1,opt,name=id,proto3" json:"id,omitempty"`
	Sec           int64                  `protobuf:"varint,2,opt,name=sec,proto3" json:"sec,omitempty"`
	Url           string                 `protobuf:"bytes,3,opt,name=url,proto3" json:"url,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *ReviewVoice) Reset() {
	*x = ReviewVoice{}
	mi := &file_svcreview_proto_msgTypes[1]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ReviewVoice) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ReviewVoice) ProtoMessage() {}

func (x *ReviewVoice) ProtoReflect() protoreflect.Message {
	mi := &file_svcreview_proto_msgTypes[1]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ReviewVoice.ProtoReflect.Descriptor instead.
func (*ReviewVoice) Descriptor() ([]byte, []int) {
	return file_svcreview_proto_rawDescGZIP(), []int{1}
}

func (x *ReviewVoice) GetId() string {
	if x != nil {
		return x.Id
	}
	return ""
}

func (x *ReviewVoice) GetSec() int64 {
	if x != nil {
		return x.Sec
	}
	return 0
}

func (x *ReviewVoice) GetUrl() string {
	if x != nil {
		return x.Url
	}
	return ""
}

type ReviewVideo struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Id            string                 `protobuf:"bytes,1,opt,name=id,proto3" json:"id,omitempty"`
	Sec           int64                  `protobuf:"varint,2,opt,name=sec,proto3" json:"sec,omitempty"`
	Url           string                 `protobuf:"bytes,3,opt,name=url,proto3" json:"url,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *ReviewVideo) Reset() {
	*x = ReviewVideo{}
	mi := &file_svcreview_proto_msgTypes[2]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ReviewVideo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ReviewVideo) ProtoMessage() {}

func (x *ReviewVideo) ProtoReflect() protoreflect.Message {
	mi := &file_svcreview_proto_msgTypes[2]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ReviewVideo.ProtoReflect.Descriptor instead.
func (*ReviewVideo) Descriptor() ([]byte, []int) {
	return file_svcreview_proto_rawDescGZIP(), []int{2}
}

func (x *ReviewVideo) GetId() string {
	if x != nil {
		return x.Id
	}
	return ""
}

func (x *ReviewVideo) GetSec() int64 {
	if x != nil {
		return x.Sec
	}
	return 0
}

func (x *ReviewVideo) GetUrl() string {
	if x != nil {
		return x.Url
	}
	return ""
}

type ShumeiAudioCallbackReq struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Code          int32                  `protobuf:"varint,1,opt,name=code,proto3" json:"code,omitempty"`
	Message       string                 `protobuf:"bytes,2,opt,name=message,proto3" json:"message,omitempty"`
	RequestId     string                 `protobuf:"bytes,3,opt,name=requestId,proto3" json:"requestId,omitempty"`
	BtId          string                 `protobuf:"bytes,4,opt,name=btId,proto3" json:"btId,omitempty"`
	AudioText     string                 `protobuf:"bytes,5,opt,name=audioText,proto3" json:"audioText,omitempty"`
	AudioTime     int32                  `protobuf:"varint,6,opt,name=audioTime,proto3" json:"audioTime,omitempty"`
	Labels        string                 `protobuf:"bytes,7,opt,name=labels,proto3" json:"labels,omitempty"`
	RiskLevel     string                 `protobuf:"bytes,8,opt,name=riskLevel,proto3" json:"riskLevel,omitempty"`
	Detail        []*ShumeiAudioDetail   `protobuf:"bytes,9,rep,name=detail,proto3" json:"detail,omitempty"`
	Gender        *ShumeiAudioGender     `protobuf:"bytes,10,opt,name=gender,proto3" json:"gender,omitempty"`
	Tags          []*ShumeiAudioTags     `protobuf:"bytes,11,rep,name=tags,proto3" json:"tags,omitempty"`
	CallbackParam *ReviewCallbackOrigin  `protobuf:"bytes,12,opt,name=callbackParam,proto3" json:"callbackParam,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *ShumeiAudioCallbackReq) Reset() {
	*x = ShumeiAudioCallbackReq{}
	mi := &file_svcreview_proto_msgTypes[3]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ShumeiAudioCallbackReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ShumeiAudioCallbackReq) ProtoMessage() {}

func (x *ShumeiAudioCallbackReq) ProtoReflect() protoreflect.Message {
	mi := &file_svcreview_proto_msgTypes[3]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ShumeiAudioCallbackReq.ProtoReflect.Descriptor instead.
func (*ShumeiAudioCallbackReq) Descriptor() ([]byte, []int) {
	return file_svcreview_proto_rawDescGZIP(), []int{3}
}

func (x *ShumeiAudioCallbackReq) GetCode() int32 {
	if x != nil {
		return x.Code
	}
	return 0
}

func (x *ShumeiAudioCallbackReq) GetMessage() string {
	if x != nil {
		return x.Message
	}
	return ""
}

func (x *ShumeiAudioCallbackReq) GetRequestId() string {
	if x != nil {
		return x.RequestId
	}
	return ""
}

func (x *ShumeiAudioCallbackReq) GetBtId() string {
	if x != nil {
		return x.BtId
	}
	return ""
}

func (x *ShumeiAudioCallbackReq) GetAudioText() string {
	if x != nil {
		return x.AudioText
	}
	return ""
}

func (x *ShumeiAudioCallbackReq) GetAudioTime() int32 {
	if x != nil {
		return x.AudioTime
	}
	return 0
}

func (x *ShumeiAudioCallbackReq) GetLabels() string {
	if x != nil {
		return x.Labels
	}
	return ""
}

func (x *ShumeiAudioCallbackReq) GetRiskLevel() string {
	if x != nil {
		return x.RiskLevel
	}
	return ""
}

func (x *ShumeiAudioCallbackReq) GetDetail() []*ShumeiAudioDetail {
	if x != nil {
		return x.Detail
	}
	return nil
}

func (x *ShumeiAudioCallbackReq) GetGender() *ShumeiAudioGender {
	if x != nil {
		return x.Gender
	}
	return nil
}

func (x *ShumeiAudioCallbackReq) GetTags() []*ShumeiAudioTags {
	if x != nil {
		return x.Tags
	}
	return nil
}

func (x *ShumeiAudioCallbackReq) GetCallbackParam() *ReviewCallbackOrigin {
	if x != nil {
		return x.CallbackParam
	}
	return nil
}

type ShumeiAudioDetail struct {
	state            protoimpl.MessageState `protogen:"open.v1"`
	AudioStarttime   int32                  `protobuf:"varint,1,opt,name=audioStarttime,proto3" json:"audioStarttime,omitempty"`
	AudioEndtime     int32                  `protobuf:"varint,2,opt,name=audioEndtime,proto3" json:"audioEndtime,omitempty"`
	AudioUrl         string                 `protobuf:"bytes,3,opt,name=audioUrl,proto3" json:"audioUrl,omitempty"`
	AudioText        string                 `protobuf:"bytes,4,opt,name=audioText,proto3" json:"audioText,omitempty"`
	RiskLevel        string                 `protobuf:"bytes,5,opt,name=riskLevel,proto3" json:"riskLevel,omitempty"`
	RiskType         int32                  `protobuf:"varint,6,opt,name=riskType,proto3" json:"riskType,omitempty"`
	AudioMatchedItem string                 `protobuf:"bytes,7,opt,name=audioMatchedItem,proto3" json:"audioMatchedItem,omitempty"`
	Description      string                 `protobuf:"bytes,8,opt,name=description,proto3" json:"description,omitempty"`
	unknownFields    protoimpl.UnknownFields
	sizeCache        protoimpl.SizeCache
}

func (x *ShumeiAudioDetail) Reset() {
	*x = ShumeiAudioDetail{}
	mi := &file_svcreview_proto_msgTypes[4]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ShumeiAudioDetail) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ShumeiAudioDetail) ProtoMessage() {}

func (x *ShumeiAudioDetail) ProtoReflect() protoreflect.Message {
	mi := &file_svcreview_proto_msgTypes[4]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ShumeiAudioDetail.ProtoReflect.Descriptor instead.
func (*ShumeiAudioDetail) Descriptor() ([]byte, []int) {
	return file_svcreview_proto_rawDescGZIP(), []int{4}
}

func (x *ShumeiAudioDetail) GetAudioStarttime() int32 {
	if x != nil {
		return x.AudioStarttime
	}
	return 0
}

func (x *ShumeiAudioDetail) GetAudioEndtime() int32 {
	if x != nil {
		return x.AudioEndtime
	}
	return 0
}

func (x *ShumeiAudioDetail) GetAudioUrl() string {
	if x != nil {
		return x.AudioUrl
	}
	return ""
}

func (x *ShumeiAudioDetail) GetAudioText() string {
	if x != nil {
		return x.AudioText
	}
	return ""
}

func (x *ShumeiAudioDetail) GetRiskLevel() string {
	if x != nil {
		return x.RiskLevel
	}
	return ""
}

func (x *ShumeiAudioDetail) GetRiskType() int32 {
	if x != nil {
		return x.RiskType
	}
	return 0
}

func (x *ShumeiAudioDetail) GetAudioMatchedItem() string {
	if x != nil {
		return x.AudioMatchedItem
	}
	return ""
}

func (x *ShumeiAudioDetail) GetDescription() string {
	if x != nil {
		return x.Description
	}
	return ""
}

type ShumeiAudioGender struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Label         string                 `protobuf:"bytes,1,opt,name=label,proto3" json:"label,omitempty"`
	Confidence    int32                  `protobuf:"varint,2,opt,name=confidence,proto3" json:"confidence,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *ShumeiAudioGender) Reset() {
	*x = ShumeiAudioGender{}
	mi := &file_svcreview_proto_msgTypes[5]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ShumeiAudioGender) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ShumeiAudioGender) ProtoMessage() {}

func (x *ShumeiAudioGender) ProtoReflect() protoreflect.Message {
	mi := &file_svcreview_proto_msgTypes[5]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ShumeiAudioGender.ProtoReflect.Descriptor instead.
func (*ShumeiAudioGender) Descriptor() ([]byte, []int) {
	return file_svcreview_proto_rawDescGZIP(), []int{5}
}

func (x *ShumeiAudioGender) GetLabel() string {
	if x != nil {
		return x.Label
	}
	return ""
}

func (x *ShumeiAudioGender) GetConfidence() int32 {
	if x != nil {
		return x.Confidence
	}
	return 0
}

type ShumeiAudioTags struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Label         string                 `protobuf:"bytes,1,opt,name=label,proto3" json:"label,omitempty"`
	Confidence    int32                  `protobuf:"varint,2,opt,name=confidence,proto3" json:"confidence,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *ShumeiAudioTags) Reset() {
	*x = ShumeiAudioTags{}
	mi := &file_svcreview_proto_msgTypes[6]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ShumeiAudioTags) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ShumeiAudioTags) ProtoMessage() {}

func (x *ShumeiAudioTags) ProtoReflect() protoreflect.Message {
	mi := &file_svcreview_proto_msgTypes[6]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ShumeiAudioTags.ProtoReflect.Descriptor instead.
func (*ShumeiAudioTags) Descriptor() ([]byte, []int) {
	return file_svcreview_proto_rawDescGZIP(), []int{6}
}

func (x *ShumeiAudioTags) GetLabel() string {
	if x != nil {
		return x.Label
	}
	return ""
}

func (x *ShumeiAudioTags) GetConfidence() int32 {
	if x != nil {
		return x.Confidence
	}
	return 0
}

type ReviewCallbackOrigin struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Seqid         int64                  `protobuf:"varint,1,opt,name=seqid,proto3" json:"seqid,omitempty"`
	Content       string                 `protobuf:"bytes,2,opt,name=content,proto3" json:"content,omitempty"`
	BizType       string                 `protobuf:"bytes,3,opt,name=biz_type,json=bizType,proto3" json:"biz_type,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *ReviewCallbackOrigin) Reset() {
	*x = ReviewCallbackOrigin{}
	mi := &file_svcreview_proto_msgTypes[7]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ReviewCallbackOrigin) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ReviewCallbackOrigin) ProtoMessage() {}

func (x *ReviewCallbackOrigin) ProtoReflect() protoreflect.Message {
	mi := &file_svcreview_proto_msgTypes[7]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ReviewCallbackOrigin.ProtoReflect.Descriptor instead.
func (*ReviewCallbackOrigin) Descriptor() ([]byte, []int) {
	return file_svcreview_proto_rawDescGZIP(), []int{7}
}

func (x *ReviewCallbackOrigin) GetSeqid() int64 {
	if x != nil {
		return x.Seqid
	}
	return 0
}

func (x *ReviewCallbackOrigin) GetContent() string {
	if x != nil {
		return x.Content
	}
	return ""
}

func (x *ReviewCallbackOrigin) GetBizType() string {
	if x != nil {
		return x.BizType
	}
	return ""
}

type ReviewCommonResp struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Base          *common.SvcBaseResp    `protobuf:"bytes,1,opt,name=base,proto3" json:"base,omitempty"`
	Result        AuditResult            `protobuf:"varint,2,opt,name=result,proto3,enum=vc.svcreview.AuditResult" json:"result,omitempty"`
	Reason        string                 `protobuf:"bytes,3,opt,name=reason,proto3" json:"reason,omitempty"`
	Detail        *ReviewResultDetail    `protobuf:"bytes,4,opt,name=detail,proto3" json:"detail,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *ReviewCommonResp) Reset() {
	*x = ReviewCommonResp{}
	mi := &file_svcreview_proto_msgTypes[8]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ReviewCommonResp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ReviewCommonResp) ProtoMessage() {}

func (x *ReviewCommonResp) ProtoReflect() protoreflect.Message {
	mi := &file_svcreview_proto_msgTypes[8]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ReviewCommonResp.ProtoReflect.Descriptor instead.
func (*ReviewCommonResp) Descriptor() ([]byte, []int) {
	return file_svcreview_proto_rawDescGZIP(), []int{8}
}

func (x *ReviewCommonResp) GetBase() *common.SvcBaseResp {
	if x != nil {
		return x.Base
	}
	return nil
}

func (x *ReviewCommonResp) GetResult() AuditResult {
	if x != nil {
		return x.Result
	}
	return AuditResult_audit_result_unknown
}

func (x *ReviewCommonResp) GetReason() string {
	if x != nil {
		return x.Reason
	}
	return ""
}

func (x *ReviewCommonResp) GetDetail() *ReviewResultDetail {
	if x != nil {
		return x.Detail
	}
	return nil
}

type ReviewResultDetail struct {
	state          protoimpl.MessageState `protogen:"open.v1"`
	Result         AuditResult            `protobuf:"varint,1,opt,name=result,proto3,enum=vc.svcreview.AuditResult" json:"result,omitempty"`
	Text           *ReviewTextResult      `protobuf:"bytes,4,opt,name=text,proto3" json:"text,omitempty"`
	Image          []*ReviewImageResult   `protobuf:"bytes,5,rep,name=image,proto3" json:"image,omitempty"`
	Voice          *ReviewVoiceResult     `protobuf:"bytes,6,opt,name=voice,proto3" json:"voice,omitempty"`
	Video          *ReviewVideoResult     `protobuf:"bytes,7,opt,name=video,proto3" json:"video,omitempty"`
	NoneedPushTips bool                   `protobuf:"varint,8,opt,name=noneed_push_tips,json=noneedPushTips,proto3" json:"noneed_push_tips,omitempty"`
	unknownFields  protoimpl.UnknownFields
	sizeCache      protoimpl.SizeCache
}

func (x *ReviewResultDetail) Reset() {
	*x = ReviewResultDetail{}
	mi := &file_svcreview_proto_msgTypes[9]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ReviewResultDetail) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ReviewResultDetail) ProtoMessage() {}

func (x *ReviewResultDetail) ProtoReflect() protoreflect.Message {
	mi := &file_svcreview_proto_msgTypes[9]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ReviewResultDetail.ProtoReflect.Descriptor instead.
func (*ReviewResultDetail) Descriptor() ([]byte, []int) {
	return file_svcreview_proto_rawDescGZIP(), []int{9}
}

func (x *ReviewResultDetail) GetResult() AuditResult {
	if x != nil {
		return x.Result
	}
	return AuditResult_audit_result_unknown
}

func (x *ReviewResultDetail) GetText() *ReviewTextResult {
	if x != nil {
		return x.Text
	}
	return nil
}

func (x *ReviewResultDetail) GetImage() []*ReviewImageResult {
	if x != nil {
		return x.Image
	}
	return nil
}

func (x *ReviewResultDetail) GetVoice() *ReviewVoiceResult {
	if x != nil {
		return x.Voice
	}
	return nil
}

func (x *ReviewResultDetail) GetVideo() *ReviewVideoResult {
	if x != nil {
		return x.Video
	}
	return nil
}

func (x *ReviewResultDetail) GetNoneedPushTips() bool {
	if x != nil {
		return x.NoneedPushTips
	}
	return false
}

type ReviewImageResult struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Image         *ReviewImage           `protobuf:"bytes,1,opt,name=image,proto3" json:"image,omitempty"`
	Result        AuditResult            `protobuf:"varint,2,opt,name=result,proto3,enum=vc.svcreview.AuditResult" json:"result,omitempty"`
	Reason        string                 `protobuf:"bytes,3,opt,name=reason,proto3" json:"reason,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *ReviewImageResult) Reset() {
	*x = ReviewImageResult{}
	mi := &file_svcreview_proto_msgTypes[10]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ReviewImageResult) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ReviewImageResult) ProtoMessage() {}

func (x *ReviewImageResult) ProtoReflect() protoreflect.Message {
	mi := &file_svcreview_proto_msgTypes[10]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ReviewImageResult.ProtoReflect.Descriptor instead.
func (*ReviewImageResult) Descriptor() ([]byte, []int) {
	return file_svcreview_proto_rawDescGZIP(), []int{10}
}

func (x *ReviewImageResult) GetImage() *ReviewImage {
	if x != nil {
		return x.Image
	}
	return nil
}

func (x *ReviewImageResult) GetResult() AuditResult {
	if x != nil {
		return x.Result
	}
	return AuditResult_audit_result_unknown
}

func (x *ReviewImageResult) GetReason() string {
	if x != nil {
		return x.Reason
	}
	return ""
}

type ReviewVoiceResult struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Voice         *ReviewVoice           `protobuf:"bytes,1,opt,name=voice,proto3" json:"voice,omitempty"`
	Result        AuditResult            `protobuf:"varint,2,opt,name=result,proto3,enum=vc.svcreview.AuditResult" json:"result,omitempty"`
	Reason        string                 `protobuf:"bytes,3,opt,name=reason,proto3" json:"reason,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *ReviewVoiceResult) Reset() {
	*x = ReviewVoiceResult{}
	mi := &file_svcreview_proto_msgTypes[11]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ReviewVoiceResult) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ReviewVoiceResult) ProtoMessage() {}

func (x *ReviewVoiceResult) ProtoReflect() protoreflect.Message {
	mi := &file_svcreview_proto_msgTypes[11]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ReviewVoiceResult.ProtoReflect.Descriptor instead.
func (*ReviewVoiceResult) Descriptor() ([]byte, []int) {
	return file_svcreview_proto_rawDescGZIP(), []int{11}
}

func (x *ReviewVoiceResult) GetVoice() *ReviewVoice {
	if x != nil {
		return x.Voice
	}
	return nil
}

func (x *ReviewVoiceResult) GetResult() AuditResult {
	if x != nil {
		return x.Result
	}
	return AuditResult_audit_result_unknown
}

func (x *ReviewVoiceResult) GetReason() string {
	if x != nil {
		return x.Reason
	}
	return ""
}

type ReviewVideoResult struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Video         *ReviewVideo           `protobuf:"bytes,1,opt,name=video,proto3" json:"video,omitempty"`
	Result        AuditResult            `protobuf:"varint,2,opt,name=result,proto3,enum=vc.svcreview.AuditResult" json:"result,omitempty"`
	Reason        string                 `protobuf:"bytes,3,opt,name=reason,proto3" json:"reason,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *ReviewVideoResult) Reset() {
	*x = ReviewVideoResult{}
	mi := &file_svcreview_proto_msgTypes[12]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ReviewVideoResult) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ReviewVideoResult) ProtoMessage() {}

func (x *ReviewVideoResult) ProtoReflect() protoreflect.Message {
	mi := &file_svcreview_proto_msgTypes[12]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ReviewVideoResult.ProtoReflect.Descriptor instead.
func (*ReviewVideoResult) Descriptor() ([]byte, []int) {
	return file_svcreview_proto_rawDescGZIP(), []int{12}
}

func (x *ReviewVideoResult) GetVideo() *ReviewVideo {
	if x != nil {
		return x.Video
	}
	return nil
}

func (x *ReviewVideoResult) GetResult() AuditResult {
	if x != nil {
		return x.Result
	}
	return AuditResult_audit_result_unknown
}

func (x *ReviewVideoResult) GetReason() string {
	if x != nil {
		return x.Reason
	}
	return ""
}

type ReviewTextResult struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Text          string                 `protobuf:"bytes,1,opt,name=text,proto3" json:"text,omitempty"`
	Result        AuditResult            `protobuf:"varint,2,opt,name=result,proto3,enum=vc.svcreview.AuditResult" json:"result,omitempty"`
	Reason        string                 `protobuf:"bytes,3,opt,name=reason,proto3" json:"reason,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *ReviewTextResult) Reset() {
	*x = ReviewTextResult{}
	mi := &file_svcreview_proto_msgTypes[13]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ReviewTextResult) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ReviewTextResult) ProtoMessage() {}

func (x *ReviewTextResult) ProtoReflect() protoreflect.Message {
	mi := &file_svcreview_proto_msgTypes[13]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ReviewTextResult.ProtoReflect.Descriptor instead.
func (*ReviewTextResult) Descriptor() ([]byte, []int) {
	return file_svcreview_proto_rawDescGZIP(), []int{13}
}

func (x *ReviewTextResult) GetText() string {
	if x != nil {
		return x.Text
	}
	return ""
}

func (x *ReviewTextResult) GetResult() AuditResult {
	if x != nil {
		return x.Result
	}
	return AuditResult_audit_result_unknown
}

func (x *ReviewTextResult) GetReason() string {
	if x != nil {
		return x.Reason
	}
	return ""
}

type ShumeiMixCallbackReq struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	BtId          string                 `protobuf:"bytes,1,opt,name=btId,proto3" json:"btId,omitempty"`
	Code          int32                  `protobuf:"varint,2,opt,name=code,proto3" json:"code,omitempty"`
	RequestId     string                 `protobuf:"bytes,3,opt,name=requestId,proto3" json:"requestId,omitempty"`
	RiskLevel     string                 `protobuf:"bytes,4,opt,name=riskLevel,proto3" json:"riskLevel,omitempty"`
	ResultType    int32                  `protobuf:"varint,5,opt,name=resultType,proto3" json:"resultType,omitempty"` //0：机审，1：人审
	Details       *ShumeiMixDetail       `protobuf:"bytes,6,opt,name=details,proto3" json:"details,omitempty"`
	PassThrough   *ReviewCallbackData    `protobuf:"bytes,7,opt,name=passThrough,proto3" json:"passThrough,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *ShumeiMixCallbackReq) Reset() {
	*x = ShumeiMixCallbackReq{}
	mi := &file_svcreview_proto_msgTypes[14]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ShumeiMixCallbackReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ShumeiMixCallbackReq) ProtoMessage() {}

func (x *ShumeiMixCallbackReq) ProtoReflect() protoreflect.Message {
	mi := &file_svcreview_proto_msgTypes[14]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ShumeiMixCallbackReq.ProtoReflect.Descriptor instead.
func (*ShumeiMixCallbackReq) Descriptor() ([]byte, []int) {
	return file_svcreview_proto_rawDescGZIP(), []int{14}
}

func (x *ShumeiMixCallbackReq) GetBtId() string {
	if x != nil {
		return x.BtId
	}
	return ""
}

func (x *ShumeiMixCallbackReq) GetCode() int32 {
	if x != nil {
		return x.Code
	}
	return 0
}

func (x *ShumeiMixCallbackReq) GetRequestId() string {
	if x != nil {
		return x.RequestId
	}
	return ""
}

func (x *ShumeiMixCallbackReq) GetRiskLevel() string {
	if x != nil {
		return x.RiskLevel
	}
	return ""
}

func (x *ShumeiMixCallbackReq) GetResultType() int32 {
	if x != nil {
		return x.ResultType
	}
	return 0
}

func (x *ShumeiMixCallbackReq) GetDetails() *ShumeiMixDetail {
	if x != nil {
		return x.Details
	}
	return nil
}

func (x *ShumeiMixCallbackReq) GetPassThrough() *ReviewCallbackData {
	if x != nil {
		return x.PassThrough
	}
	return nil
}

type ShumeiMixDetail struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Texts         []*ShumeiMixTextDetail `protobuf:"bytes,1,rep,name=texts,proto3" json:"texts,omitempty"`
	Images        []*ShumeiMixImgDetail  `protobuf:"bytes,2,rep,name=images,proto3" json:"images,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *ShumeiMixDetail) Reset() {
	*x = ShumeiMixDetail{}
	mi := &file_svcreview_proto_msgTypes[15]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ShumeiMixDetail) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ShumeiMixDetail) ProtoMessage() {}

func (x *ShumeiMixDetail) ProtoReflect() protoreflect.Message {
	mi := &file_svcreview_proto_msgTypes[15]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ShumeiMixDetail.ProtoReflect.Descriptor instead.
func (*ShumeiMixDetail) Descriptor() ([]byte, []int) {
	return file_svcreview_proto_rawDescGZIP(), []int{15}
}

func (x *ShumeiMixDetail) GetTexts() []*ShumeiMixTextDetail {
	if x != nil {
		return x.Texts
	}
	return nil
}

func (x *ShumeiMixDetail) GetImages() []*ShumeiMixImgDetail {
	if x != nil {
		return x.Images
	}
	return nil
}

type ShumeiMixTextDetail struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Code          int32                  `protobuf:"varint,1,opt,name=code,proto3" json:"code,omitempty"`
	Message       string                 `protobuf:"bytes,2,opt,name=message,proto3" json:"message,omitempty"`
	RequestId     string                 `protobuf:"bytes,3,opt,name=requestId,proto3" json:"requestId,omitempty"`
	DataId        string                 `protobuf:"bytes,4,opt,name=dataId,proto3" json:"dataId,omitempty"`
	BtId          string                 `protobuf:"bytes,5,opt,name=btId,proto3" json:"btId,omitempty"`
	RiskLevel     string                 `protobuf:"bytes,6,opt,name=riskLevel,proto3" json:"riskLevel,omitempty"` //PASS：正常 REVIEW：可疑，建议人工审核 REJECT：违规
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *ShumeiMixTextDetail) Reset() {
	*x = ShumeiMixTextDetail{}
	mi := &file_svcreview_proto_msgTypes[16]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ShumeiMixTextDetail) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ShumeiMixTextDetail) ProtoMessage() {}

func (x *ShumeiMixTextDetail) ProtoReflect() protoreflect.Message {
	mi := &file_svcreview_proto_msgTypes[16]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ShumeiMixTextDetail.ProtoReflect.Descriptor instead.
func (*ShumeiMixTextDetail) Descriptor() ([]byte, []int) {
	return file_svcreview_proto_rawDescGZIP(), []int{16}
}

func (x *ShumeiMixTextDetail) GetCode() int32 {
	if x != nil {
		return x.Code
	}
	return 0
}

func (x *ShumeiMixTextDetail) GetMessage() string {
	if x != nil {
		return x.Message
	}
	return ""
}

func (x *ShumeiMixTextDetail) GetRequestId() string {
	if x != nil {
		return x.RequestId
	}
	return ""
}

func (x *ShumeiMixTextDetail) GetDataId() string {
	if x != nil {
		return x.DataId
	}
	return ""
}

func (x *ShumeiMixTextDetail) GetBtId() string {
	if x != nil {
		return x.BtId
	}
	return ""
}

func (x *ShumeiMixTextDetail) GetRiskLevel() string {
	if x != nil {
		return x.RiskLevel
	}
	return ""
}

type ShumeiMixImgDetail struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Code          int32                  `protobuf:"varint,1,opt,name=code,proto3" json:"code,omitempty"`
	Message       string                 `protobuf:"bytes,2,opt,name=message,proto3" json:"message,omitempty"`
	RequestId     string                 `protobuf:"bytes,3,opt,name=requestId,proto3" json:"requestId,omitempty"`
	DataId        string                 `protobuf:"bytes,4,opt,name=dataId,proto3" json:"dataId,omitempty"`
	BtId          string                 `protobuf:"bytes,5,opt,name=btId,proto3" json:"btId,omitempty"`
	RiskLevel     string                 `protobuf:"bytes,6,opt,name=riskLevel,proto3" json:"riskLevel,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *ShumeiMixImgDetail) Reset() {
	*x = ShumeiMixImgDetail{}
	mi := &file_svcreview_proto_msgTypes[17]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ShumeiMixImgDetail) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ShumeiMixImgDetail) ProtoMessage() {}

func (x *ShumeiMixImgDetail) ProtoReflect() protoreflect.Message {
	mi := &file_svcreview_proto_msgTypes[17]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ShumeiMixImgDetail.ProtoReflect.Descriptor instead.
func (*ShumeiMixImgDetail) Descriptor() ([]byte, []int) {
	return file_svcreview_proto_rawDescGZIP(), []int{17}
}

func (x *ShumeiMixImgDetail) GetCode() int32 {
	if x != nil {
		return x.Code
	}
	return 0
}

func (x *ShumeiMixImgDetail) GetMessage() string {
	if x != nil {
		return x.Message
	}
	return ""
}

func (x *ShumeiMixImgDetail) GetRequestId() string {
	if x != nil {
		return x.RequestId
	}
	return ""
}

func (x *ShumeiMixImgDetail) GetDataId() string {
	if x != nil {
		return x.DataId
	}
	return ""
}

func (x *ShumeiMixImgDetail) GetBtId() string {
	if x != nil {
		return x.BtId
	}
	return ""
}

func (x *ShumeiMixImgDetail) GetRiskLevel() string {
	if x != nil {
		return x.RiskLevel
	}
	return ""
}

type ReviewCallbackData struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Id            int64                  `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	BizType       string                 `protobuf:"bytes,2,opt,name=biz_type,json=bizType,proto3" json:"biz_type,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *ReviewCallbackData) Reset() {
	*x = ReviewCallbackData{}
	mi := &file_svcreview_proto_msgTypes[18]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ReviewCallbackData) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ReviewCallbackData) ProtoMessage() {}

func (x *ReviewCallbackData) ProtoReflect() protoreflect.Message {
	mi := &file_svcreview_proto_msgTypes[18]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ReviewCallbackData.ProtoReflect.Descriptor instead.
func (*ReviewCallbackData) Descriptor() ([]byte, []int) {
	return file_svcreview_proto_rawDescGZIP(), []int{18}
}

func (x *ReviewCallbackData) GetId() int64 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *ReviewCallbackData) GetBizType() string {
	if x != nil {
		return x.BizType
	}
	return ""
}

type ShumeiMultiImageCallbackReq struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	BtId          string                 `protobuf:"bytes,1,opt,name=btId,proto3" json:"btId,omitempty"`
	Code          int32                  `protobuf:"varint,2,opt,name=code,proto3" json:"code,omitempty"`
	RequestId     string                 `protobuf:"bytes,3,opt,name=requestId,proto3" json:"requestId,omitempty"`
	Imgs          []*ShumeiMultiImage    `protobuf:"bytes,6,rep,name=imgs,proto3" json:"imgs,omitempty"`
	AuxInfo       *ShumeiMultiImageExtra `protobuf:"bytes,7,opt,name=auxInfo,proto3" json:"auxInfo,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *ShumeiMultiImageCallbackReq) Reset() {
	*x = ShumeiMultiImageCallbackReq{}
	mi := &file_svcreview_proto_msgTypes[19]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ShumeiMultiImageCallbackReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ShumeiMultiImageCallbackReq) ProtoMessage() {}

func (x *ShumeiMultiImageCallbackReq) ProtoReflect() protoreflect.Message {
	mi := &file_svcreview_proto_msgTypes[19]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ShumeiMultiImageCallbackReq.ProtoReflect.Descriptor instead.
func (*ShumeiMultiImageCallbackReq) Descriptor() ([]byte, []int) {
	return file_svcreview_proto_rawDescGZIP(), []int{19}
}

func (x *ShumeiMultiImageCallbackReq) GetBtId() string {
	if x != nil {
		return x.BtId
	}
	return ""
}

func (x *ShumeiMultiImageCallbackReq) GetCode() int32 {
	if x != nil {
		return x.Code
	}
	return 0
}

func (x *ShumeiMultiImageCallbackReq) GetRequestId() string {
	if x != nil {
		return x.RequestId
	}
	return ""
}

func (x *ShumeiMultiImageCallbackReq) GetImgs() []*ShumeiMultiImage {
	if x != nil {
		return x.Imgs
	}
	return nil
}

func (x *ShumeiMultiImageCallbackReq) GetAuxInfo() *ShumeiMultiImageExtra {
	if x != nil {
		return x.AuxInfo
	}
	return nil
}

type ShumeiMultiImage struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	BtId          string                 `protobuf:"bytes,1,opt,name=btId,proto3" json:"btId,omitempty"`
	RiskLevel     string                 `protobuf:"bytes,2,opt,name=riskLevel,proto3" json:"riskLevel,omitempty"`
	Code          int32                  `protobuf:"varint,3,opt,name=code,proto3" json:"code,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *ShumeiMultiImage) Reset() {
	*x = ShumeiMultiImage{}
	mi := &file_svcreview_proto_msgTypes[20]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ShumeiMultiImage) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ShumeiMultiImage) ProtoMessage() {}

func (x *ShumeiMultiImage) ProtoReflect() protoreflect.Message {
	mi := &file_svcreview_proto_msgTypes[20]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ShumeiMultiImage.ProtoReflect.Descriptor instead.
func (*ShumeiMultiImage) Descriptor() ([]byte, []int) {
	return file_svcreview_proto_rawDescGZIP(), []int{20}
}

func (x *ShumeiMultiImage) GetBtId() string {
	if x != nil {
		return x.BtId
	}
	return ""
}

func (x *ShumeiMultiImage) GetRiskLevel() string {
	if x != nil {
		return x.RiskLevel
	}
	return ""
}

func (x *ShumeiMultiImage) GetCode() int32 {
	if x != nil {
		return x.Code
	}
	return 0
}

type ShumeiMultiImageExtra struct {
	state         protoimpl.MessageState        `protogen:"open.v1"`
	PassThrough   *ReviewMultiImageCallbackData `protobuf:"bytes,1,opt,name=passThrough,proto3" json:"passThrough,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *ShumeiMultiImageExtra) Reset() {
	*x = ShumeiMultiImageExtra{}
	mi := &file_svcreview_proto_msgTypes[21]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ShumeiMultiImageExtra) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ShumeiMultiImageExtra) ProtoMessage() {}

func (x *ShumeiMultiImageExtra) ProtoReflect() protoreflect.Message {
	mi := &file_svcreview_proto_msgTypes[21]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ShumeiMultiImageExtra.ProtoReflect.Descriptor instead.
func (*ShumeiMultiImageExtra) Descriptor() ([]byte, []int) {
	return file_svcreview_proto_rawDescGZIP(), []int{21}
}

func (x *ShumeiMultiImageExtra) GetPassThrough() *ReviewMultiImageCallbackData {
	if x != nil {
		return x.PassThrough
	}
	return nil
}

type ReviewMultiImageCallbackData struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Id            int64                  `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	BizType       string                 `protobuf:"bytes,2,opt,name=bizType,proto3" json:"bizType,omitempty"`
	Userid        int64                  `protobuf:"varint,3,opt,name=userid,proto3" json:"userid,omitempty"`
	Sex           int32                  `protobuf:"varint,4,opt,name=sex,proto3" json:"sex,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *ReviewMultiImageCallbackData) Reset() {
	*x = ReviewMultiImageCallbackData{}
	mi := &file_svcreview_proto_msgTypes[22]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ReviewMultiImageCallbackData) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ReviewMultiImageCallbackData) ProtoMessage() {}

func (x *ReviewMultiImageCallbackData) ProtoReflect() protoreflect.Message {
	mi := &file_svcreview_proto_msgTypes[22]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ReviewMultiImageCallbackData.ProtoReflect.Descriptor instead.
func (*ReviewMultiImageCallbackData) Descriptor() ([]byte, []int) {
	return file_svcreview_proto_rawDescGZIP(), []int{22}
}

func (x *ReviewMultiImageCallbackData) GetId() int64 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *ReviewMultiImageCallbackData) GetBizType() string {
	if x != nil {
		return x.BizType
	}
	return ""
}

func (x *ReviewMultiImageCallbackData) GetUserid() int64 {
	if x != nil {
		return x.Userid
	}
	return 0
}

func (x *ReviewMultiImageCallbackData) GetSex() int32 {
	if x != nil {
		return x.Sex
	}
	return 0
}

type ReviewMemberInfoCallbackData struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Userid        int64                  `protobuf:"varint,1,opt,name=userid,proto3" json:"userid,omitempty"`
	Id            int64                  `protobuf:"varint,2,opt,name=id,proto3" json:"id,omitempty"`
	Type          ReviewBizType          `protobuf:"varint,3,opt,name=type,proto3,enum=vc.svcreview.ReviewBizType" json:"type,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *ReviewMemberInfoCallbackData) Reset() {
	*x = ReviewMemberInfoCallbackData{}
	mi := &file_svcreview_proto_msgTypes[23]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ReviewMemberInfoCallbackData) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ReviewMemberInfoCallbackData) ProtoMessage() {}

func (x *ReviewMemberInfoCallbackData) ProtoReflect() protoreflect.Message {
	mi := &file_svcreview_proto_msgTypes[23]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ReviewMemberInfoCallbackData.ProtoReflect.Descriptor instead.
func (*ReviewMemberInfoCallbackData) Descriptor() ([]byte, []int) {
	return file_svcreview_proto_rawDescGZIP(), []int{23}
}

func (x *ReviewMemberInfoCallbackData) GetUserid() int64 {
	if x != nil {
		return x.Userid
	}
	return 0
}

func (x *ReviewMemberInfoCallbackData) GetId() int64 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *ReviewMemberInfoCallbackData) GetType() ReviewBizType {
	if x != nil {
		return x.Type
	}
	return ReviewBizType_unknown
}

type ReviewMemberInfoReq struct {
	state        protoimpl.MessageState        `protogen:"open.v1"`
	Userid       int64                         `protobuf:"varint,1,opt,name=userid,proto3" json:"userid,omitempty"`
	Sex          int32                         `protobuf:"varint,2,opt,name=sex,proto3" json:"sex,omitempty"` //女用户头像需是真人头像
	BizType      ReviewBizType                 `protobuf:"varint,3,opt,name=bizType,proto3,enum=vc.svcreview.ReviewBizType" json:"bizType,omitempty"`
	Text         []string                      `protobuf:"bytes,4,rep,name=text,proto3" json:"text,omitempty"`
	Images       []*ReviewImage                `protobuf:"bytes,5,rep,name=images,proto3" json:"images,omitempty"`
	Voice        *ReviewVoice                  `protobuf:"bytes,6,opt,name=voice,proto3" json:"voice,omitempty"`
	CallbackData *ReviewMemberInfoCallbackData `protobuf:"bytes,7,opt,name=callback_data,json=callbackData,proto3" json:"callback_data,omitempty"`
	Xluid        string                        `protobuf:"bytes,8,opt,name=xluid,proto3" json:"xluid,omitempty"` // 注册的头像和昵称审核人审回调使用
	// 国家
	Country       string `protobuf:"bytes,9,opt,name=country,proto3" json:"country,omitempty"`
	Lang          string `protobuf:"bytes,10,opt,name=lang,proto3" json:"lang,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *ReviewMemberInfoReq) Reset() {
	*x = ReviewMemberInfoReq{}
	mi := &file_svcreview_proto_msgTypes[24]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ReviewMemberInfoReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ReviewMemberInfoReq) ProtoMessage() {}

func (x *ReviewMemberInfoReq) ProtoReflect() protoreflect.Message {
	mi := &file_svcreview_proto_msgTypes[24]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ReviewMemberInfoReq.ProtoReflect.Descriptor instead.
func (*ReviewMemberInfoReq) Descriptor() ([]byte, []int) {
	return file_svcreview_proto_rawDescGZIP(), []int{24}
}

func (x *ReviewMemberInfoReq) GetUserid() int64 {
	if x != nil {
		return x.Userid
	}
	return 0
}

func (x *ReviewMemberInfoReq) GetSex() int32 {
	if x != nil {
		return x.Sex
	}
	return 0
}

func (x *ReviewMemberInfoReq) GetBizType() ReviewBizType {
	if x != nil {
		return x.BizType
	}
	return ReviewBizType_unknown
}

func (x *ReviewMemberInfoReq) GetText() []string {
	if x != nil {
		return x.Text
	}
	return nil
}

func (x *ReviewMemberInfoReq) GetImages() []*ReviewImage {
	if x != nil {
		return x.Images
	}
	return nil
}

func (x *ReviewMemberInfoReq) GetVoice() *ReviewVoice {
	if x != nil {
		return x.Voice
	}
	return nil
}

func (x *ReviewMemberInfoReq) GetCallbackData() *ReviewMemberInfoCallbackData {
	if x != nil {
		return x.CallbackData
	}
	return nil
}

func (x *ReviewMemberInfoReq) GetXluid() string {
	if x != nil {
		return x.Xluid
	}
	return ""
}

func (x *ReviewMemberInfoReq) GetCountry() string {
	if x != nil {
		return x.Country
	}
	return ""
}

func (x *ReviewMemberInfoReq) GetLang() string {
	if x != nil {
		return x.Lang
	}
	return ""
}

type ReviewMomentReq struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Userid        int64                  `protobuf:"varint,1,opt,name=userid,proto3" json:"userid,omitempty"`
	Moid          int64                  `protobuf:"varint,2,opt,name=moid,proto3" json:"moid,omitempty"`
	Text          string                 `protobuf:"bytes,3,opt,name=text,proto3" json:"text,omitempty"`
	Images        []string               `protobuf:"bytes,4,rep,name=images,proto3" json:"images,omitempty"`
	Voice         *ReviewVoice           `protobuf:"bytes,5,opt,name=voice,proto3" json:"voice,omitempty"`
	Video         *ReviewVideo           `protobuf:"bytes,6,opt,name=video,proto3" json:"video,omitempty"`
	CallbackData  *ReviewCallbackData    `protobuf:"bytes,7,opt,name=callback_data,json=callbackData,proto3" json:"callback_data,omitempty"`
	Country       string                 `protobuf:"bytes,8,opt,name=country,proto3" json:"country,omitempty"`
	Lang          string                 `protobuf:"bytes,9,opt,name=lang,proto3" json:"lang,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *ReviewMomentReq) Reset() {
	*x = ReviewMomentReq{}
	mi := &file_svcreview_proto_msgTypes[25]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ReviewMomentReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ReviewMomentReq) ProtoMessage() {}

func (x *ReviewMomentReq) ProtoReflect() protoreflect.Message {
	mi := &file_svcreview_proto_msgTypes[25]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ReviewMomentReq.ProtoReflect.Descriptor instead.
func (*ReviewMomentReq) Descriptor() ([]byte, []int) {
	return file_svcreview_proto_rawDescGZIP(), []int{25}
}

func (x *ReviewMomentReq) GetUserid() int64 {
	if x != nil {
		return x.Userid
	}
	return 0
}

func (x *ReviewMomentReq) GetMoid() int64 {
	if x != nil {
		return x.Moid
	}
	return 0
}

func (x *ReviewMomentReq) GetText() string {
	if x != nil {
		return x.Text
	}
	return ""
}

func (x *ReviewMomentReq) GetImages() []string {
	if x != nil {
		return x.Images
	}
	return nil
}

func (x *ReviewMomentReq) GetVoice() *ReviewVoice {
	if x != nil {
		return x.Voice
	}
	return nil
}

func (x *ReviewMomentReq) GetVideo() *ReviewVideo {
	if x != nil {
		return x.Video
	}
	return nil
}

func (x *ReviewMomentReq) GetCallbackData() *ReviewCallbackData {
	if x != nil {
		return x.CallbackData
	}
	return nil
}

func (x *ReviewMomentReq) GetCountry() string {
	if x != nil {
		return x.Country
	}
	return ""
}

func (x *ReviewMomentReq) GetLang() string {
	if x != nil {
		return x.Lang
	}
	return ""
}

type ReviewUserCommonMsgReq struct {
	state         protoimpl.MessageState           `protogen:"open.v1"`
	Userid        int64                            `protobuf:"varint,1,opt,name=userid,proto3" json:"userid,omitempty"`
	Id            int64                            `protobuf:"varint,2,opt,name=id,proto3" json:"id,omitempty"`
	Text          string                           `protobuf:"bytes,3,opt,name=text,proto3" json:"text,omitempty"`
	CallbackData  *ReviewUserCommonMsgCallbackData `protobuf:"bytes,4,opt,name=callback_data,json=callbackData,proto3" json:"callback_data,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *ReviewUserCommonMsgReq) Reset() {
	*x = ReviewUserCommonMsgReq{}
	mi := &file_svcreview_proto_msgTypes[26]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ReviewUserCommonMsgReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ReviewUserCommonMsgReq) ProtoMessage() {}

func (x *ReviewUserCommonMsgReq) ProtoReflect() protoreflect.Message {
	mi := &file_svcreview_proto_msgTypes[26]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ReviewUserCommonMsgReq.ProtoReflect.Descriptor instead.
func (*ReviewUserCommonMsgReq) Descriptor() ([]byte, []int) {
	return file_svcreview_proto_rawDescGZIP(), []int{26}
}

func (x *ReviewUserCommonMsgReq) GetUserid() int64 {
	if x != nil {
		return x.Userid
	}
	return 0
}

func (x *ReviewUserCommonMsgReq) GetId() int64 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *ReviewUserCommonMsgReq) GetText() string {
	if x != nil {
		return x.Text
	}
	return ""
}

func (x *ReviewUserCommonMsgReq) GetCallbackData() *ReviewUserCommonMsgCallbackData {
	if x != nil {
		return x.CallbackData
	}
	return nil
}

type ReviewUserCommonMsgCallbackData struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Userid        int64                  `protobuf:"varint,1,opt,name=userid,proto3" json:"userid,omitempty"`
	Id            int64                  `protobuf:"varint,2,opt,name=id,proto3" json:"id,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *ReviewUserCommonMsgCallbackData) Reset() {
	*x = ReviewUserCommonMsgCallbackData{}
	mi := &file_svcreview_proto_msgTypes[27]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ReviewUserCommonMsgCallbackData) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ReviewUserCommonMsgCallbackData) ProtoMessage() {}

func (x *ReviewUserCommonMsgCallbackData) ProtoReflect() protoreflect.Message {
	mi := &file_svcreview_proto_msgTypes[27]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ReviewUserCommonMsgCallbackData.ProtoReflect.Descriptor instead.
func (*ReviewUserCommonMsgCallbackData) Descriptor() ([]byte, []int) {
	return file_svcreview_proto_rawDescGZIP(), []int{27}
}

func (x *ReviewUserCommonMsgCallbackData) GetUserid() int64 {
	if x != nil {
		return x.Userid
	}
	return 0
}

func (x *ReviewUserCommonMsgCallbackData) GetId() int64 {
	if x != nil {
		return x.Id
	}
	return 0
}

type ReviewCallbackReq struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	AppId         string                 `protobuf:"bytes,1,opt,name=appId,proto3" json:"appId,omitempty"`
	BizType       string                 `protobuf:"bytes,2,opt,name=bizType,proto3" json:"bizType,omitempty"`
	Auditor       string                 `protobuf:"bytes,3,opt,name=auditor,proto3" json:"auditor,omitempty"`
	CaseId        string                 `protobuf:"bytes,4,opt,name=caseId,proto3" json:"caseId,omitempty"`
	ContentType   string                 `protobuf:"bytes,5,opt,name=contentType,proto3" json:"contentType,omitempty"`
	DataId        string                 `protobuf:"bytes,6,opt,name=dataId,proto3" json:"dataId,omitempty"`
	Suggestion    string                 `protobuf:"bytes,7,opt,name=suggestion,proto3" json:"suggestion,omitempty"`                  //人审建议值. pass: 通过, reject: 拒绝.
	Label         string                 `protobuf:"bytes,8,opt,name=label,proto3" json:"label,omitempty"`                            //人审(或机审)标签.
	LabelKey      string                 `protobuf:"bytes,9,opt,name=label_key,json=labelKey,proto3" json:"label_key,omitempty"`      //人审(或机审)标签 key.
	LabelCode     int32                  `protobuf:"varint,10,opt,name=label_code,json=labelCode,proto3" json:"label_code,omitempty"` //人审(或机审)标签 code.
	Origin        *ReviewCallbackOrigin  `protobuf:"bytes,11,opt,name=origin,proto3" json:"origin,omitempty"`                         //scan接口请求时的origin信息
	Secret        string                 `protobuf:"bytes,12,opt,name=secret,proto3" json:"secret,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *ReviewCallbackReq) Reset() {
	*x = ReviewCallbackReq{}
	mi := &file_svcreview_proto_msgTypes[28]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ReviewCallbackReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ReviewCallbackReq) ProtoMessage() {}

func (x *ReviewCallbackReq) ProtoReflect() protoreflect.Message {
	mi := &file_svcreview_proto_msgTypes[28]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ReviewCallbackReq.ProtoReflect.Descriptor instead.
func (*ReviewCallbackReq) Descriptor() ([]byte, []int) {
	return file_svcreview_proto_rawDescGZIP(), []int{28}
}

func (x *ReviewCallbackReq) GetAppId() string {
	if x != nil {
		return x.AppId
	}
	return ""
}

func (x *ReviewCallbackReq) GetBizType() string {
	if x != nil {
		return x.BizType
	}
	return ""
}

func (x *ReviewCallbackReq) GetAuditor() string {
	if x != nil {
		return x.Auditor
	}
	return ""
}

func (x *ReviewCallbackReq) GetCaseId() string {
	if x != nil {
		return x.CaseId
	}
	return ""
}

func (x *ReviewCallbackReq) GetContentType() string {
	if x != nil {
		return x.ContentType
	}
	return ""
}

func (x *ReviewCallbackReq) GetDataId() string {
	if x != nil {
		return x.DataId
	}
	return ""
}

func (x *ReviewCallbackReq) GetSuggestion() string {
	if x != nil {
		return x.Suggestion
	}
	return ""
}

func (x *ReviewCallbackReq) GetLabel() string {
	if x != nil {
		return x.Label
	}
	return ""
}

func (x *ReviewCallbackReq) GetLabelKey() string {
	if x != nil {
		return x.LabelKey
	}
	return ""
}

func (x *ReviewCallbackReq) GetLabelCode() int32 {
	if x != nil {
		return x.LabelCode
	}
	return 0
}

func (x *ReviewCallbackReq) GetOrigin() *ReviewCallbackOrigin {
	if x != nil {
		return x.Origin
	}
	return nil
}

func (x *ReviewCallbackReq) GetSecret() string {
	if x != nil {
		return x.Secret
	}
	return ""
}

type ReviewRoomInfoCallbackData struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Roomid        int64                  `protobuf:"varint,1,opt,name=roomid,proto3" json:"roomid,omitempty"`
	Id            int64                  `protobuf:"varint,2,opt,name=id,proto3" json:"id,omitempty"`
	Type          int32                  `protobuf:"varint,3,opt,name=type,proto3" json:"type,omitempty"` //类型 1 房间封面 2 房间标题 3 房间公告 4房间背景
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *ReviewRoomInfoCallbackData) Reset() {
	*x = ReviewRoomInfoCallbackData{}
	mi := &file_svcreview_proto_msgTypes[29]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ReviewRoomInfoCallbackData) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ReviewRoomInfoCallbackData) ProtoMessage() {}

func (x *ReviewRoomInfoCallbackData) ProtoReflect() protoreflect.Message {
	mi := &file_svcreview_proto_msgTypes[29]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ReviewRoomInfoCallbackData.ProtoReflect.Descriptor instead.
func (*ReviewRoomInfoCallbackData) Descriptor() ([]byte, []int) {
	return file_svcreview_proto_rawDescGZIP(), []int{29}
}

func (x *ReviewRoomInfoCallbackData) GetRoomid() int64 {
	if x != nil {
		return x.Roomid
	}
	return 0
}

func (x *ReviewRoomInfoCallbackData) GetId() int64 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *ReviewRoomInfoCallbackData) GetType() int32 {
	if x != nil {
		return x.Type
	}
	return 0
}

type ReviewRoomInfoReq struct {
	state        protoimpl.MessageState      `protogen:"open.v1"`
	Roomid       int64                       `protobuf:"varint,1,opt,name=roomid,proto3" json:"roomid,omitempty"`
	Userid       int64                       `protobuf:"varint,2,opt,name=userid,proto3" json:"userid,omitempty"`
	BizType      ReviewBizType               `protobuf:"varint,3,opt,name=bizType,proto3,enum=vc.svcreview.ReviewBizType" json:"bizType,omitempty"`
	Text         string                      `protobuf:"bytes,4,opt,name=text,proto3" json:"text,omitempty"`
	Image        *ReviewImage                `protobuf:"bytes,5,opt,name=image,proto3" json:"image,omitempty"`
	CallbackData *ReviewRoomInfoCallbackData `protobuf:"bytes,7,opt,name=callback_data,json=callbackData,proto3" json:"callback_data,omitempty"`
	// 国家
	Country       string `protobuf:"bytes,8,opt,name=country,proto3" json:"country,omitempty"`
	Lang          string `protobuf:"bytes,9,opt,name=lang,proto3" json:"lang,omitempty"` //  basemsgtransfer.SessionType session_type= 10;
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *ReviewRoomInfoReq) Reset() {
	*x = ReviewRoomInfoReq{}
	mi := &file_svcreview_proto_msgTypes[30]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ReviewRoomInfoReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ReviewRoomInfoReq) ProtoMessage() {}

func (x *ReviewRoomInfoReq) ProtoReflect() protoreflect.Message {
	mi := &file_svcreview_proto_msgTypes[30]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ReviewRoomInfoReq.ProtoReflect.Descriptor instead.
func (*ReviewRoomInfoReq) Descriptor() ([]byte, []int) {
	return file_svcreview_proto_rawDescGZIP(), []int{30}
}

func (x *ReviewRoomInfoReq) GetRoomid() int64 {
	if x != nil {
		return x.Roomid
	}
	return 0
}

func (x *ReviewRoomInfoReq) GetUserid() int64 {
	if x != nil {
		return x.Userid
	}
	return 0
}

func (x *ReviewRoomInfoReq) GetBizType() ReviewBizType {
	if x != nil {
		return x.BizType
	}
	return ReviewBizType_unknown
}

func (x *ReviewRoomInfoReq) GetText() string {
	if x != nil {
		return x.Text
	}
	return ""
}

func (x *ReviewRoomInfoReq) GetImage() *ReviewImage {
	if x != nil {
		return x.Image
	}
	return nil
}

func (x *ReviewRoomInfoReq) GetCallbackData() *ReviewRoomInfoCallbackData {
	if x != nil {
		return x.CallbackData
	}
	return nil
}

func (x *ReviewRoomInfoReq) GetCountry() string {
	if x != nil {
		return x.Country
	}
	return ""
}

func (x *ReviewRoomInfoReq) GetLang() string {
	if x != nil {
		return x.Lang
	}
	return ""
}

type ReviewRoomInfoResp struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Base          *common.SvcBaseResp    `protobuf:"bytes,1,opt,name=base,proto3" json:"base,omitempty"`
	Result        AuditResult            `protobuf:"varint,2,opt,name=result,proto3,enum=vc.svcreview.AuditResult" json:"result,omitempty"`
	Reason        string                 `protobuf:"bytes,3,opt,name=reason,proto3" json:"reason,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *ReviewRoomInfoResp) Reset() {
	*x = ReviewRoomInfoResp{}
	mi := &file_svcreview_proto_msgTypes[31]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ReviewRoomInfoResp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ReviewRoomInfoResp) ProtoMessage() {}

func (x *ReviewRoomInfoResp) ProtoReflect() protoreflect.Message {
	mi := &file_svcreview_proto_msgTypes[31]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ReviewRoomInfoResp.ProtoReflect.Descriptor instead.
func (*ReviewRoomInfoResp) Descriptor() ([]byte, []int) {
	return file_svcreview_proto_rawDescGZIP(), []int{31}
}

func (x *ReviewRoomInfoResp) GetBase() *common.SvcBaseResp {
	if x != nil {
		return x.Base
	}
	return nil
}

func (x *ReviewRoomInfoResp) GetResult() AuditResult {
	if x != nil {
		return x.Result
	}
	return AuditResult_audit_result_unknown
}

func (x *ReviewRoomInfoResp) GetReason() string {
	if x != nil {
		return x.Reason
	}
	return ""
}

type ReviewRoomChatReq struct {
	state  protoimpl.MessageState `protogen:"open.v1"`
	Userid int64                  `protobuf:"varint,1,opt,name=userid,proto3" json:"userid,omitempty"`
	Roomid int64                  `protobuf:"varint,2,opt,name=roomid,proto3" json:"roomid,omitempty"`
	Sid    int64                  `protobuf:"varint,3,opt,name=sid,proto3" json:"sid,omitempty"`
	// basemsgtransfer.ContentType msg_data_type = 4;
	SubType int32  `protobuf:"varint,5,opt,name=sub_type,json=subType,proto3" json:"sub_type,omitempty"`
	Content string `protobuf:"bytes,6,opt,name=content,proto3" json:"content,omitempty"`
	Ip      string `protobuf:"bytes,7,opt,name=ip,proto3" json:"ip,omitempty"`
	// 国家
	Country       string            `protobuf:"bytes,8,opt,name=country,proto3" json:"country,omitempty"`
	Lang          string            `protobuf:"bytes,9,opt,name=lang,proto3" json:"lang,omitempty"`
	Base          *common.BaseParam `protobuf:"bytes,10,opt,name=base,proto3" json:"base,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *ReviewRoomChatReq) Reset() {
	*x = ReviewRoomChatReq{}
	mi := &file_svcreview_proto_msgTypes[32]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ReviewRoomChatReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ReviewRoomChatReq) ProtoMessage() {}

func (x *ReviewRoomChatReq) ProtoReflect() protoreflect.Message {
	mi := &file_svcreview_proto_msgTypes[32]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ReviewRoomChatReq.ProtoReflect.Descriptor instead.
func (*ReviewRoomChatReq) Descriptor() ([]byte, []int) {
	return file_svcreview_proto_rawDescGZIP(), []int{32}
}

func (x *ReviewRoomChatReq) GetUserid() int64 {
	if x != nil {
		return x.Userid
	}
	return 0
}

func (x *ReviewRoomChatReq) GetRoomid() int64 {
	if x != nil {
		return x.Roomid
	}
	return 0
}

func (x *ReviewRoomChatReq) GetSid() int64 {
	if x != nil {
		return x.Sid
	}
	return 0
}

func (x *ReviewRoomChatReq) GetSubType() int32 {
	if x != nil {
		return x.SubType
	}
	return 0
}

func (x *ReviewRoomChatReq) GetContent() string {
	if x != nil {
		return x.Content
	}
	return ""
}

func (x *ReviewRoomChatReq) GetIp() string {
	if x != nil {
		return x.Ip
	}
	return ""
}

func (x *ReviewRoomChatReq) GetCountry() string {
	if x != nil {
		return x.Country
	}
	return ""
}

func (x *ReviewRoomChatReq) GetLang() string {
	if x != nil {
		return x.Lang
	}
	return ""
}

func (x *ReviewRoomChatReq) GetBase() *common.BaseParam {
	if x != nil {
		return x.Base
	}
	return nil
}

type ReviewRoomChatResp struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Base          *common.SvcBaseResp    `protobuf:"bytes,1,opt,name=base,proto3" json:"base,omitempty"`
	Result        AuditResult            `protobuf:"varint,2,opt,name=result,proto3,enum=vc.svcreview.AuditResult" json:"result,omitempty"`
	Reason        string                 `protobuf:"bytes,3,opt,name=reason,proto3" json:"reason,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *ReviewRoomChatResp) Reset() {
	*x = ReviewRoomChatResp{}
	mi := &file_svcreview_proto_msgTypes[33]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ReviewRoomChatResp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ReviewRoomChatResp) ProtoMessage() {}

func (x *ReviewRoomChatResp) ProtoReflect() protoreflect.Message {
	mi := &file_svcreview_proto_msgTypes[33]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ReviewRoomChatResp.ProtoReflect.Descriptor instead.
func (*ReviewRoomChatResp) Descriptor() ([]byte, []int) {
	return file_svcreview_proto_rawDescGZIP(), []int{33}
}

func (x *ReviewRoomChatResp) GetBase() *common.SvcBaseResp {
	if x != nil {
		return x.Base
	}
	return nil
}

func (x *ReviewRoomChatResp) GetResult() AuditResult {
	if x != nil {
		return x.Result
	}
	return AuditResult_audit_result_unknown
}

func (x *ReviewRoomChatResp) GetReason() string {
	if x != nil {
		return x.Reason
	}
	return ""
}

type ReviewChatCallbackData struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Sid           string                 `protobuf:"bytes,1,opt,name=sid,proto3" json:"sid,omitempty"`
	Msgid         string                 `protobuf:"bytes,2,opt,name=msgid,proto3" json:"msgid,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *ReviewChatCallbackData) Reset() {
	*x = ReviewChatCallbackData{}
	mi := &file_svcreview_proto_msgTypes[34]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ReviewChatCallbackData) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ReviewChatCallbackData) ProtoMessage() {}

func (x *ReviewChatCallbackData) ProtoReflect() protoreflect.Message {
	mi := &file_svcreview_proto_msgTypes[34]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ReviewChatCallbackData.ProtoReflect.Descriptor instead.
func (*ReviewChatCallbackData) Descriptor() ([]byte, []int) {
	return file_svcreview_proto_rawDescGZIP(), []int{34}
}

func (x *ReviewChatCallbackData) GetSid() string {
	if x != nil {
		return x.Sid
	}
	return ""
}

func (x *ReviewChatCallbackData) GetMsgid() string {
	if x != nil {
		return x.Msgid
	}
	return ""
}

type ReviewChatReq struct {
	state  protoimpl.MessageState `protogen:"open.v1"`
	Userid int64                  `protobuf:"varint,1,opt,name=userid,proto3" json:"userid,omitempty"`
	Sid    string                 `protobuf:"bytes,2,opt,name=sid,proto3" json:"sid,omitempty"`
	MsgId  string                 `protobuf:"bytes,3,opt,name=msgId,proto3" json:"msgId,omitempty"`
	// basemsgtransfer.ContentType msgType = 4; // 1 2 3 4 5
	Text         string                  `protobuf:"bytes,5,opt,name=text,proto3" json:"text,omitempty"`
	Image        *ReviewImage            `protobuf:"bytes,6,opt,name=image,proto3" json:"image,omitempty"`
	Voice        *ReviewVoice            `protobuf:"bytes,7,opt,name=voice,proto3" json:"voice,omitempty"`
	Video        *ReviewVideo            `protobuf:"bytes,8,opt,name=video,proto3" json:"video,omitempty"`
	CallbackData *ReviewChatCallbackData `protobuf:"bytes,9,opt,name=callback_data,json=callbackData,proto3" json:"callback_data,omitempty"`
	Sex          int32                   `protobuf:"varint,10,opt,name=sex,proto3" json:"sex,omitempty"`
	SweetRate    int32                   `protobuf:"varint,11,opt,name=sweetRate,proto3" json:"sweetRate,omitempty"`
	// oss etag
	ETag    string `protobuf:"bytes,12,opt,name=eTag,proto3" json:"eTag,omitempty"`
	PeerId  int64  `protobuf:"varint,13,opt,name=peer_id,json=peerId,proto3" json:"peer_id,omitempty"`
	Ip      string `protobuf:"bytes,14,opt,name=ip,proto3" json:"ip,omitempty"`
	Country string `protobuf:"bytes,15,opt,name=country,proto3" json:"country,omitempty"`
	Lang    string `protobuf:"bytes,16,opt,name=lang,proto3" json:"lang,omitempty"`
	// 关系对会话消息
	Count         int64             `protobuf:"varint,17,opt,name=count,proto3" json:"count,omitempty"`
	Base          *common.BaseParam `protobuf:"bytes,18,opt,name=base,proto3" json:"base,omitempty"` //  basemsgtransfer.SessionType sessionType = 19;
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *ReviewChatReq) Reset() {
	*x = ReviewChatReq{}
	mi := &file_svcreview_proto_msgTypes[35]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ReviewChatReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ReviewChatReq) ProtoMessage() {}

func (x *ReviewChatReq) ProtoReflect() protoreflect.Message {
	mi := &file_svcreview_proto_msgTypes[35]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ReviewChatReq.ProtoReflect.Descriptor instead.
func (*ReviewChatReq) Descriptor() ([]byte, []int) {
	return file_svcreview_proto_rawDescGZIP(), []int{35}
}

func (x *ReviewChatReq) GetUserid() int64 {
	if x != nil {
		return x.Userid
	}
	return 0
}

func (x *ReviewChatReq) GetSid() string {
	if x != nil {
		return x.Sid
	}
	return ""
}

func (x *ReviewChatReq) GetMsgId() string {
	if x != nil {
		return x.MsgId
	}
	return ""
}

func (x *ReviewChatReq) GetText() string {
	if x != nil {
		return x.Text
	}
	return ""
}

func (x *ReviewChatReq) GetImage() *ReviewImage {
	if x != nil {
		return x.Image
	}
	return nil
}

func (x *ReviewChatReq) GetVoice() *ReviewVoice {
	if x != nil {
		return x.Voice
	}
	return nil
}

func (x *ReviewChatReq) GetVideo() *ReviewVideo {
	if x != nil {
		return x.Video
	}
	return nil
}

func (x *ReviewChatReq) GetCallbackData() *ReviewChatCallbackData {
	if x != nil {
		return x.CallbackData
	}
	return nil
}

func (x *ReviewChatReq) GetSex() int32 {
	if x != nil {
		return x.Sex
	}
	return 0
}

func (x *ReviewChatReq) GetSweetRate() int32 {
	if x != nil {
		return x.SweetRate
	}
	return 0
}

func (x *ReviewChatReq) GetETag() string {
	if x != nil {
		return x.ETag
	}
	return ""
}

func (x *ReviewChatReq) GetPeerId() int64 {
	if x != nil {
		return x.PeerId
	}
	return 0
}

func (x *ReviewChatReq) GetIp() string {
	if x != nil {
		return x.Ip
	}
	return ""
}

func (x *ReviewChatReq) GetCountry() string {
	if x != nil {
		return x.Country
	}
	return ""
}

func (x *ReviewChatReq) GetLang() string {
	if x != nil {
		return x.Lang
	}
	return ""
}

func (x *ReviewChatReq) GetCount() int64 {
	if x != nil {
		return x.Count
	}
	return 0
}

func (x *ReviewChatReq) GetBase() *common.BaseParam {
	if x != nil {
		return x.Base
	}
	return nil
}

type ReviewChatResp struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Base          *common.SvcBaseResp    `protobuf:"bytes,1,opt,name=base,proto3" json:"base,omitempty"`
	Sync          int32                  `protobuf:"varint,2,opt,name=sync,proto3" json:"sync,omitempty"` // 0 异步 1同步返回结果 (文字和图片使用)
	Result        AuditResult            `protobuf:"varint,3,opt,name=result,proto3,enum=vc.svcreview.AuditResult" json:"result,omitempty"`
	Reason        string                 `protobuf:"bytes,4,opt,name=reason,proto3" json:"reason,omitempty"`
	SubResult     AuditSubResult         `protobuf:"varint,5,opt,name=sub_result,json=subResult,proto3,enum=vc.svcreview.AuditSubResult" json:"sub_result,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *ReviewChatResp) Reset() {
	*x = ReviewChatResp{}
	mi := &file_svcreview_proto_msgTypes[36]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ReviewChatResp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ReviewChatResp) ProtoMessage() {}

func (x *ReviewChatResp) ProtoReflect() protoreflect.Message {
	mi := &file_svcreview_proto_msgTypes[36]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ReviewChatResp.ProtoReflect.Descriptor instead.
func (*ReviewChatResp) Descriptor() ([]byte, []int) {
	return file_svcreview_proto_rawDescGZIP(), []int{36}
}

func (x *ReviewChatResp) GetBase() *common.SvcBaseResp {
	if x != nil {
		return x.Base
	}
	return nil
}

func (x *ReviewChatResp) GetSync() int32 {
	if x != nil {
		return x.Sync
	}
	return 0
}

func (x *ReviewChatResp) GetResult() AuditResult {
	if x != nil {
		return x.Result
	}
	return AuditResult_audit_result_unknown
}

func (x *ReviewChatResp) GetReason() string {
	if x != nil {
		return x.Reason
	}
	return ""
}

func (x *ReviewChatResp) GetSubResult() AuditSubResult {
	if x != nil {
		return x.SubResult
	}
	return AuditSubResult_audit_sub_unknown
}

// 剧本审核
type ReviewScriptReq struct {
	state         protoimpl.MessageState    `protogen:"open.v1"`
	UserId        int64                     `protobuf:"varint,1,opt,name=user_id,json=userId,proto3" json:"user_id,omitempty"`
	Id            int64                     `protobuf:"varint,2,opt,name=id,proto3" json:"id,omitempty"`
	BizType       ReviewBizType             `protobuf:"varint,3,opt,name=bizType,proto3,enum=vc.svcreview.ReviewBizType" json:"bizType,omitempty"`
	Text          []string                  `protobuf:"bytes,4,rep,name=text,proto3" json:"text,omitempty"`
	Images        []string                  `protobuf:"bytes,5,rep,name=images,proto3" json:"images,omitempty"`
	Voice         *ReviewVoice              `protobuf:"bytes,6,opt,name=voice,proto3" json:"voice,omitempty"`
	CallbackData  *ReviewScriptCallbackData `protobuf:"bytes,7,opt,name=callback_data,json=callbackData,proto3" json:"callback_data,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *ReviewScriptReq) Reset() {
	*x = ReviewScriptReq{}
	mi := &file_svcreview_proto_msgTypes[37]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ReviewScriptReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ReviewScriptReq) ProtoMessage() {}

func (x *ReviewScriptReq) ProtoReflect() protoreflect.Message {
	mi := &file_svcreview_proto_msgTypes[37]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ReviewScriptReq.ProtoReflect.Descriptor instead.
func (*ReviewScriptReq) Descriptor() ([]byte, []int) {
	return file_svcreview_proto_rawDescGZIP(), []int{37}
}

func (x *ReviewScriptReq) GetUserId() int64 {
	if x != nil {
		return x.UserId
	}
	return 0
}

func (x *ReviewScriptReq) GetId() int64 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *ReviewScriptReq) GetBizType() ReviewBizType {
	if x != nil {
		return x.BizType
	}
	return ReviewBizType_unknown
}

func (x *ReviewScriptReq) GetText() []string {
	if x != nil {
		return x.Text
	}
	return nil
}

func (x *ReviewScriptReq) GetImages() []string {
	if x != nil {
		return x.Images
	}
	return nil
}

func (x *ReviewScriptReq) GetVoice() *ReviewVoice {
	if x != nil {
		return x.Voice
	}
	return nil
}

func (x *ReviewScriptReq) GetCallbackData() *ReviewScriptCallbackData {
	if x != nil {
		return x.CallbackData
	}
	return nil
}

type ReviewScriptCallbackData struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Userid        int64                  `protobuf:"varint,1,opt,name=userid,proto3" json:"userid,omitempty"`
	Id            int64                  `protobuf:"varint,2,opt,name=id,proto3" json:"id,omitempty"`
	Type          ReviewBizType          `protobuf:"varint,3,opt,name=type,proto3,enum=vc.svcreview.ReviewBizType" json:"type,omitempty"`
	Result        AuditResult            `protobuf:"varint,4,opt,name=result,proto3,enum=vc.svcreview.AuditResult" json:"result,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *ReviewScriptCallbackData) Reset() {
	*x = ReviewScriptCallbackData{}
	mi := &file_svcreview_proto_msgTypes[38]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ReviewScriptCallbackData) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ReviewScriptCallbackData) ProtoMessage() {}

func (x *ReviewScriptCallbackData) ProtoReflect() protoreflect.Message {
	mi := &file_svcreview_proto_msgTypes[38]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ReviewScriptCallbackData.ProtoReflect.Descriptor instead.
func (*ReviewScriptCallbackData) Descriptor() ([]byte, []int) {
	return file_svcreview_proto_rawDescGZIP(), []int{38}
}

func (x *ReviewScriptCallbackData) GetUserid() int64 {
	if x != nil {
		return x.Userid
	}
	return 0
}

func (x *ReviewScriptCallbackData) GetId() int64 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *ReviewScriptCallbackData) GetType() ReviewBizType {
	if x != nil {
		return x.Type
	}
	return ReviewBizType_unknown
}

func (x *ReviewScriptCallbackData) GetResult() AuditResult {
	if x != nil {
		return x.Result
	}
	return AuditResult_audit_result_unknown
}

// 查询审核记录请求
type GetReviewLogReq struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Userid        int64                  `protobuf:"varint,1,opt,name=userid,proto3" json:"userid,omitempty"`
	BizType       string                 `protobuf:"bytes,2,opt,name=biz_type,json=bizType,proto3" json:"biz_type,omitempty"`
	BizId         int64                  `protobuf:"varint,3,opt,name=biz_id,json=bizId,proto3" json:"biz_id,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *GetReviewLogReq) Reset() {
	*x = GetReviewLogReq{}
	mi := &file_svcreview_proto_msgTypes[39]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GetReviewLogReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetReviewLogReq) ProtoMessage() {}

func (x *GetReviewLogReq) ProtoReflect() protoreflect.Message {
	mi := &file_svcreview_proto_msgTypes[39]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetReviewLogReq.ProtoReflect.Descriptor instead.
func (*GetReviewLogReq) Descriptor() ([]byte, []int) {
	return file_svcreview_proto_rawDescGZIP(), []int{39}
}

func (x *GetReviewLogReq) GetUserid() int64 {
	if x != nil {
		return x.Userid
	}
	return 0
}

func (x *GetReviewLogReq) GetBizType() string {
	if x != nil {
		return x.BizType
	}
	return ""
}

func (x *GetReviewLogReq) GetBizId() int64 {
	if x != nil {
		return x.BizId
	}
	return 0
}

// 查询审核记录响应
type GetReviewLogResp struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Base          *common.SvcBaseResp    `protobuf:"bytes,1,opt,name=base,proto3" json:"base,omitempty"`
	Log           *ReviewLogInfo         `protobuf:"bytes,2,opt,name=log,proto3" json:"log,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *GetReviewLogResp) Reset() {
	*x = GetReviewLogResp{}
	mi := &file_svcreview_proto_msgTypes[40]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GetReviewLogResp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetReviewLogResp) ProtoMessage() {}

func (x *GetReviewLogResp) ProtoReflect() protoreflect.Message {
	mi := &file_svcreview_proto_msgTypes[40]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetReviewLogResp.ProtoReflect.Descriptor instead.
func (*GetReviewLogResp) Descriptor() ([]byte, []int) {
	return file_svcreview_proto_rawDescGZIP(), []int{40}
}

func (x *GetReviewLogResp) GetBase() *common.SvcBaseResp {
	if x != nil {
		return x.Base
	}
	return nil
}

func (x *GetReviewLogResp) GetLog() *ReviewLogInfo {
	if x != nil {
		return x.Log
	}
	return nil
}

// 查询审核记录列表请求
type GetReviewLogsReq struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Userid        int64                  `protobuf:"varint,1,opt,name=userid,proto3" json:"userid,omitempty"`
	BizType       string                 `protobuf:"bytes,2,opt,name=biz_type,json=bizType,proto3" json:"biz_type,omitempty"`
	RiskLevel     string                 `protobuf:"bytes,3,opt,name=risk_level,json=riskLevel,proto3" json:"risk_level,omitempty"`
	Page          int32                  `protobuf:"varint,4,opt,name=page,proto3" json:"page,omitempty"`
	PageSize      int32                  `protobuf:"varint,5,opt,name=page_size,json=pageSize,proto3" json:"page_size,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *GetReviewLogsReq) Reset() {
	*x = GetReviewLogsReq{}
	mi := &file_svcreview_proto_msgTypes[41]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GetReviewLogsReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetReviewLogsReq) ProtoMessage() {}

func (x *GetReviewLogsReq) ProtoReflect() protoreflect.Message {
	mi := &file_svcreview_proto_msgTypes[41]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetReviewLogsReq.ProtoReflect.Descriptor instead.
func (*GetReviewLogsReq) Descriptor() ([]byte, []int) {
	return file_svcreview_proto_rawDescGZIP(), []int{41}
}

func (x *GetReviewLogsReq) GetUserid() int64 {
	if x != nil {
		return x.Userid
	}
	return 0
}

func (x *GetReviewLogsReq) GetBizType() string {
	if x != nil {
		return x.BizType
	}
	return ""
}

func (x *GetReviewLogsReq) GetRiskLevel() string {
	if x != nil {
		return x.RiskLevel
	}
	return ""
}

func (x *GetReviewLogsReq) GetPage() int32 {
	if x != nil {
		return x.Page
	}
	return 0
}

func (x *GetReviewLogsReq) GetPageSize() int32 {
	if x != nil {
		return x.PageSize
	}
	return 0
}

// 查询审核记录列表响应
type GetReviewLogsResp struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Base          *common.SvcBaseResp    `protobuf:"bytes,1,opt,name=base,proto3" json:"base,omitempty"`
	Logs          []*ReviewLogInfo       `protobuf:"bytes,2,rep,name=logs,proto3" json:"logs,omitempty"`
	Total         int64                  `protobuf:"varint,3,opt,name=total,proto3" json:"total,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *GetReviewLogsResp) Reset() {
	*x = GetReviewLogsResp{}
	mi := &file_svcreview_proto_msgTypes[42]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GetReviewLogsResp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetReviewLogsResp) ProtoMessage() {}

func (x *GetReviewLogsResp) ProtoReflect() protoreflect.Message {
	mi := &file_svcreview_proto_msgTypes[42]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetReviewLogsResp.ProtoReflect.Descriptor instead.
func (*GetReviewLogsResp) Descriptor() ([]byte, []int) {
	return file_svcreview_proto_rawDescGZIP(), []int{42}
}

func (x *GetReviewLogsResp) GetBase() *common.SvcBaseResp {
	if x != nil {
		return x.Base
	}
	return nil
}

func (x *GetReviewLogsResp) GetLogs() []*ReviewLogInfo {
	if x != nil {
		return x.Logs
	}
	return nil
}

func (x *GetReviewLogsResp) GetTotal() int64 {
	if x != nil {
		return x.Total
	}
	return 0
}

// 审核记录信息
type ReviewLogInfo struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Id            int64                  `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	Userid        int64                  `protobuf:"varint,2,opt,name=userid,proto3" json:"userid,omitempty"`
	BizType       string                 `protobuf:"bytes,3,opt,name=biz_type,json=bizType,proto3" json:"biz_type,omitempty"`
	BizId         int64                  `protobuf:"varint,4,opt,name=biz_id,json=bizId,proto3" json:"biz_id,omitempty"`
	Data          string                 `protobuf:"bytes,5,opt,name=data,proto3" json:"data,omitempty"`
	Status        int32                  `protobuf:"varint,6,opt,name=status,proto3" json:"status,omitempty"`
	RequestId     string                 `protobuf:"bytes,7,opt,name=request_id,json=requestId,proto3" json:"request_id,omitempty"`
	RiskReason    string                 `protobuf:"bytes,8,opt,name=risk_reason,json=riskReason,proto3" json:"risk_reason,omitempty"`
	RiskLabels    string                 `protobuf:"bytes,9,opt,name=risk_labels,json=riskLabels,proto3" json:"risk_labels,omitempty"`
	RiskLevel     string                 `protobuf:"bytes,10,opt,name=risk_level,json=riskLevel,proto3" json:"risk_level,omitempty"`
	AuditDetail   string                 `protobuf:"bytes,11,opt,name=audit_detail,json=auditDetail,proto3" json:"audit_detail,omitempty"`
	Ct            int64                  `protobuf:"varint,12,opt,name=ct,proto3" json:"ct,omitempty"`
	Ut            int64                  `protobuf:"varint,13,opt,name=ut,proto3" json:"ut,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *ReviewLogInfo) Reset() {
	*x = ReviewLogInfo{}
	mi := &file_svcreview_proto_msgTypes[43]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ReviewLogInfo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ReviewLogInfo) ProtoMessage() {}

func (x *ReviewLogInfo) ProtoReflect() protoreflect.Message {
	mi := &file_svcreview_proto_msgTypes[43]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ReviewLogInfo.ProtoReflect.Descriptor instead.
func (*ReviewLogInfo) Descriptor() ([]byte, []int) {
	return file_svcreview_proto_rawDescGZIP(), []int{43}
}

func (x *ReviewLogInfo) GetId() int64 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *ReviewLogInfo) GetUserid() int64 {
	if x != nil {
		return x.Userid
	}
	return 0
}

func (x *ReviewLogInfo) GetBizType() string {
	if x != nil {
		return x.BizType
	}
	return ""
}

func (x *ReviewLogInfo) GetBizId() int64 {
	if x != nil {
		return x.BizId
	}
	return 0
}

func (x *ReviewLogInfo) GetData() string {
	if x != nil {
		return x.Data
	}
	return ""
}

func (x *ReviewLogInfo) GetStatus() int32 {
	if x != nil {
		return x.Status
	}
	return 0
}

func (x *ReviewLogInfo) GetRequestId() string {
	if x != nil {
		return x.RequestId
	}
	return ""
}

func (x *ReviewLogInfo) GetRiskReason() string {
	if x != nil {
		return x.RiskReason
	}
	return ""
}

func (x *ReviewLogInfo) GetRiskLabels() string {
	if x != nil {
		return x.RiskLabels
	}
	return ""
}

func (x *ReviewLogInfo) GetRiskLevel() string {
	if x != nil {
		return x.RiskLevel
	}
	return ""
}

func (x *ReviewLogInfo) GetAuditDetail() string {
	if x != nil {
		return x.AuditDetail
	}
	return ""
}

func (x *ReviewLogInfo) GetCt() int64 {
	if x != nil {
		return x.Ct
	}
	return 0
}

func (x *ReviewLogInfo) GetUt() int64 {
	if x != nil {
		return x.Ut
	}
	return 0
}

var File_svcreview_proto protoreflect.FileDescriptor

const file_svcreview_proto_rawDesc = "" +
	"\n" +
	"\x0fsvcreview.proto\x12\fvc.svcreview\x1a\x13common/common.proto\"/\n" +
	"\vReviewImage\x12\x0e\n" +
	"\x02id\x18\x01 \x01(\tR\x02id\x12\x10\n" +
	"\x03url\x18\x02 \x01(\tR\x03url\"A\n" +
	"\vReviewVoice\x12\x0e\n" +
	"\x02id\x18\x01 \x01(\tR\x02id\x12\x10\n" +
	"\x03sec\x18\x02 \x01(\x03R\x03sec\x12\x10\n" +
	"\x03url\x18\x03 \x01(\tR\x03url\"A\n" +
	"\vReviewVideo\x12\x0e\n" +
	"\x02id\x18\x01 \x01(\tR\x02id\x12\x10\n" +
	"\x03sec\x18\x02 \x01(\x03R\x03sec\x12\x10\n" +
	"\x03url\x18\x03 \x01(\tR\x03url\"\xd9\x03\n" +
	"\x16ShumeiAudioCallbackReq\x12\x12\n" +
	"\x04code\x18\x01 \x01(\x05R\x04code\x12\x18\n" +
	"\amessage\x18\x02 \x01(\tR\amessage\x12\x1c\n" +
	"\trequestId\x18\x03 \x01(\tR\trequestId\x12\x12\n" +
	"\x04btId\x18\x04 \x01(\tR\x04btId\x12\x1c\n" +
	"\taudioText\x18\x05 \x01(\tR\taudioText\x12\x1c\n" +
	"\taudioTime\x18\x06 \x01(\x05R\taudioTime\x12\x16\n" +
	"\x06labels\x18\a \x01(\tR\x06labels\x12\x1c\n" +
	"\triskLevel\x18\b \x01(\tR\triskLevel\x127\n" +
	"\x06detail\x18\t \x03(\v2\x1f.vc.svcreview.ShumeiAudioDetailR\x06detail\x127\n" +
	"\x06gender\x18\n" +
	" \x01(\v2\x1f.vc.svcreview.ShumeiAudioGenderR\x06gender\x121\n" +
	"\x04tags\x18\v \x03(\v2\x1d.vc.svcreview.ShumeiAudioTagsR\x04tags\x12H\n" +
	"\rcallbackParam\x18\f \x01(\v2\".vc.svcreview.ReviewCallbackOriginR\rcallbackParam\"\xa1\x02\n" +
	"\x11ShumeiAudioDetail\x12&\n" +
	"\x0eaudioStarttime\x18\x01 \x01(\x05R\x0eaudioStarttime\x12\"\n" +
	"\faudioEndtime\x18\x02 \x01(\x05R\faudioEndtime\x12\x1a\n" +
	"\baudioUrl\x18\x03 \x01(\tR\baudioUrl\x12\x1c\n" +
	"\taudioText\x18\x04 \x01(\tR\taudioText\x12\x1c\n" +
	"\triskLevel\x18\x05 \x01(\tR\triskLevel\x12\x1a\n" +
	"\briskType\x18\x06 \x01(\x05R\briskType\x12*\n" +
	"\x10audioMatchedItem\x18\a \x01(\tR\x10audioMatchedItem\x12 \n" +
	"\vdescription\x18\b \x01(\tR\vdescription\"I\n" +
	"\x11ShumeiAudioGender\x12\x14\n" +
	"\x05label\x18\x01 \x01(\tR\x05label\x12\x1e\n" +
	"\n" +
	"confidence\x18\x02 \x01(\x05R\n" +
	"confidence\"G\n" +
	"\x0fShumeiAudioTags\x12\x14\n" +
	"\x05label\x18\x01 \x01(\tR\x05label\x12\x1e\n" +
	"\n" +
	"confidence\x18\x02 \x01(\x05R\n" +
	"confidence\"a\n" +
	"\x14ReviewCallbackOrigin\x12\x14\n" +
	"\x05seqid\x18\x01 \x01(\x03R\x05seqid\x12\x18\n" +
	"\acontent\x18\x02 \x01(\tR\acontent\x12\x19\n" +
	"\bbiz_type\x18\x03 \x01(\tR\abizType\"\xc0\x01\n" +
	"\x10ReviewCommonResp\x12'\n" +
	"\x04base\x18\x01 \x01(\v2\x13.common.SvcBaseRespR\x04base\x121\n" +
	"\x06result\x18\x02 \x01(\x0e2\x19.vc.svcreview.AuditResultR\x06result\x12\x16\n" +
	"\x06reason\x18\x03 \x01(\tR\x06reason\x128\n" +
	"\x06detail\x18\x04 \x01(\v2 .vc.svcreview.ReviewResultDetailR\x06detail\"\xca\x02\n" +
	"\x12ReviewResultDetail\x121\n" +
	"\x06result\x18\x01 \x01(\x0e2\x19.vc.svcreview.AuditResultR\x06result\x122\n" +
	"\x04text\x18\x04 \x01(\v2\x1e.vc.svcreview.ReviewTextResultR\x04text\x125\n" +
	"\x05image\x18\x05 \x03(\v2\x1f.vc.svcreview.ReviewImageResultR\x05image\x125\n" +
	"\x05voice\x18\x06 \x01(\v2\x1f.vc.svcreview.ReviewVoiceResultR\x05voice\x125\n" +
	"\x05video\x18\a \x01(\v2\x1f.vc.svcreview.ReviewVideoResultR\x05video\x12(\n" +
	"\x10noneed_push_tips\x18\b \x01(\bR\x0enoneedPushTips\"\x8f\x01\n" +
	"\x11ReviewImageResult\x12/\n" +
	"\x05image\x18\x01 \x01(\v2\x19.vc.svcreview.ReviewImageR\x05image\x121\n" +
	"\x06result\x18\x02 \x01(\x0e2\x19.vc.svcreview.AuditResultR\x06result\x12\x16\n" +
	"\x06reason\x18\x03 \x01(\tR\x06reason\"\x8f\x01\n" +
	"\x11ReviewVoiceResult\x12/\n" +
	"\x05voice\x18\x01 \x01(\v2\x19.vc.svcreview.ReviewVoiceR\x05voice\x121\n" +
	"\x06result\x18\x02 \x01(\x0e2\x19.vc.svcreview.AuditResultR\x06result\x12\x16\n" +
	"\x06reason\x18\x03 \x01(\tR\x06reason\"\x8f\x01\n" +
	"\x11ReviewVideoResult\x12/\n" +
	"\x05video\x18\x01 \x01(\v2\x19.vc.svcreview.ReviewVideoR\x05video\x121\n" +
	"\x06result\x18\x02 \x01(\x0e2\x19.vc.svcreview.AuditResultR\x06result\x12\x16\n" +
	"\x06reason\x18\x03 \x01(\tR\x06reason\"q\n" +
	"\x10ReviewTextResult\x12\x12\n" +
	"\x04text\x18\x01 \x01(\tR\x04text\x121\n" +
	"\x06result\x18\x02 \x01(\x0e2\x19.vc.svcreview.AuditResultR\x06result\x12\x16\n" +
	"\x06reason\x18\x03 \x01(\tR\x06reason\"\x97\x02\n" +
	"\x14ShumeiMixCallbackReq\x12\x12\n" +
	"\x04btId\x18\x01 \x01(\tR\x04btId\x12\x12\n" +
	"\x04code\x18\x02 \x01(\x05R\x04code\x12\x1c\n" +
	"\trequestId\x18\x03 \x01(\tR\trequestId\x12\x1c\n" +
	"\triskLevel\x18\x04 \x01(\tR\triskLevel\x12\x1e\n" +
	"\n" +
	"resultType\x18\x05 \x01(\x05R\n" +
	"resultType\x127\n" +
	"\adetails\x18\x06 \x01(\v2\x1d.vc.svcreview.ShumeiMixDetailR\adetails\x12B\n" +
	"\vpassThrough\x18\a \x01(\v2 .vc.svcreview.ReviewCallbackDataR\vpassThrough\"\x84\x01\n" +
	"\x0fShumeiMixDetail\x127\n" +
	"\x05texts\x18\x01 \x03(\v2!.vc.svcreview.ShumeiMixTextDetailR\x05texts\x128\n" +
	"\x06images\x18\x02 \x03(\v2 .vc.svcreview.ShumeiMixImgDetailR\x06images\"\xab\x01\n" +
	"\x13ShumeiMixTextDetail\x12\x12\n" +
	"\x04code\x18\x01 \x01(\x05R\x04code\x12\x18\n" +
	"\amessage\x18\x02 \x01(\tR\amessage\x12\x1c\n" +
	"\trequestId\x18\x03 \x01(\tR\trequestId\x12\x16\n" +
	"\x06dataId\x18\x04 \x01(\tR\x06dataId\x12\x12\n" +
	"\x04btId\x18\x05 \x01(\tR\x04btId\x12\x1c\n" +
	"\triskLevel\x18\x06 \x01(\tR\triskLevel\"\xaa\x01\n" +
	"\x12ShumeiMixImgDetail\x12\x12\n" +
	"\x04code\x18\x01 \x01(\x05R\x04code\x12\x18\n" +
	"\amessage\x18\x02 \x01(\tR\amessage\x12\x1c\n" +
	"\trequestId\x18\x03 \x01(\tR\trequestId\x12\x16\n" +
	"\x06dataId\x18\x04 \x01(\tR\x06dataId\x12\x12\n" +
	"\x04btId\x18\x05 \x01(\tR\x04btId\x12\x1c\n" +
	"\triskLevel\x18\x06 \x01(\tR\triskLevel\"?\n" +
	"\x12ReviewCallbackData\x12\x0e\n" +
	"\x02id\x18\x01 \x01(\x03R\x02id\x12\x19\n" +
	"\bbiz_type\x18\x02 \x01(\tR\abizType\"\xd6\x01\n" +
	"\x1bShumeiMultiImageCallbackReq\x12\x12\n" +
	"\x04btId\x18\x01 \x01(\tR\x04btId\x12\x12\n" +
	"\x04code\x18\x02 \x01(\x05R\x04code\x12\x1c\n" +
	"\trequestId\x18\x03 \x01(\tR\trequestId\x122\n" +
	"\x04imgs\x18\x06 \x03(\v2\x1e.vc.svcreview.ShumeiMultiImageR\x04imgs\x12=\n" +
	"\aauxInfo\x18\a \x01(\v2#.vc.svcreview.ShumeiMultiImageExtraR\aauxInfo\"X\n" +
	"\x10ShumeiMultiImage\x12\x12\n" +
	"\x04btId\x18\x01 \x01(\tR\x04btId\x12\x1c\n" +
	"\triskLevel\x18\x02 \x01(\tR\triskLevel\x12\x12\n" +
	"\x04code\x18\x03 \x01(\x05R\x04code\"e\n" +
	"\x15ShumeiMultiImageExtra\x12L\n" +
	"\vpassThrough\x18\x01 \x01(\v2*.vc.svcreview.ReviewMultiImageCallbackDataR\vpassThrough\"r\n" +
	"\x1cReviewMultiImageCallbackData\x12\x0e\n" +
	"\x02id\x18\x01 \x01(\x03R\x02id\x12\x18\n" +
	"\abizType\x18\x02 \x01(\tR\abizType\x12\x16\n" +
	"\x06userid\x18\x03 \x01(\x03R\x06userid\x12\x10\n" +
	"\x03sex\x18\x04 \x01(\x05R\x03sex\"w\n" +
	"\x1cReviewMemberInfoCallbackData\x12\x16\n" +
	"\x06userid\x18\x01 \x01(\x03R\x06userid\x12\x0e\n" +
	"\x02id\x18\x02 \x01(\x03R\x02id\x12/\n" +
	"\x04type\x18\x03 \x01(\x0e2\x1b.vc.svcreview.ReviewBizTypeR\x04type\"\x83\x03\n" +
	"\x13ReviewMemberInfoReq\x12\x16\n" +
	"\x06userid\x18\x01 \x01(\x03R\x06userid\x12\x10\n" +
	"\x03sex\x18\x02 \x01(\x05R\x03sex\x125\n" +
	"\abizType\x18\x03 \x01(\x0e2\x1b.vc.svcreview.ReviewBizTypeR\abizType\x12\x12\n" +
	"\x04text\x18\x04 \x03(\tR\x04text\x121\n" +
	"\x06images\x18\x05 \x03(\v2\x19.vc.svcreview.ReviewImageR\x06images\x12/\n" +
	"\x05voice\x18\x06 \x01(\v2\x19.vc.svcreview.ReviewVoiceR\x05voice\x12O\n" +
	"\rcallback_data\x18\a \x01(\v2*.vc.svcreview.ReviewMemberInfoCallbackDataR\fcallbackData\x12\x14\n" +
	"\x05xluid\x18\b \x01(\tR\x05xluid\x12\x18\n" +
	"\acountry\x18\t \x01(\tR\acountry\x12\x12\n" +
	"\x04lang\x18\n" +
	" \x01(\tR\x04lang\"\xc0\x02\n" +
	"\x0fReviewMomentReq\x12\x16\n" +
	"\x06userid\x18\x01 \x01(\x03R\x06userid\x12\x12\n" +
	"\x04moid\x18\x02 \x01(\x03R\x04moid\x12\x12\n" +
	"\x04text\x18\x03 \x01(\tR\x04text\x12\x16\n" +
	"\x06images\x18\x04 \x03(\tR\x06images\x12/\n" +
	"\x05voice\x18\x05 \x01(\v2\x19.vc.svcreview.ReviewVoiceR\x05voice\x12/\n" +
	"\x05video\x18\x06 \x01(\v2\x19.vc.svcreview.ReviewVideoR\x05video\x12E\n" +
	"\rcallback_data\x18\a \x01(\v2 .vc.svcreview.ReviewCallbackDataR\fcallbackData\x12\x18\n" +
	"\acountry\x18\b \x01(\tR\acountry\x12\x12\n" +
	"\x04lang\x18\t \x01(\tR\x04lang\"\xa8\x01\n" +
	"\x16ReviewUserCommonMsgReq\x12\x16\n" +
	"\x06userid\x18\x01 \x01(\x03R\x06userid\x12\x0e\n" +
	"\x02id\x18\x02 \x01(\x03R\x02id\x12\x12\n" +
	"\x04text\x18\x03 \x01(\tR\x04text\x12R\n" +
	"\rcallback_data\x18\x04 \x01(\v2-.vc.svcreview.ReviewUserCommonMsgCallbackDataR\fcallbackData\"I\n" +
	"\x1fReviewUserCommonMsgCallbackData\x12\x16\n" +
	"\x06userid\x18\x01 \x01(\x03R\x06userid\x12\x0e\n" +
	"\x02id\x18\x02 \x01(\x03R\x02id\"\xf5\x02\n" +
	"\x11ReviewCallbackReq\x12\x14\n" +
	"\x05appId\x18\x01 \x01(\tR\x05appId\x12\x18\n" +
	"\abizType\x18\x02 \x01(\tR\abizType\x12\x18\n" +
	"\aauditor\x18\x03 \x01(\tR\aauditor\x12\x16\n" +
	"\x06caseId\x18\x04 \x01(\tR\x06caseId\x12 \n" +
	"\vcontentType\x18\x05 \x01(\tR\vcontentType\x12\x16\n" +
	"\x06dataId\x18\x06 \x01(\tR\x06dataId\x12\x1e\n" +
	"\n" +
	"suggestion\x18\a \x01(\tR\n" +
	"suggestion\x12\x14\n" +
	"\x05label\x18\b \x01(\tR\x05label\x12\x1b\n" +
	"\tlabel_key\x18\t \x01(\tR\blabelKey\x12\x1d\n" +
	"\n" +
	"label_code\x18\n" +
	" \x01(\x05R\tlabelCode\x12:\n" +
	"\x06origin\x18\v \x01(\v2\".vc.svcreview.ReviewCallbackOriginR\x06origin\x12\x16\n" +
	"\x06secret\x18\f \x01(\tR\x06secret\"X\n" +
	"\x1aReviewRoomInfoCallbackData\x12\x16\n" +
	"\x06roomid\x18\x01 \x01(\x03R\x06roomid\x12\x0e\n" +
	"\x02id\x18\x02 \x01(\x03R\x02id\x12\x12\n" +
	"\x04type\x18\x03 \x01(\x05R\x04type\"\xbc\x02\n" +
	"\x11ReviewRoomInfoReq\x12\x16\n" +
	"\x06roomid\x18\x01 \x01(\x03R\x06roomid\x12\x16\n" +
	"\x06userid\x18\x02 \x01(\x03R\x06userid\x125\n" +
	"\abizType\x18\x03 \x01(\x0e2\x1b.vc.svcreview.ReviewBizTypeR\abizType\x12\x12\n" +
	"\x04text\x18\x04 \x01(\tR\x04text\x12/\n" +
	"\x05image\x18\x05 \x01(\v2\x19.vc.svcreview.ReviewImageR\x05image\x12M\n" +
	"\rcallback_data\x18\a \x01(\v2(.vc.svcreview.ReviewRoomInfoCallbackDataR\fcallbackData\x12\x18\n" +
	"\acountry\x18\b \x01(\tR\acountry\x12\x12\n" +
	"\x04lang\x18\t \x01(\tR\x04lang\"\x88\x01\n" +
	"\x12ReviewRoomInfoResp\x12'\n" +
	"\x04base\x18\x01 \x01(\v2\x13.common.SvcBaseRespR\x04base\x121\n" +
	"\x06result\x18\x02 \x01(\x0e2\x19.vc.svcreview.AuditResultR\x06result\x12\x16\n" +
	"\x06reason\x18\x03 \x01(\tR\x06reason\"\xef\x01\n" +
	"\x11ReviewRoomChatReq\x12\x16\n" +
	"\x06userid\x18\x01 \x01(\x03R\x06userid\x12\x16\n" +
	"\x06roomid\x18\x02 \x01(\x03R\x06roomid\x12\x10\n" +
	"\x03sid\x18\x03 \x01(\x03R\x03sid\x12\x19\n" +
	"\bsub_type\x18\x05 \x01(\x05R\asubType\x12\x18\n" +
	"\acontent\x18\x06 \x01(\tR\acontent\x12\x0e\n" +
	"\x02ip\x18\a \x01(\tR\x02ip\x12\x18\n" +
	"\acountry\x18\b \x01(\tR\acountry\x12\x12\n" +
	"\x04lang\x18\t \x01(\tR\x04lang\x12%\n" +
	"\x04base\x18\n" +
	" \x01(\v2\x11.common.BaseParamR\x04base\"\x88\x01\n" +
	"\x12ReviewRoomChatResp\x12'\n" +
	"\x04base\x18\x01 \x01(\v2\x13.common.SvcBaseRespR\x04base\x121\n" +
	"\x06result\x18\x02 \x01(\x0e2\x19.vc.svcreview.AuditResultR\x06result\x12\x16\n" +
	"\x06reason\x18\x03 \x01(\tR\x06reason\"@\n" +
	"\x16ReviewChatCallbackData\x12\x10\n" +
	"\x03sid\x18\x01 \x01(\tR\x03sid\x12\x14\n" +
	"\x05msgid\x18\x02 \x01(\tR\x05msgid\"\x99\x04\n" +
	"\rReviewChatReq\x12\x16\n" +
	"\x06userid\x18\x01 \x01(\x03R\x06userid\x12\x10\n" +
	"\x03sid\x18\x02 \x01(\tR\x03sid\x12\x14\n" +
	"\x05msgId\x18\x03 \x01(\tR\x05msgId\x12\x12\n" +
	"\x04text\x18\x05 \x01(\tR\x04text\x12/\n" +
	"\x05image\x18\x06 \x01(\v2\x19.vc.svcreview.ReviewImageR\x05image\x12/\n" +
	"\x05voice\x18\a \x01(\v2\x19.vc.svcreview.ReviewVoiceR\x05voice\x12/\n" +
	"\x05video\x18\b \x01(\v2\x19.vc.svcreview.ReviewVideoR\x05video\x12I\n" +
	"\rcallback_data\x18\t \x01(\v2$.vc.svcreview.ReviewChatCallbackDataR\fcallbackData\x12\x10\n" +
	"\x03sex\x18\n" +
	" \x01(\x05R\x03sex\x12\x1c\n" +
	"\tsweetRate\x18\v \x01(\x05R\tsweetRate\x12\x12\n" +
	"\x04eTag\x18\f \x01(\tR\x04eTag\x12\x17\n" +
	"\apeer_id\x18\r \x01(\x03R\x06peerId\x12\x0e\n" +
	"\x02ip\x18\x0e \x01(\tR\x02ip\x12\x18\n" +
	"\acountry\x18\x0f \x01(\tR\acountry\x12\x12\n" +
	"\x04lang\x18\x10 \x01(\tR\x04lang\x12\x14\n" +
	"\x05count\x18\x11 \x01(\x03R\x05count\x12%\n" +
	"\x04base\x18\x12 \x01(\v2\x11.common.BaseParamR\x04base\"\xd5\x01\n" +
	"\x0eReviewChatResp\x12'\n" +
	"\x04base\x18\x01 \x01(\v2\x13.common.SvcBaseRespR\x04base\x12\x12\n" +
	"\x04sync\x18\x02 \x01(\x05R\x04sync\x121\n" +
	"\x06result\x18\x03 \x01(\x0e2\x19.vc.svcreview.AuditResultR\x06result\x12\x16\n" +
	"\x06reason\x18\x04 \x01(\tR\x06reason\x12;\n" +
	"\n" +
	"sub_result\x18\x05 \x01(\x0e2\x1c.vc.svcreview.AuditSubResultR\tsubResult\"\x9b\x02\n" +
	"\x0fReviewScriptReq\x12\x17\n" +
	"\auser_id\x18\x01 \x01(\x03R\x06userId\x12\x0e\n" +
	"\x02id\x18\x02 \x01(\x03R\x02id\x125\n" +
	"\abizType\x18\x03 \x01(\x0e2\x1b.vc.svcreview.ReviewBizTypeR\abizType\x12\x12\n" +
	"\x04text\x18\x04 \x03(\tR\x04text\x12\x16\n" +
	"\x06images\x18\x05 \x03(\tR\x06images\x12/\n" +
	"\x05voice\x18\x06 \x01(\v2\x19.vc.svcreview.ReviewVoiceR\x05voice\x12K\n" +
	"\rcallback_data\x18\a \x01(\v2&.vc.svcreview.ReviewScriptCallbackDataR\fcallbackData\"\xa6\x01\n" +
	"\x18ReviewScriptCallbackData\x12\x16\n" +
	"\x06userid\x18\x01 \x01(\x03R\x06userid\x12\x0e\n" +
	"\x02id\x18\x02 \x01(\x03R\x02id\x12/\n" +
	"\x04type\x18\x03 \x01(\x0e2\x1b.vc.svcreview.ReviewBizTypeR\x04type\x121\n" +
	"\x06result\x18\x04 \x01(\x0e2\x19.vc.svcreview.AuditResultR\x06result\"[\n" +
	"\x0fGetReviewLogReq\x12\x16\n" +
	"\x06userid\x18\x01 \x01(\x03R\x06userid\x12\x19\n" +
	"\bbiz_type\x18\x02 \x01(\tR\abizType\x12\x15\n" +
	"\x06biz_id\x18\x03 \x01(\x03R\x05bizId\"j\n" +
	"\x10GetReviewLogResp\x12'\n" +
	"\x04base\x18\x01 \x01(\v2\x13.common.SvcBaseRespR\x04base\x12-\n" +
	"\x03log\x18\x02 \x01(\v2\x1b.vc.svcreview.ReviewLogInfoR\x03log\"\x95\x01\n" +
	"\x10GetReviewLogsReq\x12\x16\n" +
	"\x06userid\x18\x01 \x01(\x03R\x06userid\x12\x19\n" +
	"\bbiz_type\x18\x02 \x01(\tR\abizType\x12\x1d\n" +
	"\n" +
	"risk_level\x18\x03 \x01(\tR\triskLevel\x12\x12\n" +
	"\x04page\x18\x04 \x01(\x05R\x04page\x12\x1b\n" +
	"\tpage_size\x18\x05 \x01(\x05R\bpageSize\"\x83\x01\n" +
	"\x11GetReviewLogsResp\x12'\n" +
	"\x04base\x18\x01 \x01(\v2\x13.common.SvcBaseRespR\x04base\x12/\n" +
	"\x04logs\x18\x02 \x03(\v2\x1b.vc.svcreview.ReviewLogInfoR\x04logs\x12\x14\n" +
	"\x05total\x18\x03 \x01(\x03R\x05total\"\xd8\x02\n" +
	"\rReviewLogInfo\x12\x0e\n" +
	"\x02id\x18\x01 \x01(\x03R\x02id\x12\x16\n" +
	"\x06userid\x18\x02 \x01(\x03R\x06userid\x12\x19\n" +
	"\bbiz_type\x18\x03 \x01(\tR\abizType\x12\x15\n" +
	"\x06biz_id\x18\x04 \x01(\x03R\x05bizId\x12\x12\n" +
	"\x04data\x18\x05 \x01(\tR\x04data\x12\x16\n" +
	"\x06status\x18\x06 \x01(\x05R\x06status\x12\x1d\n" +
	"\n" +
	"request_id\x18\a \x01(\tR\trequestId\x12\x1f\n" +
	"\vrisk_reason\x18\b \x01(\tR\n" +
	"riskReason\x12\x1f\n" +
	"\vrisk_labels\x18\t \x01(\tR\n" +
	"riskLabels\x12\x1d\n" +
	"\n" +
	"risk_level\x18\n" +
	" \x01(\tR\triskLevel\x12!\n" +
	"\faudit_detail\x18\v \x01(\tR\vauditDetail\x12\x0e\n" +
	"\x02ct\x18\f \x01(\x03R\x02ct\x12\x0e\n" +
	"\x02ut\x18\r \x01(\x03R\x02ut*\xe6\x02\n" +
	"\rReviewBizType\x12\v\n" +
	"\aunknown\x10\x00\x12\x10\n" +
	"\fUserNickname\x10\x01\x12\x0e\n" +
	"\n" +
	"UserAvatar\x10\x02\x12\x11\n" +
	"\rUserSignVoice\x10\x03\x12\x10\n" +
	"\fUserTextSign\x10\x04\x12\r\n" +
	"\tUserAlbum\x10\x05\x12\n" +
	"\n" +
	"\x06Moment\x10\x06\x12\v\n" +
	"\aComment\x10\a\x12\r\n" +
	"\tRoomTitle\x10\b\x12\x0e\n" +
	"\n" +
	"RoomNotice\x10\t\x12\r\n" +
	"\tRoomCover\x10\n" +
	"\x12\n" +
	"\n" +
	"\x06RoomBg\x10\v\x12\x0f\n" +
	"\vScriptTitle\x10\f\x12\x0f\n" +
	"\vScriptCover\x10\r\x12\x0e\n" +
	"\n" +
	"ScriptLine\x10\x0e\x12\x0f\n" +
	"\vScriptTopic\x10\x0f\x12\x15\n" +
	"\x11ScriptDubbingText\x10\x10\x12\x16\n" +
	"\x12ScriptDubbingVoice\x10\x11\x12\x15\n" +
	"\x11ScriptCommentText\x10\x12\x12\x16\n" +
	"\x12ScriptCommentVoice\x10\x13*H\n" +
	"\x10ReviewMemberType\x12\x12\n" +
	"\x0ereview_unknown\x10\x00\x12\n" +
	"\n" +
	"\x06avatar\x10\x01\x12\t\n" +
	"\x05album\x10\x02\x12\t\n" +
	"\x05other\x10\x03*X\n" +
	"\vAuditResult\x12\x18\n" +
	"\x14audit_result_unknown\x10\x00\x12\b\n" +
	"\x04pass\x10\x01\x12\n" +
	"\n" +
	"\x06reject\x10\x02\x12\n" +
	"\n" +
	"\x06review\x10\x03\x12\r\n" +
	"\tfake_send\x10\x04*I\n" +
	"\x0eAuditSubResult\x12\x15\n" +
	"\x11audit_sub_unknown\x10\x00\x12 \n" +
	"\x1caudit_sub_male_lianxifangshi\x10\x01*Y\n" +
	"\fShieldResult\x12\x12\n" +
	"\x0eshield_unknown\x10\x00\x12\x0f\n" +
	"\vshield_pass\x10\x01\x12\x11\n" +
	"\rshield_reject\x10\x02\x12\x11\n" +
	"\rshield_review\x10\x032\xf4\x03\n" +
	"\x01s\x12J\n" +
	"\x0eReviewCallback\x12\x1f.vc.svcreview.ReviewCallbackReq\x1a\x15.common.SvcCommonResp\"\x00\x12W\n" +
	"\x10ReviewMemberInfo\x12!.vc.svcreview.ReviewMemberInfoReq\x1a\x1e.vc.svcreview.ReviewCommonResp\"\x00\x12O\n" +
	"\fReviewScript\x12\x1d.vc.svcreview.ReviewScriptReq\x1a\x1e.vc.svcreview.ReviewCommonResp\"\x00\x12T\n" +
	"\x13ShumeiAudioCallback\x12$.vc.svcreview.ShumeiAudioCallbackReq\x1a\x15.common.SvcCommonResp\"\x00\x12O\n" +
	"\fGetReviewLog\x12\x1d.vc.svcreview.GetReviewLogReq\x1a\x1e.vc.svcreview.GetReviewLogResp\"\x00\x12R\n" +
	"\rGetReviewLogs\x12\x1e.vc.svcreview.GetReviewLogsReq\x1a\x1f.vc.svcreview.GetReviewLogsResp\"\x00BGZEnew-gitlab.xunlei.cn/vcproject/backends/proto/api/svcreview;svcreviewb\x06proto3"

var (
	file_svcreview_proto_rawDescOnce sync.Once
	file_svcreview_proto_rawDescData []byte
)

func file_svcreview_proto_rawDescGZIP() []byte {
	file_svcreview_proto_rawDescOnce.Do(func() {
		file_svcreview_proto_rawDescData = protoimpl.X.CompressGZIP(unsafe.Slice(unsafe.StringData(file_svcreview_proto_rawDesc), len(file_svcreview_proto_rawDesc)))
	})
	return file_svcreview_proto_rawDescData
}

var file_svcreview_proto_enumTypes = make([]protoimpl.EnumInfo, 5)
var file_svcreview_proto_msgTypes = make([]protoimpl.MessageInfo, 44)
var file_svcreview_proto_goTypes = []any{
	(ReviewBizType)(0),                      // 0: vc.svcreview.ReviewBizType
	(ReviewMemberType)(0),                   // 1: vc.svcreview.ReviewMemberType
	(AuditResult)(0),                        // 2: vc.svcreview.AuditResult
	(AuditSubResult)(0),                     // 3: vc.svcreview.AuditSubResult
	(ShieldResult)(0),                       // 4: vc.svcreview.ShieldResult
	(*ReviewImage)(nil),                     // 5: vc.svcreview.ReviewImage
	(*ReviewVoice)(nil),                     // 6: vc.svcreview.ReviewVoice
	(*ReviewVideo)(nil),                     // 7: vc.svcreview.ReviewVideo
	(*ShumeiAudioCallbackReq)(nil),          // 8: vc.svcreview.ShumeiAudioCallbackReq
	(*ShumeiAudioDetail)(nil),               // 9: vc.svcreview.ShumeiAudioDetail
	(*ShumeiAudioGender)(nil),               // 10: vc.svcreview.ShumeiAudioGender
	(*ShumeiAudioTags)(nil),                 // 11: vc.svcreview.ShumeiAudioTags
	(*ReviewCallbackOrigin)(nil),            // 12: vc.svcreview.ReviewCallbackOrigin
	(*ReviewCommonResp)(nil),                // 13: vc.svcreview.ReviewCommonResp
	(*ReviewResultDetail)(nil),              // 14: vc.svcreview.ReviewResultDetail
	(*ReviewImageResult)(nil),               // 15: vc.svcreview.ReviewImageResult
	(*ReviewVoiceResult)(nil),               // 16: vc.svcreview.ReviewVoiceResult
	(*ReviewVideoResult)(nil),               // 17: vc.svcreview.ReviewVideoResult
	(*ReviewTextResult)(nil),                // 18: vc.svcreview.ReviewTextResult
	(*ShumeiMixCallbackReq)(nil),            // 19: vc.svcreview.ShumeiMixCallbackReq
	(*ShumeiMixDetail)(nil),                 // 20: vc.svcreview.ShumeiMixDetail
	(*ShumeiMixTextDetail)(nil),             // 21: vc.svcreview.ShumeiMixTextDetail
	(*ShumeiMixImgDetail)(nil),              // 22: vc.svcreview.ShumeiMixImgDetail
	(*ReviewCallbackData)(nil),              // 23: vc.svcreview.ReviewCallbackData
	(*ShumeiMultiImageCallbackReq)(nil),     // 24: vc.svcreview.ShumeiMultiImageCallbackReq
	(*ShumeiMultiImage)(nil),                // 25: vc.svcreview.ShumeiMultiImage
	(*ShumeiMultiImageExtra)(nil),           // 26: vc.svcreview.ShumeiMultiImageExtra
	(*ReviewMultiImageCallbackData)(nil),    // 27: vc.svcreview.ReviewMultiImageCallbackData
	(*ReviewMemberInfoCallbackData)(nil),    // 28: vc.svcreview.ReviewMemberInfoCallbackData
	(*ReviewMemberInfoReq)(nil),             // 29: vc.svcreview.ReviewMemberInfoReq
	(*ReviewMomentReq)(nil),                 // 30: vc.svcreview.ReviewMomentReq
	(*ReviewUserCommonMsgReq)(nil),          // 31: vc.svcreview.ReviewUserCommonMsgReq
	(*ReviewUserCommonMsgCallbackData)(nil), // 32: vc.svcreview.ReviewUserCommonMsgCallbackData
	(*ReviewCallbackReq)(nil),               // 33: vc.svcreview.ReviewCallbackReq
	(*ReviewRoomInfoCallbackData)(nil),      // 34: vc.svcreview.ReviewRoomInfoCallbackData
	(*ReviewRoomInfoReq)(nil),               // 35: vc.svcreview.ReviewRoomInfoReq
	(*ReviewRoomInfoResp)(nil),              // 36: vc.svcreview.ReviewRoomInfoResp
	(*ReviewRoomChatReq)(nil),               // 37: vc.svcreview.ReviewRoomChatReq
	(*ReviewRoomChatResp)(nil),              // 38: vc.svcreview.ReviewRoomChatResp
	(*ReviewChatCallbackData)(nil),          // 39: vc.svcreview.ReviewChatCallbackData
	(*ReviewChatReq)(nil),                   // 40: vc.svcreview.ReviewChatReq
	(*ReviewChatResp)(nil),                  // 41: vc.svcreview.ReviewChatResp
	(*ReviewScriptReq)(nil),                 // 42: vc.svcreview.ReviewScriptReq
	(*ReviewScriptCallbackData)(nil),        // 43: vc.svcreview.ReviewScriptCallbackData
	(*GetReviewLogReq)(nil),                 // 44: vc.svcreview.GetReviewLogReq
	(*GetReviewLogResp)(nil),                // 45: vc.svcreview.GetReviewLogResp
	(*GetReviewLogsReq)(nil),                // 46: vc.svcreview.GetReviewLogsReq
	(*GetReviewLogsResp)(nil),               // 47: vc.svcreview.GetReviewLogsResp
	(*ReviewLogInfo)(nil),                   // 48: vc.svcreview.ReviewLogInfo
	(*common.SvcBaseResp)(nil),              // 49: common.SvcBaseResp
	(*common.BaseParam)(nil),                // 50: common.BaseParam
	(*common.SvcCommonResp)(nil),            // 51: common.SvcCommonResp
}
var file_svcreview_proto_depIdxs = []int32{
	9,  // 0: vc.svcreview.ShumeiAudioCallbackReq.detail:type_name -> vc.svcreview.ShumeiAudioDetail
	10, // 1: vc.svcreview.ShumeiAudioCallbackReq.gender:type_name -> vc.svcreview.ShumeiAudioGender
	11, // 2: vc.svcreview.ShumeiAudioCallbackReq.tags:type_name -> vc.svcreview.ShumeiAudioTags
	12, // 3: vc.svcreview.ShumeiAudioCallbackReq.callbackParam:type_name -> vc.svcreview.ReviewCallbackOrigin
	49, // 4: vc.svcreview.ReviewCommonResp.base:type_name -> common.SvcBaseResp
	2,  // 5: vc.svcreview.ReviewCommonResp.result:type_name -> vc.svcreview.AuditResult
	14, // 6: vc.svcreview.ReviewCommonResp.detail:type_name -> vc.svcreview.ReviewResultDetail
	2,  // 7: vc.svcreview.ReviewResultDetail.result:type_name -> vc.svcreview.AuditResult
	18, // 8: vc.svcreview.ReviewResultDetail.text:type_name -> vc.svcreview.ReviewTextResult
	15, // 9: vc.svcreview.ReviewResultDetail.image:type_name -> vc.svcreview.ReviewImageResult
	16, // 10: vc.svcreview.ReviewResultDetail.voice:type_name -> vc.svcreview.ReviewVoiceResult
	17, // 11: vc.svcreview.ReviewResultDetail.video:type_name -> vc.svcreview.ReviewVideoResult
	5,  // 12: vc.svcreview.ReviewImageResult.image:type_name -> vc.svcreview.ReviewImage
	2,  // 13: vc.svcreview.ReviewImageResult.result:type_name -> vc.svcreview.AuditResult
	6,  // 14: vc.svcreview.ReviewVoiceResult.voice:type_name -> vc.svcreview.ReviewVoice
	2,  // 15: vc.svcreview.ReviewVoiceResult.result:type_name -> vc.svcreview.AuditResult
	7,  // 16: vc.svcreview.ReviewVideoResult.video:type_name -> vc.svcreview.ReviewVideo
	2,  // 17: vc.svcreview.ReviewVideoResult.result:type_name -> vc.svcreview.AuditResult
	2,  // 18: vc.svcreview.ReviewTextResult.result:type_name -> vc.svcreview.AuditResult
	20, // 19: vc.svcreview.ShumeiMixCallbackReq.details:type_name -> vc.svcreview.ShumeiMixDetail
	23, // 20: vc.svcreview.ShumeiMixCallbackReq.passThrough:type_name -> vc.svcreview.ReviewCallbackData
	21, // 21: vc.svcreview.ShumeiMixDetail.texts:type_name -> vc.svcreview.ShumeiMixTextDetail
	22, // 22: vc.svcreview.ShumeiMixDetail.images:type_name -> vc.svcreview.ShumeiMixImgDetail
	25, // 23: vc.svcreview.ShumeiMultiImageCallbackReq.imgs:type_name -> vc.svcreview.ShumeiMultiImage
	26, // 24: vc.svcreview.ShumeiMultiImageCallbackReq.auxInfo:type_name -> vc.svcreview.ShumeiMultiImageExtra
	27, // 25: vc.svcreview.ShumeiMultiImageExtra.passThrough:type_name -> vc.svcreview.ReviewMultiImageCallbackData
	0,  // 26: vc.svcreview.ReviewMemberInfoCallbackData.type:type_name -> vc.svcreview.ReviewBizType
	0,  // 27: vc.svcreview.ReviewMemberInfoReq.bizType:type_name -> vc.svcreview.ReviewBizType
	5,  // 28: vc.svcreview.ReviewMemberInfoReq.images:type_name -> vc.svcreview.ReviewImage
	6,  // 29: vc.svcreview.ReviewMemberInfoReq.voice:type_name -> vc.svcreview.ReviewVoice
	28, // 30: vc.svcreview.ReviewMemberInfoReq.callback_data:type_name -> vc.svcreview.ReviewMemberInfoCallbackData
	6,  // 31: vc.svcreview.ReviewMomentReq.voice:type_name -> vc.svcreview.ReviewVoice
	7,  // 32: vc.svcreview.ReviewMomentReq.video:type_name -> vc.svcreview.ReviewVideo
	23, // 33: vc.svcreview.ReviewMomentReq.callback_data:type_name -> vc.svcreview.ReviewCallbackData
	32, // 34: vc.svcreview.ReviewUserCommonMsgReq.callback_data:type_name -> vc.svcreview.ReviewUserCommonMsgCallbackData
	12, // 35: vc.svcreview.ReviewCallbackReq.origin:type_name -> vc.svcreview.ReviewCallbackOrigin
	0,  // 36: vc.svcreview.ReviewRoomInfoReq.bizType:type_name -> vc.svcreview.ReviewBizType
	5,  // 37: vc.svcreview.ReviewRoomInfoReq.image:type_name -> vc.svcreview.ReviewImage
	34, // 38: vc.svcreview.ReviewRoomInfoReq.callback_data:type_name -> vc.svcreview.ReviewRoomInfoCallbackData
	49, // 39: vc.svcreview.ReviewRoomInfoResp.base:type_name -> common.SvcBaseResp
	2,  // 40: vc.svcreview.ReviewRoomInfoResp.result:type_name -> vc.svcreview.AuditResult
	50, // 41: vc.svcreview.ReviewRoomChatReq.base:type_name -> common.BaseParam
	49, // 42: vc.svcreview.ReviewRoomChatResp.base:type_name -> common.SvcBaseResp
	2,  // 43: vc.svcreview.ReviewRoomChatResp.result:type_name -> vc.svcreview.AuditResult
	5,  // 44: vc.svcreview.ReviewChatReq.image:type_name -> vc.svcreview.ReviewImage
	6,  // 45: vc.svcreview.ReviewChatReq.voice:type_name -> vc.svcreview.ReviewVoice
	7,  // 46: vc.svcreview.ReviewChatReq.video:type_name -> vc.svcreview.ReviewVideo
	39, // 47: vc.svcreview.ReviewChatReq.callback_data:type_name -> vc.svcreview.ReviewChatCallbackData
	50, // 48: vc.svcreview.ReviewChatReq.base:type_name -> common.BaseParam
	49, // 49: vc.svcreview.ReviewChatResp.base:type_name -> common.SvcBaseResp
	2,  // 50: vc.svcreview.ReviewChatResp.result:type_name -> vc.svcreview.AuditResult
	3,  // 51: vc.svcreview.ReviewChatResp.sub_result:type_name -> vc.svcreview.AuditSubResult
	0,  // 52: vc.svcreview.ReviewScriptReq.bizType:type_name -> vc.svcreview.ReviewBizType
	6,  // 53: vc.svcreview.ReviewScriptReq.voice:type_name -> vc.svcreview.ReviewVoice
	43, // 54: vc.svcreview.ReviewScriptReq.callback_data:type_name -> vc.svcreview.ReviewScriptCallbackData
	0,  // 55: vc.svcreview.ReviewScriptCallbackData.type:type_name -> vc.svcreview.ReviewBizType
	2,  // 56: vc.svcreview.ReviewScriptCallbackData.result:type_name -> vc.svcreview.AuditResult
	49, // 57: vc.svcreview.GetReviewLogResp.base:type_name -> common.SvcBaseResp
	48, // 58: vc.svcreview.GetReviewLogResp.log:type_name -> vc.svcreview.ReviewLogInfo
	49, // 59: vc.svcreview.GetReviewLogsResp.base:type_name -> common.SvcBaseResp
	48, // 60: vc.svcreview.GetReviewLogsResp.logs:type_name -> vc.svcreview.ReviewLogInfo
	33, // 61: vc.svcreview.s.ReviewCallback:input_type -> vc.svcreview.ReviewCallbackReq
	29, // 62: vc.svcreview.s.ReviewMemberInfo:input_type -> vc.svcreview.ReviewMemberInfoReq
	42, // 63: vc.svcreview.s.ReviewScript:input_type -> vc.svcreview.ReviewScriptReq
	8,  // 64: vc.svcreview.s.ShumeiAudioCallback:input_type -> vc.svcreview.ShumeiAudioCallbackReq
	44, // 65: vc.svcreview.s.GetReviewLog:input_type -> vc.svcreview.GetReviewLogReq
	46, // 66: vc.svcreview.s.GetReviewLogs:input_type -> vc.svcreview.GetReviewLogsReq
	51, // 67: vc.svcreview.s.ReviewCallback:output_type -> common.SvcCommonResp
	13, // 68: vc.svcreview.s.ReviewMemberInfo:output_type -> vc.svcreview.ReviewCommonResp
	13, // 69: vc.svcreview.s.ReviewScript:output_type -> vc.svcreview.ReviewCommonResp
	51, // 70: vc.svcreview.s.ShumeiAudioCallback:output_type -> common.SvcCommonResp
	45, // 71: vc.svcreview.s.GetReviewLog:output_type -> vc.svcreview.GetReviewLogResp
	47, // 72: vc.svcreview.s.GetReviewLogs:output_type -> vc.svcreview.GetReviewLogsResp
	67, // [67:73] is the sub-list for method output_type
	61, // [61:67] is the sub-list for method input_type
	61, // [61:61] is the sub-list for extension type_name
	61, // [61:61] is the sub-list for extension extendee
	0,  // [0:61] is the sub-list for field type_name
}

func init() { file_svcreview_proto_init() }
func file_svcreview_proto_init() {
	if File_svcreview_proto != nil {
		return
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: unsafe.Slice(unsafe.StringData(file_svcreview_proto_rawDesc), len(file_svcreview_proto_rawDesc)),
			NumEnums:      5,
			NumMessages:   44,
			NumExtensions: 0,
			NumServices:   1,
		},
		GoTypes:           file_svcreview_proto_goTypes,
		DependencyIndexes: file_svcreview_proto_depIdxs,
		EnumInfos:         file_svcreview_proto_enumTypes,
		MessageInfos:      file_svcreview_proto_msgTypes,
	}.Build()
	File_svcreview_proto = out.File
	file_svcreview_proto_goTypes = nil
	file_svcreview_proto_depIdxs = nil
}
