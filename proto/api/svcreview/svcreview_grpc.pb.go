// Code generated by protoc-gen-go-grpc. DO NOT EDIT.
// versions:
// - protoc-gen-go-grpc v1.5.1
// - protoc             v5.29.3
// source: svcreview.proto

package svcreview

import (
	context "context"
	grpc "google.golang.org/grpc"
	codes "google.golang.org/grpc/codes"
	status "google.golang.org/grpc/status"
	common "new-gitlab.xunlei.cn/vcproject/backends/proto/api/common"
)

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
// Requires gRPC-Go v1.64.0 or later.
const _ = grpc.SupportPackageIsVersion9

const (
	S_ReviewCallback_FullMethodName      = "/vc.svcreview.s/ReviewCallback"
	S_ReviewMemberInfo_FullMethodName    = "/vc.svcreview.s/ReviewMemberInfo"
	S_ReviewScript_FullMethodName        = "/vc.svcreview.s/ReviewScript"
	S_ShumeiAudioCallback_FullMethodName = "/vc.svcreview.s/ShumeiAudioCallback"
	S_GetReviewLog_FullMethodName        = "/vc.svcreview.s/GetReviewLog"
	S_GetReviewLogs_FullMethodName       = "/vc.svcreview.s/GetReviewLogs"
)

// SClient is the client API for S service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://pkg.go.dev/google.golang.org/grpc/?tab=doc#ClientConn.NewStream.
//
// 审核服务
type SClient interface {
	// 数美 audio 回调
	ReviewCallback(ctx context.Context, in *ReviewCallbackReq, opts ...grpc.CallOption) (*common.SvcCommonResp, error)
	ReviewMemberInfo(ctx context.Context, in *ReviewMemberInfoReq, opts ...grpc.CallOption) (*ReviewCommonResp, error)
	//	rpc ReviewMoment (ReviewMomentReq) returns (common.SvcCommonResp) {}
	//
	// 剧本审核
	ReviewScript(ctx context.Context, in *ReviewScriptReq, opts ...grpc.CallOption) (*ReviewCommonResp, error)
	ShumeiAudioCallback(ctx context.Context, in *ShumeiAudioCallbackReq, opts ...grpc.CallOption) (*common.SvcCommonResp, error)
	// 查询审核记录
	GetReviewLog(ctx context.Context, in *GetReviewLogReq, opts ...grpc.CallOption) (*GetReviewLogResp, error)
	GetReviewLogs(ctx context.Context, in *GetReviewLogsReq, opts ...grpc.CallOption) (*GetReviewLogsResp, error)
}

type sClient struct {
	cc grpc.ClientConnInterface
}

func NewSClient(cc grpc.ClientConnInterface) SClient {
	return &sClient{cc}
}

func (c *sClient) ReviewCallback(ctx context.Context, in *ReviewCallbackReq, opts ...grpc.CallOption) (*common.SvcCommonResp, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(common.SvcCommonResp)
	err := c.cc.Invoke(ctx, S_ReviewCallback_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *sClient) ReviewMemberInfo(ctx context.Context, in *ReviewMemberInfoReq, opts ...grpc.CallOption) (*ReviewCommonResp, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(ReviewCommonResp)
	err := c.cc.Invoke(ctx, S_ReviewMemberInfo_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *sClient) ReviewScript(ctx context.Context, in *ReviewScriptReq, opts ...grpc.CallOption) (*ReviewCommonResp, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(ReviewCommonResp)
	err := c.cc.Invoke(ctx, S_ReviewScript_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *sClient) ShumeiAudioCallback(ctx context.Context, in *ShumeiAudioCallbackReq, opts ...grpc.CallOption) (*common.SvcCommonResp, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(common.SvcCommonResp)
	err := c.cc.Invoke(ctx, S_ShumeiAudioCallback_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *sClient) GetReviewLog(ctx context.Context, in *GetReviewLogReq, opts ...grpc.CallOption) (*GetReviewLogResp, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(GetReviewLogResp)
	err := c.cc.Invoke(ctx, S_GetReviewLog_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *sClient) GetReviewLogs(ctx context.Context, in *GetReviewLogsReq, opts ...grpc.CallOption) (*GetReviewLogsResp, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(GetReviewLogsResp)
	err := c.cc.Invoke(ctx, S_GetReviewLogs_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// SServer is the server API for S service.
// All implementations should embed UnimplementedSServer
// for forward compatibility.
//
// 审核服务
type SServer interface {
	// 数美 audio 回调
	ReviewCallback(context.Context, *ReviewCallbackReq) (*common.SvcCommonResp, error)
	ReviewMemberInfo(context.Context, *ReviewMemberInfoReq) (*ReviewCommonResp, error)
	//	rpc ReviewMoment (ReviewMomentReq) returns (common.SvcCommonResp) {}
	//
	// 剧本审核
	ReviewScript(context.Context, *ReviewScriptReq) (*ReviewCommonResp, error)
	ShumeiAudioCallback(context.Context, *ShumeiAudioCallbackReq) (*common.SvcCommonResp, error)
	// 查询审核记录
	GetReviewLog(context.Context, *GetReviewLogReq) (*GetReviewLogResp, error)
	GetReviewLogs(context.Context, *GetReviewLogsReq) (*GetReviewLogsResp, error)
}

// UnimplementedSServer should be embedded to have
// forward compatible implementations.
//
// NOTE: this should be embedded by value instead of pointer to avoid a nil
// pointer dereference when methods are called.
type UnimplementedSServer struct{}

func (UnimplementedSServer) ReviewCallback(context.Context, *ReviewCallbackReq) (*common.SvcCommonResp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ReviewCallback not implemented")
}
func (UnimplementedSServer) ReviewMemberInfo(context.Context, *ReviewMemberInfoReq) (*ReviewCommonResp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ReviewMemberInfo not implemented")
}
func (UnimplementedSServer) ReviewScript(context.Context, *ReviewScriptReq) (*ReviewCommonResp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ReviewScript not implemented")
}
func (UnimplementedSServer) ShumeiAudioCallback(context.Context, *ShumeiAudioCallbackReq) (*common.SvcCommonResp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ShumeiAudioCallback not implemented")
}
func (UnimplementedSServer) GetReviewLog(context.Context, *GetReviewLogReq) (*GetReviewLogResp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetReviewLog not implemented")
}
func (UnimplementedSServer) GetReviewLogs(context.Context, *GetReviewLogsReq) (*GetReviewLogsResp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetReviewLogs not implemented")
}
func (UnimplementedSServer) testEmbeddedByValue() {}

// UnsafeSServer may be embedded to opt out of forward compatibility for this service.
// Use of this interface is not recommended, as added methods to SServer will
// result in compilation errors.
type UnsafeSServer interface {
	mustEmbedUnimplementedSServer()
}

func RegisterSServer(s grpc.ServiceRegistrar, srv SServer) {
	// If the following call pancis, it indicates UnimplementedSServer was
	// embedded by pointer and is nil.  This will cause panics if an
	// unimplemented method is ever invoked, so we test this at initialization
	// time to prevent it from happening at runtime later due to I/O.
	if t, ok := srv.(interface{ testEmbeddedByValue() }); ok {
		t.testEmbeddedByValue()
	}
	s.RegisterService(&S_ServiceDesc, srv)
}

func _S_ReviewCallback_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ReviewCallbackReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(SServer).ReviewCallback(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: S_ReviewCallback_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(SServer).ReviewCallback(ctx, req.(*ReviewCallbackReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _S_ReviewMemberInfo_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ReviewMemberInfoReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(SServer).ReviewMemberInfo(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: S_ReviewMemberInfo_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(SServer).ReviewMemberInfo(ctx, req.(*ReviewMemberInfoReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _S_ReviewScript_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ReviewScriptReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(SServer).ReviewScript(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: S_ReviewScript_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(SServer).ReviewScript(ctx, req.(*ReviewScriptReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _S_ShumeiAudioCallback_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ShumeiAudioCallbackReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(SServer).ShumeiAudioCallback(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: S_ShumeiAudioCallback_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(SServer).ShumeiAudioCallback(ctx, req.(*ShumeiAudioCallbackReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _S_GetReviewLog_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetReviewLogReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(SServer).GetReviewLog(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: S_GetReviewLog_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(SServer).GetReviewLog(ctx, req.(*GetReviewLogReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _S_GetReviewLogs_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetReviewLogsReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(SServer).GetReviewLogs(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: S_GetReviewLogs_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(SServer).GetReviewLogs(ctx, req.(*GetReviewLogsReq))
	}
	return interceptor(ctx, in, info, handler)
}

// S_ServiceDesc is the grpc.ServiceDesc for S service.
// It's only intended for direct use with grpc.RegisterService,
// and not to be introspected or modified (even as a copy)
var S_ServiceDesc = grpc.ServiceDesc{
	ServiceName: "vc.svcreview.s",
	HandlerType: (*SServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "ReviewCallback",
			Handler:    _S_ReviewCallback_Handler,
		},
		{
			MethodName: "ReviewMemberInfo",
			Handler:    _S_ReviewMemberInfo_Handler,
		},
		{
			MethodName: "ReviewScript",
			Handler:    _S_ReviewScript_Handler,
		},
		{
			MethodName: "ShumeiAudioCallback",
			Handler:    _S_ShumeiAudioCallback_Handler,
		},
		{
			MethodName: "GetReviewLog",
			Handler:    _S_GetReviewLog_Handler,
		},
		{
			MethodName: "GetReviewLogs",
			Handler:    _S_GetReviewLogs_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "svcreview.proto",
}
