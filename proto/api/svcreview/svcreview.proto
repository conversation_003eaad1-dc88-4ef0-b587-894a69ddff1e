syntax = "proto3";

package vc.svcreview;
option go_package = "new-gitlab.xunlei.cn/vcproject/backends/proto/api/svcreview;svcreview";

import "common/common.proto";

enum ReviewBizType {
  unknown = 0;
  UserNickname = 1; // 用户昵称
  UserAvatar = 2; // 用户头像
  UserSignVoice = 3;  // 用户签名语音
  UserTextSign = 4; // 用户签名文字
  UserAlbum = 5; // 用户相册
  Moment = 6;
  Comment = 7;
  RoomTitle = 8;
  RoomNotice = 9;
  RoomCover = 10;
  RoomBg = 11;
  ScriptTitle = 12; //剧本标题
  ScriptCover = 13; //剧本封面
  ScriptLine = 14;  //剧本台词
  ScriptTopic = 15; //剧本话题
  ScriptDubbingText = 16; //剧本配音文本
  ScriptDubbingVoice = 17; //剧本配音语音
  ScriptCommentText = 18; //剧本评论文本
  ScriptCommentVoice = 19; //剧本评论语音
}

enum ReviewMemberType {
  review_unknown = 0;
  avatar = 1; // 1头像
  album = 2; // 2相册
  other = 3; // 3其它字段
}

// 审核结果
enum AuditResult {
  // 默认，不使用
  audit_result_unknown = 0;
  // 通过
  pass = 1;
  // 拒绝
  reject = 2;
  // 审核中
  review = 3;
  // 伪发送 (命中了拒绝并且一级标签是广告)
  fake_send = 4;
}

enum AuditSubResult {
  // 默认，不使用
  audit_sub_unknown = 0;
  // 联系方式拦截 男用户亲密的等级过低
  audit_sub_male_lianxifangshi = 1;
}

// shield 结果
enum ShieldResult {
  shield_unknown = 0; // 默认，不使用
  shield_pass = 1; //通过
  shield_reject=2; //拒绝
  shield_review=3; //
}

message ReviewImage {
  string id = 1;
  string url = 2;
}

message ReviewVoice {
  string id = 1;
  int64 sec = 2;
  string url = 3;
}

message ReviewVideo {
  string id = 1;
  int64 sec = 2;
  string url = 3;
}

message ShumeiAudioCallbackReq {
  int32 code = 1;
  string message = 2;
  string requestId = 3;
  string btId = 4;
  string audioText = 5;
  int32 audioTime = 6;
  string labels = 7;
  string riskLevel = 8;
  repeated ShumeiAudioDetail detail = 9;
  ShumeiAudioGender gender = 10;
  repeated ShumeiAudioTags tags = 11;
  ReviewCallbackOrigin  callbackParam = 12;
}

message ShumeiAudioDetail {
  int32 audioStarttime = 1;
  int32 audioEndtime = 2;
  string audioUrl = 3;
  string audioText = 4;
  string riskLevel = 5;
  int32 riskType = 6;
  string audioMatchedItem = 7;
  string description = 8;
}

message ShumeiAudioGender {
  string label = 1;
  int32 confidence = 2;
}

message ShumeiAudioTags {
  string label = 1;
  int32 confidence = 2;
}

message ReviewCallbackOrigin {
  int64 seqid = 1;
  string content = 2;
  string biz_type = 3;
}

//--

message ReviewCommonResp {
  common.SvcBaseResp base = 1;
  AuditResult result = 2;
  string reason = 3;
  ReviewResultDetail detail = 4;
}

message ReviewResultDetail {
  AuditResult result = 1;
  ReviewTextResult text = 4;
  repeated ReviewImageResult image = 5;
  ReviewVoiceResult voice = 6;
  ReviewVideoResult video = 7;
  bool noneed_push_tips = 8;
}

message ReviewImageResult {
  ReviewImage image = 1;
  AuditResult result = 2;
  string reason = 3;
}

message ReviewVoiceResult {
  ReviewVoice voice = 1;
  AuditResult result = 2;
  string reason = 3;
}

message ReviewVideoResult {
  ReviewVideo video = 1;
  AuditResult result = 2;
  string reason = 3;
}

message ReviewTextResult {
  string text = 1;
  AuditResult result = 2;
  string reason = 3;
}

message ShumeiMixCallbackReq {
  string btId = 1;
  int32 code = 2;
  string requestId = 3;
  string riskLevel = 4;
  int32 resultType = 5; //0：机审，1：人审
  ShumeiMixDetail details = 6;
  ReviewCallbackData  passThrough = 7;
}

message ShumeiMixDetail {
  repeated ShumeiMixTextDetail  texts = 1;
  repeated ShumeiMixImgDetail  images = 2;
  //string audios = 3;
  //string videos = 4;
}

message ShumeiMixTextDetail {
  int32 code =1;
  string message = 2;
  string requestId = 3;
  string dataId = 4;
  string btId = 5;
  string riskLevel =6; //PASS：正常 REVIEW：可疑，建议人工审核 REJECT：违规
}

message ShumeiMixImgDetail {
  int32 code =1;
  string message = 2;
  string requestId = 3;
  string dataId = 4;
  string btId = 5;
  string riskLevel =6;
}

message ReviewCallbackData {
  int64 id = 1;
  string biz_type = 2;
}

message ShumeiMultiImageCallbackReq {
  string btId = 1;
  int32 code = 2;
  string requestId = 3;
  repeated ShumeiMultiImage imgs = 6;
  ShumeiMultiImageExtra  auxInfo = 7;
}

message ShumeiMultiImage {
  string btId = 1;
  string riskLevel = 2;
  int32 code = 3;
}

message ShumeiMultiImageExtra {
  ReviewMultiImageCallbackData  passThrough = 1;
}

message ReviewMultiImageCallbackData {
  int64 id = 1;
  string bizType = 2;
  int64 userid = 3;
  int32 sex= 4;
}


message ReviewMemberInfoCallbackData {
  int64 userid = 1;
  int64 id = 2;
  ReviewBizType type = 3;
}

message ReviewMemberInfoReq {
  int64 userid = 1;
  int32 sex = 2; //女用户头像需是真人头像
  ReviewBizType bizType = 3;
  repeated string text = 4;
  repeated ReviewImage images = 5;
  ReviewVoice voice = 6;
  ReviewMemberInfoCallbackData callback_data = 7;
  string xluid = 8; // 注册的头像和昵称审核人审回调使用
  // 国家
  string country = 9;
  string lang = 10;
}

message ReviewMomentReq {
  int64 userid = 1;
  int64 moid = 2;
  string text = 3;
  repeated string images = 4;
  ReviewVoice voice = 5;
  ReviewVideo video = 6;
  ReviewCallbackData callback_data = 7;
  string country = 8;
  string lang = 9;
}

message ReviewUserCommonMsgReq {
  int64 userid = 1;
  int64 id = 2;
  string text = 3;
  ReviewUserCommonMsgCallbackData callback_data = 4;
}

message ReviewUserCommonMsgCallbackData {
  int64 userid = 1;
  int64 id = 2;
}

message ReviewCallbackReq {
  string appId = 1;
  string bizType = 2;
  string auditor = 3;
  string caseId = 4;
  string contentType = 5;
  string dataId = 6;
  string suggestion = 7; //人审建议值. pass: 通过, reject: 拒绝.
  string label = 8; //人审(或机审)标签.
  string label_key = 9; //人审(或机审)标签 key.
  int32 label_code = 10;//人审(或机审)标签 code.
  ReviewCallbackOrigin origin = 11;//scan接口请求时的origin信息
  string secret = 12;
}

message ReviewRoomInfoCallbackData {
  int64 roomid = 1;
  int64 id = 2;
  int32 type = 3; //类型 1 房间封面 2 房间标题 3 房间公告 4房间背景
}

message ReviewRoomInfoReq {
  int64 roomid = 1;
  int64 userid = 2;
  ReviewBizType bizType = 3;
  string text = 4;
  ReviewImage image = 5;
  ReviewRoomInfoCallbackData callback_data = 7;
  // 国家
  string country = 8;
  string lang = 9;
//  basemsgtransfer.SessionType session_type= 10;
}

message ReviewRoomInfoResp {
  common.SvcBaseResp base = 1;
  AuditResult result = 2;
  string reason = 3;
}

message ReviewRoomChatReq {
  int64 userid = 1;
  int64 roomid = 2;
  int64 sid = 3;
//  basemsgtransfer.ContentType msg_data_type = 4;
  int32 sub_type = 5;
  string content = 6;
  string ip = 7;
  // 国家
  string country = 8;
  string lang = 9;
  common.BaseParam base = 10;
}

message ReviewRoomChatResp {
  common.SvcBaseResp base = 1;
  AuditResult result = 2;
  string reason = 3;
}

message ReviewChatCallbackData {
  string sid = 1;
  string msgid = 2;
}

message ReviewChatReq {
  int64 userid = 1;
  string sid = 2;
  string msgId = 3;
//  basemsgtransfer.ContentType msgType = 4; // 1 2 3 4 5
  string text = 5;
  ReviewImage image = 6;
  ReviewVoice voice = 7;
  ReviewVideo video = 8;
  ReviewChatCallbackData callback_data = 9;
  int32 sex = 10;
  int32 sweetRate = 11;
  // oss etag
  string eTag = 12;
  int64 peer_id = 13;
  string ip = 14;
  string country = 15;
  string lang = 16;
  // 关系对会话消息
  int64 count = 17;

  common.BaseParam base = 18;
//  basemsgtransfer.SessionType sessionType = 19;
}

message ReviewChatResp {
  common.SvcBaseResp base = 1;
  int32 sync = 2; // 0 异步 1同步返回结果 (文字和图片使用)
  AuditResult result = 3;
  string reason = 4;
  AuditSubResult sub_result = 5;
}

// 剧本审核
message ReviewScriptReq {
  int64 user_id = 1;
  int64 id = 2;
  ReviewBizType bizType = 3;
  repeated string text = 4;
  repeated string images = 5;
  ReviewVoice voice = 6;
  ReviewScriptCallbackData callback_data = 7;
}

message ReviewScriptCallbackData {
  int64 userid = 1;
  int64 id = 2;
  ReviewBizType type = 3;
  AuditResult result = 4;
}

// 查询审核记录请求
message GetReviewLogReq {
  int64 userid = 1;
  string biz_type = 2;
  int64 biz_id = 3;
}

// 查询审核记录响应
message GetReviewLogResp {
  common.SvcBaseResp base = 1;
  ReviewLogInfo log = 2;
}

// 查询审核记录列表请求
message GetReviewLogsReq {
  int64 userid = 1;
  string biz_type = 2;
  string risk_level = 3;
  int32 page = 4;
  int32 page_size = 5;
}

// 查询审核记录列表响应
message GetReviewLogsResp {
  common.SvcBaseResp base = 1;
  repeated ReviewLogInfo logs = 2;
  int64 total = 3;
}

// 审核记录信息
message ReviewLogInfo {
  int64 id = 1;
  int64 userid = 2;
  string biz_type = 3;
  int64 biz_id = 4;
  string data = 5;
  int32 status = 6;
  string request_id = 7;
  string risk_reason = 8;
  string risk_labels = 9;
  string risk_level = 10;
  string audit_detail = 11;
  int64 ct = 12;
  int64 ut = 13;
}

// 审核服务
service s {
  // 数美 audio 回调
  rpc ReviewCallback (ReviewCallbackReq) returns (common.SvcCommonResp) {}
//  rpc ShumeiMixCallback (ShumeiMixCallbackReq) returns (common.SvcCommonResp) {}
//  rpc ShumeiMultiImageCallback (ShumeiMultiImageCallbackReq) returns (common.SvcCommonResp) {}

  rpc ReviewMemberInfo (ReviewMemberInfoReq) returns (ReviewCommonResp) {}
//  rpc ReviewMoment (ReviewMomentReq) returns (common.SvcCommonResp) {}
  // 剧本审核
  rpc ReviewScript(ReviewScriptReq) returns (ReviewCommonResp) {}

//  rpc ReviewUserCommonMsg(ReviewUserCommonMsgReq) returns (ReviewCommonResp) {}
//  rpc ReviewRoomInfo(ReviewRoomInfoReq) returns (ReviewRoomInfoResp) {}
//  rpc ReviewRoomChat(ReviewRoomChatReq) returns (ReviewRoomChatResp) {}

  rpc ShumeiAudioCallback (ShumeiAudioCallbackReq) returns (common.SvcCommonResp) {}

  // 查询审核记录
  rpc GetReviewLog (GetReviewLogReq) returns (GetReviewLogResp) {}
  rpc GetReviewLogs (GetReviewLogsReq) returns (GetReviewLogsResp) {}
}