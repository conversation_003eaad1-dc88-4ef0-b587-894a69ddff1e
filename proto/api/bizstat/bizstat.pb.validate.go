// Code generated by protoc-gen-validate. DO NOT EDIT.
// source: bizstat.proto

package bizstat

import (
	"bytes"
	"errors"
	"fmt"
	"net"
	"net/mail"
	"net/url"
	"regexp"
	"sort"
	"strings"
	"time"
	"unicode/utf8"

	"google.golang.org/protobuf/types/known/anypb"
)

// ensure the imports are used
var (
	_ = bytes.MinRead
	_ = errors.New("")
	_ = fmt.Print
	_ = utf8.UTFMax
	_ = (*regexp.Regexp)(nil)
	_ = (*strings.Reader)(nil)
	_ = net.IPv4len
	_ = time.Duration(0)
	_ = (*url.URL)(nil)
	_ = (*mail.Address)(nil)
	_ = anypb.Any{}
	_ = sort.Sort
)

// Validate checks the field values on EventPushResp with the rules defined in
// the proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *EventPushResp) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on EventPushResp with the rules defined
// in the proto definition for this message. If any rules are violated, the
// result is a list of violation errors wrapped in EventPushRespMultiError, or
// nil if none found.
func (m *EventPushResp) ValidateAll() error {
	return m.validate(true)
}

func (m *EventPushResp) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Code

	// no validation rules for Msg

	if all {
		switch v := interface{}(m.GetData()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, EventPushRespValidationError{
					field:  "Data",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, EventPushRespValidationError{
					field:  "Data",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetData()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return EventPushRespValidationError{
				field:  "Data",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return EventPushRespMultiError(errors)
	}

	return nil
}

// EventPushRespMultiError is an error wrapping multiple validation errors
// returned by EventPushResp.ValidateAll() if the designated constraints
// aren't met.
type EventPushRespMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m EventPushRespMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m EventPushRespMultiError) AllErrors() []error { return m }

// EventPushRespValidationError is the validation error returned by
// EventPushResp.Validate if the designated constraints aren't met.
type EventPushRespValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e EventPushRespValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e EventPushRespValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e EventPushRespValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e EventPushRespValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e EventPushRespValidationError) ErrorName() string { return "EventPushRespValidationError" }

// Error satisfies the builtin error interface
func (e EventPushRespValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sEventPushResp.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = EventPushRespValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = EventPushRespValidationError{}

// Validate checks the field values on EventPushRespData with the rules defined
// in the proto definition for this message. If any rules are violated, the
// first error encountered is returned, or nil if there are no violations.
func (m *EventPushRespData) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on EventPushRespData with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// EventPushRespDataMultiError, or nil if none found.
func (m *EventPushRespData) ValidateAll() error {
	return m.validate(true)
}

func (m *EventPushRespData) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if len(errors) > 0 {
		return EventPushRespDataMultiError(errors)
	}

	return nil
}

// EventPushRespDataMultiError is an error wrapping multiple validation errors
// returned by EventPushRespData.ValidateAll() if the designated constraints
// aren't met.
type EventPushRespDataMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m EventPushRespDataMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m EventPushRespDataMultiError) AllErrors() []error { return m }

// EventPushRespDataValidationError is the validation error returned by
// EventPushRespData.Validate if the designated constraints aren't met.
type EventPushRespDataValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e EventPushRespDataValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e EventPushRespDataValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e EventPushRespDataValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e EventPushRespDataValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e EventPushRespDataValidationError) ErrorName() string {
	return "EventPushRespDataValidationError"
}

// Error satisfies the builtin error interface
func (e EventPushRespDataValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sEventPushRespData.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = EventPushRespDataValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = EventPushRespDataValidationError{}
