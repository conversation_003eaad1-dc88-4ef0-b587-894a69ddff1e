syntax = "proto3";

package vc.bizstat;
option go_package = "new-gitlab.xunlei.cn/vcproject/backends/proto/api/bizstat;bizstat";

import "google/api/annotations.proto";
import "svcstat/svcstat.proto";
  
  message EventPushResp {
    int32 code=1;
    string msg=2;
    EventPushRespData data=3;
  }
  
  message EventPushRespData {
     repeated string list=1;
  }


service s {
    // app，前端消息上报
    rpc PushEvent(svcstat.EventPushReq) returns (EventPushResp) {
      option (google.api.http) = {
        post: "/vc.bizstat.s/v1/event_report"
        body: "*"
      };
    }
}
  
