// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.31.0
// 	protoc        v3.21.4
// source: bizstat.proto

package bizstat

import (
	_ "google.golang.org/genproto/googleapis/api/annotations"
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	svcstat "new-gitlab.xunlei.cn/vcproject/backends/proto/api/svcstat"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type EventPushResp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Code int32              `protobuf:"varint,1,opt,name=code,proto3" json:"code,omitempty"`
	Msg  string             `protobuf:"bytes,2,opt,name=msg,proto3" json:"msg,omitempty"`
	Data *EventPushRespData `protobuf:"bytes,3,opt,name=data,proto3" json:"data,omitempty"`
}

func (x *EventPushResp) Reset() {
	*x = EventPushResp{}
	if protoimpl.UnsafeEnabled {
		mi := &file_bizstat_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *EventPushResp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*EventPushResp) ProtoMessage() {}

func (x *EventPushResp) ProtoReflect() protoreflect.Message {
	mi := &file_bizstat_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use EventPushResp.ProtoReflect.Descriptor instead.
func (*EventPushResp) Descriptor() ([]byte, []int) {
	return file_bizstat_proto_rawDescGZIP(), []int{0}
}

func (x *EventPushResp) GetCode() int32 {
	if x != nil {
		return x.Code
	}
	return 0
}

func (x *EventPushResp) GetMsg() string {
	if x != nil {
		return x.Msg
	}
	return ""
}

func (x *EventPushResp) GetData() *EventPushRespData {
	if x != nil {
		return x.Data
	}
	return nil
}

type EventPushRespData struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	List []string `protobuf:"bytes,1,rep,name=list,proto3" json:"list,omitempty"`
}

func (x *EventPushRespData) Reset() {
	*x = EventPushRespData{}
	if protoimpl.UnsafeEnabled {
		mi := &file_bizstat_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *EventPushRespData) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*EventPushRespData) ProtoMessage() {}

func (x *EventPushRespData) ProtoReflect() protoreflect.Message {
	mi := &file_bizstat_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use EventPushRespData.ProtoReflect.Descriptor instead.
func (*EventPushRespData) Descriptor() ([]byte, []int) {
	return file_bizstat_proto_rawDescGZIP(), []int{1}
}

func (x *EventPushRespData) GetList() []string {
	if x != nil {
		return x.List
	}
	return nil
}

var File_bizstat_proto protoreflect.FileDescriptor

var file_bizstat_proto_rawDesc = []byte{
	0x0a, 0x0d, 0x62, 0x69, 0x7a, 0x73, 0x74, 0x61, 0x74, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x12,
	0x0a, 0x76, 0x63, 0x2e, 0x62, 0x69, 0x7a, 0x73, 0x74, 0x61, 0x74, 0x1a, 0x1c, 0x67, 0x6f, 0x6f,
	0x67, 0x6c, 0x65, 0x2f, 0x61, 0x70, 0x69, 0x2f, 0x61, 0x6e, 0x6e, 0x6f, 0x74, 0x61, 0x74, 0x69,
	0x6f, 0x6e, 0x73, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x15, 0x73, 0x76, 0x63, 0x73, 0x74,
	0x61, 0x74, 0x2f, 0x73, 0x76, 0x63, 0x73, 0x74, 0x61, 0x74, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f,
	0x22, 0x68, 0x0a, 0x0d, 0x45, 0x76, 0x65, 0x6e, 0x74, 0x50, 0x75, 0x73, 0x68, 0x52, 0x65, 0x73,
	0x70, 0x12, 0x12, 0x0a, 0x04, 0x63, 0x6f, 0x64, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x05, 0x52,
	0x04, 0x63, 0x6f, 0x64, 0x65, 0x12, 0x10, 0x0a, 0x03, 0x6d, 0x73, 0x67, 0x18, 0x02, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x03, 0x6d, 0x73, 0x67, 0x12, 0x31, 0x0a, 0x04, 0x64, 0x61, 0x74, 0x61, 0x18,
	0x03, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1d, 0x2e, 0x76, 0x63, 0x2e, 0x62, 0x69, 0x7a, 0x73, 0x74,
	0x61, 0x74, 0x2e, 0x45, 0x76, 0x65, 0x6e, 0x74, 0x50, 0x75, 0x73, 0x68, 0x52, 0x65, 0x73, 0x70,
	0x44, 0x61, 0x74, 0x61, 0x52, 0x04, 0x64, 0x61, 0x74, 0x61, 0x22, 0x27, 0x0a, 0x11, 0x45, 0x76,
	0x65, 0x6e, 0x74, 0x50, 0x75, 0x73, 0x68, 0x52, 0x65, 0x73, 0x70, 0x44, 0x61, 0x74, 0x61, 0x12,
	0x12, 0x0a, 0x04, 0x6c, 0x69, 0x73, 0x74, 0x18, 0x01, 0x20, 0x03, 0x28, 0x09, 0x52, 0x04, 0x6c,
	0x69, 0x73, 0x74, 0x32, 0x6f, 0x0a, 0x01, 0x73, 0x12, 0x6a, 0x0a, 0x09, 0x50, 0x75, 0x73, 0x68,
	0x45, 0x76, 0x65, 0x6e, 0x74, 0x12, 0x18, 0x2e, 0x76, 0x63, 0x2e, 0x73, 0x76, 0x63, 0x73, 0x74,
	0x61, 0x74, 0x2e, 0x45, 0x76, 0x65, 0x6e, 0x74, 0x50, 0x75, 0x73, 0x68, 0x52, 0x65, 0x71, 0x1a,
	0x19, 0x2e, 0x76, 0x63, 0x2e, 0x62, 0x69, 0x7a, 0x73, 0x74, 0x61, 0x74, 0x2e, 0x45, 0x76, 0x65,
	0x6e, 0x74, 0x50, 0x75, 0x73, 0x68, 0x52, 0x65, 0x73, 0x70, 0x22, 0x28, 0x82, 0xd3, 0xe4, 0x93,
	0x02, 0x22, 0x3a, 0x01, 0x2a, 0x22, 0x1d, 0x2f, 0x76, 0x63, 0x2e, 0x62, 0x69, 0x7a, 0x73, 0x74,
	0x61, 0x74, 0x2e, 0x73, 0x2f, 0x76, 0x31, 0x2f, 0x65, 0x76, 0x65, 0x6e, 0x74, 0x5f, 0x72, 0x65,
	0x70, 0x6f, 0x72, 0x74, 0x42, 0x43, 0x5a, 0x41, 0x6e, 0x65, 0x77, 0x2d, 0x67, 0x69, 0x74, 0x6c,
	0x61, 0x62, 0x2e, 0x78, 0x75, 0x6e, 0x6c, 0x65, 0x69, 0x2e, 0x63, 0x6e, 0x2f, 0x76, 0x63, 0x70,
	0x72, 0x6f, 0x6a, 0x65, 0x63, 0x74, 0x2f, 0x62, 0x61, 0x63, 0x6b, 0x65, 0x6e, 0x64, 0x73, 0x2f,
	0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2f, 0x61, 0x70, 0x69, 0x2f, 0x62, 0x69, 0x7a, 0x73, 0x74, 0x61,
	0x74, 0x3b, 0x62, 0x69, 0x7a, 0x73, 0x74, 0x61, 0x74, 0x62, 0x06, 0x70, 0x72, 0x6f, 0x74, 0x6f,
	0x33,
}

var (
	file_bizstat_proto_rawDescOnce sync.Once
	file_bizstat_proto_rawDescData = file_bizstat_proto_rawDesc
)

func file_bizstat_proto_rawDescGZIP() []byte {
	file_bizstat_proto_rawDescOnce.Do(func() {
		file_bizstat_proto_rawDescData = protoimpl.X.CompressGZIP(file_bizstat_proto_rawDescData)
	})
	return file_bizstat_proto_rawDescData
}

var file_bizstat_proto_msgTypes = make([]protoimpl.MessageInfo, 2)
var file_bizstat_proto_goTypes = []interface{}{
	(*EventPushResp)(nil),        // 0: vc.bizstat.EventPushResp
	(*EventPushRespData)(nil),    // 1: vc.bizstat.EventPushRespData
	(*svcstat.EventPushReq)(nil), // 2: vc.svcstat.EventPushReq
}
var file_bizstat_proto_depIdxs = []int32{
	1, // 0: vc.bizstat.EventPushResp.data:type_name -> vc.bizstat.EventPushRespData
	2, // 1: vc.bizstat.s.PushEvent:input_type -> vc.svcstat.EventPushReq
	0, // 2: vc.bizstat.s.PushEvent:output_type -> vc.bizstat.EventPushResp
	2, // [2:3] is the sub-list for method output_type
	1, // [1:2] is the sub-list for method input_type
	1, // [1:1] is the sub-list for extension type_name
	1, // [1:1] is the sub-list for extension extendee
	0, // [0:1] is the sub-list for field type_name
}

func init() { file_bizstat_proto_init() }
func file_bizstat_proto_init() {
	if File_bizstat_proto != nil {
		return
	}
	if !protoimpl.UnsafeEnabled {
		file_bizstat_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*EventPushResp); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_bizstat_proto_msgTypes[1].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*EventPushRespData); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_bizstat_proto_rawDesc,
			NumEnums:      0,
			NumMessages:   2,
			NumExtensions: 0,
			NumServices:   1,
		},
		GoTypes:           file_bizstat_proto_goTypes,
		DependencyIndexes: file_bizstat_proto_depIdxs,
		MessageInfos:      file_bizstat_proto_msgTypes,
	}.Build()
	File_bizstat_proto = out.File
	file_bizstat_proto_rawDesc = nil
	file_bizstat_proto_goTypes = nil
	file_bizstat_proto_depIdxs = nil
}
