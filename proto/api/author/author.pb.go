// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.34.2
// 	protoc        v4.24.4
// source: author/author.proto

package author

import (
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type AuthorInfo struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Mid     int64  `protobuf:"varint,1,opt,name=mid,proto3" json:"mid,omitempty"`
	Sex     int32  `protobuf:"varint,2,opt,name=sex,proto3" json:"sex,omitempty"`
	Ip      string `protobuf:"bytes,3,opt,name=ip,proto3" json:"ip,omitempty"`
	RegTime int64  `protobuf:"varint,4,opt,name=reg_time,json=regTime,proto3" json:"reg_time,omitempty"`
}

func (x *AuthorInfo) Reset() {
	*x = AuthorInfo{}
	if protoimpl.UnsafeEnabled {
		mi := &file_author_author_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *AuthorInfo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*AuthorInfo) ProtoMessage() {}

func (x *AuthorInfo) ProtoReflect() protoreflect.Message {
	mi := &file_author_author_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use AuthorInfo.ProtoReflect.Descriptor instead.
func (*AuthorInfo) Descriptor() ([]byte, []int) {
	return file_author_author_proto_rawDescGZIP(), []int{0}
}

func (x *AuthorInfo) GetMid() int64 {
	if x != nil {
		return x.Mid
	}
	return 0
}

func (x *AuthorInfo) GetSex() int32 {
	if x != nil {
		return x.Sex
	}
	return 0
}

func (x *AuthorInfo) GetIp() string {
	if x != nil {
		return x.Ip
	}
	return ""
}

func (x *AuthorInfo) GetRegTime() int64 {
	if x != nil {
		return x.RegTime
	}
	return 0
}

var File_author_author_proto protoreflect.FileDescriptor

var file_author_author_proto_rawDesc = []byte{
	0x0a, 0x13, 0x61, 0x75, 0x74, 0x68, 0x6f, 0x72, 0x2f, 0x61, 0x75, 0x74, 0x68, 0x6f, 0x72, 0x2e,
	0x70, 0x72, 0x6f, 0x74, 0x6f, 0x12, 0x06, 0x61, 0x75, 0x74, 0x68, 0x6f, 0x72, 0x22, 0x5b, 0x0a,
	0x0a, 0x41, 0x75, 0x74, 0x68, 0x6f, 0x72, 0x49, 0x6e, 0x66, 0x6f, 0x12, 0x10, 0x0a, 0x03, 0x6d,
	0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x52, 0x03, 0x6d, 0x69, 0x64, 0x12, 0x10, 0x0a,
	0x03, 0x73, 0x65, 0x78, 0x18, 0x02, 0x20, 0x01, 0x28, 0x05, 0x52, 0x03, 0x73, 0x65, 0x78, 0x12,
	0x0e, 0x0a, 0x02, 0x69, 0x70, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x02, 0x69, 0x70, 0x12,
	0x19, 0x0a, 0x08, 0x72, 0x65, 0x67, 0x5f, 0x74, 0x69, 0x6d, 0x65, 0x18, 0x04, 0x20, 0x01, 0x28,
	0x03, 0x52, 0x07, 0x72, 0x65, 0x67, 0x54, 0x69, 0x6d, 0x65, 0x42, 0x41, 0x5a, 0x3f, 0x6e, 0x65,
	0x77, 0x2d, 0x67, 0x69, 0x74, 0x6c, 0x61, 0x62, 0x2e, 0x78, 0x75, 0x6e, 0x6c, 0x65, 0x69, 0x2e,
	0x63, 0x6e, 0x2f, 0x76, 0x63, 0x70, 0x72, 0x6f, 0x6a, 0x65, 0x63, 0x74, 0x2f, 0x62, 0x61, 0x63,
	0x6b, 0x65, 0x6e, 0x64, 0x73, 0x2f, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2f, 0x61, 0x70, 0x69, 0x2f,
	0x61, 0x75, 0x74, 0x68, 0x6f, 0x72, 0x3b, 0x61, 0x75, 0x74, 0x68, 0x6f, 0x72, 0x62, 0x06, 0x70,
	0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_author_author_proto_rawDescOnce sync.Once
	file_author_author_proto_rawDescData = file_author_author_proto_rawDesc
)

func file_author_author_proto_rawDescGZIP() []byte {
	file_author_author_proto_rawDescOnce.Do(func() {
		file_author_author_proto_rawDescData = protoimpl.X.CompressGZIP(file_author_author_proto_rawDescData)
	})
	return file_author_author_proto_rawDescData
}

var file_author_author_proto_msgTypes = make([]protoimpl.MessageInfo, 1)
var file_author_author_proto_goTypes = []any{
	(*AuthorInfo)(nil), // 0: author.AuthorInfo
}
var file_author_author_proto_depIdxs = []int32{
	0, // [0:0] is the sub-list for method output_type
	0, // [0:0] is the sub-list for method input_type
	0, // [0:0] is the sub-list for extension type_name
	0, // [0:0] is the sub-list for extension extendee
	0, // [0:0] is the sub-list for field type_name
}

func init() { file_author_author_proto_init() }
func file_author_author_proto_init() {
	if File_author_author_proto != nil {
		return
	}
	if !protoimpl.UnsafeEnabled {
		file_author_author_proto_msgTypes[0].Exporter = func(v any, i int) any {
			switch v := v.(*AuthorInfo); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_author_author_proto_rawDesc,
			NumEnums:      0,
			NumMessages:   1,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_author_author_proto_goTypes,
		DependencyIndexes: file_author_author_proto_depIdxs,
		MessageInfos:      file_author_author_proto_msgTypes,
	}.Build()
	File_author_author_proto = out.File
	file_author_author_proto_rawDesc = nil
	file_author_author_proto_goTypes = nil
	file_author_author_proto_depIdxs = nil
}
