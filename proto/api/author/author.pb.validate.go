// Code generated by protoc-gen-validate. DO NOT EDIT.
// source: author/author.proto

package author

import (
	"bytes"
	"errors"
	"fmt"
	"net"
	"net/mail"
	"net/url"
	"regexp"
	"sort"
	"strings"
	"time"
	"unicode/utf8"

	"google.golang.org/protobuf/types/known/anypb"
)

// ensure the imports are used
var (
	_ = bytes.MinRead
	_ = errors.New("")
	_ = fmt.Print
	_ = utf8.UTFMax
	_ = (*regexp.Regexp)(nil)
	_ = (*strings.Reader)(nil)
	_ = net.IPv4len
	_ = time.Duration(0)
	_ = (*url.URL)(nil)
	_ = (*mail.Address)(nil)
	_ = anypb.Any{}
	_ = sort.Sort
)

// Validate checks the field values on AuthorInfo with the rules defined in the
// proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *AuthorInfo) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on AuthorInfo with the rules defined in
// the proto definition for this message. If any rules are violated, the
// result is a list of violation errors wrapped in AuthorInfoMultiError, or
// nil if none found.
func (m *AuthorInfo) ValidateAll() error {
	return m.validate(true)
}

func (m *AuthorInfo) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for UserId

	// no validation rules for Gender

	// no validation rules for Ip

	// no validation rules for RegTime

	if len(errors) > 0 {
		return AuthorInfoMultiError(errors)
	}

	return nil
}

// AuthorInfoMultiError is an error wrapping multiple validation errors
// returned by AuthorInfo.ValidateAll() if the designated constraints aren't met.
type AuthorInfoMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m AuthorInfoMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m AuthorInfoMultiError) AllErrors() []error { return m }

// AuthorInfoValidationError is the validation error returned by
// AuthorInfo.Validate if the designated constraints aren't met.
type AuthorInfoValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e AuthorInfoValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e AuthorInfoValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e AuthorInfoValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e AuthorInfoValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e AuthorInfoValidationError) ErrorName() string { return "AuthorInfoValidationError" }

// Error satisfies the builtin error interface
func (e AuthorInfoValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sAuthorInfo.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = AuthorInfoValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = AuthorInfoValidationError{}
