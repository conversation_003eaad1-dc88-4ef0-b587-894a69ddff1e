syntax = "proto3";

package vc.bizmoment;

import "google/api/annotations.proto";
import "protoc-gen-validate/validate/validate.proto";
import "svcmoment/svcmoment.proto";

option go_package = "new-gitlab.xunlei.cn/vcproject/backends/proto/api/bizmoment";

// GetCharacterListReq 获取角色列表请求
message GetCharacterListReq {
	int32 page = 1;      // 页码
	int32 page_size = 2; // 每页数量
}

// GetCharacterListResp 获取角色列表响应
message GetCharacterListResp {
	int32 code = 1;      // 错误码
	string msg = 2;      // 错误信息
	svcmoment.CharacterList data = 3; // 响应数据
}

// CreateCharacterReq 创建角色请求
message CreateCharacterReq {
	string name = 1;       // 角色名称
	string avatar_url = 2; // 角色头像
	string external_id = 3; // 外部角色ID
}

// CreateCharacterResp 创建角色响应
message CreateCharacterResp {
	int32 code = 1;      // 错误码
	string msg = 2;      // 错误信息
	svcmoment.CreateCharacterRespData data = 3; // 响应数据
}

// PublishMomentReq 发布朋友圈请求
message PublishMomentReq {
	int64 character_id = 1;    // 角色ID
	string origin_audio_url = 2; // 原始音频URL
	string voice_audio_url = 3;  // 语音音频URL
	string content = 4;         // 内容
	int32 duration = 5;        // 时长(秒)
}

// PublishMomentResp 发布朋友圈响应
message PublishMomentResp {
	int32 code = 1;      // 错误码
	string msg = 2;      // 错误信息
	svcmoment.PublishMomentRespData data = 3; // 响应数据
}

// GetMyMomentsReq 获取我发布的朋友圈列表请求
message GetMyMomentsReq {
    int32 page = 1;                // 页码
    int32 page_size = 2;           // 每页数量
}

// GetMyMomentsResp 获取我发布的朋友圈列表响应
message GetMyMomentsResp {
    int32 code = 1;      // 错误码
    string msg = 2;      // 错误信息
    svcmoment.GetMyMomentsRespData data = 3;  // 响应数据
}

// GetMomentsByIdsReq 通过ID列表获取朋友圈请求
message GetMomentsByIdsReq {
    repeated int64 moment_ids = 1 [(validate.rules).repeated.min_items = 1]; // 朋友圈ID列表
}

// GetMomentsByIdsResp 通过ID列表获取朋友圈响应
message GetMomentsByIdsResp {
    int32 code = 1;      // 错误码
    string msg = 2;      // 错误信息
    svcmoment.GetMomentsByIdsRespData data = 3;  // 响应数据
}

// LikeMomentReq 点赞请求
message LikeMomentReq {
    int64 moment_id = 1 [(validate.rules).int64.gt = 0];   // 朋友圈ID
    int64 character_id = 2 [(validate.rules).int64.gt = 0];     // 角色ID
}

// LikeMomentResp 点赞响应
message LikeMomentResp {
    int32 code = 1;      // 错误码
    string msg = 2;      // 错误信息
}

// UnlikeMomentReq 取消点赞请求
message UnlikeMomentReq {
    int64 moment_id = 1 [(validate.rules).int64.gt = 0];   // 朋友圈ID
}

// UnlikeMomentResp 取消点赞响应
message UnlikeMomentResp {
    int32 code = 1;      // 错误码
    string msg = 2;      // 错误信息
}

// GetMyLikedMomentsReq 获取我点赞的朋友圈列表请求
message GetMyLikedMomentsReq {
    int32 page = 1;                // 页码
    int32 page_size = 2;           // 每页数量
}

// GetMyLikedMomentsResp 获取我点赞的朋友圈列表响应
message GetMyLikedMomentsResp {
    int32 code = 1;      // 错误码
    string msg = 2;      // 错误信息
    svcmoment.GetMyLikedMomentsRespData data = 3;  // 响应数据
}

// UpdateUserCharacterReq 更新用户当前使用的角色请求
message UpdateUserCharacterReq {
    int64 character_id = 1 [(validate.rules).int64.gt = 0];     // 角色ID
}

// UpdateUserCharacterResp 更新用户当前使用的角色响应
message UpdateUserCharacterResp {
    int32 code = 1;      // 错误码
    string msg = 2;      // 错误信息
}

// GetUserCharacterReq 获取用户当前使用的角色请求
message GetUserCharacterReq {
}

// GetUserCharacterResp 获取用户当前使用的角色响应
message GetUserCharacterResp {
    int32 code = 1;      // 错误码
    string msg = 2;      // 错误信息
    svcmoment.GetUserCharacterRespData data = 3;  // 响应数据
}

// GetMomentSquareReq 获取朋友圈广场请求
message GetMomentSquareReq {
    int32 page = 1;      // 页码，从1开始
    int32 page_size = 2; // 每页数量
}

// GetMomentSquareResp 获取朋友圈广场响应
message GetMomentSquareResp {
    int32 code = 1;      // 错误码
    string msg = 2;      // 错误信息
    svcmoment.GetMomentSquareRespData data = 3;  // 响应数据
}

// 举报
message ReportResp {
  int32 code = 1;
  string msg = 2;
}

// 朋友圈服务
service s {
    // 获取角色列表
    rpc GetCharacterList(GetCharacterListReq) returns (GetCharacterListResp) {
        option (google.api.http) = {
            post: "/vc.bizmoment.s/v1/characters"
        };
    }
    // 发布朋友圈
    rpc PublishMoment(PublishMomentReq) returns (PublishMomentResp) {
        option (google.api.http) = {
            post: "/vc.bizmoment.s/v1/publish"
            body: "*"
        };
    }
    // 获取我发布的朋友圈列表
    rpc GetMyMoments(GetMyMomentsReq) returns (GetMyMomentsResp) {
        option (google.api.http) = {
            post: "/vc.bizmoment.s/v1/my-moments"
        };
    }
    // 通过ID列表获取朋友圈
    rpc GetMomentsByIds(GetMomentsByIdsReq) returns (GetMomentsByIdsResp) {
        option (google.api.http) = {
            post: "/vc.bizmoment.s/v1/moments-by-ids"
            body: "*"
        };
    }
    // 点赞
    rpc LikeMoment(LikeMomentReq) returns (LikeMomentResp) {
        option (google.api.http) = {
            post: "/vc.bizmoment.s/v1/like"
            body: "*"
        };
    }
    // 取消点赞
    rpc UnlikeMoment(UnlikeMomentReq) returns (UnlikeMomentResp) {
        option (google.api.http) = {
            post: "/vc.bizmoment.s/v1/unlike"
            body: "*"
        };
    }
    // 获取我点赞的朋友圈列表
    rpc GetMyLikedMoments(GetMyLikedMomentsReq) returns (GetMyLikedMomentsResp) {
        option (google.api.http) = {
            post: "/vc.bizmoment.s/v1/my-liked-moments"
        };
    }
    // 更新用户当前使用的角色
    rpc UpdateUserCharacter(UpdateUserCharacterReq) returns (UpdateUserCharacterResp) {
        option (google.api.http) = {
            post: "/vc.bizmoment.s/v1/update-user-character"
            body: "*"
        };
    }
    // 获取用户当前使用的角色
    rpc GetUserCharacter(GetUserCharacterReq) returns (GetUserCharacterResp) {
        option (google.api.http) = {
            post: "/vc.bizmoment.s/v1/user-character"
        };
    }
    // 获取朋友圈广场
    rpc GetMomentSquare(GetMomentSquareReq) returns (GetMomentSquareResp) {
        option (google.api.http) = {
            post: "/vc.bizmoment.s/v1/moment-square"
        };
    }

    // 举报
    rpc Report(svcmoment.ReportReq) returns (ReportResp) {
        option (google.api.http) = {
          post: "/vc.bizmoment.s/v1/report"
          body: "*"
        };
    }
}

