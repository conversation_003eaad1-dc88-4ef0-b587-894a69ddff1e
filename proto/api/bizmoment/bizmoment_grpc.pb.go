// Code generated by protoc-gen-go-grpc. DO NOT EDIT.

package bizmoment

import (
	context "context"
	grpc "google.golang.org/grpc"
	codes "google.golang.org/grpc/codes"
	status "google.golang.org/grpc/status"
	svcmoment "new-gitlab.xunlei.cn/vcproject/backends/proto/api/svcmoment"
)

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
// Requires gRPC-Go v1.32.0 or later.
const _ = grpc.SupportPackageIsVersion7

// SClient is the client API for S service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://pkg.go.dev/google.golang.org/grpc/?tab=doc#ClientConn.NewStream.
type SClient interface {
	// 获取角色列表
	GetCharacterList(ctx context.Context, in *GetCharacterListReq, opts ...grpc.CallOption) (*GetCharacterListResp, error)
	// 发布朋友圈
	PublishMoment(ctx context.Context, in *PublishMomentReq, opts ...grpc.CallOption) (*PublishMomentResp, error)
	// 获取我发布的朋友圈列表
	GetMyMoments(ctx context.Context, in *GetMyMomentsReq, opts ...grpc.CallOption) (*GetMyMomentsResp, error)
	// 通过ID列表获取朋友圈
	GetMomentsByIds(ctx context.Context, in *GetMomentsByIdsReq, opts ...grpc.CallOption) (*GetMomentsByIdsResp, error)
	// 点赞
	LikeMoment(ctx context.Context, in *LikeMomentReq, opts ...grpc.CallOption) (*LikeMomentResp, error)
	// 取消点赞
	UnlikeMoment(ctx context.Context, in *UnlikeMomentReq, opts ...grpc.CallOption) (*UnlikeMomentResp, error)
	// 获取我点赞的朋友圈列表
	GetMyLikedMoments(ctx context.Context, in *GetMyLikedMomentsReq, opts ...grpc.CallOption) (*GetMyLikedMomentsResp, error)
	// 更新用户当前使用的角色
	UpdateUserCharacter(ctx context.Context, in *UpdateUserCharacterReq, opts ...grpc.CallOption) (*UpdateUserCharacterResp, error)
	// 获取用户当前使用的角色
	GetUserCharacter(ctx context.Context, in *GetUserCharacterReq, opts ...grpc.CallOption) (*GetUserCharacterResp, error)
	// 获取朋友圈广场
	GetMomentSquare(ctx context.Context, in *GetMomentSquareReq, opts ...grpc.CallOption) (*GetMomentSquareResp, error)
	// 举报
	Report(ctx context.Context, in *svcmoment.ReportReq, opts ...grpc.CallOption) (*ReportResp, error)
}

type sClient struct {
	cc grpc.ClientConnInterface
}

func NewSClient(cc grpc.ClientConnInterface) SClient {
	return &sClient{cc}
}

func (c *sClient) GetCharacterList(ctx context.Context, in *GetCharacterListReq, opts ...grpc.CallOption) (*GetCharacterListResp, error) {
	out := new(GetCharacterListResp)
	err := c.cc.Invoke(ctx, "/vc.bizmoment.s/GetCharacterList", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *sClient) PublishMoment(ctx context.Context, in *PublishMomentReq, opts ...grpc.CallOption) (*PublishMomentResp, error) {
	out := new(PublishMomentResp)
	err := c.cc.Invoke(ctx, "/vc.bizmoment.s/PublishMoment", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *sClient) GetMyMoments(ctx context.Context, in *GetMyMomentsReq, opts ...grpc.CallOption) (*GetMyMomentsResp, error) {
	out := new(GetMyMomentsResp)
	err := c.cc.Invoke(ctx, "/vc.bizmoment.s/GetMyMoments", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *sClient) GetMomentsByIds(ctx context.Context, in *GetMomentsByIdsReq, opts ...grpc.CallOption) (*GetMomentsByIdsResp, error) {
	out := new(GetMomentsByIdsResp)
	err := c.cc.Invoke(ctx, "/vc.bizmoment.s/GetMomentsByIds", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *sClient) LikeMoment(ctx context.Context, in *LikeMomentReq, opts ...grpc.CallOption) (*LikeMomentResp, error) {
	out := new(LikeMomentResp)
	err := c.cc.Invoke(ctx, "/vc.bizmoment.s/LikeMoment", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *sClient) UnlikeMoment(ctx context.Context, in *UnlikeMomentReq, opts ...grpc.CallOption) (*UnlikeMomentResp, error) {
	out := new(UnlikeMomentResp)
	err := c.cc.Invoke(ctx, "/vc.bizmoment.s/UnlikeMoment", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *sClient) GetMyLikedMoments(ctx context.Context, in *GetMyLikedMomentsReq, opts ...grpc.CallOption) (*GetMyLikedMomentsResp, error) {
	out := new(GetMyLikedMomentsResp)
	err := c.cc.Invoke(ctx, "/vc.bizmoment.s/GetMyLikedMoments", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *sClient) UpdateUserCharacter(ctx context.Context, in *UpdateUserCharacterReq, opts ...grpc.CallOption) (*UpdateUserCharacterResp, error) {
	out := new(UpdateUserCharacterResp)
	err := c.cc.Invoke(ctx, "/vc.bizmoment.s/UpdateUserCharacter", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *sClient) GetUserCharacter(ctx context.Context, in *GetUserCharacterReq, opts ...grpc.CallOption) (*GetUserCharacterResp, error) {
	out := new(GetUserCharacterResp)
	err := c.cc.Invoke(ctx, "/vc.bizmoment.s/GetUserCharacter", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *sClient) GetMomentSquare(ctx context.Context, in *GetMomentSquareReq, opts ...grpc.CallOption) (*GetMomentSquareResp, error) {
	out := new(GetMomentSquareResp)
	err := c.cc.Invoke(ctx, "/vc.bizmoment.s/GetMomentSquare", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *sClient) Report(ctx context.Context, in *svcmoment.ReportReq, opts ...grpc.CallOption) (*ReportResp, error) {
	out := new(ReportResp)
	err := c.cc.Invoke(ctx, "/vc.bizmoment.s/Report", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// SServer is the server API for S service.
// All implementations should embed UnimplementedSServer
// for forward compatibility
type SServer interface {
	// 获取角色列表
	GetCharacterList(context.Context, *GetCharacterListReq) (*GetCharacterListResp, error)
	// 发布朋友圈
	PublishMoment(context.Context, *PublishMomentReq) (*PublishMomentResp, error)
	// 获取我发布的朋友圈列表
	GetMyMoments(context.Context, *GetMyMomentsReq) (*GetMyMomentsResp, error)
	// 通过ID列表获取朋友圈
	GetMomentsByIds(context.Context, *GetMomentsByIdsReq) (*GetMomentsByIdsResp, error)
	// 点赞
	LikeMoment(context.Context, *LikeMomentReq) (*LikeMomentResp, error)
	// 取消点赞
	UnlikeMoment(context.Context, *UnlikeMomentReq) (*UnlikeMomentResp, error)
	// 获取我点赞的朋友圈列表
	GetMyLikedMoments(context.Context, *GetMyLikedMomentsReq) (*GetMyLikedMomentsResp, error)
	// 更新用户当前使用的角色
	UpdateUserCharacter(context.Context, *UpdateUserCharacterReq) (*UpdateUserCharacterResp, error)
	// 获取用户当前使用的角色
	GetUserCharacter(context.Context, *GetUserCharacterReq) (*GetUserCharacterResp, error)
	// 获取朋友圈广场
	GetMomentSquare(context.Context, *GetMomentSquareReq) (*GetMomentSquareResp, error)
	// 举报
	Report(context.Context, *svcmoment.ReportReq) (*ReportResp, error)
}

// UnimplementedSServer should be embedded to have forward compatible implementations.
type UnimplementedSServer struct {
}

func (UnimplementedSServer) GetCharacterList(context.Context, *GetCharacterListReq) (*GetCharacterListResp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetCharacterList not implemented")
}
func (UnimplementedSServer) PublishMoment(context.Context, *PublishMomentReq) (*PublishMomentResp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method PublishMoment not implemented")
}
func (UnimplementedSServer) GetMyMoments(context.Context, *GetMyMomentsReq) (*GetMyMomentsResp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetMyMoments not implemented")
}
func (UnimplementedSServer) GetMomentsByIds(context.Context, *GetMomentsByIdsReq) (*GetMomentsByIdsResp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetMomentsByIds not implemented")
}
func (UnimplementedSServer) LikeMoment(context.Context, *LikeMomentReq) (*LikeMomentResp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method LikeMoment not implemented")
}
func (UnimplementedSServer) UnlikeMoment(context.Context, *UnlikeMomentReq) (*UnlikeMomentResp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method UnlikeMoment not implemented")
}
func (UnimplementedSServer) GetMyLikedMoments(context.Context, *GetMyLikedMomentsReq) (*GetMyLikedMomentsResp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetMyLikedMoments not implemented")
}
func (UnimplementedSServer) UpdateUserCharacter(context.Context, *UpdateUserCharacterReq) (*UpdateUserCharacterResp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method UpdateUserCharacter not implemented")
}
func (UnimplementedSServer) GetUserCharacter(context.Context, *GetUserCharacterReq) (*GetUserCharacterResp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetUserCharacter not implemented")
}
func (UnimplementedSServer) GetMomentSquare(context.Context, *GetMomentSquareReq) (*GetMomentSquareResp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetMomentSquare not implemented")
}
func (UnimplementedSServer) Report(context.Context, *svcmoment.ReportReq) (*ReportResp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method Report not implemented")
}

// UnsafeSServer may be embedded to opt out of forward compatibility for this service.
// Use of this interface is not recommended, as added methods to SServer will
// result in compilation errors.
type UnsafeSServer interface {
	mustEmbedUnimplementedSServer()
}

func RegisterSServer(s grpc.ServiceRegistrar, srv SServer) {
	s.RegisterService(&S_ServiceDesc, srv)
}

func _S_GetCharacterList_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetCharacterListReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(SServer).GetCharacterList(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/vc.bizmoment.s/GetCharacterList",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(SServer).GetCharacterList(ctx, req.(*GetCharacterListReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _S_PublishMoment_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(PublishMomentReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(SServer).PublishMoment(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/vc.bizmoment.s/PublishMoment",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(SServer).PublishMoment(ctx, req.(*PublishMomentReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _S_GetMyMoments_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetMyMomentsReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(SServer).GetMyMoments(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/vc.bizmoment.s/GetMyMoments",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(SServer).GetMyMoments(ctx, req.(*GetMyMomentsReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _S_GetMomentsByIds_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetMomentsByIdsReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(SServer).GetMomentsByIds(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/vc.bizmoment.s/GetMomentsByIds",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(SServer).GetMomentsByIds(ctx, req.(*GetMomentsByIdsReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _S_LikeMoment_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(LikeMomentReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(SServer).LikeMoment(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/vc.bizmoment.s/LikeMoment",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(SServer).LikeMoment(ctx, req.(*LikeMomentReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _S_UnlikeMoment_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(UnlikeMomentReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(SServer).UnlikeMoment(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/vc.bizmoment.s/UnlikeMoment",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(SServer).UnlikeMoment(ctx, req.(*UnlikeMomentReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _S_GetMyLikedMoments_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetMyLikedMomentsReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(SServer).GetMyLikedMoments(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/vc.bizmoment.s/GetMyLikedMoments",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(SServer).GetMyLikedMoments(ctx, req.(*GetMyLikedMomentsReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _S_UpdateUserCharacter_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(UpdateUserCharacterReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(SServer).UpdateUserCharacter(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/vc.bizmoment.s/UpdateUserCharacter",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(SServer).UpdateUserCharacter(ctx, req.(*UpdateUserCharacterReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _S_GetUserCharacter_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetUserCharacterReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(SServer).GetUserCharacter(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/vc.bizmoment.s/GetUserCharacter",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(SServer).GetUserCharacter(ctx, req.(*GetUserCharacterReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _S_GetMomentSquare_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetMomentSquareReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(SServer).GetMomentSquare(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/vc.bizmoment.s/GetMomentSquare",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(SServer).GetMomentSquare(ctx, req.(*GetMomentSquareReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _S_Report_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(svcmoment.ReportReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(SServer).Report(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/vc.bizmoment.s/Report",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(SServer).Report(ctx, req.(*svcmoment.ReportReq))
	}
	return interceptor(ctx, in, info, handler)
}

// S_ServiceDesc is the grpc.ServiceDesc for S service.
// It's only intended for direct use with grpc.RegisterService,
// and not to be introspected or modified (even as a copy)
var S_ServiceDesc = grpc.ServiceDesc{
	ServiceName: "vc.bizmoment.s",
	HandlerType: (*SServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "GetCharacterList",
			Handler:    _S_GetCharacterList_Handler,
		},
		{
			MethodName: "PublishMoment",
			Handler:    _S_PublishMoment_Handler,
		},
		{
			MethodName: "GetMyMoments",
			Handler:    _S_GetMyMoments_Handler,
		},
		{
			MethodName: "GetMomentsByIds",
			Handler:    _S_GetMomentsByIds_Handler,
		},
		{
			MethodName: "LikeMoment",
			Handler:    _S_LikeMoment_Handler,
		},
		{
			MethodName: "UnlikeMoment",
			Handler:    _S_UnlikeMoment_Handler,
		},
		{
			MethodName: "GetMyLikedMoments",
			Handler:    _S_GetMyLikedMoments_Handler,
		},
		{
			MethodName: "UpdateUserCharacter",
			Handler:    _S_UpdateUserCharacter_Handler,
		},
		{
			MethodName: "GetUserCharacter",
			Handler:    _S_GetUserCharacter_Handler,
		},
		{
			MethodName: "GetMomentSquare",
			Handler:    _S_GetMomentSquare_Handler,
		},
		{
			MethodName: "Report",
			Handler:    _S_Report_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "bizmoment.proto",
}
