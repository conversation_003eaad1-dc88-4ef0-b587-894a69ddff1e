// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.33.0
// 	protoc        v5.29.3
// source: bizmoment.proto

package bizmoment

import (
	_ "github.com/envoyproxy/protoc-gen-validate/validate"
	_ "google.golang.org/genproto/googleapis/api/annotations"
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	svcmoment "new-gitlab.xunlei.cn/vcproject/backends/proto/api/svcmoment"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

// GetCharacterListReq 获取角色列表请求
type GetCharacterListReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Page     int32 `protobuf:"varint,1,opt,name=page,proto3" json:"page,omitempty"`                         // 页码
	PageSize int32 `protobuf:"varint,2,opt,name=page_size,json=pageSize,proto3" json:"page_size,omitempty"` // 每页数量
}

func (x *GetCharacterListReq) Reset() {
	*x = GetCharacterListReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_bizmoment_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetCharacterListReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetCharacterListReq) ProtoMessage() {}

func (x *GetCharacterListReq) ProtoReflect() protoreflect.Message {
	mi := &file_bizmoment_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetCharacterListReq.ProtoReflect.Descriptor instead.
func (*GetCharacterListReq) Descriptor() ([]byte, []int) {
	return file_bizmoment_proto_rawDescGZIP(), []int{0}
}

func (x *GetCharacterListReq) GetPage() int32 {
	if x != nil {
		return x.Page
	}
	return 0
}

func (x *GetCharacterListReq) GetPageSize() int32 {
	if x != nil {
		return x.PageSize
	}
	return 0
}

// GetCharacterListResp 获取角色列表响应
type GetCharacterListResp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Code int32                    `protobuf:"varint,1,opt,name=code,proto3" json:"code,omitempty"` // 错误码
	Msg  string                   `protobuf:"bytes,2,opt,name=msg,proto3" json:"msg,omitempty"`    // 错误信息
	Data *svcmoment.CharacterList `protobuf:"bytes,3,opt,name=data,proto3" json:"data,omitempty"`  // 响应数据
}

func (x *GetCharacterListResp) Reset() {
	*x = GetCharacterListResp{}
	if protoimpl.UnsafeEnabled {
		mi := &file_bizmoment_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetCharacterListResp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetCharacterListResp) ProtoMessage() {}

func (x *GetCharacterListResp) ProtoReflect() protoreflect.Message {
	mi := &file_bizmoment_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetCharacterListResp.ProtoReflect.Descriptor instead.
func (*GetCharacterListResp) Descriptor() ([]byte, []int) {
	return file_bizmoment_proto_rawDescGZIP(), []int{1}
}

func (x *GetCharacterListResp) GetCode() int32 {
	if x != nil {
		return x.Code
	}
	return 0
}

func (x *GetCharacterListResp) GetMsg() string {
	if x != nil {
		return x.Msg
	}
	return ""
}

func (x *GetCharacterListResp) GetData() *svcmoment.CharacterList {
	if x != nil {
		return x.Data
	}
	return nil
}

// CreateCharacterReq 创建角色请求
type CreateCharacterReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Name       string `protobuf:"bytes,1,opt,name=name,proto3" json:"name,omitempty"`                               // 角色名称
	AvatarUrl  string `protobuf:"bytes,2,opt,name=avatar_url,json=avatarUrl,proto3" json:"avatar_url,omitempty"`    // 角色头像
	ExternalId string `protobuf:"bytes,3,opt,name=external_id,json=externalId,proto3" json:"external_id,omitempty"` // 外部角色ID
}

func (x *CreateCharacterReq) Reset() {
	*x = CreateCharacterReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_bizmoment_proto_msgTypes[2]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CreateCharacterReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CreateCharacterReq) ProtoMessage() {}

func (x *CreateCharacterReq) ProtoReflect() protoreflect.Message {
	mi := &file_bizmoment_proto_msgTypes[2]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CreateCharacterReq.ProtoReflect.Descriptor instead.
func (*CreateCharacterReq) Descriptor() ([]byte, []int) {
	return file_bizmoment_proto_rawDescGZIP(), []int{2}
}

func (x *CreateCharacterReq) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *CreateCharacterReq) GetAvatarUrl() string {
	if x != nil {
		return x.AvatarUrl
	}
	return ""
}

func (x *CreateCharacterReq) GetExternalId() string {
	if x != nil {
		return x.ExternalId
	}
	return ""
}

// CreateCharacterResp 创建角色响应
type CreateCharacterResp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Code int32                              `protobuf:"varint,1,opt,name=code,proto3" json:"code,omitempty"` // 错误码
	Msg  string                             `protobuf:"bytes,2,opt,name=msg,proto3" json:"msg,omitempty"`    // 错误信息
	Data *svcmoment.CreateCharacterRespData `protobuf:"bytes,3,opt,name=data,proto3" json:"data,omitempty"`  // 响应数据
}

func (x *CreateCharacterResp) Reset() {
	*x = CreateCharacterResp{}
	if protoimpl.UnsafeEnabled {
		mi := &file_bizmoment_proto_msgTypes[3]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CreateCharacterResp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CreateCharacterResp) ProtoMessage() {}

func (x *CreateCharacterResp) ProtoReflect() protoreflect.Message {
	mi := &file_bizmoment_proto_msgTypes[3]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CreateCharacterResp.ProtoReflect.Descriptor instead.
func (*CreateCharacterResp) Descriptor() ([]byte, []int) {
	return file_bizmoment_proto_rawDescGZIP(), []int{3}
}

func (x *CreateCharacterResp) GetCode() int32 {
	if x != nil {
		return x.Code
	}
	return 0
}

func (x *CreateCharacterResp) GetMsg() string {
	if x != nil {
		return x.Msg
	}
	return ""
}

func (x *CreateCharacterResp) GetData() *svcmoment.CreateCharacterRespData {
	if x != nil {
		return x.Data
	}
	return nil
}

// PublishMomentReq 发布朋友圈请求
type PublishMomentReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	CharacterId    int64  `protobuf:"varint,1,opt,name=character_id,json=characterId,proto3" json:"character_id,omitempty"`           // 角色ID
	OriginAudioUrl string `protobuf:"bytes,2,opt,name=origin_audio_url,json=originAudioUrl,proto3" json:"origin_audio_url,omitempty"` // 原始音频URL
	VoiceAudioUrl  string `protobuf:"bytes,3,opt,name=voice_audio_url,json=voiceAudioUrl,proto3" json:"voice_audio_url,omitempty"`    // 语音音频URL
	Content        string `protobuf:"bytes,4,opt,name=content,proto3" json:"content,omitempty"`                                       // 内容
	Duration       int32  `protobuf:"varint,5,opt,name=duration,proto3" json:"duration,omitempty"`                                    // 时长(秒)
}

func (x *PublishMomentReq) Reset() {
	*x = PublishMomentReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_bizmoment_proto_msgTypes[4]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *PublishMomentReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PublishMomentReq) ProtoMessage() {}

func (x *PublishMomentReq) ProtoReflect() protoreflect.Message {
	mi := &file_bizmoment_proto_msgTypes[4]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PublishMomentReq.ProtoReflect.Descriptor instead.
func (*PublishMomentReq) Descriptor() ([]byte, []int) {
	return file_bizmoment_proto_rawDescGZIP(), []int{4}
}

func (x *PublishMomentReq) GetCharacterId() int64 {
	if x != nil {
		return x.CharacterId
	}
	return 0
}

func (x *PublishMomentReq) GetOriginAudioUrl() string {
	if x != nil {
		return x.OriginAudioUrl
	}
	return ""
}

func (x *PublishMomentReq) GetVoiceAudioUrl() string {
	if x != nil {
		return x.VoiceAudioUrl
	}
	return ""
}

func (x *PublishMomentReq) GetContent() string {
	if x != nil {
		return x.Content
	}
	return ""
}

func (x *PublishMomentReq) GetDuration() int32 {
	if x != nil {
		return x.Duration
	}
	return 0
}

// PublishMomentResp 发布朋友圈响应
type PublishMomentResp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Code int32                            `protobuf:"varint,1,opt,name=code,proto3" json:"code,omitempty"` // 错误码
	Msg  string                           `protobuf:"bytes,2,opt,name=msg,proto3" json:"msg,omitempty"`    // 错误信息
	Data *svcmoment.PublishMomentRespData `protobuf:"bytes,3,opt,name=data,proto3" json:"data,omitempty"`  // 响应数据
}

func (x *PublishMomentResp) Reset() {
	*x = PublishMomentResp{}
	if protoimpl.UnsafeEnabled {
		mi := &file_bizmoment_proto_msgTypes[5]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *PublishMomentResp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PublishMomentResp) ProtoMessage() {}

func (x *PublishMomentResp) ProtoReflect() protoreflect.Message {
	mi := &file_bizmoment_proto_msgTypes[5]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PublishMomentResp.ProtoReflect.Descriptor instead.
func (*PublishMomentResp) Descriptor() ([]byte, []int) {
	return file_bizmoment_proto_rawDescGZIP(), []int{5}
}

func (x *PublishMomentResp) GetCode() int32 {
	if x != nil {
		return x.Code
	}
	return 0
}

func (x *PublishMomentResp) GetMsg() string {
	if x != nil {
		return x.Msg
	}
	return ""
}

func (x *PublishMomentResp) GetData() *svcmoment.PublishMomentRespData {
	if x != nil {
		return x.Data
	}
	return nil
}

// GetMyMomentsReq 获取我发布的朋友圈列表请求
type GetMyMomentsReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Page     int32 `protobuf:"varint,1,opt,name=page,proto3" json:"page,omitempty"`                         // 页码
	PageSize int32 `protobuf:"varint,2,opt,name=page_size,json=pageSize,proto3" json:"page_size,omitempty"` // 每页数量
}

func (x *GetMyMomentsReq) Reset() {
	*x = GetMyMomentsReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_bizmoment_proto_msgTypes[6]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetMyMomentsReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetMyMomentsReq) ProtoMessage() {}

func (x *GetMyMomentsReq) ProtoReflect() protoreflect.Message {
	mi := &file_bizmoment_proto_msgTypes[6]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetMyMomentsReq.ProtoReflect.Descriptor instead.
func (*GetMyMomentsReq) Descriptor() ([]byte, []int) {
	return file_bizmoment_proto_rawDescGZIP(), []int{6}
}

func (x *GetMyMomentsReq) GetPage() int32 {
	if x != nil {
		return x.Page
	}
	return 0
}

func (x *GetMyMomentsReq) GetPageSize() int32 {
	if x != nil {
		return x.PageSize
	}
	return 0
}

// GetMyMomentsResp 获取我发布的朋友圈列表响应
type GetMyMomentsResp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Code int32                           `protobuf:"varint,1,opt,name=code,proto3" json:"code,omitempty"` // 错误码
	Msg  string                          `protobuf:"bytes,2,opt,name=msg,proto3" json:"msg,omitempty"`    // 错误信息
	Data *svcmoment.GetMyMomentsRespData `protobuf:"bytes,3,opt,name=data,proto3" json:"data,omitempty"`  // 响应数据
}

func (x *GetMyMomentsResp) Reset() {
	*x = GetMyMomentsResp{}
	if protoimpl.UnsafeEnabled {
		mi := &file_bizmoment_proto_msgTypes[7]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetMyMomentsResp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetMyMomentsResp) ProtoMessage() {}

func (x *GetMyMomentsResp) ProtoReflect() protoreflect.Message {
	mi := &file_bizmoment_proto_msgTypes[7]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetMyMomentsResp.ProtoReflect.Descriptor instead.
func (*GetMyMomentsResp) Descriptor() ([]byte, []int) {
	return file_bizmoment_proto_rawDescGZIP(), []int{7}
}

func (x *GetMyMomentsResp) GetCode() int32 {
	if x != nil {
		return x.Code
	}
	return 0
}

func (x *GetMyMomentsResp) GetMsg() string {
	if x != nil {
		return x.Msg
	}
	return ""
}

func (x *GetMyMomentsResp) GetData() *svcmoment.GetMyMomentsRespData {
	if x != nil {
		return x.Data
	}
	return nil
}

// GetMomentsByIdsReq 通过ID列表获取朋友圈请求
type GetMomentsByIdsReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	MomentIds []int64 `protobuf:"varint,1,rep,packed,name=moment_ids,json=momentIds,proto3" json:"moment_ids,omitempty"` // 朋友圈ID列表
}

func (x *GetMomentsByIdsReq) Reset() {
	*x = GetMomentsByIdsReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_bizmoment_proto_msgTypes[8]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetMomentsByIdsReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetMomentsByIdsReq) ProtoMessage() {}

func (x *GetMomentsByIdsReq) ProtoReflect() protoreflect.Message {
	mi := &file_bizmoment_proto_msgTypes[8]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetMomentsByIdsReq.ProtoReflect.Descriptor instead.
func (*GetMomentsByIdsReq) Descriptor() ([]byte, []int) {
	return file_bizmoment_proto_rawDescGZIP(), []int{8}
}

func (x *GetMomentsByIdsReq) GetMomentIds() []int64 {
	if x != nil {
		return x.MomentIds
	}
	return nil
}

// GetMomentsByIdsResp 通过ID列表获取朋友圈响应
type GetMomentsByIdsResp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Code int32                              `protobuf:"varint,1,opt,name=code,proto3" json:"code,omitempty"` // 错误码
	Msg  string                             `protobuf:"bytes,2,opt,name=msg,proto3" json:"msg,omitempty"`    // 错误信息
	Data *svcmoment.GetMomentsByIdsRespData `protobuf:"bytes,3,opt,name=data,proto3" json:"data,omitempty"`  // 响应数据
}

func (x *GetMomentsByIdsResp) Reset() {
	*x = GetMomentsByIdsResp{}
	if protoimpl.UnsafeEnabled {
		mi := &file_bizmoment_proto_msgTypes[9]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetMomentsByIdsResp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetMomentsByIdsResp) ProtoMessage() {}

func (x *GetMomentsByIdsResp) ProtoReflect() protoreflect.Message {
	mi := &file_bizmoment_proto_msgTypes[9]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetMomentsByIdsResp.ProtoReflect.Descriptor instead.
func (*GetMomentsByIdsResp) Descriptor() ([]byte, []int) {
	return file_bizmoment_proto_rawDescGZIP(), []int{9}
}

func (x *GetMomentsByIdsResp) GetCode() int32 {
	if x != nil {
		return x.Code
	}
	return 0
}

func (x *GetMomentsByIdsResp) GetMsg() string {
	if x != nil {
		return x.Msg
	}
	return ""
}

func (x *GetMomentsByIdsResp) GetData() *svcmoment.GetMomentsByIdsRespData {
	if x != nil {
		return x.Data
	}
	return nil
}

// LikeMomentReq 点赞请求
type LikeMomentReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	MomentId    int64 `protobuf:"varint,1,opt,name=moment_id,json=momentId,proto3" json:"moment_id,omitempty"`          // 朋友圈ID
	CharacterId int64 `protobuf:"varint,2,opt,name=character_id,json=characterId,proto3" json:"character_id,omitempty"` // 角色ID
}

func (x *LikeMomentReq) Reset() {
	*x = LikeMomentReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_bizmoment_proto_msgTypes[10]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *LikeMomentReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*LikeMomentReq) ProtoMessage() {}

func (x *LikeMomentReq) ProtoReflect() protoreflect.Message {
	mi := &file_bizmoment_proto_msgTypes[10]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use LikeMomentReq.ProtoReflect.Descriptor instead.
func (*LikeMomentReq) Descriptor() ([]byte, []int) {
	return file_bizmoment_proto_rawDescGZIP(), []int{10}
}

func (x *LikeMomentReq) GetMomentId() int64 {
	if x != nil {
		return x.MomentId
	}
	return 0
}

func (x *LikeMomentReq) GetCharacterId() int64 {
	if x != nil {
		return x.CharacterId
	}
	return 0
}

// LikeMomentResp 点赞响应
type LikeMomentResp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Code int32  `protobuf:"varint,1,opt,name=code,proto3" json:"code,omitempty"` // 错误码
	Msg  string `protobuf:"bytes,2,opt,name=msg,proto3" json:"msg,omitempty"`    // 错误信息
}

func (x *LikeMomentResp) Reset() {
	*x = LikeMomentResp{}
	if protoimpl.UnsafeEnabled {
		mi := &file_bizmoment_proto_msgTypes[11]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *LikeMomentResp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*LikeMomentResp) ProtoMessage() {}

func (x *LikeMomentResp) ProtoReflect() protoreflect.Message {
	mi := &file_bizmoment_proto_msgTypes[11]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use LikeMomentResp.ProtoReflect.Descriptor instead.
func (*LikeMomentResp) Descriptor() ([]byte, []int) {
	return file_bizmoment_proto_rawDescGZIP(), []int{11}
}

func (x *LikeMomentResp) GetCode() int32 {
	if x != nil {
		return x.Code
	}
	return 0
}

func (x *LikeMomentResp) GetMsg() string {
	if x != nil {
		return x.Msg
	}
	return ""
}

// UnlikeMomentReq 取消点赞请求
type UnlikeMomentReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	MomentId int64 `protobuf:"varint,1,opt,name=moment_id,json=momentId,proto3" json:"moment_id,omitempty"` // 朋友圈ID
}

func (x *UnlikeMomentReq) Reset() {
	*x = UnlikeMomentReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_bizmoment_proto_msgTypes[12]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *UnlikeMomentReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UnlikeMomentReq) ProtoMessage() {}

func (x *UnlikeMomentReq) ProtoReflect() protoreflect.Message {
	mi := &file_bizmoment_proto_msgTypes[12]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UnlikeMomentReq.ProtoReflect.Descriptor instead.
func (*UnlikeMomentReq) Descriptor() ([]byte, []int) {
	return file_bizmoment_proto_rawDescGZIP(), []int{12}
}

func (x *UnlikeMomentReq) GetMomentId() int64 {
	if x != nil {
		return x.MomentId
	}
	return 0
}

// UnlikeMomentResp 取消点赞响应
type UnlikeMomentResp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Code int32  `protobuf:"varint,1,opt,name=code,proto3" json:"code,omitempty"` // 错误码
	Msg  string `protobuf:"bytes,2,opt,name=msg,proto3" json:"msg,omitempty"`    // 错误信息
}

func (x *UnlikeMomentResp) Reset() {
	*x = UnlikeMomentResp{}
	if protoimpl.UnsafeEnabled {
		mi := &file_bizmoment_proto_msgTypes[13]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *UnlikeMomentResp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UnlikeMomentResp) ProtoMessage() {}

func (x *UnlikeMomentResp) ProtoReflect() protoreflect.Message {
	mi := &file_bizmoment_proto_msgTypes[13]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UnlikeMomentResp.ProtoReflect.Descriptor instead.
func (*UnlikeMomentResp) Descriptor() ([]byte, []int) {
	return file_bizmoment_proto_rawDescGZIP(), []int{13}
}

func (x *UnlikeMomentResp) GetCode() int32 {
	if x != nil {
		return x.Code
	}
	return 0
}

func (x *UnlikeMomentResp) GetMsg() string {
	if x != nil {
		return x.Msg
	}
	return ""
}

// GetMyLikedMomentsReq 获取我点赞的朋友圈列表请求
type GetMyLikedMomentsReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Page     int32 `protobuf:"varint,1,opt,name=page,proto3" json:"page,omitempty"`                         // 页码
	PageSize int32 `protobuf:"varint,2,opt,name=page_size,json=pageSize,proto3" json:"page_size,omitempty"` // 每页数量
}

func (x *GetMyLikedMomentsReq) Reset() {
	*x = GetMyLikedMomentsReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_bizmoment_proto_msgTypes[14]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetMyLikedMomentsReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetMyLikedMomentsReq) ProtoMessage() {}

func (x *GetMyLikedMomentsReq) ProtoReflect() protoreflect.Message {
	mi := &file_bizmoment_proto_msgTypes[14]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetMyLikedMomentsReq.ProtoReflect.Descriptor instead.
func (*GetMyLikedMomentsReq) Descriptor() ([]byte, []int) {
	return file_bizmoment_proto_rawDescGZIP(), []int{14}
}

func (x *GetMyLikedMomentsReq) GetPage() int32 {
	if x != nil {
		return x.Page
	}
	return 0
}

func (x *GetMyLikedMomentsReq) GetPageSize() int32 {
	if x != nil {
		return x.PageSize
	}
	return 0
}

// GetMyLikedMomentsResp 获取我点赞的朋友圈列表响应
type GetMyLikedMomentsResp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Code int32                                `protobuf:"varint,1,opt,name=code,proto3" json:"code,omitempty"` // 错误码
	Msg  string                               `protobuf:"bytes,2,opt,name=msg,proto3" json:"msg,omitempty"`    // 错误信息
	Data *svcmoment.GetMyLikedMomentsRespData `protobuf:"bytes,3,opt,name=data,proto3" json:"data,omitempty"`  // 响应数据
}

func (x *GetMyLikedMomentsResp) Reset() {
	*x = GetMyLikedMomentsResp{}
	if protoimpl.UnsafeEnabled {
		mi := &file_bizmoment_proto_msgTypes[15]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetMyLikedMomentsResp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetMyLikedMomentsResp) ProtoMessage() {}

func (x *GetMyLikedMomentsResp) ProtoReflect() protoreflect.Message {
	mi := &file_bizmoment_proto_msgTypes[15]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetMyLikedMomentsResp.ProtoReflect.Descriptor instead.
func (*GetMyLikedMomentsResp) Descriptor() ([]byte, []int) {
	return file_bizmoment_proto_rawDescGZIP(), []int{15}
}

func (x *GetMyLikedMomentsResp) GetCode() int32 {
	if x != nil {
		return x.Code
	}
	return 0
}

func (x *GetMyLikedMomentsResp) GetMsg() string {
	if x != nil {
		return x.Msg
	}
	return ""
}

func (x *GetMyLikedMomentsResp) GetData() *svcmoment.GetMyLikedMomentsRespData {
	if x != nil {
		return x.Data
	}
	return nil
}

// UpdateUserCharacterReq 更新用户当前使用的角色请求
type UpdateUserCharacterReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	CharacterId int64 `protobuf:"varint,1,opt,name=character_id,json=characterId,proto3" json:"character_id,omitempty"` // 角色ID
}

func (x *UpdateUserCharacterReq) Reset() {
	*x = UpdateUserCharacterReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_bizmoment_proto_msgTypes[16]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *UpdateUserCharacterReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UpdateUserCharacterReq) ProtoMessage() {}

func (x *UpdateUserCharacterReq) ProtoReflect() protoreflect.Message {
	mi := &file_bizmoment_proto_msgTypes[16]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UpdateUserCharacterReq.ProtoReflect.Descriptor instead.
func (*UpdateUserCharacterReq) Descriptor() ([]byte, []int) {
	return file_bizmoment_proto_rawDescGZIP(), []int{16}
}

func (x *UpdateUserCharacterReq) GetCharacterId() int64 {
	if x != nil {
		return x.CharacterId
	}
	return 0
}

// UpdateUserCharacterResp 更新用户当前使用的角色响应
type UpdateUserCharacterResp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Code int32  `protobuf:"varint,1,opt,name=code,proto3" json:"code,omitempty"` // 错误码
	Msg  string `protobuf:"bytes,2,opt,name=msg,proto3" json:"msg,omitempty"`    // 错误信息
}

func (x *UpdateUserCharacterResp) Reset() {
	*x = UpdateUserCharacterResp{}
	if protoimpl.UnsafeEnabled {
		mi := &file_bizmoment_proto_msgTypes[17]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *UpdateUserCharacterResp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UpdateUserCharacterResp) ProtoMessage() {}

func (x *UpdateUserCharacterResp) ProtoReflect() protoreflect.Message {
	mi := &file_bizmoment_proto_msgTypes[17]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UpdateUserCharacterResp.ProtoReflect.Descriptor instead.
func (*UpdateUserCharacterResp) Descriptor() ([]byte, []int) {
	return file_bizmoment_proto_rawDescGZIP(), []int{17}
}

func (x *UpdateUserCharacterResp) GetCode() int32 {
	if x != nil {
		return x.Code
	}
	return 0
}

func (x *UpdateUserCharacterResp) GetMsg() string {
	if x != nil {
		return x.Msg
	}
	return ""
}

// GetUserCharacterReq 获取用户当前使用的角色请求
type GetUserCharacterReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields
}

func (x *GetUserCharacterReq) Reset() {
	*x = GetUserCharacterReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_bizmoment_proto_msgTypes[18]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetUserCharacterReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetUserCharacterReq) ProtoMessage() {}

func (x *GetUserCharacterReq) ProtoReflect() protoreflect.Message {
	mi := &file_bizmoment_proto_msgTypes[18]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetUserCharacterReq.ProtoReflect.Descriptor instead.
func (*GetUserCharacterReq) Descriptor() ([]byte, []int) {
	return file_bizmoment_proto_rawDescGZIP(), []int{18}
}

// GetUserCharacterResp 获取用户当前使用的角色响应
type GetUserCharacterResp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Code int32                               `protobuf:"varint,1,opt,name=code,proto3" json:"code,omitempty"` // 错误码
	Msg  string                              `protobuf:"bytes,2,opt,name=msg,proto3" json:"msg,omitempty"`    // 错误信息
	Data *svcmoment.GetUserCharacterRespData `protobuf:"bytes,3,opt,name=data,proto3" json:"data,omitempty"`  // 响应数据
}

func (x *GetUserCharacterResp) Reset() {
	*x = GetUserCharacterResp{}
	if protoimpl.UnsafeEnabled {
		mi := &file_bizmoment_proto_msgTypes[19]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetUserCharacterResp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetUserCharacterResp) ProtoMessage() {}

func (x *GetUserCharacterResp) ProtoReflect() protoreflect.Message {
	mi := &file_bizmoment_proto_msgTypes[19]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetUserCharacterResp.ProtoReflect.Descriptor instead.
func (*GetUserCharacterResp) Descriptor() ([]byte, []int) {
	return file_bizmoment_proto_rawDescGZIP(), []int{19}
}

func (x *GetUserCharacterResp) GetCode() int32 {
	if x != nil {
		return x.Code
	}
	return 0
}

func (x *GetUserCharacterResp) GetMsg() string {
	if x != nil {
		return x.Msg
	}
	return ""
}

func (x *GetUserCharacterResp) GetData() *svcmoment.GetUserCharacterRespData {
	if x != nil {
		return x.Data
	}
	return nil
}

// GetMomentSquareReq 获取朋友圈广场请求
type GetMomentSquareReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Page     int32 `protobuf:"varint,1,opt,name=page,proto3" json:"page,omitempty"`                         // 页码，从1开始
	PageSize int32 `protobuf:"varint,2,opt,name=page_size,json=pageSize,proto3" json:"page_size,omitempty"` // 每页数量
}

func (x *GetMomentSquareReq) Reset() {
	*x = GetMomentSquareReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_bizmoment_proto_msgTypes[20]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetMomentSquareReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetMomentSquareReq) ProtoMessage() {}

func (x *GetMomentSquareReq) ProtoReflect() protoreflect.Message {
	mi := &file_bizmoment_proto_msgTypes[20]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetMomentSquareReq.ProtoReflect.Descriptor instead.
func (*GetMomentSquareReq) Descriptor() ([]byte, []int) {
	return file_bizmoment_proto_rawDescGZIP(), []int{20}
}

func (x *GetMomentSquareReq) GetPage() int32 {
	if x != nil {
		return x.Page
	}
	return 0
}

func (x *GetMomentSquareReq) GetPageSize() int32 {
	if x != nil {
		return x.PageSize
	}
	return 0
}

// GetMomentSquareResp 获取朋友圈广场响应
type GetMomentSquareResp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Code int32                              `protobuf:"varint,1,opt,name=code,proto3" json:"code,omitempty"` // 错误码
	Msg  string                             `protobuf:"bytes,2,opt,name=msg,proto3" json:"msg,omitempty"`    // 错误信息
	Data *svcmoment.GetMomentSquareRespData `protobuf:"bytes,3,opt,name=data,proto3" json:"data,omitempty"`  // 响应数据
}

func (x *GetMomentSquareResp) Reset() {
	*x = GetMomentSquareResp{}
	if protoimpl.UnsafeEnabled {
		mi := &file_bizmoment_proto_msgTypes[21]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetMomentSquareResp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetMomentSquareResp) ProtoMessage() {}

func (x *GetMomentSquareResp) ProtoReflect() protoreflect.Message {
	mi := &file_bizmoment_proto_msgTypes[21]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetMomentSquareResp.ProtoReflect.Descriptor instead.
func (*GetMomentSquareResp) Descriptor() ([]byte, []int) {
	return file_bizmoment_proto_rawDescGZIP(), []int{21}
}

func (x *GetMomentSquareResp) GetCode() int32 {
	if x != nil {
		return x.Code
	}
	return 0
}

func (x *GetMomentSquareResp) GetMsg() string {
	if x != nil {
		return x.Msg
	}
	return ""
}

func (x *GetMomentSquareResp) GetData() *svcmoment.GetMomentSquareRespData {
	if x != nil {
		return x.Data
	}
	return nil
}

// 举报
type ReportResp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Code int32  `protobuf:"varint,1,opt,name=code,proto3" json:"code,omitempty"`
	Msg  string `protobuf:"bytes,2,opt,name=msg,proto3" json:"msg,omitempty"`
}

func (x *ReportResp) Reset() {
	*x = ReportResp{}
	if protoimpl.UnsafeEnabled {
		mi := &file_bizmoment_proto_msgTypes[22]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ReportResp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ReportResp) ProtoMessage() {}

func (x *ReportResp) ProtoReflect() protoreflect.Message {
	mi := &file_bizmoment_proto_msgTypes[22]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ReportResp.ProtoReflect.Descriptor instead.
func (*ReportResp) Descriptor() ([]byte, []int) {
	return file_bizmoment_proto_rawDescGZIP(), []int{22}
}

func (x *ReportResp) GetCode() int32 {
	if x != nil {
		return x.Code
	}
	return 0
}

func (x *ReportResp) GetMsg() string {
	if x != nil {
		return x.Msg
	}
	return ""
}

var File_bizmoment_proto protoreflect.FileDescriptor

var file_bizmoment_proto_rawDesc = []byte{
	0x0a, 0x0f, 0x62, 0x69, 0x7a, 0x6d, 0x6f, 0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x70, 0x72, 0x6f, 0x74,
	0x6f, 0x12, 0x0c, 0x76, 0x63, 0x2e, 0x62, 0x69, 0x7a, 0x6d, 0x6f, 0x6d, 0x65, 0x6e, 0x74, 0x1a,
	0x1c, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2f, 0x61, 0x70, 0x69, 0x2f, 0x61, 0x6e, 0x6e, 0x6f,
	0x74, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x2b, 0x70,
	0x72, 0x6f, 0x74, 0x6f, 0x63, 0x2d, 0x67, 0x65, 0x6e, 0x2d, 0x76, 0x61, 0x6c, 0x69, 0x64, 0x61,
	0x74, 0x65, 0x2f, 0x76, 0x61, 0x6c, 0x69, 0x64, 0x61, 0x74, 0x65, 0x2f, 0x76, 0x61, 0x6c, 0x69,
	0x64, 0x61, 0x74, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x19, 0x73, 0x76, 0x63, 0x6d,
	0x6f, 0x6d, 0x65, 0x6e, 0x74, 0x2f, 0x73, 0x76, 0x63, 0x6d, 0x6f, 0x6d, 0x65, 0x6e, 0x74, 0x2e,
	0x70, 0x72, 0x6f, 0x74, 0x6f, 0x22, 0x46, 0x0a, 0x13, 0x47, 0x65, 0x74, 0x43, 0x68, 0x61, 0x72,
	0x61, 0x63, 0x74, 0x65, 0x72, 0x4c, 0x69, 0x73, 0x74, 0x52, 0x65, 0x71, 0x12, 0x12, 0x0a, 0x04,
	0x70, 0x61, 0x67, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x05, 0x52, 0x04, 0x70, 0x61, 0x67, 0x65,
	0x12, 0x1b, 0x0a, 0x09, 0x70, 0x61, 0x67, 0x65, 0x5f, 0x73, 0x69, 0x7a, 0x65, 0x18, 0x02, 0x20,
	0x01, 0x28, 0x05, 0x52, 0x08, 0x70, 0x61, 0x67, 0x65, 0x53, 0x69, 0x7a, 0x65, 0x22, 0x6d, 0x0a,
	0x14, 0x47, 0x65, 0x74, 0x43, 0x68, 0x61, 0x72, 0x61, 0x63, 0x74, 0x65, 0x72, 0x4c, 0x69, 0x73,
	0x74, 0x52, 0x65, 0x73, 0x70, 0x12, 0x12, 0x0a, 0x04, 0x63, 0x6f, 0x64, 0x65, 0x18, 0x01, 0x20,
	0x01, 0x28, 0x05, 0x52, 0x04, 0x63, 0x6f, 0x64, 0x65, 0x12, 0x10, 0x0a, 0x03, 0x6d, 0x73, 0x67,
	0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x03, 0x6d, 0x73, 0x67, 0x12, 0x2f, 0x0a, 0x04, 0x64,
	0x61, 0x74, 0x61, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1b, 0x2e, 0x76, 0x63, 0x2e, 0x73,
	0x76, 0x63, 0x6d, 0x6f, 0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x43, 0x68, 0x61, 0x72, 0x61, 0x63, 0x74,
	0x65, 0x72, 0x4c, 0x69, 0x73, 0x74, 0x52, 0x04, 0x64, 0x61, 0x74, 0x61, 0x22, 0x68, 0x0a, 0x12,
	0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x43, 0x68, 0x61, 0x72, 0x61, 0x63, 0x74, 0x65, 0x72, 0x52,
	0x65, 0x71, 0x12, 0x12, 0x0a, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x12, 0x1d, 0x0a, 0x0a, 0x61, 0x76, 0x61, 0x74, 0x61, 0x72,
	0x5f, 0x75, 0x72, 0x6c, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x61, 0x76, 0x61, 0x74,
	0x61, 0x72, 0x55, 0x72, 0x6c, 0x12, 0x1f, 0x0a, 0x0b, 0x65, 0x78, 0x74, 0x65, 0x72, 0x6e, 0x61,
	0x6c, 0x5f, 0x69, 0x64, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0a, 0x65, 0x78, 0x74, 0x65,
	0x72, 0x6e, 0x61, 0x6c, 0x49, 0x64, 0x22, 0x76, 0x0a, 0x13, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65,
	0x43, 0x68, 0x61, 0x72, 0x61, 0x63, 0x74, 0x65, 0x72, 0x52, 0x65, 0x73, 0x70, 0x12, 0x12, 0x0a,
	0x04, 0x63, 0x6f, 0x64, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x05, 0x52, 0x04, 0x63, 0x6f, 0x64,
	0x65, 0x12, 0x10, 0x0a, 0x03, 0x6d, 0x73, 0x67, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x03,
	0x6d, 0x73, 0x67, 0x12, 0x39, 0x0a, 0x04, 0x64, 0x61, 0x74, 0x61, 0x18, 0x03, 0x20, 0x01, 0x28,
	0x0b, 0x32, 0x25, 0x2e, 0x76, 0x63, 0x2e, 0x73, 0x76, 0x63, 0x6d, 0x6f, 0x6d, 0x65, 0x6e, 0x74,
	0x2e, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x43, 0x68, 0x61, 0x72, 0x61, 0x63, 0x74, 0x65, 0x72,
	0x52, 0x65, 0x73, 0x70, 0x44, 0x61, 0x74, 0x61, 0x52, 0x04, 0x64, 0x61, 0x74, 0x61, 0x22, 0xbd,
	0x01, 0x0a, 0x10, 0x50, 0x75, 0x62, 0x6c, 0x69, 0x73, 0x68, 0x4d, 0x6f, 0x6d, 0x65, 0x6e, 0x74,
	0x52, 0x65, 0x71, 0x12, 0x21, 0x0a, 0x0c, 0x63, 0x68, 0x61, 0x72, 0x61, 0x63, 0x74, 0x65, 0x72,
	0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x52, 0x0b, 0x63, 0x68, 0x61, 0x72, 0x61,
	0x63, 0x74, 0x65, 0x72, 0x49, 0x64, 0x12, 0x28, 0x0a, 0x10, 0x6f, 0x72, 0x69, 0x67, 0x69, 0x6e,
	0x5f, 0x61, 0x75, 0x64, 0x69, 0x6f, 0x5f, 0x75, 0x72, 0x6c, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x0e, 0x6f, 0x72, 0x69, 0x67, 0x69, 0x6e, 0x41, 0x75, 0x64, 0x69, 0x6f, 0x55, 0x72, 0x6c,
	0x12, 0x26, 0x0a, 0x0f, 0x76, 0x6f, 0x69, 0x63, 0x65, 0x5f, 0x61, 0x75, 0x64, 0x69, 0x6f, 0x5f,
	0x75, 0x72, 0x6c, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0d, 0x76, 0x6f, 0x69, 0x63, 0x65,
	0x41, 0x75, 0x64, 0x69, 0x6f, 0x55, 0x72, 0x6c, 0x12, 0x18, 0x0a, 0x07, 0x63, 0x6f, 0x6e, 0x74,
	0x65, 0x6e, 0x74, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x63, 0x6f, 0x6e, 0x74, 0x65,
	0x6e, 0x74, 0x12, 0x1a, 0x0a, 0x08, 0x64, 0x75, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x18, 0x05,
	0x20, 0x01, 0x28, 0x05, 0x52, 0x08, 0x64, 0x75, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x22, 0x72,
	0x0a, 0x11, 0x50, 0x75, 0x62, 0x6c, 0x69, 0x73, 0x68, 0x4d, 0x6f, 0x6d, 0x65, 0x6e, 0x74, 0x52,
	0x65, 0x73, 0x70, 0x12, 0x12, 0x0a, 0x04, 0x63, 0x6f, 0x64, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28,
	0x05, 0x52, 0x04, 0x63, 0x6f, 0x64, 0x65, 0x12, 0x10, 0x0a, 0x03, 0x6d, 0x73, 0x67, 0x18, 0x02,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x03, 0x6d, 0x73, 0x67, 0x12, 0x37, 0x0a, 0x04, 0x64, 0x61, 0x74,
	0x61, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x23, 0x2e, 0x76, 0x63, 0x2e, 0x73, 0x76, 0x63,
	0x6d, 0x6f, 0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x50, 0x75, 0x62, 0x6c, 0x69, 0x73, 0x68, 0x4d, 0x6f,
	0x6d, 0x65, 0x6e, 0x74, 0x52, 0x65, 0x73, 0x70, 0x44, 0x61, 0x74, 0x61, 0x52, 0x04, 0x64, 0x61,
	0x74, 0x61, 0x22, 0x42, 0x0a, 0x0f, 0x47, 0x65, 0x74, 0x4d, 0x79, 0x4d, 0x6f, 0x6d, 0x65, 0x6e,
	0x74, 0x73, 0x52, 0x65, 0x71, 0x12, 0x12, 0x0a, 0x04, 0x70, 0x61, 0x67, 0x65, 0x18, 0x01, 0x20,
	0x01, 0x28, 0x05, 0x52, 0x04, 0x70, 0x61, 0x67, 0x65, 0x12, 0x1b, 0x0a, 0x09, 0x70, 0x61, 0x67,
	0x65, 0x5f, 0x73, 0x69, 0x7a, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x05, 0x52, 0x08, 0x70, 0x61,
	0x67, 0x65, 0x53, 0x69, 0x7a, 0x65, 0x22, 0x70, 0x0a, 0x10, 0x47, 0x65, 0x74, 0x4d, 0x79, 0x4d,
	0x6f, 0x6d, 0x65, 0x6e, 0x74, 0x73, 0x52, 0x65, 0x73, 0x70, 0x12, 0x12, 0x0a, 0x04, 0x63, 0x6f,
	0x64, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x05, 0x52, 0x04, 0x63, 0x6f, 0x64, 0x65, 0x12, 0x10,
	0x0a, 0x03, 0x6d, 0x73, 0x67, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x03, 0x6d, 0x73, 0x67,
	0x12, 0x36, 0x0a, 0x04, 0x64, 0x61, 0x74, 0x61, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x22,
	0x2e, 0x76, 0x63, 0x2e, 0x73, 0x76, 0x63, 0x6d, 0x6f, 0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x47, 0x65,
	0x74, 0x4d, 0x79, 0x4d, 0x6f, 0x6d, 0x65, 0x6e, 0x74, 0x73, 0x52, 0x65, 0x73, 0x70, 0x44, 0x61,
	0x74, 0x61, 0x52, 0x04, 0x64, 0x61, 0x74, 0x61, 0x22, 0x3d, 0x0a, 0x12, 0x47, 0x65, 0x74, 0x4d,
	0x6f, 0x6d, 0x65, 0x6e, 0x74, 0x73, 0x42, 0x79, 0x49, 0x64, 0x73, 0x52, 0x65, 0x71, 0x12, 0x27,
	0x0a, 0x0a, 0x6d, 0x6f, 0x6d, 0x65, 0x6e, 0x74, 0x5f, 0x69, 0x64, 0x73, 0x18, 0x01, 0x20, 0x03,
	0x28, 0x03, 0x42, 0x08, 0xfa, 0x42, 0x05, 0x92, 0x01, 0x02, 0x08, 0x01, 0x52, 0x09, 0x6d, 0x6f,
	0x6d, 0x65, 0x6e, 0x74, 0x49, 0x64, 0x73, 0x22, 0x76, 0x0a, 0x13, 0x47, 0x65, 0x74, 0x4d, 0x6f,
	0x6d, 0x65, 0x6e, 0x74, 0x73, 0x42, 0x79, 0x49, 0x64, 0x73, 0x52, 0x65, 0x73, 0x70, 0x12, 0x12,
	0x0a, 0x04, 0x63, 0x6f, 0x64, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x05, 0x52, 0x04, 0x63, 0x6f,
	0x64, 0x65, 0x12, 0x10, 0x0a, 0x03, 0x6d, 0x73, 0x67, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x03, 0x6d, 0x73, 0x67, 0x12, 0x39, 0x0a, 0x04, 0x64, 0x61, 0x74, 0x61, 0x18, 0x03, 0x20, 0x01,
	0x28, 0x0b, 0x32, 0x25, 0x2e, 0x76, 0x63, 0x2e, 0x73, 0x76, 0x63, 0x6d, 0x6f, 0x6d, 0x65, 0x6e,
	0x74, 0x2e, 0x47, 0x65, 0x74, 0x4d, 0x6f, 0x6d, 0x65, 0x6e, 0x74, 0x73, 0x42, 0x79, 0x49, 0x64,
	0x73, 0x52, 0x65, 0x73, 0x70, 0x44, 0x61, 0x74, 0x61, 0x52, 0x04, 0x64, 0x61, 0x74, 0x61, 0x22,
	0x61, 0x0a, 0x0d, 0x4c, 0x69, 0x6b, 0x65, 0x4d, 0x6f, 0x6d, 0x65, 0x6e, 0x74, 0x52, 0x65, 0x71,
	0x12, 0x24, 0x0a, 0x09, 0x6d, 0x6f, 0x6d, 0x65, 0x6e, 0x74, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20,
	0x01, 0x28, 0x03, 0x42, 0x07, 0xfa, 0x42, 0x04, 0x22, 0x02, 0x20, 0x00, 0x52, 0x08, 0x6d, 0x6f,
	0x6d, 0x65, 0x6e, 0x74, 0x49, 0x64, 0x12, 0x2a, 0x0a, 0x0c, 0x63, 0x68, 0x61, 0x72, 0x61, 0x63,
	0x74, 0x65, 0x72, 0x5f, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x03, 0x42, 0x07, 0xfa, 0x42,
	0x04, 0x22, 0x02, 0x20, 0x00, 0x52, 0x0b, 0x63, 0x68, 0x61, 0x72, 0x61, 0x63, 0x74, 0x65, 0x72,
	0x49, 0x64, 0x22, 0x36, 0x0a, 0x0e, 0x4c, 0x69, 0x6b, 0x65, 0x4d, 0x6f, 0x6d, 0x65, 0x6e, 0x74,
	0x52, 0x65, 0x73, 0x70, 0x12, 0x12, 0x0a, 0x04, 0x63, 0x6f, 0x64, 0x65, 0x18, 0x01, 0x20, 0x01,
	0x28, 0x05, 0x52, 0x04, 0x63, 0x6f, 0x64, 0x65, 0x12, 0x10, 0x0a, 0x03, 0x6d, 0x73, 0x67, 0x18,
	0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x03, 0x6d, 0x73, 0x67, 0x22, 0x37, 0x0a, 0x0f, 0x55, 0x6e,
	0x6c, 0x69, 0x6b, 0x65, 0x4d, 0x6f, 0x6d, 0x65, 0x6e, 0x74, 0x52, 0x65, 0x71, 0x12, 0x24, 0x0a,
	0x09, 0x6d, 0x6f, 0x6d, 0x65, 0x6e, 0x74, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03,
	0x42, 0x07, 0xfa, 0x42, 0x04, 0x22, 0x02, 0x20, 0x00, 0x52, 0x08, 0x6d, 0x6f, 0x6d, 0x65, 0x6e,
	0x74, 0x49, 0x64, 0x22, 0x38, 0x0a, 0x10, 0x55, 0x6e, 0x6c, 0x69, 0x6b, 0x65, 0x4d, 0x6f, 0x6d,
	0x65, 0x6e, 0x74, 0x52, 0x65, 0x73, 0x70, 0x12, 0x12, 0x0a, 0x04, 0x63, 0x6f, 0x64, 0x65, 0x18,
	0x01, 0x20, 0x01, 0x28, 0x05, 0x52, 0x04, 0x63, 0x6f, 0x64, 0x65, 0x12, 0x10, 0x0a, 0x03, 0x6d,
	0x73, 0x67, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x03, 0x6d, 0x73, 0x67, 0x22, 0x47, 0x0a,
	0x14, 0x47, 0x65, 0x74, 0x4d, 0x79, 0x4c, 0x69, 0x6b, 0x65, 0x64, 0x4d, 0x6f, 0x6d, 0x65, 0x6e,
	0x74, 0x73, 0x52, 0x65, 0x71, 0x12, 0x12, 0x0a, 0x04, 0x70, 0x61, 0x67, 0x65, 0x18, 0x01, 0x20,
	0x01, 0x28, 0x05, 0x52, 0x04, 0x70, 0x61, 0x67, 0x65, 0x12, 0x1b, 0x0a, 0x09, 0x70, 0x61, 0x67,
	0x65, 0x5f, 0x73, 0x69, 0x7a, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x05, 0x52, 0x08, 0x70, 0x61,
	0x67, 0x65, 0x53, 0x69, 0x7a, 0x65, 0x22, 0x7a, 0x0a, 0x15, 0x47, 0x65, 0x74, 0x4d, 0x79, 0x4c,
	0x69, 0x6b, 0x65, 0x64, 0x4d, 0x6f, 0x6d, 0x65, 0x6e, 0x74, 0x73, 0x52, 0x65, 0x73, 0x70, 0x12,
	0x12, 0x0a, 0x04, 0x63, 0x6f, 0x64, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x05, 0x52, 0x04, 0x63,
	0x6f, 0x64, 0x65, 0x12, 0x10, 0x0a, 0x03, 0x6d, 0x73, 0x67, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x03, 0x6d, 0x73, 0x67, 0x12, 0x3b, 0x0a, 0x04, 0x64, 0x61, 0x74, 0x61, 0x18, 0x03, 0x20,
	0x01, 0x28, 0x0b, 0x32, 0x27, 0x2e, 0x76, 0x63, 0x2e, 0x73, 0x76, 0x63, 0x6d, 0x6f, 0x6d, 0x65,
	0x6e, 0x74, 0x2e, 0x47, 0x65, 0x74, 0x4d, 0x79, 0x4c, 0x69, 0x6b, 0x65, 0x64, 0x4d, 0x6f, 0x6d,
	0x65, 0x6e, 0x74, 0x73, 0x52, 0x65, 0x73, 0x70, 0x44, 0x61, 0x74, 0x61, 0x52, 0x04, 0x64, 0x61,
	0x74, 0x61, 0x22, 0x44, 0x0a, 0x16, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x55, 0x73, 0x65, 0x72,
	0x43, 0x68, 0x61, 0x72, 0x61, 0x63, 0x74, 0x65, 0x72, 0x52, 0x65, 0x71, 0x12, 0x2a, 0x0a, 0x0c,
	0x63, 0x68, 0x61, 0x72, 0x61, 0x63, 0x74, 0x65, 0x72, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01,
	0x28, 0x03, 0x42, 0x07, 0xfa, 0x42, 0x04, 0x22, 0x02, 0x20, 0x00, 0x52, 0x0b, 0x63, 0x68, 0x61,
	0x72, 0x61, 0x63, 0x74, 0x65, 0x72, 0x49, 0x64, 0x22, 0x3f, 0x0a, 0x17, 0x55, 0x70, 0x64, 0x61,
	0x74, 0x65, 0x55, 0x73, 0x65, 0x72, 0x43, 0x68, 0x61, 0x72, 0x61, 0x63, 0x74, 0x65, 0x72, 0x52,
	0x65, 0x73, 0x70, 0x12, 0x12, 0x0a, 0x04, 0x63, 0x6f, 0x64, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28,
	0x05, 0x52, 0x04, 0x63, 0x6f, 0x64, 0x65, 0x12, 0x10, 0x0a, 0x03, 0x6d, 0x73, 0x67, 0x18, 0x02,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x03, 0x6d, 0x73, 0x67, 0x22, 0x15, 0x0a, 0x13, 0x47, 0x65, 0x74,
	0x55, 0x73, 0x65, 0x72, 0x43, 0x68, 0x61, 0x72, 0x61, 0x63, 0x74, 0x65, 0x72, 0x52, 0x65, 0x71,
	0x22, 0x78, 0x0a, 0x14, 0x47, 0x65, 0x74, 0x55, 0x73, 0x65, 0x72, 0x43, 0x68, 0x61, 0x72, 0x61,
	0x63, 0x74, 0x65, 0x72, 0x52, 0x65, 0x73, 0x70, 0x12, 0x12, 0x0a, 0x04, 0x63, 0x6f, 0x64, 0x65,
	0x18, 0x01, 0x20, 0x01, 0x28, 0x05, 0x52, 0x04, 0x63, 0x6f, 0x64, 0x65, 0x12, 0x10, 0x0a, 0x03,
	0x6d, 0x73, 0x67, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x03, 0x6d, 0x73, 0x67, 0x12, 0x3a,
	0x0a, 0x04, 0x64, 0x61, 0x74, 0x61, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x26, 0x2e, 0x76,
	0x63, 0x2e, 0x73, 0x76, 0x63, 0x6d, 0x6f, 0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x47, 0x65, 0x74, 0x55,
	0x73, 0x65, 0x72, 0x43, 0x68, 0x61, 0x72, 0x61, 0x63, 0x74, 0x65, 0x72, 0x52, 0x65, 0x73, 0x70,
	0x44, 0x61, 0x74, 0x61, 0x52, 0x04, 0x64, 0x61, 0x74, 0x61, 0x22, 0x45, 0x0a, 0x12, 0x47, 0x65,
	0x74, 0x4d, 0x6f, 0x6d, 0x65, 0x6e, 0x74, 0x53, 0x71, 0x75, 0x61, 0x72, 0x65, 0x52, 0x65, 0x71,
	0x12, 0x12, 0x0a, 0x04, 0x70, 0x61, 0x67, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x05, 0x52, 0x04,
	0x70, 0x61, 0x67, 0x65, 0x12, 0x1b, 0x0a, 0x09, 0x70, 0x61, 0x67, 0x65, 0x5f, 0x73, 0x69, 0x7a,
	0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x05, 0x52, 0x08, 0x70, 0x61, 0x67, 0x65, 0x53, 0x69, 0x7a,
	0x65, 0x22, 0x76, 0x0a, 0x13, 0x47, 0x65, 0x74, 0x4d, 0x6f, 0x6d, 0x65, 0x6e, 0x74, 0x53, 0x71,
	0x75, 0x61, 0x72, 0x65, 0x52, 0x65, 0x73, 0x70, 0x12, 0x12, 0x0a, 0x04, 0x63, 0x6f, 0x64, 0x65,
	0x18, 0x01, 0x20, 0x01, 0x28, 0x05, 0x52, 0x04, 0x63, 0x6f, 0x64, 0x65, 0x12, 0x10, 0x0a, 0x03,
	0x6d, 0x73, 0x67, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x03, 0x6d, 0x73, 0x67, 0x12, 0x39,
	0x0a, 0x04, 0x64, 0x61, 0x74, 0x61, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x25, 0x2e, 0x76,
	0x63, 0x2e, 0x73, 0x76, 0x63, 0x6d, 0x6f, 0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x47, 0x65, 0x74, 0x4d,
	0x6f, 0x6d, 0x65, 0x6e, 0x74, 0x53, 0x71, 0x75, 0x61, 0x72, 0x65, 0x52, 0x65, 0x73, 0x70, 0x44,
	0x61, 0x74, 0x61, 0x52, 0x04, 0x64, 0x61, 0x74, 0x61, 0x22, 0x32, 0x0a, 0x0a, 0x52, 0x65, 0x70,
	0x6f, 0x72, 0x74, 0x52, 0x65, 0x73, 0x70, 0x12, 0x12, 0x0a, 0x04, 0x63, 0x6f, 0x64, 0x65, 0x18,
	0x01, 0x20, 0x01, 0x28, 0x05, 0x52, 0x04, 0x63, 0x6f, 0x64, 0x65, 0x12, 0x10, 0x0a, 0x03, 0x6d,
	0x73, 0x67, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x03, 0x6d, 0x73, 0x67, 0x32, 0xf1, 0x0a,
	0x0a, 0x01, 0x73, 0x12, 0x80, 0x01, 0x0a, 0x10, 0x47, 0x65, 0x74, 0x43, 0x68, 0x61, 0x72, 0x61,
	0x63, 0x74, 0x65, 0x72, 0x4c, 0x69, 0x73, 0x74, 0x12, 0x21, 0x2e, 0x76, 0x63, 0x2e, 0x62, 0x69,
	0x7a, 0x6d, 0x6f, 0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x47, 0x65, 0x74, 0x43, 0x68, 0x61, 0x72, 0x61,
	0x63, 0x74, 0x65, 0x72, 0x4c, 0x69, 0x73, 0x74, 0x52, 0x65, 0x71, 0x1a, 0x22, 0x2e, 0x76, 0x63,
	0x2e, 0x62, 0x69, 0x7a, 0x6d, 0x6f, 0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x47, 0x65, 0x74, 0x43, 0x68,
	0x61, 0x72, 0x61, 0x63, 0x74, 0x65, 0x72, 0x4c, 0x69, 0x73, 0x74, 0x52, 0x65, 0x73, 0x70, 0x22,
	0x25, 0x82, 0xd3, 0xe4, 0x93, 0x02, 0x1f, 0x22, 0x1d, 0x2f, 0x76, 0x63, 0x2e, 0x62, 0x69, 0x7a,
	0x6d, 0x6f, 0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x73, 0x2f, 0x76, 0x31, 0x2f, 0x63, 0x68, 0x61, 0x72,
	0x61, 0x63, 0x74, 0x65, 0x72, 0x73, 0x12, 0x77, 0x0a, 0x0d, 0x50, 0x75, 0x62, 0x6c, 0x69, 0x73,
	0x68, 0x4d, 0x6f, 0x6d, 0x65, 0x6e, 0x74, 0x12, 0x1e, 0x2e, 0x76, 0x63, 0x2e, 0x62, 0x69, 0x7a,
	0x6d, 0x6f, 0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x50, 0x75, 0x62, 0x6c, 0x69, 0x73, 0x68, 0x4d, 0x6f,
	0x6d, 0x65, 0x6e, 0x74, 0x52, 0x65, 0x71, 0x1a, 0x1f, 0x2e, 0x76, 0x63, 0x2e, 0x62, 0x69, 0x7a,
	0x6d, 0x6f, 0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x50, 0x75, 0x62, 0x6c, 0x69, 0x73, 0x68, 0x4d, 0x6f,
	0x6d, 0x65, 0x6e, 0x74, 0x52, 0x65, 0x73, 0x70, 0x22, 0x25, 0x82, 0xd3, 0xe4, 0x93, 0x02, 0x1f,
	0x3a, 0x01, 0x2a, 0x22, 0x1a, 0x2f, 0x76, 0x63, 0x2e, 0x62, 0x69, 0x7a, 0x6d, 0x6f, 0x6d, 0x65,
	0x6e, 0x74, 0x2e, 0x73, 0x2f, 0x76, 0x31, 0x2f, 0x70, 0x75, 0x62, 0x6c, 0x69, 0x73, 0x68, 0x12,
	0x74, 0x0a, 0x0c, 0x47, 0x65, 0x74, 0x4d, 0x79, 0x4d, 0x6f, 0x6d, 0x65, 0x6e, 0x74, 0x73, 0x12,
	0x1d, 0x2e, 0x76, 0x63, 0x2e, 0x62, 0x69, 0x7a, 0x6d, 0x6f, 0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x47,
	0x65, 0x74, 0x4d, 0x79, 0x4d, 0x6f, 0x6d, 0x65, 0x6e, 0x74, 0x73, 0x52, 0x65, 0x71, 0x1a, 0x1e,
	0x2e, 0x76, 0x63, 0x2e, 0x62, 0x69, 0x7a, 0x6d, 0x6f, 0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x47, 0x65,
	0x74, 0x4d, 0x79, 0x4d, 0x6f, 0x6d, 0x65, 0x6e, 0x74, 0x73, 0x52, 0x65, 0x73, 0x70, 0x22, 0x25,
	0x82, 0xd3, 0xe4, 0x93, 0x02, 0x1f, 0x22, 0x1d, 0x2f, 0x76, 0x63, 0x2e, 0x62, 0x69, 0x7a, 0x6d,
	0x6f, 0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x73, 0x2f, 0x76, 0x31, 0x2f, 0x6d, 0x79, 0x2d, 0x6d, 0x6f,
	0x6d, 0x65, 0x6e, 0x74, 0x73, 0x12, 0x84, 0x01, 0x0a, 0x0f, 0x47, 0x65, 0x74, 0x4d, 0x6f, 0x6d,
	0x65, 0x6e, 0x74, 0x73, 0x42, 0x79, 0x49, 0x64, 0x73, 0x12, 0x20, 0x2e, 0x76, 0x63, 0x2e, 0x62,
	0x69, 0x7a, 0x6d, 0x6f, 0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x47, 0x65, 0x74, 0x4d, 0x6f, 0x6d, 0x65,
	0x6e, 0x74, 0x73, 0x42, 0x79, 0x49, 0x64, 0x73, 0x52, 0x65, 0x71, 0x1a, 0x21, 0x2e, 0x76, 0x63,
	0x2e, 0x62, 0x69, 0x7a, 0x6d, 0x6f, 0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x47, 0x65, 0x74, 0x4d, 0x6f,
	0x6d, 0x65, 0x6e, 0x74, 0x73, 0x42, 0x79, 0x49, 0x64, 0x73, 0x52, 0x65, 0x73, 0x70, 0x22, 0x2c,
	0x82, 0xd3, 0xe4, 0x93, 0x02, 0x26, 0x3a, 0x01, 0x2a, 0x22, 0x21, 0x2f, 0x76, 0x63, 0x2e, 0x62,
	0x69, 0x7a, 0x6d, 0x6f, 0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x73, 0x2f, 0x76, 0x31, 0x2f, 0x6d, 0x6f,
	0x6d, 0x65, 0x6e, 0x74, 0x73, 0x2d, 0x62, 0x79, 0x2d, 0x69, 0x64, 0x73, 0x12, 0x6b, 0x0a, 0x0a,
	0x4c, 0x69, 0x6b, 0x65, 0x4d, 0x6f, 0x6d, 0x65, 0x6e, 0x74, 0x12, 0x1b, 0x2e, 0x76, 0x63, 0x2e,
	0x62, 0x69, 0x7a, 0x6d, 0x6f, 0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x4c, 0x69, 0x6b, 0x65, 0x4d, 0x6f,
	0x6d, 0x65, 0x6e, 0x74, 0x52, 0x65, 0x71, 0x1a, 0x1c, 0x2e, 0x76, 0x63, 0x2e, 0x62, 0x69, 0x7a,
	0x6d, 0x6f, 0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x4c, 0x69, 0x6b, 0x65, 0x4d, 0x6f, 0x6d, 0x65, 0x6e,
	0x74, 0x52, 0x65, 0x73, 0x70, 0x22, 0x22, 0x82, 0xd3, 0xe4, 0x93, 0x02, 0x1c, 0x3a, 0x01, 0x2a,
	0x22, 0x17, 0x2f, 0x76, 0x63, 0x2e, 0x62, 0x69, 0x7a, 0x6d, 0x6f, 0x6d, 0x65, 0x6e, 0x74, 0x2e,
	0x73, 0x2f, 0x76, 0x31, 0x2f, 0x6c, 0x69, 0x6b, 0x65, 0x12, 0x73, 0x0a, 0x0c, 0x55, 0x6e, 0x6c,
	0x69, 0x6b, 0x65, 0x4d, 0x6f, 0x6d, 0x65, 0x6e, 0x74, 0x12, 0x1d, 0x2e, 0x76, 0x63, 0x2e, 0x62,
	0x69, 0x7a, 0x6d, 0x6f, 0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x55, 0x6e, 0x6c, 0x69, 0x6b, 0x65, 0x4d,
	0x6f, 0x6d, 0x65, 0x6e, 0x74, 0x52, 0x65, 0x71, 0x1a, 0x1e, 0x2e, 0x76, 0x63, 0x2e, 0x62, 0x69,
	0x7a, 0x6d, 0x6f, 0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x55, 0x6e, 0x6c, 0x69, 0x6b, 0x65, 0x4d, 0x6f,
	0x6d, 0x65, 0x6e, 0x74, 0x52, 0x65, 0x73, 0x70, 0x22, 0x24, 0x82, 0xd3, 0xe4, 0x93, 0x02, 0x1e,
	0x3a, 0x01, 0x2a, 0x22, 0x19, 0x2f, 0x76, 0x63, 0x2e, 0x62, 0x69, 0x7a, 0x6d, 0x6f, 0x6d, 0x65,
	0x6e, 0x74, 0x2e, 0x73, 0x2f, 0x76, 0x31, 0x2f, 0x75, 0x6e, 0x6c, 0x69, 0x6b, 0x65, 0x12, 0x89,
	0x01, 0x0a, 0x11, 0x47, 0x65, 0x74, 0x4d, 0x79, 0x4c, 0x69, 0x6b, 0x65, 0x64, 0x4d, 0x6f, 0x6d,
	0x65, 0x6e, 0x74, 0x73, 0x12, 0x22, 0x2e, 0x76, 0x63, 0x2e, 0x62, 0x69, 0x7a, 0x6d, 0x6f, 0x6d,
	0x65, 0x6e, 0x74, 0x2e, 0x47, 0x65, 0x74, 0x4d, 0x79, 0x4c, 0x69, 0x6b, 0x65, 0x64, 0x4d, 0x6f,
	0x6d, 0x65, 0x6e, 0x74, 0x73, 0x52, 0x65, 0x71, 0x1a, 0x23, 0x2e, 0x76, 0x63, 0x2e, 0x62, 0x69,
	0x7a, 0x6d, 0x6f, 0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x47, 0x65, 0x74, 0x4d, 0x79, 0x4c, 0x69, 0x6b,
	0x65, 0x64, 0x4d, 0x6f, 0x6d, 0x65, 0x6e, 0x74, 0x73, 0x52, 0x65, 0x73, 0x70, 0x22, 0x2b, 0x82,
	0xd3, 0xe4, 0x93, 0x02, 0x25, 0x22, 0x23, 0x2f, 0x76, 0x63, 0x2e, 0x62, 0x69, 0x7a, 0x6d, 0x6f,
	0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x73, 0x2f, 0x76, 0x31, 0x2f, 0x6d, 0x79, 0x2d, 0x6c, 0x69, 0x6b,
	0x65, 0x64, 0x2d, 0x6d, 0x6f, 0x6d, 0x65, 0x6e, 0x74, 0x73, 0x12, 0x97, 0x01, 0x0a, 0x13, 0x55,
	0x70, 0x64, 0x61, 0x74, 0x65, 0x55, 0x73, 0x65, 0x72, 0x43, 0x68, 0x61, 0x72, 0x61, 0x63, 0x74,
	0x65, 0x72, 0x12, 0x24, 0x2e, 0x76, 0x63, 0x2e, 0x62, 0x69, 0x7a, 0x6d, 0x6f, 0x6d, 0x65, 0x6e,
	0x74, 0x2e, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x55, 0x73, 0x65, 0x72, 0x43, 0x68, 0x61, 0x72,
	0x61, 0x63, 0x74, 0x65, 0x72, 0x52, 0x65, 0x71, 0x1a, 0x25, 0x2e, 0x76, 0x63, 0x2e, 0x62, 0x69,
	0x7a, 0x6d, 0x6f, 0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x55, 0x73,
	0x65, 0x72, 0x43, 0x68, 0x61, 0x72, 0x61, 0x63, 0x74, 0x65, 0x72, 0x52, 0x65, 0x73, 0x70, 0x22,
	0x33, 0x82, 0xd3, 0xe4, 0x93, 0x02, 0x2d, 0x3a, 0x01, 0x2a, 0x22, 0x28, 0x2f, 0x76, 0x63, 0x2e,
	0x62, 0x69, 0x7a, 0x6d, 0x6f, 0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x73, 0x2f, 0x76, 0x31, 0x2f, 0x75,
	0x70, 0x64, 0x61, 0x74, 0x65, 0x2d, 0x75, 0x73, 0x65, 0x72, 0x2d, 0x63, 0x68, 0x61, 0x72, 0x61,
	0x63, 0x74, 0x65, 0x72, 0x12, 0x84, 0x01, 0x0a, 0x10, 0x47, 0x65, 0x74, 0x55, 0x73, 0x65, 0x72,
	0x43, 0x68, 0x61, 0x72, 0x61, 0x63, 0x74, 0x65, 0x72, 0x12, 0x21, 0x2e, 0x76, 0x63, 0x2e, 0x62,
	0x69, 0x7a, 0x6d, 0x6f, 0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x47, 0x65, 0x74, 0x55, 0x73, 0x65, 0x72,
	0x43, 0x68, 0x61, 0x72, 0x61, 0x63, 0x74, 0x65, 0x72, 0x52, 0x65, 0x71, 0x1a, 0x22, 0x2e, 0x76,
	0x63, 0x2e, 0x62, 0x69, 0x7a, 0x6d, 0x6f, 0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x47, 0x65, 0x74, 0x55,
	0x73, 0x65, 0x72, 0x43, 0x68, 0x61, 0x72, 0x61, 0x63, 0x74, 0x65, 0x72, 0x52, 0x65, 0x73, 0x70,
	0x22, 0x29, 0x82, 0xd3, 0xe4, 0x93, 0x02, 0x23, 0x22, 0x21, 0x2f, 0x76, 0x63, 0x2e, 0x62, 0x69,
	0x7a, 0x6d, 0x6f, 0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x73, 0x2f, 0x76, 0x31, 0x2f, 0x75, 0x73, 0x65,
	0x72, 0x2d, 0x63, 0x68, 0x61, 0x72, 0x61, 0x63, 0x74, 0x65, 0x72, 0x12, 0x80, 0x01, 0x0a, 0x0f,
	0x47, 0x65, 0x74, 0x4d, 0x6f, 0x6d, 0x65, 0x6e, 0x74, 0x53, 0x71, 0x75, 0x61, 0x72, 0x65, 0x12,
	0x20, 0x2e, 0x76, 0x63, 0x2e, 0x62, 0x69, 0x7a, 0x6d, 0x6f, 0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x47,
	0x65, 0x74, 0x4d, 0x6f, 0x6d, 0x65, 0x6e, 0x74, 0x53, 0x71, 0x75, 0x61, 0x72, 0x65, 0x52, 0x65,
	0x71, 0x1a, 0x21, 0x2e, 0x76, 0x63, 0x2e, 0x62, 0x69, 0x7a, 0x6d, 0x6f, 0x6d, 0x65, 0x6e, 0x74,
	0x2e, 0x47, 0x65, 0x74, 0x4d, 0x6f, 0x6d, 0x65, 0x6e, 0x74, 0x53, 0x71, 0x75, 0x61, 0x72, 0x65,
	0x52, 0x65, 0x73, 0x70, 0x22, 0x28, 0x82, 0xd3, 0xe4, 0x93, 0x02, 0x22, 0x22, 0x20, 0x2f, 0x76,
	0x63, 0x2e, 0x62, 0x69, 0x7a, 0x6d, 0x6f, 0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x73, 0x2f, 0x76, 0x31,
	0x2f, 0x6d, 0x6f, 0x6d, 0x65, 0x6e, 0x74, 0x2d, 0x73, 0x71, 0x75, 0x61, 0x72, 0x65, 0x12, 0x61,
	0x0a, 0x06, 0x52, 0x65, 0x70, 0x6f, 0x72, 0x74, 0x12, 0x17, 0x2e, 0x76, 0x63, 0x2e, 0x73, 0x76,
	0x63, 0x6d, 0x6f, 0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x52, 0x65, 0x70, 0x6f, 0x72, 0x74, 0x52, 0x65,
	0x71, 0x1a, 0x18, 0x2e, 0x76, 0x63, 0x2e, 0x62, 0x69, 0x7a, 0x6d, 0x6f, 0x6d, 0x65, 0x6e, 0x74,
	0x2e, 0x52, 0x65, 0x70, 0x6f, 0x72, 0x74, 0x52, 0x65, 0x73, 0x70, 0x22, 0x24, 0x82, 0xd3, 0xe4,
	0x93, 0x02, 0x1e, 0x3a, 0x01, 0x2a, 0x22, 0x19, 0x2f, 0x76, 0x63, 0x2e, 0x62, 0x69, 0x7a, 0x6d,
	0x6f, 0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x73, 0x2f, 0x76, 0x31, 0x2f, 0x72, 0x65, 0x70, 0x6f, 0x72,
	0x74, 0x42, 0x3d, 0x5a, 0x3b, 0x6e, 0x65, 0x77, 0x2d, 0x67, 0x69, 0x74, 0x6c, 0x61, 0x62, 0x2e,
	0x78, 0x75, 0x6e, 0x6c, 0x65, 0x69, 0x2e, 0x63, 0x6e, 0x2f, 0x76, 0x63, 0x70, 0x72, 0x6f, 0x6a,
	0x65, 0x63, 0x74, 0x2f, 0x62, 0x61, 0x63, 0x6b, 0x65, 0x6e, 0x64, 0x73, 0x2f, 0x70, 0x72, 0x6f,
	0x74, 0x6f, 0x2f, 0x61, 0x70, 0x69, 0x2f, 0x62, 0x69, 0x7a, 0x6d, 0x6f, 0x6d, 0x65, 0x6e, 0x74,
	0x62, 0x06, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_bizmoment_proto_rawDescOnce sync.Once
	file_bizmoment_proto_rawDescData = file_bizmoment_proto_rawDesc
)

func file_bizmoment_proto_rawDescGZIP() []byte {
	file_bizmoment_proto_rawDescOnce.Do(func() {
		file_bizmoment_proto_rawDescData = protoimpl.X.CompressGZIP(file_bizmoment_proto_rawDescData)
	})
	return file_bizmoment_proto_rawDescData
}

var file_bizmoment_proto_msgTypes = make([]protoimpl.MessageInfo, 23)
var file_bizmoment_proto_goTypes = []interface{}{
	(*GetCharacterListReq)(nil),                 // 0: vc.bizmoment.GetCharacterListReq
	(*GetCharacterListResp)(nil),                // 1: vc.bizmoment.GetCharacterListResp
	(*CreateCharacterReq)(nil),                  // 2: vc.bizmoment.CreateCharacterReq
	(*CreateCharacterResp)(nil),                 // 3: vc.bizmoment.CreateCharacterResp
	(*PublishMomentReq)(nil),                    // 4: vc.bizmoment.PublishMomentReq
	(*PublishMomentResp)(nil),                   // 5: vc.bizmoment.PublishMomentResp
	(*GetMyMomentsReq)(nil),                     // 6: vc.bizmoment.GetMyMomentsReq
	(*GetMyMomentsResp)(nil),                    // 7: vc.bizmoment.GetMyMomentsResp
	(*GetMomentsByIdsReq)(nil),                  // 8: vc.bizmoment.GetMomentsByIdsReq
	(*GetMomentsByIdsResp)(nil),                 // 9: vc.bizmoment.GetMomentsByIdsResp
	(*LikeMomentReq)(nil),                       // 10: vc.bizmoment.LikeMomentReq
	(*LikeMomentResp)(nil),                      // 11: vc.bizmoment.LikeMomentResp
	(*UnlikeMomentReq)(nil),                     // 12: vc.bizmoment.UnlikeMomentReq
	(*UnlikeMomentResp)(nil),                    // 13: vc.bizmoment.UnlikeMomentResp
	(*GetMyLikedMomentsReq)(nil),                // 14: vc.bizmoment.GetMyLikedMomentsReq
	(*GetMyLikedMomentsResp)(nil),               // 15: vc.bizmoment.GetMyLikedMomentsResp
	(*UpdateUserCharacterReq)(nil),              // 16: vc.bizmoment.UpdateUserCharacterReq
	(*UpdateUserCharacterResp)(nil),             // 17: vc.bizmoment.UpdateUserCharacterResp
	(*GetUserCharacterReq)(nil),                 // 18: vc.bizmoment.GetUserCharacterReq
	(*GetUserCharacterResp)(nil),                // 19: vc.bizmoment.GetUserCharacterResp
	(*GetMomentSquareReq)(nil),                  // 20: vc.bizmoment.GetMomentSquareReq
	(*GetMomentSquareResp)(nil),                 // 21: vc.bizmoment.GetMomentSquareResp
	(*ReportResp)(nil),                          // 22: vc.bizmoment.ReportResp
	(*svcmoment.CharacterList)(nil),             // 23: vc.svcmoment.CharacterList
	(*svcmoment.CreateCharacterRespData)(nil),   // 24: vc.svcmoment.CreateCharacterRespData
	(*svcmoment.PublishMomentRespData)(nil),     // 25: vc.svcmoment.PublishMomentRespData
	(*svcmoment.GetMyMomentsRespData)(nil),      // 26: vc.svcmoment.GetMyMomentsRespData
	(*svcmoment.GetMomentsByIdsRespData)(nil),   // 27: vc.svcmoment.GetMomentsByIdsRespData
	(*svcmoment.GetMyLikedMomentsRespData)(nil), // 28: vc.svcmoment.GetMyLikedMomentsRespData
	(*svcmoment.GetUserCharacterRespData)(nil),  // 29: vc.svcmoment.GetUserCharacterRespData
	(*svcmoment.GetMomentSquareRespData)(nil),   // 30: vc.svcmoment.GetMomentSquareRespData
	(*svcmoment.ReportReq)(nil),                 // 31: vc.svcmoment.ReportReq
}
var file_bizmoment_proto_depIdxs = []int32{
	23, // 0: vc.bizmoment.GetCharacterListResp.data:type_name -> vc.svcmoment.CharacterList
	24, // 1: vc.bizmoment.CreateCharacterResp.data:type_name -> vc.svcmoment.CreateCharacterRespData
	25, // 2: vc.bizmoment.PublishMomentResp.data:type_name -> vc.svcmoment.PublishMomentRespData
	26, // 3: vc.bizmoment.GetMyMomentsResp.data:type_name -> vc.svcmoment.GetMyMomentsRespData
	27, // 4: vc.bizmoment.GetMomentsByIdsResp.data:type_name -> vc.svcmoment.GetMomentsByIdsRespData
	28, // 5: vc.bizmoment.GetMyLikedMomentsResp.data:type_name -> vc.svcmoment.GetMyLikedMomentsRespData
	29, // 6: vc.bizmoment.GetUserCharacterResp.data:type_name -> vc.svcmoment.GetUserCharacterRespData
	30, // 7: vc.bizmoment.GetMomentSquareResp.data:type_name -> vc.svcmoment.GetMomentSquareRespData
	0,  // 8: vc.bizmoment.s.GetCharacterList:input_type -> vc.bizmoment.GetCharacterListReq
	4,  // 9: vc.bizmoment.s.PublishMoment:input_type -> vc.bizmoment.PublishMomentReq
	6,  // 10: vc.bizmoment.s.GetMyMoments:input_type -> vc.bizmoment.GetMyMomentsReq
	8,  // 11: vc.bizmoment.s.GetMomentsByIds:input_type -> vc.bizmoment.GetMomentsByIdsReq
	10, // 12: vc.bizmoment.s.LikeMoment:input_type -> vc.bizmoment.LikeMomentReq
	12, // 13: vc.bizmoment.s.UnlikeMoment:input_type -> vc.bizmoment.UnlikeMomentReq
	14, // 14: vc.bizmoment.s.GetMyLikedMoments:input_type -> vc.bizmoment.GetMyLikedMomentsReq
	16, // 15: vc.bizmoment.s.UpdateUserCharacter:input_type -> vc.bizmoment.UpdateUserCharacterReq
	18, // 16: vc.bizmoment.s.GetUserCharacter:input_type -> vc.bizmoment.GetUserCharacterReq
	20, // 17: vc.bizmoment.s.GetMomentSquare:input_type -> vc.bizmoment.GetMomentSquareReq
	31, // 18: vc.bizmoment.s.Report:input_type -> vc.svcmoment.ReportReq
	1,  // 19: vc.bizmoment.s.GetCharacterList:output_type -> vc.bizmoment.GetCharacterListResp
	5,  // 20: vc.bizmoment.s.PublishMoment:output_type -> vc.bizmoment.PublishMomentResp
	7,  // 21: vc.bizmoment.s.GetMyMoments:output_type -> vc.bizmoment.GetMyMomentsResp
	9,  // 22: vc.bizmoment.s.GetMomentsByIds:output_type -> vc.bizmoment.GetMomentsByIdsResp
	11, // 23: vc.bizmoment.s.LikeMoment:output_type -> vc.bizmoment.LikeMomentResp
	13, // 24: vc.bizmoment.s.UnlikeMoment:output_type -> vc.bizmoment.UnlikeMomentResp
	15, // 25: vc.bizmoment.s.GetMyLikedMoments:output_type -> vc.bizmoment.GetMyLikedMomentsResp
	17, // 26: vc.bizmoment.s.UpdateUserCharacter:output_type -> vc.bizmoment.UpdateUserCharacterResp
	19, // 27: vc.bizmoment.s.GetUserCharacter:output_type -> vc.bizmoment.GetUserCharacterResp
	21, // 28: vc.bizmoment.s.GetMomentSquare:output_type -> vc.bizmoment.GetMomentSquareResp
	22, // 29: vc.bizmoment.s.Report:output_type -> vc.bizmoment.ReportResp
	19, // [19:30] is the sub-list for method output_type
	8,  // [8:19] is the sub-list for method input_type
	8,  // [8:8] is the sub-list for extension type_name
	8,  // [8:8] is the sub-list for extension extendee
	0,  // [0:8] is the sub-list for field type_name
}

func init() { file_bizmoment_proto_init() }
func file_bizmoment_proto_init() {
	if File_bizmoment_proto != nil {
		return
	}
	if !protoimpl.UnsafeEnabled {
		file_bizmoment_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetCharacterListReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_bizmoment_proto_msgTypes[1].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetCharacterListResp); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_bizmoment_proto_msgTypes[2].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CreateCharacterReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_bizmoment_proto_msgTypes[3].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CreateCharacterResp); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_bizmoment_proto_msgTypes[4].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*PublishMomentReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_bizmoment_proto_msgTypes[5].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*PublishMomentResp); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_bizmoment_proto_msgTypes[6].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetMyMomentsReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_bizmoment_proto_msgTypes[7].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetMyMomentsResp); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_bizmoment_proto_msgTypes[8].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetMomentsByIdsReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_bizmoment_proto_msgTypes[9].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetMomentsByIdsResp); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_bizmoment_proto_msgTypes[10].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*LikeMomentReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_bizmoment_proto_msgTypes[11].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*LikeMomentResp); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_bizmoment_proto_msgTypes[12].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*UnlikeMomentReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_bizmoment_proto_msgTypes[13].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*UnlikeMomentResp); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_bizmoment_proto_msgTypes[14].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetMyLikedMomentsReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_bizmoment_proto_msgTypes[15].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetMyLikedMomentsResp); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_bizmoment_proto_msgTypes[16].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*UpdateUserCharacterReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_bizmoment_proto_msgTypes[17].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*UpdateUserCharacterResp); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_bizmoment_proto_msgTypes[18].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetUserCharacterReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_bizmoment_proto_msgTypes[19].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetUserCharacterResp); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_bizmoment_proto_msgTypes[20].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetMomentSquareReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_bizmoment_proto_msgTypes[21].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetMomentSquareResp); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_bizmoment_proto_msgTypes[22].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ReportResp); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_bizmoment_proto_rawDesc,
			NumEnums:      0,
			NumMessages:   23,
			NumExtensions: 0,
			NumServices:   1,
		},
		GoTypes:           file_bizmoment_proto_goTypes,
		DependencyIndexes: file_bizmoment_proto_depIdxs,
		MessageInfos:      file_bizmoment_proto_msgTypes,
	}.Build()
	File_bizmoment_proto = out.File
	file_bizmoment_proto_rawDesc = nil
	file_bizmoment_proto_goTypes = nil
	file_bizmoment_proto_depIdxs = nil
}
