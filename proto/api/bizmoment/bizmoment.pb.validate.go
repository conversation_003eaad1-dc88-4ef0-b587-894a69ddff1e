// Code generated by protoc-gen-validate. DO NOT EDIT.
// source: bizmoment.proto

package bizmoment

import (
	"bytes"
	"errors"
	"fmt"
	"net"
	"net/mail"
	"net/url"
	"regexp"
	"sort"
	"strings"
	"time"
	"unicode/utf8"

	"google.golang.org/protobuf/types/known/anypb"
)

// ensure the imports are used
var (
	_ = bytes.MinRead
	_ = errors.New("")
	_ = fmt.Print
	_ = utf8.UTFMax
	_ = (*regexp.Regexp)(nil)
	_ = (*strings.Reader)(nil)
	_ = net.IPv4len
	_ = time.Duration(0)
	_ = (*url.URL)(nil)
	_ = (*mail.Address)(nil)
	_ = anypb.Any{}
	_ = sort.Sort
)

// Validate checks the field values on GetCharacterListReq with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *GetCharacterListReq) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GetCharacterListReq with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// GetCharacterListReqMultiError, or nil if none found.
func (m *GetCharacterListReq) ValidateAll() error {
	return m.validate(true)
}

func (m *GetCharacterListReq) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Page

	// no validation rules for PageSize

	if len(errors) > 0 {
		return GetCharacterListReqMultiError(errors)
	}

	return nil
}

// GetCharacterListReqMultiError is an error wrapping multiple validation
// errors returned by GetCharacterListReq.ValidateAll() if the designated
// constraints aren't met.
type GetCharacterListReqMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetCharacterListReqMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetCharacterListReqMultiError) AllErrors() []error { return m }

// GetCharacterListReqValidationError is the validation error returned by
// GetCharacterListReq.Validate if the designated constraints aren't met.
type GetCharacterListReqValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetCharacterListReqValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetCharacterListReqValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetCharacterListReqValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetCharacterListReqValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetCharacterListReqValidationError) ErrorName() string {
	return "GetCharacterListReqValidationError"
}

// Error satisfies the builtin error interface
func (e GetCharacterListReqValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetCharacterListReq.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetCharacterListReqValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetCharacterListReqValidationError{}

// Validate checks the field values on GetCharacterListResp with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *GetCharacterListResp) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GetCharacterListResp with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// GetCharacterListRespMultiError, or nil if none found.
func (m *GetCharacterListResp) ValidateAll() error {
	return m.validate(true)
}

func (m *GetCharacterListResp) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Code

	// no validation rules for Msg

	if all {
		switch v := interface{}(m.GetData()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetCharacterListRespValidationError{
					field:  "Data",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetCharacterListRespValidationError{
					field:  "Data",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetData()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetCharacterListRespValidationError{
				field:  "Data",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return GetCharacterListRespMultiError(errors)
	}

	return nil
}

// GetCharacterListRespMultiError is an error wrapping multiple validation
// errors returned by GetCharacterListResp.ValidateAll() if the designated
// constraints aren't met.
type GetCharacterListRespMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetCharacterListRespMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetCharacterListRespMultiError) AllErrors() []error { return m }

// GetCharacterListRespValidationError is the validation error returned by
// GetCharacterListResp.Validate if the designated constraints aren't met.
type GetCharacterListRespValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetCharacterListRespValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetCharacterListRespValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetCharacterListRespValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetCharacterListRespValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetCharacterListRespValidationError) ErrorName() string {
	return "GetCharacterListRespValidationError"
}

// Error satisfies the builtin error interface
func (e GetCharacterListRespValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetCharacterListResp.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetCharacterListRespValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetCharacterListRespValidationError{}

// Validate checks the field values on CreateCharacterReq with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *CreateCharacterReq) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on CreateCharacterReq with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// CreateCharacterReqMultiError, or nil if none found.
func (m *CreateCharacterReq) ValidateAll() error {
	return m.validate(true)
}

func (m *CreateCharacterReq) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Name

	// no validation rules for AvatarUrl

	// no validation rules for ExternalId

	if len(errors) > 0 {
		return CreateCharacterReqMultiError(errors)
	}

	return nil
}

// CreateCharacterReqMultiError is an error wrapping multiple validation errors
// returned by CreateCharacterReq.ValidateAll() if the designated constraints
// aren't met.
type CreateCharacterReqMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m CreateCharacterReqMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m CreateCharacterReqMultiError) AllErrors() []error { return m }

// CreateCharacterReqValidationError is the validation error returned by
// CreateCharacterReq.Validate if the designated constraints aren't met.
type CreateCharacterReqValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e CreateCharacterReqValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e CreateCharacterReqValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e CreateCharacterReqValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e CreateCharacterReqValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e CreateCharacterReqValidationError) ErrorName() string {
	return "CreateCharacterReqValidationError"
}

// Error satisfies the builtin error interface
func (e CreateCharacterReqValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sCreateCharacterReq.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = CreateCharacterReqValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = CreateCharacterReqValidationError{}

// Validate checks the field values on CreateCharacterResp with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *CreateCharacterResp) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on CreateCharacterResp with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// CreateCharacterRespMultiError, or nil if none found.
func (m *CreateCharacterResp) ValidateAll() error {
	return m.validate(true)
}

func (m *CreateCharacterResp) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Code

	// no validation rules for Msg

	if all {
		switch v := interface{}(m.GetData()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, CreateCharacterRespValidationError{
					field:  "Data",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, CreateCharacterRespValidationError{
					field:  "Data",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetData()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return CreateCharacterRespValidationError{
				field:  "Data",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return CreateCharacterRespMultiError(errors)
	}

	return nil
}

// CreateCharacterRespMultiError is an error wrapping multiple validation
// errors returned by CreateCharacterResp.ValidateAll() if the designated
// constraints aren't met.
type CreateCharacterRespMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m CreateCharacterRespMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m CreateCharacterRespMultiError) AllErrors() []error { return m }

// CreateCharacterRespValidationError is the validation error returned by
// CreateCharacterResp.Validate if the designated constraints aren't met.
type CreateCharacterRespValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e CreateCharacterRespValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e CreateCharacterRespValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e CreateCharacterRespValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e CreateCharacterRespValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e CreateCharacterRespValidationError) ErrorName() string {
	return "CreateCharacterRespValidationError"
}

// Error satisfies the builtin error interface
func (e CreateCharacterRespValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sCreateCharacterResp.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = CreateCharacterRespValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = CreateCharacterRespValidationError{}

// Validate checks the field values on PublishMomentReq with the rules defined
// in the proto definition for this message. If any rules are violated, the
// first error encountered is returned, or nil if there are no violations.
func (m *PublishMomentReq) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on PublishMomentReq with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// PublishMomentReqMultiError, or nil if none found.
func (m *PublishMomentReq) ValidateAll() error {
	return m.validate(true)
}

func (m *PublishMomentReq) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for CharacterId

	// no validation rules for OriginAudioUrl

	// no validation rules for VoiceAudioUrl

	// no validation rules for Content

	// no validation rules for Duration

	if len(errors) > 0 {
		return PublishMomentReqMultiError(errors)
	}

	return nil
}

// PublishMomentReqMultiError is an error wrapping multiple validation errors
// returned by PublishMomentReq.ValidateAll() if the designated constraints
// aren't met.
type PublishMomentReqMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m PublishMomentReqMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m PublishMomentReqMultiError) AllErrors() []error { return m }

// PublishMomentReqValidationError is the validation error returned by
// PublishMomentReq.Validate if the designated constraints aren't met.
type PublishMomentReqValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e PublishMomentReqValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e PublishMomentReqValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e PublishMomentReqValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e PublishMomentReqValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e PublishMomentReqValidationError) ErrorName() string { return "PublishMomentReqValidationError" }

// Error satisfies the builtin error interface
func (e PublishMomentReqValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sPublishMomentReq.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = PublishMomentReqValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = PublishMomentReqValidationError{}

// Validate checks the field values on PublishMomentResp with the rules defined
// in the proto definition for this message. If any rules are violated, the
// first error encountered is returned, or nil if there are no violations.
func (m *PublishMomentResp) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on PublishMomentResp with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// PublishMomentRespMultiError, or nil if none found.
func (m *PublishMomentResp) ValidateAll() error {
	return m.validate(true)
}

func (m *PublishMomentResp) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Code

	// no validation rules for Msg

	if all {
		switch v := interface{}(m.GetData()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, PublishMomentRespValidationError{
					field:  "Data",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, PublishMomentRespValidationError{
					field:  "Data",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetData()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return PublishMomentRespValidationError{
				field:  "Data",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return PublishMomentRespMultiError(errors)
	}

	return nil
}

// PublishMomentRespMultiError is an error wrapping multiple validation errors
// returned by PublishMomentResp.ValidateAll() if the designated constraints
// aren't met.
type PublishMomentRespMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m PublishMomentRespMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m PublishMomentRespMultiError) AllErrors() []error { return m }

// PublishMomentRespValidationError is the validation error returned by
// PublishMomentResp.Validate if the designated constraints aren't met.
type PublishMomentRespValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e PublishMomentRespValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e PublishMomentRespValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e PublishMomentRespValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e PublishMomentRespValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e PublishMomentRespValidationError) ErrorName() string {
	return "PublishMomentRespValidationError"
}

// Error satisfies the builtin error interface
func (e PublishMomentRespValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sPublishMomentResp.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = PublishMomentRespValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = PublishMomentRespValidationError{}

// Validate checks the field values on GetMyMomentsReq with the rules defined
// in the proto definition for this message. If any rules are violated, the
// first error encountered is returned, or nil if there are no violations.
func (m *GetMyMomentsReq) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GetMyMomentsReq with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// GetMyMomentsReqMultiError, or nil if none found.
func (m *GetMyMomentsReq) ValidateAll() error {
	return m.validate(true)
}

func (m *GetMyMomentsReq) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Page

	// no validation rules for PageSize

	if len(errors) > 0 {
		return GetMyMomentsReqMultiError(errors)
	}

	return nil
}

// GetMyMomentsReqMultiError is an error wrapping multiple validation errors
// returned by GetMyMomentsReq.ValidateAll() if the designated constraints
// aren't met.
type GetMyMomentsReqMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetMyMomentsReqMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetMyMomentsReqMultiError) AllErrors() []error { return m }

// GetMyMomentsReqValidationError is the validation error returned by
// GetMyMomentsReq.Validate if the designated constraints aren't met.
type GetMyMomentsReqValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetMyMomentsReqValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetMyMomentsReqValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetMyMomentsReqValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetMyMomentsReqValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetMyMomentsReqValidationError) ErrorName() string { return "GetMyMomentsReqValidationError" }

// Error satisfies the builtin error interface
func (e GetMyMomentsReqValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetMyMomentsReq.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetMyMomentsReqValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetMyMomentsReqValidationError{}

// Validate checks the field values on GetMyMomentsResp with the rules defined
// in the proto definition for this message. If any rules are violated, the
// first error encountered is returned, or nil if there are no violations.
func (m *GetMyMomentsResp) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GetMyMomentsResp with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// GetMyMomentsRespMultiError, or nil if none found.
func (m *GetMyMomentsResp) ValidateAll() error {
	return m.validate(true)
}

func (m *GetMyMomentsResp) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Code

	// no validation rules for Msg

	if all {
		switch v := interface{}(m.GetData()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetMyMomentsRespValidationError{
					field:  "Data",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetMyMomentsRespValidationError{
					field:  "Data",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetData()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetMyMomentsRespValidationError{
				field:  "Data",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return GetMyMomentsRespMultiError(errors)
	}

	return nil
}

// GetMyMomentsRespMultiError is an error wrapping multiple validation errors
// returned by GetMyMomentsResp.ValidateAll() if the designated constraints
// aren't met.
type GetMyMomentsRespMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetMyMomentsRespMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetMyMomentsRespMultiError) AllErrors() []error { return m }

// GetMyMomentsRespValidationError is the validation error returned by
// GetMyMomentsResp.Validate if the designated constraints aren't met.
type GetMyMomentsRespValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetMyMomentsRespValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetMyMomentsRespValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetMyMomentsRespValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetMyMomentsRespValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetMyMomentsRespValidationError) ErrorName() string { return "GetMyMomentsRespValidationError" }

// Error satisfies the builtin error interface
func (e GetMyMomentsRespValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetMyMomentsResp.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetMyMomentsRespValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetMyMomentsRespValidationError{}

// Validate checks the field values on GetMomentsByIdsReq with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *GetMomentsByIdsReq) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GetMomentsByIdsReq with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// GetMomentsByIdsReqMultiError, or nil if none found.
func (m *GetMomentsByIdsReq) ValidateAll() error {
	return m.validate(true)
}

func (m *GetMomentsByIdsReq) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if len(m.GetMomentIds()) < 1 {
		err := GetMomentsByIdsReqValidationError{
			field:  "MomentIds",
			reason: "value must contain at least 1 item(s)",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if len(errors) > 0 {
		return GetMomentsByIdsReqMultiError(errors)
	}

	return nil
}

// GetMomentsByIdsReqMultiError is an error wrapping multiple validation errors
// returned by GetMomentsByIdsReq.ValidateAll() if the designated constraints
// aren't met.
type GetMomentsByIdsReqMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetMomentsByIdsReqMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetMomentsByIdsReqMultiError) AllErrors() []error { return m }

// GetMomentsByIdsReqValidationError is the validation error returned by
// GetMomentsByIdsReq.Validate if the designated constraints aren't met.
type GetMomentsByIdsReqValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetMomentsByIdsReqValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetMomentsByIdsReqValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetMomentsByIdsReqValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetMomentsByIdsReqValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetMomentsByIdsReqValidationError) ErrorName() string {
	return "GetMomentsByIdsReqValidationError"
}

// Error satisfies the builtin error interface
func (e GetMomentsByIdsReqValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetMomentsByIdsReq.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetMomentsByIdsReqValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetMomentsByIdsReqValidationError{}

// Validate checks the field values on GetMomentsByIdsResp with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *GetMomentsByIdsResp) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GetMomentsByIdsResp with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// GetMomentsByIdsRespMultiError, or nil if none found.
func (m *GetMomentsByIdsResp) ValidateAll() error {
	return m.validate(true)
}

func (m *GetMomentsByIdsResp) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Code

	// no validation rules for Msg

	if all {
		switch v := interface{}(m.GetData()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetMomentsByIdsRespValidationError{
					field:  "Data",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetMomentsByIdsRespValidationError{
					field:  "Data",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetData()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetMomentsByIdsRespValidationError{
				field:  "Data",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return GetMomentsByIdsRespMultiError(errors)
	}

	return nil
}

// GetMomentsByIdsRespMultiError is an error wrapping multiple validation
// errors returned by GetMomentsByIdsResp.ValidateAll() if the designated
// constraints aren't met.
type GetMomentsByIdsRespMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetMomentsByIdsRespMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetMomentsByIdsRespMultiError) AllErrors() []error { return m }

// GetMomentsByIdsRespValidationError is the validation error returned by
// GetMomentsByIdsResp.Validate if the designated constraints aren't met.
type GetMomentsByIdsRespValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetMomentsByIdsRespValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetMomentsByIdsRespValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetMomentsByIdsRespValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetMomentsByIdsRespValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetMomentsByIdsRespValidationError) ErrorName() string {
	return "GetMomentsByIdsRespValidationError"
}

// Error satisfies the builtin error interface
func (e GetMomentsByIdsRespValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetMomentsByIdsResp.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetMomentsByIdsRespValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetMomentsByIdsRespValidationError{}

// Validate checks the field values on LikeMomentReq with the rules defined in
// the proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *LikeMomentReq) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on LikeMomentReq with the rules defined
// in the proto definition for this message. If any rules are violated, the
// result is a list of violation errors wrapped in LikeMomentReqMultiError, or
// nil if none found.
func (m *LikeMomentReq) ValidateAll() error {
	return m.validate(true)
}

func (m *LikeMomentReq) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if m.GetMomentId() <= 0 {
		err := LikeMomentReqValidationError{
			field:  "MomentId",
			reason: "value must be greater than 0",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if m.GetCharacterId() <= 0 {
		err := LikeMomentReqValidationError{
			field:  "CharacterId",
			reason: "value must be greater than 0",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if len(errors) > 0 {
		return LikeMomentReqMultiError(errors)
	}

	return nil
}

// LikeMomentReqMultiError is an error wrapping multiple validation errors
// returned by LikeMomentReq.ValidateAll() if the designated constraints
// aren't met.
type LikeMomentReqMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m LikeMomentReqMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m LikeMomentReqMultiError) AllErrors() []error { return m }

// LikeMomentReqValidationError is the validation error returned by
// LikeMomentReq.Validate if the designated constraints aren't met.
type LikeMomentReqValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e LikeMomentReqValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e LikeMomentReqValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e LikeMomentReqValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e LikeMomentReqValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e LikeMomentReqValidationError) ErrorName() string { return "LikeMomentReqValidationError" }

// Error satisfies the builtin error interface
func (e LikeMomentReqValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sLikeMomentReq.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = LikeMomentReqValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = LikeMomentReqValidationError{}

// Validate checks the field values on LikeMomentResp with the rules defined in
// the proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *LikeMomentResp) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on LikeMomentResp with the rules defined
// in the proto definition for this message. If any rules are violated, the
// result is a list of violation errors wrapped in LikeMomentRespMultiError,
// or nil if none found.
func (m *LikeMomentResp) ValidateAll() error {
	return m.validate(true)
}

func (m *LikeMomentResp) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Code

	// no validation rules for Msg

	if len(errors) > 0 {
		return LikeMomentRespMultiError(errors)
	}

	return nil
}

// LikeMomentRespMultiError is an error wrapping multiple validation errors
// returned by LikeMomentResp.ValidateAll() if the designated constraints
// aren't met.
type LikeMomentRespMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m LikeMomentRespMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m LikeMomentRespMultiError) AllErrors() []error { return m }

// LikeMomentRespValidationError is the validation error returned by
// LikeMomentResp.Validate if the designated constraints aren't met.
type LikeMomentRespValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e LikeMomentRespValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e LikeMomentRespValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e LikeMomentRespValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e LikeMomentRespValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e LikeMomentRespValidationError) ErrorName() string { return "LikeMomentRespValidationError" }

// Error satisfies the builtin error interface
func (e LikeMomentRespValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sLikeMomentResp.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = LikeMomentRespValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = LikeMomentRespValidationError{}

// Validate checks the field values on UnlikeMomentReq with the rules defined
// in the proto definition for this message. If any rules are violated, the
// first error encountered is returned, or nil if there are no violations.
func (m *UnlikeMomentReq) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on UnlikeMomentReq with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// UnlikeMomentReqMultiError, or nil if none found.
func (m *UnlikeMomentReq) ValidateAll() error {
	return m.validate(true)
}

func (m *UnlikeMomentReq) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if m.GetMomentId() <= 0 {
		err := UnlikeMomentReqValidationError{
			field:  "MomentId",
			reason: "value must be greater than 0",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if len(errors) > 0 {
		return UnlikeMomentReqMultiError(errors)
	}

	return nil
}

// UnlikeMomentReqMultiError is an error wrapping multiple validation errors
// returned by UnlikeMomentReq.ValidateAll() if the designated constraints
// aren't met.
type UnlikeMomentReqMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m UnlikeMomentReqMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m UnlikeMomentReqMultiError) AllErrors() []error { return m }

// UnlikeMomentReqValidationError is the validation error returned by
// UnlikeMomentReq.Validate if the designated constraints aren't met.
type UnlikeMomentReqValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e UnlikeMomentReqValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e UnlikeMomentReqValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e UnlikeMomentReqValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e UnlikeMomentReqValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e UnlikeMomentReqValidationError) ErrorName() string { return "UnlikeMomentReqValidationError" }

// Error satisfies the builtin error interface
func (e UnlikeMomentReqValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sUnlikeMomentReq.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = UnlikeMomentReqValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = UnlikeMomentReqValidationError{}

// Validate checks the field values on UnlikeMomentResp with the rules defined
// in the proto definition for this message. If any rules are violated, the
// first error encountered is returned, or nil if there are no violations.
func (m *UnlikeMomentResp) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on UnlikeMomentResp with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// UnlikeMomentRespMultiError, or nil if none found.
func (m *UnlikeMomentResp) ValidateAll() error {
	return m.validate(true)
}

func (m *UnlikeMomentResp) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Code

	// no validation rules for Msg

	if len(errors) > 0 {
		return UnlikeMomentRespMultiError(errors)
	}

	return nil
}

// UnlikeMomentRespMultiError is an error wrapping multiple validation errors
// returned by UnlikeMomentResp.ValidateAll() if the designated constraints
// aren't met.
type UnlikeMomentRespMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m UnlikeMomentRespMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m UnlikeMomentRespMultiError) AllErrors() []error { return m }

// UnlikeMomentRespValidationError is the validation error returned by
// UnlikeMomentResp.Validate if the designated constraints aren't met.
type UnlikeMomentRespValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e UnlikeMomentRespValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e UnlikeMomentRespValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e UnlikeMomentRespValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e UnlikeMomentRespValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e UnlikeMomentRespValidationError) ErrorName() string { return "UnlikeMomentRespValidationError" }

// Error satisfies the builtin error interface
func (e UnlikeMomentRespValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sUnlikeMomentResp.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = UnlikeMomentRespValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = UnlikeMomentRespValidationError{}

// Validate checks the field values on GetMyLikedMomentsReq with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *GetMyLikedMomentsReq) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GetMyLikedMomentsReq with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// GetMyLikedMomentsReqMultiError, or nil if none found.
func (m *GetMyLikedMomentsReq) ValidateAll() error {
	return m.validate(true)
}

func (m *GetMyLikedMomentsReq) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Page

	// no validation rules for PageSize

	if len(errors) > 0 {
		return GetMyLikedMomentsReqMultiError(errors)
	}

	return nil
}

// GetMyLikedMomentsReqMultiError is an error wrapping multiple validation
// errors returned by GetMyLikedMomentsReq.ValidateAll() if the designated
// constraints aren't met.
type GetMyLikedMomentsReqMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetMyLikedMomentsReqMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetMyLikedMomentsReqMultiError) AllErrors() []error { return m }

// GetMyLikedMomentsReqValidationError is the validation error returned by
// GetMyLikedMomentsReq.Validate if the designated constraints aren't met.
type GetMyLikedMomentsReqValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetMyLikedMomentsReqValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetMyLikedMomentsReqValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetMyLikedMomentsReqValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetMyLikedMomentsReqValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetMyLikedMomentsReqValidationError) ErrorName() string {
	return "GetMyLikedMomentsReqValidationError"
}

// Error satisfies the builtin error interface
func (e GetMyLikedMomentsReqValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetMyLikedMomentsReq.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetMyLikedMomentsReqValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetMyLikedMomentsReqValidationError{}

// Validate checks the field values on GetMyLikedMomentsResp with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *GetMyLikedMomentsResp) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GetMyLikedMomentsResp with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// GetMyLikedMomentsRespMultiError, or nil if none found.
func (m *GetMyLikedMomentsResp) ValidateAll() error {
	return m.validate(true)
}

func (m *GetMyLikedMomentsResp) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Code

	// no validation rules for Msg

	if all {
		switch v := interface{}(m.GetData()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetMyLikedMomentsRespValidationError{
					field:  "Data",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetMyLikedMomentsRespValidationError{
					field:  "Data",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetData()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetMyLikedMomentsRespValidationError{
				field:  "Data",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return GetMyLikedMomentsRespMultiError(errors)
	}

	return nil
}

// GetMyLikedMomentsRespMultiError is an error wrapping multiple validation
// errors returned by GetMyLikedMomentsResp.ValidateAll() if the designated
// constraints aren't met.
type GetMyLikedMomentsRespMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetMyLikedMomentsRespMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetMyLikedMomentsRespMultiError) AllErrors() []error { return m }

// GetMyLikedMomentsRespValidationError is the validation error returned by
// GetMyLikedMomentsResp.Validate if the designated constraints aren't met.
type GetMyLikedMomentsRespValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetMyLikedMomentsRespValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetMyLikedMomentsRespValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetMyLikedMomentsRespValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetMyLikedMomentsRespValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetMyLikedMomentsRespValidationError) ErrorName() string {
	return "GetMyLikedMomentsRespValidationError"
}

// Error satisfies the builtin error interface
func (e GetMyLikedMomentsRespValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetMyLikedMomentsResp.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetMyLikedMomentsRespValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetMyLikedMomentsRespValidationError{}

// Validate checks the field values on UpdateUserCharacterReq with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *UpdateUserCharacterReq) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on UpdateUserCharacterReq with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// UpdateUserCharacterReqMultiError, or nil if none found.
func (m *UpdateUserCharacterReq) ValidateAll() error {
	return m.validate(true)
}

func (m *UpdateUserCharacterReq) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if m.GetCharacterId() <= 0 {
		err := UpdateUserCharacterReqValidationError{
			field:  "CharacterId",
			reason: "value must be greater than 0",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if len(errors) > 0 {
		return UpdateUserCharacterReqMultiError(errors)
	}

	return nil
}

// UpdateUserCharacterReqMultiError is an error wrapping multiple validation
// errors returned by UpdateUserCharacterReq.ValidateAll() if the designated
// constraints aren't met.
type UpdateUserCharacterReqMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m UpdateUserCharacterReqMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m UpdateUserCharacterReqMultiError) AllErrors() []error { return m }

// UpdateUserCharacterReqValidationError is the validation error returned by
// UpdateUserCharacterReq.Validate if the designated constraints aren't met.
type UpdateUserCharacterReqValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e UpdateUserCharacterReqValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e UpdateUserCharacterReqValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e UpdateUserCharacterReqValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e UpdateUserCharacterReqValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e UpdateUserCharacterReqValidationError) ErrorName() string {
	return "UpdateUserCharacterReqValidationError"
}

// Error satisfies the builtin error interface
func (e UpdateUserCharacterReqValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sUpdateUserCharacterReq.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = UpdateUserCharacterReqValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = UpdateUserCharacterReqValidationError{}

// Validate checks the field values on UpdateUserCharacterResp with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *UpdateUserCharacterResp) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on UpdateUserCharacterResp with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// UpdateUserCharacterRespMultiError, or nil if none found.
func (m *UpdateUserCharacterResp) ValidateAll() error {
	return m.validate(true)
}

func (m *UpdateUserCharacterResp) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Code

	// no validation rules for Msg

	if len(errors) > 0 {
		return UpdateUserCharacterRespMultiError(errors)
	}

	return nil
}

// UpdateUserCharacterRespMultiError is an error wrapping multiple validation
// errors returned by UpdateUserCharacterResp.ValidateAll() if the designated
// constraints aren't met.
type UpdateUserCharacterRespMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m UpdateUserCharacterRespMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m UpdateUserCharacterRespMultiError) AllErrors() []error { return m }

// UpdateUserCharacterRespValidationError is the validation error returned by
// UpdateUserCharacterResp.Validate if the designated constraints aren't met.
type UpdateUserCharacterRespValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e UpdateUserCharacterRespValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e UpdateUserCharacterRespValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e UpdateUserCharacterRespValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e UpdateUserCharacterRespValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e UpdateUserCharacterRespValidationError) ErrorName() string {
	return "UpdateUserCharacterRespValidationError"
}

// Error satisfies the builtin error interface
func (e UpdateUserCharacterRespValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sUpdateUserCharacterResp.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = UpdateUserCharacterRespValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = UpdateUserCharacterRespValidationError{}

// Validate checks the field values on GetUserCharacterReq with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *GetUserCharacterReq) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GetUserCharacterReq with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// GetUserCharacterReqMultiError, or nil if none found.
func (m *GetUserCharacterReq) ValidateAll() error {
	return m.validate(true)
}

func (m *GetUserCharacterReq) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if len(errors) > 0 {
		return GetUserCharacterReqMultiError(errors)
	}

	return nil
}

// GetUserCharacterReqMultiError is an error wrapping multiple validation
// errors returned by GetUserCharacterReq.ValidateAll() if the designated
// constraints aren't met.
type GetUserCharacterReqMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetUserCharacterReqMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetUserCharacterReqMultiError) AllErrors() []error { return m }

// GetUserCharacterReqValidationError is the validation error returned by
// GetUserCharacterReq.Validate if the designated constraints aren't met.
type GetUserCharacterReqValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetUserCharacterReqValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetUserCharacterReqValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetUserCharacterReqValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetUserCharacterReqValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetUserCharacterReqValidationError) ErrorName() string {
	return "GetUserCharacterReqValidationError"
}

// Error satisfies the builtin error interface
func (e GetUserCharacterReqValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetUserCharacterReq.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetUserCharacterReqValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetUserCharacterReqValidationError{}

// Validate checks the field values on GetUserCharacterResp with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *GetUserCharacterResp) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GetUserCharacterResp with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// GetUserCharacterRespMultiError, or nil if none found.
func (m *GetUserCharacterResp) ValidateAll() error {
	return m.validate(true)
}

func (m *GetUserCharacterResp) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Code

	// no validation rules for Msg

	if all {
		switch v := interface{}(m.GetData()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetUserCharacterRespValidationError{
					field:  "Data",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetUserCharacterRespValidationError{
					field:  "Data",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetData()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetUserCharacterRespValidationError{
				field:  "Data",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return GetUserCharacterRespMultiError(errors)
	}

	return nil
}

// GetUserCharacterRespMultiError is an error wrapping multiple validation
// errors returned by GetUserCharacterResp.ValidateAll() if the designated
// constraints aren't met.
type GetUserCharacterRespMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetUserCharacterRespMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetUserCharacterRespMultiError) AllErrors() []error { return m }

// GetUserCharacterRespValidationError is the validation error returned by
// GetUserCharacterResp.Validate if the designated constraints aren't met.
type GetUserCharacterRespValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetUserCharacterRespValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetUserCharacterRespValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetUserCharacterRespValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetUserCharacterRespValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetUserCharacterRespValidationError) ErrorName() string {
	return "GetUserCharacterRespValidationError"
}

// Error satisfies the builtin error interface
func (e GetUserCharacterRespValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetUserCharacterResp.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetUserCharacterRespValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetUserCharacterRespValidationError{}

// Validate checks the field values on GetMomentSquareReq with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *GetMomentSquareReq) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GetMomentSquareReq with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// GetMomentSquareReqMultiError, or nil if none found.
func (m *GetMomentSquareReq) ValidateAll() error {
	return m.validate(true)
}

func (m *GetMomentSquareReq) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Page

	// no validation rules for PageSize

	if len(errors) > 0 {
		return GetMomentSquareReqMultiError(errors)
	}

	return nil
}

// GetMomentSquareReqMultiError is an error wrapping multiple validation errors
// returned by GetMomentSquareReq.ValidateAll() if the designated constraints
// aren't met.
type GetMomentSquareReqMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetMomentSquareReqMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetMomentSquareReqMultiError) AllErrors() []error { return m }

// GetMomentSquareReqValidationError is the validation error returned by
// GetMomentSquareReq.Validate if the designated constraints aren't met.
type GetMomentSquareReqValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetMomentSquareReqValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetMomentSquareReqValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetMomentSquareReqValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetMomentSquareReqValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetMomentSquareReqValidationError) ErrorName() string {
	return "GetMomentSquareReqValidationError"
}

// Error satisfies the builtin error interface
func (e GetMomentSquareReqValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetMomentSquareReq.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetMomentSquareReqValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetMomentSquareReqValidationError{}

// Validate checks the field values on GetMomentSquareResp with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *GetMomentSquareResp) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GetMomentSquareResp with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// GetMomentSquareRespMultiError, or nil if none found.
func (m *GetMomentSquareResp) ValidateAll() error {
	return m.validate(true)
}

func (m *GetMomentSquareResp) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Code

	// no validation rules for Msg

	if all {
		switch v := interface{}(m.GetData()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetMomentSquareRespValidationError{
					field:  "Data",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetMomentSquareRespValidationError{
					field:  "Data",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetData()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetMomentSquareRespValidationError{
				field:  "Data",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return GetMomentSquareRespMultiError(errors)
	}

	return nil
}

// GetMomentSquareRespMultiError is an error wrapping multiple validation
// errors returned by GetMomentSquareResp.ValidateAll() if the designated
// constraints aren't met.
type GetMomentSquareRespMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetMomentSquareRespMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetMomentSquareRespMultiError) AllErrors() []error { return m }

// GetMomentSquareRespValidationError is the validation error returned by
// GetMomentSquareResp.Validate if the designated constraints aren't met.
type GetMomentSquareRespValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetMomentSquareRespValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetMomentSquareRespValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetMomentSquareRespValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetMomentSquareRespValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetMomentSquareRespValidationError) ErrorName() string {
	return "GetMomentSquareRespValidationError"
}

// Error satisfies the builtin error interface
func (e GetMomentSquareRespValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetMomentSquareResp.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetMomentSquareRespValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetMomentSquareRespValidationError{}

// Validate checks the field values on ReportResp with the rules defined in the
// proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *ReportResp) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on ReportResp with the rules defined in
// the proto definition for this message. If any rules are violated, the
// result is a list of violation errors wrapped in ReportRespMultiError, or
// nil if none found.
func (m *ReportResp) ValidateAll() error {
	return m.validate(true)
}

func (m *ReportResp) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Code

	// no validation rules for Msg

	if len(errors) > 0 {
		return ReportRespMultiError(errors)
	}

	return nil
}

// ReportRespMultiError is an error wrapping multiple validation errors
// returned by ReportResp.ValidateAll() if the designated constraints aren't met.
type ReportRespMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m ReportRespMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m ReportRespMultiError) AllErrors() []error { return m }

// ReportRespValidationError is the validation error returned by
// ReportResp.Validate if the designated constraints aren't met.
type ReportRespValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e ReportRespValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e ReportRespValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e ReportRespValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e ReportRespValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e ReportRespValidationError) ErrorName() string { return "ReportRespValidationError" }

// Error satisfies the builtin error interface
func (e ReportRespValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sReportResp.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = ReportRespValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = ReportRespValidationError{}
