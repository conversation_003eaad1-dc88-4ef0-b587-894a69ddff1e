// Code generated by protoc-gen-validate. DO NOT EDIT.
// source: svcaccount.proto

package svcaccount

import (
	"bytes"
	"errors"
	"fmt"
	"net"
	"net/mail"
	"net/url"
	"regexp"
	"sort"
	"strings"
	"time"
	"unicode/utf8"

	"google.golang.org/protobuf/types/known/anypb"

	svcreview "new-gitlab.xunlei.cn/vcproject/backends/proto/api/svcreview"
)

// ensure the imports are used
var (
	_ = bytes.MinRead
	_ = errors.New("")
	_ = fmt.Print
	_ = utf8.UTFMax
	_ = (*regexp.Regexp)(nil)
	_ = (*strings.Reader)(nil)
	_ = net.IPv4len
	_ = time.Duration(0)
	_ = (*url.URL)(nil)
	_ = (*mail.Address)(nil)
	_ = anypb.Any{}
	_ = sort.Sort

	_ = svcreview.ReviewBizType(0)
)

// Validate checks the field values on GetVerificationCodeReq with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *GetVerificationCodeReq) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GetVerificationCodeReq with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// GetVerificationCodeReqMultiError, or nil if none found.
func (m *GetVerificationCodeReq) ValidateAll() error {
	return m.validate(true)
}

func (m *GetVerificationCodeReq) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if l := len(m.GetPhoneNumber()); l < 1 || l > 11 {
		err := GetVerificationCodeReqValidationError{
			field:  "PhoneNumber",
			reason: "value length must be between 1 and 11 bytes, inclusive",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if len(errors) > 0 {
		return GetVerificationCodeReqMultiError(errors)
	}

	return nil
}

// GetVerificationCodeReqMultiError is an error wrapping multiple validation
// errors returned by GetVerificationCodeReq.ValidateAll() if the designated
// constraints aren't met.
type GetVerificationCodeReqMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetVerificationCodeReqMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetVerificationCodeReqMultiError) AllErrors() []error { return m }

// GetVerificationCodeReqValidationError is the validation error returned by
// GetVerificationCodeReq.Validate if the designated constraints aren't met.
type GetVerificationCodeReqValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetVerificationCodeReqValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetVerificationCodeReqValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetVerificationCodeReqValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetVerificationCodeReqValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetVerificationCodeReqValidationError) ErrorName() string {
	return "GetVerificationCodeReqValidationError"
}

// Error satisfies the builtin error interface
func (e GetVerificationCodeReqValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetVerificationCodeReq.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetVerificationCodeReqValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetVerificationCodeReqValidationError{}

// Validate checks the field values on GetVerificationCodeResp with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *GetVerificationCodeResp) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GetVerificationCodeResp with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// GetVerificationCodeRespMultiError, or nil if none found.
func (m *GetVerificationCodeResp) ValidateAll() error {
	return m.validate(true)
}

func (m *GetVerificationCodeResp) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetBase()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetVerificationCodeRespValidationError{
					field:  "Base",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetVerificationCodeRespValidationError{
					field:  "Base",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetBase()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetVerificationCodeRespValidationError{
				field:  "Base",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetData()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetVerificationCodeRespValidationError{
					field:  "Data",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetVerificationCodeRespValidationError{
					field:  "Data",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetData()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetVerificationCodeRespValidationError{
				field:  "Data",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return GetVerificationCodeRespMultiError(errors)
	}

	return nil
}

// GetVerificationCodeRespMultiError is an error wrapping multiple validation
// errors returned by GetVerificationCodeResp.ValidateAll() if the designated
// constraints aren't met.
type GetVerificationCodeRespMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetVerificationCodeRespMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetVerificationCodeRespMultiError) AllErrors() []error { return m }

// GetVerificationCodeRespValidationError is the validation error returned by
// GetVerificationCodeResp.Validate if the designated constraints aren't met.
type GetVerificationCodeRespValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetVerificationCodeRespValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetVerificationCodeRespValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetVerificationCodeRespValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetVerificationCodeRespValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetVerificationCodeRespValidationError) ErrorName() string {
	return "GetVerificationCodeRespValidationError"
}

// Error satisfies the builtin error interface
func (e GetVerificationCodeRespValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetVerificationCodeResp.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetVerificationCodeRespValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetVerificationCodeRespValidationError{}

// Validate checks the field values on GetVerificationCodeRespData with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *GetVerificationCodeRespData) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GetVerificationCodeRespData with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// GetVerificationCodeRespDataMultiError, or nil if none found.
func (m *GetVerificationCodeRespData) ValidateAll() error {
	return m.validate(true)
}

func (m *GetVerificationCodeRespData) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for IsRegistered

	if len(m.GetVerificationToken()) < 1 {
		err := GetVerificationCodeRespDataValidationError{
			field:  "VerificationToken",
			reason: "value length must be at least 1 bytes",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if len(errors) > 0 {
		return GetVerificationCodeRespDataMultiError(errors)
	}

	return nil
}

// GetVerificationCodeRespDataMultiError is an error wrapping multiple
// validation errors returned by GetVerificationCodeRespData.ValidateAll() if
// the designated constraints aren't met.
type GetVerificationCodeRespDataMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetVerificationCodeRespDataMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetVerificationCodeRespDataMultiError) AllErrors() []error { return m }

// GetVerificationCodeRespDataValidationError is the validation error returned
// by GetVerificationCodeRespData.Validate if the designated constraints
// aren't met.
type GetVerificationCodeRespDataValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetVerificationCodeRespDataValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetVerificationCodeRespDataValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetVerificationCodeRespDataValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetVerificationCodeRespDataValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetVerificationCodeRespDataValidationError) ErrorName() string {
	return "GetVerificationCodeRespDataValidationError"
}

// Error satisfies the builtin error interface
func (e GetVerificationCodeRespDataValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetVerificationCodeRespData.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetVerificationCodeRespDataValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetVerificationCodeRespDataValidationError{}

// Validate checks the field values on AccountSignUpReq with the rules defined
// in the proto definition for this message. If any rules are violated, the
// first error encountered is returned, or nil if there are no violations.
func (m *AccountSignUpReq) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on AccountSignUpReq with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// AccountSignUpReqMultiError, or nil if none found.
func (m *AccountSignUpReq) ValidateAll() error {
	return m.validate(true)
}

func (m *AccountSignUpReq) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if l := len(m.GetSmsCode()); l < 1 || l > 11 {
		err := AccountSignUpReqValidationError{
			field:  "SmsCode",
			reason: "value length must be between 1 and 11 bytes, inclusive",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if len(m.GetVerificationToken()) < 1 {
		err := AccountSignUpReqValidationError{
			field:  "VerificationToken",
			reason: "value length must be at least 1 bytes",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if l := len(m.GetPhoneNumber()); l < 1 || l > 11 {
		err := AccountSignUpReqValidationError{
			field:  "PhoneNumber",
			reason: "value length must be between 1 and 11 bytes, inclusive",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if len(errors) > 0 {
		return AccountSignUpReqMultiError(errors)
	}

	return nil
}

// AccountSignUpReqMultiError is an error wrapping multiple validation errors
// returned by AccountSignUpReq.ValidateAll() if the designated constraints
// aren't met.
type AccountSignUpReqMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m AccountSignUpReqMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m AccountSignUpReqMultiError) AllErrors() []error { return m }

// AccountSignUpReqValidationError is the validation error returned by
// AccountSignUpReq.Validate if the designated constraints aren't met.
type AccountSignUpReqValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e AccountSignUpReqValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e AccountSignUpReqValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e AccountSignUpReqValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e AccountSignUpReqValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e AccountSignUpReqValidationError) ErrorName() string { return "AccountSignUpReqValidationError" }

// Error satisfies the builtin error interface
func (e AccountSignUpReqValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sAccountSignUpReq.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = AccountSignUpReqValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = AccountSignUpReqValidationError{}

// Validate checks the field values on TokenRespData with the rules defined in
// the proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *TokenRespData) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on TokenRespData with the rules defined
// in the proto definition for this message. If any rules are violated, the
// result is a list of violation errors wrapped in TokenRespDataMultiError, or
// nil if none found.
func (m *TokenRespData) ValidateAll() error {
	return m.validate(true)
}

func (m *TokenRespData) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for TokenType

	// no validation rules for AccessToken

	// no validation rules for RefreshToken

	// no validation rules for ExpiresIn

	// no validation rules for UserId

	// no validation rules for NickName

	// no validation rules for Avatar

	// no validation rules for Gender

	if len(errors) > 0 {
		return TokenRespDataMultiError(errors)
	}

	return nil
}

// TokenRespDataMultiError is an error wrapping multiple validation errors
// returned by TokenRespData.ValidateAll() if the designated constraints
// aren't met.
type TokenRespDataMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m TokenRespDataMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m TokenRespDataMultiError) AllErrors() []error { return m }

// TokenRespDataValidationError is the validation error returned by
// TokenRespData.Validate if the designated constraints aren't met.
type TokenRespDataValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e TokenRespDataValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e TokenRespDataValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e TokenRespDataValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e TokenRespDataValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e TokenRespDataValidationError) ErrorName() string { return "TokenRespDataValidationError" }

// Error satisfies the builtin error interface
func (e TokenRespDataValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sTokenRespData.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = TokenRespDataValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = TokenRespDataValidationError{}

// Validate checks the field values on AccountSignUpResp with the rules defined
// in the proto definition for this message. If any rules are violated, the
// first error encountered is returned, or nil if there are no violations.
func (m *AccountSignUpResp) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on AccountSignUpResp with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// AccountSignUpRespMultiError, or nil if none found.
func (m *AccountSignUpResp) ValidateAll() error {
	return m.validate(true)
}

func (m *AccountSignUpResp) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetBase()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, AccountSignUpRespValidationError{
					field:  "Base",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, AccountSignUpRespValidationError{
					field:  "Base",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetBase()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return AccountSignUpRespValidationError{
				field:  "Base",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetData()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, AccountSignUpRespValidationError{
					field:  "Data",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, AccountSignUpRespValidationError{
					field:  "Data",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetData()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return AccountSignUpRespValidationError{
				field:  "Data",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return AccountSignUpRespMultiError(errors)
	}

	return nil
}

// AccountSignUpRespMultiError is an error wrapping multiple validation errors
// returned by AccountSignUpResp.ValidateAll() if the designated constraints
// aren't met.
type AccountSignUpRespMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m AccountSignUpRespMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m AccountSignUpRespMultiError) AllErrors() []error { return m }

// AccountSignUpRespValidationError is the validation error returned by
// AccountSignUpResp.Validate if the designated constraints aren't met.
type AccountSignUpRespValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e AccountSignUpRespValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e AccountSignUpRespValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e AccountSignUpRespValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e AccountSignUpRespValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e AccountSignUpRespValidationError) ErrorName() string {
	return "AccountSignUpRespValidationError"
}

// Error satisfies the builtin error interface
func (e AccountSignUpRespValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sAccountSignUpResp.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = AccountSignUpRespValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = AccountSignUpRespValidationError{}

// Validate checks the field values on AccountSignInReq with the rules defined
// in the proto definition for this message. If any rules are violated, the
// first error encountered is returned, or nil if there are no violations.
func (m *AccountSignInReq) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on AccountSignInReq with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// AccountSignInReqMultiError, or nil if none found.
func (m *AccountSignInReq) ValidateAll() error {
	return m.validate(true)
}

func (m *AccountSignInReq) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if len(m.GetSmsCode()) < 1 {
		err := AccountSignInReqValidationError{
			field:  "SmsCode",
			reason: "value length must be at least 1 bytes",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if len(m.GetVerificationToken()) < 1 {
		err := AccountSignInReqValidationError{
			field:  "VerificationToken",
			reason: "value length must be at least 1 bytes",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if l := len(m.GetPhoneNumber()); l < 1 || l > 11 {
		err := AccountSignInReqValidationError{
			field:  "PhoneNumber",
			reason: "value length must be between 1 and 11 bytes, inclusive",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if len(errors) > 0 {
		return AccountSignInReqMultiError(errors)
	}

	return nil
}

// AccountSignInReqMultiError is an error wrapping multiple validation errors
// returned by AccountSignInReq.ValidateAll() if the designated constraints
// aren't met.
type AccountSignInReqMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m AccountSignInReqMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m AccountSignInReqMultiError) AllErrors() []error { return m }

// AccountSignInReqValidationError is the validation error returned by
// AccountSignInReq.Validate if the designated constraints aren't met.
type AccountSignInReqValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e AccountSignInReqValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e AccountSignInReqValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e AccountSignInReqValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e AccountSignInReqValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e AccountSignInReqValidationError) ErrorName() string { return "AccountSignInReqValidationError" }

// Error satisfies the builtin error interface
func (e AccountSignInReqValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sAccountSignInReq.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = AccountSignInReqValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = AccountSignInReqValidationError{}

// Validate checks the field values on AccountSignInResp with the rules defined
// in the proto definition for this message. If any rules are violated, the
// first error encountered is returned, or nil if there are no violations.
func (m *AccountSignInResp) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on AccountSignInResp with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// AccountSignInRespMultiError, or nil if none found.
func (m *AccountSignInResp) ValidateAll() error {
	return m.validate(true)
}

func (m *AccountSignInResp) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetBase()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, AccountSignInRespValidationError{
					field:  "Base",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, AccountSignInRespValidationError{
					field:  "Base",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetBase()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return AccountSignInRespValidationError{
				field:  "Base",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetData()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, AccountSignInRespValidationError{
					field:  "Data",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, AccountSignInRespValidationError{
					field:  "Data",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetData()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return AccountSignInRespValidationError{
				field:  "Data",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return AccountSignInRespMultiError(errors)
	}

	return nil
}

// AccountSignInRespMultiError is an error wrapping multiple validation errors
// returned by AccountSignInResp.ValidateAll() if the designated constraints
// aren't met.
type AccountSignInRespMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m AccountSignInRespMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m AccountSignInRespMultiError) AllErrors() []error { return m }

// AccountSignInRespValidationError is the validation error returned by
// AccountSignInResp.Validate if the designated constraints aren't met.
type AccountSignInRespValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e AccountSignInRespValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e AccountSignInRespValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e AccountSignInRespValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e AccountSignInRespValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e AccountSignInRespValidationError) ErrorName() string {
	return "AccountSignInRespValidationError"
}

// Error satisfies the builtin error interface
func (e AccountSignInRespValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sAccountSignInResp.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = AccountSignInRespValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = AccountSignInRespValidationError{}

// Validate checks the field values on AccountSignOutReq with the rules defined
// in the proto definition for this message. If any rules are violated, the
// first error encountered is returned, or nil if there are no violations.
func (m *AccountSignOutReq) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on AccountSignOutReq with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// AccountSignOutReqMultiError, or nil if none found.
func (m *AccountSignOutReq) ValidateAll() error {
	return m.validate(true)
}

func (m *AccountSignOutReq) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if len(errors) > 0 {
		return AccountSignOutReqMultiError(errors)
	}

	return nil
}

// AccountSignOutReqMultiError is an error wrapping multiple validation errors
// returned by AccountSignOutReq.ValidateAll() if the designated constraints
// aren't met.
type AccountSignOutReqMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m AccountSignOutReqMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m AccountSignOutReqMultiError) AllErrors() []error { return m }

// AccountSignOutReqValidationError is the validation error returned by
// AccountSignOutReq.Validate if the designated constraints aren't met.
type AccountSignOutReqValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e AccountSignOutReqValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e AccountSignOutReqValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e AccountSignOutReqValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e AccountSignOutReqValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e AccountSignOutReqValidationError) ErrorName() string {
	return "AccountSignOutReqValidationError"
}

// Error satisfies the builtin error interface
func (e AccountSignOutReqValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sAccountSignOutReq.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = AccountSignOutReqValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = AccountSignOutReqValidationError{}

// Validate checks the field values on AccountSignOutResp with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *AccountSignOutResp) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on AccountSignOutResp with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// AccountSignOutRespMultiError, or nil if none found.
func (m *AccountSignOutResp) ValidateAll() error {
	return m.validate(true)
}

func (m *AccountSignOutResp) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetBase()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, AccountSignOutRespValidationError{
					field:  "Base",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, AccountSignOutRespValidationError{
					field:  "Base",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetBase()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return AccountSignOutRespValidationError{
				field:  "Base",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return AccountSignOutRespMultiError(errors)
	}

	return nil
}

// AccountSignOutRespMultiError is an error wrapping multiple validation errors
// returned by AccountSignOutResp.ValidateAll() if the designated constraints
// aren't met.
type AccountSignOutRespMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m AccountSignOutRespMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m AccountSignOutRespMultiError) AllErrors() []error { return m }

// AccountSignOutRespValidationError is the validation error returned by
// AccountSignOutResp.Validate if the designated constraints aren't met.
type AccountSignOutRespValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e AccountSignOutRespValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e AccountSignOutRespValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e AccountSignOutRespValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e AccountSignOutRespValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e AccountSignOutRespValidationError) ErrorName() string {
	return "AccountSignOutRespValidationError"
}

// Error satisfies the builtin error interface
func (e AccountSignOutRespValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sAccountSignOutResp.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = AccountSignOutRespValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = AccountSignOutRespValidationError{}

// Validate checks the field values on RefreshTokenReq with the rules defined
// in the proto definition for this message. If any rules are violated, the
// first error encountered is returned, or nil if there are no violations.
func (m *RefreshTokenReq) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on RefreshTokenReq with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// RefreshTokenReqMultiError, or nil if none found.
func (m *RefreshTokenReq) ValidateAll() error {
	return m.validate(true)
}

func (m *RefreshTokenReq) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if len(m.GetRefreshToken()) < 1 {
		err := RefreshTokenReqValidationError{
			field:  "RefreshToken",
			reason: "value length must be at least 1 bytes",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if len(errors) > 0 {
		return RefreshTokenReqMultiError(errors)
	}

	return nil
}

// RefreshTokenReqMultiError is an error wrapping multiple validation errors
// returned by RefreshTokenReq.ValidateAll() if the designated constraints
// aren't met.
type RefreshTokenReqMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m RefreshTokenReqMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m RefreshTokenReqMultiError) AllErrors() []error { return m }

// RefreshTokenReqValidationError is the validation error returned by
// RefreshTokenReq.Validate if the designated constraints aren't met.
type RefreshTokenReqValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e RefreshTokenReqValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e RefreshTokenReqValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e RefreshTokenReqValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e RefreshTokenReqValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e RefreshTokenReqValidationError) ErrorName() string { return "RefreshTokenReqValidationError" }

// Error satisfies the builtin error interface
func (e RefreshTokenReqValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sRefreshTokenReq.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = RefreshTokenReqValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = RefreshTokenReqValidationError{}

// Validate checks the field values on RefreshTokenResp with the rules defined
// in the proto definition for this message. If any rules are violated, the
// first error encountered is returned, or nil if there are no violations.
func (m *RefreshTokenResp) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on RefreshTokenResp with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// RefreshTokenRespMultiError, or nil if none found.
func (m *RefreshTokenResp) ValidateAll() error {
	return m.validate(true)
}

func (m *RefreshTokenResp) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetBase()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, RefreshTokenRespValidationError{
					field:  "Base",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, RefreshTokenRespValidationError{
					field:  "Base",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetBase()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return RefreshTokenRespValidationError{
				field:  "Base",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetData()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, RefreshTokenRespValidationError{
					field:  "Data",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, RefreshTokenRespValidationError{
					field:  "Data",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetData()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return RefreshTokenRespValidationError{
				field:  "Data",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return RefreshTokenRespMultiError(errors)
	}

	return nil
}

// RefreshTokenRespMultiError is an error wrapping multiple validation errors
// returned by RefreshTokenResp.ValidateAll() if the designated constraints
// aren't met.
type RefreshTokenRespMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m RefreshTokenRespMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m RefreshTokenRespMultiError) AllErrors() []error { return m }

// RefreshTokenRespValidationError is the validation error returned by
// RefreshTokenResp.Validate if the designated constraints aren't met.
type RefreshTokenRespValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e RefreshTokenRespValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e RefreshTokenRespValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e RefreshTokenRespValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e RefreshTokenRespValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e RefreshTokenRespValidationError) ErrorName() string { return "RefreshTokenRespValidationError" }

// Error satisfies the builtin error interface
func (e RefreshTokenRespValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sRefreshTokenResp.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = RefreshTokenRespValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = RefreshTokenRespValidationError{}

// Validate checks the field values on AccountDeleteReq with the rules defined
// in the proto definition for this message. If any rules are violated, the
// first error encountered is returned, or nil if there are no violations.
func (m *AccountDeleteReq) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on AccountDeleteReq with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// AccountDeleteReqMultiError, or nil if none found.
func (m *AccountDeleteReq) ValidateAll() error {
	return m.validate(true)
}

func (m *AccountDeleteReq) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if len(errors) > 0 {
		return AccountDeleteReqMultiError(errors)
	}

	return nil
}

// AccountDeleteReqMultiError is an error wrapping multiple validation errors
// returned by AccountDeleteReq.ValidateAll() if the designated constraints
// aren't met.
type AccountDeleteReqMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m AccountDeleteReqMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m AccountDeleteReqMultiError) AllErrors() []error { return m }

// AccountDeleteReqValidationError is the validation error returned by
// AccountDeleteReq.Validate if the designated constraints aren't met.
type AccountDeleteReqValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e AccountDeleteReqValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e AccountDeleteReqValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e AccountDeleteReqValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e AccountDeleteReqValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e AccountDeleteReqValidationError) ErrorName() string { return "AccountDeleteReqValidationError" }

// Error satisfies the builtin error interface
func (e AccountDeleteReqValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sAccountDeleteReq.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = AccountDeleteReqValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = AccountDeleteReqValidationError{}

// Validate checks the field values on AccountDeleteResp with the rules defined
// in the proto definition for this message. If any rules are violated, the
// first error encountered is returned, or nil if there are no violations.
func (m *AccountDeleteResp) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on AccountDeleteResp with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// AccountDeleteRespMultiError, or nil if none found.
func (m *AccountDeleteResp) ValidateAll() error {
	return m.validate(true)
}

func (m *AccountDeleteResp) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetBase()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, AccountDeleteRespValidationError{
					field:  "Base",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, AccountDeleteRespValidationError{
					field:  "Base",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetBase()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return AccountDeleteRespValidationError{
				field:  "Base",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return AccountDeleteRespMultiError(errors)
	}

	return nil
}

// AccountDeleteRespMultiError is an error wrapping multiple validation errors
// returned by AccountDeleteResp.ValidateAll() if the designated constraints
// aren't met.
type AccountDeleteRespMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m AccountDeleteRespMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m AccountDeleteRespMultiError) AllErrors() []error { return m }

// AccountDeleteRespValidationError is the validation error returned by
// AccountDeleteResp.Validate if the designated constraints aren't met.
type AccountDeleteRespValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e AccountDeleteRespValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e AccountDeleteRespValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e AccountDeleteRespValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e AccountDeleteRespValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e AccountDeleteRespValidationError) ErrorName() string {
	return "AccountDeleteRespValidationError"
}

// Error satisfies the builtin error interface
func (e AccountDeleteRespValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sAccountDeleteResp.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = AccountDeleteRespValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = AccountDeleteRespValidationError{}

// Validate checks the field values on GetUserInfoReq with the rules defined in
// the proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *GetUserInfoReq) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GetUserInfoReq with the rules defined
// in the proto definition for this message. If any rules are violated, the
// result is a list of violation errors wrapped in GetUserInfoReqMultiError,
// or nil if none found.
func (m *GetUserInfoReq) ValidateAll() error {
	return m.validate(true)
}

func (m *GetUserInfoReq) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for UserId

	// no validation rules for WithFollow

	// no validation rules for WithScore

	if len(errors) > 0 {
		return GetUserInfoReqMultiError(errors)
	}

	return nil
}

// GetUserInfoReqMultiError is an error wrapping multiple validation errors
// returned by GetUserInfoReq.ValidateAll() if the designated constraints
// aren't met.
type GetUserInfoReqMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetUserInfoReqMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetUserInfoReqMultiError) AllErrors() []error { return m }

// GetUserInfoReqValidationError is the validation error returned by
// GetUserInfoReq.Validate if the designated constraints aren't met.
type GetUserInfoReqValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetUserInfoReqValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetUserInfoReqValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetUserInfoReqValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetUserInfoReqValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetUserInfoReqValidationError) ErrorName() string { return "GetUserInfoReqValidationError" }

// Error satisfies the builtin error interface
func (e GetUserInfoReqValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetUserInfoReq.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetUserInfoReqValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetUserInfoReqValidationError{}

// Validate checks the field values on GetUserInfoByCreationUserIdReq with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *GetUserInfoByCreationUserIdReq) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GetUserInfoByCreationUserIdReq with
// the rules defined in the proto definition for this message. If any rules
// are violated, the result is a list of violation errors wrapped in
// GetUserInfoByCreationUserIdReqMultiError, or nil if none found.
func (m *GetUserInfoByCreationUserIdReq) ValidateAll() error {
	return m.validate(true)
}

func (m *GetUserInfoByCreationUserIdReq) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if m.GetCreationUserId() <= 0 {
		err := GetUserInfoByCreationUserIdReqValidationError{
			field:  "CreationUserId",
			reason: "value must be greater than 0",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	// no validation rules for WithFollow

	// no validation rules for WithScore

	if len(errors) > 0 {
		return GetUserInfoByCreationUserIdReqMultiError(errors)
	}

	return nil
}

// GetUserInfoByCreationUserIdReqMultiError is an error wrapping multiple
// validation errors returned by GetUserInfoByCreationUserIdReq.ValidateAll()
// if the designated constraints aren't met.
type GetUserInfoByCreationUserIdReqMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetUserInfoByCreationUserIdReqMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetUserInfoByCreationUserIdReqMultiError) AllErrors() []error { return m }

// GetUserInfoByCreationUserIdReqValidationError is the validation error
// returned by GetUserInfoByCreationUserIdReq.Validate if the designated
// constraints aren't met.
type GetUserInfoByCreationUserIdReqValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetUserInfoByCreationUserIdReqValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetUserInfoByCreationUserIdReqValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetUserInfoByCreationUserIdReqValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetUserInfoByCreationUserIdReqValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetUserInfoByCreationUserIdReqValidationError) ErrorName() string {
	return "GetUserInfoByCreationUserIdReqValidationError"
}

// Error satisfies the builtin error interface
func (e GetUserInfoByCreationUserIdReqValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetUserInfoByCreationUserIdReq.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetUserInfoByCreationUserIdReqValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetUserInfoByCreationUserIdReqValidationError{}

// Validate checks the field values on GetUserInfoByCreationUserIdResp with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *GetUserInfoByCreationUserIdResp) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GetUserInfoByCreationUserIdResp with
// the rules defined in the proto definition for this message. If any rules
// are violated, the result is a list of violation errors wrapped in
// GetUserInfoByCreationUserIdRespMultiError, or nil if none found.
func (m *GetUserInfoByCreationUserIdResp) ValidateAll() error {
	return m.validate(true)
}

func (m *GetUserInfoByCreationUserIdResp) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetBase()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetUserInfoByCreationUserIdRespValidationError{
					field:  "Base",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetUserInfoByCreationUserIdRespValidationError{
					field:  "Base",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetBase()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetUserInfoByCreationUserIdRespValidationError{
				field:  "Base",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetData()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetUserInfoByCreationUserIdRespValidationError{
					field:  "Data",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetUserInfoByCreationUserIdRespValidationError{
					field:  "Data",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetData()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetUserInfoByCreationUserIdRespValidationError{
				field:  "Data",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return GetUserInfoByCreationUserIdRespMultiError(errors)
	}

	return nil
}

// GetUserInfoByCreationUserIdRespMultiError is an error wrapping multiple
// validation errors returned by GetUserInfoByCreationUserIdResp.ValidateAll()
// if the designated constraints aren't met.
type GetUserInfoByCreationUserIdRespMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetUserInfoByCreationUserIdRespMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetUserInfoByCreationUserIdRespMultiError) AllErrors() []error { return m }

// GetUserInfoByCreationUserIdRespValidationError is the validation error
// returned by GetUserInfoByCreationUserIdResp.Validate if the designated
// constraints aren't met.
type GetUserInfoByCreationUserIdRespValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetUserInfoByCreationUserIdRespValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetUserInfoByCreationUserIdRespValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetUserInfoByCreationUserIdRespValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetUserInfoByCreationUserIdRespValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetUserInfoByCreationUserIdRespValidationError) ErrorName() string {
	return "GetUserInfoByCreationUserIdRespValidationError"
}

// Error satisfies the builtin error interface
func (e GetUserInfoByCreationUserIdRespValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetUserInfoByCreationUserIdResp.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetUserInfoByCreationUserIdRespValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetUserInfoByCreationUserIdRespValidationError{}

// Validate checks the field values on GetUserInfoResp with the rules defined
// in the proto definition for this message. If any rules are violated, the
// first error encountered is returned, or nil if there are no violations.
func (m *GetUserInfoResp) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GetUserInfoResp with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// GetUserInfoRespMultiError, or nil if none found.
func (m *GetUserInfoResp) ValidateAll() error {
	return m.validate(true)
}

func (m *GetUserInfoResp) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetBase()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetUserInfoRespValidationError{
					field:  "Base",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetUserInfoRespValidationError{
					field:  "Base",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetBase()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetUserInfoRespValidationError{
				field:  "Base",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetData()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetUserInfoRespValidationError{
					field:  "Data",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetUserInfoRespValidationError{
					field:  "Data",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetData()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetUserInfoRespValidationError{
				field:  "Data",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return GetUserInfoRespMultiError(errors)
	}

	return nil
}

// GetUserInfoRespMultiError is an error wrapping multiple validation errors
// returned by GetUserInfoResp.ValidateAll() if the designated constraints
// aren't met.
type GetUserInfoRespMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetUserInfoRespMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetUserInfoRespMultiError) AllErrors() []error { return m }

// GetUserInfoRespValidationError is the validation error returned by
// GetUserInfoResp.Validate if the designated constraints aren't met.
type GetUserInfoRespValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetUserInfoRespValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetUserInfoRespValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetUserInfoRespValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetUserInfoRespValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetUserInfoRespValidationError) ErrorName() string { return "GetUserInfoRespValidationError" }

// Error satisfies the builtin error interface
func (e GetUserInfoRespValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetUserInfoResp.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetUserInfoRespValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetUserInfoRespValidationError{}

// Validate checks the field values on UserInfo with the rules defined in the
// proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *UserInfo) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on UserInfo with the rules defined in
// the proto definition for this message. If any rules are violated, the
// result is a list of violation errors wrapped in UserInfoMultiError, or nil
// if none found.
func (m *UserInfo) ValidateAll() error {
	return m.validate(true)
}

func (m *UserInfo) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for UserId

	// no validation rules for Nickname

	// no validation rules for Phone

	// no validation rules for Avatar

	// no validation rules for Gender

	// no validation rules for Status

	// no validation rules for CreatedAt

	// no validation rules for BackgroundUrl

	if all {
		switch v := interface{}(m.GetVoiceSignature()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, UserInfoValidationError{
					field:  "VoiceSignature",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, UserInfoValidationError{
					field:  "VoiceSignature",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetVoiceSignature()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return UserInfoValidationError{
				field:  "VoiceSignature",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for IsPremiumCreator

	// no validation rules for FollowersCount

	// no validation rules for FollowingCount

	// no validation rules for IsFollowing

	// no validation rules for IsFollowed

	if all {
		switch v := interface{}(m.GetScoreData()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, UserInfoValidationError{
					field:  "ScoreData",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, UserInfoValidationError{
					field:  "ScoreData",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetScoreData()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return UserInfoValidationError{
				field:  "ScoreData",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for ScriptCount

	// no validation rules for UserCharacterCount

	// no validation rules for IsDeleted

	// no validation rules for CreationUserId

	if len(errors) > 0 {
		return UserInfoMultiError(errors)
	}

	return nil
}

// UserInfoMultiError is an error wrapping multiple validation errors returned
// by UserInfo.ValidateAll() if the designated constraints aren't met.
type UserInfoMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m UserInfoMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m UserInfoMultiError) AllErrors() []error { return m }

// UserInfoValidationError is the validation error returned by
// UserInfo.Validate if the designated constraints aren't met.
type UserInfoValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e UserInfoValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e UserInfoValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e UserInfoValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e UserInfoValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e UserInfoValidationError) ErrorName() string { return "UserInfoValidationError" }

// Error satisfies the builtin error interface
func (e UserInfoValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sUserInfo.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = UserInfoValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = UserInfoValidationError{}

// Validate checks the field values on FollowData with the rules defined in the
// proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *FollowData) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on FollowData with the rules defined in
// the proto definition for this message. If any rules are violated, the
// result is a list of violation errors wrapped in FollowDataMultiError, or
// nil if none found.
func (m *FollowData) ValidateAll() error {
	return m.validate(true)
}

func (m *FollowData) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for FollowersCount

	// no validation rules for FollowingCount

	// no validation rules for IsFollowing

	// no validation rules for IsFollowed

	if len(errors) > 0 {
		return FollowDataMultiError(errors)
	}

	return nil
}

// FollowDataMultiError is an error wrapping multiple validation errors
// returned by FollowData.ValidateAll() if the designated constraints aren't met.
type FollowDataMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m FollowDataMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m FollowDataMultiError) AllErrors() []error { return m }

// FollowDataValidationError is the validation error returned by
// FollowData.Validate if the designated constraints aren't met.
type FollowDataValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e FollowDataValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e FollowDataValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e FollowDataValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e FollowDataValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e FollowDataValidationError) ErrorName() string { return "FollowDataValidationError" }

// Error satisfies the builtin error interface
func (e FollowDataValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sFollowData.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = FollowDataValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = FollowDataValidationError{}

// Validate checks the field values on VoiceSignature with the rules defined in
// the proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *VoiceSignature) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on VoiceSignature with the rules defined
// in the proto definition for this message. If any rules are violated, the
// result is a list of violation errors wrapped in VoiceSignatureMultiError,
// or nil if none found.
func (m *VoiceSignature) ValidateAll() error {
	return m.validate(true)
}

func (m *VoiceSignature) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for VoiceSignatureUrl

	// no validation rules for Duration

	// no validation rules for IsReviewing

	if len(errors) > 0 {
		return VoiceSignatureMultiError(errors)
	}

	return nil
}

// VoiceSignatureMultiError is an error wrapping multiple validation errors
// returned by VoiceSignature.ValidateAll() if the designated constraints
// aren't met.
type VoiceSignatureMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m VoiceSignatureMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m VoiceSignatureMultiError) AllErrors() []error { return m }

// VoiceSignatureValidationError is the validation error returned by
// VoiceSignature.Validate if the designated constraints aren't met.
type VoiceSignatureValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e VoiceSignatureValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e VoiceSignatureValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e VoiceSignatureValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e VoiceSignatureValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e VoiceSignatureValidationError) ErrorName() string { return "VoiceSignatureValidationError" }

// Error satisfies the builtin error interface
func (e VoiceSignatureValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sVoiceSignature.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = VoiceSignatureValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = VoiceSignatureValidationError{}

// Validate checks the field values on BatchGetUserInfoReq with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *BatchGetUserInfoReq) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on BatchGetUserInfoReq with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// BatchGetUserInfoReqMultiError, or nil if none found.
func (m *BatchGetUserInfoReq) ValidateAll() error {
	return m.validate(true)
}

func (m *BatchGetUserInfoReq) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for UserId

	if len(errors) > 0 {
		return BatchGetUserInfoReqMultiError(errors)
	}

	return nil
}

// BatchGetUserInfoReqMultiError is an error wrapping multiple validation
// errors returned by BatchGetUserInfoReq.ValidateAll() if the designated
// constraints aren't met.
type BatchGetUserInfoReqMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m BatchGetUserInfoReqMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m BatchGetUserInfoReqMultiError) AllErrors() []error { return m }

// BatchGetUserInfoReqValidationError is the validation error returned by
// BatchGetUserInfoReq.Validate if the designated constraints aren't met.
type BatchGetUserInfoReqValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e BatchGetUserInfoReqValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e BatchGetUserInfoReqValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e BatchGetUserInfoReqValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e BatchGetUserInfoReqValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e BatchGetUserInfoReqValidationError) ErrorName() string {
	return "BatchGetUserInfoReqValidationError"
}

// Error satisfies the builtin error interface
func (e BatchGetUserInfoReqValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sBatchGetUserInfoReq.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = BatchGetUserInfoReqValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = BatchGetUserInfoReqValidationError{}

// Validate checks the field values on BatchGetUserInfoResp with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *BatchGetUserInfoResp) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on BatchGetUserInfoResp with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// BatchGetUserInfoRespMultiError, or nil if none found.
func (m *BatchGetUserInfoResp) ValidateAll() error {
	return m.validate(true)
}

func (m *BatchGetUserInfoResp) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetBase()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, BatchGetUserInfoRespValidationError{
					field:  "Base",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, BatchGetUserInfoRespValidationError{
					field:  "Base",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetBase()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return BatchGetUserInfoRespValidationError{
				field:  "Base",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	{
		sorted_keys := make([]int64, len(m.GetUserInfoMap()))
		i := 0
		for key := range m.GetUserInfoMap() {
			sorted_keys[i] = key
			i++
		}
		sort.Slice(sorted_keys, func(i, j int) bool { return sorted_keys[i] < sorted_keys[j] })
		for _, key := range sorted_keys {
			val := m.GetUserInfoMap()[key]
			_ = val

			// no validation rules for UserInfoMap[key]

			if all {
				switch v := interface{}(val).(type) {
				case interface{ ValidateAll() error }:
					if err := v.ValidateAll(); err != nil {
						errors = append(errors, BatchGetUserInfoRespValidationError{
							field:  fmt.Sprintf("UserInfoMap[%v]", key),
							reason: "embedded message failed validation",
							cause:  err,
						})
					}
				case interface{ Validate() error }:
					if err := v.Validate(); err != nil {
						errors = append(errors, BatchGetUserInfoRespValidationError{
							field:  fmt.Sprintf("UserInfoMap[%v]", key),
							reason: "embedded message failed validation",
							cause:  err,
						})
					}
				}
			} else if v, ok := interface{}(val).(interface{ Validate() error }); ok {
				if err := v.Validate(); err != nil {
					return BatchGetUserInfoRespValidationError{
						field:  fmt.Sprintf("UserInfoMap[%v]", key),
						reason: "embedded message failed validation",
						cause:  err,
					}
				}
			}

		}
	}

	if len(errors) > 0 {
		return BatchGetUserInfoRespMultiError(errors)
	}

	return nil
}

// BatchGetUserInfoRespMultiError is an error wrapping multiple validation
// errors returned by BatchGetUserInfoResp.ValidateAll() if the designated
// constraints aren't met.
type BatchGetUserInfoRespMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m BatchGetUserInfoRespMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m BatchGetUserInfoRespMultiError) AllErrors() []error { return m }

// BatchGetUserInfoRespValidationError is the validation error returned by
// BatchGetUserInfoResp.Validate if the designated constraints aren't met.
type BatchGetUserInfoRespValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e BatchGetUserInfoRespValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e BatchGetUserInfoRespValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e BatchGetUserInfoRespValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e BatchGetUserInfoRespValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e BatchGetUserInfoRespValidationError) ErrorName() string {
	return "BatchGetUserInfoRespValidationError"
}

// Error satisfies the builtin error interface
func (e BatchGetUserInfoRespValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sBatchGetUserInfoResp.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = BatchGetUserInfoRespValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = BatchGetUserInfoRespValidationError{}

// Validate checks the field values on UpdateUserInfoReq with the rules defined
// in the proto definition for this message. If any rules are violated, the
// first error encountered is returned, or nil if there are no violations.
func (m *UpdateUserInfoReq) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on UpdateUserInfoReq with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// UpdateUserInfoReqMultiError, or nil if none found.
func (m *UpdateUserInfoReq) ValidateAll() error {
	return m.validate(true)
}

func (m *UpdateUserInfoReq) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if m.GetUserId() <= 0 {
		err := UpdateUserInfoReqValidationError{
			field:  "UserId",
			reason: "value must be greater than 0",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	// no validation rules for Avatar

	// no validation rules for Nickname

	// no validation rules for BackgroundUrl

	// no validation rules for VoiceSignatureUrl

	// no validation rules for VoiceDuration

	// no validation rules for IsPremiumCreator

	// no validation rules for ResetVoiceSignature

	// no validation rules for CreationUserId

	if len(errors) > 0 {
		return UpdateUserInfoReqMultiError(errors)
	}

	return nil
}

// UpdateUserInfoReqMultiError is an error wrapping multiple validation errors
// returned by UpdateUserInfoReq.ValidateAll() if the designated constraints
// aren't met.
type UpdateUserInfoReqMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m UpdateUserInfoReqMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m UpdateUserInfoReqMultiError) AllErrors() []error { return m }

// UpdateUserInfoReqValidationError is the validation error returned by
// UpdateUserInfoReq.Validate if the designated constraints aren't met.
type UpdateUserInfoReqValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e UpdateUserInfoReqValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e UpdateUserInfoReqValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e UpdateUserInfoReqValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e UpdateUserInfoReqValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e UpdateUserInfoReqValidationError) ErrorName() string {
	return "UpdateUserInfoReqValidationError"
}

// Error satisfies the builtin error interface
func (e UpdateUserInfoReqValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sUpdateUserInfoReq.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = UpdateUserInfoReqValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = UpdateUserInfoReqValidationError{}

// Validate checks the field values on UpdateUserInfoResp with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *UpdateUserInfoResp) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on UpdateUserInfoResp with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// UpdateUserInfoRespMultiError, or nil if none found.
func (m *UpdateUserInfoResp) ValidateAll() error {
	return m.validate(true)
}

func (m *UpdateUserInfoResp) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetBase()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, UpdateUserInfoRespValidationError{
					field:  "Base",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, UpdateUserInfoRespValidationError{
					field:  "Base",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetBase()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return UpdateUserInfoRespValidationError{
				field:  "Base",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return UpdateUserInfoRespMultiError(errors)
	}

	return nil
}

// UpdateUserInfoRespMultiError is an error wrapping multiple validation errors
// returned by UpdateUserInfoResp.ValidateAll() if the designated constraints
// aren't met.
type UpdateUserInfoRespMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m UpdateUserInfoRespMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m UpdateUserInfoRespMultiError) AllErrors() []error { return m }

// UpdateUserInfoRespValidationError is the validation error returned by
// UpdateUserInfoResp.Validate if the designated constraints aren't met.
type UpdateUserInfoRespValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e UpdateUserInfoRespValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e UpdateUserInfoRespValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e UpdateUserInfoRespValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e UpdateUserInfoRespValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e UpdateUserInfoRespValidationError) ErrorName() string {
	return "UpdateUserInfoRespValidationError"
}

// Error satisfies the builtin error interface
func (e UpdateUserInfoRespValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sUpdateUserInfoResp.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = UpdateUserInfoRespValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = UpdateUserInfoRespValidationError{}

// Validate checks the field values on SetPremiumCreatorReq with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *SetPremiumCreatorReq) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on SetPremiumCreatorReq with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// SetPremiumCreatorReqMultiError, or nil if none found.
func (m *SetPremiumCreatorReq) ValidateAll() error {
	return m.validate(true)
}

func (m *SetPremiumCreatorReq) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if m.GetUserId() <= 0 {
		err := SetPremiumCreatorReqValidationError{
			field:  "UserId",
			reason: "value must be greater than 0",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	// no validation rules for IsPremiumCreator

	if len(errors) > 0 {
		return SetPremiumCreatorReqMultiError(errors)
	}

	return nil
}

// SetPremiumCreatorReqMultiError is an error wrapping multiple validation
// errors returned by SetPremiumCreatorReq.ValidateAll() if the designated
// constraints aren't met.
type SetPremiumCreatorReqMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m SetPremiumCreatorReqMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m SetPremiumCreatorReqMultiError) AllErrors() []error { return m }

// SetPremiumCreatorReqValidationError is the validation error returned by
// SetPremiumCreatorReq.Validate if the designated constraints aren't met.
type SetPremiumCreatorReqValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e SetPremiumCreatorReqValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e SetPremiumCreatorReqValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e SetPremiumCreatorReqValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e SetPremiumCreatorReqValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e SetPremiumCreatorReqValidationError) ErrorName() string {
	return "SetPremiumCreatorReqValidationError"
}

// Error satisfies the builtin error interface
func (e SetPremiumCreatorReqValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sSetPremiumCreatorReq.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = SetPremiumCreatorReqValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = SetPremiumCreatorReqValidationError{}

// Validate checks the field values on SetPremiumCreatorResp with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *SetPremiumCreatorResp) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on SetPremiumCreatorResp with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// SetPremiumCreatorRespMultiError, or nil if none found.
func (m *SetPremiumCreatorResp) ValidateAll() error {
	return m.validate(true)
}

func (m *SetPremiumCreatorResp) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetBase()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, SetPremiumCreatorRespValidationError{
					field:  "Base",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, SetPremiumCreatorRespValidationError{
					field:  "Base",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetBase()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return SetPremiumCreatorRespValidationError{
				field:  "Base",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return SetPremiumCreatorRespMultiError(errors)
	}

	return nil
}

// SetPremiumCreatorRespMultiError is an error wrapping multiple validation
// errors returned by SetPremiumCreatorResp.ValidateAll() if the designated
// constraints aren't met.
type SetPremiumCreatorRespMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m SetPremiumCreatorRespMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m SetPremiumCreatorRespMultiError) AllErrors() []error { return m }

// SetPremiumCreatorRespValidationError is the validation error returned by
// SetPremiumCreatorResp.Validate if the designated constraints aren't met.
type SetPremiumCreatorRespValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e SetPremiumCreatorRespValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e SetPremiumCreatorRespValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e SetPremiumCreatorRespValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e SetPremiumCreatorRespValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e SetPremiumCreatorRespValidationError) ErrorName() string {
	return "SetPremiumCreatorRespValidationError"
}

// Error satisfies the builtin error interface
func (e SetPremiumCreatorRespValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sSetPremiumCreatorResp.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = SetPremiumCreatorRespValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = SetPremiumCreatorRespValidationError{}

// Validate checks the field values on AccountOnlineReq with the rules defined
// in the proto definition for this message. If any rules are violated, the
// first error encountered is returned, or nil if there are no violations.
func (m *AccountOnlineReq) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on AccountOnlineReq with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// AccountOnlineReqMultiError, or nil if none found.
func (m *AccountOnlineReq) ValidateAll() error {
	return m.validate(true)
}

func (m *AccountOnlineReq) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetData()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, AccountOnlineReqValidationError{
					field:  "Data",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, AccountOnlineReqValidationError{
					field:  "Data",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetData()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return AccountOnlineReqValidationError{
				field:  "Data",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if m.GetUserid() <= 0 {
		err := AccountOnlineReqValidationError{
			field:  "Userid",
			reason: "value must be greater than 0",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	// no validation rules for State

	// no validation rules for Heartbeat

	// no validation rules for NeedResp

	// no validation rules for Front

	// no validation rules for OnConnectType

	if len(errors) > 0 {
		return AccountOnlineReqMultiError(errors)
	}

	return nil
}

// AccountOnlineReqMultiError is an error wrapping multiple validation errors
// returned by AccountOnlineReq.ValidateAll() if the designated constraints
// aren't met.
type AccountOnlineReqMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m AccountOnlineReqMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m AccountOnlineReqMultiError) AllErrors() []error { return m }

// AccountOnlineReqValidationError is the validation error returned by
// AccountOnlineReq.Validate if the designated constraints aren't met.
type AccountOnlineReqValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e AccountOnlineReqValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e AccountOnlineReqValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e AccountOnlineReqValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e AccountOnlineReqValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e AccountOnlineReqValidationError) ErrorName() string { return "AccountOnlineReqValidationError" }

// Error satisfies the builtin error interface
func (e AccountOnlineReqValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sAccountOnlineReq.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = AccountOnlineReqValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = AccountOnlineReqValidationError{}

// Validate checks the field values on AccountOnlineResp with the rules defined
// in the proto definition for this message. If any rules are violated, the
// first error encountered is returned, or nil if there are no violations.
func (m *AccountOnlineResp) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on AccountOnlineResp with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// AccountOnlineRespMultiError, or nil if none found.
func (m *AccountOnlineResp) ValidateAll() error {
	return m.validate(true)
}

func (m *AccountOnlineResp) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetBase()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, AccountOnlineRespValidationError{
					field:  "Base",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, AccountOnlineRespValidationError{
					field:  "Base",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetBase()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return AccountOnlineRespValidationError{
				field:  "Base",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return AccountOnlineRespMultiError(errors)
	}

	return nil
}

// AccountOnlineRespMultiError is an error wrapping multiple validation errors
// returned by AccountOnlineResp.ValidateAll() if the designated constraints
// aren't met.
type AccountOnlineRespMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m AccountOnlineRespMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m AccountOnlineRespMultiError) AllErrors() []error { return m }

// AccountOnlineRespValidationError is the validation error returned by
// AccountOnlineResp.Validate if the designated constraints aren't met.
type AccountOnlineRespValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e AccountOnlineRespValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e AccountOnlineRespValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e AccountOnlineRespValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e AccountOnlineRespValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e AccountOnlineRespValidationError) ErrorName() string {
	return "AccountOnlineRespValidationError"
}

// Error satisfies the builtin error interface
func (e AccountOnlineRespValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sAccountOnlineResp.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = AccountOnlineRespValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = AccountOnlineRespValidationError{}

// Validate checks the field values on IsUserBlacklistReq with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *IsUserBlacklistReq) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on IsUserBlacklistReq with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// IsUserBlacklistReqMultiError, or nil if none found.
func (m *IsUserBlacklistReq) ValidateAll() error {
	return m.validate(true)
}

func (m *IsUserBlacklistReq) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for UserId

	if len(errors) > 0 {
		return IsUserBlacklistReqMultiError(errors)
	}

	return nil
}

// IsUserBlacklistReqMultiError is an error wrapping multiple validation errors
// returned by IsUserBlacklistReq.ValidateAll() if the designated constraints
// aren't met.
type IsUserBlacklistReqMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m IsUserBlacklistReqMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m IsUserBlacklistReqMultiError) AllErrors() []error { return m }

// IsUserBlacklistReqValidationError is the validation error returned by
// IsUserBlacklistReq.Validate if the designated constraints aren't met.
type IsUserBlacklistReqValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e IsUserBlacklistReqValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e IsUserBlacklistReqValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e IsUserBlacklistReqValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e IsUserBlacklistReqValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e IsUserBlacklistReqValidationError) ErrorName() string {
	return "IsUserBlacklistReqValidationError"
}

// Error satisfies the builtin error interface
func (e IsUserBlacklistReqValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sIsUserBlacklistReq.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = IsUserBlacklistReqValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = IsUserBlacklistReqValidationError{}

// Validate checks the field values on IsUserBlacklistResp with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *IsUserBlacklistResp) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on IsUserBlacklistResp with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// IsUserBlacklistRespMultiError, or nil if none found.
func (m *IsUserBlacklistResp) ValidateAll() error {
	return m.validate(true)
}

func (m *IsUserBlacklistResp) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetBase()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, IsUserBlacklistRespValidationError{
					field:  "Base",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, IsUserBlacklistRespValidationError{
					field:  "Base",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetBase()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return IsUserBlacklistRespValidationError{
				field:  "Base",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for IsBlacklist

	if len(errors) > 0 {
		return IsUserBlacklistRespMultiError(errors)
	}

	return nil
}

// IsUserBlacklistRespMultiError is an error wrapping multiple validation
// errors returned by IsUserBlacklistResp.ValidateAll() if the designated
// constraints aren't met.
type IsUserBlacklistRespMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m IsUserBlacklistRespMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m IsUserBlacklistRespMultiError) AllErrors() []error { return m }

// IsUserBlacklistRespValidationError is the validation error returned by
// IsUserBlacklistResp.Validate if the designated constraints aren't met.
type IsUserBlacklistRespValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e IsUserBlacklistRespValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e IsUserBlacklistRespValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e IsUserBlacklistRespValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e IsUserBlacklistRespValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e IsUserBlacklistRespValidationError) ErrorName() string {
	return "IsUserBlacklistRespValidationError"
}

// Error satisfies the builtin error interface
func (e IsUserBlacklistRespValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sIsUserBlacklistResp.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = IsUserBlacklistRespValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = IsUserBlacklistRespValidationError{}

// Validate checks the field values on GetUserScoreReq with the rules defined
// in the proto definition for this message. If any rules are violated, the
// first error encountered is returned, or nil if there are no violations.
func (m *GetUserScoreReq) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GetUserScoreReq with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// GetUserScoreReqMultiError, or nil if none found.
func (m *GetUserScoreReq) ValidateAll() error {
	return m.validate(true)
}

func (m *GetUserScoreReq) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for UserId

	if len(errors) > 0 {
		return GetUserScoreReqMultiError(errors)
	}

	return nil
}

// GetUserScoreReqMultiError is an error wrapping multiple validation errors
// returned by GetUserScoreReq.ValidateAll() if the designated constraints
// aren't met.
type GetUserScoreReqMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetUserScoreReqMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetUserScoreReqMultiError) AllErrors() []error { return m }

// GetUserScoreReqValidationError is the validation error returned by
// GetUserScoreReq.Validate if the designated constraints aren't met.
type GetUserScoreReqValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetUserScoreReqValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetUserScoreReqValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetUserScoreReqValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetUserScoreReqValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetUserScoreReqValidationError) ErrorName() string { return "GetUserScoreReqValidationError" }

// Error satisfies the builtin error interface
func (e GetUserScoreReqValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetUserScoreReq.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetUserScoreReqValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetUserScoreReqValidationError{}

// Validate checks the field values on GetUserScoreResp with the rules defined
// in the proto definition for this message. If any rules are violated, the
// first error encountered is returned, or nil if there are no violations.
func (m *GetUserScoreResp) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GetUserScoreResp with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// GetUserScoreRespMultiError, or nil if none found.
func (m *GetUserScoreResp) ValidateAll() error {
	return m.validate(true)
}

func (m *GetUserScoreResp) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetBase()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetUserScoreRespValidationError{
					field:  "Base",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetUserScoreRespValidationError{
					field:  "Base",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetBase()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetUserScoreRespValidationError{
				field:  "Base",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetData()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetUserScoreRespValidationError{
					field:  "Data",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetUserScoreRespValidationError{
					field:  "Data",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetData()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetUserScoreRespValidationError{
				field:  "Data",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return GetUserScoreRespMultiError(errors)
	}

	return nil
}

// GetUserScoreRespMultiError is an error wrapping multiple validation errors
// returned by GetUserScoreResp.ValidateAll() if the designated constraints
// aren't met.
type GetUserScoreRespMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetUserScoreRespMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetUserScoreRespMultiError) AllErrors() []error { return m }

// GetUserScoreRespValidationError is the validation error returned by
// GetUserScoreResp.Validate if the designated constraints aren't met.
type GetUserScoreRespValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetUserScoreRespValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetUserScoreRespValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetUserScoreRespValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetUserScoreRespValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetUserScoreRespValidationError) ErrorName() string { return "GetUserScoreRespValidationError" }

// Error satisfies the builtin error interface
func (e GetUserScoreRespValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetUserScoreResp.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetUserScoreRespValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetUserScoreRespValidationError{}

// Validate checks the field values on AddUserScoreReq with the rules defined
// in the proto definition for this message. If any rules are violated, the
// first error encountered is returned, or nil if there are no violations.
func (m *AddUserScoreReq) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on AddUserScoreReq with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// AddUserScoreReqMultiError, or nil if none found.
func (m *AddUserScoreReq) ValidateAll() error {
	return m.validate(true)
}

func (m *AddUserScoreReq) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if m.GetUserId() <= 0 {
		err := AddUserScoreReqValidationError{
			field:  "UserId",
			reason: "value must be greater than 0",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if m.GetValuableScore() < 0 {
		err := AddUserScoreReqValidationError{
			field:  "ValuableScore",
			reason: "value must be greater than or equal to 0",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if m.GetFeeScore() < 0 {
		err := AddUserScoreReqValidationError{
			field:  "FeeScore",
			reason: "value must be greater than or equal to 0",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if m.GetBizType() <= 0 {
		err := AddUserScoreReqValidationError{
			field:  "BizType",
			reason: "value must be greater than 0",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if l := len(m.GetBizName()); l < 1 || l > 64 {
		err := AddUserScoreReqValidationError{
			field:  "BizName",
			reason: "value length must be between 1 and 64 bytes, inclusive",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if l := len(m.GetOrderId()); l < 1 || l > 64 {
		err := AddUserScoreReqValidationError{
			field:  "OrderId",
			reason: "value length must be between 1 and 64 bytes, inclusive",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if len(m.GetRemark()) > 255 {
		err := AddUserScoreReqValidationError{
			field:  "Remark",
			reason: "value length must be at most 255 bytes",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if len(errors) > 0 {
		return AddUserScoreReqMultiError(errors)
	}

	return nil
}

// AddUserScoreReqMultiError is an error wrapping multiple validation errors
// returned by AddUserScoreReq.ValidateAll() if the designated constraints
// aren't met.
type AddUserScoreReqMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m AddUserScoreReqMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m AddUserScoreReqMultiError) AllErrors() []error { return m }

// AddUserScoreReqValidationError is the validation error returned by
// AddUserScoreReq.Validate if the designated constraints aren't met.
type AddUserScoreReqValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e AddUserScoreReqValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e AddUserScoreReqValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e AddUserScoreReqValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e AddUserScoreReqValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e AddUserScoreReqValidationError) ErrorName() string { return "AddUserScoreReqValidationError" }

// Error satisfies the builtin error interface
func (e AddUserScoreReqValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sAddUserScoreReq.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = AddUserScoreReqValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = AddUserScoreReqValidationError{}

// Validate checks the field values on AddUserScoreResp with the rules defined
// in the proto definition for this message. If any rules are violated, the
// first error encountered is returned, or nil if there are no violations.
func (m *AddUserScoreResp) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on AddUserScoreResp with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// AddUserScoreRespMultiError, or nil if none found.
func (m *AddUserScoreResp) ValidateAll() error {
	return m.validate(true)
}

func (m *AddUserScoreResp) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetBase()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, AddUserScoreRespValidationError{
					field:  "Base",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, AddUserScoreRespValidationError{
					field:  "Base",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetBase()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return AddUserScoreRespValidationError{
				field:  "Base",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return AddUserScoreRespMultiError(errors)
	}

	return nil
}

// AddUserScoreRespMultiError is an error wrapping multiple validation errors
// returned by AddUserScoreResp.ValidateAll() if the designated constraints
// aren't met.
type AddUserScoreRespMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m AddUserScoreRespMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m AddUserScoreRespMultiError) AllErrors() []error { return m }

// AddUserScoreRespValidationError is the validation error returned by
// AddUserScoreResp.Validate if the designated constraints aren't met.
type AddUserScoreRespValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e AddUserScoreRespValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e AddUserScoreRespValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e AddUserScoreRespValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e AddUserScoreRespValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e AddUserScoreRespValidationError) ErrorName() string { return "AddUserScoreRespValidationError" }

// Error satisfies the builtin error interface
func (e AddUserScoreRespValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sAddUserScoreResp.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = AddUserScoreRespValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = AddUserScoreRespValidationError{}

// Validate checks the field values on ConsumeUserScoreReq with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *ConsumeUserScoreReq) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on ConsumeUserScoreReq with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// ConsumeUserScoreReqMultiError, or nil if none found.
func (m *ConsumeUserScoreReq) ValidateAll() error {
	return m.validate(true)
}

func (m *ConsumeUserScoreReq) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if m.GetUserId() <= 0 {
		err := ConsumeUserScoreReqValidationError{
			field:  "UserId",
			reason: "value must be greater than 0",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if m.GetScore() <= 0 {
		err := ConsumeUserScoreReqValidationError{
			field:  "Score",
			reason: "value must be greater than 0",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if m.GetBizType() <= 0 {
		err := ConsumeUserScoreReqValidationError{
			field:  "BizType",
			reason: "value must be greater than 0",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if l := len(m.GetBizName()); l < 1 || l > 64 {
		err := ConsumeUserScoreReqValidationError{
			field:  "BizName",
			reason: "value length must be between 1 and 64 bytes, inclusive",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if l := len(m.GetOrderId()); l < 1 || l > 64 {
		err := ConsumeUserScoreReqValidationError{
			field:  "OrderId",
			reason: "value length must be between 1 and 64 bytes, inclusive",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	// no validation rules for AllowOverdraw

	if len(errors) > 0 {
		return ConsumeUserScoreReqMultiError(errors)
	}

	return nil
}

// ConsumeUserScoreReqMultiError is an error wrapping multiple validation
// errors returned by ConsumeUserScoreReq.ValidateAll() if the designated
// constraints aren't met.
type ConsumeUserScoreReqMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m ConsumeUserScoreReqMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m ConsumeUserScoreReqMultiError) AllErrors() []error { return m }

// ConsumeUserScoreReqValidationError is the validation error returned by
// ConsumeUserScoreReq.Validate if the designated constraints aren't met.
type ConsumeUserScoreReqValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e ConsumeUserScoreReqValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e ConsumeUserScoreReqValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e ConsumeUserScoreReqValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e ConsumeUserScoreReqValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e ConsumeUserScoreReqValidationError) ErrorName() string {
	return "ConsumeUserScoreReqValidationError"
}

// Error satisfies the builtin error interface
func (e ConsumeUserScoreReqValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sConsumeUserScoreReq.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = ConsumeUserScoreReqValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = ConsumeUserScoreReqValidationError{}

// Validate checks the field values on ConsumeUserScoreResp with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *ConsumeUserScoreResp) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on ConsumeUserScoreResp with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// ConsumeUserScoreRespMultiError, or nil if none found.
func (m *ConsumeUserScoreResp) ValidateAll() error {
	return m.validate(true)
}

func (m *ConsumeUserScoreResp) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetBase()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, ConsumeUserScoreRespValidationError{
					field:  "Base",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, ConsumeUserScoreRespValidationError{
					field:  "Base",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetBase()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return ConsumeUserScoreRespValidationError{
				field:  "Base",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetData()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, ConsumeUserScoreRespValidationError{
					field:  "Data",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, ConsumeUserScoreRespValidationError{
					field:  "Data",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetData()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return ConsumeUserScoreRespValidationError{
				field:  "Data",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return ConsumeUserScoreRespMultiError(errors)
	}

	return nil
}

// ConsumeUserScoreRespMultiError is an error wrapping multiple validation
// errors returned by ConsumeUserScoreResp.ValidateAll() if the designated
// constraints aren't met.
type ConsumeUserScoreRespMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m ConsumeUserScoreRespMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m ConsumeUserScoreRespMultiError) AllErrors() []error { return m }

// ConsumeUserScoreRespValidationError is the validation error returned by
// ConsumeUserScoreResp.Validate if the designated constraints aren't met.
type ConsumeUserScoreRespValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e ConsumeUserScoreRespValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e ConsumeUserScoreRespValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e ConsumeUserScoreRespValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e ConsumeUserScoreRespValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e ConsumeUserScoreRespValidationError) ErrorName() string {
	return "ConsumeUserScoreRespValidationError"
}

// Error satisfies the builtin error interface
func (e ConsumeUserScoreRespValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sConsumeUserScoreResp.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = ConsumeUserScoreRespValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = ConsumeUserScoreRespValidationError{}

// Validate checks the field values on ConsumeUserScoreRespData with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *ConsumeUserScoreRespData) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on ConsumeUserScoreRespData with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// ConsumeUserScoreRespDataMultiError, or nil if none found.
func (m *ConsumeUserScoreRespData) ValidateAll() error {
	return m.validate(true)
}

func (m *ConsumeUserScoreRespData) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for ConsumeValuableScore

	// no validation rules for ConsumeFreeScore

	if len(errors) > 0 {
		return ConsumeUserScoreRespDataMultiError(errors)
	}

	return nil
}

// ConsumeUserScoreRespDataMultiError is an error wrapping multiple validation
// errors returned by ConsumeUserScoreRespData.ValidateAll() if the designated
// constraints aren't met.
type ConsumeUserScoreRespDataMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m ConsumeUserScoreRespDataMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m ConsumeUserScoreRespDataMultiError) AllErrors() []error { return m }

// ConsumeUserScoreRespDataValidationError is the validation error returned by
// ConsumeUserScoreRespData.Validate if the designated constraints aren't met.
type ConsumeUserScoreRespDataValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e ConsumeUserScoreRespDataValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e ConsumeUserScoreRespDataValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e ConsumeUserScoreRespDataValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e ConsumeUserScoreRespDataValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e ConsumeUserScoreRespDataValidationError) ErrorName() string {
	return "ConsumeUserScoreRespDataValidationError"
}

// Error satisfies the builtin error interface
func (e ConsumeUserScoreRespDataValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sConsumeUserScoreRespData.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = ConsumeUserScoreRespDataValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = ConsumeUserScoreRespDataValidationError{}

// Validate checks the field values on UserScoreData with the rules defined in
// the proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *UserScoreData) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on UserScoreData with the rules defined
// in the proto definition for this message. If any rules are violated, the
// result is a list of violation errors wrapped in UserScoreDataMultiError, or
// nil if none found.
func (m *UserScoreData) ValidateAll() error {
	return m.validate(true)
}

func (m *UserScoreData) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for TotalBalance

	// no validation rules for FreeBalance

	// no validation rules for ValuableBalance

	// no validation rules for TotalValuableScore

	// no validation rules for TotalFreeScore

	// no validation rules for DisplayTotalBalance

	if len(errors) > 0 {
		return UserScoreDataMultiError(errors)
	}

	return nil
}

// UserScoreDataMultiError is an error wrapping multiple validation errors
// returned by UserScoreData.ValidateAll() if the designated constraints
// aren't met.
type UserScoreDataMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m UserScoreDataMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m UserScoreDataMultiError) AllErrors() []error { return m }

// UserScoreDataValidationError is the validation error returned by
// UserScoreData.Validate if the designated constraints aren't met.
type UserScoreDataValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e UserScoreDataValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e UserScoreDataValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e UserScoreDataValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e UserScoreDataValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e UserScoreDataValidationError) ErrorName() string { return "UserScoreDataValidationError" }

// Error satisfies the builtin error interface
func (e UserScoreDataValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sUserScoreData.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = UserScoreDataValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = UserScoreDataValidationError{}

// Validate checks the field values on SearchUsersReq with the rules defined in
// the proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *SearchUsersReq) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on SearchUsersReq with the rules defined
// in the proto definition for this message. If any rules are violated, the
// result is a list of violation errors wrapped in SearchUsersReqMultiError,
// or nil if none found.
func (m *SearchUsersReq) ValidateAll() error {
	return m.validate(true)
}

func (m *SearchUsersReq) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if utf8.RuneCountInString(m.GetKeyword()) < 1 {
		err := SearchUsersReqValidationError{
			field:  "Keyword",
			reason: "value length must be at least 1 runes",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	// no validation rules for Page

	// no validation rules for PageSize

	if len(errors) > 0 {
		return SearchUsersReqMultiError(errors)
	}

	return nil
}

// SearchUsersReqMultiError is an error wrapping multiple validation errors
// returned by SearchUsersReq.ValidateAll() if the designated constraints
// aren't met.
type SearchUsersReqMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m SearchUsersReqMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m SearchUsersReqMultiError) AllErrors() []error { return m }

// SearchUsersReqValidationError is the validation error returned by
// SearchUsersReq.Validate if the designated constraints aren't met.
type SearchUsersReqValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e SearchUsersReqValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e SearchUsersReqValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e SearchUsersReqValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e SearchUsersReqValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e SearchUsersReqValidationError) ErrorName() string { return "SearchUsersReqValidationError" }

// Error satisfies the builtin error interface
func (e SearchUsersReqValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sSearchUsersReq.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = SearchUsersReqValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = SearchUsersReqValidationError{}

// Validate checks the field values on SearchUsersResp with the rules defined
// in the proto definition for this message. If any rules are violated, the
// first error encountered is returned, or nil if there are no violations.
func (m *SearchUsersResp) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on SearchUsersResp with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// SearchUsersRespMultiError, or nil if none found.
func (m *SearchUsersResp) ValidateAll() error {
	return m.validate(true)
}

func (m *SearchUsersResp) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetBase()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, SearchUsersRespValidationError{
					field:  "Base",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, SearchUsersRespValidationError{
					field:  "Base",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetBase()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return SearchUsersRespValidationError{
				field:  "Base",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetData()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, SearchUsersRespValidationError{
					field:  "Data",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, SearchUsersRespValidationError{
					field:  "Data",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetData()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return SearchUsersRespValidationError{
				field:  "Data",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return SearchUsersRespMultiError(errors)
	}

	return nil
}

// SearchUsersRespMultiError is an error wrapping multiple validation errors
// returned by SearchUsersResp.ValidateAll() if the designated constraints
// aren't met.
type SearchUsersRespMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m SearchUsersRespMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m SearchUsersRespMultiError) AllErrors() []error { return m }

// SearchUsersRespValidationError is the validation error returned by
// SearchUsersResp.Validate if the designated constraints aren't met.
type SearchUsersRespValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e SearchUsersRespValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e SearchUsersRespValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e SearchUsersRespValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e SearchUsersRespValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e SearchUsersRespValidationError) ErrorName() string { return "SearchUsersRespValidationError" }

// Error satisfies the builtin error interface
func (e SearchUsersRespValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sSearchUsersResp.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = SearchUsersRespValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = SearchUsersRespValidationError{}

// Validate checks the field values on SearchUsersRespData with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *SearchUsersRespData) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on SearchUsersRespData with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// SearchUsersRespDataMultiError, or nil if none found.
func (m *SearchUsersRespData) ValidateAll() error {
	return m.validate(true)
}

func (m *SearchUsersRespData) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	for idx, item := range m.GetUsers() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, SearchUsersRespDataValidationError{
						field:  fmt.Sprintf("Users[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, SearchUsersRespDataValidationError{
						field:  fmt.Sprintf("Users[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return SearchUsersRespDataValidationError{
					field:  fmt.Sprintf("Users[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	// no validation rules for Total

	if len(errors) > 0 {
		return SearchUsersRespDataMultiError(errors)
	}

	return nil
}

// SearchUsersRespDataMultiError is an error wrapping multiple validation
// errors returned by SearchUsersRespData.ValidateAll() if the designated
// constraints aren't met.
type SearchUsersRespDataMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m SearchUsersRespDataMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m SearchUsersRespDataMultiError) AllErrors() []error { return m }

// SearchUsersRespDataValidationError is the validation error returned by
// SearchUsersRespData.Validate if the designated constraints aren't met.
type SearchUsersRespDataValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e SearchUsersRespDataValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e SearchUsersRespDataValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e SearchUsersRespDataValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e SearchUsersRespDataValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e SearchUsersRespDataValidationError) ErrorName() string {
	return "SearchUsersRespDataValidationError"
}

// Error satisfies the builtin error interface
func (e SearchUsersRespDataValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sSearchUsersRespData.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = SearchUsersRespDataValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = SearchUsersRespDataValidationError{}

// Validate checks the field values on FollowUserReq with the rules defined in
// the proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *FollowUserReq) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on FollowUserReq with the rules defined
// in the proto definition for this message. If any rules are violated, the
// result is a list of violation errors wrapped in FollowUserReqMultiError, or
// nil if none found.
func (m *FollowUserReq) ValidateAll() error {
	return m.validate(true)
}

func (m *FollowUserReq) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if m.GetFollowerId() <= 0 {
		err := FollowUserReqValidationError{
			field:  "FollowerId",
			reason: "value must be greater than 0",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if m.GetFollowingId() <= 0 {
		err := FollowUserReqValidationError{
			field:  "FollowingId",
			reason: "value must be greater than 0",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if len(errors) > 0 {
		return FollowUserReqMultiError(errors)
	}

	return nil
}

// FollowUserReqMultiError is an error wrapping multiple validation errors
// returned by FollowUserReq.ValidateAll() if the designated constraints
// aren't met.
type FollowUserReqMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m FollowUserReqMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m FollowUserReqMultiError) AllErrors() []error { return m }

// FollowUserReqValidationError is the validation error returned by
// FollowUserReq.Validate if the designated constraints aren't met.
type FollowUserReqValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e FollowUserReqValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e FollowUserReqValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e FollowUserReqValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e FollowUserReqValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e FollowUserReqValidationError) ErrorName() string { return "FollowUserReqValidationError" }

// Error satisfies the builtin error interface
func (e FollowUserReqValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sFollowUserReq.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = FollowUserReqValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = FollowUserReqValidationError{}

// Validate checks the field values on FollowUserResp with the rules defined in
// the proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *FollowUserResp) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on FollowUserResp with the rules defined
// in the proto definition for this message. If any rules are violated, the
// result is a list of violation errors wrapped in FollowUserRespMultiError,
// or nil if none found.
func (m *FollowUserResp) ValidateAll() error {
	return m.validate(true)
}

func (m *FollowUserResp) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetBase()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, FollowUserRespValidationError{
					field:  "Base",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, FollowUserRespValidationError{
					field:  "Base",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetBase()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return FollowUserRespValidationError{
				field:  "Base",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return FollowUserRespMultiError(errors)
	}

	return nil
}

// FollowUserRespMultiError is an error wrapping multiple validation errors
// returned by FollowUserResp.ValidateAll() if the designated constraints
// aren't met.
type FollowUserRespMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m FollowUserRespMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m FollowUserRespMultiError) AllErrors() []error { return m }

// FollowUserRespValidationError is the validation error returned by
// FollowUserResp.Validate if the designated constraints aren't met.
type FollowUserRespValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e FollowUserRespValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e FollowUserRespValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e FollowUserRespValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e FollowUserRespValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e FollowUserRespValidationError) ErrorName() string { return "FollowUserRespValidationError" }

// Error satisfies the builtin error interface
func (e FollowUserRespValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sFollowUserResp.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = FollowUserRespValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = FollowUserRespValidationError{}

// Validate checks the field values on UnfollowUserReq with the rules defined
// in the proto definition for this message. If any rules are violated, the
// first error encountered is returned, or nil if there are no violations.
func (m *UnfollowUserReq) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on UnfollowUserReq with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// UnfollowUserReqMultiError, or nil if none found.
func (m *UnfollowUserReq) ValidateAll() error {
	return m.validate(true)
}

func (m *UnfollowUserReq) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if m.GetFollowerId() <= 0 {
		err := UnfollowUserReqValidationError{
			field:  "FollowerId",
			reason: "value must be greater than 0",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if m.GetFollowingId() <= 0 {
		err := UnfollowUserReqValidationError{
			field:  "FollowingId",
			reason: "value must be greater than 0",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if len(errors) > 0 {
		return UnfollowUserReqMultiError(errors)
	}

	return nil
}

// UnfollowUserReqMultiError is an error wrapping multiple validation errors
// returned by UnfollowUserReq.ValidateAll() if the designated constraints
// aren't met.
type UnfollowUserReqMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m UnfollowUserReqMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m UnfollowUserReqMultiError) AllErrors() []error { return m }

// UnfollowUserReqValidationError is the validation error returned by
// UnfollowUserReq.Validate if the designated constraints aren't met.
type UnfollowUserReqValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e UnfollowUserReqValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e UnfollowUserReqValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e UnfollowUserReqValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e UnfollowUserReqValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e UnfollowUserReqValidationError) ErrorName() string { return "UnfollowUserReqValidationError" }

// Error satisfies the builtin error interface
func (e UnfollowUserReqValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sUnfollowUserReq.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = UnfollowUserReqValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = UnfollowUserReqValidationError{}

// Validate checks the field values on UnfollowUserResp with the rules defined
// in the proto definition for this message. If any rules are violated, the
// first error encountered is returned, or nil if there are no violations.
func (m *UnfollowUserResp) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on UnfollowUserResp with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// UnfollowUserRespMultiError, or nil if none found.
func (m *UnfollowUserResp) ValidateAll() error {
	return m.validate(true)
}

func (m *UnfollowUserResp) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetBase()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, UnfollowUserRespValidationError{
					field:  "Base",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, UnfollowUserRespValidationError{
					field:  "Base",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetBase()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return UnfollowUserRespValidationError{
				field:  "Base",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return UnfollowUserRespMultiError(errors)
	}

	return nil
}

// UnfollowUserRespMultiError is an error wrapping multiple validation errors
// returned by UnfollowUserResp.ValidateAll() if the designated constraints
// aren't met.
type UnfollowUserRespMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m UnfollowUserRespMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m UnfollowUserRespMultiError) AllErrors() []error { return m }

// UnfollowUserRespValidationError is the validation error returned by
// UnfollowUserResp.Validate if the designated constraints aren't met.
type UnfollowUserRespValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e UnfollowUserRespValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e UnfollowUserRespValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e UnfollowUserRespValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e UnfollowUserRespValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e UnfollowUserRespValidationError) ErrorName() string { return "UnfollowUserRespValidationError" }

// Error satisfies the builtin error interface
func (e UnfollowUserRespValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sUnfollowUserResp.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = UnfollowUserRespValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = UnfollowUserRespValidationError{}

// Validate checks the field values on CheckFollowingReq with the rules defined
// in the proto definition for this message. If any rules are violated, the
// first error encountered is returned, or nil if there are no violations.
func (m *CheckFollowingReq) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on CheckFollowingReq with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// CheckFollowingReqMultiError, or nil if none found.
func (m *CheckFollowingReq) ValidateAll() error {
	return m.validate(true)
}

func (m *CheckFollowingReq) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if m.GetFollowerId() <= 0 {
		err := CheckFollowingReqValidationError{
			field:  "FollowerId",
			reason: "value must be greater than 0",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if m.GetFollowingId() <= 0 {
		err := CheckFollowingReqValidationError{
			field:  "FollowingId",
			reason: "value must be greater than 0",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if len(errors) > 0 {
		return CheckFollowingReqMultiError(errors)
	}

	return nil
}

// CheckFollowingReqMultiError is an error wrapping multiple validation errors
// returned by CheckFollowingReq.ValidateAll() if the designated constraints
// aren't met.
type CheckFollowingReqMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m CheckFollowingReqMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m CheckFollowingReqMultiError) AllErrors() []error { return m }

// CheckFollowingReqValidationError is the validation error returned by
// CheckFollowingReq.Validate if the designated constraints aren't met.
type CheckFollowingReqValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e CheckFollowingReqValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e CheckFollowingReqValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e CheckFollowingReqValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e CheckFollowingReqValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e CheckFollowingReqValidationError) ErrorName() string {
	return "CheckFollowingReqValidationError"
}

// Error satisfies the builtin error interface
func (e CheckFollowingReqValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sCheckFollowingReq.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = CheckFollowingReqValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = CheckFollowingReqValidationError{}

// Validate checks the field values on CheckFollowingResp with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *CheckFollowingResp) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on CheckFollowingResp with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// CheckFollowingRespMultiError, or nil if none found.
func (m *CheckFollowingResp) ValidateAll() error {
	return m.validate(true)
}

func (m *CheckFollowingResp) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetBase()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, CheckFollowingRespValidationError{
					field:  "Base",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, CheckFollowingRespValidationError{
					field:  "Base",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetBase()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return CheckFollowingRespValidationError{
				field:  "Base",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for IsFollowing

	if len(errors) > 0 {
		return CheckFollowingRespMultiError(errors)
	}

	return nil
}

// CheckFollowingRespMultiError is an error wrapping multiple validation errors
// returned by CheckFollowingResp.ValidateAll() if the designated constraints
// aren't met.
type CheckFollowingRespMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m CheckFollowingRespMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m CheckFollowingRespMultiError) AllErrors() []error { return m }

// CheckFollowingRespValidationError is the validation error returned by
// CheckFollowingResp.Validate if the designated constraints aren't met.
type CheckFollowingRespValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e CheckFollowingRespValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e CheckFollowingRespValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e CheckFollowingRespValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e CheckFollowingRespValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e CheckFollowingRespValidationError) ErrorName() string {
	return "CheckFollowingRespValidationError"
}

// Error satisfies the builtin error interface
func (e CheckFollowingRespValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sCheckFollowingResp.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = CheckFollowingRespValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = CheckFollowingRespValidationError{}

// Validate checks the field values on GetFollowingUsersReq with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *GetFollowingUsersReq) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GetFollowingUsersReq with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// GetFollowingUsersReqMultiError, or nil if none found.
func (m *GetFollowingUsersReq) ValidateAll() error {
	return m.validate(true)
}

func (m *GetFollowingUsersReq) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if m.GetUserId() <= 0 {
		err := GetFollowingUsersReqValidationError{
			field:  "UserId",
			reason: "value must be greater than 0",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	// no validation rules for Page

	// no validation rules for PageSize

	if len(errors) > 0 {
		return GetFollowingUsersReqMultiError(errors)
	}

	return nil
}

// GetFollowingUsersReqMultiError is an error wrapping multiple validation
// errors returned by GetFollowingUsersReq.ValidateAll() if the designated
// constraints aren't met.
type GetFollowingUsersReqMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetFollowingUsersReqMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetFollowingUsersReqMultiError) AllErrors() []error { return m }

// GetFollowingUsersReqValidationError is the validation error returned by
// GetFollowingUsersReq.Validate if the designated constraints aren't met.
type GetFollowingUsersReqValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetFollowingUsersReqValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetFollowingUsersReqValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetFollowingUsersReqValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetFollowingUsersReqValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetFollowingUsersReqValidationError) ErrorName() string {
	return "GetFollowingUsersReqValidationError"
}

// Error satisfies the builtin error interface
func (e GetFollowingUsersReqValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetFollowingUsersReq.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetFollowingUsersReqValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetFollowingUsersReqValidationError{}

// Validate checks the field values on GetFollowingUsersResp with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *GetFollowingUsersResp) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GetFollowingUsersResp with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// GetFollowingUsersRespMultiError, or nil if none found.
func (m *GetFollowingUsersResp) ValidateAll() error {
	return m.validate(true)
}

func (m *GetFollowingUsersResp) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetBase()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetFollowingUsersRespValidationError{
					field:  "Base",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetFollowingUsersRespValidationError{
					field:  "Base",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetBase()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetFollowingUsersRespValidationError{
				field:  "Base",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetData()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetFollowingUsersRespValidationError{
					field:  "Data",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetFollowingUsersRespValidationError{
					field:  "Data",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetData()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetFollowingUsersRespValidationError{
				field:  "Data",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return GetFollowingUsersRespMultiError(errors)
	}

	return nil
}

// GetFollowingUsersRespMultiError is an error wrapping multiple validation
// errors returned by GetFollowingUsersResp.ValidateAll() if the designated
// constraints aren't met.
type GetFollowingUsersRespMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetFollowingUsersRespMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetFollowingUsersRespMultiError) AllErrors() []error { return m }

// GetFollowingUsersRespValidationError is the validation error returned by
// GetFollowingUsersResp.Validate if the designated constraints aren't met.
type GetFollowingUsersRespValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetFollowingUsersRespValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetFollowingUsersRespValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetFollowingUsersRespValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetFollowingUsersRespValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetFollowingUsersRespValidationError) ErrorName() string {
	return "GetFollowingUsersRespValidationError"
}

// Error satisfies the builtin error interface
func (e GetFollowingUsersRespValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetFollowingUsersResp.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetFollowingUsersRespValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetFollowingUsersRespValidationError{}

// Validate checks the field values on GetFollowingUsersRespData with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *GetFollowingUsersRespData) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GetFollowingUsersRespData with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// GetFollowingUsersRespDataMultiError, or nil if none found.
func (m *GetFollowingUsersRespData) ValidateAll() error {
	return m.validate(true)
}

func (m *GetFollowingUsersRespData) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	for idx, item := range m.GetUsers() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, GetFollowingUsersRespDataValidationError{
						field:  fmt.Sprintf("Users[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, GetFollowingUsersRespDataValidationError{
						field:  fmt.Sprintf("Users[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return GetFollowingUsersRespDataValidationError{
					field:  fmt.Sprintf("Users[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	// no validation rules for Total

	if len(errors) > 0 {
		return GetFollowingUsersRespDataMultiError(errors)
	}

	return nil
}

// GetFollowingUsersRespDataMultiError is an error wrapping multiple validation
// errors returned by GetFollowingUsersRespData.ValidateAll() if the
// designated constraints aren't met.
type GetFollowingUsersRespDataMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetFollowingUsersRespDataMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetFollowingUsersRespDataMultiError) AllErrors() []error { return m }

// GetFollowingUsersRespDataValidationError is the validation error returned by
// GetFollowingUsersRespData.Validate if the designated constraints aren't met.
type GetFollowingUsersRespDataValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetFollowingUsersRespDataValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetFollowingUsersRespDataValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetFollowingUsersRespDataValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetFollowingUsersRespDataValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetFollowingUsersRespDataValidationError) ErrorName() string {
	return "GetFollowingUsersRespDataValidationError"
}

// Error satisfies the builtin error interface
func (e GetFollowingUsersRespDataValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetFollowingUsersRespData.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetFollowingUsersRespDataValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetFollowingUsersRespDataValidationError{}

// Validate checks the field values on GetFollowersReq with the rules defined
// in the proto definition for this message. If any rules are violated, the
// first error encountered is returned, or nil if there are no violations.
func (m *GetFollowersReq) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GetFollowersReq with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// GetFollowersReqMultiError, or nil if none found.
func (m *GetFollowersReq) ValidateAll() error {
	return m.validate(true)
}

func (m *GetFollowersReq) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if m.GetUserId() <= 0 {
		err := GetFollowersReqValidationError{
			field:  "UserId",
			reason: "value must be greater than 0",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	// no validation rules for Page

	// no validation rules for PageSize

	if len(errors) > 0 {
		return GetFollowersReqMultiError(errors)
	}

	return nil
}

// GetFollowersReqMultiError is an error wrapping multiple validation errors
// returned by GetFollowersReq.ValidateAll() if the designated constraints
// aren't met.
type GetFollowersReqMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetFollowersReqMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetFollowersReqMultiError) AllErrors() []error { return m }

// GetFollowersReqValidationError is the validation error returned by
// GetFollowersReq.Validate if the designated constraints aren't met.
type GetFollowersReqValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetFollowersReqValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetFollowersReqValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetFollowersReqValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetFollowersReqValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetFollowersReqValidationError) ErrorName() string { return "GetFollowersReqValidationError" }

// Error satisfies the builtin error interface
func (e GetFollowersReqValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetFollowersReq.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetFollowersReqValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetFollowersReqValidationError{}

// Validate checks the field values on GetFollowersResp with the rules defined
// in the proto definition for this message. If any rules are violated, the
// first error encountered is returned, or nil if there are no violations.
func (m *GetFollowersResp) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GetFollowersResp with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// GetFollowersRespMultiError, or nil if none found.
func (m *GetFollowersResp) ValidateAll() error {
	return m.validate(true)
}

func (m *GetFollowersResp) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetBase()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetFollowersRespValidationError{
					field:  "Base",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetFollowersRespValidationError{
					field:  "Base",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetBase()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetFollowersRespValidationError{
				field:  "Base",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetData()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetFollowersRespValidationError{
					field:  "Data",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetFollowersRespValidationError{
					field:  "Data",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetData()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetFollowersRespValidationError{
				field:  "Data",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return GetFollowersRespMultiError(errors)
	}

	return nil
}

// GetFollowersRespMultiError is an error wrapping multiple validation errors
// returned by GetFollowersResp.ValidateAll() if the designated constraints
// aren't met.
type GetFollowersRespMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetFollowersRespMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetFollowersRespMultiError) AllErrors() []error { return m }

// GetFollowersRespValidationError is the validation error returned by
// GetFollowersResp.Validate if the designated constraints aren't met.
type GetFollowersRespValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetFollowersRespValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetFollowersRespValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetFollowersRespValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetFollowersRespValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetFollowersRespValidationError) ErrorName() string { return "GetFollowersRespValidationError" }

// Error satisfies the builtin error interface
func (e GetFollowersRespValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetFollowersResp.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetFollowersRespValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetFollowersRespValidationError{}

// Validate checks the field values on GetFollowersRespData with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *GetFollowersRespData) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GetFollowersRespData with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// GetFollowersRespDataMultiError, or nil if none found.
func (m *GetFollowersRespData) ValidateAll() error {
	return m.validate(true)
}

func (m *GetFollowersRespData) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	for idx, item := range m.GetUsers() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, GetFollowersRespDataValidationError{
						field:  fmt.Sprintf("Users[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, GetFollowersRespDataValidationError{
						field:  fmt.Sprintf("Users[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return GetFollowersRespDataValidationError{
					field:  fmt.Sprintf("Users[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	// no validation rules for Total

	if len(errors) > 0 {
		return GetFollowersRespDataMultiError(errors)
	}

	return nil
}

// GetFollowersRespDataMultiError is an error wrapping multiple validation
// errors returned by GetFollowersRespData.ValidateAll() if the designated
// constraints aren't met.
type GetFollowersRespDataMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetFollowersRespDataMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetFollowersRespDataMultiError) AllErrors() []error { return m }

// GetFollowersRespDataValidationError is the validation error returned by
// GetFollowersRespData.Validate if the designated constraints aren't met.
type GetFollowersRespDataValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetFollowersRespDataValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetFollowersRespDataValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetFollowersRespDataValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetFollowersRespDataValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetFollowersRespDataValidationError) ErrorName() string {
	return "GetFollowersRespDataValidationError"
}

// Error satisfies the builtin error interface
func (e GetFollowersRespDataValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetFollowersRespData.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetFollowersRespDataValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetFollowersRespDataValidationError{}

// Validate checks the field values on GetFollowingUserIDsReq with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *GetFollowingUserIDsReq) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GetFollowingUserIDsReq with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// GetFollowingUserIDsReqMultiError, or nil if none found.
func (m *GetFollowingUserIDsReq) ValidateAll() error {
	return m.validate(true)
}

func (m *GetFollowingUserIDsReq) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if m.GetUserId() <= 0 {
		err := GetFollowingUserIDsReqValidationError{
			field:  "UserId",
			reason: "value must be greater than 0",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if len(errors) > 0 {
		return GetFollowingUserIDsReqMultiError(errors)
	}

	return nil
}

// GetFollowingUserIDsReqMultiError is an error wrapping multiple validation
// errors returned by GetFollowingUserIDsReq.ValidateAll() if the designated
// constraints aren't met.
type GetFollowingUserIDsReqMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetFollowingUserIDsReqMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetFollowingUserIDsReqMultiError) AllErrors() []error { return m }

// GetFollowingUserIDsReqValidationError is the validation error returned by
// GetFollowingUserIDsReq.Validate if the designated constraints aren't met.
type GetFollowingUserIDsReqValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetFollowingUserIDsReqValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetFollowingUserIDsReqValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetFollowingUserIDsReqValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetFollowingUserIDsReqValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetFollowingUserIDsReqValidationError) ErrorName() string {
	return "GetFollowingUserIDsReqValidationError"
}

// Error satisfies the builtin error interface
func (e GetFollowingUserIDsReqValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetFollowingUserIDsReq.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetFollowingUserIDsReqValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetFollowingUserIDsReqValidationError{}

// Validate checks the field values on GetFollowingUserIDsResp with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *GetFollowingUserIDsResp) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GetFollowingUserIDsResp with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// GetFollowingUserIDsRespMultiError, or nil if none found.
func (m *GetFollowingUserIDsResp) ValidateAll() error {
	return m.validate(true)
}

func (m *GetFollowingUserIDsResp) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetBase()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetFollowingUserIDsRespValidationError{
					field:  "Base",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetFollowingUserIDsRespValidationError{
					field:  "Base",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetBase()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetFollowingUserIDsRespValidationError{
				field:  "Base",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetData()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetFollowingUserIDsRespValidationError{
					field:  "Data",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetFollowingUserIDsRespValidationError{
					field:  "Data",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetData()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetFollowingUserIDsRespValidationError{
				field:  "Data",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return GetFollowingUserIDsRespMultiError(errors)
	}

	return nil
}

// GetFollowingUserIDsRespMultiError is an error wrapping multiple validation
// errors returned by GetFollowingUserIDsResp.ValidateAll() if the designated
// constraints aren't met.
type GetFollowingUserIDsRespMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetFollowingUserIDsRespMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetFollowingUserIDsRespMultiError) AllErrors() []error { return m }

// GetFollowingUserIDsRespValidationError is the validation error returned by
// GetFollowingUserIDsResp.Validate if the designated constraints aren't met.
type GetFollowingUserIDsRespValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetFollowingUserIDsRespValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetFollowingUserIDsRespValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetFollowingUserIDsRespValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetFollowingUserIDsRespValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetFollowingUserIDsRespValidationError) ErrorName() string {
	return "GetFollowingUserIDsRespValidationError"
}

// Error satisfies the builtin error interface
func (e GetFollowingUserIDsRespValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetFollowingUserIDsResp.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetFollowingUserIDsRespValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetFollowingUserIDsRespValidationError{}

// Validate checks the field values on GetFollowingUserIDsRespData with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *GetFollowingUserIDsRespData) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GetFollowingUserIDsRespData with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// GetFollowingUserIDsRespDataMultiError, or nil if none found.
func (m *GetFollowingUserIDsRespData) ValidateAll() error {
	return m.validate(true)
}

func (m *GetFollowingUserIDsRespData) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if len(errors) > 0 {
		return GetFollowingUserIDsRespDataMultiError(errors)
	}

	return nil
}

// GetFollowingUserIDsRespDataMultiError is an error wrapping multiple
// validation errors returned by GetFollowingUserIDsRespData.ValidateAll() if
// the designated constraints aren't met.
type GetFollowingUserIDsRespDataMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetFollowingUserIDsRespDataMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetFollowingUserIDsRespDataMultiError) AllErrors() []error { return m }

// GetFollowingUserIDsRespDataValidationError is the validation error returned
// by GetFollowingUserIDsRespData.Validate if the designated constraints
// aren't met.
type GetFollowingUserIDsRespDataValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetFollowingUserIDsRespDataValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetFollowingUserIDsRespDataValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetFollowingUserIDsRespDataValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetFollowingUserIDsRespDataValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetFollowingUserIDsRespDataValidationError) ErrorName() string {
	return "GetFollowingUserIDsRespDataValidationError"
}

// Error satisfies the builtin error interface
func (e GetFollowingUserIDsRespDataValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetFollowingUserIDsRespData.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetFollowingUserIDsRespDataValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetFollowingUserIDsRespDataValidationError{}

// Validate checks the field values on BatchCheckFollowingReq with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *BatchCheckFollowingReq) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on BatchCheckFollowingReq with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// BatchCheckFollowingReqMultiError, or nil if none found.
func (m *BatchCheckFollowingReq) ValidateAll() error {
	return m.validate(true)
}

func (m *BatchCheckFollowingReq) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if m.GetFollowerId() <= 0 {
		err := BatchCheckFollowingReqValidationError{
			field:  "FollowerId",
			reason: "value must be greater than 0",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if l := len(m.GetFollowingIds()); l < 1 || l > 1000 {
		err := BatchCheckFollowingReqValidationError{
			field:  "FollowingIds",
			reason: "value must contain between 1 and 1000 items, inclusive",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if len(errors) > 0 {
		return BatchCheckFollowingReqMultiError(errors)
	}

	return nil
}

// BatchCheckFollowingReqMultiError is an error wrapping multiple validation
// errors returned by BatchCheckFollowingReq.ValidateAll() if the designated
// constraints aren't met.
type BatchCheckFollowingReqMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m BatchCheckFollowingReqMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m BatchCheckFollowingReqMultiError) AllErrors() []error { return m }

// BatchCheckFollowingReqValidationError is the validation error returned by
// BatchCheckFollowingReq.Validate if the designated constraints aren't met.
type BatchCheckFollowingReqValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e BatchCheckFollowingReqValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e BatchCheckFollowingReqValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e BatchCheckFollowingReqValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e BatchCheckFollowingReqValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e BatchCheckFollowingReqValidationError) ErrorName() string {
	return "BatchCheckFollowingReqValidationError"
}

// Error satisfies the builtin error interface
func (e BatchCheckFollowingReqValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sBatchCheckFollowingReq.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = BatchCheckFollowingReqValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = BatchCheckFollowingReqValidationError{}

// Validate checks the field values on BatchCheckFollowingResp with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *BatchCheckFollowingResp) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on BatchCheckFollowingResp with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// BatchCheckFollowingRespMultiError, or nil if none found.
func (m *BatchCheckFollowingResp) ValidateAll() error {
	return m.validate(true)
}

func (m *BatchCheckFollowingResp) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetBase()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, BatchCheckFollowingRespValidationError{
					field:  "Base",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, BatchCheckFollowingRespValidationError{
					field:  "Base",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetBase()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return BatchCheckFollowingRespValidationError{
				field:  "Base",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetData()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, BatchCheckFollowingRespValidationError{
					field:  "Data",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, BatchCheckFollowingRespValidationError{
					field:  "Data",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetData()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return BatchCheckFollowingRespValidationError{
				field:  "Data",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return BatchCheckFollowingRespMultiError(errors)
	}

	return nil
}

// BatchCheckFollowingRespMultiError is an error wrapping multiple validation
// errors returned by BatchCheckFollowingResp.ValidateAll() if the designated
// constraints aren't met.
type BatchCheckFollowingRespMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m BatchCheckFollowingRespMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m BatchCheckFollowingRespMultiError) AllErrors() []error { return m }

// BatchCheckFollowingRespValidationError is the validation error returned by
// BatchCheckFollowingResp.Validate if the designated constraints aren't met.
type BatchCheckFollowingRespValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e BatchCheckFollowingRespValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e BatchCheckFollowingRespValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e BatchCheckFollowingRespValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e BatchCheckFollowingRespValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e BatchCheckFollowingRespValidationError) ErrorName() string {
	return "BatchCheckFollowingRespValidationError"
}

// Error satisfies the builtin error interface
func (e BatchCheckFollowingRespValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sBatchCheckFollowingResp.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = BatchCheckFollowingRespValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = BatchCheckFollowingRespValidationError{}

// Validate checks the field values on FollowDataItem with the rules defined in
// the proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *FollowDataItem) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on FollowDataItem with the rules defined
// in the proto definition for this message. If any rules are violated, the
// result is a list of violation errors wrapped in FollowDataItemMultiError,
// or nil if none found.
func (m *FollowDataItem) ValidateAll() error {
	return m.validate(true)
}

func (m *FollowDataItem) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	{
		sorted_keys := make([]int64, len(m.GetItems()))
		i := 0
		for key := range m.GetItems() {
			sorted_keys[i] = key
			i++
		}
		sort.Slice(sorted_keys, func(i, j int) bool { return sorted_keys[i] < sorted_keys[j] })
		for _, key := range sorted_keys {
			val := m.GetItems()[key]
			_ = val

			// no validation rules for Items[key]

			if all {
				switch v := interface{}(val).(type) {
				case interface{ ValidateAll() error }:
					if err := v.ValidateAll(); err != nil {
						errors = append(errors, FollowDataItemValidationError{
							field:  fmt.Sprintf("Items[%v]", key),
							reason: "embedded message failed validation",
							cause:  err,
						})
					}
				case interface{ Validate() error }:
					if err := v.Validate(); err != nil {
						errors = append(errors, FollowDataItemValidationError{
							field:  fmt.Sprintf("Items[%v]", key),
							reason: "embedded message failed validation",
							cause:  err,
						})
					}
				}
			} else if v, ok := interface{}(val).(interface{ Validate() error }); ok {
				if err := v.Validate(); err != nil {
					return FollowDataItemValidationError{
						field:  fmt.Sprintf("Items[%v]", key),
						reason: "embedded message failed validation",
						cause:  err,
					}
				}
			}

		}
	}

	if len(errors) > 0 {
		return FollowDataItemMultiError(errors)
	}

	return nil
}

// FollowDataItemMultiError is an error wrapping multiple validation errors
// returned by FollowDataItem.ValidateAll() if the designated constraints
// aren't met.
type FollowDataItemMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m FollowDataItemMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m FollowDataItemMultiError) AllErrors() []error { return m }

// FollowDataItemValidationError is the validation error returned by
// FollowDataItem.Validate if the designated constraints aren't met.
type FollowDataItemValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e FollowDataItemValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e FollowDataItemValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e FollowDataItemValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e FollowDataItemValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e FollowDataItemValidationError) ErrorName() string { return "FollowDataItemValidationError" }

// Error satisfies the builtin error interface
func (e FollowDataItemValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sFollowDataItem.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = FollowDataItemValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = FollowDataItemValidationError{}

// Validate checks the field values on ReviewCallbackReq with the rules defined
// in the proto definition for this message. If any rules are violated, the
// first error encountered is returned, or nil if there are no violations.
func (m *ReviewCallbackReq) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on ReviewCallbackReq with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// ReviewCallbackReqMultiError, or nil if none found.
func (m *ReviewCallbackReq) ValidateAll() error {
	return m.validate(true)
}

func (m *ReviewCallbackReq) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Userid

	// no validation rules for Id

	// no validation rules for Type

	// no validation rules for Result

	// no validation rules for Reason

	if len(errors) > 0 {
		return ReviewCallbackReqMultiError(errors)
	}

	return nil
}

// ReviewCallbackReqMultiError is an error wrapping multiple validation errors
// returned by ReviewCallbackReq.ValidateAll() if the designated constraints
// aren't met.
type ReviewCallbackReqMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m ReviewCallbackReqMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m ReviewCallbackReqMultiError) AllErrors() []error { return m }

// ReviewCallbackReqValidationError is the validation error returned by
// ReviewCallbackReq.Validate if the designated constraints aren't met.
type ReviewCallbackReqValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e ReviewCallbackReqValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e ReviewCallbackReqValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e ReviewCallbackReqValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e ReviewCallbackReqValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e ReviewCallbackReqValidationError) ErrorName() string {
	return "ReviewCallbackReqValidationError"
}

// Error satisfies the builtin error interface
func (e ReviewCallbackReqValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sReviewCallbackReq.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = ReviewCallbackReqValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = ReviewCallbackReqValidationError{}

// Validate checks the field values on IsPhoneWhitelistReq with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *IsPhoneWhitelistReq) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on IsPhoneWhitelistReq with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// IsPhoneWhitelistReqMultiError, or nil if none found.
func (m *IsPhoneWhitelistReq) ValidateAll() error {
	return m.validate(true)
}

func (m *IsPhoneWhitelistReq) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if l := len(m.GetPhoneNumber()); l < 1 || l > 11 {
		err := IsPhoneWhitelistReqValidationError{
			field:  "PhoneNumber",
			reason: "value length must be between 1 and 11 bytes, inclusive",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if len(errors) > 0 {
		return IsPhoneWhitelistReqMultiError(errors)
	}

	return nil
}

// IsPhoneWhitelistReqMultiError is an error wrapping multiple validation
// errors returned by IsPhoneWhitelistReq.ValidateAll() if the designated
// constraints aren't met.
type IsPhoneWhitelistReqMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m IsPhoneWhitelistReqMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m IsPhoneWhitelistReqMultiError) AllErrors() []error { return m }

// IsPhoneWhitelistReqValidationError is the validation error returned by
// IsPhoneWhitelistReq.Validate if the designated constraints aren't met.
type IsPhoneWhitelistReqValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e IsPhoneWhitelistReqValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e IsPhoneWhitelistReqValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e IsPhoneWhitelistReqValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e IsPhoneWhitelistReqValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e IsPhoneWhitelistReqValidationError) ErrorName() string {
	return "IsPhoneWhitelistReqValidationError"
}

// Error satisfies the builtin error interface
func (e IsPhoneWhitelistReqValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sIsPhoneWhitelistReq.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = IsPhoneWhitelistReqValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = IsPhoneWhitelistReqValidationError{}

// Validate checks the field values on IsPhoneWhitelistResp with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *IsPhoneWhitelistResp) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on IsPhoneWhitelistResp with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// IsPhoneWhitelistRespMultiError, or nil if none found.
func (m *IsPhoneWhitelistResp) ValidateAll() error {
	return m.validate(true)
}

func (m *IsPhoneWhitelistResp) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetBase()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, IsPhoneWhitelistRespValidationError{
					field:  "Base",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, IsPhoneWhitelistRespValidationError{
					field:  "Base",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetBase()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return IsPhoneWhitelistRespValidationError{
				field:  "Base",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for IsWhitelist

	if len(errors) > 0 {
		return IsPhoneWhitelistRespMultiError(errors)
	}

	return nil
}

// IsPhoneWhitelistRespMultiError is an error wrapping multiple validation
// errors returned by IsPhoneWhitelistResp.ValidateAll() if the designated
// constraints aren't met.
type IsPhoneWhitelistRespMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m IsPhoneWhitelistRespMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m IsPhoneWhitelistRespMultiError) AllErrors() []error { return m }

// IsPhoneWhitelistRespValidationError is the validation error returned by
// IsPhoneWhitelistResp.Validate if the designated constraints aren't met.
type IsPhoneWhitelistRespValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e IsPhoneWhitelistRespValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e IsPhoneWhitelistRespValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e IsPhoneWhitelistRespValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e IsPhoneWhitelistRespValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e IsPhoneWhitelistRespValidationError) ErrorName() string {
	return "IsPhoneWhitelistRespValidationError"
}

// Error satisfies the builtin error interface
func (e IsPhoneWhitelistRespValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sIsPhoneWhitelistResp.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = IsPhoneWhitelistRespValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = IsPhoneWhitelistRespValidationError{}

// Validate checks the field values on GetLoginStatusReq with the rules defined
// in the proto definition for this message. If any rules are violated, the
// first error encountered is returned, or nil if there are no violations.
func (m *GetLoginStatusReq) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GetLoginStatusReq with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// GetLoginStatusReqMultiError, or nil if none found.
func (m *GetLoginStatusReq) ValidateAll() error {
	return m.validate(true)
}

func (m *GetLoginStatusReq) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if len(errors) > 0 {
		return GetLoginStatusReqMultiError(errors)
	}

	return nil
}

// GetLoginStatusReqMultiError is an error wrapping multiple validation errors
// returned by GetLoginStatusReq.ValidateAll() if the designated constraints
// aren't met.
type GetLoginStatusReqMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetLoginStatusReqMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetLoginStatusReqMultiError) AllErrors() []error { return m }

// GetLoginStatusReqValidationError is the validation error returned by
// GetLoginStatusReq.Validate if the designated constraints aren't met.
type GetLoginStatusReqValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetLoginStatusReqValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetLoginStatusReqValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetLoginStatusReqValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetLoginStatusReqValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetLoginStatusReqValidationError) ErrorName() string {
	return "GetLoginStatusReqValidationError"
}

// Error satisfies the builtin error interface
func (e GetLoginStatusReqValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetLoginStatusReq.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetLoginStatusReqValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetLoginStatusReqValidationError{}

// Validate checks the field values on LoginStatusInfo with the rules defined
// in the proto definition for this message. If any rules are violated, the
// first error encountered is returned, or nil if there are no violations.
func (m *LoginStatusInfo) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on LoginStatusInfo with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// LoginStatusInfoMultiError, or nil if none found.
func (m *LoginStatusInfo) ValidateAll() error {
	return m.validate(true)
}

func (m *LoginStatusInfo) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for DeviceId

	// no validation rules for Platform

	// no validation rules for LoginTime

	// no validation rules for LastActive

	// no validation rules for IpAddress

	if len(errors) > 0 {
		return LoginStatusInfoMultiError(errors)
	}

	return nil
}

// LoginStatusInfoMultiError is an error wrapping multiple validation errors
// returned by LoginStatusInfo.ValidateAll() if the designated constraints
// aren't met.
type LoginStatusInfoMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m LoginStatusInfoMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m LoginStatusInfoMultiError) AllErrors() []error { return m }

// LoginStatusInfoValidationError is the validation error returned by
// LoginStatusInfo.Validate if the designated constraints aren't met.
type LoginStatusInfoValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e LoginStatusInfoValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e LoginStatusInfoValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e LoginStatusInfoValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e LoginStatusInfoValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e LoginStatusInfoValidationError) ErrorName() string { return "LoginStatusInfoValidationError" }

// Error satisfies the builtin error interface
func (e LoginStatusInfoValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sLoginStatusInfo.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = LoginStatusInfoValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = LoginStatusInfoValidationError{}

// Validate checks the field values on GetLoginStatusResp with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *GetLoginStatusResp) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GetLoginStatusResp with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// GetLoginStatusRespMultiError, or nil if none found.
func (m *GetLoginStatusResp) ValidateAll() error {
	return m.validate(true)
}

func (m *GetLoginStatusResp) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetBase()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetLoginStatusRespValidationError{
					field:  "Base",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetLoginStatusRespValidationError{
					field:  "Base",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetBase()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetLoginStatusRespValidationError{
				field:  "Base",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetData()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetLoginStatusRespValidationError{
					field:  "Data",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetLoginStatusRespValidationError{
					field:  "Data",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetData()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetLoginStatusRespValidationError{
				field:  "Data",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return GetLoginStatusRespMultiError(errors)
	}

	return nil
}

// GetLoginStatusRespMultiError is an error wrapping multiple validation errors
// returned by GetLoginStatusResp.ValidateAll() if the designated constraints
// aren't met.
type GetLoginStatusRespMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetLoginStatusRespMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetLoginStatusRespMultiError) AllErrors() []error { return m }

// GetLoginStatusRespValidationError is the validation error returned by
// GetLoginStatusResp.Validate if the designated constraints aren't met.
type GetLoginStatusRespValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetLoginStatusRespValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetLoginStatusRespValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetLoginStatusRespValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetLoginStatusRespValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetLoginStatusRespValidationError) ErrorName() string {
	return "GetLoginStatusRespValidationError"
}

// Error satisfies the builtin error interface
func (e GetLoginStatusRespValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetLoginStatusResp.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetLoginStatusRespValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetLoginStatusRespValidationError{}

// Validate checks the field values on KickOffDeviceReq with the rules defined
// in the proto definition for this message. If any rules are violated, the
// first error encountered is returned, or nil if there are no violations.
func (m *KickOffDeviceReq) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on KickOffDeviceReq with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// KickOffDeviceReqMultiError, or nil if none found.
func (m *KickOffDeviceReq) ValidateAll() error {
	return m.validate(true)
}

func (m *KickOffDeviceReq) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if len(m.GetDeviceId()) < 1 {
		err := KickOffDeviceReqValidationError{
			field:  "DeviceId",
			reason: "value length must be at least 1 bytes",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if len(errors) > 0 {
		return KickOffDeviceReqMultiError(errors)
	}

	return nil
}

// KickOffDeviceReqMultiError is an error wrapping multiple validation errors
// returned by KickOffDeviceReq.ValidateAll() if the designated constraints
// aren't met.
type KickOffDeviceReqMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m KickOffDeviceReqMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m KickOffDeviceReqMultiError) AllErrors() []error { return m }

// KickOffDeviceReqValidationError is the validation error returned by
// KickOffDeviceReq.Validate if the designated constraints aren't met.
type KickOffDeviceReqValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e KickOffDeviceReqValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e KickOffDeviceReqValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e KickOffDeviceReqValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e KickOffDeviceReqValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e KickOffDeviceReqValidationError) ErrorName() string { return "KickOffDeviceReqValidationError" }

// Error satisfies the builtin error interface
func (e KickOffDeviceReqValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sKickOffDeviceReq.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = KickOffDeviceReqValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = KickOffDeviceReqValidationError{}

// Validate checks the field values on KickOffDeviceResp with the rules defined
// in the proto definition for this message. If any rules are violated, the
// first error encountered is returned, or nil if there are no violations.
func (m *KickOffDeviceResp) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on KickOffDeviceResp with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// KickOffDeviceRespMultiError, or nil if none found.
func (m *KickOffDeviceResp) ValidateAll() error {
	return m.validate(true)
}

func (m *KickOffDeviceResp) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetBase()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, KickOffDeviceRespValidationError{
					field:  "Base",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, KickOffDeviceRespValidationError{
					field:  "Base",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetBase()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return KickOffDeviceRespValidationError{
				field:  "Base",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return KickOffDeviceRespMultiError(errors)
	}

	return nil
}

// KickOffDeviceRespMultiError is an error wrapping multiple validation errors
// returned by KickOffDeviceResp.ValidateAll() if the designated constraints
// aren't met.
type KickOffDeviceRespMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m KickOffDeviceRespMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m KickOffDeviceRespMultiError) AllErrors() []error { return m }

// KickOffDeviceRespValidationError is the validation error returned by
// KickOffDeviceResp.Validate if the designated constraints aren't met.
type KickOffDeviceRespValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e KickOffDeviceRespValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e KickOffDeviceRespValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e KickOffDeviceRespValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e KickOffDeviceRespValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e KickOffDeviceRespValidationError) ErrorName() string {
	return "KickOffDeviceRespValidationError"
}

// Error satisfies the builtin error interface
func (e KickOffDeviceRespValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sKickOffDeviceResp.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = KickOffDeviceRespValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = KickOffDeviceRespValidationError{}

// Validate checks the field values on DeviceHistoryInfo with the rules defined
// in the proto definition for this message. If any rules are violated, the
// first error encountered is returned, or nil if there are no violations.
func (m *DeviceHistoryInfo) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on DeviceHistoryInfo with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// DeviceHistoryInfoMultiError, or nil if none found.
func (m *DeviceHistoryInfo) ValidateAll() error {
	return m.validate(true)
}

func (m *DeviceHistoryInfo) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for DeviceId

	// no validation rules for Platform

	// no validation rules for LoginTime

	// no validation rules for IpAddress

	if len(errors) > 0 {
		return DeviceHistoryInfoMultiError(errors)
	}

	return nil
}

// DeviceHistoryInfoMultiError is an error wrapping multiple validation errors
// returned by DeviceHistoryInfo.ValidateAll() if the designated constraints
// aren't met.
type DeviceHistoryInfoMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m DeviceHistoryInfoMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m DeviceHistoryInfoMultiError) AllErrors() []error { return m }

// DeviceHistoryInfoValidationError is the validation error returned by
// DeviceHistoryInfo.Validate if the designated constraints aren't met.
type DeviceHistoryInfoValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e DeviceHistoryInfoValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e DeviceHistoryInfoValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e DeviceHistoryInfoValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e DeviceHistoryInfoValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e DeviceHistoryInfoValidationError) ErrorName() string {
	return "DeviceHistoryInfoValidationError"
}

// Error satisfies the builtin error interface
func (e DeviceHistoryInfoValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sDeviceHistoryInfo.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = DeviceHistoryInfoValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = DeviceHistoryInfoValidationError{}

// Validate checks the field values on GetDeviceHistoryReq with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *GetDeviceHistoryReq) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GetDeviceHistoryReq with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// GetDeviceHistoryReqMultiError, or nil if none found.
func (m *GetDeviceHistoryReq) ValidateAll() error {
	return m.validate(true)
}

func (m *GetDeviceHistoryReq) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if len(errors) > 0 {
		return GetDeviceHistoryReqMultiError(errors)
	}

	return nil
}

// GetDeviceHistoryReqMultiError is an error wrapping multiple validation
// errors returned by GetDeviceHistoryReq.ValidateAll() if the designated
// constraints aren't met.
type GetDeviceHistoryReqMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetDeviceHistoryReqMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetDeviceHistoryReqMultiError) AllErrors() []error { return m }

// GetDeviceHistoryReqValidationError is the validation error returned by
// GetDeviceHistoryReq.Validate if the designated constraints aren't met.
type GetDeviceHistoryReqValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetDeviceHistoryReqValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetDeviceHistoryReqValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetDeviceHistoryReqValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetDeviceHistoryReqValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetDeviceHistoryReqValidationError) ErrorName() string {
	return "GetDeviceHistoryReqValidationError"
}

// Error satisfies the builtin error interface
func (e GetDeviceHistoryReqValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetDeviceHistoryReq.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetDeviceHistoryReqValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetDeviceHistoryReqValidationError{}

// Validate checks the field values on GetDeviceHistoryResp with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *GetDeviceHistoryResp) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GetDeviceHistoryResp with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// GetDeviceHistoryRespMultiError, or nil if none found.
func (m *GetDeviceHistoryResp) ValidateAll() error {
	return m.validate(true)
}

func (m *GetDeviceHistoryResp) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetBase()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetDeviceHistoryRespValidationError{
					field:  "Base",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetDeviceHistoryRespValidationError{
					field:  "Base",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetBase()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetDeviceHistoryRespValidationError{
				field:  "Base",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	for idx, item := range m.GetData() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, GetDeviceHistoryRespValidationError{
						field:  fmt.Sprintf("Data[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, GetDeviceHistoryRespValidationError{
						field:  fmt.Sprintf("Data[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return GetDeviceHistoryRespValidationError{
					field:  fmt.Sprintf("Data[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	if len(errors) > 0 {
		return GetDeviceHistoryRespMultiError(errors)
	}

	return nil
}

// GetDeviceHistoryRespMultiError is an error wrapping multiple validation
// errors returned by GetDeviceHistoryResp.ValidateAll() if the designated
// constraints aren't met.
type GetDeviceHistoryRespMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetDeviceHistoryRespMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetDeviceHistoryRespMultiError) AllErrors() []error { return m }

// GetDeviceHistoryRespValidationError is the validation error returned by
// GetDeviceHistoryResp.Validate if the designated constraints aren't met.
type GetDeviceHistoryRespValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetDeviceHistoryRespValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetDeviceHistoryRespValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetDeviceHistoryRespValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetDeviceHistoryRespValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetDeviceHistoryRespValidationError) ErrorName() string {
	return "GetDeviceHistoryRespValidationError"
}

// Error satisfies the builtin error interface
func (e GetDeviceHistoryRespValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetDeviceHistoryResp.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetDeviceHistoryRespValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetDeviceHistoryRespValidationError{}

// Validate checks the field values on VerifyTokenReq with the rules defined in
// the proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *VerifyTokenReq) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on VerifyTokenReq with the rules defined
// in the proto definition for this message. If any rules are violated, the
// result is a list of violation errors wrapped in VerifyTokenReqMultiError,
// or nil if none found.
func (m *VerifyTokenReq) ValidateAll() error {
	return m.validate(true)
}

func (m *VerifyTokenReq) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if len(m.GetToken()) < 1 {
		err := VerifyTokenReqValidationError{
			field:  "Token",
			reason: "value length must be at least 1 bytes",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	// no validation rules for DeviceId

	// no validation rules for Platform

	// no validation rules for UserAgent

	if len(errors) > 0 {
		return VerifyTokenReqMultiError(errors)
	}

	return nil
}

// VerifyTokenReqMultiError is an error wrapping multiple validation errors
// returned by VerifyTokenReq.ValidateAll() if the designated constraints
// aren't met.
type VerifyTokenReqMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m VerifyTokenReqMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m VerifyTokenReqMultiError) AllErrors() []error { return m }

// VerifyTokenReqValidationError is the validation error returned by
// VerifyTokenReq.Validate if the designated constraints aren't met.
type VerifyTokenReqValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e VerifyTokenReqValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e VerifyTokenReqValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e VerifyTokenReqValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e VerifyTokenReqValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e VerifyTokenReqValidationError) ErrorName() string { return "VerifyTokenReqValidationError" }

// Error satisfies the builtin error interface
func (e VerifyTokenReqValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sVerifyTokenReq.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = VerifyTokenReqValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = VerifyTokenReqValidationError{}

// Validate checks the field values on TokenVerifyInfo with the rules defined
// in the proto definition for this message. If any rules are violated, the
// first error encountered is returned, or nil if there are no violations.
func (m *TokenVerifyInfo) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on TokenVerifyInfo with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// TokenVerifyInfoMultiError, or nil if none found.
func (m *TokenVerifyInfo) ValidateAll() error {
	return m.validate(true)
}

func (m *TokenVerifyInfo) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for UserId

	// no validation rules for DeviceId

	// no validation rules for Platform

	// no validation rules for IsValid

	// no validation rules for Reason

	if len(errors) > 0 {
		return TokenVerifyInfoMultiError(errors)
	}

	return nil
}

// TokenVerifyInfoMultiError is an error wrapping multiple validation errors
// returned by TokenVerifyInfo.ValidateAll() if the designated constraints
// aren't met.
type TokenVerifyInfoMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m TokenVerifyInfoMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m TokenVerifyInfoMultiError) AllErrors() []error { return m }

// TokenVerifyInfoValidationError is the validation error returned by
// TokenVerifyInfo.Validate if the designated constraints aren't met.
type TokenVerifyInfoValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e TokenVerifyInfoValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e TokenVerifyInfoValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e TokenVerifyInfoValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e TokenVerifyInfoValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e TokenVerifyInfoValidationError) ErrorName() string { return "TokenVerifyInfoValidationError" }

// Error satisfies the builtin error interface
func (e TokenVerifyInfoValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sTokenVerifyInfo.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = TokenVerifyInfoValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = TokenVerifyInfoValidationError{}

// Validate checks the field values on VerifyTokenResp with the rules defined
// in the proto definition for this message. If any rules are violated, the
// first error encountered is returned, or nil if there are no violations.
func (m *VerifyTokenResp) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on VerifyTokenResp with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// VerifyTokenRespMultiError, or nil if none found.
func (m *VerifyTokenResp) ValidateAll() error {
	return m.validate(true)
}

func (m *VerifyTokenResp) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetBase()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, VerifyTokenRespValidationError{
					field:  "Base",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, VerifyTokenRespValidationError{
					field:  "Base",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetBase()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return VerifyTokenRespValidationError{
				field:  "Base",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetData()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, VerifyTokenRespValidationError{
					field:  "Data",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, VerifyTokenRespValidationError{
					field:  "Data",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetData()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return VerifyTokenRespValidationError{
				field:  "Data",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return VerifyTokenRespMultiError(errors)
	}

	return nil
}

// VerifyTokenRespMultiError is an error wrapping multiple validation errors
// returned by VerifyTokenResp.ValidateAll() if the designated constraints
// aren't met.
type VerifyTokenRespMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m VerifyTokenRespMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m VerifyTokenRespMultiError) AllErrors() []error { return m }

// VerifyTokenRespValidationError is the validation error returned by
// VerifyTokenResp.Validate if the designated constraints aren't met.
type VerifyTokenRespValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e VerifyTokenRespValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e VerifyTokenRespValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e VerifyTokenRespValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e VerifyTokenRespValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e VerifyTokenRespValidationError) ErrorName() string { return "VerifyTokenRespValidationError" }

// Error satisfies the builtin error interface
func (e VerifyTokenRespValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sVerifyTokenResp.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = VerifyTokenRespValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = VerifyTokenRespValidationError{}

// Validate checks the field values on ResetUserInfoReq with the rules defined
// in the proto definition for this message. If any rules are violated, the
// first error encountered is returned, or nil if there are no violations.
func (m *ResetUserInfoReq) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on ResetUserInfoReq with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// ResetUserInfoReqMultiError, or nil if none found.
func (m *ResetUserInfoReq) ValidateAll() error {
	return m.validate(true)
}

func (m *ResetUserInfoReq) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if m.GetUserId() <= 0 {
		err := ResetUserInfoReqValidationError{
			field:  "UserId",
			reason: "value must be greater than 0",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if len(errors) > 0 {
		return ResetUserInfoReqMultiError(errors)
	}

	return nil
}

// ResetUserInfoReqMultiError is an error wrapping multiple validation errors
// returned by ResetUserInfoReq.ValidateAll() if the designated constraints
// aren't met.
type ResetUserInfoReqMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m ResetUserInfoReqMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m ResetUserInfoReqMultiError) AllErrors() []error { return m }

// ResetUserInfoReqValidationError is the validation error returned by
// ResetUserInfoReq.Validate if the designated constraints aren't met.
type ResetUserInfoReqValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e ResetUserInfoReqValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e ResetUserInfoReqValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e ResetUserInfoReqValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e ResetUserInfoReqValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e ResetUserInfoReqValidationError) ErrorName() string { return "ResetUserInfoReqValidationError" }

// Error satisfies the builtin error interface
func (e ResetUserInfoReqValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sResetUserInfoReq.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = ResetUserInfoReqValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = ResetUserInfoReqValidationError{}

// Validate checks the field values on ResetUserInfoResp with the rules defined
// in the proto definition for this message. If any rules are violated, the
// first error encountered is returned, or nil if there are no violations.
func (m *ResetUserInfoResp) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on ResetUserInfoResp with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// ResetUserInfoRespMultiError, or nil if none found.
func (m *ResetUserInfoResp) ValidateAll() error {
	return m.validate(true)
}

func (m *ResetUserInfoResp) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetBase()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, ResetUserInfoRespValidationError{
					field:  "Base",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, ResetUserInfoRespValidationError{
					field:  "Base",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetBase()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return ResetUserInfoRespValidationError{
				field:  "Base",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return ResetUserInfoRespMultiError(errors)
	}

	return nil
}

// ResetUserInfoRespMultiError is an error wrapping multiple validation errors
// returned by ResetUserInfoResp.ValidateAll() if the designated constraints
// aren't met.
type ResetUserInfoRespMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m ResetUserInfoRespMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m ResetUserInfoRespMultiError) AllErrors() []error { return m }

// ResetUserInfoRespValidationError is the validation error returned by
// ResetUserInfoResp.Validate if the designated constraints aren't met.
type ResetUserInfoRespValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e ResetUserInfoRespValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e ResetUserInfoRespValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e ResetUserInfoRespValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e ResetUserInfoRespValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e ResetUserInfoRespValidationError) ErrorName() string {
	return "ResetUserInfoRespValidationError"
}

// Error satisfies the builtin error interface
func (e ResetUserInfoRespValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sResetUserInfoResp.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = ResetUserInfoRespValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = ResetUserInfoRespValidationError{}

// Validate checks the field values on BanUserReq with the rules defined in the
// proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *BanUserReq) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on BanUserReq with the rules defined in
// the proto definition for this message. If any rules are violated, the
// result is a list of violation errors wrapped in BanUserReqMultiError, or
// nil if none found.
func (m *BanUserReq) ValidateAll() error {
	return m.validate(true)
}

func (m *BanUserReq) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if m.GetUserId() <= 0 {
		err := BanUserReqValidationError{
			field:  "UserId",
			reason: "value must be greater than 0",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if len(errors) > 0 {
		return BanUserReqMultiError(errors)
	}

	return nil
}

// BanUserReqMultiError is an error wrapping multiple validation errors
// returned by BanUserReq.ValidateAll() if the designated constraints aren't met.
type BanUserReqMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m BanUserReqMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m BanUserReqMultiError) AllErrors() []error { return m }

// BanUserReqValidationError is the validation error returned by
// BanUserReq.Validate if the designated constraints aren't met.
type BanUserReqValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e BanUserReqValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e BanUserReqValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e BanUserReqValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e BanUserReqValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e BanUserReqValidationError) ErrorName() string { return "BanUserReqValidationError" }

// Error satisfies the builtin error interface
func (e BanUserReqValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sBanUserReq.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = BanUserReqValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = BanUserReqValidationError{}

// Validate checks the field values on BanUserResp with the rules defined in
// the proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *BanUserResp) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on BanUserResp with the rules defined in
// the proto definition for this message. If any rules are violated, the
// result is a list of violation errors wrapped in BanUserRespMultiError, or
// nil if none found.
func (m *BanUserResp) ValidateAll() error {
	return m.validate(true)
}

func (m *BanUserResp) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetBase()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, BanUserRespValidationError{
					field:  "Base",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, BanUserRespValidationError{
					field:  "Base",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetBase()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return BanUserRespValidationError{
				field:  "Base",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return BanUserRespMultiError(errors)
	}

	return nil
}

// BanUserRespMultiError is an error wrapping multiple validation errors
// returned by BanUserResp.ValidateAll() if the designated constraints aren't met.
type BanUserRespMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m BanUserRespMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m BanUserRespMultiError) AllErrors() []error { return m }

// BanUserRespValidationError is the validation error returned by
// BanUserResp.Validate if the designated constraints aren't met.
type BanUserRespValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e BanUserRespValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e BanUserRespValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e BanUserRespValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e BanUserRespValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e BanUserRespValidationError) ErrorName() string { return "BanUserRespValidationError" }

// Error satisfies the builtin error interface
func (e BanUserRespValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sBanUserResp.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = BanUserRespValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = BanUserRespValidationError{}

// Validate checks the field values on UnbanUserReq with the rules defined in
// the proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *UnbanUserReq) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on UnbanUserReq with the rules defined
// in the proto definition for this message. If any rules are violated, the
// result is a list of violation errors wrapped in UnbanUserReqMultiError, or
// nil if none found.
func (m *UnbanUserReq) ValidateAll() error {
	return m.validate(true)
}

func (m *UnbanUserReq) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if m.GetUserId() <= 0 {
		err := UnbanUserReqValidationError{
			field:  "UserId",
			reason: "value must be greater than 0",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if len(errors) > 0 {
		return UnbanUserReqMultiError(errors)
	}

	return nil
}

// UnbanUserReqMultiError is an error wrapping multiple validation errors
// returned by UnbanUserReq.ValidateAll() if the designated constraints aren't met.
type UnbanUserReqMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m UnbanUserReqMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m UnbanUserReqMultiError) AllErrors() []error { return m }

// UnbanUserReqValidationError is the validation error returned by
// UnbanUserReq.Validate if the designated constraints aren't met.
type UnbanUserReqValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e UnbanUserReqValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e UnbanUserReqValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e UnbanUserReqValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e UnbanUserReqValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e UnbanUserReqValidationError) ErrorName() string { return "UnbanUserReqValidationError" }

// Error satisfies the builtin error interface
func (e UnbanUserReqValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sUnbanUserReq.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = UnbanUserReqValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = UnbanUserReqValidationError{}

// Validate checks the field values on UnbanUserResp with the rules defined in
// the proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *UnbanUserResp) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on UnbanUserResp with the rules defined
// in the proto definition for this message. If any rules are violated, the
// result is a list of violation errors wrapped in UnbanUserRespMultiError, or
// nil if none found.
func (m *UnbanUserResp) ValidateAll() error {
	return m.validate(true)
}

func (m *UnbanUserResp) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetBase()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, UnbanUserRespValidationError{
					field:  "Base",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, UnbanUserRespValidationError{
					field:  "Base",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetBase()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return UnbanUserRespValidationError{
				field:  "Base",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return UnbanUserRespMultiError(errors)
	}

	return nil
}

// UnbanUserRespMultiError is an error wrapping multiple validation errors
// returned by UnbanUserResp.ValidateAll() if the designated constraints
// aren't met.
type UnbanUserRespMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m UnbanUserRespMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m UnbanUserRespMultiError) AllErrors() []error { return m }

// UnbanUserRespValidationError is the validation error returned by
// UnbanUserResp.Validate if the designated constraints aren't met.
type UnbanUserRespValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e UnbanUserRespValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e UnbanUserRespValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e UnbanUserRespValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e UnbanUserRespValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e UnbanUserRespValidationError) ErrorName() string { return "UnbanUserRespValidationError" }

// Error satisfies the builtin error interface
func (e UnbanUserRespValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sUnbanUserResp.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = UnbanUserRespValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = UnbanUserRespValidationError{}

// Validate checks the field values on AdminUpdateUserInfoReq with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *AdminUpdateUserInfoReq) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on AdminUpdateUserInfoReq with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// AdminUpdateUserInfoReqMultiError, or nil if none found.
func (m *AdminUpdateUserInfoReq) ValidateAll() error {
	return m.validate(true)
}

func (m *AdminUpdateUserInfoReq) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if m.GetUserId() <= 0 {
		err := AdminUpdateUserInfoReqValidationError{
			field:  "UserId",
			reason: "value must be greater than 0",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	// no validation rules for Avatar

	// no validation rules for Nickname

	// no validation rules for BackgroundUrl

	// no validation rules for VoiceSignatureUrl

	// no validation rules for VoiceDuration

	if len(errors) > 0 {
		return AdminUpdateUserInfoReqMultiError(errors)
	}

	return nil
}

// AdminUpdateUserInfoReqMultiError is an error wrapping multiple validation
// errors returned by AdminUpdateUserInfoReq.ValidateAll() if the designated
// constraints aren't met.
type AdminUpdateUserInfoReqMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m AdminUpdateUserInfoReqMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m AdminUpdateUserInfoReqMultiError) AllErrors() []error { return m }

// AdminUpdateUserInfoReqValidationError is the validation error returned by
// AdminUpdateUserInfoReq.Validate if the designated constraints aren't met.
type AdminUpdateUserInfoReqValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e AdminUpdateUserInfoReqValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e AdminUpdateUserInfoReqValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e AdminUpdateUserInfoReqValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e AdminUpdateUserInfoReqValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e AdminUpdateUserInfoReqValidationError) ErrorName() string {
	return "AdminUpdateUserInfoReqValidationError"
}

// Error satisfies the builtin error interface
func (e AdminUpdateUserInfoReqValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sAdminUpdateUserInfoReq.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = AdminUpdateUserInfoReqValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = AdminUpdateUserInfoReqValidationError{}

// Validate checks the field values on AdminUpdateUserInfoResp with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *AdminUpdateUserInfoResp) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on AdminUpdateUserInfoResp with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// AdminUpdateUserInfoRespMultiError, or nil if none found.
func (m *AdminUpdateUserInfoResp) ValidateAll() error {
	return m.validate(true)
}

func (m *AdminUpdateUserInfoResp) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetBase()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, AdminUpdateUserInfoRespValidationError{
					field:  "Base",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, AdminUpdateUserInfoRespValidationError{
					field:  "Base",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetBase()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return AdminUpdateUserInfoRespValidationError{
				field:  "Base",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return AdminUpdateUserInfoRespMultiError(errors)
	}

	return nil
}

// AdminUpdateUserInfoRespMultiError is an error wrapping multiple validation
// errors returned by AdminUpdateUserInfoResp.ValidateAll() if the designated
// constraints aren't met.
type AdminUpdateUserInfoRespMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m AdminUpdateUserInfoRespMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m AdminUpdateUserInfoRespMultiError) AllErrors() []error { return m }

// AdminUpdateUserInfoRespValidationError is the validation error returned by
// AdminUpdateUserInfoResp.Validate if the designated constraints aren't met.
type AdminUpdateUserInfoRespValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e AdminUpdateUserInfoRespValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e AdminUpdateUserInfoRespValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e AdminUpdateUserInfoRespValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e AdminUpdateUserInfoRespValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e AdminUpdateUserInfoRespValidationError) ErrorName() string {
	return "AdminUpdateUserInfoRespValidationError"
}

// Error satisfies the builtin error interface
func (e AdminUpdateUserInfoRespValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sAdminUpdateUserInfoResp.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = AdminUpdateUserInfoRespValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = AdminUpdateUserInfoRespValidationError{}

// Validate checks the field values on AdminCreateUserReq with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *AdminCreateUserReq) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on AdminCreateUserReq with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// AdminCreateUserReqMultiError, or nil if none found.
func (m *AdminCreateUserReq) ValidateAll() error {
	return m.validate(true)
}

func (m *AdminCreateUserReq) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if l := len(m.GetPhone()); l < 1 || l > 11 {
		err := AdminCreateUserReqValidationError{
			field:  "Phone",
			reason: "value length must be between 1 and 11 bytes, inclusive",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	// no validation rules for Nickname

	// no validation rules for Avatar

	// no validation rules for Gender

	// no validation rules for Status

	// no validation rules for BackgroundUrl

	// no validation rules for VoiceSignatureUrl

	// no validation rules for VoiceDuration

	// no validation rules for IsPremiumCreator

	// no validation rules for CreationUserId

	if len(errors) > 0 {
		return AdminCreateUserReqMultiError(errors)
	}

	return nil
}

// AdminCreateUserReqMultiError is an error wrapping multiple validation errors
// returned by AdminCreateUserReq.ValidateAll() if the designated constraints
// aren't met.
type AdminCreateUserReqMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m AdminCreateUserReqMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m AdminCreateUserReqMultiError) AllErrors() []error { return m }

// AdminCreateUserReqValidationError is the validation error returned by
// AdminCreateUserReq.Validate if the designated constraints aren't met.
type AdminCreateUserReqValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e AdminCreateUserReqValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e AdminCreateUserReqValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e AdminCreateUserReqValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e AdminCreateUserReqValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e AdminCreateUserReqValidationError) ErrorName() string {
	return "AdminCreateUserReqValidationError"
}

// Error satisfies the builtin error interface
func (e AdminCreateUserReqValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sAdminCreateUserReq.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = AdminCreateUserReqValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = AdminCreateUserReqValidationError{}

// Validate checks the field values on AdminCreateUserResp with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *AdminCreateUserResp) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on AdminCreateUserResp with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// AdminCreateUserRespMultiError, or nil if none found.
func (m *AdminCreateUserResp) ValidateAll() error {
	return m.validate(true)
}

func (m *AdminCreateUserResp) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetBase()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, AdminCreateUserRespValidationError{
					field:  "Base",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, AdminCreateUserRespValidationError{
					field:  "Base",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetBase()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return AdminCreateUserRespValidationError{
				field:  "Base",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for UserId

	if len(errors) > 0 {
		return AdminCreateUserRespMultiError(errors)
	}

	return nil
}

// AdminCreateUserRespMultiError is an error wrapping multiple validation
// errors returned by AdminCreateUserResp.ValidateAll() if the designated
// constraints aren't met.
type AdminCreateUserRespMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m AdminCreateUserRespMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m AdminCreateUserRespMultiError) AllErrors() []error { return m }

// AdminCreateUserRespValidationError is the validation error returned by
// AdminCreateUserResp.Validate if the designated constraints aren't met.
type AdminCreateUserRespValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e AdminCreateUserRespValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e AdminCreateUserRespValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e AdminCreateUserRespValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e AdminCreateUserRespValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e AdminCreateUserRespValidationError) ErrorName() string {
	return "AdminCreateUserRespValidationError"
}

// Error satisfies the builtin error interface
func (e AdminCreateUserRespValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sAdminCreateUserResp.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = AdminCreateUserRespValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = AdminCreateUserRespValidationError{}

// Validate checks the field values on ReviewNotifyReq with the rules defined
// in the proto definition for this message. If any rules are violated, the
// first error encountered is returned, or nil if there are no violations.
func (m *ReviewNotifyReq) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on ReviewNotifyReq with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// ReviewNotifyReqMultiError, or nil if none found.
func (m *ReviewNotifyReq) ValidateAll() error {
	return m.validate(true)
}

func (m *ReviewNotifyReq) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if m.GetUserId() <= 0 {
		err := ReviewNotifyReqValidationError{
			field:  "UserId",
			reason: "value must be greater than 0",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if _, ok := ReviewNotifyType_name[int32(m.GetReviewNotifyType())]; !ok {
		err := ReviewNotifyReqValidationError{
			field:  "ReviewNotifyType",
			reason: "value must be one of the defined enum values",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if len(errors) > 0 {
		return ReviewNotifyReqMultiError(errors)
	}

	return nil
}

// ReviewNotifyReqMultiError is an error wrapping multiple validation errors
// returned by ReviewNotifyReq.ValidateAll() if the designated constraints
// aren't met.
type ReviewNotifyReqMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m ReviewNotifyReqMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m ReviewNotifyReqMultiError) AllErrors() []error { return m }

// ReviewNotifyReqValidationError is the validation error returned by
// ReviewNotifyReq.Validate if the designated constraints aren't met.
type ReviewNotifyReqValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e ReviewNotifyReqValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e ReviewNotifyReqValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e ReviewNotifyReqValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e ReviewNotifyReqValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e ReviewNotifyReqValidationError) ErrorName() string { return "ReviewNotifyReqValidationError" }

// Error satisfies the builtin error interface
func (e ReviewNotifyReqValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sReviewNotifyReq.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = ReviewNotifyReqValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = ReviewNotifyReqValidationError{}
