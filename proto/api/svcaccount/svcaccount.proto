syntax = "proto3";

package vc.svcaccount;
option go_package = "new-gitlab.xunlei.cn/vcproject/backends/proto/api/svcaccount;svcaccount";

import "protoc-gen-validate/validate/validate.proto";
import "common/common.proto";
import "svcreview/svcreview.proto";

enum OnConnectType {
    invalid = 0;
    CONNECTED = 1;
    PING = 2;
    DISCONNECTED = 3;
}

enum ReviewNotifyType {
    INVALID = 0;
    AVATAR_REJECT = 1;
    AVATAR_APPROVE = 2;
    NICKNAME_REJECT = 3;
    NICKNAME_APPROVE = 4;
    BACKGROUND_IMAGE_REJECT = 5;
    BACKGROUND_IMAGE_APPROVE = 6;
    SIGNATURE_REJECT = 7;
    SIGNATURE_APPROVE = 8;
}

message GetVerificationCodeReq {
    //phone number
    string phone_number = 1 [(validate.rules).string={min_bytes:1, max_bytes:11}];
}

message GetVerificationCodeResp {
    common.SvcBaseResp base = 1;
    GetVerificationCodeRespData data = 2;
}

message GetVerificationCodeRespData {
    //is registered
    bool is_registered = 1;
    //verification token
    string verification_token = 2[(validate.rules).string={min_bytes:1}];
}

message AccountSignUpReq {
  // sms code
  string sms_code = 1[(validate.rules).string={min_bytes:1, max_bytes:11}];
  // verification token
  string verification_token = 2[(validate.rules).string={min_bytes:1}];
  // 手机号
  string phone_number = 3[(validate.rules).string={min_bytes:1, max_bytes:11}];
}

// 公共的 Token 响应数据结构
message TokenRespData {
    string token_type = 1;      // 令牌类型，通常为 "Bearer"
    string access_token = 2;    // 访问令牌
    string refresh_token = 3;   // 刷新令牌
    int64 expires_in = 4;       // 访问令牌的过期时间（秒）
    int64 user_id = 5;         // 用户ID
    string nick_name = 6;       // 用户昵称
    string avatar = 7;          // 头像
    int32 gender = 8;           // 性别
}

message AccountSignUpResp {
    common.SvcBaseResp base = 1;
    TokenRespData data = 2;
}

message AccountSignInReq {
    // sms code
    string sms_code = 1[(validate.rules).string={min_bytes:1}];
    //verification token
    string verification_token = 2[(validate.rules).string={min_bytes:1}];
    // 手机号
    string phone_number = 3[(validate.rules).string={min_bytes:1, max_bytes:11}];
}

message AccountSignInResp {
    common.SvcBaseResp base = 1;
    TokenRespData data = 2;
}

message AccountSignOutReq {
}

message AccountSignOutResp {
    common.SvcBaseResp base = 1;
}

message RefreshTokenReq {
    string refresh_token = 1 [(validate.rules).string={min_bytes:1}];
}

message RefreshTokenResp {
    common.SvcBaseResp base = 1;
    TokenRespData data = 2;
}

message AccountDeleteReq {}

message AccountDeleteResp {
    common.SvcBaseResp base = 1;
}

message GetUserInfoReq {
    int64 user_id = 1;
    bool with_follow = 2;  // 带关注信息
    bool with_score = 3; // 带积分信息
}

// 根据admin用户ID获取关联的业务用户信息
message GetUserInfoByCreationUserIdReq {
    uint32 creation_user_id = 1 [(validate.rules).uint32.gt = 0]; // admin用户ID
    bool with_follow = 2;  // 带关注信息
    bool with_score = 3; // 带积分信息
}

message GetUserInfoByCreationUserIdResp {
    common.SvcBaseResp base = 1;
    UserInfo data = 2;  // 基础信息
}

message GetUserInfoResp {
    common.SvcBaseResp base = 1;
    UserInfo data = 2;  // 基础信息
}

message UserInfo {
    int64 user_id = 1;      // 用户ID
    string nickname = 2;     // 昵称
    string phone = 3;        // 手机号
    string avatar = 4;       // 头像
    int32 gender = 5;        // 性别
    int32 status = 6;        // 状态
    int64 created_at = 7;    // 创建时间（毫秒）
    string background_url = 8; // 背景图
    VoiceSignature voice_signature = 9; // 语音签名
    bool is_premium_creator = 10; //是否优质创作者
    int64 followers_count = 11; // 粉丝数
    int64 following_count = 12; // 关注数
    bool is_following = 13;   // 当前用户是否关注了该用户
    bool is_followed = 14;    // 该用户是否关注了当前用户
    UserScoreData score_data = 15; // 分贝数据
    int32 script_count = 16; //剧本数
    int32 user_character_count = 17; // 用户角色数（音包数）
    bool is_deleted = 18;    // 是否已注销
    uint32 creation_user_id = 19; // 创建该用户的admin用户ID
}

message FollowData {
  int64 followers_count = 8; // 粉丝数
  int64 following_count = 9; // 关注数
  bool is_following = 10;   // 当前用户是否关注了该用户
  bool is_followed = 11;    // 该用户是否关注了当前用户
}

message VoiceSignature {
    string voice_signature_url = 1; // 语音签名
    int32 duration = 2; //时长（秒）
    bool is_reviewing = 3; //是否正在审核中
}

// 批量获取用户信息请求
message BatchGetUserInfoReq {
    int64 user_id = 1; //当前用户ID
    repeated int64 user_ids = 2;  // 用户ID列表
}

// 批量获取用户信息响应
message BatchGetUserInfoResp {
    common.SvcBaseResp base = 1;
    map<int64, UserInfo> user_info_map = 2;
}

// 更新用户
message UpdateUserInfoReq {
  int64 user_id = 1 [(validate.rules).int64.gt = 0]; // 用户ID
  string avatar = 2;  // 头像URL
  string nickname = 3;  // 昵称
  string background_url = 4; // 背景图
  string voice_signature_url = 5; // 语音签名
  int32 voice_duration = 6; //语音时长
  bool is_premium_creator = 7; //是否优质创作者
  bool reset_voice_signature = 8; // 是否重置语音签名
  uint32 creation_user_id = 9; // 关联的创作者admin用户ID
}

// 更新用户
message UpdateUserInfoResp {
  common.SvcBaseResp base = 1;
}

// 设置用户为优质创作者请求
message SetPremiumCreatorReq {
  int64 user_id = 1 [(validate.rules).int64.gt = 0]; // 用户ID
  bool is_premium_creator = 2; // 是否设置为优质创作者
}

// 设置用户为优质创作者响应
message SetPremiumCreatorResp {
  common.SvcBaseResp base = 1;
}

message AccountOnlineReq {
    common.BaseParam data = 1;
    int64 userid = 2[(validate.rules).int64.gt=0];
    bool state = 3;
    bool heartbeat = 4;
    bool need_resp = 5;
    bool front = 6;
    OnConnectType  on_connect_type = 7;
}

message AccountOnlineResp {
    common.SvcBaseResp base = 1;
}

message IsUserBlacklistReq {
  int64 user_id = 1;
}

message IsUserBlacklistResp {
  common.SvcBaseResp base = 1;
  bool is_blacklist = 2;
}

message GetUserScoreReq {
  int64 user_id = 1;
}

message GetUserScoreResp {
  common.SvcBaseResp base = 1;
  UserScoreData data = 2;
}

message AddUserScoreReq {
  // 用户ID
  int64 user_id = 1[(validate.rules).int64.gt=0];
  // 积分
  int64 valuable_score = 2[(validate.rules).int64.gte=0];
  // 免费积分
  int64 fee_score = 3[(validate.rules).int64.gte=0];
  // 积分来源
  int64 biz_type = 4[(validate.rules).int64.gt=0];
  // 积分来源名
  string biz_name = 5[(validate.rules).string={min_bytes:1, max_bytes:64}];
  // 订单ID
  string order_id = 6[(validate.rules).string={min_bytes:1, max_bytes:64}];
  // 备注
  string remark= 7[(validate.rules).string={max_bytes:255}];
}

message AddUserScoreResp {
  common.SvcBaseResp base = 1;
}

message ConsumeUserScoreReq {
  // 用户ID
  int64 user_id = 1[(validate.rules).int64.gt=0];
  // 积分
  int64 score = 2[(validate.rules).int64.gt=0];
  // 积分来源
  int64 biz_type = 3[(validate.rules).int64.gt=0];
  // 积分来源名
  string biz_name = 4[(validate.rules).string={min_bytes:1, max_bytes:64}];
  // 订单ID
  string order_id = 5[(validate.rules).string={min_bytes:1, max_bytes:64}];
  // 允许透支消费，超额扣为0
  bool allow_overdraw = 7;
}

message ConsumeUserScoreResp {
  common.SvcBaseResp base = 1;
  ConsumeUserScoreRespData data = 2;
}

message ConsumeUserScoreRespData {
  // 消耗的有价值积分
  int64 consume_valuable_score=1;
  // 消耗免费积分
  int64 consume_free_score=2;
}

message UserScoreData {
  // 总积分余额（包含免费，有价值) 注意放大100倍
  int64 total_balance = 1;
  // 免费积分， 注意放大100倍
  int64 free_balance = 2;
  // 有价值积分, 注意放大100倍
  int64 valuable_balance = 3;
  // 历史累计总有价值积分
  int64 total_valuable_score=4;
  // 历史累计免费价值积分
  int64 total_free_score=5;

  string display_total_balance = 11; // 格式化后的总积分余额
}

// 搜索用户请求
message SearchUsersReq {
    string keyword = 1 [(validate.rules).string.min_len = 1];  // 搜索关键词
    int32 page = 2;            // 页码
    int32 page_size = 3;       // 每页数量
}

// 搜索用户响应
message SearchUsersResp {
    common.SvcBaseResp base = 1;
    SearchUsersRespData data = 2;
}

// 搜索用户响应数据
message SearchUsersRespData {
    repeated UserInfo users = 1;  // 用户列表
    int64 total = 2;             // 总数量
}

// 关注用户请求
message FollowUserReq {
    int64 follower_id = 1 [(validate.rules).int64.gt = 0]; // 关注者ID（当前用户）
    int64 following_id = 2 [(validate.rules).int64.gt = 0]; // 被关注者ID
}

// 关注用户响应
message FollowUserResp {
    common.SvcBaseResp base = 1;
}

// 取消关注用户请求
message UnfollowUserReq {
    int64 follower_id = 1 [(validate.rules).int64.gt = 0]; // 关注者ID（当前用户）
    int64 following_id = 2 [(validate.rules).int64.gt = 0]; // 被关注者ID
}

// 取消关注用户响应
message UnfollowUserResp {
    common.SvcBaseResp base = 1;
}

// 检查是否关注请求
message CheckFollowingReq {
    int64 follower_id = 1 [(validate.rules).int64.gt = 0]; // 关注者ID（当前用户）
    int64 following_id = 2 [(validate.rules).int64.gt = 0]; // 被关注者ID
}

// 检查是否关注响应
message CheckFollowingResp {
    common.SvcBaseResp base = 1;
    bool is_following = 2; // 是否关注
}

// 获取关注的用户列表请求
message GetFollowingUsersReq {
    int64 user_id = 1 [(validate.rules).int64.gt = 0]; // 用户ID
    int32 page = 2; // 页码
    int32 page_size = 3; // 每页数量
}

// 获取关注的用户列表响应
message GetFollowingUsersResp {
    common.SvcBaseResp base = 1;
    GetFollowingUsersRespData data = 2;
}

// 获取关注的用户列表响应数据
message GetFollowingUsersRespData {
    repeated UserInfo users = 1; // 用户列表
    int64 total = 2; // 总数
}

// 获取粉丝列表请求
message GetFollowersReq {
    int64 user_id = 1 [(validate.rules).int64.gt = 0]; // 用户ID
    int32 page = 2; // 页码
    int32 page_size = 3; // 每页数量
}

// 获取粉丝列表响应
message GetFollowersResp {
    common.SvcBaseResp base = 1;
    GetFollowersRespData data = 2;
}

// 获取粉丝列表响应数据
message GetFollowersRespData {
    repeated UserInfo users = 1; // 用户列表
    int64 total = 2; // 总数
}

// 获取关注用户ID列表请求
message GetFollowingUserIDsReq {
    int64 user_id = 1 [(validate.rules).int64.gt = 0]; // 用户ID
}

// 获取关注用户ID列表响应
message GetFollowingUserIDsResp {
    common.SvcBaseResp base = 1;
    GetFollowingUserIDsRespData data = 2;
}

// 获取关注用户ID列表响应数据
message GetFollowingUserIDsRespData {
  repeated int64 user_ids = 1; // 用户ID列表
}

// 批量检查是否关注请求
message BatchCheckFollowingReq {
    int64 follower_id = 1 [(validate.rules).int64.gt = 0]; // 关注者ID（当前用户）
    repeated int64 following_ids = 2 [(validate.rules).repeated = {min_items: 1, max_items: 1000}]; // 被关注者ID列表
}

// 批量检查是否关注响应
message BatchCheckFollowingResp {
    common.SvcBaseResp base = 1;
    FollowDataItem data = 2;
}

message FollowDataItem {
    map<int64, FollowData> items = 1; // key: 被关注者ID, value: FollowData
}

// 审核回调
message ReviewCallbackReq {
  int64 userid = 1;
  int64 id = 2;
  svcreview.ReviewBizType type = 3;
  svcreview.AuditResult result = 4;
  string reason = 5;
}

// 检查手机号是否在白名单请求
message IsPhoneWhitelistReq {
  string phone_number = 1 [(validate.rules).string={min_bytes:1, max_bytes:11}];
}

// 检查手机号是否在白名单响应
message IsPhoneWhitelistResp {
  common.SvcBaseResp base = 1;
  bool is_whitelist = 2;
}

// 获取登录状态请求
message GetLoginStatusReq {}

// 登录状态信息
message LoginStatusInfo {
  string device_id = 1;
  int32 platform = 2;
  int64 login_time = 3;
  int64 last_active = 4;
  string ip_address = 5;
}

// 获取登录状态响应
message GetLoginStatusResp {
  common.SvcBaseResp base = 1;
  LoginStatusInfo data = 2;
}

// 踢下线设备请求
message KickOffDeviceReq {
  string device_id = 1 [(validate.rules).string={min_bytes:1}];
}

// 踢下线设备响应
message KickOffDeviceResp {
  common.SvcBaseResp base = 1;
}

// 设备登录历史信息
message DeviceHistoryInfo {
  string device_id = 1;
  int32 platform = 2;
  int64 login_time = 3;
  string ip_address = 4;
}

// 获取设备登录历史请求
message GetDeviceHistoryReq {}

// 获取设备登录历史响应
message GetDeviceHistoryResp {
  common.SvcBaseResp base = 1;
  repeated DeviceHistoryInfo data = 2;
}

// 验证Token请求
message VerifyTokenReq {
  string token = 1 [(validate.rules).string={min_bytes:1}];
  string device_id = 2;
  int32 platform = 3;
  string user_agent = 4;
}

// Token验证信息
message TokenVerifyInfo {
  int64 user_id = 1;
  string device_id = 2;
  int32 platform = 3;
  bool is_valid = 4;
  string reason = 5; // 如果无效，说明原因
}

// 验证Token响应
message VerifyTokenResp {
  common.SvcBaseResp base = 1;
  TokenVerifyInfo data = 2;
}

// 重置用户信息请求
message ResetUserInfoReq {
  int64 user_id = 1 [(validate.rules).int64.gt = 0]; // 用户ID
}

// 重置用户信息响应
message ResetUserInfoResp {
  common.SvcBaseResp base = 1;
}

// 封禁用户请求
message BanUserReq {
  int64 user_id = 1 [(validate.rules).int64.gt = 0]; // 用户ID
}

// 封禁用户响应
message BanUserResp {
  common.SvcBaseResp base = 1;
}

// 解封用户请求
message UnbanUserReq {
  int64 user_id = 1 [(validate.rules).int64.gt = 0]; // 用户ID
}

// 解封用户响应
message UnbanUserResp {
  common.SvcBaseResp base = 1;
}

// Admin审核更新用户信息请求
message AdminUpdateUserInfoReq {
  int64 user_id = 1 [(validate.rules).int64.gt = 0]; // 用户ID
  string avatar = 2;  // 头像URL
  string nickname = 3;  // 昵称
  string background_url = 4; // 背景图
  string voice_signature_url = 5; // 语音签名
  int32 voice_duration = 6; //语音时长
}

// Admin审核更新用户信息响应
message AdminUpdateUserInfoResp {
  common.SvcBaseResp base = 1;
}

// Admin创建用户请求
message AdminCreateUserReq {
  string phone = 1 [(validate.rules).string={min_bytes:1, max_bytes:11}]; // 手机号
  string nickname = 2; // 昵称
  string avatar = 3; // 头像URL
  int32 gender = 4; // 性别 0:未知 1:男 2:女
  int32 status = 5; // 状态 1:正常 3:禁用
  string background_url = 6; // 背景图
  string voice_signature_url = 7; // 语音签名
  int32 voice_duration = 8; // 语音时长
  bool is_premium_creator = 9; // 是否优质创作者
  uint32 creation_user_id = 10; // 关联的创作者admin用户ID
}

// Admin创建用户响应
message AdminCreateUserResp {
  common.SvcBaseResp base = 1;
  int64 user_id = 2; // 创建的用户ID
}

message ReviewNotifyReq {
  int64 user_id = 1 [(validate.rules).int64.gt = 0]; // 用户ID
  ReviewNotifyType review_notify_type = 2 [(validate.rules).enum.defined_only = true]; //审核通知类型
}

service s {
    // 获取验证码
    rpc GetVerificationCode(GetVerificationCodeReq) returns (GetVerificationCodeResp) {}

    // 注册
    rpc AccountSignUp(AccountSignUpReq) returns (AccountSignUpResp) {}

    // 登录
    rpc AccountSignIn(AccountSignInReq) returns (AccountSignInResp) {}

    // 退出登录
    rpc AccountSignOut(AccountSignOutReq) returns (AccountSignOutResp) {}

    // 刷新token
    rpc RefreshToken(RefreshTokenReq) returns (RefreshTokenResp) {}

    // 注销账号
    rpc AccountDelete(AccountDeleteReq) returns (AccountDeleteResp) {}

    // 获取用户信息
    rpc GetUserInfo(GetUserInfoReq) returns (GetUserInfoResp) {}

    // 根据admin用户ID获取关联的业务用户信息
    rpc GetUserInfoByCreationUserId(GetUserInfoByCreationUserIdReq) returns (GetUserInfoByCreationUserIdResp) {}

    // 批量获取用户信息
    rpc BatchGetUserInfo(BatchGetUserInfoReq) returns (BatchGetUserInfoResp) {}

    // 更新用户信息
    rpc UpdateUserInfo(UpdateUserInfoReq) returns (UpdateUserInfoResp);

    // 设置用户为优质创作者
    rpc SetPremiumCreator(SetPremiumCreatorReq) returns (SetPremiumCreatorResp) {}

    // 状态上报
    rpc OnlineLimiter(AccountOnlineReq) returns (AccountOnlineResp) {}

    // 用户是否拉黑
    rpc IsUserBlacklist(IsUserBlacklistReq) returns (IsUserBlacklistResp) {}

    // 获取用户积分
    rpc GetUserScore(GetUserScoreReq) returns (GetUserScoreResp) {}

    // 增加用户积分
    rpc AddUserScore(AddUserScoreReq) returns (AddUserScoreResp) {}

    // 消耗用户积分
    rpc ConsumeUserScore(ConsumeUserScoreReq) returns (ConsumeUserScoreResp) {}
    
    // 搜索用户
    rpc SearchUsers(SearchUsersReq) returns (SearchUsersResp) {}

    // 关注用户
    rpc FollowUser(FollowUserReq) returns (FollowUserResp) {}

    // 取消关注用户
    rpc UnfollowUser(UnfollowUserReq) returns (UnfollowUserResp) {}

    // 检查是否关注
    rpc CheckFollowing(CheckFollowingReq) returns (CheckFollowingResp) {}

    // 获取关注的用户列表
    rpc GetFollowingUsers(GetFollowingUsersReq) returns (GetFollowingUsersResp) {}

    // 获取粉丝列表
    rpc GetFollowers(GetFollowersReq) returns (GetFollowersResp) {}

    // 获取关注用户ID列表（精简版，只返回用户ID）
    rpc GetFollowingUserIDs(GetFollowingUserIDsReq) returns (GetFollowingUserIDsResp) {}

    // 批量检查是否关注
    rpc BatchCheckFollowing(BatchCheckFollowingReq) returns (BatchCheckFollowingResp) {}

    // 用户审核callback （语音签名）
    rpc ReviewCallback(ReviewCallbackReq) returns (common.SvcCommonResp);

    // 检查手机号是否在白名单
    rpc IsPhoneWhitelist(IsPhoneWhitelistReq) returns (IsPhoneWhitelistResp) {}

    // 获取登录状态
    rpc GetLoginStatus(GetLoginStatusReq) returns (GetLoginStatusResp) {}

    // 踢下线设备
    rpc KickOffDevice(KickOffDeviceReq) returns (KickOffDeviceResp) {}

    // 获取设备登录历史
    rpc GetDeviceHistory(GetDeviceHistoryReq) returns (GetDeviceHistoryResp) {}

    // 验证Token（包括黑名单检查）
    rpc VerifyToken(VerifyTokenReq) returns (VerifyTokenResp) {}

    // 重置用户信息
    rpc ResetUserInfo(ResetUserInfoReq) returns (ResetUserInfoResp) {}

    // 封禁用户
    rpc BanUser(BanUserReq) returns (BanUserResp) {}

    // 解封用户
    rpc UnbanUser(UnbanUserReq) returns (UnbanUserResp) {}

    // Admin审核更新用户信息（不触发审核逻辑）
    rpc AdminUpdateUserInfo(AdminUpdateUserInfoReq) returns (AdminUpdateUserInfoResp) {}

    // Admin创建用户
    rpc AdminCreateUser(AdminCreateUserReq) returns (AdminCreateUserResp) {}

    // 审核通知
    rpc ReviewNotify(ReviewNotifyReq) returns (common.SvcCommonResp) {}
}
