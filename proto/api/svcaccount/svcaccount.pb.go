// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.36.6
// 	protoc        v5.29.3
// source: svcaccount.proto

package svcaccount

import (
	_ "github.com/envoyproxy/protoc-gen-validate/validate"
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	common "new-gitlab.xunlei.cn/vcproject/backends/proto/api/common"
	svcreview "new-gitlab.xunlei.cn/vcproject/backends/proto/api/svcreview"
	reflect "reflect"
	sync "sync"
	unsafe "unsafe"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type OnConnectType int32

const (
	OnConnectType_invalid      OnConnectType = 0
	OnConnectType_CONNECTED    OnConnectType = 1
	OnConnectType_PING         OnConnectType = 2
	OnConnectType_DISCONNECTED OnConnectType = 3
)

// Enum value maps for OnConnectType.
var (
	OnConnectType_name = map[int32]string{
		0: "invalid",
		1: "CONNECTED",
		2: "PING",
		3: "DISCONNECTED",
	}
	OnConnectType_value = map[string]int32{
		"invalid":      0,
		"CONNECTED":    1,
		"PING":         2,
		"DISCONNECTED": 3,
	}
)

func (x OnConnectType) Enum() *OnConnectType {
	p := new(OnConnectType)
	*p = x
	return p
}

func (x OnConnectType) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (OnConnectType) Descriptor() protoreflect.EnumDescriptor {
	return file_svcaccount_proto_enumTypes[0].Descriptor()
}

func (OnConnectType) Type() protoreflect.EnumType {
	return &file_svcaccount_proto_enumTypes[0]
}

func (x OnConnectType) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use OnConnectType.Descriptor instead.
func (OnConnectType) EnumDescriptor() ([]byte, []int) {
	return file_svcaccount_proto_rawDescGZIP(), []int{0}
}

type ReviewNotifyType int32

const (
	ReviewNotifyType_INVALID                  ReviewNotifyType = 0
	ReviewNotifyType_AVATAR_REJECT            ReviewNotifyType = 1
	ReviewNotifyType_AVATAR_APPROVE           ReviewNotifyType = 2
	ReviewNotifyType_NICKNAME_REJECT          ReviewNotifyType = 3
	ReviewNotifyType_NICKNAME_APPROVE         ReviewNotifyType = 4
	ReviewNotifyType_BACKGROUND_IMAGE_REJECT  ReviewNotifyType = 5
	ReviewNotifyType_BACKGROUND_IMAGE_APPROVE ReviewNotifyType = 6
	ReviewNotifyType_SIGNATURE_REJECT         ReviewNotifyType = 7
	ReviewNotifyType_SIGNATURE_APPROVE        ReviewNotifyType = 8
)

// Enum value maps for ReviewNotifyType.
var (
	ReviewNotifyType_name = map[int32]string{
		0: "INVALID",
		1: "AVATAR_REJECT",
		2: "AVATAR_APPROVE",
		3: "NICKNAME_REJECT",
		4: "NICKNAME_APPROVE",
		5: "BACKGROUND_IMAGE_REJECT",
		6: "BACKGROUND_IMAGE_APPROVE",
		7: "SIGNATURE_REJECT",
		8: "SIGNATURE_APPROVE",
	}
	ReviewNotifyType_value = map[string]int32{
		"INVALID":                  0,
		"AVATAR_REJECT":            1,
		"AVATAR_APPROVE":           2,
		"NICKNAME_REJECT":          3,
		"NICKNAME_APPROVE":         4,
		"BACKGROUND_IMAGE_REJECT":  5,
		"BACKGROUND_IMAGE_APPROVE": 6,
		"SIGNATURE_REJECT":         7,
		"SIGNATURE_APPROVE":        8,
	}
)

func (x ReviewNotifyType) Enum() *ReviewNotifyType {
	p := new(ReviewNotifyType)
	*p = x
	return p
}

func (x ReviewNotifyType) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (ReviewNotifyType) Descriptor() protoreflect.EnumDescriptor {
	return file_svcaccount_proto_enumTypes[1].Descriptor()
}

func (ReviewNotifyType) Type() protoreflect.EnumType {
	return &file_svcaccount_proto_enumTypes[1]
}

func (x ReviewNotifyType) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use ReviewNotifyType.Descriptor instead.
func (ReviewNotifyType) EnumDescriptor() ([]byte, []int) {
	return file_svcaccount_proto_rawDescGZIP(), []int{1}
}

type GetVerificationCodeReq struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// phone number
	PhoneNumber   string `protobuf:"bytes,1,opt,name=phone_number,json=phoneNumber,proto3" json:"phone_number,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *GetVerificationCodeReq) Reset() {
	*x = GetVerificationCodeReq{}
	mi := &file_svcaccount_proto_msgTypes[0]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GetVerificationCodeReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetVerificationCodeReq) ProtoMessage() {}

func (x *GetVerificationCodeReq) ProtoReflect() protoreflect.Message {
	mi := &file_svcaccount_proto_msgTypes[0]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetVerificationCodeReq.ProtoReflect.Descriptor instead.
func (*GetVerificationCodeReq) Descriptor() ([]byte, []int) {
	return file_svcaccount_proto_rawDescGZIP(), []int{0}
}

func (x *GetVerificationCodeReq) GetPhoneNumber() string {
	if x != nil {
		return x.PhoneNumber
	}
	return ""
}

type GetVerificationCodeResp struct {
	state         protoimpl.MessageState       `protogen:"open.v1"`
	Base          *common.SvcBaseResp          `protobuf:"bytes,1,opt,name=base,proto3" json:"base,omitempty"`
	Data          *GetVerificationCodeRespData `protobuf:"bytes,2,opt,name=data,proto3" json:"data,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *GetVerificationCodeResp) Reset() {
	*x = GetVerificationCodeResp{}
	mi := &file_svcaccount_proto_msgTypes[1]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GetVerificationCodeResp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetVerificationCodeResp) ProtoMessage() {}

func (x *GetVerificationCodeResp) ProtoReflect() protoreflect.Message {
	mi := &file_svcaccount_proto_msgTypes[1]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetVerificationCodeResp.ProtoReflect.Descriptor instead.
func (*GetVerificationCodeResp) Descriptor() ([]byte, []int) {
	return file_svcaccount_proto_rawDescGZIP(), []int{1}
}

func (x *GetVerificationCodeResp) GetBase() *common.SvcBaseResp {
	if x != nil {
		return x.Base
	}
	return nil
}

func (x *GetVerificationCodeResp) GetData() *GetVerificationCodeRespData {
	if x != nil {
		return x.Data
	}
	return nil
}

type GetVerificationCodeRespData struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// is registered
	IsRegistered bool `protobuf:"varint,1,opt,name=is_registered,json=isRegistered,proto3" json:"is_registered,omitempty"`
	// verification token
	VerificationToken string `protobuf:"bytes,2,opt,name=verification_token,json=verificationToken,proto3" json:"verification_token,omitempty"`
	unknownFields     protoimpl.UnknownFields
	sizeCache         protoimpl.SizeCache
}

func (x *GetVerificationCodeRespData) Reset() {
	*x = GetVerificationCodeRespData{}
	mi := &file_svcaccount_proto_msgTypes[2]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GetVerificationCodeRespData) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetVerificationCodeRespData) ProtoMessage() {}

func (x *GetVerificationCodeRespData) ProtoReflect() protoreflect.Message {
	mi := &file_svcaccount_proto_msgTypes[2]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetVerificationCodeRespData.ProtoReflect.Descriptor instead.
func (*GetVerificationCodeRespData) Descriptor() ([]byte, []int) {
	return file_svcaccount_proto_rawDescGZIP(), []int{2}
}

func (x *GetVerificationCodeRespData) GetIsRegistered() bool {
	if x != nil {
		return x.IsRegistered
	}
	return false
}

func (x *GetVerificationCodeRespData) GetVerificationToken() string {
	if x != nil {
		return x.VerificationToken
	}
	return ""
}

type AccountSignUpReq struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// sms code
	SmsCode string `protobuf:"bytes,1,opt,name=sms_code,json=smsCode,proto3" json:"sms_code,omitempty"`
	// verification token
	VerificationToken string `protobuf:"bytes,2,opt,name=verification_token,json=verificationToken,proto3" json:"verification_token,omitempty"`
	// 手机号
	PhoneNumber   string `protobuf:"bytes,3,opt,name=phone_number,json=phoneNumber,proto3" json:"phone_number,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *AccountSignUpReq) Reset() {
	*x = AccountSignUpReq{}
	mi := &file_svcaccount_proto_msgTypes[3]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *AccountSignUpReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*AccountSignUpReq) ProtoMessage() {}

func (x *AccountSignUpReq) ProtoReflect() protoreflect.Message {
	mi := &file_svcaccount_proto_msgTypes[3]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use AccountSignUpReq.ProtoReflect.Descriptor instead.
func (*AccountSignUpReq) Descriptor() ([]byte, []int) {
	return file_svcaccount_proto_rawDescGZIP(), []int{3}
}

func (x *AccountSignUpReq) GetSmsCode() string {
	if x != nil {
		return x.SmsCode
	}
	return ""
}

func (x *AccountSignUpReq) GetVerificationToken() string {
	if x != nil {
		return x.VerificationToken
	}
	return ""
}

func (x *AccountSignUpReq) GetPhoneNumber() string {
	if x != nil {
		return x.PhoneNumber
	}
	return ""
}

// 公共的 Token 响应数据结构
type TokenRespData struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	TokenType     string                 `protobuf:"bytes,1,opt,name=token_type,json=tokenType,proto3" json:"token_type,omitempty"`          // 令牌类型，通常为 "Bearer"
	AccessToken   string                 `protobuf:"bytes,2,opt,name=access_token,json=accessToken,proto3" json:"access_token,omitempty"`    // 访问令牌
	RefreshToken  string                 `protobuf:"bytes,3,opt,name=refresh_token,json=refreshToken,proto3" json:"refresh_token,omitempty"` // 刷新令牌
	ExpiresIn     int64                  `protobuf:"varint,4,opt,name=expires_in,json=expiresIn,proto3" json:"expires_in,omitempty"`         // 访问令牌的过期时间（秒）
	UserId        int64                  `protobuf:"varint,5,opt,name=user_id,json=userId,proto3" json:"user_id,omitempty"`                  // 用户ID
	NickName      string                 `protobuf:"bytes,6,opt,name=nick_name,json=nickName,proto3" json:"nick_name,omitempty"`             // 用户昵称
	Avatar        string                 `protobuf:"bytes,7,opt,name=avatar,proto3" json:"avatar,omitempty"`                                 // 头像
	Gender        int32                  `protobuf:"varint,8,opt,name=gender,proto3" json:"gender,omitempty"`                                // 性别
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *TokenRespData) Reset() {
	*x = TokenRespData{}
	mi := &file_svcaccount_proto_msgTypes[4]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *TokenRespData) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*TokenRespData) ProtoMessage() {}

func (x *TokenRespData) ProtoReflect() protoreflect.Message {
	mi := &file_svcaccount_proto_msgTypes[4]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use TokenRespData.ProtoReflect.Descriptor instead.
func (*TokenRespData) Descriptor() ([]byte, []int) {
	return file_svcaccount_proto_rawDescGZIP(), []int{4}
}

func (x *TokenRespData) GetTokenType() string {
	if x != nil {
		return x.TokenType
	}
	return ""
}

func (x *TokenRespData) GetAccessToken() string {
	if x != nil {
		return x.AccessToken
	}
	return ""
}

func (x *TokenRespData) GetRefreshToken() string {
	if x != nil {
		return x.RefreshToken
	}
	return ""
}

func (x *TokenRespData) GetExpiresIn() int64 {
	if x != nil {
		return x.ExpiresIn
	}
	return 0
}

func (x *TokenRespData) GetUserId() int64 {
	if x != nil {
		return x.UserId
	}
	return 0
}

func (x *TokenRespData) GetNickName() string {
	if x != nil {
		return x.NickName
	}
	return ""
}

func (x *TokenRespData) GetAvatar() string {
	if x != nil {
		return x.Avatar
	}
	return ""
}

func (x *TokenRespData) GetGender() int32 {
	if x != nil {
		return x.Gender
	}
	return 0
}

type AccountSignUpResp struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Base          *common.SvcBaseResp    `protobuf:"bytes,1,opt,name=base,proto3" json:"base,omitempty"`
	Data          *TokenRespData         `protobuf:"bytes,2,opt,name=data,proto3" json:"data,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *AccountSignUpResp) Reset() {
	*x = AccountSignUpResp{}
	mi := &file_svcaccount_proto_msgTypes[5]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *AccountSignUpResp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*AccountSignUpResp) ProtoMessage() {}

func (x *AccountSignUpResp) ProtoReflect() protoreflect.Message {
	mi := &file_svcaccount_proto_msgTypes[5]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use AccountSignUpResp.ProtoReflect.Descriptor instead.
func (*AccountSignUpResp) Descriptor() ([]byte, []int) {
	return file_svcaccount_proto_rawDescGZIP(), []int{5}
}

func (x *AccountSignUpResp) GetBase() *common.SvcBaseResp {
	if x != nil {
		return x.Base
	}
	return nil
}

func (x *AccountSignUpResp) GetData() *TokenRespData {
	if x != nil {
		return x.Data
	}
	return nil
}

type AccountSignInReq struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// sms code
	SmsCode string `protobuf:"bytes,1,opt,name=sms_code,json=smsCode,proto3" json:"sms_code,omitempty"`
	// verification token
	VerificationToken string `protobuf:"bytes,2,opt,name=verification_token,json=verificationToken,proto3" json:"verification_token,omitempty"`
	// 手机号
	PhoneNumber   string `protobuf:"bytes,3,opt,name=phone_number,json=phoneNumber,proto3" json:"phone_number,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *AccountSignInReq) Reset() {
	*x = AccountSignInReq{}
	mi := &file_svcaccount_proto_msgTypes[6]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *AccountSignInReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*AccountSignInReq) ProtoMessage() {}

func (x *AccountSignInReq) ProtoReflect() protoreflect.Message {
	mi := &file_svcaccount_proto_msgTypes[6]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use AccountSignInReq.ProtoReflect.Descriptor instead.
func (*AccountSignInReq) Descriptor() ([]byte, []int) {
	return file_svcaccount_proto_rawDescGZIP(), []int{6}
}

func (x *AccountSignInReq) GetSmsCode() string {
	if x != nil {
		return x.SmsCode
	}
	return ""
}

func (x *AccountSignInReq) GetVerificationToken() string {
	if x != nil {
		return x.VerificationToken
	}
	return ""
}

func (x *AccountSignInReq) GetPhoneNumber() string {
	if x != nil {
		return x.PhoneNumber
	}
	return ""
}

type AccountSignInResp struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Base          *common.SvcBaseResp    `protobuf:"bytes,1,opt,name=base,proto3" json:"base,omitempty"`
	Data          *TokenRespData         `protobuf:"bytes,2,opt,name=data,proto3" json:"data,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *AccountSignInResp) Reset() {
	*x = AccountSignInResp{}
	mi := &file_svcaccount_proto_msgTypes[7]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *AccountSignInResp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*AccountSignInResp) ProtoMessage() {}

func (x *AccountSignInResp) ProtoReflect() protoreflect.Message {
	mi := &file_svcaccount_proto_msgTypes[7]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use AccountSignInResp.ProtoReflect.Descriptor instead.
func (*AccountSignInResp) Descriptor() ([]byte, []int) {
	return file_svcaccount_proto_rawDescGZIP(), []int{7}
}

func (x *AccountSignInResp) GetBase() *common.SvcBaseResp {
	if x != nil {
		return x.Base
	}
	return nil
}

func (x *AccountSignInResp) GetData() *TokenRespData {
	if x != nil {
		return x.Data
	}
	return nil
}

type AccountSignOutReq struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *AccountSignOutReq) Reset() {
	*x = AccountSignOutReq{}
	mi := &file_svcaccount_proto_msgTypes[8]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *AccountSignOutReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*AccountSignOutReq) ProtoMessage() {}

func (x *AccountSignOutReq) ProtoReflect() protoreflect.Message {
	mi := &file_svcaccount_proto_msgTypes[8]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use AccountSignOutReq.ProtoReflect.Descriptor instead.
func (*AccountSignOutReq) Descriptor() ([]byte, []int) {
	return file_svcaccount_proto_rawDescGZIP(), []int{8}
}

type AccountSignOutResp struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Base          *common.SvcBaseResp    `protobuf:"bytes,1,opt,name=base,proto3" json:"base,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *AccountSignOutResp) Reset() {
	*x = AccountSignOutResp{}
	mi := &file_svcaccount_proto_msgTypes[9]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *AccountSignOutResp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*AccountSignOutResp) ProtoMessage() {}

func (x *AccountSignOutResp) ProtoReflect() protoreflect.Message {
	mi := &file_svcaccount_proto_msgTypes[9]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use AccountSignOutResp.ProtoReflect.Descriptor instead.
func (*AccountSignOutResp) Descriptor() ([]byte, []int) {
	return file_svcaccount_proto_rawDescGZIP(), []int{9}
}

func (x *AccountSignOutResp) GetBase() *common.SvcBaseResp {
	if x != nil {
		return x.Base
	}
	return nil
}

type RefreshTokenReq struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	RefreshToken  string                 `protobuf:"bytes,1,opt,name=refresh_token,json=refreshToken,proto3" json:"refresh_token,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *RefreshTokenReq) Reset() {
	*x = RefreshTokenReq{}
	mi := &file_svcaccount_proto_msgTypes[10]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *RefreshTokenReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*RefreshTokenReq) ProtoMessage() {}

func (x *RefreshTokenReq) ProtoReflect() protoreflect.Message {
	mi := &file_svcaccount_proto_msgTypes[10]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use RefreshTokenReq.ProtoReflect.Descriptor instead.
func (*RefreshTokenReq) Descriptor() ([]byte, []int) {
	return file_svcaccount_proto_rawDescGZIP(), []int{10}
}

func (x *RefreshTokenReq) GetRefreshToken() string {
	if x != nil {
		return x.RefreshToken
	}
	return ""
}

type RefreshTokenResp struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Base          *common.SvcBaseResp    `protobuf:"bytes,1,opt,name=base,proto3" json:"base,omitempty"`
	Data          *TokenRespData         `protobuf:"bytes,2,opt,name=data,proto3" json:"data,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *RefreshTokenResp) Reset() {
	*x = RefreshTokenResp{}
	mi := &file_svcaccount_proto_msgTypes[11]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *RefreshTokenResp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*RefreshTokenResp) ProtoMessage() {}

func (x *RefreshTokenResp) ProtoReflect() protoreflect.Message {
	mi := &file_svcaccount_proto_msgTypes[11]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use RefreshTokenResp.ProtoReflect.Descriptor instead.
func (*RefreshTokenResp) Descriptor() ([]byte, []int) {
	return file_svcaccount_proto_rawDescGZIP(), []int{11}
}

func (x *RefreshTokenResp) GetBase() *common.SvcBaseResp {
	if x != nil {
		return x.Base
	}
	return nil
}

func (x *RefreshTokenResp) GetData() *TokenRespData {
	if x != nil {
		return x.Data
	}
	return nil
}

type AccountDeleteReq struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *AccountDeleteReq) Reset() {
	*x = AccountDeleteReq{}
	mi := &file_svcaccount_proto_msgTypes[12]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *AccountDeleteReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*AccountDeleteReq) ProtoMessage() {}

func (x *AccountDeleteReq) ProtoReflect() protoreflect.Message {
	mi := &file_svcaccount_proto_msgTypes[12]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use AccountDeleteReq.ProtoReflect.Descriptor instead.
func (*AccountDeleteReq) Descriptor() ([]byte, []int) {
	return file_svcaccount_proto_rawDescGZIP(), []int{12}
}

type AccountDeleteResp struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Base          *common.SvcBaseResp    `protobuf:"bytes,1,opt,name=base,proto3" json:"base,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *AccountDeleteResp) Reset() {
	*x = AccountDeleteResp{}
	mi := &file_svcaccount_proto_msgTypes[13]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *AccountDeleteResp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*AccountDeleteResp) ProtoMessage() {}

func (x *AccountDeleteResp) ProtoReflect() protoreflect.Message {
	mi := &file_svcaccount_proto_msgTypes[13]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use AccountDeleteResp.ProtoReflect.Descriptor instead.
func (*AccountDeleteResp) Descriptor() ([]byte, []int) {
	return file_svcaccount_proto_rawDescGZIP(), []int{13}
}

func (x *AccountDeleteResp) GetBase() *common.SvcBaseResp {
	if x != nil {
		return x.Base
	}
	return nil
}

type GetUserInfoReq struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	UserId        int64                  `protobuf:"varint,1,opt,name=user_id,json=userId,proto3" json:"user_id,omitempty"`
	WithFollow    bool                   `protobuf:"varint,2,opt,name=with_follow,json=withFollow,proto3" json:"with_follow,omitempty"` // 带关注信息
	WithScore     bool                   `protobuf:"varint,3,opt,name=with_score,json=withScore,proto3" json:"with_score,omitempty"`    // 带积分信息
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *GetUserInfoReq) Reset() {
	*x = GetUserInfoReq{}
	mi := &file_svcaccount_proto_msgTypes[14]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GetUserInfoReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetUserInfoReq) ProtoMessage() {}

func (x *GetUserInfoReq) ProtoReflect() protoreflect.Message {
	mi := &file_svcaccount_proto_msgTypes[14]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetUserInfoReq.ProtoReflect.Descriptor instead.
func (*GetUserInfoReq) Descriptor() ([]byte, []int) {
	return file_svcaccount_proto_rawDescGZIP(), []int{14}
}

func (x *GetUserInfoReq) GetUserId() int64 {
	if x != nil {
		return x.UserId
	}
	return 0
}

func (x *GetUserInfoReq) GetWithFollow() bool {
	if x != nil {
		return x.WithFollow
	}
	return false
}

func (x *GetUserInfoReq) GetWithScore() bool {
	if x != nil {
		return x.WithScore
	}
	return false
}

// 根据admin用户ID获取关联的业务用户信息
type GetUserInfoByCreationUserIdReq struct {
	state          protoimpl.MessageState `protogen:"open.v1"`
	CreationUserId uint32                 `protobuf:"varint,1,opt,name=creation_user_id,json=creationUserId,proto3" json:"creation_user_id,omitempty"` // admin用户ID
	WithFollow     bool                   `protobuf:"varint,2,opt,name=with_follow,json=withFollow,proto3" json:"with_follow,omitempty"`               // 带关注信息
	WithScore      bool                   `protobuf:"varint,3,opt,name=with_score,json=withScore,proto3" json:"with_score,omitempty"`                  // 带积分信息
	unknownFields  protoimpl.UnknownFields
	sizeCache      protoimpl.SizeCache
}

func (x *GetUserInfoByCreationUserIdReq) Reset() {
	*x = GetUserInfoByCreationUserIdReq{}
	mi := &file_svcaccount_proto_msgTypes[15]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GetUserInfoByCreationUserIdReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetUserInfoByCreationUserIdReq) ProtoMessage() {}

func (x *GetUserInfoByCreationUserIdReq) ProtoReflect() protoreflect.Message {
	mi := &file_svcaccount_proto_msgTypes[15]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetUserInfoByCreationUserIdReq.ProtoReflect.Descriptor instead.
func (*GetUserInfoByCreationUserIdReq) Descriptor() ([]byte, []int) {
	return file_svcaccount_proto_rawDescGZIP(), []int{15}
}

func (x *GetUserInfoByCreationUserIdReq) GetCreationUserId() uint32 {
	if x != nil {
		return x.CreationUserId
	}
	return 0
}

func (x *GetUserInfoByCreationUserIdReq) GetWithFollow() bool {
	if x != nil {
		return x.WithFollow
	}
	return false
}

func (x *GetUserInfoByCreationUserIdReq) GetWithScore() bool {
	if x != nil {
		return x.WithScore
	}
	return false
}

type GetUserInfoByCreationUserIdResp struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Base          *common.SvcBaseResp    `protobuf:"bytes,1,opt,name=base,proto3" json:"base,omitempty"`
	Data          *UserInfo              `protobuf:"bytes,2,opt,name=data,proto3" json:"data,omitempty"` // 基础信息
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *GetUserInfoByCreationUserIdResp) Reset() {
	*x = GetUserInfoByCreationUserIdResp{}
	mi := &file_svcaccount_proto_msgTypes[16]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GetUserInfoByCreationUserIdResp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetUserInfoByCreationUserIdResp) ProtoMessage() {}

func (x *GetUserInfoByCreationUserIdResp) ProtoReflect() protoreflect.Message {
	mi := &file_svcaccount_proto_msgTypes[16]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetUserInfoByCreationUserIdResp.ProtoReflect.Descriptor instead.
func (*GetUserInfoByCreationUserIdResp) Descriptor() ([]byte, []int) {
	return file_svcaccount_proto_rawDescGZIP(), []int{16}
}

func (x *GetUserInfoByCreationUserIdResp) GetBase() *common.SvcBaseResp {
	if x != nil {
		return x.Base
	}
	return nil
}

func (x *GetUserInfoByCreationUserIdResp) GetData() *UserInfo {
	if x != nil {
		return x.Data
	}
	return nil
}

type GetUserInfoResp struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Base          *common.SvcBaseResp    `protobuf:"bytes,1,opt,name=base,proto3" json:"base,omitempty"`
	Data          *UserInfo              `protobuf:"bytes,2,opt,name=data,proto3" json:"data,omitempty"` // 基础信息
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *GetUserInfoResp) Reset() {
	*x = GetUserInfoResp{}
	mi := &file_svcaccount_proto_msgTypes[17]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GetUserInfoResp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetUserInfoResp) ProtoMessage() {}

func (x *GetUserInfoResp) ProtoReflect() protoreflect.Message {
	mi := &file_svcaccount_proto_msgTypes[17]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetUserInfoResp.ProtoReflect.Descriptor instead.
func (*GetUserInfoResp) Descriptor() ([]byte, []int) {
	return file_svcaccount_proto_rawDescGZIP(), []int{17}
}

func (x *GetUserInfoResp) GetBase() *common.SvcBaseResp {
	if x != nil {
		return x.Base
	}
	return nil
}

func (x *GetUserInfoResp) GetData() *UserInfo {
	if x != nil {
		return x.Data
	}
	return nil
}

type UserInfo struct {
	state              protoimpl.MessageState `protogen:"open.v1"`
	UserId             int64                  `protobuf:"varint,1,opt,name=user_id,json=userId,proto3" json:"user_id,omitempty"`                                        // 用户ID
	Nickname           string                 `protobuf:"bytes,2,opt,name=nickname,proto3" json:"nickname,omitempty"`                                                   // 昵称
	Phone              string                 `protobuf:"bytes,3,opt,name=phone,proto3" json:"phone,omitempty"`                                                         // 手机号
	Avatar             string                 `protobuf:"bytes,4,opt,name=avatar,proto3" json:"avatar,omitempty"`                                                       // 头像
	Gender             int32                  `protobuf:"varint,5,opt,name=gender,proto3" json:"gender,omitempty"`                                                      // 性别
	Status             int32                  `protobuf:"varint,6,opt,name=status,proto3" json:"status,omitempty"`                                                      // 状态
	CreatedAt          int64                  `protobuf:"varint,7,opt,name=created_at,json=createdAt,proto3" json:"created_at,omitempty"`                               // 创建时间（毫秒）
	BackgroundUrl      string                 `protobuf:"bytes,8,opt,name=background_url,json=backgroundUrl,proto3" json:"background_url,omitempty"`                    // 背景图
	VoiceSignature     *VoiceSignature        `protobuf:"bytes,9,opt,name=voice_signature,json=voiceSignature,proto3" json:"voice_signature,omitempty"`                 // 语音签名
	IsPremiumCreator   bool                   `protobuf:"varint,10,opt,name=is_premium_creator,json=isPremiumCreator,proto3" json:"is_premium_creator,omitempty"`       //是否优质创作者
	FollowersCount     int64                  `protobuf:"varint,11,opt,name=followers_count,json=followersCount,proto3" json:"followers_count,omitempty"`               // 粉丝数
	FollowingCount     int64                  `protobuf:"varint,12,opt,name=following_count,json=followingCount,proto3" json:"following_count,omitempty"`               // 关注数
	IsFollowing        bool                   `protobuf:"varint,13,opt,name=is_following,json=isFollowing,proto3" json:"is_following,omitempty"`                        // 当前用户是否关注了该用户
	IsFollowed         bool                   `protobuf:"varint,14,opt,name=is_followed,json=isFollowed,proto3" json:"is_followed,omitempty"`                           // 该用户是否关注了当前用户
	ScoreData          *UserScoreData         `protobuf:"bytes,15,opt,name=score_data,json=scoreData,proto3" json:"score_data,omitempty"`                               // 分贝数据
	ScriptCount        int32                  `protobuf:"varint,16,opt,name=script_count,json=scriptCount,proto3" json:"script_count,omitempty"`                        //剧本数
	UserCharacterCount int32                  `protobuf:"varint,17,opt,name=user_character_count,json=userCharacterCount,proto3" json:"user_character_count,omitempty"` // 用户角色数（音包数）
	IsDeleted          bool                   `protobuf:"varint,18,opt,name=is_deleted,json=isDeleted,proto3" json:"is_deleted,omitempty"`                              // 是否已注销
	CreationUserId     uint32                 `protobuf:"varint,19,opt,name=creation_user_id,json=creationUserId,proto3" json:"creation_user_id,omitempty"`             // 创建该用户的admin用户ID
	unknownFields      protoimpl.UnknownFields
	sizeCache          protoimpl.SizeCache
}

func (x *UserInfo) Reset() {
	*x = UserInfo{}
	mi := &file_svcaccount_proto_msgTypes[18]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *UserInfo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UserInfo) ProtoMessage() {}

func (x *UserInfo) ProtoReflect() protoreflect.Message {
	mi := &file_svcaccount_proto_msgTypes[18]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UserInfo.ProtoReflect.Descriptor instead.
func (*UserInfo) Descriptor() ([]byte, []int) {
	return file_svcaccount_proto_rawDescGZIP(), []int{18}
}

func (x *UserInfo) GetUserId() int64 {
	if x != nil {
		return x.UserId
	}
	return 0
}

func (x *UserInfo) GetNickname() string {
	if x != nil {
		return x.Nickname
	}
	return ""
}

func (x *UserInfo) GetPhone() string {
	if x != nil {
		return x.Phone
	}
	return ""
}

func (x *UserInfo) GetAvatar() string {
	if x != nil {
		return x.Avatar
	}
	return ""
}

func (x *UserInfo) GetGender() int32 {
	if x != nil {
		return x.Gender
	}
	return 0
}

func (x *UserInfo) GetStatus() int32 {
	if x != nil {
		return x.Status
	}
	return 0
}

func (x *UserInfo) GetCreatedAt() int64 {
	if x != nil {
		return x.CreatedAt
	}
	return 0
}

func (x *UserInfo) GetBackgroundUrl() string {
	if x != nil {
		return x.BackgroundUrl
	}
	return ""
}

func (x *UserInfo) GetVoiceSignature() *VoiceSignature {
	if x != nil {
		return x.VoiceSignature
	}
	return nil
}

func (x *UserInfo) GetIsPremiumCreator() bool {
	if x != nil {
		return x.IsPremiumCreator
	}
	return false
}

func (x *UserInfo) GetFollowersCount() int64 {
	if x != nil {
		return x.FollowersCount
	}
	return 0
}

func (x *UserInfo) GetFollowingCount() int64 {
	if x != nil {
		return x.FollowingCount
	}
	return 0
}

func (x *UserInfo) GetIsFollowing() bool {
	if x != nil {
		return x.IsFollowing
	}
	return false
}

func (x *UserInfo) GetIsFollowed() bool {
	if x != nil {
		return x.IsFollowed
	}
	return false
}

func (x *UserInfo) GetScoreData() *UserScoreData {
	if x != nil {
		return x.ScoreData
	}
	return nil
}

func (x *UserInfo) GetScriptCount() int32 {
	if x != nil {
		return x.ScriptCount
	}
	return 0
}

func (x *UserInfo) GetUserCharacterCount() int32 {
	if x != nil {
		return x.UserCharacterCount
	}
	return 0
}

func (x *UserInfo) GetIsDeleted() bool {
	if x != nil {
		return x.IsDeleted
	}
	return false
}

func (x *UserInfo) GetCreationUserId() uint32 {
	if x != nil {
		return x.CreationUserId
	}
	return 0
}

type FollowData struct {
	state          protoimpl.MessageState `protogen:"open.v1"`
	FollowersCount int64                  `protobuf:"varint,8,opt,name=followers_count,json=followersCount,proto3" json:"followers_count,omitempty"` // 粉丝数
	FollowingCount int64                  `protobuf:"varint,9,opt,name=following_count,json=followingCount,proto3" json:"following_count,omitempty"` // 关注数
	IsFollowing    bool                   `protobuf:"varint,10,opt,name=is_following,json=isFollowing,proto3" json:"is_following,omitempty"`         // 当前用户是否关注了该用户
	IsFollowed     bool                   `protobuf:"varint,11,opt,name=is_followed,json=isFollowed,proto3" json:"is_followed,omitempty"`            // 该用户是否关注了当前用户
	unknownFields  protoimpl.UnknownFields
	sizeCache      protoimpl.SizeCache
}

func (x *FollowData) Reset() {
	*x = FollowData{}
	mi := &file_svcaccount_proto_msgTypes[19]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *FollowData) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*FollowData) ProtoMessage() {}

func (x *FollowData) ProtoReflect() protoreflect.Message {
	mi := &file_svcaccount_proto_msgTypes[19]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use FollowData.ProtoReflect.Descriptor instead.
func (*FollowData) Descriptor() ([]byte, []int) {
	return file_svcaccount_proto_rawDescGZIP(), []int{19}
}

func (x *FollowData) GetFollowersCount() int64 {
	if x != nil {
		return x.FollowersCount
	}
	return 0
}

func (x *FollowData) GetFollowingCount() int64 {
	if x != nil {
		return x.FollowingCount
	}
	return 0
}

func (x *FollowData) GetIsFollowing() bool {
	if x != nil {
		return x.IsFollowing
	}
	return false
}

func (x *FollowData) GetIsFollowed() bool {
	if x != nil {
		return x.IsFollowed
	}
	return false
}

type VoiceSignature struct {
	state             protoimpl.MessageState `protogen:"open.v1"`
	VoiceSignatureUrl string                 `protobuf:"bytes,1,opt,name=voice_signature_url,json=voiceSignatureUrl,proto3" json:"voice_signature_url,omitempty"` // 语音签名
	Duration          int32                  `protobuf:"varint,2,opt,name=duration,proto3" json:"duration,omitempty"`                                             //时长（秒）
	IsReviewing       bool                   `protobuf:"varint,3,opt,name=is_reviewing,json=isReviewing,proto3" json:"is_reviewing,omitempty"`                    //是否正在审核中
	unknownFields     protoimpl.UnknownFields
	sizeCache         protoimpl.SizeCache
}

func (x *VoiceSignature) Reset() {
	*x = VoiceSignature{}
	mi := &file_svcaccount_proto_msgTypes[20]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *VoiceSignature) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*VoiceSignature) ProtoMessage() {}

func (x *VoiceSignature) ProtoReflect() protoreflect.Message {
	mi := &file_svcaccount_proto_msgTypes[20]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use VoiceSignature.ProtoReflect.Descriptor instead.
func (*VoiceSignature) Descriptor() ([]byte, []int) {
	return file_svcaccount_proto_rawDescGZIP(), []int{20}
}

func (x *VoiceSignature) GetVoiceSignatureUrl() string {
	if x != nil {
		return x.VoiceSignatureUrl
	}
	return ""
}

func (x *VoiceSignature) GetDuration() int32 {
	if x != nil {
		return x.Duration
	}
	return 0
}

func (x *VoiceSignature) GetIsReviewing() bool {
	if x != nil {
		return x.IsReviewing
	}
	return false
}

// 批量获取用户信息请求
type BatchGetUserInfoReq struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	UserId        int64                  `protobuf:"varint,1,opt,name=user_id,json=userId,proto3" json:"user_id,omitempty"`           //当前用户ID
	UserIds       []int64                `protobuf:"varint,2,rep,packed,name=user_ids,json=userIds,proto3" json:"user_ids,omitempty"` // 用户ID列表
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *BatchGetUserInfoReq) Reset() {
	*x = BatchGetUserInfoReq{}
	mi := &file_svcaccount_proto_msgTypes[21]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *BatchGetUserInfoReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BatchGetUserInfoReq) ProtoMessage() {}

func (x *BatchGetUserInfoReq) ProtoReflect() protoreflect.Message {
	mi := &file_svcaccount_proto_msgTypes[21]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BatchGetUserInfoReq.ProtoReflect.Descriptor instead.
func (*BatchGetUserInfoReq) Descriptor() ([]byte, []int) {
	return file_svcaccount_proto_rawDescGZIP(), []int{21}
}

func (x *BatchGetUserInfoReq) GetUserId() int64 {
	if x != nil {
		return x.UserId
	}
	return 0
}

func (x *BatchGetUserInfoReq) GetUserIds() []int64 {
	if x != nil {
		return x.UserIds
	}
	return nil
}

// 批量获取用户信息响应
type BatchGetUserInfoResp struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Base          *common.SvcBaseResp    `protobuf:"bytes,1,opt,name=base,proto3" json:"base,omitempty"`
	UserInfoMap   map[int64]*UserInfo    `protobuf:"bytes,2,rep,name=user_info_map,json=userInfoMap,proto3" json:"user_info_map,omitempty" protobuf_key:"varint,1,opt,name=key" protobuf_val:"bytes,2,opt,name=value"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *BatchGetUserInfoResp) Reset() {
	*x = BatchGetUserInfoResp{}
	mi := &file_svcaccount_proto_msgTypes[22]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *BatchGetUserInfoResp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BatchGetUserInfoResp) ProtoMessage() {}

func (x *BatchGetUserInfoResp) ProtoReflect() protoreflect.Message {
	mi := &file_svcaccount_proto_msgTypes[22]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BatchGetUserInfoResp.ProtoReflect.Descriptor instead.
func (*BatchGetUserInfoResp) Descriptor() ([]byte, []int) {
	return file_svcaccount_proto_rawDescGZIP(), []int{22}
}

func (x *BatchGetUserInfoResp) GetBase() *common.SvcBaseResp {
	if x != nil {
		return x.Base
	}
	return nil
}

func (x *BatchGetUserInfoResp) GetUserInfoMap() map[int64]*UserInfo {
	if x != nil {
		return x.UserInfoMap
	}
	return nil
}

// 更新用户
type UpdateUserInfoReq struct {
	state               protoimpl.MessageState `protogen:"open.v1"`
	UserId              int64                  `protobuf:"varint,1,opt,name=user_id,json=userId,proto3" json:"user_id,omitempty"`                                          // 用户ID
	Avatar              string                 `protobuf:"bytes,2,opt,name=avatar,proto3" json:"avatar,omitempty"`                                                         // 头像URL
	Nickname            string                 `protobuf:"bytes,3,opt,name=nickname,proto3" json:"nickname,omitempty"`                                                     // 昵称
	BackgroundUrl       string                 `protobuf:"bytes,4,opt,name=background_url,json=backgroundUrl,proto3" json:"background_url,omitempty"`                      // 背景图
	VoiceSignatureUrl   string                 `protobuf:"bytes,5,opt,name=voice_signature_url,json=voiceSignatureUrl,proto3" json:"voice_signature_url,omitempty"`        // 语音签名
	VoiceDuration       int32                  `protobuf:"varint,6,opt,name=voice_duration,json=voiceDuration,proto3" json:"voice_duration,omitempty"`                     //语音时长
	IsPremiumCreator    bool                   `protobuf:"varint,7,opt,name=is_premium_creator,json=isPremiumCreator,proto3" json:"is_premium_creator,omitempty"`          //是否优质创作者
	ResetVoiceSignature bool                   `protobuf:"varint,8,opt,name=reset_voice_signature,json=resetVoiceSignature,proto3" json:"reset_voice_signature,omitempty"` // 是否重置语音签名
	CreationUserId      uint32                 `protobuf:"varint,9,opt,name=creation_user_id,json=creationUserId,proto3" json:"creation_user_id,omitempty"`                // 关联的创作者admin用户ID
	unknownFields       protoimpl.UnknownFields
	sizeCache           protoimpl.SizeCache
}

func (x *UpdateUserInfoReq) Reset() {
	*x = UpdateUserInfoReq{}
	mi := &file_svcaccount_proto_msgTypes[23]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *UpdateUserInfoReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UpdateUserInfoReq) ProtoMessage() {}

func (x *UpdateUserInfoReq) ProtoReflect() protoreflect.Message {
	mi := &file_svcaccount_proto_msgTypes[23]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UpdateUserInfoReq.ProtoReflect.Descriptor instead.
func (*UpdateUserInfoReq) Descriptor() ([]byte, []int) {
	return file_svcaccount_proto_rawDescGZIP(), []int{23}
}

func (x *UpdateUserInfoReq) GetUserId() int64 {
	if x != nil {
		return x.UserId
	}
	return 0
}

func (x *UpdateUserInfoReq) GetAvatar() string {
	if x != nil {
		return x.Avatar
	}
	return ""
}

func (x *UpdateUserInfoReq) GetNickname() string {
	if x != nil {
		return x.Nickname
	}
	return ""
}

func (x *UpdateUserInfoReq) GetBackgroundUrl() string {
	if x != nil {
		return x.BackgroundUrl
	}
	return ""
}

func (x *UpdateUserInfoReq) GetVoiceSignatureUrl() string {
	if x != nil {
		return x.VoiceSignatureUrl
	}
	return ""
}

func (x *UpdateUserInfoReq) GetVoiceDuration() int32 {
	if x != nil {
		return x.VoiceDuration
	}
	return 0
}

func (x *UpdateUserInfoReq) GetIsPremiumCreator() bool {
	if x != nil {
		return x.IsPremiumCreator
	}
	return false
}

func (x *UpdateUserInfoReq) GetResetVoiceSignature() bool {
	if x != nil {
		return x.ResetVoiceSignature
	}
	return false
}

func (x *UpdateUserInfoReq) GetCreationUserId() uint32 {
	if x != nil {
		return x.CreationUserId
	}
	return 0
}

// 更新用户
type UpdateUserInfoResp struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Base          *common.SvcBaseResp    `protobuf:"bytes,1,opt,name=base,proto3" json:"base,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *UpdateUserInfoResp) Reset() {
	*x = UpdateUserInfoResp{}
	mi := &file_svcaccount_proto_msgTypes[24]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *UpdateUserInfoResp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UpdateUserInfoResp) ProtoMessage() {}

func (x *UpdateUserInfoResp) ProtoReflect() protoreflect.Message {
	mi := &file_svcaccount_proto_msgTypes[24]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UpdateUserInfoResp.ProtoReflect.Descriptor instead.
func (*UpdateUserInfoResp) Descriptor() ([]byte, []int) {
	return file_svcaccount_proto_rawDescGZIP(), []int{24}
}

func (x *UpdateUserInfoResp) GetBase() *common.SvcBaseResp {
	if x != nil {
		return x.Base
	}
	return nil
}

// 设置用户为优质创作者请求
type SetPremiumCreatorReq struct {
	state            protoimpl.MessageState `protogen:"open.v1"`
	UserId           int64                  `protobuf:"varint,1,opt,name=user_id,json=userId,proto3" json:"user_id,omitempty"`                                 // 用户ID
	IsPremiumCreator bool                   `protobuf:"varint,2,opt,name=is_premium_creator,json=isPremiumCreator,proto3" json:"is_premium_creator,omitempty"` // 是否设置为优质创作者
	unknownFields    protoimpl.UnknownFields
	sizeCache        protoimpl.SizeCache
}

func (x *SetPremiumCreatorReq) Reset() {
	*x = SetPremiumCreatorReq{}
	mi := &file_svcaccount_proto_msgTypes[25]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *SetPremiumCreatorReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SetPremiumCreatorReq) ProtoMessage() {}

func (x *SetPremiumCreatorReq) ProtoReflect() protoreflect.Message {
	mi := &file_svcaccount_proto_msgTypes[25]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SetPremiumCreatorReq.ProtoReflect.Descriptor instead.
func (*SetPremiumCreatorReq) Descriptor() ([]byte, []int) {
	return file_svcaccount_proto_rawDescGZIP(), []int{25}
}

func (x *SetPremiumCreatorReq) GetUserId() int64 {
	if x != nil {
		return x.UserId
	}
	return 0
}

func (x *SetPremiumCreatorReq) GetIsPremiumCreator() bool {
	if x != nil {
		return x.IsPremiumCreator
	}
	return false
}

// 设置用户为优质创作者响应
type SetPremiumCreatorResp struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Base          *common.SvcBaseResp    `protobuf:"bytes,1,opt,name=base,proto3" json:"base,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *SetPremiumCreatorResp) Reset() {
	*x = SetPremiumCreatorResp{}
	mi := &file_svcaccount_proto_msgTypes[26]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *SetPremiumCreatorResp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SetPremiumCreatorResp) ProtoMessage() {}

func (x *SetPremiumCreatorResp) ProtoReflect() protoreflect.Message {
	mi := &file_svcaccount_proto_msgTypes[26]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SetPremiumCreatorResp.ProtoReflect.Descriptor instead.
func (*SetPremiumCreatorResp) Descriptor() ([]byte, []int) {
	return file_svcaccount_proto_rawDescGZIP(), []int{26}
}

func (x *SetPremiumCreatorResp) GetBase() *common.SvcBaseResp {
	if x != nil {
		return x.Base
	}
	return nil
}

type AccountOnlineReq struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Data          *common.BaseParam      `protobuf:"bytes,1,opt,name=data,proto3" json:"data,omitempty"`
	Userid        int64                  `protobuf:"varint,2,opt,name=userid,proto3" json:"userid,omitempty"`
	State         bool                   `protobuf:"varint,3,opt,name=state,proto3" json:"state,omitempty"`
	Heartbeat     bool                   `protobuf:"varint,4,opt,name=heartbeat,proto3" json:"heartbeat,omitempty"`
	NeedResp      bool                   `protobuf:"varint,5,opt,name=need_resp,json=needResp,proto3" json:"need_resp,omitempty"`
	Front         bool                   `protobuf:"varint,6,opt,name=front,proto3" json:"front,omitempty"`
	OnConnectType OnConnectType          `protobuf:"varint,7,opt,name=on_connect_type,json=onConnectType,proto3,enum=vc.svcaccount.OnConnectType" json:"on_connect_type,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *AccountOnlineReq) Reset() {
	*x = AccountOnlineReq{}
	mi := &file_svcaccount_proto_msgTypes[27]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *AccountOnlineReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*AccountOnlineReq) ProtoMessage() {}

func (x *AccountOnlineReq) ProtoReflect() protoreflect.Message {
	mi := &file_svcaccount_proto_msgTypes[27]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use AccountOnlineReq.ProtoReflect.Descriptor instead.
func (*AccountOnlineReq) Descriptor() ([]byte, []int) {
	return file_svcaccount_proto_rawDescGZIP(), []int{27}
}

func (x *AccountOnlineReq) GetData() *common.BaseParam {
	if x != nil {
		return x.Data
	}
	return nil
}

func (x *AccountOnlineReq) GetUserid() int64 {
	if x != nil {
		return x.Userid
	}
	return 0
}

func (x *AccountOnlineReq) GetState() bool {
	if x != nil {
		return x.State
	}
	return false
}

func (x *AccountOnlineReq) GetHeartbeat() bool {
	if x != nil {
		return x.Heartbeat
	}
	return false
}

func (x *AccountOnlineReq) GetNeedResp() bool {
	if x != nil {
		return x.NeedResp
	}
	return false
}

func (x *AccountOnlineReq) GetFront() bool {
	if x != nil {
		return x.Front
	}
	return false
}

func (x *AccountOnlineReq) GetOnConnectType() OnConnectType {
	if x != nil {
		return x.OnConnectType
	}
	return OnConnectType_invalid
}

type AccountOnlineResp struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Base          *common.SvcBaseResp    `protobuf:"bytes,1,opt,name=base,proto3" json:"base,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *AccountOnlineResp) Reset() {
	*x = AccountOnlineResp{}
	mi := &file_svcaccount_proto_msgTypes[28]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *AccountOnlineResp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*AccountOnlineResp) ProtoMessage() {}

func (x *AccountOnlineResp) ProtoReflect() protoreflect.Message {
	mi := &file_svcaccount_proto_msgTypes[28]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use AccountOnlineResp.ProtoReflect.Descriptor instead.
func (*AccountOnlineResp) Descriptor() ([]byte, []int) {
	return file_svcaccount_proto_rawDescGZIP(), []int{28}
}

func (x *AccountOnlineResp) GetBase() *common.SvcBaseResp {
	if x != nil {
		return x.Base
	}
	return nil
}

type IsUserBlacklistReq struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	UserId        int64                  `protobuf:"varint,1,opt,name=user_id,json=userId,proto3" json:"user_id,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *IsUserBlacklistReq) Reset() {
	*x = IsUserBlacklistReq{}
	mi := &file_svcaccount_proto_msgTypes[29]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *IsUserBlacklistReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*IsUserBlacklistReq) ProtoMessage() {}

func (x *IsUserBlacklistReq) ProtoReflect() protoreflect.Message {
	mi := &file_svcaccount_proto_msgTypes[29]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use IsUserBlacklistReq.ProtoReflect.Descriptor instead.
func (*IsUserBlacklistReq) Descriptor() ([]byte, []int) {
	return file_svcaccount_proto_rawDescGZIP(), []int{29}
}

func (x *IsUserBlacklistReq) GetUserId() int64 {
	if x != nil {
		return x.UserId
	}
	return 0
}

type IsUserBlacklistResp struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Base          *common.SvcBaseResp    `protobuf:"bytes,1,opt,name=base,proto3" json:"base,omitempty"`
	IsBlacklist   bool                   `protobuf:"varint,2,opt,name=is_blacklist,json=isBlacklist,proto3" json:"is_blacklist,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *IsUserBlacklistResp) Reset() {
	*x = IsUserBlacklistResp{}
	mi := &file_svcaccount_proto_msgTypes[30]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *IsUserBlacklistResp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*IsUserBlacklistResp) ProtoMessage() {}

func (x *IsUserBlacklistResp) ProtoReflect() protoreflect.Message {
	mi := &file_svcaccount_proto_msgTypes[30]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use IsUserBlacklistResp.ProtoReflect.Descriptor instead.
func (*IsUserBlacklistResp) Descriptor() ([]byte, []int) {
	return file_svcaccount_proto_rawDescGZIP(), []int{30}
}

func (x *IsUserBlacklistResp) GetBase() *common.SvcBaseResp {
	if x != nil {
		return x.Base
	}
	return nil
}

func (x *IsUserBlacklistResp) GetIsBlacklist() bool {
	if x != nil {
		return x.IsBlacklist
	}
	return false
}

type GetUserScoreReq struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	UserId        int64                  `protobuf:"varint,1,opt,name=user_id,json=userId,proto3" json:"user_id,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *GetUserScoreReq) Reset() {
	*x = GetUserScoreReq{}
	mi := &file_svcaccount_proto_msgTypes[31]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GetUserScoreReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetUserScoreReq) ProtoMessage() {}

func (x *GetUserScoreReq) ProtoReflect() protoreflect.Message {
	mi := &file_svcaccount_proto_msgTypes[31]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetUserScoreReq.ProtoReflect.Descriptor instead.
func (*GetUserScoreReq) Descriptor() ([]byte, []int) {
	return file_svcaccount_proto_rawDescGZIP(), []int{31}
}

func (x *GetUserScoreReq) GetUserId() int64 {
	if x != nil {
		return x.UserId
	}
	return 0
}

type GetUserScoreResp struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Base          *common.SvcBaseResp    `protobuf:"bytes,1,opt,name=base,proto3" json:"base,omitempty"`
	Data          *UserScoreData         `protobuf:"bytes,2,opt,name=data,proto3" json:"data,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *GetUserScoreResp) Reset() {
	*x = GetUserScoreResp{}
	mi := &file_svcaccount_proto_msgTypes[32]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GetUserScoreResp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetUserScoreResp) ProtoMessage() {}

func (x *GetUserScoreResp) ProtoReflect() protoreflect.Message {
	mi := &file_svcaccount_proto_msgTypes[32]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetUserScoreResp.ProtoReflect.Descriptor instead.
func (*GetUserScoreResp) Descriptor() ([]byte, []int) {
	return file_svcaccount_proto_rawDescGZIP(), []int{32}
}

func (x *GetUserScoreResp) GetBase() *common.SvcBaseResp {
	if x != nil {
		return x.Base
	}
	return nil
}

func (x *GetUserScoreResp) GetData() *UserScoreData {
	if x != nil {
		return x.Data
	}
	return nil
}

type AddUserScoreReq struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// 用户ID
	UserId int64 `protobuf:"varint,1,opt,name=user_id,json=userId,proto3" json:"user_id,omitempty"`
	// 积分
	ValuableScore int64 `protobuf:"varint,2,opt,name=valuable_score,json=valuableScore,proto3" json:"valuable_score,omitempty"`
	// 免费积分
	FeeScore int64 `protobuf:"varint,3,opt,name=fee_score,json=feeScore,proto3" json:"fee_score,omitempty"`
	// 积分来源
	BizType int64 `protobuf:"varint,4,opt,name=biz_type,json=bizType,proto3" json:"biz_type,omitempty"`
	// 积分来源名
	BizName string `protobuf:"bytes,5,opt,name=biz_name,json=bizName,proto3" json:"biz_name,omitempty"`
	// 订单ID
	OrderId string `protobuf:"bytes,6,opt,name=order_id,json=orderId,proto3" json:"order_id,omitempty"`
	// 备注
	Remark        string `protobuf:"bytes,7,opt,name=remark,proto3" json:"remark,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *AddUserScoreReq) Reset() {
	*x = AddUserScoreReq{}
	mi := &file_svcaccount_proto_msgTypes[33]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *AddUserScoreReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*AddUserScoreReq) ProtoMessage() {}

func (x *AddUserScoreReq) ProtoReflect() protoreflect.Message {
	mi := &file_svcaccount_proto_msgTypes[33]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use AddUserScoreReq.ProtoReflect.Descriptor instead.
func (*AddUserScoreReq) Descriptor() ([]byte, []int) {
	return file_svcaccount_proto_rawDescGZIP(), []int{33}
}

func (x *AddUserScoreReq) GetUserId() int64 {
	if x != nil {
		return x.UserId
	}
	return 0
}

func (x *AddUserScoreReq) GetValuableScore() int64 {
	if x != nil {
		return x.ValuableScore
	}
	return 0
}

func (x *AddUserScoreReq) GetFeeScore() int64 {
	if x != nil {
		return x.FeeScore
	}
	return 0
}

func (x *AddUserScoreReq) GetBizType() int64 {
	if x != nil {
		return x.BizType
	}
	return 0
}

func (x *AddUserScoreReq) GetBizName() string {
	if x != nil {
		return x.BizName
	}
	return ""
}

func (x *AddUserScoreReq) GetOrderId() string {
	if x != nil {
		return x.OrderId
	}
	return ""
}

func (x *AddUserScoreReq) GetRemark() string {
	if x != nil {
		return x.Remark
	}
	return ""
}

type AddUserScoreResp struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Base          *common.SvcBaseResp    `protobuf:"bytes,1,opt,name=base,proto3" json:"base,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *AddUserScoreResp) Reset() {
	*x = AddUserScoreResp{}
	mi := &file_svcaccount_proto_msgTypes[34]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *AddUserScoreResp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*AddUserScoreResp) ProtoMessage() {}

func (x *AddUserScoreResp) ProtoReflect() protoreflect.Message {
	mi := &file_svcaccount_proto_msgTypes[34]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use AddUserScoreResp.ProtoReflect.Descriptor instead.
func (*AddUserScoreResp) Descriptor() ([]byte, []int) {
	return file_svcaccount_proto_rawDescGZIP(), []int{34}
}

func (x *AddUserScoreResp) GetBase() *common.SvcBaseResp {
	if x != nil {
		return x.Base
	}
	return nil
}

type ConsumeUserScoreReq struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// 用户ID
	UserId int64 `protobuf:"varint,1,opt,name=user_id,json=userId,proto3" json:"user_id,omitempty"`
	// 积分
	Score int64 `protobuf:"varint,2,opt,name=score,proto3" json:"score,omitempty"`
	// 积分来源
	BizType int64 `protobuf:"varint,3,opt,name=biz_type,json=bizType,proto3" json:"biz_type,omitempty"`
	// 积分来源名
	BizName string `protobuf:"bytes,4,opt,name=biz_name,json=bizName,proto3" json:"biz_name,omitempty"`
	// 订单ID
	OrderId string `protobuf:"bytes,5,opt,name=order_id,json=orderId,proto3" json:"order_id,omitempty"`
	// 允许透支消费，超额扣为0
	AllowOverdraw bool `protobuf:"varint,7,opt,name=allow_overdraw,json=allowOverdraw,proto3" json:"allow_overdraw,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *ConsumeUserScoreReq) Reset() {
	*x = ConsumeUserScoreReq{}
	mi := &file_svcaccount_proto_msgTypes[35]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ConsumeUserScoreReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ConsumeUserScoreReq) ProtoMessage() {}

func (x *ConsumeUserScoreReq) ProtoReflect() protoreflect.Message {
	mi := &file_svcaccount_proto_msgTypes[35]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ConsumeUserScoreReq.ProtoReflect.Descriptor instead.
func (*ConsumeUserScoreReq) Descriptor() ([]byte, []int) {
	return file_svcaccount_proto_rawDescGZIP(), []int{35}
}

func (x *ConsumeUserScoreReq) GetUserId() int64 {
	if x != nil {
		return x.UserId
	}
	return 0
}

func (x *ConsumeUserScoreReq) GetScore() int64 {
	if x != nil {
		return x.Score
	}
	return 0
}

func (x *ConsumeUserScoreReq) GetBizType() int64 {
	if x != nil {
		return x.BizType
	}
	return 0
}

func (x *ConsumeUserScoreReq) GetBizName() string {
	if x != nil {
		return x.BizName
	}
	return ""
}

func (x *ConsumeUserScoreReq) GetOrderId() string {
	if x != nil {
		return x.OrderId
	}
	return ""
}

func (x *ConsumeUserScoreReq) GetAllowOverdraw() bool {
	if x != nil {
		return x.AllowOverdraw
	}
	return false
}

type ConsumeUserScoreResp struct {
	state         protoimpl.MessageState    `protogen:"open.v1"`
	Base          *common.SvcBaseResp       `protobuf:"bytes,1,opt,name=base,proto3" json:"base,omitempty"`
	Data          *ConsumeUserScoreRespData `protobuf:"bytes,2,opt,name=data,proto3" json:"data,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *ConsumeUserScoreResp) Reset() {
	*x = ConsumeUserScoreResp{}
	mi := &file_svcaccount_proto_msgTypes[36]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ConsumeUserScoreResp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ConsumeUserScoreResp) ProtoMessage() {}

func (x *ConsumeUserScoreResp) ProtoReflect() protoreflect.Message {
	mi := &file_svcaccount_proto_msgTypes[36]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ConsumeUserScoreResp.ProtoReflect.Descriptor instead.
func (*ConsumeUserScoreResp) Descriptor() ([]byte, []int) {
	return file_svcaccount_proto_rawDescGZIP(), []int{36}
}

func (x *ConsumeUserScoreResp) GetBase() *common.SvcBaseResp {
	if x != nil {
		return x.Base
	}
	return nil
}

func (x *ConsumeUserScoreResp) GetData() *ConsumeUserScoreRespData {
	if x != nil {
		return x.Data
	}
	return nil
}

type ConsumeUserScoreRespData struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// 消耗的有价值积分
	ConsumeValuableScore int64 `protobuf:"varint,1,opt,name=consume_valuable_score,json=consumeValuableScore,proto3" json:"consume_valuable_score,omitempty"`
	// 消耗免费积分
	ConsumeFreeScore int64 `protobuf:"varint,2,opt,name=consume_free_score,json=consumeFreeScore,proto3" json:"consume_free_score,omitempty"`
	unknownFields    protoimpl.UnknownFields
	sizeCache        protoimpl.SizeCache
}

func (x *ConsumeUserScoreRespData) Reset() {
	*x = ConsumeUserScoreRespData{}
	mi := &file_svcaccount_proto_msgTypes[37]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ConsumeUserScoreRespData) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ConsumeUserScoreRespData) ProtoMessage() {}

func (x *ConsumeUserScoreRespData) ProtoReflect() protoreflect.Message {
	mi := &file_svcaccount_proto_msgTypes[37]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ConsumeUserScoreRespData.ProtoReflect.Descriptor instead.
func (*ConsumeUserScoreRespData) Descriptor() ([]byte, []int) {
	return file_svcaccount_proto_rawDescGZIP(), []int{37}
}

func (x *ConsumeUserScoreRespData) GetConsumeValuableScore() int64 {
	if x != nil {
		return x.ConsumeValuableScore
	}
	return 0
}

func (x *ConsumeUserScoreRespData) GetConsumeFreeScore() int64 {
	if x != nil {
		return x.ConsumeFreeScore
	}
	return 0
}

type UserScoreData struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// 总积分余额（包含免费，有价值) 注意放大100倍
	TotalBalance int64 `protobuf:"varint,1,opt,name=total_balance,json=totalBalance,proto3" json:"total_balance,omitempty"`
	// 免费积分， 注意放大100倍
	FreeBalance int64 `protobuf:"varint,2,opt,name=free_balance,json=freeBalance,proto3" json:"free_balance,omitempty"`
	// 有价值积分, 注意放大100倍
	ValuableBalance int64 `protobuf:"varint,3,opt,name=valuable_balance,json=valuableBalance,proto3" json:"valuable_balance,omitempty"`
	// 历史累计总有价值积分
	TotalValuableScore int64 `protobuf:"varint,4,opt,name=total_valuable_score,json=totalValuableScore,proto3" json:"total_valuable_score,omitempty"`
	// 历史累计免费价值积分
	TotalFreeScore      int64  `protobuf:"varint,5,opt,name=total_free_score,json=totalFreeScore,proto3" json:"total_free_score,omitempty"`
	DisplayTotalBalance string `protobuf:"bytes,11,opt,name=display_total_balance,json=displayTotalBalance,proto3" json:"display_total_balance,omitempty"` // 格式化后的总积分余额
	unknownFields       protoimpl.UnknownFields
	sizeCache           protoimpl.SizeCache
}

func (x *UserScoreData) Reset() {
	*x = UserScoreData{}
	mi := &file_svcaccount_proto_msgTypes[38]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *UserScoreData) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UserScoreData) ProtoMessage() {}

func (x *UserScoreData) ProtoReflect() protoreflect.Message {
	mi := &file_svcaccount_proto_msgTypes[38]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UserScoreData.ProtoReflect.Descriptor instead.
func (*UserScoreData) Descriptor() ([]byte, []int) {
	return file_svcaccount_proto_rawDescGZIP(), []int{38}
}

func (x *UserScoreData) GetTotalBalance() int64 {
	if x != nil {
		return x.TotalBalance
	}
	return 0
}

func (x *UserScoreData) GetFreeBalance() int64 {
	if x != nil {
		return x.FreeBalance
	}
	return 0
}

func (x *UserScoreData) GetValuableBalance() int64 {
	if x != nil {
		return x.ValuableBalance
	}
	return 0
}

func (x *UserScoreData) GetTotalValuableScore() int64 {
	if x != nil {
		return x.TotalValuableScore
	}
	return 0
}

func (x *UserScoreData) GetTotalFreeScore() int64 {
	if x != nil {
		return x.TotalFreeScore
	}
	return 0
}

func (x *UserScoreData) GetDisplayTotalBalance() string {
	if x != nil {
		return x.DisplayTotalBalance
	}
	return ""
}

// 搜索用户请求
type SearchUsersReq struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Keyword       string                 `protobuf:"bytes,1,opt,name=keyword,proto3" json:"keyword,omitempty"`                    // 搜索关键词
	Page          int32                  `protobuf:"varint,2,opt,name=page,proto3" json:"page,omitempty"`                         // 页码
	PageSize      int32                  `protobuf:"varint,3,opt,name=page_size,json=pageSize,proto3" json:"page_size,omitempty"` // 每页数量
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *SearchUsersReq) Reset() {
	*x = SearchUsersReq{}
	mi := &file_svcaccount_proto_msgTypes[39]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *SearchUsersReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SearchUsersReq) ProtoMessage() {}

func (x *SearchUsersReq) ProtoReflect() protoreflect.Message {
	mi := &file_svcaccount_proto_msgTypes[39]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SearchUsersReq.ProtoReflect.Descriptor instead.
func (*SearchUsersReq) Descriptor() ([]byte, []int) {
	return file_svcaccount_proto_rawDescGZIP(), []int{39}
}

func (x *SearchUsersReq) GetKeyword() string {
	if x != nil {
		return x.Keyword
	}
	return ""
}

func (x *SearchUsersReq) GetPage() int32 {
	if x != nil {
		return x.Page
	}
	return 0
}

func (x *SearchUsersReq) GetPageSize() int32 {
	if x != nil {
		return x.PageSize
	}
	return 0
}

// 搜索用户响应
type SearchUsersResp struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Base          *common.SvcBaseResp    `protobuf:"bytes,1,opt,name=base,proto3" json:"base,omitempty"`
	Data          *SearchUsersRespData   `protobuf:"bytes,2,opt,name=data,proto3" json:"data,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *SearchUsersResp) Reset() {
	*x = SearchUsersResp{}
	mi := &file_svcaccount_proto_msgTypes[40]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *SearchUsersResp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SearchUsersResp) ProtoMessage() {}

func (x *SearchUsersResp) ProtoReflect() protoreflect.Message {
	mi := &file_svcaccount_proto_msgTypes[40]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SearchUsersResp.ProtoReflect.Descriptor instead.
func (*SearchUsersResp) Descriptor() ([]byte, []int) {
	return file_svcaccount_proto_rawDescGZIP(), []int{40}
}

func (x *SearchUsersResp) GetBase() *common.SvcBaseResp {
	if x != nil {
		return x.Base
	}
	return nil
}

func (x *SearchUsersResp) GetData() *SearchUsersRespData {
	if x != nil {
		return x.Data
	}
	return nil
}

// 搜索用户响应数据
type SearchUsersRespData struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Users         []*UserInfo            `protobuf:"bytes,1,rep,name=users,proto3" json:"users,omitempty"`  // 用户列表
	Total         int64                  `protobuf:"varint,2,opt,name=total,proto3" json:"total,omitempty"` // 总数量
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *SearchUsersRespData) Reset() {
	*x = SearchUsersRespData{}
	mi := &file_svcaccount_proto_msgTypes[41]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *SearchUsersRespData) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SearchUsersRespData) ProtoMessage() {}

func (x *SearchUsersRespData) ProtoReflect() protoreflect.Message {
	mi := &file_svcaccount_proto_msgTypes[41]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SearchUsersRespData.ProtoReflect.Descriptor instead.
func (*SearchUsersRespData) Descriptor() ([]byte, []int) {
	return file_svcaccount_proto_rawDescGZIP(), []int{41}
}

func (x *SearchUsersRespData) GetUsers() []*UserInfo {
	if x != nil {
		return x.Users
	}
	return nil
}

func (x *SearchUsersRespData) GetTotal() int64 {
	if x != nil {
		return x.Total
	}
	return 0
}

// 关注用户请求
type FollowUserReq struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	FollowerId    int64                  `protobuf:"varint,1,opt,name=follower_id,json=followerId,proto3" json:"follower_id,omitempty"`    // 关注者ID（当前用户）
	FollowingId   int64                  `protobuf:"varint,2,opt,name=following_id,json=followingId,proto3" json:"following_id,omitempty"` // 被关注者ID
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *FollowUserReq) Reset() {
	*x = FollowUserReq{}
	mi := &file_svcaccount_proto_msgTypes[42]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *FollowUserReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*FollowUserReq) ProtoMessage() {}

func (x *FollowUserReq) ProtoReflect() protoreflect.Message {
	mi := &file_svcaccount_proto_msgTypes[42]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use FollowUserReq.ProtoReflect.Descriptor instead.
func (*FollowUserReq) Descriptor() ([]byte, []int) {
	return file_svcaccount_proto_rawDescGZIP(), []int{42}
}

func (x *FollowUserReq) GetFollowerId() int64 {
	if x != nil {
		return x.FollowerId
	}
	return 0
}

func (x *FollowUserReq) GetFollowingId() int64 {
	if x != nil {
		return x.FollowingId
	}
	return 0
}

// 关注用户响应
type FollowUserResp struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Base          *common.SvcBaseResp    `protobuf:"bytes,1,opt,name=base,proto3" json:"base,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *FollowUserResp) Reset() {
	*x = FollowUserResp{}
	mi := &file_svcaccount_proto_msgTypes[43]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *FollowUserResp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*FollowUserResp) ProtoMessage() {}

func (x *FollowUserResp) ProtoReflect() protoreflect.Message {
	mi := &file_svcaccount_proto_msgTypes[43]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use FollowUserResp.ProtoReflect.Descriptor instead.
func (*FollowUserResp) Descriptor() ([]byte, []int) {
	return file_svcaccount_proto_rawDescGZIP(), []int{43}
}

func (x *FollowUserResp) GetBase() *common.SvcBaseResp {
	if x != nil {
		return x.Base
	}
	return nil
}

// 取消关注用户请求
type UnfollowUserReq struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	FollowerId    int64                  `protobuf:"varint,1,opt,name=follower_id,json=followerId,proto3" json:"follower_id,omitempty"`    // 关注者ID（当前用户）
	FollowingId   int64                  `protobuf:"varint,2,opt,name=following_id,json=followingId,proto3" json:"following_id,omitempty"` // 被关注者ID
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *UnfollowUserReq) Reset() {
	*x = UnfollowUserReq{}
	mi := &file_svcaccount_proto_msgTypes[44]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *UnfollowUserReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UnfollowUserReq) ProtoMessage() {}

func (x *UnfollowUserReq) ProtoReflect() protoreflect.Message {
	mi := &file_svcaccount_proto_msgTypes[44]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UnfollowUserReq.ProtoReflect.Descriptor instead.
func (*UnfollowUserReq) Descriptor() ([]byte, []int) {
	return file_svcaccount_proto_rawDescGZIP(), []int{44}
}

func (x *UnfollowUserReq) GetFollowerId() int64 {
	if x != nil {
		return x.FollowerId
	}
	return 0
}

func (x *UnfollowUserReq) GetFollowingId() int64 {
	if x != nil {
		return x.FollowingId
	}
	return 0
}

// 取消关注用户响应
type UnfollowUserResp struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Base          *common.SvcBaseResp    `protobuf:"bytes,1,opt,name=base,proto3" json:"base,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *UnfollowUserResp) Reset() {
	*x = UnfollowUserResp{}
	mi := &file_svcaccount_proto_msgTypes[45]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *UnfollowUserResp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UnfollowUserResp) ProtoMessage() {}

func (x *UnfollowUserResp) ProtoReflect() protoreflect.Message {
	mi := &file_svcaccount_proto_msgTypes[45]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UnfollowUserResp.ProtoReflect.Descriptor instead.
func (*UnfollowUserResp) Descriptor() ([]byte, []int) {
	return file_svcaccount_proto_rawDescGZIP(), []int{45}
}

func (x *UnfollowUserResp) GetBase() *common.SvcBaseResp {
	if x != nil {
		return x.Base
	}
	return nil
}

// 检查是否关注请求
type CheckFollowingReq struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	FollowerId    int64                  `protobuf:"varint,1,opt,name=follower_id,json=followerId,proto3" json:"follower_id,omitempty"`    // 关注者ID（当前用户）
	FollowingId   int64                  `protobuf:"varint,2,opt,name=following_id,json=followingId,proto3" json:"following_id,omitempty"` // 被关注者ID
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *CheckFollowingReq) Reset() {
	*x = CheckFollowingReq{}
	mi := &file_svcaccount_proto_msgTypes[46]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *CheckFollowingReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CheckFollowingReq) ProtoMessage() {}

func (x *CheckFollowingReq) ProtoReflect() protoreflect.Message {
	mi := &file_svcaccount_proto_msgTypes[46]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CheckFollowingReq.ProtoReflect.Descriptor instead.
func (*CheckFollowingReq) Descriptor() ([]byte, []int) {
	return file_svcaccount_proto_rawDescGZIP(), []int{46}
}

func (x *CheckFollowingReq) GetFollowerId() int64 {
	if x != nil {
		return x.FollowerId
	}
	return 0
}

func (x *CheckFollowingReq) GetFollowingId() int64 {
	if x != nil {
		return x.FollowingId
	}
	return 0
}

// 检查是否关注响应
type CheckFollowingResp struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Base          *common.SvcBaseResp    `protobuf:"bytes,1,opt,name=base,proto3" json:"base,omitempty"`
	IsFollowing   bool                   `protobuf:"varint,2,opt,name=is_following,json=isFollowing,proto3" json:"is_following,omitempty"` // 是否关注
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *CheckFollowingResp) Reset() {
	*x = CheckFollowingResp{}
	mi := &file_svcaccount_proto_msgTypes[47]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *CheckFollowingResp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CheckFollowingResp) ProtoMessage() {}

func (x *CheckFollowingResp) ProtoReflect() protoreflect.Message {
	mi := &file_svcaccount_proto_msgTypes[47]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CheckFollowingResp.ProtoReflect.Descriptor instead.
func (*CheckFollowingResp) Descriptor() ([]byte, []int) {
	return file_svcaccount_proto_rawDescGZIP(), []int{47}
}

func (x *CheckFollowingResp) GetBase() *common.SvcBaseResp {
	if x != nil {
		return x.Base
	}
	return nil
}

func (x *CheckFollowingResp) GetIsFollowing() bool {
	if x != nil {
		return x.IsFollowing
	}
	return false
}

// 获取关注的用户列表请求
type GetFollowingUsersReq struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	UserId        int64                  `protobuf:"varint,1,opt,name=user_id,json=userId,proto3" json:"user_id,omitempty"`       // 用户ID
	Page          int32                  `protobuf:"varint,2,opt,name=page,proto3" json:"page,omitempty"`                         // 页码
	PageSize      int32                  `protobuf:"varint,3,opt,name=page_size,json=pageSize,proto3" json:"page_size,omitempty"` // 每页数量
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *GetFollowingUsersReq) Reset() {
	*x = GetFollowingUsersReq{}
	mi := &file_svcaccount_proto_msgTypes[48]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GetFollowingUsersReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetFollowingUsersReq) ProtoMessage() {}

func (x *GetFollowingUsersReq) ProtoReflect() protoreflect.Message {
	mi := &file_svcaccount_proto_msgTypes[48]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetFollowingUsersReq.ProtoReflect.Descriptor instead.
func (*GetFollowingUsersReq) Descriptor() ([]byte, []int) {
	return file_svcaccount_proto_rawDescGZIP(), []int{48}
}

func (x *GetFollowingUsersReq) GetUserId() int64 {
	if x != nil {
		return x.UserId
	}
	return 0
}

func (x *GetFollowingUsersReq) GetPage() int32 {
	if x != nil {
		return x.Page
	}
	return 0
}

func (x *GetFollowingUsersReq) GetPageSize() int32 {
	if x != nil {
		return x.PageSize
	}
	return 0
}

// 获取关注的用户列表响应
type GetFollowingUsersResp struct {
	state         protoimpl.MessageState     `protogen:"open.v1"`
	Base          *common.SvcBaseResp        `protobuf:"bytes,1,opt,name=base,proto3" json:"base,omitempty"`
	Data          *GetFollowingUsersRespData `protobuf:"bytes,2,opt,name=data,proto3" json:"data,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *GetFollowingUsersResp) Reset() {
	*x = GetFollowingUsersResp{}
	mi := &file_svcaccount_proto_msgTypes[49]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GetFollowingUsersResp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetFollowingUsersResp) ProtoMessage() {}

func (x *GetFollowingUsersResp) ProtoReflect() protoreflect.Message {
	mi := &file_svcaccount_proto_msgTypes[49]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetFollowingUsersResp.ProtoReflect.Descriptor instead.
func (*GetFollowingUsersResp) Descriptor() ([]byte, []int) {
	return file_svcaccount_proto_rawDescGZIP(), []int{49}
}

func (x *GetFollowingUsersResp) GetBase() *common.SvcBaseResp {
	if x != nil {
		return x.Base
	}
	return nil
}

func (x *GetFollowingUsersResp) GetData() *GetFollowingUsersRespData {
	if x != nil {
		return x.Data
	}
	return nil
}

// 获取关注的用户列表响应数据
type GetFollowingUsersRespData struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Users         []*UserInfo            `protobuf:"bytes,1,rep,name=users,proto3" json:"users,omitempty"`  // 用户列表
	Total         int64                  `protobuf:"varint,2,opt,name=total,proto3" json:"total,omitempty"` // 总数
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *GetFollowingUsersRespData) Reset() {
	*x = GetFollowingUsersRespData{}
	mi := &file_svcaccount_proto_msgTypes[50]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GetFollowingUsersRespData) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetFollowingUsersRespData) ProtoMessage() {}

func (x *GetFollowingUsersRespData) ProtoReflect() protoreflect.Message {
	mi := &file_svcaccount_proto_msgTypes[50]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetFollowingUsersRespData.ProtoReflect.Descriptor instead.
func (*GetFollowingUsersRespData) Descriptor() ([]byte, []int) {
	return file_svcaccount_proto_rawDescGZIP(), []int{50}
}

func (x *GetFollowingUsersRespData) GetUsers() []*UserInfo {
	if x != nil {
		return x.Users
	}
	return nil
}

func (x *GetFollowingUsersRespData) GetTotal() int64 {
	if x != nil {
		return x.Total
	}
	return 0
}

// 获取粉丝列表请求
type GetFollowersReq struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	UserId        int64                  `protobuf:"varint,1,opt,name=user_id,json=userId,proto3" json:"user_id,omitempty"`       // 用户ID
	Page          int32                  `protobuf:"varint,2,opt,name=page,proto3" json:"page,omitempty"`                         // 页码
	PageSize      int32                  `protobuf:"varint,3,opt,name=page_size,json=pageSize,proto3" json:"page_size,omitempty"` // 每页数量
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *GetFollowersReq) Reset() {
	*x = GetFollowersReq{}
	mi := &file_svcaccount_proto_msgTypes[51]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GetFollowersReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetFollowersReq) ProtoMessage() {}

func (x *GetFollowersReq) ProtoReflect() protoreflect.Message {
	mi := &file_svcaccount_proto_msgTypes[51]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetFollowersReq.ProtoReflect.Descriptor instead.
func (*GetFollowersReq) Descriptor() ([]byte, []int) {
	return file_svcaccount_proto_rawDescGZIP(), []int{51}
}

func (x *GetFollowersReq) GetUserId() int64 {
	if x != nil {
		return x.UserId
	}
	return 0
}

func (x *GetFollowersReq) GetPage() int32 {
	if x != nil {
		return x.Page
	}
	return 0
}

func (x *GetFollowersReq) GetPageSize() int32 {
	if x != nil {
		return x.PageSize
	}
	return 0
}

// 获取粉丝列表响应
type GetFollowersResp struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Base          *common.SvcBaseResp    `protobuf:"bytes,1,opt,name=base,proto3" json:"base,omitempty"`
	Data          *GetFollowersRespData  `protobuf:"bytes,2,opt,name=data,proto3" json:"data,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *GetFollowersResp) Reset() {
	*x = GetFollowersResp{}
	mi := &file_svcaccount_proto_msgTypes[52]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GetFollowersResp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetFollowersResp) ProtoMessage() {}

func (x *GetFollowersResp) ProtoReflect() protoreflect.Message {
	mi := &file_svcaccount_proto_msgTypes[52]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetFollowersResp.ProtoReflect.Descriptor instead.
func (*GetFollowersResp) Descriptor() ([]byte, []int) {
	return file_svcaccount_proto_rawDescGZIP(), []int{52}
}

func (x *GetFollowersResp) GetBase() *common.SvcBaseResp {
	if x != nil {
		return x.Base
	}
	return nil
}

func (x *GetFollowersResp) GetData() *GetFollowersRespData {
	if x != nil {
		return x.Data
	}
	return nil
}

// 获取粉丝列表响应数据
type GetFollowersRespData struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Users         []*UserInfo            `protobuf:"bytes,1,rep,name=users,proto3" json:"users,omitempty"`  // 用户列表
	Total         int64                  `protobuf:"varint,2,opt,name=total,proto3" json:"total,omitempty"` // 总数
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *GetFollowersRespData) Reset() {
	*x = GetFollowersRespData{}
	mi := &file_svcaccount_proto_msgTypes[53]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GetFollowersRespData) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetFollowersRespData) ProtoMessage() {}

func (x *GetFollowersRespData) ProtoReflect() protoreflect.Message {
	mi := &file_svcaccount_proto_msgTypes[53]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetFollowersRespData.ProtoReflect.Descriptor instead.
func (*GetFollowersRespData) Descriptor() ([]byte, []int) {
	return file_svcaccount_proto_rawDescGZIP(), []int{53}
}

func (x *GetFollowersRespData) GetUsers() []*UserInfo {
	if x != nil {
		return x.Users
	}
	return nil
}

func (x *GetFollowersRespData) GetTotal() int64 {
	if x != nil {
		return x.Total
	}
	return 0
}

// 获取关注用户ID列表请求
type GetFollowingUserIDsReq struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	UserId        int64                  `protobuf:"varint,1,opt,name=user_id,json=userId,proto3" json:"user_id,omitempty"` // 用户ID
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *GetFollowingUserIDsReq) Reset() {
	*x = GetFollowingUserIDsReq{}
	mi := &file_svcaccount_proto_msgTypes[54]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GetFollowingUserIDsReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetFollowingUserIDsReq) ProtoMessage() {}

func (x *GetFollowingUserIDsReq) ProtoReflect() protoreflect.Message {
	mi := &file_svcaccount_proto_msgTypes[54]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetFollowingUserIDsReq.ProtoReflect.Descriptor instead.
func (*GetFollowingUserIDsReq) Descriptor() ([]byte, []int) {
	return file_svcaccount_proto_rawDescGZIP(), []int{54}
}

func (x *GetFollowingUserIDsReq) GetUserId() int64 {
	if x != nil {
		return x.UserId
	}
	return 0
}

// 获取关注用户ID列表响应
type GetFollowingUserIDsResp struct {
	state         protoimpl.MessageState       `protogen:"open.v1"`
	Base          *common.SvcBaseResp          `protobuf:"bytes,1,opt,name=base,proto3" json:"base,omitempty"`
	Data          *GetFollowingUserIDsRespData `protobuf:"bytes,2,opt,name=data,proto3" json:"data,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *GetFollowingUserIDsResp) Reset() {
	*x = GetFollowingUserIDsResp{}
	mi := &file_svcaccount_proto_msgTypes[55]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GetFollowingUserIDsResp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetFollowingUserIDsResp) ProtoMessage() {}

func (x *GetFollowingUserIDsResp) ProtoReflect() protoreflect.Message {
	mi := &file_svcaccount_proto_msgTypes[55]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetFollowingUserIDsResp.ProtoReflect.Descriptor instead.
func (*GetFollowingUserIDsResp) Descriptor() ([]byte, []int) {
	return file_svcaccount_proto_rawDescGZIP(), []int{55}
}

func (x *GetFollowingUserIDsResp) GetBase() *common.SvcBaseResp {
	if x != nil {
		return x.Base
	}
	return nil
}

func (x *GetFollowingUserIDsResp) GetData() *GetFollowingUserIDsRespData {
	if x != nil {
		return x.Data
	}
	return nil
}

// 获取关注用户ID列表响应数据
type GetFollowingUserIDsRespData struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	UserIds       []int64                `protobuf:"varint,1,rep,packed,name=user_ids,json=userIds,proto3" json:"user_ids,omitempty"` // 用户ID列表
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *GetFollowingUserIDsRespData) Reset() {
	*x = GetFollowingUserIDsRespData{}
	mi := &file_svcaccount_proto_msgTypes[56]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GetFollowingUserIDsRespData) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetFollowingUserIDsRespData) ProtoMessage() {}

func (x *GetFollowingUserIDsRespData) ProtoReflect() protoreflect.Message {
	mi := &file_svcaccount_proto_msgTypes[56]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetFollowingUserIDsRespData.ProtoReflect.Descriptor instead.
func (*GetFollowingUserIDsRespData) Descriptor() ([]byte, []int) {
	return file_svcaccount_proto_rawDescGZIP(), []int{56}
}

func (x *GetFollowingUserIDsRespData) GetUserIds() []int64 {
	if x != nil {
		return x.UserIds
	}
	return nil
}

// 批量检查是否关注请求
type BatchCheckFollowingReq struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	FollowerId    int64                  `protobuf:"varint,1,opt,name=follower_id,json=followerId,proto3" json:"follower_id,omitempty"`              // 关注者ID（当前用户）
	FollowingIds  []int64                `protobuf:"varint,2,rep,packed,name=following_ids,json=followingIds,proto3" json:"following_ids,omitempty"` // 被关注者ID列表
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *BatchCheckFollowingReq) Reset() {
	*x = BatchCheckFollowingReq{}
	mi := &file_svcaccount_proto_msgTypes[57]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *BatchCheckFollowingReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BatchCheckFollowingReq) ProtoMessage() {}

func (x *BatchCheckFollowingReq) ProtoReflect() protoreflect.Message {
	mi := &file_svcaccount_proto_msgTypes[57]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BatchCheckFollowingReq.ProtoReflect.Descriptor instead.
func (*BatchCheckFollowingReq) Descriptor() ([]byte, []int) {
	return file_svcaccount_proto_rawDescGZIP(), []int{57}
}

func (x *BatchCheckFollowingReq) GetFollowerId() int64 {
	if x != nil {
		return x.FollowerId
	}
	return 0
}

func (x *BatchCheckFollowingReq) GetFollowingIds() []int64 {
	if x != nil {
		return x.FollowingIds
	}
	return nil
}

// 批量检查是否关注响应
type BatchCheckFollowingResp struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Base          *common.SvcBaseResp    `protobuf:"bytes,1,opt,name=base,proto3" json:"base,omitempty"`
	Data          *FollowDataItem        `protobuf:"bytes,2,opt,name=data,proto3" json:"data,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *BatchCheckFollowingResp) Reset() {
	*x = BatchCheckFollowingResp{}
	mi := &file_svcaccount_proto_msgTypes[58]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *BatchCheckFollowingResp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BatchCheckFollowingResp) ProtoMessage() {}

func (x *BatchCheckFollowingResp) ProtoReflect() protoreflect.Message {
	mi := &file_svcaccount_proto_msgTypes[58]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BatchCheckFollowingResp.ProtoReflect.Descriptor instead.
func (*BatchCheckFollowingResp) Descriptor() ([]byte, []int) {
	return file_svcaccount_proto_rawDescGZIP(), []int{58}
}

func (x *BatchCheckFollowingResp) GetBase() *common.SvcBaseResp {
	if x != nil {
		return x.Base
	}
	return nil
}

func (x *BatchCheckFollowingResp) GetData() *FollowDataItem {
	if x != nil {
		return x.Data
	}
	return nil
}

type FollowDataItem struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Items         map[int64]*FollowData  `protobuf:"bytes,1,rep,name=items,proto3" json:"items,omitempty" protobuf_key:"varint,1,opt,name=key" protobuf_val:"bytes,2,opt,name=value"` // key: 被关注者ID, value: FollowData
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *FollowDataItem) Reset() {
	*x = FollowDataItem{}
	mi := &file_svcaccount_proto_msgTypes[59]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *FollowDataItem) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*FollowDataItem) ProtoMessage() {}

func (x *FollowDataItem) ProtoReflect() protoreflect.Message {
	mi := &file_svcaccount_proto_msgTypes[59]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use FollowDataItem.ProtoReflect.Descriptor instead.
func (*FollowDataItem) Descriptor() ([]byte, []int) {
	return file_svcaccount_proto_rawDescGZIP(), []int{59}
}

func (x *FollowDataItem) GetItems() map[int64]*FollowData {
	if x != nil {
		return x.Items
	}
	return nil
}

// 审核回调
type ReviewCallbackReq struct {
	state         protoimpl.MessageState  `protogen:"open.v1"`
	Userid        int64                   `protobuf:"varint,1,opt,name=userid,proto3" json:"userid,omitempty"`
	Id            int64                   `protobuf:"varint,2,opt,name=id,proto3" json:"id,omitempty"`
	Type          svcreview.ReviewBizType `protobuf:"varint,3,opt,name=type,proto3,enum=vc.svcreview.ReviewBizType" json:"type,omitempty"`
	Result        svcreview.AuditResult   `protobuf:"varint,4,opt,name=result,proto3,enum=vc.svcreview.AuditResult" json:"result,omitempty"`
	Reason        string                  `protobuf:"bytes,5,opt,name=reason,proto3" json:"reason,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *ReviewCallbackReq) Reset() {
	*x = ReviewCallbackReq{}
	mi := &file_svcaccount_proto_msgTypes[60]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ReviewCallbackReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ReviewCallbackReq) ProtoMessage() {}

func (x *ReviewCallbackReq) ProtoReflect() protoreflect.Message {
	mi := &file_svcaccount_proto_msgTypes[60]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ReviewCallbackReq.ProtoReflect.Descriptor instead.
func (*ReviewCallbackReq) Descriptor() ([]byte, []int) {
	return file_svcaccount_proto_rawDescGZIP(), []int{60}
}

func (x *ReviewCallbackReq) GetUserid() int64 {
	if x != nil {
		return x.Userid
	}
	return 0
}

func (x *ReviewCallbackReq) GetId() int64 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *ReviewCallbackReq) GetType() svcreview.ReviewBizType {
	if x != nil {
		return x.Type
	}
	return svcreview.ReviewBizType(0)
}

func (x *ReviewCallbackReq) GetResult() svcreview.AuditResult {
	if x != nil {
		return x.Result
	}
	return svcreview.AuditResult(0)
}

func (x *ReviewCallbackReq) GetReason() string {
	if x != nil {
		return x.Reason
	}
	return ""
}

// 检查手机号是否在白名单请求
type IsPhoneWhitelistReq struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	PhoneNumber   string                 `protobuf:"bytes,1,opt,name=phone_number,json=phoneNumber,proto3" json:"phone_number,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *IsPhoneWhitelistReq) Reset() {
	*x = IsPhoneWhitelistReq{}
	mi := &file_svcaccount_proto_msgTypes[61]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *IsPhoneWhitelistReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*IsPhoneWhitelistReq) ProtoMessage() {}

func (x *IsPhoneWhitelistReq) ProtoReflect() protoreflect.Message {
	mi := &file_svcaccount_proto_msgTypes[61]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use IsPhoneWhitelistReq.ProtoReflect.Descriptor instead.
func (*IsPhoneWhitelistReq) Descriptor() ([]byte, []int) {
	return file_svcaccount_proto_rawDescGZIP(), []int{61}
}

func (x *IsPhoneWhitelistReq) GetPhoneNumber() string {
	if x != nil {
		return x.PhoneNumber
	}
	return ""
}

// 检查手机号是否在白名单响应
type IsPhoneWhitelistResp struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Base          *common.SvcBaseResp    `protobuf:"bytes,1,opt,name=base,proto3" json:"base,omitempty"`
	IsWhitelist   bool                   `protobuf:"varint,2,opt,name=is_whitelist,json=isWhitelist,proto3" json:"is_whitelist,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *IsPhoneWhitelistResp) Reset() {
	*x = IsPhoneWhitelistResp{}
	mi := &file_svcaccount_proto_msgTypes[62]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *IsPhoneWhitelistResp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*IsPhoneWhitelistResp) ProtoMessage() {}

func (x *IsPhoneWhitelistResp) ProtoReflect() protoreflect.Message {
	mi := &file_svcaccount_proto_msgTypes[62]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use IsPhoneWhitelistResp.ProtoReflect.Descriptor instead.
func (*IsPhoneWhitelistResp) Descriptor() ([]byte, []int) {
	return file_svcaccount_proto_rawDescGZIP(), []int{62}
}

func (x *IsPhoneWhitelistResp) GetBase() *common.SvcBaseResp {
	if x != nil {
		return x.Base
	}
	return nil
}

func (x *IsPhoneWhitelistResp) GetIsWhitelist() bool {
	if x != nil {
		return x.IsWhitelist
	}
	return false
}

// 获取登录状态请求
type GetLoginStatusReq struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *GetLoginStatusReq) Reset() {
	*x = GetLoginStatusReq{}
	mi := &file_svcaccount_proto_msgTypes[63]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GetLoginStatusReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetLoginStatusReq) ProtoMessage() {}

func (x *GetLoginStatusReq) ProtoReflect() protoreflect.Message {
	mi := &file_svcaccount_proto_msgTypes[63]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetLoginStatusReq.ProtoReflect.Descriptor instead.
func (*GetLoginStatusReq) Descriptor() ([]byte, []int) {
	return file_svcaccount_proto_rawDescGZIP(), []int{63}
}

// 登录状态信息
type LoginStatusInfo struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	DeviceId      string                 `protobuf:"bytes,1,opt,name=device_id,json=deviceId,proto3" json:"device_id,omitempty"`
	Platform      int32                  `protobuf:"varint,2,opt,name=platform,proto3" json:"platform,omitempty"`
	LoginTime     int64                  `protobuf:"varint,3,opt,name=login_time,json=loginTime,proto3" json:"login_time,omitempty"`
	LastActive    int64                  `protobuf:"varint,4,opt,name=last_active,json=lastActive,proto3" json:"last_active,omitempty"`
	IpAddress     string                 `protobuf:"bytes,5,opt,name=ip_address,json=ipAddress,proto3" json:"ip_address,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *LoginStatusInfo) Reset() {
	*x = LoginStatusInfo{}
	mi := &file_svcaccount_proto_msgTypes[64]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *LoginStatusInfo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*LoginStatusInfo) ProtoMessage() {}

func (x *LoginStatusInfo) ProtoReflect() protoreflect.Message {
	mi := &file_svcaccount_proto_msgTypes[64]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use LoginStatusInfo.ProtoReflect.Descriptor instead.
func (*LoginStatusInfo) Descriptor() ([]byte, []int) {
	return file_svcaccount_proto_rawDescGZIP(), []int{64}
}

func (x *LoginStatusInfo) GetDeviceId() string {
	if x != nil {
		return x.DeviceId
	}
	return ""
}

func (x *LoginStatusInfo) GetPlatform() int32 {
	if x != nil {
		return x.Platform
	}
	return 0
}

func (x *LoginStatusInfo) GetLoginTime() int64 {
	if x != nil {
		return x.LoginTime
	}
	return 0
}

func (x *LoginStatusInfo) GetLastActive() int64 {
	if x != nil {
		return x.LastActive
	}
	return 0
}

func (x *LoginStatusInfo) GetIpAddress() string {
	if x != nil {
		return x.IpAddress
	}
	return ""
}

// 获取登录状态响应
type GetLoginStatusResp struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Base          *common.SvcBaseResp    `protobuf:"bytes,1,opt,name=base,proto3" json:"base,omitempty"`
	Data          *LoginStatusInfo       `protobuf:"bytes,2,opt,name=data,proto3" json:"data,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *GetLoginStatusResp) Reset() {
	*x = GetLoginStatusResp{}
	mi := &file_svcaccount_proto_msgTypes[65]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GetLoginStatusResp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetLoginStatusResp) ProtoMessage() {}

func (x *GetLoginStatusResp) ProtoReflect() protoreflect.Message {
	mi := &file_svcaccount_proto_msgTypes[65]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetLoginStatusResp.ProtoReflect.Descriptor instead.
func (*GetLoginStatusResp) Descriptor() ([]byte, []int) {
	return file_svcaccount_proto_rawDescGZIP(), []int{65}
}

func (x *GetLoginStatusResp) GetBase() *common.SvcBaseResp {
	if x != nil {
		return x.Base
	}
	return nil
}

func (x *GetLoginStatusResp) GetData() *LoginStatusInfo {
	if x != nil {
		return x.Data
	}
	return nil
}

// 踢下线设备请求
type KickOffDeviceReq struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	DeviceId      string                 `protobuf:"bytes,1,opt,name=device_id,json=deviceId,proto3" json:"device_id,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *KickOffDeviceReq) Reset() {
	*x = KickOffDeviceReq{}
	mi := &file_svcaccount_proto_msgTypes[66]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *KickOffDeviceReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*KickOffDeviceReq) ProtoMessage() {}

func (x *KickOffDeviceReq) ProtoReflect() protoreflect.Message {
	mi := &file_svcaccount_proto_msgTypes[66]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use KickOffDeviceReq.ProtoReflect.Descriptor instead.
func (*KickOffDeviceReq) Descriptor() ([]byte, []int) {
	return file_svcaccount_proto_rawDescGZIP(), []int{66}
}

func (x *KickOffDeviceReq) GetDeviceId() string {
	if x != nil {
		return x.DeviceId
	}
	return ""
}

// 踢下线设备响应
type KickOffDeviceResp struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Base          *common.SvcBaseResp    `protobuf:"bytes,1,opt,name=base,proto3" json:"base,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *KickOffDeviceResp) Reset() {
	*x = KickOffDeviceResp{}
	mi := &file_svcaccount_proto_msgTypes[67]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *KickOffDeviceResp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*KickOffDeviceResp) ProtoMessage() {}

func (x *KickOffDeviceResp) ProtoReflect() protoreflect.Message {
	mi := &file_svcaccount_proto_msgTypes[67]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use KickOffDeviceResp.ProtoReflect.Descriptor instead.
func (*KickOffDeviceResp) Descriptor() ([]byte, []int) {
	return file_svcaccount_proto_rawDescGZIP(), []int{67}
}

func (x *KickOffDeviceResp) GetBase() *common.SvcBaseResp {
	if x != nil {
		return x.Base
	}
	return nil
}

// 设备登录历史信息
type DeviceHistoryInfo struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	DeviceId      string                 `protobuf:"bytes,1,opt,name=device_id,json=deviceId,proto3" json:"device_id,omitempty"`
	Platform      int32                  `protobuf:"varint,2,opt,name=platform,proto3" json:"platform,omitempty"`
	LoginTime     int64                  `protobuf:"varint,3,opt,name=login_time,json=loginTime,proto3" json:"login_time,omitempty"`
	IpAddress     string                 `protobuf:"bytes,4,opt,name=ip_address,json=ipAddress,proto3" json:"ip_address,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *DeviceHistoryInfo) Reset() {
	*x = DeviceHistoryInfo{}
	mi := &file_svcaccount_proto_msgTypes[68]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *DeviceHistoryInfo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DeviceHistoryInfo) ProtoMessage() {}

func (x *DeviceHistoryInfo) ProtoReflect() protoreflect.Message {
	mi := &file_svcaccount_proto_msgTypes[68]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DeviceHistoryInfo.ProtoReflect.Descriptor instead.
func (*DeviceHistoryInfo) Descriptor() ([]byte, []int) {
	return file_svcaccount_proto_rawDescGZIP(), []int{68}
}

func (x *DeviceHistoryInfo) GetDeviceId() string {
	if x != nil {
		return x.DeviceId
	}
	return ""
}

func (x *DeviceHistoryInfo) GetPlatform() int32 {
	if x != nil {
		return x.Platform
	}
	return 0
}

func (x *DeviceHistoryInfo) GetLoginTime() int64 {
	if x != nil {
		return x.LoginTime
	}
	return 0
}

func (x *DeviceHistoryInfo) GetIpAddress() string {
	if x != nil {
		return x.IpAddress
	}
	return ""
}

// 获取设备登录历史请求
type GetDeviceHistoryReq struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *GetDeviceHistoryReq) Reset() {
	*x = GetDeviceHistoryReq{}
	mi := &file_svcaccount_proto_msgTypes[69]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GetDeviceHistoryReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetDeviceHistoryReq) ProtoMessage() {}

func (x *GetDeviceHistoryReq) ProtoReflect() protoreflect.Message {
	mi := &file_svcaccount_proto_msgTypes[69]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetDeviceHistoryReq.ProtoReflect.Descriptor instead.
func (*GetDeviceHistoryReq) Descriptor() ([]byte, []int) {
	return file_svcaccount_proto_rawDescGZIP(), []int{69}
}

// 获取设备登录历史响应
type GetDeviceHistoryResp struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Base          *common.SvcBaseResp    `protobuf:"bytes,1,opt,name=base,proto3" json:"base,omitempty"`
	Data          []*DeviceHistoryInfo   `protobuf:"bytes,2,rep,name=data,proto3" json:"data,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *GetDeviceHistoryResp) Reset() {
	*x = GetDeviceHistoryResp{}
	mi := &file_svcaccount_proto_msgTypes[70]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GetDeviceHistoryResp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetDeviceHistoryResp) ProtoMessage() {}

func (x *GetDeviceHistoryResp) ProtoReflect() protoreflect.Message {
	mi := &file_svcaccount_proto_msgTypes[70]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetDeviceHistoryResp.ProtoReflect.Descriptor instead.
func (*GetDeviceHistoryResp) Descriptor() ([]byte, []int) {
	return file_svcaccount_proto_rawDescGZIP(), []int{70}
}

func (x *GetDeviceHistoryResp) GetBase() *common.SvcBaseResp {
	if x != nil {
		return x.Base
	}
	return nil
}

func (x *GetDeviceHistoryResp) GetData() []*DeviceHistoryInfo {
	if x != nil {
		return x.Data
	}
	return nil
}

// 验证Token请求
type VerifyTokenReq struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Token         string                 `protobuf:"bytes,1,opt,name=token,proto3" json:"token,omitempty"`
	DeviceId      string                 `protobuf:"bytes,2,opt,name=device_id,json=deviceId,proto3" json:"device_id,omitempty"`
	Platform      int32                  `protobuf:"varint,3,opt,name=platform,proto3" json:"platform,omitempty"`
	UserAgent     string                 `protobuf:"bytes,4,opt,name=user_agent,json=userAgent,proto3" json:"user_agent,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *VerifyTokenReq) Reset() {
	*x = VerifyTokenReq{}
	mi := &file_svcaccount_proto_msgTypes[71]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *VerifyTokenReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*VerifyTokenReq) ProtoMessage() {}

func (x *VerifyTokenReq) ProtoReflect() protoreflect.Message {
	mi := &file_svcaccount_proto_msgTypes[71]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use VerifyTokenReq.ProtoReflect.Descriptor instead.
func (*VerifyTokenReq) Descriptor() ([]byte, []int) {
	return file_svcaccount_proto_rawDescGZIP(), []int{71}
}

func (x *VerifyTokenReq) GetToken() string {
	if x != nil {
		return x.Token
	}
	return ""
}

func (x *VerifyTokenReq) GetDeviceId() string {
	if x != nil {
		return x.DeviceId
	}
	return ""
}

func (x *VerifyTokenReq) GetPlatform() int32 {
	if x != nil {
		return x.Platform
	}
	return 0
}

func (x *VerifyTokenReq) GetUserAgent() string {
	if x != nil {
		return x.UserAgent
	}
	return ""
}

// Token验证信息
type TokenVerifyInfo struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	UserId        int64                  `protobuf:"varint,1,opt,name=user_id,json=userId,proto3" json:"user_id,omitempty"`
	DeviceId      string                 `protobuf:"bytes,2,opt,name=device_id,json=deviceId,proto3" json:"device_id,omitempty"`
	Platform      int32                  `protobuf:"varint,3,opt,name=platform,proto3" json:"platform,omitempty"`
	IsValid       bool                   `protobuf:"varint,4,opt,name=is_valid,json=isValid,proto3" json:"is_valid,omitempty"`
	Reason        string                 `protobuf:"bytes,5,opt,name=reason,proto3" json:"reason,omitempty"` // 如果无效，说明原因
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *TokenVerifyInfo) Reset() {
	*x = TokenVerifyInfo{}
	mi := &file_svcaccount_proto_msgTypes[72]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *TokenVerifyInfo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*TokenVerifyInfo) ProtoMessage() {}

func (x *TokenVerifyInfo) ProtoReflect() protoreflect.Message {
	mi := &file_svcaccount_proto_msgTypes[72]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use TokenVerifyInfo.ProtoReflect.Descriptor instead.
func (*TokenVerifyInfo) Descriptor() ([]byte, []int) {
	return file_svcaccount_proto_rawDescGZIP(), []int{72}
}

func (x *TokenVerifyInfo) GetUserId() int64 {
	if x != nil {
		return x.UserId
	}
	return 0
}

func (x *TokenVerifyInfo) GetDeviceId() string {
	if x != nil {
		return x.DeviceId
	}
	return ""
}

func (x *TokenVerifyInfo) GetPlatform() int32 {
	if x != nil {
		return x.Platform
	}
	return 0
}

func (x *TokenVerifyInfo) GetIsValid() bool {
	if x != nil {
		return x.IsValid
	}
	return false
}

func (x *TokenVerifyInfo) GetReason() string {
	if x != nil {
		return x.Reason
	}
	return ""
}

// 验证Token响应
type VerifyTokenResp struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Base          *common.SvcBaseResp    `protobuf:"bytes,1,opt,name=base,proto3" json:"base,omitempty"`
	Data          *TokenVerifyInfo       `protobuf:"bytes,2,opt,name=data,proto3" json:"data,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *VerifyTokenResp) Reset() {
	*x = VerifyTokenResp{}
	mi := &file_svcaccount_proto_msgTypes[73]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *VerifyTokenResp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*VerifyTokenResp) ProtoMessage() {}

func (x *VerifyTokenResp) ProtoReflect() protoreflect.Message {
	mi := &file_svcaccount_proto_msgTypes[73]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use VerifyTokenResp.ProtoReflect.Descriptor instead.
func (*VerifyTokenResp) Descriptor() ([]byte, []int) {
	return file_svcaccount_proto_rawDescGZIP(), []int{73}
}

func (x *VerifyTokenResp) GetBase() *common.SvcBaseResp {
	if x != nil {
		return x.Base
	}
	return nil
}

func (x *VerifyTokenResp) GetData() *TokenVerifyInfo {
	if x != nil {
		return x.Data
	}
	return nil
}

// 重置用户信息请求
type ResetUserInfoReq struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	UserId        int64                  `protobuf:"varint,1,opt,name=user_id,json=userId,proto3" json:"user_id,omitempty"` // 用户ID
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *ResetUserInfoReq) Reset() {
	*x = ResetUserInfoReq{}
	mi := &file_svcaccount_proto_msgTypes[74]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ResetUserInfoReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ResetUserInfoReq) ProtoMessage() {}

func (x *ResetUserInfoReq) ProtoReflect() protoreflect.Message {
	mi := &file_svcaccount_proto_msgTypes[74]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ResetUserInfoReq.ProtoReflect.Descriptor instead.
func (*ResetUserInfoReq) Descriptor() ([]byte, []int) {
	return file_svcaccount_proto_rawDescGZIP(), []int{74}
}

func (x *ResetUserInfoReq) GetUserId() int64 {
	if x != nil {
		return x.UserId
	}
	return 0
}

// 重置用户信息响应
type ResetUserInfoResp struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Base          *common.SvcBaseResp    `protobuf:"bytes,1,opt,name=base,proto3" json:"base,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *ResetUserInfoResp) Reset() {
	*x = ResetUserInfoResp{}
	mi := &file_svcaccount_proto_msgTypes[75]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ResetUserInfoResp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ResetUserInfoResp) ProtoMessage() {}

func (x *ResetUserInfoResp) ProtoReflect() protoreflect.Message {
	mi := &file_svcaccount_proto_msgTypes[75]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ResetUserInfoResp.ProtoReflect.Descriptor instead.
func (*ResetUserInfoResp) Descriptor() ([]byte, []int) {
	return file_svcaccount_proto_rawDescGZIP(), []int{75}
}

func (x *ResetUserInfoResp) GetBase() *common.SvcBaseResp {
	if x != nil {
		return x.Base
	}
	return nil
}

// 封禁用户请求
type BanUserReq struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	UserId        int64                  `protobuf:"varint,1,opt,name=user_id,json=userId,proto3" json:"user_id,omitempty"` // 用户ID
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *BanUserReq) Reset() {
	*x = BanUserReq{}
	mi := &file_svcaccount_proto_msgTypes[76]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *BanUserReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BanUserReq) ProtoMessage() {}

func (x *BanUserReq) ProtoReflect() protoreflect.Message {
	mi := &file_svcaccount_proto_msgTypes[76]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BanUserReq.ProtoReflect.Descriptor instead.
func (*BanUserReq) Descriptor() ([]byte, []int) {
	return file_svcaccount_proto_rawDescGZIP(), []int{76}
}

func (x *BanUserReq) GetUserId() int64 {
	if x != nil {
		return x.UserId
	}
	return 0
}

// 封禁用户响应
type BanUserResp struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Base          *common.SvcBaseResp    `protobuf:"bytes,1,opt,name=base,proto3" json:"base,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *BanUserResp) Reset() {
	*x = BanUserResp{}
	mi := &file_svcaccount_proto_msgTypes[77]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *BanUserResp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BanUserResp) ProtoMessage() {}

func (x *BanUserResp) ProtoReflect() protoreflect.Message {
	mi := &file_svcaccount_proto_msgTypes[77]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BanUserResp.ProtoReflect.Descriptor instead.
func (*BanUserResp) Descriptor() ([]byte, []int) {
	return file_svcaccount_proto_rawDescGZIP(), []int{77}
}

func (x *BanUserResp) GetBase() *common.SvcBaseResp {
	if x != nil {
		return x.Base
	}
	return nil
}

// 解封用户请求
type UnbanUserReq struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	UserId        int64                  `protobuf:"varint,1,opt,name=user_id,json=userId,proto3" json:"user_id,omitempty"` // 用户ID
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *UnbanUserReq) Reset() {
	*x = UnbanUserReq{}
	mi := &file_svcaccount_proto_msgTypes[78]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *UnbanUserReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UnbanUserReq) ProtoMessage() {}

func (x *UnbanUserReq) ProtoReflect() protoreflect.Message {
	mi := &file_svcaccount_proto_msgTypes[78]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UnbanUserReq.ProtoReflect.Descriptor instead.
func (*UnbanUserReq) Descriptor() ([]byte, []int) {
	return file_svcaccount_proto_rawDescGZIP(), []int{78}
}

func (x *UnbanUserReq) GetUserId() int64 {
	if x != nil {
		return x.UserId
	}
	return 0
}

// 解封用户响应
type UnbanUserResp struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Base          *common.SvcBaseResp    `protobuf:"bytes,1,opt,name=base,proto3" json:"base,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *UnbanUserResp) Reset() {
	*x = UnbanUserResp{}
	mi := &file_svcaccount_proto_msgTypes[79]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *UnbanUserResp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UnbanUserResp) ProtoMessage() {}

func (x *UnbanUserResp) ProtoReflect() protoreflect.Message {
	mi := &file_svcaccount_proto_msgTypes[79]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UnbanUserResp.ProtoReflect.Descriptor instead.
func (*UnbanUserResp) Descriptor() ([]byte, []int) {
	return file_svcaccount_proto_rawDescGZIP(), []int{79}
}

func (x *UnbanUserResp) GetBase() *common.SvcBaseResp {
	if x != nil {
		return x.Base
	}
	return nil
}

// Admin审核更新用户信息请求
type AdminUpdateUserInfoReq struct {
	state             protoimpl.MessageState `protogen:"open.v1"`
	UserId            int64                  `protobuf:"varint,1,opt,name=user_id,json=userId,proto3" json:"user_id,omitempty"`                                   // 用户ID
	Avatar            string                 `protobuf:"bytes,2,opt,name=avatar,proto3" json:"avatar,omitempty"`                                                  // 头像URL
	Nickname          string                 `protobuf:"bytes,3,opt,name=nickname,proto3" json:"nickname,omitempty"`                                              // 昵称
	BackgroundUrl     string                 `protobuf:"bytes,4,opt,name=background_url,json=backgroundUrl,proto3" json:"background_url,omitempty"`               // 背景图
	VoiceSignatureUrl string                 `protobuf:"bytes,5,opt,name=voice_signature_url,json=voiceSignatureUrl,proto3" json:"voice_signature_url,omitempty"` // 语音签名
	VoiceDuration     int32                  `protobuf:"varint,6,opt,name=voice_duration,json=voiceDuration,proto3" json:"voice_duration,omitempty"`              //语音时长
	unknownFields     protoimpl.UnknownFields
	sizeCache         protoimpl.SizeCache
}

func (x *AdminUpdateUserInfoReq) Reset() {
	*x = AdminUpdateUserInfoReq{}
	mi := &file_svcaccount_proto_msgTypes[80]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *AdminUpdateUserInfoReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*AdminUpdateUserInfoReq) ProtoMessage() {}

func (x *AdminUpdateUserInfoReq) ProtoReflect() protoreflect.Message {
	mi := &file_svcaccount_proto_msgTypes[80]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use AdminUpdateUserInfoReq.ProtoReflect.Descriptor instead.
func (*AdminUpdateUserInfoReq) Descriptor() ([]byte, []int) {
	return file_svcaccount_proto_rawDescGZIP(), []int{80}
}

func (x *AdminUpdateUserInfoReq) GetUserId() int64 {
	if x != nil {
		return x.UserId
	}
	return 0
}

func (x *AdminUpdateUserInfoReq) GetAvatar() string {
	if x != nil {
		return x.Avatar
	}
	return ""
}

func (x *AdminUpdateUserInfoReq) GetNickname() string {
	if x != nil {
		return x.Nickname
	}
	return ""
}

func (x *AdminUpdateUserInfoReq) GetBackgroundUrl() string {
	if x != nil {
		return x.BackgroundUrl
	}
	return ""
}

func (x *AdminUpdateUserInfoReq) GetVoiceSignatureUrl() string {
	if x != nil {
		return x.VoiceSignatureUrl
	}
	return ""
}

func (x *AdminUpdateUserInfoReq) GetVoiceDuration() int32 {
	if x != nil {
		return x.VoiceDuration
	}
	return 0
}

// Admin审核更新用户信息响应
type AdminUpdateUserInfoResp struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Base          *common.SvcBaseResp    `protobuf:"bytes,1,opt,name=base,proto3" json:"base,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *AdminUpdateUserInfoResp) Reset() {
	*x = AdminUpdateUserInfoResp{}
	mi := &file_svcaccount_proto_msgTypes[81]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *AdminUpdateUserInfoResp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*AdminUpdateUserInfoResp) ProtoMessage() {}

func (x *AdminUpdateUserInfoResp) ProtoReflect() protoreflect.Message {
	mi := &file_svcaccount_proto_msgTypes[81]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use AdminUpdateUserInfoResp.ProtoReflect.Descriptor instead.
func (*AdminUpdateUserInfoResp) Descriptor() ([]byte, []int) {
	return file_svcaccount_proto_rawDescGZIP(), []int{81}
}

func (x *AdminUpdateUserInfoResp) GetBase() *common.SvcBaseResp {
	if x != nil {
		return x.Base
	}
	return nil
}

// Admin创建用户请求
type AdminCreateUserReq struct {
	state             protoimpl.MessageState `protogen:"open.v1"`
	Phone             string                 `protobuf:"bytes,1,opt,name=phone,proto3" json:"phone,omitempty"`                                                    // 手机号
	Nickname          string                 `protobuf:"bytes,2,opt,name=nickname,proto3" json:"nickname,omitempty"`                                              // 昵称
	Avatar            string                 `protobuf:"bytes,3,opt,name=avatar,proto3" json:"avatar,omitempty"`                                                  // 头像URL
	Gender            int32                  `protobuf:"varint,4,opt,name=gender,proto3" json:"gender,omitempty"`                                                 // 性别 0:未知 1:男 2:女
	Status            int32                  `protobuf:"varint,5,opt,name=status,proto3" json:"status,omitempty"`                                                 // 状态 1:正常 3:禁用
	BackgroundUrl     string                 `protobuf:"bytes,6,opt,name=background_url,json=backgroundUrl,proto3" json:"background_url,omitempty"`               // 背景图
	VoiceSignatureUrl string                 `protobuf:"bytes,7,opt,name=voice_signature_url,json=voiceSignatureUrl,proto3" json:"voice_signature_url,omitempty"` // 语音签名
	VoiceDuration     int32                  `protobuf:"varint,8,opt,name=voice_duration,json=voiceDuration,proto3" json:"voice_duration,omitempty"`              // 语音时长
	IsPremiumCreator  bool                   `protobuf:"varint,9,opt,name=is_premium_creator,json=isPremiumCreator,proto3" json:"is_premium_creator,omitempty"`   // 是否优质创作者
	CreationUserId    uint32                 `protobuf:"varint,10,opt,name=creation_user_id,json=creationUserId,proto3" json:"creation_user_id,omitempty"`        // 关联的创作者admin用户ID
	unknownFields     protoimpl.UnknownFields
	sizeCache         protoimpl.SizeCache
}

func (x *AdminCreateUserReq) Reset() {
	*x = AdminCreateUserReq{}
	mi := &file_svcaccount_proto_msgTypes[82]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *AdminCreateUserReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*AdminCreateUserReq) ProtoMessage() {}

func (x *AdminCreateUserReq) ProtoReflect() protoreflect.Message {
	mi := &file_svcaccount_proto_msgTypes[82]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use AdminCreateUserReq.ProtoReflect.Descriptor instead.
func (*AdminCreateUserReq) Descriptor() ([]byte, []int) {
	return file_svcaccount_proto_rawDescGZIP(), []int{82}
}

func (x *AdminCreateUserReq) GetPhone() string {
	if x != nil {
		return x.Phone
	}
	return ""
}

func (x *AdminCreateUserReq) GetNickname() string {
	if x != nil {
		return x.Nickname
	}
	return ""
}

func (x *AdminCreateUserReq) GetAvatar() string {
	if x != nil {
		return x.Avatar
	}
	return ""
}

func (x *AdminCreateUserReq) GetGender() int32 {
	if x != nil {
		return x.Gender
	}
	return 0
}

func (x *AdminCreateUserReq) GetStatus() int32 {
	if x != nil {
		return x.Status
	}
	return 0
}

func (x *AdminCreateUserReq) GetBackgroundUrl() string {
	if x != nil {
		return x.BackgroundUrl
	}
	return ""
}

func (x *AdminCreateUserReq) GetVoiceSignatureUrl() string {
	if x != nil {
		return x.VoiceSignatureUrl
	}
	return ""
}

func (x *AdminCreateUserReq) GetVoiceDuration() int32 {
	if x != nil {
		return x.VoiceDuration
	}
	return 0
}

func (x *AdminCreateUserReq) GetIsPremiumCreator() bool {
	if x != nil {
		return x.IsPremiumCreator
	}
	return false
}

func (x *AdminCreateUserReq) GetCreationUserId() uint32 {
	if x != nil {
		return x.CreationUserId
	}
	return 0
}

// Admin创建用户响应
type AdminCreateUserResp struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Base          *common.SvcBaseResp    `protobuf:"bytes,1,opt,name=base,proto3" json:"base,omitempty"`
	UserId        int64                  `protobuf:"varint,2,opt,name=user_id,json=userId,proto3" json:"user_id,omitempty"` // 创建的用户ID
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *AdminCreateUserResp) Reset() {
	*x = AdminCreateUserResp{}
	mi := &file_svcaccount_proto_msgTypes[83]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *AdminCreateUserResp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*AdminCreateUserResp) ProtoMessage() {}

func (x *AdminCreateUserResp) ProtoReflect() protoreflect.Message {
	mi := &file_svcaccount_proto_msgTypes[83]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use AdminCreateUserResp.ProtoReflect.Descriptor instead.
func (*AdminCreateUserResp) Descriptor() ([]byte, []int) {
	return file_svcaccount_proto_rawDescGZIP(), []int{83}
}

func (x *AdminCreateUserResp) GetBase() *common.SvcBaseResp {
	if x != nil {
		return x.Base
	}
	return nil
}

func (x *AdminCreateUserResp) GetUserId() int64 {
	if x != nil {
		return x.UserId
	}
	return 0
}

type ReviewNotifyReq struct {
	state            protoimpl.MessageState `protogen:"open.v1"`
	UserId           int64                  `protobuf:"varint,1,opt,name=user_id,json=userId,proto3" json:"user_id,omitempty"`                                                                     // 用户ID
	ReviewNotifyType ReviewNotifyType       `protobuf:"varint,2,opt,name=review_notify_type,json=reviewNotifyType,proto3,enum=vc.svcaccount.ReviewNotifyType" json:"review_notify_type,omitempty"` //审核通知类型
	unknownFields    protoimpl.UnknownFields
	sizeCache        protoimpl.SizeCache
}

func (x *ReviewNotifyReq) Reset() {
	*x = ReviewNotifyReq{}
	mi := &file_svcaccount_proto_msgTypes[84]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ReviewNotifyReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ReviewNotifyReq) ProtoMessage() {}

func (x *ReviewNotifyReq) ProtoReflect() protoreflect.Message {
	mi := &file_svcaccount_proto_msgTypes[84]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ReviewNotifyReq.ProtoReflect.Descriptor instead.
func (*ReviewNotifyReq) Descriptor() ([]byte, []int) {
	return file_svcaccount_proto_rawDescGZIP(), []int{84}
}

func (x *ReviewNotifyReq) GetUserId() int64 {
	if x != nil {
		return x.UserId
	}
	return 0
}

func (x *ReviewNotifyReq) GetReviewNotifyType() ReviewNotifyType {
	if x != nil {
		return x.ReviewNotifyType
	}
	return ReviewNotifyType_INVALID
}

var File_svcaccount_proto protoreflect.FileDescriptor

const file_svcaccount_proto_rawDesc = "" +
	"\n" +
	"\x10svcaccount.proto\x12\rvc.svcaccount\x1a+protoc-gen-validate/validate/validate.proto\x1a\x13common/common.proto\x1a\x19svcreview/svcreview.proto\"F\n" +
	"\x16GetVerificationCodeReq\x12,\n" +
	"\fphone_number\x18\x01 \x01(\tB\t\xfaB\x06r\x04 \x01(\vR\vphoneNumber\"\x82\x01\n" +
	"\x17GetVerificationCodeResp\x12'\n" +
	"\x04base\x18\x01 \x01(\v2\x13.common.SvcBaseRespR\x04base\x12>\n" +
	"\x04data\x18\x02 \x01(\v2*.vc.svcaccount.GetVerificationCodeRespDataR\x04data\"z\n" +
	"\x1bGetVerificationCodeRespData\x12#\n" +
	"\ris_registered\x18\x01 \x01(\bR\fisRegistered\x126\n" +
	"\x12verification_token\x18\x02 \x01(\tB\a\xfaB\x04r\x02 \x01R\x11verificationToken\"\x9e\x01\n" +
	"\x10AccountSignUpReq\x12$\n" +
	"\bsms_code\x18\x01 \x01(\tB\t\xfaB\x06r\x04 \x01(\vR\asmsCode\x126\n" +
	"\x12verification_token\x18\x02 \x01(\tB\a\xfaB\x04r\x02 \x01R\x11verificationToken\x12,\n" +
	"\fphone_number\x18\x03 \x01(\tB\t\xfaB\x06r\x04 \x01(\vR\vphoneNumber\"\xfb\x01\n" +
	"\rTokenRespData\x12\x1d\n" +
	"\n" +
	"token_type\x18\x01 \x01(\tR\ttokenType\x12!\n" +
	"\faccess_token\x18\x02 \x01(\tR\vaccessToken\x12#\n" +
	"\rrefresh_token\x18\x03 \x01(\tR\frefreshToken\x12\x1d\n" +
	"\n" +
	"expires_in\x18\x04 \x01(\x03R\texpiresIn\x12\x17\n" +
	"\auser_id\x18\x05 \x01(\x03R\x06userId\x12\x1b\n" +
	"\tnick_name\x18\x06 \x01(\tR\bnickName\x12\x16\n" +
	"\x06avatar\x18\a \x01(\tR\x06avatar\x12\x16\n" +
	"\x06gender\x18\b \x01(\x05R\x06gender\"n\n" +
	"\x11AccountSignUpResp\x12'\n" +
	"\x04base\x18\x01 \x01(\v2\x13.common.SvcBaseRespR\x04base\x120\n" +
	"\x04data\x18\x02 \x01(\v2\x1c.vc.svcaccount.TokenRespDataR\x04data\"\x9c\x01\n" +
	"\x10AccountSignInReq\x12\"\n" +
	"\bsms_code\x18\x01 \x01(\tB\a\xfaB\x04r\x02 \x01R\asmsCode\x126\n" +
	"\x12verification_token\x18\x02 \x01(\tB\a\xfaB\x04r\x02 \x01R\x11verificationToken\x12,\n" +
	"\fphone_number\x18\x03 \x01(\tB\t\xfaB\x06r\x04 \x01(\vR\vphoneNumber\"n\n" +
	"\x11AccountSignInResp\x12'\n" +
	"\x04base\x18\x01 \x01(\v2\x13.common.SvcBaseRespR\x04base\x120\n" +
	"\x04data\x18\x02 \x01(\v2\x1c.vc.svcaccount.TokenRespDataR\x04data\"\x13\n" +
	"\x11AccountSignOutReq\"=\n" +
	"\x12AccountSignOutResp\x12'\n" +
	"\x04base\x18\x01 \x01(\v2\x13.common.SvcBaseRespR\x04base\"?\n" +
	"\x0fRefreshTokenReq\x12,\n" +
	"\rrefresh_token\x18\x01 \x01(\tB\a\xfaB\x04r\x02 \x01R\frefreshToken\"m\n" +
	"\x10RefreshTokenResp\x12'\n" +
	"\x04base\x18\x01 \x01(\v2\x13.common.SvcBaseRespR\x04base\x120\n" +
	"\x04data\x18\x02 \x01(\v2\x1c.vc.svcaccount.TokenRespDataR\x04data\"\x12\n" +
	"\x10AccountDeleteReq\"<\n" +
	"\x11AccountDeleteResp\x12'\n" +
	"\x04base\x18\x01 \x01(\v2\x13.common.SvcBaseRespR\x04base\"i\n" +
	"\x0eGetUserInfoReq\x12\x17\n" +
	"\auser_id\x18\x01 \x01(\x03R\x06userId\x12\x1f\n" +
	"\vwith_follow\x18\x02 \x01(\bR\n" +
	"withFollow\x12\x1d\n" +
	"\n" +
	"with_score\x18\x03 \x01(\bR\twithScore\"\x93\x01\n" +
	"\x1eGetUserInfoByCreationUserIdReq\x121\n" +
	"\x10creation_user_id\x18\x01 \x01(\rB\a\xfaB\x04*\x02 \x00R\x0ecreationUserId\x12\x1f\n" +
	"\vwith_follow\x18\x02 \x01(\bR\n" +
	"withFollow\x12\x1d\n" +
	"\n" +
	"with_score\x18\x03 \x01(\bR\twithScore\"w\n" +
	"\x1fGetUserInfoByCreationUserIdResp\x12'\n" +
	"\x04base\x18\x01 \x01(\v2\x13.common.SvcBaseRespR\x04base\x12+\n" +
	"\x04data\x18\x02 \x01(\v2\x17.vc.svcaccount.UserInfoR\x04data\"g\n" +
	"\x0fGetUserInfoResp\x12'\n" +
	"\x04base\x18\x01 \x01(\v2\x13.common.SvcBaseRespR\x04base\x12+\n" +
	"\x04data\x18\x02 \x01(\v2\x17.vc.svcaccount.UserInfoR\x04data\"\xca\x05\n" +
	"\bUserInfo\x12\x17\n" +
	"\auser_id\x18\x01 \x01(\x03R\x06userId\x12\x1a\n" +
	"\bnickname\x18\x02 \x01(\tR\bnickname\x12\x14\n" +
	"\x05phone\x18\x03 \x01(\tR\x05phone\x12\x16\n" +
	"\x06avatar\x18\x04 \x01(\tR\x06avatar\x12\x16\n" +
	"\x06gender\x18\x05 \x01(\x05R\x06gender\x12\x16\n" +
	"\x06status\x18\x06 \x01(\x05R\x06status\x12\x1d\n" +
	"\n" +
	"created_at\x18\a \x01(\x03R\tcreatedAt\x12%\n" +
	"\x0ebackground_url\x18\b \x01(\tR\rbackgroundUrl\x12F\n" +
	"\x0fvoice_signature\x18\t \x01(\v2\x1d.vc.svcaccount.VoiceSignatureR\x0evoiceSignature\x12,\n" +
	"\x12is_premium_creator\x18\n" +
	" \x01(\bR\x10isPremiumCreator\x12'\n" +
	"\x0ffollowers_count\x18\v \x01(\x03R\x0efollowersCount\x12'\n" +
	"\x0ffollowing_count\x18\f \x01(\x03R\x0efollowingCount\x12!\n" +
	"\fis_following\x18\r \x01(\bR\visFollowing\x12\x1f\n" +
	"\vis_followed\x18\x0e \x01(\bR\n" +
	"isFollowed\x12;\n" +
	"\n" +
	"score_data\x18\x0f \x01(\v2\x1c.vc.svcaccount.UserScoreDataR\tscoreData\x12!\n" +
	"\fscript_count\x18\x10 \x01(\x05R\vscriptCount\x120\n" +
	"\x14user_character_count\x18\x11 \x01(\x05R\x12userCharacterCount\x12\x1d\n" +
	"\n" +
	"is_deleted\x18\x12 \x01(\bR\tisDeleted\x12(\n" +
	"\x10creation_user_id\x18\x13 \x01(\rR\x0ecreationUserId\"\xa2\x01\n" +
	"\n" +
	"FollowData\x12'\n" +
	"\x0ffollowers_count\x18\b \x01(\x03R\x0efollowersCount\x12'\n" +
	"\x0ffollowing_count\x18\t \x01(\x03R\x0efollowingCount\x12!\n" +
	"\fis_following\x18\n" +
	" \x01(\bR\visFollowing\x12\x1f\n" +
	"\vis_followed\x18\v \x01(\bR\n" +
	"isFollowed\"\x7f\n" +
	"\x0eVoiceSignature\x12.\n" +
	"\x13voice_signature_url\x18\x01 \x01(\tR\x11voiceSignatureUrl\x12\x1a\n" +
	"\bduration\x18\x02 \x01(\x05R\bduration\x12!\n" +
	"\fis_reviewing\x18\x03 \x01(\bR\visReviewing\"I\n" +
	"\x13BatchGetUserInfoReq\x12\x17\n" +
	"\auser_id\x18\x01 \x01(\x03R\x06userId\x12\x19\n" +
	"\buser_ids\x18\x02 \x03(\x03R\auserIds\"\xf2\x01\n" +
	"\x14BatchGetUserInfoResp\x12'\n" +
	"\x04base\x18\x01 \x01(\v2\x13.common.SvcBaseRespR\x04base\x12X\n" +
	"\ruser_info_map\x18\x02 \x03(\v24.vc.svcaccount.BatchGetUserInfoResp.UserInfoMapEntryR\vuserInfoMap\x1aW\n" +
	"\x10UserInfoMapEntry\x12\x10\n" +
	"\x03key\x18\x01 \x01(\x03R\x03key\x12-\n" +
	"\x05value\x18\x02 \x01(\v2\x17.vc.svcaccount.UserInfoR\x05value:\x028\x01\"\xf3\x02\n" +
	"\x11UpdateUserInfoReq\x12 \n" +
	"\auser_id\x18\x01 \x01(\x03B\a\xfaB\x04\"\x02 \x00R\x06userId\x12\x16\n" +
	"\x06avatar\x18\x02 \x01(\tR\x06avatar\x12\x1a\n" +
	"\bnickname\x18\x03 \x01(\tR\bnickname\x12%\n" +
	"\x0ebackground_url\x18\x04 \x01(\tR\rbackgroundUrl\x12.\n" +
	"\x13voice_signature_url\x18\x05 \x01(\tR\x11voiceSignatureUrl\x12%\n" +
	"\x0evoice_duration\x18\x06 \x01(\x05R\rvoiceDuration\x12,\n" +
	"\x12is_premium_creator\x18\a \x01(\bR\x10isPremiumCreator\x122\n" +
	"\x15reset_voice_signature\x18\b \x01(\bR\x13resetVoiceSignature\x12(\n" +
	"\x10creation_user_id\x18\t \x01(\rR\x0ecreationUserId\"=\n" +
	"\x12UpdateUserInfoResp\x12'\n" +
	"\x04base\x18\x01 \x01(\v2\x13.common.SvcBaseRespR\x04base\"f\n" +
	"\x14SetPremiumCreatorReq\x12 \n" +
	"\auser_id\x18\x01 \x01(\x03B\a\xfaB\x04\"\x02 \x00R\x06userId\x12,\n" +
	"\x12is_premium_creator\x18\x02 \x01(\bR\x10isPremiumCreator\"@\n" +
	"\x15SetPremiumCreatorResp\x12'\n" +
	"\x04base\x18\x01 \x01(\v2\x13.common.SvcBaseRespR\x04base\"\x87\x02\n" +
	"\x10AccountOnlineReq\x12%\n" +
	"\x04data\x18\x01 \x01(\v2\x11.common.BaseParamR\x04data\x12\x1f\n" +
	"\x06userid\x18\x02 \x01(\x03B\a\xfaB\x04\"\x02 \x00R\x06userid\x12\x14\n" +
	"\x05state\x18\x03 \x01(\bR\x05state\x12\x1c\n" +
	"\theartbeat\x18\x04 \x01(\bR\theartbeat\x12\x1b\n" +
	"\tneed_resp\x18\x05 \x01(\bR\bneedResp\x12\x14\n" +
	"\x05front\x18\x06 \x01(\bR\x05front\x12D\n" +
	"\x0fon_connect_type\x18\a \x01(\x0e2\x1c.vc.svcaccount.OnConnectTypeR\ronConnectType\"<\n" +
	"\x11AccountOnlineResp\x12'\n" +
	"\x04base\x18\x01 \x01(\v2\x13.common.SvcBaseRespR\x04base\"-\n" +
	"\x12IsUserBlacklistReq\x12\x17\n" +
	"\auser_id\x18\x01 \x01(\x03R\x06userId\"a\n" +
	"\x13IsUserBlacklistResp\x12'\n" +
	"\x04base\x18\x01 \x01(\v2\x13.common.SvcBaseRespR\x04base\x12!\n" +
	"\fis_blacklist\x18\x02 \x01(\bR\visBlacklist\"*\n" +
	"\x0fGetUserScoreReq\x12\x17\n" +
	"\auser_id\x18\x01 \x01(\x03R\x06userId\"m\n" +
	"\x10GetUserScoreResp\x12'\n" +
	"\x04base\x18\x01 \x01(\v2\x13.common.SvcBaseRespR\x04base\x120\n" +
	"\x04data\x18\x02 \x01(\v2\x1c.vc.svcaccount.UserScoreDataR\x04data\"\x9b\x02\n" +
	"\x0fAddUserScoreReq\x12 \n" +
	"\auser_id\x18\x01 \x01(\x03B\a\xfaB\x04\"\x02 \x00R\x06userId\x12.\n" +
	"\x0evaluable_score\x18\x02 \x01(\x03B\a\xfaB\x04\"\x02(\x00R\rvaluableScore\x12$\n" +
	"\tfee_score\x18\x03 \x01(\x03B\a\xfaB\x04\"\x02(\x00R\bfeeScore\x12\"\n" +
	"\bbiz_type\x18\x04 \x01(\x03B\a\xfaB\x04\"\x02 \x00R\abizType\x12$\n" +
	"\bbiz_name\x18\x05 \x01(\tB\t\xfaB\x06r\x04 \x01(@R\abizName\x12$\n" +
	"\border_id\x18\x06 \x01(\tB\t\xfaB\x06r\x04 \x01(@R\aorderId\x12 \n" +
	"\x06remark\x18\a \x01(\tB\b\xfaB\x05r\x03(\xff\x01R\x06remark\";\n" +
	"\x10AddUserScoreResp\x12'\n" +
	"\x04base\x18\x01 \x01(\v2\x13.common.SvcBaseRespR\x04base\"\xed\x01\n" +
	"\x13ConsumeUserScoreReq\x12 \n" +
	"\auser_id\x18\x01 \x01(\x03B\a\xfaB\x04\"\x02 \x00R\x06userId\x12\x1d\n" +
	"\x05score\x18\x02 \x01(\x03B\a\xfaB\x04\"\x02 \x00R\x05score\x12\"\n" +
	"\bbiz_type\x18\x03 \x01(\x03B\a\xfaB\x04\"\x02 \x00R\abizType\x12$\n" +
	"\bbiz_name\x18\x04 \x01(\tB\t\xfaB\x06r\x04 \x01(@R\abizName\x12$\n" +
	"\border_id\x18\x05 \x01(\tB\t\xfaB\x06r\x04 \x01(@R\aorderId\x12%\n" +
	"\x0eallow_overdraw\x18\a \x01(\bR\rallowOverdraw\"|\n" +
	"\x14ConsumeUserScoreResp\x12'\n" +
	"\x04base\x18\x01 \x01(\v2\x13.common.SvcBaseRespR\x04base\x12;\n" +
	"\x04data\x18\x02 \x01(\v2'.vc.svcaccount.ConsumeUserScoreRespDataR\x04data\"~\n" +
	"\x18ConsumeUserScoreRespData\x124\n" +
	"\x16consume_valuable_score\x18\x01 \x01(\x03R\x14consumeValuableScore\x12,\n" +
	"\x12consume_free_score\x18\x02 \x01(\x03R\x10consumeFreeScore\"\x92\x02\n" +
	"\rUserScoreData\x12#\n" +
	"\rtotal_balance\x18\x01 \x01(\x03R\ftotalBalance\x12!\n" +
	"\ffree_balance\x18\x02 \x01(\x03R\vfreeBalance\x12)\n" +
	"\x10valuable_balance\x18\x03 \x01(\x03R\x0fvaluableBalance\x120\n" +
	"\x14total_valuable_score\x18\x04 \x01(\x03R\x12totalValuableScore\x12(\n" +
	"\x10total_free_score\x18\x05 \x01(\x03R\x0etotalFreeScore\x122\n" +
	"\x15display_total_balance\x18\v \x01(\tR\x13displayTotalBalance\"d\n" +
	"\x0eSearchUsersReq\x12!\n" +
	"\akeyword\x18\x01 \x01(\tB\a\xfaB\x04r\x02\x10\x01R\akeyword\x12\x12\n" +
	"\x04page\x18\x02 \x01(\x05R\x04page\x12\x1b\n" +
	"\tpage_size\x18\x03 \x01(\x05R\bpageSize\"r\n" +
	"\x0fSearchUsersResp\x12'\n" +
	"\x04base\x18\x01 \x01(\v2\x13.common.SvcBaseRespR\x04base\x126\n" +
	"\x04data\x18\x02 \x01(\v2\".vc.svcaccount.SearchUsersRespDataR\x04data\"Z\n" +
	"\x13SearchUsersRespData\x12-\n" +
	"\x05users\x18\x01 \x03(\v2\x17.vc.svcaccount.UserInfoR\x05users\x12\x14\n" +
	"\x05total\x18\x02 \x01(\x03R\x05total\"e\n" +
	"\rFollowUserReq\x12(\n" +
	"\vfollower_id\x18\x01 \x01(\x03B\a\xfaB\x04\"\x02 \x00R\n" +
	"followerId\x12*\n" +
	"\ffollowing_id\x18\x02 \x01(\x03B\a\xfaB\x04\"\x02 \x00R\vfollowingId\"9\n" +
	"\x0eFollowUserResp\x12'\n" +
	"\x04base\x18\x01 \x01(\v2\x13.common.SvcBaseRespR\x04base\"g\n" +
	"\x0fUnfollowUserReq\x12(\n" +
	"\vfollower_id\x18\x01 \x01(\x03B\a\xfaB\x04\"\x02 \x00R\n" +
	"followerId\x12*\n" +
	"\ffollowing_id\x18\x02 \x01(\x03B\a\xfaB\x04\"\x02 \x00R\vfollowingId\";\n" +
	"\x10UnfollowUserResp\x12'\n" +
	"\x04base\x18\x01 \x01(\v2\x13.common.SvcBaseRespR\x04base\"i\n" +
	"\x11CheckFollowingReq\x12(\n" +
	"\vfollower_id\x18\x01 \x01(\x03B\a\xfaB\x04\"\x02 \x00R\n" +
	"followerId\x12*\n" +
	"\ffollowing_id\x18\x02 \x01(\x03B\a\xfaB\x04\"\x02 \x00R\vfollowingId\"`\n" +
	"\x12CheckFollowingResp\x12'\n" +
	"\x04base\x18\x01 \x01(\v2\x13.common.SvcBaseRespR\x04base\x12!\n" +
	"\fis_following\x18\x02 \x01(\bR\visFollowing\"i\n" +
	"\x14GetFollowingUsersReq\x12 \n" +
	"\auser_id\x18\x01 \x01(\x03B\a\xfaB\x04\"\x02 \x00R\x06userId\x12\x12\n" +
	"\x04page\x18\x02 \x01(\x05R\x04page\x12\x1b\n" +
	"\tpage_size\x18\x03 \x01(\x05R\bpageSize\"~\n" +
	"\x15GetFollowingUsersResp\x12'\n" +
	"\x04base\x18\x01 \x01(\v2\x13.common.SvcBaseRespR\x04base\x12<\n" +
	"\x04data\x18\x02 \x01(\v2(.vc.svcaccount.GetFollowingUsersRespDataR\x04data\"`\n" +
	"\x19GetFollowingUsersRespData\x12-\n" +
	"\x05users\x18\x01 \x03(\v2\x17.vc.svcaccount.UserInfoR\x05users\x12\x14\n" +
	"\x05total\x18\x02 \x01(\x03R\x05total\"d\n" +
	"\x0fGetFollowersReq\x12 \n" +
	"\auser_id\x18\x01 \x01(\x03B\a\xfaB\x04\"\x02 \x00R\x06userId\x12\x12\n" +
	"\x04page\x18\x02 \x01(\x05R\x04page\x12\x1b\n" +
	"\tpage_size\x18\x03 \x01(\x05R\bpageSize\"t\n" +
	"\x10GetFollowersResp\x12'\n" +
	"\x04base\x18\x01 \x01(\v2\x13.common.SvcBaseRespR\x04base\x127\n" +
	"\x04data\x18\x02 \x01(\v2#.vc.svcaccount.GetFollowersRespDataR\x04data\"[\n" +
	"\x14GetFollowersRespData\x12-\n" +
	"\x05users\x18\x01 \x03(\v2\x17.vc.svcaccount.UserInfoR\x05users\x12\x14\n" +
	"\x05total\x18\x02 \x01(\x03R\x05total\":\n" +
	"\x16GetFollowingUserIDsReq\x12 \n" +
	"\auser_id\x18\x01 \x01(\x03B\a\xfaB\x04\"\x02 \x00R\x06userId\"\x82\x01\n" +
	"\x17GetFollowingUserIDsResp\x12'\n" +
	"\x04base\x18\x01 \x01(\v2\x13.common.SvcBaseRespR\x04base\x12>\n" +
	"\x04data\x18\x02 \x01(\v2*.vc.svcaccount.GetFollowingUserIDsRespDataR\x04data\"8\n" +
	"\x1bGetFollowingUserIDsRespData\x12\x19\n" +
	"\buser_ids\x18\x01 \x03(\x03R\auserIds\"t\n" +
	"\x16BatchCheckFollowingReq\x12(\n" +
	"\vfollower_id\x18\x01 \x01(\x03B\a\xfaB\x04\"\x02 \x00R\n" +
	"followerId\x120\n" +
	"\rfollowing_ids\x18\x02 \x03(\x03B\v\xfaB\b\x92\x01\x05\b\x01\x10\xe8\aR\ffollowingIds\"u\n" +
	"\x17BatchCheckFollowingResp\x12'\n" +
	"\x04base\x18\x01 \x01(\v2\x13.common.SvcBaseRespR\x04base\x121\n" +
	"\x04data\x18\x02 \x01(\v2\x1d.vc.svcaccount.FollowDataItemR\x04data\"\xa5\x01\n" +
	"\x0eFollowDataItem\x12>\n" +
	"\x05items\x18\x01 \x03(\v2(.vc.svcaccount.FollowDataItem.ItemsEntryR\x05items\x1aS\n" +
	"\n" +
	"ItemsEntry\x12\x10\n" +
	"\x03key\x18\x01 \x01(\x03R\x03key\x12/\n" +
	"\x05value\x18\x02 \x01(\v2\x19.vc.svcaccount.FollowDataR\x05value:\x028\x01\"\xb7\x01\n" +
	"\x11ReviewCallbackReq\x12\x16\n" +
	"\x06userid\x18\x01 \x01(\x03R\x06userid\x12\x0e\n" +
	"\x02id\x18\x02 \x01(\x03R\x02id\x12/\n" +
	"\x04type\x18\x03 \x01(\x0e2\x1b.vc.svcreview.ReviewBizTypeR\x04type\x121\n" +
	"\x06result\x18\x04 \x01(\x0e2\x19.vc.svcreview.AuditResultR\x06result\x12\x16\n" +
	"\x06reason\x18\x05 \x01(\tR\x06reason\"C\n" +
	"\x13IsPhoneWhitelistReq\x12,\n" +
	"\fphone_number\x18\x01 \x01(\tB\t\xfaB\x06r\x04 \x01(\vR\vphoneNumber\"b\n" +
	"\x14IsPhoneWhitelistResp\x12'\n" +
	"\x04base\x18\x01 \x01(\v2\x13.common.SvcBaseRespR\x04base\x12!\n" +
	"\fis_whitelist\x18\x02 \x01(\bR\visWhitelist\"\x13\n" +
	"\x11GetLoginStatusReq\"\xa9\x01\n" +
	"\x0fLoginStatusInfo\x12\x1b\n" +
	"\tdevice_id\x18\x01 \x01(\tR\bdeviceId\x12\x1a\n" +
	"\bplatform\x18\x02 \x01(\x05R\bplatform\x12\x1d\n" +
	"\n" +
	"login_time\x18\x03 \x01(\x03R\tloginTime\x12\x1f\n" +
	"\vlast_active\x18\x04 \x01(\x03R\n" +
	"lastActive\x12\x1d\n" +
	"\n" +
	"ip_address\x18\x05 \x01(\tR\tipAddress\"q\n" +
	"\x12GetLoginStatusResp\x12'\n" +
	"\x04base\x18\x01 \x01(\v2\x13.common.SvcBaseRespR\x04base\x122\n" +
	"\x04data\x18\x02 \x01(\v2\x1e.vc.svcaccount.LoginStatusInfoR\x04data\"8\n" +
	"\x10KickOffDeviceReq\x12$\n" +
	"\tdevice_id\x18\x01 \x01(\tB\a\xfaB\x04r\x02 \x01R\bdeviceId\"<\n" +
	"\x11KickOffDeviceResp\x12'\n" +
	"\x04base\x18\x01 \x01(\v2\x13.common.SvcBaseRespR\x04base\"\x8a\x01\n" +
	"\x11DeviceHistoryInfo\x12\x1b\n" +
	"\tdevice_id\x18\x01 \x01(\tR\bdeviceId\x12\x1a\n" +
	"\bplatform\x18\x02 \x01(\x05R\bplatform\x12\x1d\n" +
	"\n" +
	"login_time\x18\x03 \x01(\x03R\tloginTime\x12\x1d\n" +
	"\n" +
	"ip_address\x18\x04 \x01(\tR\tipAddress\"\x15\n" +
	"\x13GetDeviceHistoryReq\"u\n" +
	"\x14GetDeviceHistoryResp\x12'\n" +
	"\x04base\x18\x01 \x01(\v2\x13.common.SvcBaseRespR\x04base\x124\n" +
	"\x04data\x18\x02 \x03(\v2 .vc.svcaccount.DeviceHistoryInfoR\x04data\"\x87\x01\n" +
	"\x0eVerifyTokenReq\x12\x1d\n" +
	"\x05token\x18\x01 \x01(\tB\a\xfaB\x04r\x02 \x01R\x05token\x12\x1b\n" +
	"\tdevice_id\x18\x02 \x01(\tR\bdeviceId\x12\x1a\n" +
	"\bplatform\x18\x03 \x01(\x05R\bplatform\x12\x1d\n" +
	"\n" +
	"user_agent\x18\x04 \x01(\tR\tuserAgent\"\x96\x01\n" +
	"\x0fTokenVerifyInfo\x12\x17\n" +
	"\auser_id\x18\x01 \x01(\x03R\x06userId\x12\x1b\n" +
	"\tdevice_id\x18\x02 \x01(\tR\bdeviceId\x12\x1a\n" +
	"\bplatform\x18\x03 \x01(\x05R\bplatform\x12\x19\n" +
	"\bis_valid\x18\x04 \x01(\bR\aisValid\x12\x16\n" +
	"\x06reason\x18\x05 \x01(\tR\x06reason\"n\n" +
	"\x0fVerifyTokenResp\x12'\n" +
	"\x04base\x18\x01 \x01(\v2\x13.common.SvcBaseRespR\x04base\x122\n" +
	"\x04data\x18\x02 \x01(\v2\x1e.vc.svcaccount.TokenVerifyInfoR\x04data\"4\n" +
	"\x10ResetUserInfoReq\x12 \n" +
	"\auser_id\x18\x01 \x01(\x03B\a\xfaB\x04\"\x02 \x00R\x06userId\"<\n" +
	"\x11ResetUserInfoResp\x12'\n" +
	"\x04base\x18\x01 \x01(\v2\x13.common.SvcBaseRespR\x04base\".\n" +
	"\n" +
	"BanUserReq\x12 \n" +
	"\auser_id\x18\x01 \x01(\x03B\a\xfaB\x04\"\x02 \x00R\x06userId\"6\n" +
	"\vBanUserResp\x12'\n" +
	"\x04base\x18\x01 \x01(\v2\x13.common.SvcBaseRespR\x04base\"0\n" +
	"\fUnbanUserReq\x12 \n" +
	"\auser_id\x18\x01 \x01(\x03B\a\xfaB\x04\"\x02 \x00R\x06userId\"8\n" +
	"\rUnbanUserResp\x12'\n" +
	"\x04base\x18\x01 \x01(\v2\x13.common.SvcBaseRespR\x04base\"\xec\x01\n" +
	"\x16AdminUpdateUserInfoReq\x12 \n" +
	"\auser_id\x18\x01 \x01(\x03B\a\xfaB\x04\"\x02 \x00R\x06userId\x12\x16\n" +
	"\x06avatar\x18\x02 \x01(\tR\x06avatar\x12\x1a\n" +
	"\bnickname\x18\x03 \x01(\tR\bnickname\x12%\n" +
	"\x0ebackground_url\x18\x04 \x01(\tR\rbackgroundUrl\x12.\n" +
	"\x13voice_signature_url\x18\x05 \x01(\tR\x11voiceSignatureUrl\x12%\n" +
	"\x0evoice_duration\x18\x06 \x01(\x05R\rvoiceDuration\"B\n" +
	"\x17AdminUpdateUserInfoResp\x12'\n" +
	"\x04base\x18\x01 \x01(\v2\x13.common.SvcBaseRespR\x04base\"\xef\x02\n" +
	"\x12AdminCreateUserReq\x12\x1f\n" +
	"\x05phone\x18\x01 \x01(\tB\t\xfaB\x06r\x04 \x01(\vR\x05phone\x12\x1a\n" +
	"\bnickname\x18\x02 \x01(\tR\bnickname\x12\x16\n" +
	"\x06avatar\x18\x03 \x01(\tR\x06avatar\x12\x16\n" +
	"\x06gender\x18\x04 \x01(\x05R\x06gender\x12\x16\n" +
	"\x06status\x18\x05 \x01(\x05R\x06status\x12%\n" +
	"\x0ebackground_url\x18\x06 \x01(\tR\rbackgroundUrl\x12.\n" +
	"\x13voice_signature_url\x18\a \x01(\tR\x11voiceSignatureUrl\x12%\n" +
	"\x0evoice_duration\x18\b \x01(\x05R\rvoiceDuration\x12,\n" +
	"\x12is_premium_creator\x18\t \x01(\bR\x10isPremiumCreator\x12(\n" +
	"\x10creation_user_id\x18\n" +
	" \x01(\rR\x0ecreationUserId\"W\n" +
	"\x13AdminCreateUserResp\x12'\n" +
	"\x04base\x18\x01 \x01(\v2\x13.common.SvcBaseRespR\x04base\x12\x17\n" +
	"\auser_id\x18\x02 \x01(\x03R\x06userId\"\x8c\x01\n" +
	"\x0fReviewNotifyReq\x12 \n" +
	"\auser_id\x18\x01 \x01(\x03B\a\xfaB\x04\"\x02 \x00R\x06userId\x12W\n" +
	"\x12review_notify_type\x18\x02 \x01(\x0e2\x1f.vc.svcaccount.ReviewNotifyTypeB\b\xfaB\x05\x82\x01\x02\x10\x01R\x10reviewNotifyType*G\n" +
	"\rOnConnectType\x12\v\n" +
	"\ainvalid\x10\x00\x12\r\n" +
	"\tCONNECTED\x10\x01\x12\b\n" +
	"\x04PING\x10\x02\x12\x10\n" +
	"\fDISCONNECTED\x10\x03*\xd9\x01\n" +
	"\x10ReviewNotifyType\x12\v\n" +
	"\aINVALID\x10\x00\x12\x11\n" +
	"\rAVATAR_REJECT\x10\x01\x12\x12\n" +
	"\x0eAVATAR_APPROVE\x10\x02\x12\x13\n" +
	"\x0fNICKNAME_REJECT\x10\x03\x12\x14\n" +
	"\x10NICKNAME_APPROVE\x10\x04\x12\x1b\n" +
	"\x17BACKGROUND_IMAGE_REJECT\x10\x05\x12\x1c\n" +
	"\x18BACKGROUND_IMAGE_APPROVE\x10\x06\x12\x14\n" +
	"\x10SIGNATURE_REJECT\x10\a\x12\x15\n" +
	"\x11SIGNATURE_APPROVE\x10\b2\xff\x18\n" +
	"\x01s\x12f\n" +
	"\x13GetVerificationCode\x12%.vc.svcaccount.GetVerificationCodeReq\x1a&.vc.svcaccount.GetVerificationCodeResp\"\x00\x12T\n" +
	"\rAccountSignUp\x12\x1f.vc.svcaccount.AccountSignUpReq\x1a .vc.svcaccount.AccountSignUpResp\"\x00\x12T\n" +
	"\rAccountSignIn\x12\x1f.vc.svcaccount.AccountSignInReq\x1a .vc.svcaccount.AccountSignInResp\"\x00\x12W\n" +
	"\x0eAccountSignOut\x12 .vc.svcaccount.AccountSignOutReq\x1a!.vc.svcaccount.AccountSignOutResp\"\x00\x12Q\n" +
	"\fRefreshToken\x12\x1e.vc.svcaccount.RefreshTokenReq\x1a\x1f.vc.svcaccount.RefreshTokenResp\"\x00\x12T\n" +
	"\rAccountDelete\x12\x1f.vc.svcaccount.AccountDeleteReq\x1a .vc.svcaccount.AccountDeleteResp\"\x00\x12N\n" +
	"\vGetUserInfo\x12\x1d.vc.svcaccount.GetUserInfoReq\x1a\x1e.vc.svcaccount.GetUserInfoResp\"\x00\x12~\n" +
	"\x1bGetUserInfoByCreationUserId\x12-.vc.svcaccount.GetUserInfoByCreationUserIdReq\x1a..vc.svcaccount.GetUserInfoByCreationUserIdResp\"\x00\x12]\n" +
	"\x10BatchGetUserInfo\x12\".vc.svcaccount.BatchGetUserInfoReq\x1a#.vc.svcaccount.BatchGetUserInfoResp\"\x00\x12U\n" +
	"\x0eUpdateUserInfo\x12 .vc.svcaccount.UpdateUserInfoReq\x1a!.vc.svcaccount.UpdateUserInfoResp\x12`\n" +
	"\x11SetPremiumCreator\x12#.vc.svcaccount.SetPremiumCreatorReq\x1a$.vc.svcaccount.SetPremiumCreatorResp\"\x00\x12T\n" +
	"\rOnlineLimiter\x12\x1f.vc.svcaccount.AccountOnlineReq\x1a .vc.svcaccount.AccountOnlineResp\"\x00\x12Z\n" +
	"\x0fIsUserBlacklist\x12!.vc.svcaccount.IsUserBlacklistReq\x1a\".vc.svcaccount.IsUserBlacklistResp\"\x00\x12Q\n" +
	"\fGetUserScore\x12\x1e.vc.svcaccount.GetUserScoreReq\x1a\x1f.vc.svcaccount.GetUserScoreResp\"\x00\x12Q\n" +
	"\fAddUserScore\x12\x1e.vc.svcaccount.AddUserScoreReq\x1a\x1f.vc.svcaccount.AddUserScoreResp\"\x00\x12]\n" +
	"\x10ConsumeUserScore\x12\".vc.svcaccount.ConsumeUserScoreReq\x1a#.vc.svcaccount.ConsumeUserScoreResp\"\x00\x12N\n" +
	"\vSearchUsers\x12\x1d.vc.svcaccount.SearchUsersReq\x1a\x1e.vc.svcaccount.SearchUsersResp\"\x00\x12K\n" +
	"\n" +
	"FollowUser\x12\x1c.vc.svcaccount.FollowUserReq\x1a\x1d.vc.svcaccount.FollowUserResp\"\x00\x12Q\n" +
	"\fUnfollowUser\x12\x1e.vc.svcaccount.UnfollowUserReq\x1a\x1f.vc.svcaccount.UnfollowUserResp\"\x00\x12W\n" +
	"\x0eCheckFollowing\x12 .vc.svcaccount.CheckFollowingReq\x1a!.vc.svcaccount.CheckFollowingResp\"\x00\x12`\n" +
	"\x11GetFollowingUsers\x12#.vc.svcaccount.GetFollowingUsersReq\x1a$.vc.svcaccount.GetFollowingUsersResp\"\x00\x12Q\n" +
	"\fGetFollowers\x12\x1e.vc.svcaccount.GetFollowersReq\x1a\x1f.vc.svcaccount.GetFollowersResp\"\x00\x12f\n" +
	"\x13GetFollowingUserIDs\x12%.vc.svcaccount.GetFollowingUserIDsReq\x1a&.vc.svcaccount.GetFollowingUserIDsResp\"\x00\x12f\n" +
	"\x13BatchCheckFollowing\x12%.vc.svcaccount.BatchCheckFollowingReq\x1a&.vc.svcaccount.BatchCheckFollowingResp\"\x00\x12I\n" +
	"\x0eReviewCallback\x12 .vc.svcaccount.ReviewCallbackReq\x1a\x15.common.SvcCommonResp\x12]\n" +
	"\x10IsPhoneWhitelist\x12\".vc.svcaccount.IsPhoneWhitelistReq\x1a#.vc.svcaccount.IsPhoneWhitelistResp\"\x00\x12W\n" +
	"\x0eGetLoginStatus\x12 .vc.svcaccount.GetLoginStatusReq\x1a!.vc.svcaccount.GetLoginStatusResp\"\x00\x12T\n" +
	"\rKickOffDevice\x12\x1f.vc.svcaccount.KickOffDeviceReq\x1a .vc.svcaccount.KickOffDeviceResp\"\x00\x12]\n" +
	"\x10GetDeviceHistory\x12\".vc.svcaccount.GetDeviceHistoryReq\x1a#.vc.svcaccount.GetDeviceHistoryResp\"\x00\x12N\n" +
	"\vVerifyToken\x12\x1d.vc.svcaccount.VerifyTokenReq\x1a\x1e.vc.svcaccount.VerifyTokenResp\"\x00\x12T\n" +
	"\rResetUserInfo\x12\x1f.vc.svcaccount.ResetUserInfoReq\x1a .vc.svcaccount.ResetUserInfoResp\"\x00\x12B\n" +
	"\aBanUser\x12\x19.vc.svcaccount.BanUserReq\x1a\x1a.vc.svcaccount.BanUserResp\"\x00\x12H\n" +
	"\tUnbanUser\x12\x1b.vc.svcaccount.UnbanUserReq\x1a\x1c.vc.svcaccount.UnbanUserResp\"\x00\x12f\n" +
	"\x13AdminUpdateUserInfo\x12%.vc.svcaccount.AdminUpdateUserInfoReq\x1a&.vc.svcaccount.AdminUpdateUserInfoResp\"\x00\x12Z\n" +
	"\x0fAdminCreateUser\x12!.vc.svcaccount.AdminCreateUserReq\x1a\".vc.svcaccount.AdminCreateUserResp\"\x00\x12G\n" +
	"\fReviewNotify\x12\x1e.vc.svcaccount.ReviewNotifyReq\x1a\x15.common.SvcCommonResp\"\x00BIZGnew-gitlab.xunlei.cn/vcproject/backends/proto/api/svcaccount;svcaccountb\x06proto3"

var (
	file_svcaccount_proto_rawDescOnce sync.Once
	file_svcaccount_proto_rawDescData []byte
)

func file_svcaccount_proto_rawDescGZIP() []byte {
	file_svcaccount_proto_rawDescOnce.Do(func() {
		file_svcaccount_proto_rawDescData = protoimpl.X.CompressGZIP(unsafe.Slice(unsafe.StringData(file_svcaccount_proto_rawDesc), len(file_svcaccount_proto_rawDesc)))
	})
	return file_svcaccount_proto_rawDescData
}

var file_svcaccount_proto_enumTypes = make([]protoimpl.EnumInfo, 2)
var file_svcaccount_proto_msgTypes = make([]protoimpl.MessageInfo, 87)
var file_svcaccount_proto_goTypes = []any{
	(OnConnectType)(0),                      // 0: vc.svcaccount.OnConnectType
	(ReviewNotifyType)(0),                   // 1: vc.svcaccount.ReviewNotifyType
	(*GetVerificationCodeReq)(nil),          // 2: vc.svcaccount.GetVerificationCodeReq
	(*GetVerificationCodeResp)(nil),         // 3: vc.svcaccount.GetVerificationCodeResp
	(*GetVerificationCodeRespData)(nil),     // 4: vc.svcaccount.GetVerificationCodeRespData
	(*AccountSignUpReq)(nil),                // 5: vc.svcaccount.AccountSignUpReq
	(*TokenRespData)(nil),                   // 6: vc.svcaccount.TokenRespData
	(*AccountSignUpResp)(nil),               // 7: vc.svcaccount.AccountSignUpResp
	(*AccountSignInReq)(nil),                // 8: vc.svcaccount.AccountSignInReq
	(*AccountSignInResp)(nil),               // 9: vc.svcaccount.AccountSignInResp
	(*AccountSignOutReq)(nil),               // 10: vc.svcaccount.AccountSignOutReq
	(*AccountSignOutResp)(nil),              // 11: vc.svcaccount.AccountSignOutResp
	(*RefreshTokenReq)(nil),                 // 12: vc.svcaccount.RefreshTokenReq
	(*RefreshTokenResp)(nil),                // 13: vc.svcaccount.RefreshTokenResp
	(*AccountDeleteReq)(nil),                // 14: vc.svcaccount.AccountDeleteReq
	(*AccountDeleteResp)(nil),               // 15: vc.svcaccount.AccountDeleteResp
	(*GetUserInfoReq)(nil),                  // 16: vc.svcaccount.GetUserInfoReq
	(*GetUserInfoByCreationUserIdReq)(nil),  // 17: vc.svcaccount.GetUserInfoByCreationUserIdReq
	(*GetUserInfoByCreationUserIdResp)(nil), // 18: vc.svcaccount.GetUserInfoByCreationUserIdResp
	(*GetUserInfoResp)(nil),                 // 19: vc.svcaccount.GetUserInfoResp
	(*UserInfo)(nil),                        // 20: vc.svcaccount.UserInfo
	(*FollowData)(nil),                      // 21: vc.svcaccount.FollowData
	(*VoiceSignature)(nil),                  // 22: vc.svcaccount.VoiceSignature
	(*BatchGetUserInfoReq)(nil),             // 23: vc.svcaccount.BatchGetUserInfoReq
	(*BatchGetUserInfoResp)(nil),            // 24: vc.svcaccount.BatchGetUserInfoResp
	(*UpdateUserInfoReq)(nil),               // 25: vc.svcaccount.UpdateUserInfoReq
	(*UpdateUserInfoResp)(nil),              // 26: vc.svcaccount.UpdateUserInfoResp
	(*SetPremiumCreatorReq)(nil),            // 27: vc.svcaccount.SetPremiumCreatorReq
	(*SetPremiumCreatorResp)(nil),           // 28: vc.svcaccount.SetPremiumCreatorResp
	(*AccountOnlineReq)(nil),                // 29: vc.svcaccount.AccountOnlineReq
	(*AccountOnlineResp)(nil),               // 30: vc.svcaccount.AccountOnlineResp
	(*IsUserBlacklistReq)(nil),              // 31: vc.svcaccount.IsUserBlacklistReq
	(*IsUserBlacklistResp)(nil),             // 32: vc.svcaccount.IsUserBlacklistResp
	(*GetUserScoreReq)(nil),                 // 33: vc.svcaccount.GetUserScoreReq
	(*GetUserScoreResp)(nil),                // 34: vc.svcaccount.GetUserScoreResp
	(*AddUserScoreReq)(nil),                 // 35: vc.svcaccount.AddUserScoreReq
	(*AddUserScoreResp)(nil),                // 36: vc.svcaccount.AddUserScoreResp
	(*ConsumeUserScoreReq)(nil),             // 37: vc.svcaccount.ConsumeUserScoreReq
	(*ConsumeUserScoreResp)(nil),            // 38: vc.svcaccount.ConsumeUserScoreResp
	(*ConsumeUserScoreRespData)(nil),        // 39: vc.svcaccount.ConsumeUserScoreRespData
	(*UserScoreData)(nil),                   // 40: vc.svcaccount.UserScoreData
	(*SearchUsersReq)(nil),                  // 41: vc.svcaccount.SearchUsersReq
	(*SearchUsersResp)(nil),                 // 42: vc.svcaccount.SearchUsersResp
	(*SearchUsersRespData)(nil),             // 43: vc.svcaccount.SearchUsersRespData
	(*FollowUserReq)(nil),                   // 44: vc.svcaccount.FollowUserReq
	(*FollowUserResp)(nil),                  // 45: vc.svcaccount.FollowUserResp
	(*UnfollowUserReq)(nil),                 // 46: vc.svcaccount.UnfollowUserReq
	(*UnfollowUserResp)(nil),                // 47: vc.svcaccount.UnfollowUserResp
	(*CheckFollowingReq)(nil),               // 48: vc.svcaccount.CheckFollowingReq
	(*CheckFollowingResp)(nil),              // 49: vc.svcaccount.CheckFollowingResp
	(*GetFollowingUsersReq)(nil),            // 50: vc.svcaccount.GetFollowingUsersReq
	(*GetFollowingUsersResp)(nil),           // 51: vc.svcaccount.GetFollowingUsersResp
	(*GetFollowingUsersRespData)(nil),       // 52: vc.svcaccount.GetFollowingUsersRespData
	(*GetFollowersReq)(nil),                 // 53: vc.svcaccount.GetFollowersReq
	(*GetFollowersResp)(nil),                // 54: vc.svcaccount.GetFollowersResp
	(*GetFollowersRespData)(nil),            // 55: vc.svcaccount.GetFollowersRespData
	(*GetFollowingUserIDsReq)(nil),          // 56: vc.svcaccount.GetFollowingUserIDsReq
	(*GetFollowingUserIDsResp)(nil),         // 57: vc.svcaccount.GetFollowingUserIDsResp
	(*GetFollowingUserIDsRespData)(nil),     // 58: vc.svcaccount.GetFollowingUserIDsRespData
	(*BatchCheckFollowingReq)(nil),          // 59: vc.svcaccount.BatchCheckFollowingReq
	(*BatchCheckFollowingResp)(nil),         // 60: vc.svcaccount.BatchCheckFollowingResp
	(*FollowDataItem)(nil),                  // 61: vc.svcaccount.FollowDataItem
	(*ReviewCallbackReq)(nil),               // 62: vc.svcaccount.ReviewCallbackReq
	(*IsPhoneWhitelistReq)(nil),             // 63: vc.svcaccount.IsPhoneWhitelistReq
	(*IsPhoneWhitelistResp)(nil),            // 64: vc.svcaccount.IsPhoneWhitelistResp
	(*GetLoginStatusReq)(nil),               // 65: vc.svcaccount.GetLoginStatusReq
	(*LoginStatusInfo)(nil),                 // 66: vc.svcaccount.LoginStatusInfo
	(*GetLoginStatusResp)(nil),              // 67: vc.svcaccount.GetLoginStatusResp
	(*KickOffDeviceReq)(nil),                // 68: vc.svcaccount.KickOffDeviceReq
	(*KickOffDeviceResp)(nil),               // 69: vc.svcaccount.KickOffDeviceResp
	(*DeviceHistoryInfo)(nil),               // 70: vc.svcaccount.DeviceHistoryInfo
	(*GetDeviceHistoryReq)(nil),             // 71: vc.svcaccount.GetDeviceHistoryReq
	(*GetDeviceHistoryResp)(nil),            // 72: vc.svcaccount.GetDeviceHistoryResp
	(*VerifyTokenReq)(nil),                  // 73: vc.svcaccount.VerifyTokenReq
	(*TokenVerifyInfo)(nil),                 // 74: vc.svcaccount.TokenVerifyInfo
	(*VerifyTokenResp)(nil),                 // 75: vc.svcaccount.VerifyTokenResp
	(*ResetUserInfoReq)(nil),                // 76: vc.svcaccount.ResetUserInfoReq
	(*ResetUserInfoResp)(nil),               // 77: vc.svcaccount.ResetUserInfoResp
	(*BanUserReq)(nil),                      // 78: vc.svcaccount.BanUserReq
	(*BanUserResp)(nil),                     // 79: vc.svcaccount.BanUserResp
	(*UnbanUserReq)(nil),                    // 80: vc.svcaccount.UnbanUserReq
	(*UnbanUserResp)(nil),                   // 81: vc.svcaccount.UnbanUserResp
	(*AdminUpdateUserInfoReq)(nil),          // 82: vc.svcaccount.AdminUpdateUserInfoReq
	(*AdminUpdateUserInfoResp)(nil),         // 83: vc.svcaccount.AdminUpdateUserInfoResp
	(*AdminCreateUserReq)(nil),              // 84: vc.svcaccount.AdminCreateUserReq
	(*AdminCreateUserResp)(nil),             // 85: vc.svcaccount.AdminCreateUserResp
	(*ReviewNotifyReq)(nil),                 // 86: vc.svcaccount.ReviewNotifyReq
	nil,                                     // 87: vc.svcaccount.BatchGetUserInfoResp.UserInfoMapEntry
	nil,                                     // 88: vc.svcaccount.FollowDataItem.ItemsEntry
	(*common.SvcBaseResp)(nil),              // 89: common.SvcBaseResp
	(*common.BaseParam)(nil),                // 90: common.BaseParam
	(svcreview.ReviewBizType)(0),            // 91: vc.svcreview.ReviewBizType
	(svcreview.AuditResult)(0),              // 92: vc.svcreview.AuditResult
	(*common.SvcCommonResp)(nil),            // 93: common.SvcCommonResp
}
var file_svcaccount_proto_depIdxs = []int32{
	89,  // 0: vc.svcaccount.GetVerificationCodeResp.base:type_name -> common.SvcBaseResp
	4,   // 1: vc.svcaccount.GetVerificationCodeResp.data:type_name -> vc.svcaccount.GetVerificationCodeRespData
	89,  // 2: vc.svcaccount.AccountSignUpResp.base:type_name -> common.SvcBaseResp
	6,   // 3: vc.svcaccount.AccountSignUpResp.data:type_name -> vc.svcaccount.TokenRespData
	89,  // 4: vc.svcaccount.AccountSignInResp.base:type_name -> common.SvcBaseResp
	6,   // 5: vc.svcaccount.AccountSignInResp.data:type_name -> vc.svcaccount.TokenRespData
	89,  // 6: vc.svcaccount.AccountSignOutResp.base:type_name -> common.SvcBaseResp
	89,  // 7: vc.svcaccount.RefreshTokenResp.base:type_name -> common.SvcBaseResp
	6,   // 8: vc.svcaccount.RefreshTokenResp.data:type_name -> vc.svcaccount.TokenRespData
	89,  // 9: vc.svcaccount.AccountDeleteResp.base:type_name -> common.SvcBaseResp
	89,  // 10: vc.svcaccount.GetUserInfoByCreationUserIdResp.base:type_name -> common.SvcBaseResp
	20,  // 11: vc.svcaccount.GetUserInfoByCreationUserIdResp.data:type_name -> vc.svcaccount.UserInfo
	89,  // 12: vc.svcaccount.GetUserInfoResp.base:type_name -> common.SvcBaseResp
	20,  // 13: vc.svcaccount.GetUserInfoResp.data:type_name -> vc.svcaccount.UserInfo
	22,  // 14: vc.svcaccount.UserInfo.voice_signature:type_name -> vc.svcaccount.VoiceSignature
	40,  // 15: vc.svcaccount.UserInfo.score_data:type_name -> vc.svcaccount.UserScoreData
	89,  // 16: vc.svcaccount.BatchGetUserInfoResp.base:type_name -> common.SvcBaseResp
	87,  // 17: vc.svcaccount.BatchGetUserInfoResp.user_info_map:type_name -> vc.svcaccount.BatchGetUserInfoResp.UserInfoMapEntry
	89,  // 18: vc.svcaccount.UpdateUserInfoResp.base:type_name -> common.SvcBaseResp
	89,  // 19: vc.svcaccount.SetPremiumCreatorResp.base:type_name -> common.SvcBaseResp
	90,  // 20: vc.svcaccount.AccountOnlineReq.data:type_name -> common.BaseParam
	0,   // 21: vc.svcaccount.AccountOnlineReq.on_connect_type:type_name -> vc.svcaccount.OnConnectType
	89,  // 22: vc.svcaccount.AccountOnlineResp.base:type_name -> common.SvcBaseResp
	89,  // 23: vc.svcaccount.IsUserBlacklistResp.base:type_name -> common.SvcBaseResp
	89,  // 24: vc.svcaccount.GetUserScoreResp.base:type_name -> common.SvcBaseResp
	40,  // 25: vc.svcaccount.GetUserScoreResp.data:type_name -> vc.svcaccount.UserScoreData
	89,  // 26: vc.svcaccount.AddUserScoreResp.base:type_name -> common.SvcBaseResp
	89,  // 27: vc.svcaccount.ConsumeUserScoreResp.base:type_name -> common.SvcBaseResp
	39,  // 28: vc.svcaccount.ConsumeUserScoreResp.data:type_name -> vc.svcaccount.ConsumeUserScoreRespData
	89,  // 29: vc.svcaccount.SearchUsersResp.base:type_name -> common.SvcBaseResp
	43,  // 30: vc.svcaccount.SearchUsersResp.data:type_name -> vc.svcaccount.SearchUsersRespData
	20,  // 31: vc.svcaccount.SearchUsersRespData.users:type_name -> vc.svcaccount.UserInfo
	89,  // 32: vc.svcaccount.FollowUserResp.base:type_name -> common.SvcBaseResp
	89,  // 33: vc.svcaccount.UnfollowUserResp.base:type_name -> common.SvcBaseResp
	89,  // 34: vc.svcaccount.CheckFollowingResp.base:type_name -> common.SvcBaseResp
	89,  // 35: vc.svcaccount.GetFollowingUsersResp.base:type_name -> common.SvcBaseResp
	52,  // 36: vc.svcaccount.GetFollowingUsersResp.data:type_name -> vc.svcaccount.GetFollowingUsersRespData
	20,  // 37: vc.svcaccount.GetFollowingUsersRespData.users:type_name -> vc.svcaccount.UserInfo
	89,  // 38: vc.svcaccount.GetFollowersResp.base:type_name -> common.SvcBaseResp
	55,  // 39: vc.svcaccount.GetFollowersResp.data:type_name -> vc.svcaccount.GetFollowersRespData
	20,  // 40: vc.svcaccount.GetFollowersRespData.users:type_name -> vc.svcaccount.UserInfo
	89,  // 41: vc.svcaccount.GetFollowingUserIDsResp.base:type_name -> common.SvcBaseResp
	58,  // 42: vc.svcaccount.GetFollowingUserIDsResp.data:type_name -> vc.svcaccount.GetFollowingUserIDsRespData
	89,  // 43: vc.svcaccount.BatchCheckFollowingResp.base:type_name -> common.SvcBaseResp
	61,  // 44: vc.svcaccount.BatchCheckFollowingResp.data:type_name -> vc.svcaccount.FollowDataItem
	88,  // 45: vc.svcaccount.FollowDataItem.items:type_name -> vc.svcaccount.FollowDataItem.ItemsEntry
	91,  // 46: vc.svcaccount.ReviewCallbackReq.type:type_name -> vc.svcreview.ReviewBizType
	92,  // 47: vc.svcaccount.ReviewCallbackReq.result:type_name -> vc.svcreview.AuditResult
	89,  // 48: vc.svcaccount.IsPhoneWhitelistResp.base:type_name -> common.SvcBaseResp
	89,  // 49: vc.svcaccount.GetLoginStatusResp.base:type_name -> common.SvcBaseResp
	66,  // 50: vc.svcaccount.GetLoginStatusResp.data:type_name -> vc.svcaccount.LoginStatusInfo
	89,  // 51: vc.svcaccount.KickOffDeviceResp.base:type_name -> common.SvcBaseResp
	89,  // 52: vc.svcaccount.GetDeviceHistoryResp.base:type_name -> common.SvcBaseResp
	70,  // 53: vc.svcaccount.GetDeviceHistoryResp.data:type_name -> vc.svcaccount.DeviceHistoryInfo
	89,  // 54: vc.svcaccount.VerifyTokenResp.base:type_name -> common.SvcBaseResp
	74,  // 55: vc.svcaccount.VerifyTokenResp.data:type_name -> vc.svcaccount.TokenVerifyInfo
	89,  // 56: vc.svcaccount.ResetUserInfoResp.base:type_name -> common.SvcBaseResp
	89,  // 57: vc.svcaccount.BanUserResp.base:type_name -> common.SvcBaseResp
	89,  // 58: vc.svcaccount.UnbanUserResp.base:type_name -> common.SvcBaseResp
	89,  // 59: vc.svcaccount.AdminUpdateUserInfoResp.base:type_name -> common.SvcBaseResp
	89,  // 60: vc.svcaccount.AdminCreateUserResp.base:type_name -> common.SvcBaseResp
	1,   // 61: vc.svcaccount.ReviewNotifyReq.review_notify_type:type_name -> vc.svcaccount.ReviewNotifyType
	20,  // 62: vc.svcaccount.BatchGetUserInfoResp.UserInfoMapEntry.value:type_name -> vc.svcaccount.UserInfo
	21,  // 63: vc.svcaccount.FollowDataItem.ItemsEntry.value:type_name -> vc.svcaccount.FollowData
	2,   // 64: vc.svcaccount.s.GetVerificationCode:input_type -> vc.svcaccount.GetVerificationCodeReq
	5,   // 65: vc.svcaccount.s.AccountSignUp:input_type -> vc.svcaccount.AccountSignUpReq
	8,   // 66: vc.svcaccount.s.AccountSignIn:input_type -> vc.svcaccount.AccountSignInReq
	10,  // 67: vc.svcaccount.s.AccountSignOut:input_type -> vc.svcaccount.AccountSignOutReq
	12,  // 68: vc.svcaccount.s.RefreshToken:input_type -> vc.svcaccount.RefreshTokenReq
	14,  // 69: vc.svcaccount.s.AccountDelete:input_type -> vc.svcaccount.AccountDeleteReq
	16,  // 70: vc.svcaccount.s.GetUserInfo:input_type -> vc.svcaccount.GetUserInfoReq
	17,  // 71: vc.svcaccount.s.GetUserInfoByCreationUserId:input_type -> vc.svcaccount.GetUserInfoByCreationUserIdReq
	23,  // 72: vc.svcaccount.s.BatchGetUserInfo:input_type -> vc.svcaccount.BatchGetUserInfoReq
	25,  // 73: vc.svcaccount.s.UpdateUserInfo:input_type -> vc.svcaccount.UpdateUserInfoReq
	27,  // 74: vc.svcaccount.s.SetPremiumCreator:input_type -> vc.svcaccount.SetPremiumCreatorReq
	29,  // 75: vc.svcaccount.s.OnlineLimiter:input_type -> vc.svcaccount.AccountOnlineReq
	31,  // 76: vc.svcaccount.s.IsUserBlacklist:input_type -> vc.svcaccount.IsUserBlacklistReq
	33,  // 77: vc.svcaccount.s.GetUserScore:input_type -> vc.svcaccount.GetUserScoreReq
	35,  // 78: vc.svcaccount.s.AddUserScore:input_type -> vc.svcaccount.AddUserScoreReq
	37,  // 79: vc.svcaccount.s.ConsumeUserScore:input_type -> vc.svcaccount.ConsumeUserScoreReq
	41,  // 80: vc.svcaccount.s.SearchUsers:input_type -> vc.svcaccount.SearchUsersReq
	44,  // 81: vc.svcaccount.s.FollowUser:input_type -> vc.svcaccount.FollowUserReq
	46,  // 82: vc.svcaccount.s.UnfollowUser:input_type -> vc.svcaccount.UnfollowUserReq
	48,  // 83: vc.svcaccount.s.CheckFollowing:input_type -> vc.svcaccount.CheckFollowingReq
	50,  // 84: vc.svcaccount.s.GetFollowingUsers:input_type -> vc.svcaccount.GetFollowingUsersReq
	53,  // 85: vc.svcaccount.s.GetFollowers:input_type -> vc.svcaccount.GetFollowersReq
	56,  // 86: vc.svcaccount.s.GetFollowingUserIDs:input_type -> vc.svcaccount.GetFollowingUserIDsReq
	59,  // 87: vc.svcaccount.s.BatchCheckFollowing:input_type -> vc.svcaccount.BatchCheckFollowingReq
	62,  // 88: vc.svcaccount.s.ReviewCallback:input_type -> vc.svcaccount.ReviewCallbackReq
	63,  // 89: vc.svcaccount.s.IsPhoneWhitelist:input_type -> vc.svcaccount.IsPhoneWhitelistReq
	65,  // 90: vc.svcaccount.s.GetLoginStatus:input_type -> vc.svcaccount.GetLoginStatusReq
	68,  // 91: vc.svcaccount.s.KickOffDevice:input_type -> vc.svcaccount.KickOffDeviceReq
	71,  // 92: vc.svcaccount.s.GetDeviceHistory:input_type -> vc.svcaccount.GetDeviceHistoryReq
	73,  // 93: vc.svcaccount.s.VerifyToken:input_type -> vc.svcaccount.VerifyTokenReq
	76,  // 94: vc.svcaccount.s.ResetUserInfo:input_type -> vc.svcaccount.ResetUserInfoReq
	78,  // 95: vc.svcaccount.s.BanUser:input_type -> vc.svcaccount.BanUserReq
	80,  // 96: vc.svcaccount.s.UnbanUser:input_type -> vc.svcaccount.UnbanUserReq
	82,  // 97: vc.svcaccount.s.AdminUpdateUserInfo:input_type -> vc.svcaccount.AdminUpdateUserInfoReq
	84,  // 98: vc.svcaccount.s.AdminCreateUser:input_type -> vc.svcaccount.AdminCreateUserReq
	86,  // 99: vc.svcaccount.s.ReviewNotify:input_type -> vc.svcaccount.ReviewNotifyReq
	3,   // 100: vc.svcaccount.s.GetVerificationCode:output_type -> vc.svcaccount.GetVerificationCodeResp
	7,   // 101: vc.svcaccount.s.AccountSignUp:output_type -> vc.svcaccount.AccountSignUpResp
	9,   // 102: vc.svcaccount.s.AccountSignIn:output_type -> vc.svcaccount.AccountSignInResp
	11,  // 103: vc.svcaccount.s.AccountSignOut:output_type -> vc.svcaccount.AccountSignOutResp
	13,  // 104: vc.svcaccount.s.RefreshToken:output_type -> vc.svcaccount.RefreshTokenResp
	15,  // 105: vc.svcaccount.s.AccountDelete:output_type -> vc.svcaccount.AccountDeleteResp
	19,  // 106: vc.svcaccount.s.GetUserInfo:output_type -> vc.svcaccount.GetUserInfoResp
	18,  // 107: vc.svcaccount.s.GetUserInfoByCreationUserId:output_type -> vc.svcaccount.GetUserInfoByCreationUserIdResp
	24,  // 108: vc.svcaccount.s.BatchGetUserInfo:output_type -> vc.svcaccount.BatchGetUserInfoResp
	26,  // 109: vc.svcaccount.s.UpdateUserInfo:output_type -> vc.svcaccount.UpdateUserInfoResp
	28,  // 110: vc.svcaccount.s.SetPremiumCreator:output_type -> vc.svcaccount.SetPremiumCreatorResp
	30,  // 111: vc.svcaccount.s.OnlineLimiter:output_type -> vc.svcaccount.AccountOnlineResp
	32,  // 112: vc.svcaccount.s.IsUserBlacklist:output_type -> vc.svcaccount.IsUserBlacklistResp
	34,  // 113: vc.svcaccount.s.GetUserScore:output_type -> vc.svcaccount.GetUserScoreResp
	36,  // 114: vc.svcaccount.s.AddUserScore:output_type -> vc.svcaccount.AddUserScoreResp
	38,  // 115: vc.svcaccount.s.ConsumeUserScore:output_type -> vc.svcaccount.ConsumeUserScoreResp
	42,  // 116: vc.svcaccount.s.SearchUsers:output_type -> vc.svcaccount.SearchUsersResp
	45,  // 117: vc.svcaccount.s.FollowUser:output_type -> vc.svcaccount.FollowUserResp
	47,  // 118: vc.svcaccount.s.UnfollowUser:output_type -> vc.svcaccount.UnfollowUserResp
	49,  // 119: vc.svcaccount.s.CheckFollowing:output_type -> vc.svcaccount.CheckFollowingResp
	51,  // 120: vc.svcaccount.s.GetFollowingUsers:output_type -> vc.svcaccount.GetFollowingUsersResp
	54,  // 121: vc.svcaccount.s.GetFollowers:output_type -> vc.svcaccount.GetFollowersResp
	57,  // 122: vc.svcaccount.s.GetFollowingUserIDs:output_type -> vc.svcaccount.GetFollowingUserIDsResp
	60,  // 123: vc.svcaccount.s.BatchCheckFollowing:output_type -> vc.svcaccount.BatchCheckFollowingResp
	93,  // 124: vc.svcaccount.s.ReviewCallback:output_type -> common.SvcCommonResp
	64,  // 125: vc.svcaccount.s.IsPhoneWhitelist:output_type -> vc.svcaccount.IsPhoneWhitelistResp
	67,  // 126: vc.svcaccount.s.GetLoginStatus:output_type -> vc.svcaccount.GetLoginStatusResp
	69,  // 127: vc.svcaccount.s.KickOffDevice:output_type -> vc.svcaccount.KickOffDeviceResp
	72,  // 128: vc.svcaccount.s.GetDeviceHistory:output_type -> vc.svcaccount.GetDeviceHistoryResp
	75,  // 129: vc.svcaccount.s.VerifyToken:output_type -> vc.svcaccount.VerifyTokenResp
	77,  // 130: vc.svcaccount.s.ResetUserInfo:output_type -> vc.svcaccount.ResetUserInfoResp
	79,  // 131: vc.svcaccount.s.BanUser:output_type -> vc.svcaccount.BanUserResp
	81,  // 132: vc.svcaccount.s.UnbanUser:output_type -> vc.svcaccount.UnbanUserResp
	83,  // 133: vc.svcaccount.s.AdminUpdateUserInfo:output_type -> vc.svcaccount.AdminUpdateUserInfoResp
	85,  // 134: vc.svcaccount.s.AdminCreateUser:output_type -> vc.svcaccount.AdminCreateUserResp
	93,  // 135: vc.svcaccount.s.ReviewNotify:output_type -> common.SvcCommonResp
	100, // [100:136] is the sub-list for method output_type
	64,  // [64:100] is the sub-list for method input_type
	64,  // [64:64] is the sub-list for extension type_name
	64,  // [64:64] is the sub-list for extension extendee
	0,   // [0:64] is the sub-list for field type_name
}

func init() { file_svcaccount_proto_init() }
func file_svcaccount_proto_init() {
	if File_svcaccount_proto != nil {
		return
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: unsafe.Slice(unsafe.StringData(file_svcaccount_proto_rawDesc), len(file_svcaccount_proto_rawDesc)),
			NumEnums:      2,
			NumMessages:   87,
			NumExtensions: 0,
			NumServices:   1,
		},
		GoTypes:           file_svcaccount_proto_goTypes,
		DependencyIndexes: file_svcaccount_proto_depIdxs,
		EnumInfos:         file_svcaccount_proto_enumTypes,
		MessageInfos:      file_svcaccount_proto_msgTypes,
	}.Build()
	File_svcaccount_proto = out.File
	file_svcaccount_proto_goTypes = nil
	file_svcaccount_proto_depIdxs = nil
}
