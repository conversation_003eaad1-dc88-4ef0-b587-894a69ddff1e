// Code generated by protoc-gen-go-grpc. DO NOT EDIT.
// versions:
// - protoc-gen-go-grpc v1.5.1
// - protoc             v5.29.3
// source: svcaccount.proto

package svcaccount

import (
	context "context"
	grpc "google.golang.org/grpc"
	codes "google.golang.org/grpc/codes"
	status "google.golang.org/grpc/status"
	common "new-gitlab.xunlei.cn/vcproject/backends/proto/api/common"
)

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
// Requires gRPC-Go v1.64.0 or later.
const _ = grpc.SupportPackageIsVersion9

const (
	S_GetVerificationCode_FullMethodName         = "/vc.svcaccount.s/GetVerificationCode"
	S_AccountSignUp_FullMethodName               = "/vc.svcaccount.s/AccountSignUp"
	S_AccountSignIn_FullMethodName               = "/vc.svcaccount.s/AccountSignIn"
	S_AccountSignOut_FullMethodName              = "/vc.svcaccount.s/AccountSignOut"
	S_RefreshToken_FullMethodName                = "/vc.svcaccount.s/RefreshToken"
	S_AccountDelete_FullMethodName               = "/vc.svcaccount.s/AccountDelete"
	S_GetUserInfo_FullMethodName                 = "/vc.svcaccount.s/GetUserInfo"
	S_GetUserInfoByCreationUserId_FullMethodName = "/vc.svcaccount.s/GetUserInfoByCreationUserId"
	S_BatchGetUserInfo_FullMethodName            = "/vc.svcaccount.s/BatchGetUserInfo"
	S_UpdateUserInfo_FullMethodName              = "/vc.svcaccount.s/UpdateUserInfo"
	S_SetPremiumCreator_FullMethodName           = "/vc.svcaccount.s/SetPremiumCreator"
	S_OnlineLimiter_FullMethodName               = "/vc.svcaccount.s/OnlineLimiter"
	S_IsUserBlacklist_FullMethodName             = "/vc.svcaccount.s/IsUserBlacklist"
	S_GetUserScore_FullMethodName                = "/vc.svcaccount.s/GetUserScore"
	S_AddUserScore_FullMethodName                = "/vc.svcaccount.s/AddUserScore"
	S_ConsumeUserScore_FullMethodName            = "/vc.svcaccount.s/ConsumeUserScore"
	S_SearchUsers_FullMethodName                 = "/vc.svcaccount.s/SearchUsers"
	S_FollowUser_FullMethodName                  = "/vc.svcaccount.s/FollowUser"
	S_UnfollowUser_FullMethodName                = "/vc.svcaccount.s/UnfollowUser"
	S_CheckFollowing_FullMethodName              = "/vc.svcaccount.s/CheckFollowing"
	S_GetFollowingUsers_FullMethodName           = "/vc.svcaccount.s/GetFollowingUsers"
	S_GetFollowers_FullMethodName                = "/vc.svcaccount.s/GetFollowers"
	S_GetFollowingUserIDs_FullMethodName         = "/vc.svcaccount.s/GetFollowingUserIDs"
	S_BatchCheckFollowing_FullMethodName         = "/vc.svcaccount.s/BatchCheckFollowing"
	S_ReviewCallback_FullMethodName              = "/vc.svcaccount.s/ReviewCallback"
	S_IsPhoneWhitelist_FullMethodName            = "/vc.svcaccount.s/IsPhoneWhitelist"
	S_GetLoginStatus_FullMethodName              = "/vc.svcaccount.s/GetLoginStatus"
	S_KickOffDevice_FullMethodName               = "/vc.svcaccount.s/KickOffDevice"
	S_GetDeviceHistory_FullMethodName            = "/vc.svcaccount.s/GetDeviceHistory"
	S_VerifyToken_FullMethodName                 = "/vc.svcaccount.s/VerifyToken"
	S_ResetUserInfo_FullMethodName               = "/vc.svcaccount.s/ResetUserInfo"
	S_BanUser_FullMethodName                     = "/vc.svcaccount.s/BanUser"
	S_UnbanUser_FullMethodName                   = "/vc.svcaccount.s/UnbanUser"
	S_AdminUpdateUserInfo_FullMethodName         = "/vc.svcaccount.s/AdminUpdateUserInfo"
	S_AdminCreateUser_FullMethodName             = "/vc.svcaccount.s/AdminCreateUser"
	S_ReviewNotify_FullMethodName                = "/vc.svcaccount.s/ReviewNotify"
)

// SClient is the client API for S service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://pkg.go.dev/google.golang.org/grpc/?tab=doc#ClientConn.NewStream.
type SClient interface {
	// 获取验证码
	GetVerificationCode(ctx context.Context, in *GetVerificationCodeReq, opts ...grpc.CallOption) (*GetVerificationCodeResp, error)
	// 注册
	AccountSignUp(ctx context.Context, in *AccountSignUpReq, opts ...grpc.CallOption) (*AccountSignUpResp, error)
	// 登录
	AccountSignIn(ctx context.Context, in *AccountSignInReq, opts ...grpc.CallOption) (*AccountSignInResp, error)
	// 退出登录
	AccountSignOut(ctx context.Context, in *AccountSignOutReq, opts ...grpc.CallOption) (*AccountSignOutResp, error)
	// 刷新token
	RefreshToken(ctx context.Context, in *RefreshTokenReq, opts ...grpc.CallOption) (*RefreshTokenResp, error)
	// 注销账号
	AccountDelete(ctx context.Context, in *AccountDeleteReq, opts ...grpc.CallOption) (*AccountDeleteResp, error)
	// 获取用户信息
	GetUserInfo(ctx context.Context, in *GetUserInfoReq, opts ...grpc.CallOption) (*GetUserInfoResp, error)
	// 根据admin用户ID获取关联的业务用户信息
	GetUserInfoByCreationUserId(ctx context.Context, in *GetUserInfoByCreationUserIdReq, opts ...grpc.CallOption) (*GetUserInfoByCreationUserIdResp, error)
	// 批量获取用户信息
	BatchGetUserInfo(ctx context.Context, in *BatchGetUserInfoReq, opts ...grpc.CallOption) (*BatchGetUserInfoResp, error)
	// 更新用户信息
	UpdateUserInfo(ctx context.Context, in *UpdateUserInfoReq, opts ...grpc.CallOption) (*UpdateUserInfoResp, error)
	// 设置用户为优质创作者
	SetPremiumCreator(ctx context.Context, in *SetPremiumCreatorReq, opts ...grpc.CallOption) (*SetPremiumCreatorResp, error)
	// 状态上报
	OnlineLimiter(ctx context.Context, in *AccountOnlineReq, opts ...grpc.CallOption) (*AccountOnlineResp, error)
	// 用户是否拉黑
	IsUserBlacklist(ctx context.Context, in *IsUserBlacklistReq, opts ...grpc.CallOption) (*IsUserBlacklistResp, error)
	// 获取用户积分
	GetUserScore(ctx context.Context, in *GetUserScoreReq, opts ...grpc.CallOption) (*GetUserScoreResp, error)
	// 增加用户积分
	AddUserScore(ctx context.Context, in *AddUserScoreReq, opts ...grpc.CallOption) (*AddUserScoreResp, error)
	// 消耗用户积分
	ConsumeUserScore(ctx context.Context, in *ConsumeUserScoreReq, opts ...grpc.CallOption) (*ConsumeUserScoreResp, error)
	// 搜索用户
	SearchUsers(ctx context.Context, in *SearchUsersReq, opts ...grpc.CallOption) (*SearchUsersResp, error)
	// 关注用户
	FollowUser(ctx context.Context, in *FollowUserReq, opts ...grpc.CallOption) (*FollowUserResp, error)
	// 取消关注用户
	UnfollowUser(ctx context.Context, in *UnfollowUserReq, opts ...grpc.CallOption) (*UnfollowUserResp, error)
	// 检查是否关注
	CheckFollowing(ctx context.Context, in *CheckFollowingReq, opts ...grpc.CallOption) (*CheckFollowingResp, error)
	// 获取关注的用户列表
	GetFollowingUsers(ctx context.Context, in *GetFollowingUsersReq, opts ...grpc.CallOption) (*GetFollowingUsersResp, error)
	// 获取粉丝列表
	GetFollowers(ctx context.Context, in *GetFollowersReq, opts ...grpc.CallOption) (*GetFollowersResp, error)
	// 获取关注用户ID列表（精简版，只返回用户ID）
	GetFollowingUserIDs(ctx context.Context, in *GetFollowingUserIDsReq, opts ...grpc.CallOption) (*GetFollowingUserIDsResp, error)
	// 批量检查是否关注
	BatchCheckFollowing(ctx context.Context, in *BatchCheckFollowingReq, opts ...grpc.CallOption) (*BatchCheckFollowingResp, error)
	// 用户审核callback （语音签名）
	ReviewCallback(ctx context.Context, in *ReviewCallbackReq, opts ...grpc.CallOption) (*common.SvcCommonResp, error)
	// 检查手机号是否在白名单
	IsPhoneWhitelist(ctx context.Context, in *IsPhoneWhitelistReq, opts ...grpc.CallOption) (*IsPhoneWhitelistResp, error)
	// 获取登录状态
	GetLoginStatus(ctx context.Context, in *GetLoginStatusReq, opts ...grpc.CallOption) (*GetLoginStatusResp, error)
	// 踢下线设备
	KickOffDevice(ctx context.Context, in *KickOffDeviceReq, opts ...grpc.CallOption) (*KickOffDeviceResp, error)
	// 获取设备登录历史
	GetDeviceHistory(ctx context.Context, in *GetDeviceHistoryReq, opts ...grpc.CallOption) (*GetDeviceHistoryResp, error)
	// 验证Token（包括黑名单检查）
	VerifyToken(ctx context.Context, in *VerifyTokenReq, opts ...grpc.CallOption) (*VerifyTokenResp, error)
	// 重置用户信息
	ResetUserInfo(ctx context.Context, in *ResetUserInfoReq, opts ...grpc.CallOption) (*ResetUserInfoResp, error)
	// 封禁用户
	BanUser(ctx context.Context, in *BanUserReq, opts ...grpc.CallOption) (*BanUserResp, error)
	// 解封用户
	UnbanUser(ctx context.Context, in *UnbanUserReq, opts ...grpc.CallOption) (*UnbanUserResp, error)
	// Admin审核更新用户信息（不触发审核逻辑）
	AdminUpdateUserInfo(ctx context.Context, in *AdminUpdateUserInfoReq, opts ...grpc.CallOption) (*AdminUpdateUserInfoResp, error)
	// Admin创建用户
	AdminCreateUser(ctx context.Context, in *AdminCreateUserReq, opts ...grpc.CallOption) (*AdminCreateUserResp, error)
	// 审核通知
	ReviewNotify(ctx context.Context, in *ReviewNotifyReq, opts ...grpc.CallOption) (*common.SvcCommonResp, error)
}

type sClient struct {
	cc grpc.ClientConnInterface
}

func NewSClient(cc grpc.ClientConnInterface) SClient {
	return &sClient{cc}
}

func (c *sClient) GetVerificationCode(ctx context.Context, in *GetVerificationCodeReq, opts ...grpc.CallOption) (*GetVerificationCodeResp, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(GetVerificationCodeResp)
	err := c.cc.Invoke(ctx, S_GetVerificationCode_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *sClient) AccountSignUp(ctx context.Context, in *AccountSignUpReq, opts ...grpc.CallOption) (*AccountSignUpResp, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(AccountSignUpResp)
	err := c.cc.Invoke(ctx, S_AccountSignUp_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *sClient) AccountSignIn(ctx context.Context, in *AccountSignInReq, opts ...grpc.CallOption) (*AccountSignInResp, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(AccountSignInResp)
	err := c.cc.Invoke(ctx, S_AccountSignIn_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *sClient) AccountSignOut(ctx context.Context, in *AccountSignOutReq, opts ...grpc.CallOption) (*AccountSignOutResp, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(AccountSignOutResp)
	err := c.cc.Invoke(ctx, S_AccountSignOut_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *sClient) RefreshToken(ctx context.Context, in *RefreshTokenReq, opts ...grpc.CallOption) (*RefreshTokenResp, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(RefreshTokenResp)
	err := c.cc.Invoke(ctx, S_RefreshToken_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *sClient) AccountDelete(ctx context.Context, in *AccountDeleteReq, opts ...grpc.CallOption) (*AccountDeleteResp, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(AccountDeleteResp)
	err := c.cc.Invoke(ctx, S_AccountDelete_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *sClient) GetUserInfo(ctx context.Context, in *GetUserInfoReq, opts ...grpc.CallOption) (*GetUserInfoResp, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(GetUserInfoResp)
	err := c.cc.Invoke(ctx, S_GetUserInfo_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *sClient) GetUserInfoByCreationUserId(ctx context.Context, in *GetUserInfoByCreationUserIdReq, opts ...grpc.CallOption) (*GetUserInfoByCreationUserIdResp, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(GetUserInfoByCreationUserIdResp)
	err := c.cc.Invoke(ctx, S_GetUserInfoByCreationUserId_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *sClient) BatchGetUserInfo(ctx context.Context, in *BatchGetUserInfoReq, opts ...grpc.CallOption) (*BatchGetUserInfoResp, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(BatchGetUserInfoResp)
	err := c.cc.Invoke(ctx, S_BatchGetUserInfo_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *sClient) UpdateUserInfo(ctx context.Context, in *UpdateUserInfoReq, opts ...grpc.CallOption) (*UpdateUserInfoResp, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(UpdateUserInfoResp)
	err := c.cc.Invoke(ctx, S_UpdateUserInfo_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *sClient) SetPremiumCreator(ctx context.Context, in *SetPremiumCreatorReq, opts ...grpc.CallOption) (*SetPremiumCreatorResp, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(SetPremiumCreatorResp)
	err := c.cc.Invoke(ctx, S_SetPremiumCreator_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *sClient) OnlineLimiter(ctx context.Context, in *AccountOnlineReq, opts ...grpc.CallOption) (*AccountOnlineResp, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(AccountOnlineResp)
	err := c.cc.Invoke(ctx, S_OnlineLimiter_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *sClient) IsUserBlacklist(ctx context.Context, in *IsUserBlacklistReq, opts ...grpc.CallOption) (*IsUserBlacklistResp, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(IsUserBlacklistResp)
	err := c.cc.Invoke(ctx, S_IsUserBlacklist_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *sClient) GetUserScore(ctx context.Context, in *GetUserScoreReq, opts ...grpc.CallOption) (*GetUserScoreResp, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(GetUserScoreResp)
	err := c.cc.Invoke(ctx, S_GetUserScore_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *sClient) AddUserScore(ctx context.Context, in *AddUserScoreReq, opts ...grpc.CallOption) (*AddUserScoreResp, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(AddUserScoreResp)
	err := c.cc.Invoke(ctx, S_AddUserScore_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *sClient) ConsumeUserScore(ctx context.Context, in *ConsumeUserScoreReq, opts ...grpc.CallOption) (*ConsumeUserScoreResp, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(ConsumeUserScoreResp)
	err := c.cc.Invoke(ctx, S_ConsumeUserScore_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *sClient) SearchUsers(ctx context.Context, in *SearchUsersReq, opts ...grpc.CallOption) (*SearchUsersResp, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(SearchUsersResp)
	err := c.cc.Invoke(ctx, S_SearchUsers_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *sClient) FollowUser(ctx context.Context, in *FollowUserReq, opts ...grpc.CallOption) (*FollowUserResp, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(FollowUserResp)
	err := c.cc.Invoke(ctx, S_FollowUser_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *sClient) UnfollowUser(ctx context.Context, in *UnfollowUserReq, opts ...grpc.CallOption) (*UnfollowUserResp, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(UnfollowUserResp)
	err := c.cc.Invoke(ctx, S_UnfollowUser_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *sClient) CheckFollowing(ctx context.Context, in *CheckFollowingReq, opts ...grpc.CallOption) (*CheckFollowingResp, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(CheckFollowingResp)
	err := c.cc.Invoke(ctx, S_CheckFollowing_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *sClient) GetFollowingUsers(ctx context.Context, in *GetFollowingUsersReq, opts ...grpc.CallOption) (*GetFollowingUsersResp, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(GetFollowingUsersResp)
	err := c.cc.Invoke(ctx, S_GetFollowingUsers_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *sClient) GetFollowers(ctx context.Context, in *GetFollowersReq, opts ...grpc.CallOption) (*GetFollowersResp, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(GetFollowersResp)
	err := c.cc.Invoke(ctx, S_GetFollowers_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *sClient) GetFollowingUserIDs(ctx context.Context, in *GetFollowingUserIDsReq, opts ...grpc.CallOption) (*GetFollowingUserIDsResp, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(GetFollowingUserIDsResp)
	err := c.cc.Invoke(ctx, S_GetFollowingUserIDs_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *sClient) BatchCheckFollowing(ctx context.Context, in *BatchCheckFollowingReq, opts ...grpc.CallOption) (*BatchCheckFollowingResp, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(BatchCheckFollowingResp)
	err := c.cc.Invoke(ctx, S_BatchCheckFollowing_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *sClient) ReviewCallback(ctx context.Context, in *ReviewCallbackReq, opts ...grpc.CallOption) (*common.SvcCommonResp, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(common.SvcCommonResp)
	err := c.cc.Invoke(ctx, S_ReviewCallback_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *sClient) IsPhoneWhitelist(ctx context.Context, in *IsPhoneWhitelistReq, opts ...grpc.CallOption) (*IsPhoneWhitelistResp, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(IsPhoneWhitelistResp)
	err := c.cc.Invoke(ctx, S_IsPhoneWhitelist_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *sClient) GetLoginStatus(ctx context.Context, in *GetLoginStatusReq, opts ...grpc.CallOption) (*GetLoginStatusResp, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(GetLoginStatusResp)
	err := c.cc.Invoke(ctx, S_GetLoginStatus_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *sClient) KickOffDevice(ctx context.Context, in *KickOffDeviceReq, opts ...grpc.CallOption) (*KickOffDeviceResp, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(KickOffDeviceResp)
	err := c.cc.Invoke(ctx, S_KickOffDevice_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *sClient) GetDeviceHistory(ctx context.Context, in *GetDeviceHistoryReq, opts ...grpc.CallOption) (*GetDeviceHistoryResp, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(GetDeviceHistoryResp)
	err := c.cc.Invoke(ctx, S_GetDeviceHistory_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *sClient) VerifyToken(ctx context.Context, in *VerifyTokenReq, opts ...grpc.CallOption) (*VerifyTokenResp, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(VerifyTokenResp)
	err := c.cc.Invoke(ctx, S_VerifyToken_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *sClient) ResetUserInfo(ctx context.Context, in *ResetUserInfoReq, opts ...grpc.CallOption) (*ResetUserInfoResp, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(ResetUserInfoResp)
	err := c.cc.Invoke(ctx, S_ResetUserInfo_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *sClient) BanUser(ctx context.Context, in *BanUserReq, opts ...grpc.CallOption) (*BanUserResp, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(BanUserResp)
	err := c.cc.Invoke(ctx, S_BanUser_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *sClient) UnbanUser(ctx context.Context, in *UnbanUserReq, opts ...grpc.CallOption) (*UnbanUserResp, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(UnbanUserResp)
	err := c.cc.Invoke(ctx, S_UnbanUser_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *sClient) AdminUpdateUserInfo(ctx context.Context, in *AdminUpdateUserInfoReq, opts ...grpc.CallOption) (*AdminUpdateUserInfoResp, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(AdminUpdateUserInfoResp)
	err := c.cc.Invoke(ctx, S_AdminUpdateUserInfo_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *sClient) AdminCreateUser(ctx context.Context, in *AdminCreateUserReq, opts ...grpc.CallOption) (*AdminCreateUserResp, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(AdminCreateUserResp)
	err := c.cc.Invoke(ctx, S_AdminCreateUser_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *sClient) ReviewNotify(ctx context.Context, in *ReviewNotifyReq, opts ...grpc.CallOption) (*common.SvcCommonResp, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(common.SvcCommonResp)
	err := c.cc.Invoke(ctx, S_ReviewNotify_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// SServer is the server API for S service.
// All implementations should embed UnimplementedSServer
// for forward compatibility.
type SServer interface {
	// 获取验证码
	GetVerificationCode(context.Context, *GetVerificationCodeReq) (*GetVerificationCodeResp, error)
	// 注册
	AccountSignUp(context.Context, *AccountSignUpReq) (*AccountSignUpResp, error)
	// 登录
	AccountSignIn(context.Context, *AccountSignInReq) (*AccountSignInResp, error)
	// 退出登录
	AccountSignOut(context.Context, *AccountSignOutReq) (*AccountSignOutResp, error)
	// 刷新token
	RefreshToken(context.Context, *RefreshTokenReq) (*RefreshTokenResp, error)
	// 注销账号
	AccountDelete(context.Context, *AccountDeleteReq) (*AccountDeleteResp, error)
	// 获取用户信息
	GetUserInfo(context.Context, *GetUserInfoReq) (*GetUserInfoResp, error)
	// 根据admin用户ID获取关联的业务用户信息
	GetUserInfoByCreationUserId(context.Context, *GetUserInfoByCreationUserIdReq) (*GetUserInfoByCreationUserIdResp, error)
	// 批量获取用户信息
	BatchGetUserInfo(context.Context, *BatchGetUserInfoReq) (*BatchGetUserInfoResp, error)
	// 更新用户信息
	UpdateUserInfo(context.Context, *UpdateUserInfoReq) (*UpdateUserInfoResp, error)
	// 设置用户为优质创作者
	SetPremiumCreator(context.Context, *SetPremiumCreatorReq) (*SetPremiumCreatorResp, error)
	// 状态上报
	OnlineLimiter(context.Context, *AccountOnlineReq) (*AccountOnlineResp, error)
	// 用户是否拉黑
	IsUserBlacklist(context.Context, *IsUserBlacklistReq) (*IsUserBlacklistResp, error)
	// 获取用户积分
	GetUserScore(context.Context, *GetUserScoreReq) (*GetUserScoreResp, error)
	// 增加用户积分
	AddUserScore(context.Context, *AddUserScoreReq) (*AddUserScoreResp, error)
	// 消耗用户积分
	ConsumeUserScore(context.Context, *ConsumeUserScoreReq) (*ConsumeUserScoreResp, error)
	// 搜索用户
	SearchUsers(context.Context, *SearchUsersReq) (*SearchUsersResp, error)
	// 关注用户
	FollowUser(context.Context, *FollowUserReq) (*FollowUserResp, error)
	// 取消关注用户
	UnfollowUser(context.Context, *UnfollowUserReq) (*UnfollowUserResp, error)
	// 检查是否关注
	CheckFollowing(context.Context, *CheckFollowingReq) (*CheckFollowingResp, error)
	// 获取关注的用户列表
	GetFollowingUsers(context.Context, *GetFollowingUsersReq) (*GetFollowingUsersResp, error)
	// 获取粉丝列表
	GetFollowers(context.Context, *GetFollowersReq) (*GetFollowersResp, error)
	// 获取关注用户ID列表（精简版，只返回用户ID）
	GetFollowingUserIDs(context.Context, *GetFollowingUserIDsReq) (*GetFollowingUserIDsResp, error)
	// 批量检查是否关注
	BatchCheckFollowing(context.Context, *BatchCheckFollowingReq) (*BatchCheckFollowingResp, error)
	// 用户审核callback （语音签名）
	ReviewCallback(context.Context, *ReviewCallbackReq) (*common.SvcCommonResp, error)
	// 检查手机号是否在白名单
	IsPhoneWhitelist(context.Context, *IsPhoneWhitelistReq) (*IsPhoneWhitelistResp, error)
	// 获取登录状态
	GetLoginStatus(context.Context, *GetLoginStatusReq) (*GetLoginStatusResp, error)
	// 踢下线设备
	KickOffDevice(context.Context, *KickOffDeviceReq) (*KickOffDeviceResp, error)
	// 获取设备登录历史
	GetDeviceHistory(context.Context, *GetDeviceHistoryReq) (*GetDeviceHistoryResp, error)
	// 验证Token（包括黑名单检查）
	VerifyToken(context.Context, *VerifyTokenReq) (*VerifyTokenResp, error)
	// 重置用户信息
	ResetUserInfo(context.Context, *ResetUserInfoReq) (*ResetUserInfoResp, error)
	// 封禁用户
	BanUser(context.Context, *BanUserReq) (*BanUserResp, error)
	// 解封用户
	UnbanUser(context.Context, *UnbanUserReq) (*UnbanUserResp, error)
	// Admin审核更新用户信息（不触发审核逻辑）
	AdminUpdateUserInfo(context.Context, *AdminUpdateUserInfoReq) (*AdminUpdateUserInfoResp, error)
	// Admin创建用户
	AdminCreateUser(context.Context, *AdminCreateUserReq) (*AdminCreateUserResp, error)
	// 审核通知
	ReviewNotify(context.Context, *ReviewNotifyReq) (*common.SvcCommonResp, error)
}

// UnimplementedSServer should be embedded to have
// forward compatible implementations.
//
// NOTE: this should be embedded by value instead of pointer to avoid a nil
// pointer dereference when methods are called.
type UnimplementedSServer struct{}

func (UnimplementedSServer) GetVerificationCode(context.Context, *GetVerificationCodeReq) (*GetVerificationCodeResp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetVerificationCode not implemented")
}
func (UnimplementedSServer) AccountSignUp(context.Context, *AccountSignUpReq) (*AccountSignUpResp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method AccountSignUp not implemented")
}
func (UnimplementedSServer) AccountSignIn(context.Context, *AccountSignInReq) (*AccountSignInResp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method AccountSignIn not implemented")
}
func (UnimplementedSServer) AccountSignOut(context.Context, *AccountSignOutReq) (*AccountSignOutResp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method AccountSignOut not implemented")
}
func (UnimplementedSServer) RefreshToken(context.Context, *RefreshTokenReq) (*RefreshTokenResp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method RefreshToken not implemented")
}
func (UnimplementedSServer) AccountDelete(context.Context, *AccountDeleteReq) (*AccountDeleteResp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method AccountDelete not implemented")
}
func (UnimplementedSServer) GetUserInfo(context.Context, *GetUserInfoReq) (*GetUserInfoResp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetUserInfo not implemented")
}
func (UnimplementedSServer) GetUserInfoByCreationUserId(context.Context, *GetUserInfoByCreationUserIdReq) (*GetUserInfoByCreationUserIdResp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetUserInfoByCreationUserId not implemented")
}
func (UnimplementedSServer) BatchGetUserInfo(context.Context, *BatchGetUserInfoReq) (*BatchGetUserInfoResp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method BatchGetUserInfo not implemented")
}
func (UnimplementedSServer) UpdateUserInfo(context.Context, *UpdateUserInfoReq) (*UpdateUserInfoResp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method UpdateUserInfo not implemented")
}
func (UnimplementedSServer) SetPremiumCreator(context.Context, *SetPremiumCreatorReq) (*SetPremiumCreatorResp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method SetPremiumCreator not implemented")
}
func (UnimplementedSServer) OnlineLimiter(context.Context, *AccountOnlineReq) (*AccountOnlineResp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method OnlineLimiter not implemented")
}
func (UnimplementedSServer) IsUserBlacklist(context.Context, *IsUserBlacklistReq) (*IsUserBlacklistResp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method IsUserBlacklist not implemented")
}
func (UnimplementedSServer) GetUserScore(context.Context, *GetUserScoreReq) (*GetUserScoreResp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetUserScore not implemented")
}
func (UnimplementedSServer) AddUserScore(context.Context, *AddUserScoreReq) (*AddUserScoreResp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method AddUserScore not implemented")
}
func (UnimplementedSServer) ConsumeUserScore(context.Context, *ConsumeUserScoreReq) (*ConsumeUserScoreResp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ConsumeUserScore not implemented")
}
func (UnimplementedSServer) SearchUsers(context.Context, *SearchUsersReq) (*SearchUsersResp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method SearchUsers not implemented")
}
func (UnimplementedSServer) FollowUser(context.Context, *FollowUserReq) (*FollowUserResp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method FollowUser not implemented")
}
func (UnimplementedSServer) UnfollowUser(context.Context, *UnfollowUserReq) (*UnfollowUserResp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method UnfollowUser not implemented")
}
func (UnimplementedSServer) CheckFollowing(context.Context, *CheckFollowingReq) (*CheckFollowingResp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method CheckFollowing not implemented")
}
func (UnimplementedSServer) GetFollowingUsers(context.Context, *GetFollowingUsersReq) (*GetFollowingUsersResp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetFollowingUsers not implemented")
}
func (UnimplementedSServer) GetFollowers(context.Context, *GetFollowersReq) (*GetFollowersResp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetFollowers not implemented")
}
func (UnimplementedSServer) GetFollowingUserIDs(context.Context, *GetFollowingUserIDsReq) (*GetFollowingUserIDsResp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetFollowingUserIDs not implemented")
}
func (UnimplementedSServer) BatchCheckFollowing(context.Context, *BatchCheckFollowingReq) (*BatchCheckFollowingResp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method BatchCheckFollowing not implemented")
}
func (UnimplementedSServer) ReviewCallback(context.Context, *ReviewCallbackReq) (*common.SvcCommonResp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ReviewCallback not implemented")
}
func (UnimplementedSServer) IsPhoneWhitelist(context.Context, *IsPhoneWhitelistReq) (*IsPhoneWhitelistResp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method IsPhoneWhitelist not implemented")
}
func (UnimplementedSServer) GetLoginStatus(context.Context, *GetLoginStatusReq) (*GetLoginStatusResp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetLoginStatus not implemented")
}
func (UnimplementedSServer) KickOffDevice(context.Context, *KickOffDeviceReq) (*KickOffDeviceResp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method KickOffDevice not implemented")
}
func (UnimplementedSServer) GetDeviceHistory(context.Context, *GetDeviceHistoryReq) (*GetDeviceHistoryResp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetDeviceHistory not implemented")
}
func (UnimplementedSServer) VerifyToken(context.Context, *VerifyTokenReq) (*VerifyTokenResp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method VerifyToken not implemented")
}
func (UnimplementedSServer) ResetUserInfo(context.Context, *ResetUserInfoReq) (*ResetUserInfoResp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ResetUserInfo not implemented")
}
func (UnimplementedSServer) BanUser(context.Context, *BanUserReq) (*BanUserResp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method BanUser not implemented")
}
func (UnimplementedSServer) UnbanUser(context.Context, *UnbanUserReq) (*UnbanUserResp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method UnbanUser not implemented")
}
func (UnimplementedSServer) AdminUpdateUserInfo(context.Context, *AdminUpdateUserInfoReq) (*AdminUpdateUserInfoResp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method AdminUpdateUserInfo not implemented")
}
func (UnimplementedSServer) AdminCreateUser(context.Context, *AdminCreateUserReq) (*AdminCreateUserResp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method AdminCreateUser not implemented")
}
func (UnimplementedSServer) ReviewNotify(context.Context, *ReviewNotifyReq) (*common.SvcCommonResp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ReviewNotify not implemented")
}
func (UnimplementedSServer) testEmbeddedByValue() {}

// UnsafeSServer may be embedded to opt out of forward compatibility for this service.
// Use of this interface is not recommended, as added methods to SServer will
// result in compilation errors.
type UnsafeSServer interface {
	mustEmbedUnimplementedSServer()
}

func RegisterSServer(s grpc.ServiceRegistrar, srv SServer) {
	// If the following call pancis, it indicates UnimplementedSServer was
	// embedded by pointer and is nil.  This will cause panics if an
	// unimplemented method is ever invoked, so we test this at initialization
	// time to prevent it from happening at runtime later due to I/O.
	if t, ok := srv.(interface{ testEmbeddedByValue() }); ok {
		t.testEmbeddedByValue()
	}
	s.RegisterService(&S_ServiceDesc, srv)
}

func _S_GetVerificationCode_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetVerificationCodeReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(SServer).GetVerificationCode(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: S_GetVerificationCode_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(SServer).GetVerificationCode(ctx, req.(*GetVerificationCodeReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _S_AccountSignUp_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(AccountSignUpReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(SServer).AccountSignUp(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: S_AccountSignUp_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(SServer).AccountSignUp(ctx, req.(*AccountSignUpReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _S_AccountSignIn_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(AccountSignInReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(SServer).AccountSignIn(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: S_AccountSignIn_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(SServer).AccountSignIn(ctx, req.(*AccountSignInReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _S_AccountSignOut_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(AccountSignOutReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(SServer).AccountSignOut(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: S_AccountSignOut_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(SServer).AccountSignOut(ctx, req.(*AccountSignOutReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _S_RefreshToken_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(RefreshTokenReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(SServer).RefreshToken(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: S_RefreshToken_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(SServer).RefreshToken(ctx, req.(*RefreshTokenReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _S_AccountDelete_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(AccountDeleteReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(SServer).AccountDelete(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: S_AccountDelete_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(SServer).AccountDelete(ctx, req.(*AccountDeleteReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _S_GetUserInfo_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetUserInfoReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(SServer).GetUserInfo(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: S_GetUserInfo_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(SServer).GetUserInfo(ctx, req.(*GetUserInfoReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _S_GetUserInfoByCreationUserId_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetUserInfoByCreationUserIdReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(SServer).GetUserInfoByCreationUserId(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: S_GetUserInfoByCreationUserId_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(SServer).GetUserInfoByCreationUserId(ctx, req.(*GetUserInfoByCreationUserIdReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _S_BatchGetUserInfo_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(BatchGetUserInfoReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(SServer).BatchGetUserInfo(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: S_BatchGetUserInfo_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(SServer).BatchGetUserInfo(ctx, req.(*BatchGetUserInfoReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _S_UpdateUserInfo_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(UpdateUserInfoReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(SServer).UpdateUserInfo(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: S_UpdateUserInfo_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(SServer).UpdateUserInfo(ctx, req.(*UpdateUserInfoReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _S_SetPremiumCreator_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(SetPremiumCreatorReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(SServer).SetPremiumCreator(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: S_SetPremiumCreator_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(SServer).SetPremiumCreator(ctx, req.(*SetPremiumCreatorReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _S_OnlineLimiter_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(AccountOnlineReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(SServer).OnlineLimiter(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: S_OnlineLimiter_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(SServer).OnlineLimiter(ctx, req.(*AccountOnlineReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _S_IsUserBlacklist_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(IsUserBlacklistReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(SServer).IsUserBlacklist(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: S_IsUserBlacklist_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(SServer).IsUserBlacklist(ctx, req.(*IsUserBlacklistReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _S_GetUserScore_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetUserScoreReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(SServer).GetUserScore(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: S_GetUserScore_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(SServer).GetUserScore(ctx, req.(*GetUserScoreReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _S_AddUserScore_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(AddUserScoreReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(SServer).AddUserScore(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: S_AddUserScore_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(SServer).AddUserScore(ctx, req.(*AddUserScoreReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _S_ConsumeUserScore_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ConsumeUserScoreReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(SServer).ConsumeUserScore(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: S_ConsumeUserScore_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(SServer).ConsumeUserScore(ctx, req.(*ConsumeUserScoreReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _S_SearchUsers_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(SearchUsersReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(SServer).SearchUsers(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: S_SearchUsers_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(SServer).SearchUsers(ctx, req.(*SearchUsersReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _S_FollowUser_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(FollowUserReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(SServer).FollowUser(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: S_FollowUser_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(SServer).FollowUser(ctx, req.(*FollowUserReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _S_UnfollowUser_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(UnfollowUserReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(SServer).UnfollowUser(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: S_UnfollowUser_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(SServer).UnfollowUser(ctx, req.(*UnfollowUserReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _S_CheckFollowing_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(CheckFollowingReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(SServer).CheckFollowing(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: S_CheckFollowing_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(SServer).CheckFollowing(ctx, req.(*CheckFollowingReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _S_GetFollowingUsers_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetFollowingUsersReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(SServer).GetFollowingUsers(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: S_GetFollowingUsers_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(SServer).GetFollowingUsers(ctx, req.(*GetFollowingUsersReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _S_GetFollowers_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetFollowersReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(SServer).GetFollowers(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: S_GetFollowers_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(SServer).GetFollowers(ctx, req.(*GetFollowersReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _S_GetFollowingUserIDs_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetFollowingUserIDsReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(SServer).GetFollowingUserIDs(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: S_GetFollowingUserIDs_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(SServer).GetFollowingUserIDs(ctx, req.(*GetFollowingUserIDsReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _S_BatchCheckFollowing_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(BatchCheckFollowingReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(SServer).BatchCheckFollowing(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: S_BatchCheckFollowing_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(SServer).BatchCheckFollowing(ctx, req.(*BatchCheckFollowingReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _S_ReviewCallback_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ReviewCallbackReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(SServer).ReviewCallback(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: S_ReviewCallback_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(SServer).ReviewCallback(ctx, req.(*ReviewCallbackReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _S_IsPhoneWhitelist_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(IsPhoneWhitelistReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(SServer).IsPhoneWhitelist(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: S_IsPhoneWhitelist_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(SServer).IsPhoneWhitelist(ctx, req.(*IsPhoneWhitelistReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _S_GetLoginStatus_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetLoginStatusReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(SServer).GetLoginStatus(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: S_GetLoginStatus_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(SServer).GetLoginStatus(ctx, req.(*GetLoginStatusReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _S_KickOffDevice_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(KickOffDeviceReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(SServer).KickOffDevice(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: S_KickOffDevice_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(SServer).KickOffDevice(ctx, req.(*KickOffDeviceReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _S_GetDeviceHistory_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetDeviceHistoryReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(SServer).GetDeviceHistory(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: S_GetDeviceHistory_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(SServer).GetDeviceHistory(ctx, req.(*GetDeviceHistoryReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _S_VerifyToken_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(VerifyTokenReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(SServer).VerifyToken(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: S_VerifyToken_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(SServer).VerifyToken(ctx, req.(*VerifyTokenReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _S_ResetUserInfo_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ResetUserInfoReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(SServer).ResetUserInfo(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: S_ResetUserInfo_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(SServer).ResetUserInfo(ctx, req.(*ResetUserInfoReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _S_BanUser_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(BanUserReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(SServer).BanUser(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: S_BanUser_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(SServer).BanUser(ctx, req.(*BanUserReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _S_UnbanUser_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(UnbanUserReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(SServer).UnbanUser(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: S_UnbanUser_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(SServer).UnbanUser(ctx, req.(*UnbanUserReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _S_AdminUpdateUserInfo_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(AdminUpdateUserInfoReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(SServer).AdminUpdateUserInfo(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: S_AdminUpdateUserInfo_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(SServer).AdminUpdateUserInfo(ctx, req.(*AdminUpdateUserInfoReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _S_AdminCreateUser_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(AdminCreateUserReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(SServer).AdminCreateUser(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: S_AdminCreateUser_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(SServer).AdminCreateUser(ctx, req.(*AdminCreateUserReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _S_ReviewNotify_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ReviewNotifyReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(SServer).ReviewNotify(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: S_ReviewNotify_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(SServer).ReviewNotify(ctx, req.(*ReviewNotifyReq))
	}
	return interceptor(ctx, in, info, handler)
}

// S_ServiceDesc is the grpc.ServiceDesc for S service.
// It's only intended for direct use with grpc.RegisterService,
// and not to be introspected or modified (even as a copy)
var S_ServiceDesc = grpc.ServiceDesc{
	ServiceName: "vc.svcaccount.s",
	HandlerType: (*SServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "GetVerificationCode",
			Handler:    _S_GetVerificationCode_Handler,
		},
		{
			MethodName: "AccountSignUp",
			Handler:    _S_AccountSignUp_Handler,
		},
		{
			MethodName: "AccountSignIn",
			Handler:    _S_AccountSignIn_Handler,
		},
		{
			MethodName: "AccountSignOut",
			Handler:    _S_AccountSignOut_Handler,
		},
		{
			MethodName: "RefreshToken",
			Handler:    _S_RefreshToken_Handler,
		},
		{
			MethodName: "AccountDelete",
			Handler:    _S_AccountDelete_Handler,
		},
		{
			MethodName: "GetUserInfo",
			Handler:    _S_GetUserInfo_Handler,
		},
		{
			MethodName: "GetUserInfoByCreationUserId",
			Handler:    _S_GetUserInfoByCreationUserId_Handler,
		},
		{
			MethodName: "BatchGetUserInfo",
			Handler:    _S_BatchGetUserInfo_Handler,
		},
		{
			MethodName: "UpdateUserInfo",
			Handler:    _S_UpdateUserInfo_Handler,
		},
		{
			MethodName: "SetPremiumCreator",
			Handler:    _S_SetPremiumCreator_Handler,
		},
		{
			MethodName: "OnlineLimiter",
			Handler:    _S_OnlineLimiter_Handler,
		},
		{
			MethodName: "IsUserBlacklist",
			Handler:    _S_IsUserBlacklist_Handler,
		},
		{
			MethodName: "GetUserScore",
			Handler:    _S_GetUserScore_Handler,
		},
		{
			MethodName: "AddUserScore",
			Handler:    _S_AddUserScore_Handler,
		},
		{
			MethodName: "ConsumeUserScore",
			Handler:    _S_ConsumeUserScore_Handler,
		},
		{
			MethodName: "SearchUsers",
			Handler:    _S_SearchUsers_Handler,
		},
		{
			MethodName: "FollowUser",
			Handler:    _S_FollowUser_Handler,
		},
		{
			MethodName: "UnfollowUser",
			Handler:    _S_UnfollowUser_Handler,
		},
		{
			MethodName: "CheckFollowing",
			Handler:    _S_CheckFollowing_Handler,
		},
		{
			MethodName: "GetFollowingUsers",
			Handler:    _S_GetFollowingUsers_Handler,
		},
		{
			MethodName: "GetFollowers",
			Handler:    _S_GetFollowers_Handler,
		},
		{
			MethodName: "GetFollowingUserIDs",
			Handler:    _S_GetFollowingUserIDs_Handler,
		},
		{
			MethodName: "BatchCheckFollowing",
			Handler:    _S_BatchCheckFollowing_Handler,
		},
		{
			MethodName: "ReviewCallback",
			Handler:    _S_ReviewCallback_Handler,
		},
		{
			MethodName: "IsPhoneWhitelist",
			Handler:    _S_IsPhoneWhitelist_Handler,
		},
		{
			MethodName: "GetLoginStatus",
			Handler:    _S_GetLoginStatus_Handler,
		},
		{
			MethodName: "KickOffDevice",
			Handler:    _S_KickOffDevice_Handler,
		},
		{
			MethodName: "GetDeviceHistory",
			Handler:    _S_GetDeviceHistory_Handler,
		},
		{
			MethodName: "VerifyToken",
			Handler:    _S_VerifyToken_Handler,
		},
		{
			MethodName: "ResetUserInfo",
			Handler:    _S_ResetUserInfo_Handler,
		},
		{
			MethodName: "BanUser",
			Handler:    _S_BanUser_Handler,
		},
		{
			MethodName: "UnbanUser",
			Handler:    _S_UnbanUser_Handler,
		},
		{
			MethodName: "AdminUpdateUserInfo",
			Handler:    _S_AdminUpdateUserInfo_Handler,
		},
		{
			MethodName: "AdminCreateUser",
			Handler:    _S_AdminCreateUser_Handler,
		},
		{
			MethodName: "ReviewNotify",
			Handler:    _S_ReviewNotify_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "svcaccount.proto",
}
