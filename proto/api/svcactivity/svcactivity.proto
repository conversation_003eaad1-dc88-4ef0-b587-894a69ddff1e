syntax = "proto3";

package vc.svcactivity;
option go_package = "new-gitlab.xunlei.cn/vcproject/backends/proto/api/svcactivity;svcactivity";

import "protoc-gen-validate/validate/validate.proto";
import "common/common.proto";

message DailyCheckInReq {
    int64 user_id = 1[(validate.rules).int64.gt = 0];
}

message DailyCheckInResp {
    common.SvcBaseResp base=1;
    DailyCheckInRespData data=2;
}

message DailyCheckInRespData {
    bool show_popup = 1;        // 是否显示弹窗
    int64 reward_score = 2;     // 签到奖励积分数
}

service s {
    // 每日签到（获取状态并签到）
    rpc DailyCheckIn(DailyCheckInReq) returns (DailyCheckInResp) {}
}