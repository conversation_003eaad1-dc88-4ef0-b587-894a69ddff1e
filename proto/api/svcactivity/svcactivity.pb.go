// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.33.0
// 	protoc        v5.29.3
// source: svcactivity.proto

package svcactivity

import (
	_ "github.com/envoyproxy/protoc-gen-validate/validate"
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	common "new-gitlab.xunlei.cn/vcproject/backends/proto/api/common"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type DailyCheckInReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	UserId int64 `protobuf:"varint,1,opt,name=user_id,json=userId,proto3" json:"user_id,omitempty"`
}

func (x *DailyCheckInReq) Reset() {
	*x = DailyCheckInReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_svcactivity_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *DailyCheckInReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DailyCheckInReq) ProtoMessage() {}

func (x *DailyCheckInReq) ProtoReflect() protoreflect.Message {
	mi := &file_svcactivity_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DailyCheckInReq.ProtoReflect.Descriptor instead.
func (*DailyCheckInReq) Descriptor() ([]byte, []int) {
	return file_svcactivity_proto_rawDescGZIP(), []int{0}
}

func (x *DailyCheckInReq) GetUserId() int64 {
	if x != nil {
		return x.UserId
	}
	return 0
}

type DailyCheckInResp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Base *common.SvcBaseResp   `protobuf:"bytes,1,opt,name=base,proto3" json:"base,omitempty"`
	Data *DailyCheckInRespData `protobuf:"bytes,2,opt,name=data,proto3" json:"data,omitempty"`
}

func (x *DailyCheckInResp) Reset() {
	*x = DailyCheckInResp{}
	if protoimpl.UnsafeEnabled {
		mi := &file_svcactivity_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *DailyCheckInResp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DailyCheckInResp) ProtoMessage() {}

func (x *DailyCheckInResp) ProtoReflect() protoreflect.Message {
	mi := &file_svcactivity_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DailyCheckInResp.ProtoReflect.Descriptor instead.
func (*DailyCheckInResp) Descriptor() ([]byte, []int) {
	return file_svcactivity_proto_rawDescGZIP(), []int{1}
}

func (x *DailyCheckInResp) GetBase() *common.SvcBaseResp {
	if x != nil {
		return x.Base
	}
	return nil
}

func (x *DailyCheckInResp) GetData() *DailyCheckInRespData {
	if x != nil {
		return x.Data
	}
	return nil
}

type DailyCheckInRespData struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	ShowPopup   bool  `protobuf:"varint,1,opt,name=show_popup,json=showPopup,proto3" json:"show_popup,omitempty"`       // 是否显示弹窗
	RewardScore int64 `protobuf:"varint,2,opt,name=reward_score,json=rewardScore,proto3" json:"reward_score,omitempty"` // 签到奖励积分数
}

func (x *DailyCheckInRespData) Reset() {
	*x = DailyCheckInRespData{}
	if protoimpl.UnsafeEnabled {
		mi := &file_svcactivity_proto_msgTypes[2]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *DailyCheckInRespData) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DailyCheckInRespData) ProtoMessage() {}

func (x *DailyCheckInRespData) ProtoReflect() protoreflect.Message {
	mi := &file_svcactivity_proto_msgTypes[2]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DailyCheckInRespData.ProtoReflect.Descriptor instead.
func (*DailyCheckInRespData) Descriptor() ([]byte, []int) {
	return file_svcactivity_proto_rawDescGZIP(), []int{2}
}

func (x *DailyCheckInRespData) GetShowPopup() bool {
	if x != nil {
		return x.ShowPopup
	}
	return false
}

func (x *DailyCheckInRespData) GetRewardScore() int64 {
	if x != nil {
		return x.RewardScore
	}
	return 0
}

var File_svcactivity_proto protoreflect.FileDescriptor

var file_svcactivity_proto_rawDesc = []byte{
	0x0a, 0x11, 0x73, 0x76, 0x63, 0x61, 0x63, 0x74, 0x69, 0x76, 0x69, 0x74, 0x79, 0x2e, 0x70, 0x72,
	0x6f, 0x74, 0x6f, 0x12, 0x0e, 0x76, 0x63, 0x2e, 0x73, 0x76, 0x63, 0x61, 0x63, 0x74, 0x69, 0x76,
	0x69, 0x74, 0x79, 0x1a, 0x2b, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x63, 0x2d, 0x67, 0x65, 0x6e, 0x2d,
	0x76, 0x61, 0x6c, 0x69, 0x64, 0x61, 0x74, 0x65, 0x2f, 0x76, 0x61, 0x6c, 0x69, 0x64, 0x61, 0x74,
	0x65, 0x2f, 0x76, 0x61, 0x6c, 0x69, 0x64, 0x61, 0x74, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f,
	0x1a, 0x13, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2f, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e,
	0x70, 0x72, 0x6f, 0x74, 0x6f, 0x22, 0x33, 0x0a, 0x0f, 0x44, 0x61, 0x69, 0x6c, 0x79, 0x43, 0x68,
	0x65, 0x63, 0x6b, 0x49, 0x6e, 0x52, 0x65, 0x71, 0x12, 0x20, 0x0a, 0x07, 0x75, 0x73, 0x65, 0x72,
	0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x42, 0x07, 0xfa, 0x42, 0x04, 0x22, 0x02,
	0x20, 0x00, 0x52, 0x06, 0x75, 0x73, 0x65, 0x72, 0x49, 0x64, 0x22, 0x75, 0x0a, 0x10, 0x44, 0x61,
	0x69, 0x6c, 0x79, 0x43, 0x68, 0x65, 0x63, 0x6b, 0x49, 0x6e, 0x52, 0x65, 0x73, 0x70, 0x12, 0x27,
	0x0a, 0x04, 0x62, 0x61, 0x73, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x13, 0x2e, 0x63,
	0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x53, 0x76, 0x63, 0x42, 0x61, 0x73, 0x65, 0x52, 0x65, 0x73,
	0x70, 0x52, 0x04, 0x62, 0x61, 0x73, 0x65, 0x12, 0x38, 0x0a, 0x04, 0x64, 0x61, 0x74, 0x61, 0x18,
	0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x24, 0x2e, 0x76, 0x63, 0x2e, 0x73, 0x76, 0x63, 0x61, 0x63,
	0x74, 0x69, 0x76, 0x69, 0x74, 0x79, 0x2e, 0x44, 0x61, 0x69, 0x6c, 0x79, 0x43, 0x68, 0x65, 0x63,
	0x6b, 0x49, 0x6e, 0x52, 0x65, 0x73, 0x70, 0x44, 0x61, 0x74, 0x61, 0x52, 0x04, 0x64, 0x61, 0x74,
	0x61, 0x22, 0x58, 0x0a, 0x14, 0x44, 0x61, 0x69, 0x6c, 0x79, 0x43, 0x68, 0x65, 0x63, 0x6b, 0x49,
	0x6e, 0x52, 0x65, 0x73, 0x70, 0x44, 0x61, 0x74, 0x61, 0x12, 0x1d, 0x0a, 0x0a, 0x73, 0x68, 0x6f,
	0x77, 0x5f, 0x70, 0x6f, 0x70, 0x75, 0x70, 0x18, 0x01, 0x20, 0x01, 0x28, 0x08, 0x52, 0x09, 0x73,
	0x68, 0x6f, 0x77, 0x50, 0x6f, 0x70, 0x75, 0x70, 0x12, 0x21, 0x0a, 0x0c, 0x72, 0x65, 0x77, 0x61,
	0x72, 0x64, 0x5f, 0x73, 0x63, 0x6f, 0x72, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x03, 0x52, 0x0b,
	0x72, 0x65, 0x77, 0x61, 0x72, 0x64, 0x53, 0x63, 0x6f, 0x72, 0x65, 0x32, 0x58, 0x0a, 0x01, 0x73,
	0x12, 0x53, 0x0a, 0x0c, 0x44, 0x61, 0x69, 0x6c, 0x79, 0x43, 0x68, 0x65, 0x63, 0x6b, 0x49, 0x6e,
	0x12, 0x1f, 0x2e, 0x76, 0x63, 0x2e, 0x73, 0x76, 0x63, 0x61, 0x63, 0x74, 0x69, 0x76, 0x69, 0x74,
	0x79, 0x2e, 0x44, 0x61, 0x69, 0x6c, 0x79, 0x43, 0x68, 0x65, 0x63, 0x6b, 0x49, 0x6e, 0x52, 0x65,
	0x71, 0x1a, 0x20, 0x2e, 0x76, 0x63, 0x2e, 0x73, 0x76, 0x63, 0x61, 0x63, 0x74, 0x69, 0x76, 0x69,
	0x74, 0x79, 0x2e, 0x44, 0x61, 0x69, 0x6c, 0x79, 0x43, 0x68, 0x65, 0x63, 0x6b, 0x49, 0x6e, 0x52,
	0x65, 0x73, 0x70, 0x22, 0x00, 0x42, 0x4b, 0x5a, 0x49, 0x6e, 0x65, 0x77, 0x2d, 0x67, 0x69, 0x74,
	0x6c, 0x61, 0x62, 0x2e, 0x78, 0x75, 0x6e, 0x6c, 0x65, 0x69, 0x2e, 0x63, 0x6e, 0x2f, 0x76, 0x63,
	0x70, 0x72, 0x6f, 0x6a, 0x65, 0x63, 0x74, 0x2f, 0x62, 0x61, 0x63, 0x6b, 0x65, 0x6e, 0x64, 0x73,
	0x2f, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2f, 0x61, 0x70, 0x69, 0x2f, 0x73, 0x76, 0x63, 0x61, 0x63,
	0x74, 0x69, 0x76, 0x69, 0x74, 0x79, 0x3b, 0x73, 0x76, 0x63, 0x61, 0x63, 0x74, 0x69, 0x76, 0x69,
	0x74, 0x79, 0x62, 0x06, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_svcactivity_proto_rawDescOnce sync.Once
	file_svcactivity_proto_rawDescData = file_svcactivity_proto_rawDesc
)

func file_svcactivity_proto_rawDescGZIP() []byte {
	file_svcactivity_proto_rawDescOnce.Do(func() {
		file_svcactivity_proto_rawDescData = protoimpl.X.CompressGZIP(file_svcactivity_proto_rawDescData)
	})
	return file_svcactivity_proto_rawDescData
}

var file_svcactivity_proto_msgTypes = make([]protoimpl.MessageInfo, 3)
var file_svcactivity_proto_goTypes = []interface{}{
	(*DailyCheckInReq)(nil),      // 0: vc.svcactivity.DailyCheckInReq
	(*DailyCheckInResp)(nil),     // 1: vc.svcactivity.DailyCheckInResp
	(*DailyCheckInRespData)(nil), // 2: vc.svcactivity.DailyCheckInRespData
	(*common.SvcBaseResp)(nil),   // 3: common.SvcBaseResp
}
var file_svcactivity_proto_depIdxs = []int32{
	3, // 0: vc.svcactivity.DailyCheckInResp.base:type_name -> common.SvcBaseResp
	2, // 1: vc.svcactivity.DailyCheckInResp.data:type_name -> vc.svcactivity.DailyCheckInRespData
	0, // 2: vc.svcactivity.s.DailyCheckIn:input_type -> vc.svcactivity.DailyCheckInReq
	1, // 3: vc.svcactivity.s.DailyCheckIn:output_type -> vc.svcactivity.DailyCheckInResp
	3, // [3:4] is the sub-list for method output_type
	2, // [2:3] is the sub-list for method input_type
	2, // [2:2] is the sub-list for extension type_name
	2, // [2:2] is the sub-list for extension extendee
	0, // [0:2] is the sub-list for field type_name
}

func init() { file_svcactivity_proto_init() }
func file_svcactivity_proto_init() {
	if File_svcactivity_proto != nil {
		return
	}
	if !protoimpl.UnsafeEnabled {
		file_svcactivity_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*DailyCheckInReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_svcactivity_proto_msgTypes[1].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*DailyCheckInResp); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_svcactivity_proto_msgTypes[2].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*DailyCheckInRespData); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_svcactivity_proto_rawDesc,
			NumEnums:      0,
			NumMessages:   3,
			NumExtensions: 0,
			NumServices:   1,
		},
		GoTypes:           file_svcactivity_proto_goTypes,
		DependencyIndexes: file_svcactivity_proto_depIdxs,
		MessageInfos:      file_svcactivity_proto_msgTypes,
	}.Build()
	File_svcactivity_proto = out.File
	file_svcactivity_proto_rawDesc = nil
	file_svcactivity_proto_goTypes = nil
	file_svcactivity_proto_depIdxs = nil
}
