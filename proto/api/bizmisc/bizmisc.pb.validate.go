// Code generated by protoc-gen-validate. DO NOT EDIT.
// source: bizmisc.proto

package bizmisc

import (
	"bytes"
	"errors"
	"fmt"
	"net"
	"net/mail"
	"net/url"
	"regexp"
	"sort"
	"strings"
	"time"
	"unicode/utf8"

	"google.golang.org/protobuf/types/known/anypb"
)

// ensure the imports are used
var (
	_ = bytes.MinRead
	_ = errors.New("")
	_ = fmt.Print
	_ = utf8.UTFMax
	_ = (*regexp.Regexp)(nil)
	_ = (*strings.Reader)(nil)
	_ = net.IPv4len
	_ = time.Duration(0)
	_ = (*url.URL)(nil)
	_ = (*mail.Address)(nil)
	_ = anypb.Any{}
	_ = sort.Sort
)

// Validate checks the field values on AliObjectIdReq with the rules defined in
// the proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *AliObjectIdReq) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on AliObjectIdReq with the rules defined
// in the proto definition for this message. If any rules are violated, the
// result is a list of violation errors wrapped in AliObjectIdReqMultiError,
// or nil if none found.
func (m *AliObjectIdReq) ValidateAll() error {
	return m.validate(true)
}

func (m *AliObjectIdReq) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if _, ok := _AliObjectIdReq_Type_InLookup[m.GetType()]; !ok {
		err := AliObjectIdReqValidationError{
			field:  "Type",
			reason: "value must be in list [image audio video data]",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if len(m.GetBizType()) < 1 {
		err := AliObjectIdReqValidationError{
			field:  "BizType",
			reason: "value length must be at least 1 bytes",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if len(errors) > 0 {
		return AliObjectIdReqMultiError(errors)
	}

	return nil
}

// AliObjectIdReqMultiError is an error wrapping multiple validation errors
// returned by AliObjectIdReq.ValidateAll() if the designated constraints
// aren't met.
type AliObjectIdReqMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m AliObjectIdReqMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m AliObjectIdReqMultiError) AllErrors() []error { return m }

// AliObjectIdReqValidationError is the validation error returned by
// AliObjectIdReq.Validate if the designated constraints aren't met.
type AliObjectIdReqValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e AliObjectIdReqValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e AliObjectIdReqValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e AliObjectIdReqValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e AliObjectIdReqValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e AliObjectIdReqValidationError) ErrorName() string { return "AliObjectIdReqValidationError" }

// Error satisfies the builtin error interface
func (e AliObjectIdReqValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sAliObjectIdReq.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = AliObjectIdReqValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = AliObjectIdReqValidationError{}

var _AliObjectIdReq_Type_InLookup = map[string]struct{}{
	"image": {},
	"audio": {},
	"video": {},
	"data":  {},
}

// Validate checks the field values on AliObjectIdResp with the rules defined
// in the proto definition for this message. If any rules are violated, the
// first error encountered is returned, or nil if there are no violations.
func (m *AliObjectIdResp) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on AliObjectIdResp with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// AliObjectIdRespMultiError, or nil if none found.
func (m *AliObjectIdResp) ValidateAll() error {
	return m.validate(true)
}

func (m *AliObjectIdResp) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Code

	// no validation rules for Msg

	if all {
		switch v := interface{}(m.GetData()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, AliObjectIdRespValidationError{
					field:  "Data",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, AliObjectIdRespValidationError{
					field:  "Data",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetData()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return AliObjectIdRespValidationError{
				field:  "Data",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return AliObjectIdRespMultiError(errors)
	}

	return nil
}

// AliObjectIdRespMultiError is an error wrapping multiple validation errors
// returned by AliObjectIdResp.ValidateAll() if the designated constraints
// aren't met.
type AliObjectIdRespMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m AliObjectIdRespMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m AliObjectIdRespMultiError) AllErrors() []error { return m }

// AliObjectIdRespValidationError is the validation error returned by
// AliObjectIdResp.Validate if the designated constraints aren't met.
type AliObjectIdRespValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e AliObjectIdRespValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e AliObjectIdRespValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e AliObjectIdRespValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e AliObjectIdRespValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e AliObjectIdRespValidationError) ErrorName() string { return "AliObjectIdRespValidationError" }

// Error satisfies the builtin error interface
func (e AliObjectIdRespValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sAliObjectIdResp.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = AliObjectIdRespValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = AliObjectIdRespValidationError{}

// Validate checks the field values on AliObjectIdRespData with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *AliObjectIdRespData) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on AliObjectIdRespData with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// AliObjectIdRespDataMultiError, or nil if none found.
func (m *AliObjectIdRespData) ValidateAll() error {
	return m.validate(true)
}

func (m *AliObjectIdRespData) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Endpoint

	// no validation rules for Bucket

	// no validation rules for ObjectKey

	// no validation rules for ObjectId

	if len(errors) > 0 {
		return AliObjectIdRespDataMultiError(errors)
	}

	return nil
}

// AliObjectIdRespDataMultiError is an error wrapping multiple validation
// errors returned by AliObjectIdRespData.ValidateAll() if the designated
// constraints aren't met.
type AliObjectIdRespDataMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m AliObjectIdRespDataMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m AliObjectIdRespDataMultiError) AllErrors() []error { return m }

// AliObjectIdRespDataValidationError is the validation error returned by
// AliObjectIdRespData.Validate if the designated constraints aren't met.
type AliObjectIdRespDataValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e AliObjectIdRespDataValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e AliObjectIdRespDataValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e AliObjectIdRespDataValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e AliObjectIdRespDataValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e AliObjectIdRespDataValidationError) ErrorName() string {
	return "AliObjectIdRespDataValidationError"
}

// Error satisfies the builtin error interface
func (e AliObjectIdRespDataValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sAliObjectIdRespData.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = AliObjectIdRespDataValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = AliObjectIdRespDataValidationError{}

// Validate checks the field values on AliOssSigatureReq with the rules defined
// in the proto definition for this message. If any rules are violated, the
// first error encountered is returned, or nil if there are no violations.
func (m *AliOssSigatureReq) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on AliOssSigatureReq with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// AliOssSigatureReqMultiError, or nil if none found.
func (m *AliOssSigatureReq) ValidateAll() error {
	return m.validate(true)
}

func (m *AliOssSigatureReq) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if _, ok := _AliOssSigatureReq_Type_InLookup[m.GetType()]; !ok {
		err := AliOssSigatureReqValidationError{
			field:  "Type",
			reason: "value must be in list [image audio video data]",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if len(m.GetContent()) < 1 {
		err := AliOssSigatureReqValidationError{
			field:  "Content",
			reason: "value length must be at least 1 bytes",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if len(errors) > 0 {
		return AliOssSigatureReqMultiError(errors)
	}

	return nil
}

// AliOssSigatureReqMultiError is an error wrapping multiple validation errors
// returned by AliOssSigatureReq.ValidateAll() if the designated constraints
// aren't met.
type AliOssSigatureReqMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m AliOssSigatureReqMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m AliOssSigatureReqMultiError) AllErrors() []error { return m }

// AliOssSigatureReqValidationError is the validation error returned by
// AliOssSigatureReq.Validate if the designated constraints aren't met.
type AliOssSigatureReqValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e AliOssSigatureReqValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e AliOssSigatureReqValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e AliOssSigatureReqValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e AliOssSigatureReqValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e AliOssSigatureReqValidationError) ErrorName() string {
	return "AliOssSigatureReqValidationError"
}

// Error satisfies the builtin error interface
func (e AliOssSigatureReqValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sAliOssSigatureReq.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = AliOssSigatureReqValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = AliOssSigatureReqValidationError{}

var _AliOssSigatureReq_Type_InLookup = map[string]struct{}{
	"image": {},
	"audio": {},
	"video": {},
	"data":  {},
}

// Validate checks the field values on AliOssSigatureResp with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *AliOssSigatureResp) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on AliOssSigatureResp with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// AliOssSigatureRespMultiError, or nil if none found.
func (m *AliOssSigatureResp) ValidateAll() error {
	return m.validate(true)
}

func (m *AliOssSigatureResp) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Code

	// no validation rules for Msg

	if all {
		switch v := interface{}(m.GetData()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, AliOssSigatureRespValidationError{
					field:  "Data",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, AliOssSigatureRespValidationError{
					field:  "Data",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetData()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return AliOssSigatureRespValidationError{
				field:  "Data",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return AliOssSigatureRespMultiError(errors)
	}

	return nil
}

// AliOssSigatureRespMultiError is an error wrapping multiple validation errors
// returned by AliOssSigatureResp.ValidateAll() if the designated constraints
// aren't met.
type AliOssSigatureRespMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m AliOssSigatureRespMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m AliOssSigatureRespMultiError) AllErrors() []error { return m }

// AliOssSigatureRespValidationError is the validation error returned by
// AliOssSigatureResp.Validate if the designated constraints aren't met.
type AliOssSigatureRespValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e AliOssSigatureRespValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e AliOssSigatureRespValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e AliOssSigatureRespValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e AliOssSigatureRespValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e AliOssSigatureRespValidationError) ErrorName() string {
	return "AliOssSigatureRespValidationError"
}

// Error satisfies the builtin error interface
func (e AliOssSigatureRespValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sAliOssSigatureResp.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = AliOssSigatureRespValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = AliOssSigatureRespValidationError{}

// Validate checks the field values on AliOssSigatureRespData with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *AliOssSigatureRespData) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on AliOssSigatureRespData with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// AliOssSigatureRespDataMultiError, or nil if none found.
func (m *AliOssSigatureRespData) ValidateAll() error {
	return m.validate(true)
}

func (m *AliOssSigatureRespData) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Signature

	if len(errors) > 0 {
		return AliOssSigatureRespDataMultiError(errors)
	}

	return nil
}

// AliOssSigatureRespDataMultiError is an error wrapping multiple validation
// errors returned by AliOssSigatureRespData.ValidateAll() if the designated
// constraints aren't met.
type AliOssSigatureRespDataMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m AliOssSigatureRespDataMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m AliOssSigatureRespDataMultiError) AllErrors() []error { return m }

// AliOssSigatureRespDataValidationError is the validation error returned by
// AliOssSigatureRespData.Validate if the designated constraints aren't met.
type AliOssSigatureRespDataValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e AliOssSigatureRespDataValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e AliOssSigatureRespDataValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e AliOssSigatureRespDataValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e AliOssSigatureRespDataValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e AliOssSigatureRespDataValidationError) ErrorName() string {
	return "AliOssSigatureRespDataValidationError"
}

// Error satisfies the builtin error interface
func (e AliOssSigatureRespDataValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sAliOssSigatureRespData.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = AliOssSigatureRespDataValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = AliOssSigatureRespDataValidationError{}

// Validate checks the field values on GetChanTokenReq with the rules defined
// in the proto definition for this message. If any rules are violated, the
// first error encountered is returned, or nil if there are no violations.
func (m *GetChanTokenReq) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GetChanTokenReq with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// GetChanTokenReqMultiError, or nil if none found.
func (m *GetChanTokenReq) ValidateAll() error {
	return m.validate(true)
}

func (m *GetChanTokenReq) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if len(errors) > 0 {
		return GetChanTokenReqMultiError(errors)
	}

	return nil
}

// GetChanTokenReqMultiError is an error wrapping multiple validation errors
// returned by GetChanTokenReq.ValidateAll() if the designated constraints
// aren't met.
type GetChanTokenReqMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetChanTokenReqMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetChanTokenReqMultiError) AllErrors() []error { return m }

// GetChanTokenReqValidationError is the validation error returned by
// GetChanTokenReq.Validate if the designated constraints aren't met.
type GetChanTokenReqValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetChanTokenReqValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetChanTokenReqValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetChanTokenReqValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetChanTokenReqValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetChanTokenReqValidationError) ErrorName() string { return "GetChanTokenReqValidationError" }

// Error satisfies the builtin error interface
func (e GetChanTokenReqValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetChanTokenReq.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetChanTokenReqValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetChanTokenReqValidationError{}

// Validate checks the field values on GetChanTokenResp with the rules defined
// in the proto definition for this message. If any rules are violated, the
// first error encountered is returned, or nil if there are no violations.
func (m *GetChanTokenResp) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GetChanTokenResp with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// GetChanTokenRespMultiError, or nil if none found.
func (m *GetChanTokenResp) ValidateAll() error {
	return m.validate(true)
}

func (m *GetChanTokenResp) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Code

	// no validation rules for Msg

	if all {
		switch v := interface{}(m.GetData()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetChanTokenRespValidationError{
					field:  "Data",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetChanTokenRespValidationError{
					field:  "Data",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetData()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetChanTokenRespValidationError{
				field:  "Data",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return GetChanTokenRespMultiError(errors)
	}

	return nil
}

// GetChanTokenRespMultiError is an error wrapping multiple validation errors
// returned by GetChanTokenResp.ValidateAll() if the designated constraints
// aren't met.
type GetChanTokenRespMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetChanTokenRespMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetChanTokenRespMultiError) AllErrors() []error { return m }

// GetChanTokenRespValidationError is the validation error returned by
// GetChanTokenResp.Validate if the designated constraints aren't met.
type GetChanTokenRespValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetChanTokenRespValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetChanTokenRespValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetChanTokenRespValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetChanTokenRespValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetChanTokenRespValidationError) ErrorName() string { return "GetChanTokenRespValidationError" }

// Error satisfies the builtin error interface
func (e GetChanTokenRespValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetChanTokenResp.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetChanTokenRespValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetChanTokenRespValidationError{}

// Validate checks the field values on GetChanTokenRespData with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *GetChanTokenRespData) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GetChanTokenRespData with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// GetChanTokenRespDataMultiError, or nil if none found.
func (m *GetChanTokenRespData) ValidateAll() error {
	return m.validate(true)
}

func (m *GetChanTokenRespData) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for WsUrl

	// no validation rules for GeneralTopic

	// no validation rules for Gzip

	if len(errors) > 0 {
		return GetChanTokenRespDataMultiError(errors)
	}

	return nil
}

// GetChanTokenRespDataMultiError is an error wrapping multiple validation
// errors returned by GetChanTokenRespData.ValidateAll() if the designated
// constraints aren't met.
type GetChanTokenRespDataMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetChanTokenRespDataMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetChanTokenRespDataMultiError) AllErrors() []error { return m }

// GetChanTokenRespDataValidationError is the validation error returned by
// GetChanTokenRespData.Validate if the designated constraints aren't met.
type GetChanTokenRespDataValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetChanTokenRespDataValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetChanTokenRespDataValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetChanTokenRespDataValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetChanTokenRespDataValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetChanTokenRespDataValidationError) ErrorName() string {
	return "GetChanTokenRespDataValidationError"
}

// Error satisfies the builtin error interface
func (e GetChanTokenRespDataValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetChanTokenRespData.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetChanTokenRespDataValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetChanTokenRespDataValidationError{}

// Validate checks the field values on RvcInfo with the rules defined in the
// proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *RvcInfo) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on RvcInfo with the rules defined in the
// proto definition for this message. If any rules are violated, the result is
// a list of violation errors wrapped in RvcInfoMultiError, or nil if none found.
func (m *RvcInfo) ValidateAll() error {
	return m.validate(true)
}

func (m *RvcInfo) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for RvcName

	// no validation rules for SeedName

	// no validation rules for ReferenceAudioUrl

	// no validation rules for RequestId

	// no validation rules for Mid

	// no validation rules for CascadedUseRvc

	// no validation rules for F0Method

	// no validation rules for RmsMixRate

	// no validation rules for Protect

	// no validation rules for DiffusionSteps

	// no validation rules for LengthAdjust

	// no validation rules for InferenceCfgRate

	// no validation rules for SampleRate

	// no validation rules for BitDepth

	// no validation rules for Channels

	// no validation rules for OutputSampleRate

	// no validation rules for OutputBitDepth

	// no validation rules for OutputChannels

	// no validation rules for AudioMilliDuration

	// no validation rules for ChunkMs

	if len(errors) > 0 {
		return RvcInfoMultiError(errors)
	}

	return nil
}

// RvcInfoMultiError is an error wrapping multiple validation errors returned
// by RvcInfo.ValidateAll() if the designated constraints aren't met.
type RvcInfoMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m RvcInfoMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m RvcInfoMultiError) AllErrors() []error { return m }

// RvcInfoValidationError is the validation error returned by RvcInfo.Validate
// if the designated constraints aren't met.
type RvcInfoValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e RvcInfoValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e RvcInfoValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e RvcInfoValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e RvcInfoValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e RvcInfoValidationError) ErrorName() string { return "RvcInfoValidationError" }

// Error satisfies the builtin error interface
func (e RvcInfoValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sRvcInfo.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = RvcInfoValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = RvcInfoValidationError{}

// Validate checks the field values on GetAsrAddrReq with the rules defined in
// the proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *GetAsrAddrReq) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GetAsrAddrReq with the rules defined
// in the proto definition for this message. If any rules are violated, the
// result is a list of violation errors wrapped in GetAsrAddrReqMultiError, or
// nil if none found.
func (m *GetAsrAddrReq) ValidateAll() error {
	return m.validate(true)
}

func (m *GetAsrAddrReq) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if len(errors) > 0 {
		return GetAsrAddrReqMultiError(errors)
	}

	return nil
}

// GetAsrAddrReqMultiError is an error wrapping multiple validation errors
// returned by GetAsrAddrReq.ValidateAll() if the designated constraints
// aren't met.
type GetAsrAddrReqMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetAsrAddrReqMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetAsrAddrReqMultiError) AllErrors() []error { return m }

// GetAsrAddrReqValidationError is the validation error returned by
// GetAsrAddrReq.Validate if the designated constraints aren't met.
type GetAsrAddrReqValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetAsrAddrReqValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetAsrAddrReqValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetAsrAddrReqValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetAsrAddrReqValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetAsrAddrReqValidationError) ErrorName() string { return "GetAsrAddrReqValidationError" }

// Error satisfies the builtin error interface
func (e GetAsrAddrReqValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetAsrAddrReq.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetAsrAddrReqValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetAsrAddrReqValidationError{}

// Validate checks the field values on GetAsrAddrResp with the rules defined in
// the proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *GetAsrAddrResp) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GetAsrAddrResp with the rules defined
// in the proto definition for this message. If any rules are violated, the
// result is a list of violation errors wrapped in GetAsrAddrRespMultiError,
// or nil if none found.
func (m *GetAsrAddrResp) ValidateAll() error {
	return m.validate(true)
}

func (m *GetAsrAddrResp) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Code

	// no validation rules for Msg

	if all {
		switch v := interface{}(m.GetData()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetAsrAddrRespValidationError{
					field:  "Data",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetAsrAddrRespValidationError{
					field:  "Data",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetData()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetAsrAddrRespValidationError{
				field:  "Data",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return GetAsrAddrRespMultiError(errors)
	}

	return nil
}

// GetAsrAddrRespMultiError is an error wrapping multiple validation errors
// returned by GetAsrAddrResp.ValidateAll() if the designated constraints
// aren't met.
type GetAsrAddrRespMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetAsrAddrRespMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetAsrAddrRespMultiError) AllErrors() []error { return m }

// GetAsrAddrRespValidationError is the validation error returned by
// GetAsrAddrResp.Validate if the designated constraints aren't met.
type GetAsrAddrRespValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetAsrAddrRespValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetAsrAddrRespValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetAsrAddrRespValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetAsrAddrRespValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetAsrAddrRespValidationError) ErrorName() string { return "GetAsrAddrRespValidationError" }

// Error satisfies the builtin error interface
func (e GetAsrAddrRespValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetAsrAddrResp.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetAsrAddrRespValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetAsrAddrRespValidationError{}

// Validate checks the field values on GetAsrAddrRespData with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *GetAsrAddrRespData) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GetAsrAddrRespData with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// GetAsrAddrRespDataMultiError, or nil if none found.
func (m *GetAsrAddrRespData) ValidateAll() error {
	return m.validate(true)
}

func (m *GetAsrAddrRespData) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Addr

	if len(errors) > 0 {
		return GetAsrAddrRespDataMultiError(errors)
	}

	return nil
}

// GetAsrAddrRespDataMultiError is an error wrapping multiple validation errors
// returned by GetAsrAddrRespData.ValidateAll() if the designated constraints
// aren't met.
type GetAsrAddrRespDataMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetAsrAddrRespDataMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetAsrAddrRespDataMultiError) AllErrors() []error { return m }

// GetAsrAddrRespDataValidationError is the validation error returned by
// GetAsrAddrRespData.Validate if the designated constraints aren't met.
type GetAsrAddrRespDataValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetAsrAddrRespDataValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetAsrAddrRespDataValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetAsrAddrRespDataValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetAsrAddrRespDataValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetAsrAddrRespDataValidationError) ErrorName() string {
	return "GetAsrAddrRespDataValidationError"
}

// Error satisfies the builtin error interface
func (e GetAsrAddrRespDataValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetAsrAddrRespData.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetAsrAddrRespDataValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetAsrAddrRespDataValidationError{}

// Validate checks the field values on GetVcPushAddrReq with the rules defined
// in the proto definition for this message. If any rules are violated, the
// first error encountered is returned, or nil if there are no violations.
func (m *GetVcPushAddrReq) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GetVcPushAddrReq with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// GetVcPushAddrReqMultiError, or nil if none found.
func (m *GetVcPushAddrReq) ValidateAll() error {
	return m.validate(true)
}

func (m *GetVcPushAddrReq) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if m.GetCharacterId() <= 0 {
		err := GetVcPushAddrReqValidationError{
			field:  "CharacterId",
			reason: "value must be greater than 0",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if m.GetCharacterAssetsId() <= 0 {
		err := GetVcPushAddrReqValidationError{
			field:  "CharacterAssetsId",
			reason: "value must be greater than 0",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if _, ok := _GetVcPushAddrReq_Scene_InLookup[m.GetScene()]; !ok {
		err := GetVcPushAddrReqValidationError{
			field:  "Scene",
			reason: "value must be in list [comment dubbing signature]",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if len(errors) > 0 {
		return GetVcPushAddrReqMultiError(errors)
	}

	return nil
}

// GetVcPushAddrReqMultiError is an error wrapping multiple validation errors
// returned by GetVcPushAddrReq.ValidateAll() if the designated constraints
// aren't met.
type GetVcPushAddrReqMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetVcPushAddrReqMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetVcPushAddrReqMultiError) AllErrors() []error { return m }

// GetVcPushAddrReqValidationError is the validation error returned by
// GetVcPushAddrReq.Validate if the designated constraints aren't met.
type GetVcPushAddrReqValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetVcPushAddrReqValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetVcPushAddrReqValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetVcPushAddrReqValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetVcPushAddrReqValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetVcPushAddrReqValidationError) ErrorName() string { return "GetVcPushAddrReqValidationError" }

// Error satisfies the builtin error interface
func (e GetVcPushAddrReqValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetVcPushAddrReq.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetVcPushAddrReqValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetVcPushAddrReqValidationError{}

var _GetVcPushAddrReq_Scene_InLookup = map[string]struct{}{
	"comment":   {},
	"dubbing":   {},
	"signature": {},
}

// Validate checks the field values on GetVcPushAddrResp with the rules defined
// in the proto definition for this message. If any rules are violated, the
// first error encountered is returned, or nil if there are no violations.
func (m *GetVcPushAddrResp) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GetVcPushAddrResp with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// GetVcPushAddrRespMultiError, or nil if none found.
func (m *GetVcPushAddrResp) ValidateAll() error {
	return m.validate(true)
}

func (m *GetVcPushAddrResp) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Code

	// no validation rules for Msg

	if all {
		switch v := interface{}(m.GetData()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetVcPushAddrRespValidationError{
					field:  "Data",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetVcPushAddrRespValidationError{
					field:  "Data",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetData()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetVcPushAddrRespValidationError{
				field:  "Data",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return GetVcPushAddrRespMultiError(errors)
	}

	return nil
}

// GetVcPushAddrRespMultiError is an error wrapping multiple validation errors
// returned by GetVcPushAddrResp.ValidateAll() if the designated constraints
// aren't met.
type GetVcPushAddrRespMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetVcPushAddrRespMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetVcPushAddrRespMultiError) AllErrors() []error { return m }

// GetVcPushAddrRespValidationError is the validation error returned by
// GetVcPushAddrResp.Validate if the designated constraints aren't met.
type GetVcPushAddrRespValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetVcPushAddrRespValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetVcPushAddrRespValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetVcPushAddrRespValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetVcPushAddrRespValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetVcPushAddrRespValidationError) ErrorName() string {
	return "GetVcPushAddrRespValidationError"
}

// Error satisfies the builtin error interface
func (e GetVcPushAddrRespValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetVcPushAddrResp.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetVcPushAddrRespValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetVcPushAddrRespValidationError{}

// Validate checks the field values on GetVcPushAddrRespData with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *GetVcPushAddrRespData) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GetVcPushAddrRespData with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// GetVcPushAddrRespDataMultiError, or nil if none found.
func (m *GetVcPushAddrRespData) ValidateAll() error {
	return m.validate(true)
}

func (m *GetVcPushAddrRespData) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for WsAddr

	if all {
		switch v := interface{}(m.GetRvc()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetVcPushAddrRespDataValidationError{
					field:  "Rvc",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetVcPushAddrRespDataValidationError{
					field:  "Rvc",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetRvc()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetVcPushAddrRespDataValidationError{
				field:  "Rvc",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return GetVcPushAddrRespDataMultiError(errors)
	}

	return nil
}

// GetVcPushAddrRespDataMultiError is an error wrapping multiple validation
// errors returned by GetVcPushAddrRespData.ValidateAll() if the designated
// constraints aren't met.
type GetVcPushAddrRespDataMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetVcPushAddrRespDataMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetVcPushAddrRespDataMultiError) AllErrors() []error { return m }

// GetVcPushAddrRespDataValidationError is the validation error returned by
// GetVcPushAddrRespData.Validate if the designated constraints aren't met.
type GetVcPushAddrRespDataValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetVcPushAddrRespDataValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetVcPushAddrRespDataValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetVcPushAddrRespDataValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetVcPushAddrRespDataValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetVcPushAddrRespDataValidationError) ErrorName() string {
	return "GetVcPushAddrRespDataValidationError"
}

// Error satisfies the builtin error interface
func (e GetVcPushAddrRespDataValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetVcPushAddrRespData.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetVcPushAddrRespDataValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetVcPushAddrRespDataValidationError{}

// Validate checks the field values on ScriptInfo with the rules defined in the
// proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *ScriptInfo) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on ScriptInfo with the rules defined in
// the proto definition for this message. If any rules are violated, the
// result is a list of violation errors wrapped in ScriptInfoMultiError, or
// nil if none found.
func (m *ScriptInfo) ValidateAll() error {
	return m.validate(true)
}

func (m *ScriptInfo) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for ScriptId

	// no validation rules for Title

	// no validation rules for Cover

	if len(errors) > 0 {
		return ScriptInfoMultiError(errors)
	}

	return nil
}

// ScriptInfoMultiError is an error wrapping multiple validation errors
// returned by ScriptInfo.ValidateAll() if the designated constraints aren't met.
type ScriptInfoMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m ScriptInfoMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m ScriptInfoMultiError) AllErrors() []error { return m }

// ScriptInfoValidationError is the validation error returned by
// ScriptInfo.Validate if the designated constraints aren't met.
type ScriptInfoValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e ScriptInfoValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e ScriptInfoValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e ScriptInfoValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e ScriptInfoValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e ScriptInfoValidationError) ErrorName() string { return "ScriptInfoValidationError" }

// Error satisfies the builtin error interface
func (e ScriptInfoValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sScriptInfo.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = ScriptInfoValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = ScriptInfoValidationError{}

// Validate checks the field values on ScriptDetail with the rules defined in
// the proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *ScriptDetail) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on ScriptDetail with the rules defined
// in the proto definition for this message. If any rules are violated, the
// result is a list of violation errors wrapped in ScriptDetailMultiError, or
// nil if none found.
func (m *ScriptDetail) ValidateAll() error {
	return m.validate(true)
}

func (m *ScriptDetail) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if len(errors) > 0 {
		return ScriptDetailMultiError(errors)
	}

	return nil
}

// ScriptDetailMultiError is an error wrapping multiple validation errors
// returned by ScriptDetail.ValidateAll() if the designated constraints aren't met.
type ScriptDetailMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m ScriptDetailMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m ScriptDetailMultiError) AllErrors() []error { return m }

// ScriptDetailValidationError is the validation error returned by
// ScriptDetail.Validate if the designated constraints aren't met.
type ScriptDetailValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e ScriptDetailValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e ScriptDetailValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e ScriptDetailValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e ScriptDetailValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e ScriptDetailValidationError) ErrorName() string { return "ScriptDetailValidationError" }

// Error satisfies the builtin error interface
func (e ScriptDetailValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sScriptDetail.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = ScriptDetailValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = ScriptDetailValidationError{}

// Validate checks the field values on Comment with the rules defined in the
// proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *Comment) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on Comment with the rules defined in the
// proto definition for this message. If any rules are violated, the result is
// a list of violation errors wrapped in CommentMultiError, or nil if none found.
func (m *Comment) ValidateAll() error {
	return m.validate(true)
}

func (m *Comment) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for CommentId

	// no validation rules for Text

	// no validation rules for AudioUrl

	// no validation rules for Duration

	if len(errors) > 0 {
		return CommentMultiError(errors)
	}

	return nil
}

// CommentMultiError is an error wrapping multiple validation errors returned
// by Comment.ValidateAll() if the designated constraints aren't met.
type CommentMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m CommentMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m CommentMultiError) AllErrors() []error { return m }

// CommentValidationError is the validation error returned by Comment.Validate
// if the designated constraints aren't met.
type CommentValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e CommentValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e CommentValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e CommentValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e CommentValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e CommentValidationError) ErrorName() string { return "CommentValidationError" }

// Error satisfies the builtin error interface
func (e CommentValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sComment.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = CommentValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = CommentValidationError{}

// Validate checks the field values on CommentReply with the rules defined in
// the proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *CommentReply) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on CommentReply with the rules defined
// in the proto definition for this message. If any rules are violated, the
// result is a list of violation errors wrapped in CommentReplyMultiError, or
// nil if none found.
func (m *CommentReply) ValidateAll() error {
	return m.validate(true)
}

func (m *CommentReply) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for CommentId

	// no validation rules for Text

	// no validation rules for AudioUrl

	// no validation rules for Duration

	if len(errors) > 0 {
		return CommentReplyMultiError(errors)
	}

	return nil
}

// CommentReplyMultiError is an error wrapping multiple validation errors
// returned by CommentReply.ValidateAll() if the designated constraints aren't met.
type CommentReplyMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m CommentReplyMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m CommentReplyMultiError) AllErrors() []error { return m }

// CommentReplyValidationError is the validation error returned by
// CommentReply.Validate if the designated constraints aren't met.
type CommentReplyValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e CommentReplyValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e CommentReplyValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e CommentReplyValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e CommentReplyValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e CommentReplyValidationError) ErrorName() string { return "CommentReplyValidationError" }

// Error satisfies the builtin error interface
func (e CommentReplyValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sCommentReply.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = CommentReplyValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = CommentReplyValidationError{}

// Validate checks the field values on Dubbing with the rules defined in the
// proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *Dubbing) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on Dubbing with the rules defined in the
// proto definition for this message. If any rules are violated, the result is
// a list of violation errors wrapped in DubbingMultiError, or nil if none found.
func (m *Dubbing) ValidateAll() error {
	return m.validate(true)
}

func (m *Dubbing) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for DubbingId

	// no validation rules for AudioUrl

	// no validation rules for Duration

	if len(errors) > 0 {
		return DubbingMultiError(errors)
	}

	return nil
}

// DubbingMultiError is an error wrapping multiple validation errors returned
// by Dubbing.ValidateAll() if the designated constraints aren't met.
type DubbingMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m DubbingMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m DubbingMultiError) AllErrors() []error { return m }

// DubbingValidationError is the validation error returned by Dubbing.Validate
// if the designated constraints aren't met.
type DubbingValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e DubbingValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e DubbingValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e DubbingValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e DubbingValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e DubbingValidationError) ErrorName() string { return "DubbingValidationError" }

// Error satisfies the builtin error interface
func (e DubbingValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sDubbing.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = DubbingValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = DubbingValidationError{}

// Validate checks the field values on SendSecretaryMsgReq with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *SendSecretaryMsgReq) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on SendSecretaryMsgReq with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// SendSecretaryMsgReqMultiError, or nil if none found.
func (m *SendSecretaryMsgReq) ValidateAll() error {
	return m.validate(true)
}

func (m *SendSecretaryMsgReq) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if m.GetToUid() <= 0 {
		err := SendSecretaryMsgReqValidationError{
			field:  "ToUid",
			reason: "value must be greater than 0",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if m.GetContentType() <= 0 {
		err := SendSecretaryMsgReqValidationError{
			field:  "ContentType",
			reason: "value must be greater than 0",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	// no validation rules for ContentImage

	// no validation rules for Tpl

	{
		sorted_keys := make([]string, len(m.GetTplData()))
		i := 0
		for key := range m.GetTplData() {
			sorted_keys[i] = key
			i++
		}
		sort.Slice(sorted_keys, func(i, j int) bool { return sorted_keys[i] < sorted_keys[j] })
		for _, key := range sorted_keys {
			val := m.GetTplData()[key]
			_ = val

			// no validation rules for TplData[key]

			if all {
				switch v := interface{}(val).(type) {
				case interface{ ValidateAll() error }:
					if err := v.ValidateAll(); err != nil {
						errors = append(errors, SendSecretaryMsgReqValidationError{
							field:  fmt.Sprintf("TplData[%v]", key),
							reason: "embedded message failed validation",
							cause:  err,
						})
					}
				case interface{ Validate() error }:
					if err := v.Validate(); err != nil {
						errors = append(errors, SendSecretaryMsgReqValidationError{
							field:  fmt.Sprintf("TplData[%v]", key),
							reason: "embedded message failed validation",
							cause:  err,
						})
					}
				}
			} else if v, ok := interface{}(val).(interface{ Validate() error }); ok {
				if err := v.Validate(); err != nil {
					return SendSecretaryMsgReqValidationError{
						field:  fmt.Sprintf("TplData[%v]", key),
						reason: "embedded message failed validation",
						cause:  err,
					}
				}
			}

		}
	}

	if all {
		switch v := interface{}(m.GetMember()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, SendSecretaryMsgReqValidationError{
					field:  "Member",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, SendSecretaryMsgReqValidationError{
					field:  "Member",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetMember()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return SendSecretaryMsgReqValidationError{
				field:  "Member",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetScript()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, SendSecretaryMsgReqValidationError{
					field:  "Script",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, SendSecretaryMsgReqValidationError{
					field:  "Script",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetScript()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return SendSecretaryMsgReqValidationError{
				field:  "Script",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetComment()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, SendSecretaryMsgReqValidationError{
					field:  "Comment",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, SendSecretaryMsgReqValidationError{
					field:  "Comment",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetComment()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return SendSecretaryMsgReqValidationError{
				field:  "Comment",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetCommentReply()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, SendSecretaryMsgReqValidationError{
					field:  "CommentReply",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, SendSecretaryMsgReqValidationError{
					field:  "CommentReply",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetCommentReply()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return SendSecretaryMsgReqValidationError{
				field:  "CommentReply",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetDubbing()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, SendSecretaryMsgReqValidationError{
					field:  "Dubbing",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, SendSecretaryMsgReqValidationError{
					field:  "Dubbing",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetDubbing()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return SendSecretaryMsgReqValidationError{
				field:  "Dubbing",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return SendSecretaryMsgReqMultiError(errors)
	}

	return nil
}

// SendSecretaryMsgReqMultiError is an error wrapping multiple validation
// errors returned by SendSecretaryMsgReq.ValidateAll() if the designated
// constraints aren't met.
type SendSecretaryMsgReqMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m SendSecretaryMsgReqMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m SendSecretaryMsgReqMultiError) AllErrors() []error { return m }

// SendSecretaryMsgReqValidationError is the validation error returned by
// SendSecretaryMsgReq.Validate if the designated constraints aren't met.
type SendSecretaryMsgReqValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e SendSecretaryMsgReqValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e SendSecretaryMsgReqValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e SendSecretaryMsgReqValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e SendSecretaryMsgReqValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e SendSecretaryMsgReqValidationError) ErrorName() string {
	return "SendSecretaryMsgReqValidationError"
}

// Error satisfies the builtin error interface
func (e SendSecretaryMsgReqValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sSendSecretaryMsgReq.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = SendSecretaryMsgReqValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = SendSecretaryMsgReqValidationError{}

// Validate checks the field values on SendSecretaryMsgResp with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *SendSecretaryMsgResp) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on SendSecretaryMsgResp with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// SendSecretaryMsgRespMultiError, or nil if none found.
func (m *SendSecretaryMsgResp) ValidateAll() error {
	return m.validate(true)
}

func (m *SendSecretaryMsgResp) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Code

	// no validation rules for Msg

	if len(errors) > 0 {
		return SendSecretaryMsgRespMultiError(errors)
	}

	return nil
}

// SendSecretaryMsgRespMultiError is an error wrapping multiple validation
// errors returned by SendSecretaryMsgResp.ValidateAll() if the designated
// constraints aren't met.
type SendSecretaryMsgRespMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m SendSecretaryMsgRespMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m SendSecretaryMsgRespMultiError) AllErrors() []error { return m }

// SendSecretaryMsgRespValidationError is the validation error returned by
// SendSecretaryMsgResp.Validate if the designated constraints aren't met.
type SendSecretaryMsgRespValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e SendSecretaryMsgRespValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e SendSecretaryMsgRespValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e SendSecretaryMsgRespValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e SendSecretaryMsgRespValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e SendSecretaryMsgRespValidationError) ErrorName() string {
	return "SendSecretaryMsgRespValidationError"
}

// Error satisfies the builtin error interface
func (e SendSecretaryMsgRespValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sSendSecretaryMsgResp.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = SendSecretaryMsgRespValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = SendSecretaryMsgRespValidationError{}

// Validate checks the field values on TestSendSystemMsgReq with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *TestSendSystemMsgReq) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on TestSendSystemMsgReq with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// TestSendSystemMsgReqMultiError, or nil if none found.
func (m *TestSendSystemMsgReq) ValidateAll() error {
	return m.validate(true)
}

func (m *TestSendSystemMsgReq) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if m.GetToUid() <= 0 {
		err := TestSendSystemMsgReqValidationError{
			field:  "ToUid",
			reason: "value must be greater than 0",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if m.GetContentType() <= 0 {
		err := TestSendSystemMsgReqValidationError{
			field:  "ContentType",
			reason: "value must be greater than 0",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if all {
		switch v := interface{}(m.GetSystemMsg()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, TestSendSystemMsgReqValidationError{
					field:  "SystemMsg",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, TestSendSystemMsgReqValidationError{
					field:  "SystemMsg",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetSystemMsg()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return TestSendSystemMsgReqValidationError{
				field:  "SystemMsg",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetFollowMsg()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, TestSendSystemMsgReqValidationError{
					field:  "FollowMsg",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, TestSendSystemMsgReqValidationError{
					field:  "FollowMsg",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetFollowMsg()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return TestSendSystemMsgReqValidationError{
				field:  "FollowMsg",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetCommentMsg()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, TestSendSystemMsgReqValidationError{
					field:  "CommentMsg",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, TestSendSystemMsgReqValidationError{
					field:  "CommentMsg",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetCommentMsg()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return TestSendSystemMsgReqValidationError{
				field:  "CommentMsg",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetLikeMsg()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, TestSendSystemMsgReqValidationError{
					field:  "LikeMsg",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, TestSendSystemMsgReqValidationError{
					field:  "LikeMsg",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetLikeMsg()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return TestSendSystemMsgReqValidationError{
				field:  "LikeMsg",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetDubbingMsg()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, TestSendSystemMsgReqValidationError{
					field:  "DubbingMsg",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, TestSendSystemMsgReqValidationError{
					field:  "DubbingMsg",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetDubbingMsg()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return TestSendSystemMsgReqValidationError{
				field:  "DubbingMsg",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetReviewMsg()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, TestSendSystemMsgReqValidationError{
					field:  "ReviewMsg",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, TestSendSystemMsgReqValidationError{
					field:  "ReviewMsg",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetReviewMsg()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return TestSendSystemMsgReqValidationError{
				field:  "ReviewMsg",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return TestSendSystemMsgReqMultiError(errors)
	}

	return nil
}

// TestSendSystemMsgReqMultiError is an error wrapping multiple validation
// errors returned by TestSendSystemMsgReq.ValidateAll() if the designated
// constraints aren't met.
type TestSendSystemMsgReqMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m TestSendSystemMsgReqMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m TestSendSystemMsgReqMultiError) AllErrors() []error { return m }

// TestSendSystemMsgReqValidationError is the validation error returned by
// TestSendSystemMsgReq.Validate if the designated constraints aren't met.
type TestSendSystemMsgReqValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e TestSendSystemMsgReqValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e TestSendSystemMsgReqValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e TestSendSystemMsgReqValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e TestSendSystemMsgReqValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e TestSendSystemMsgReqValidationError) ErrorName() string {
	return "TestSendSystemMsgReqValidationError"
}

// Error satisfies the builtin error interface
func (e TestSendSystemMsgReqValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sTestSendSystemMsgReq.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = TestSendSystemMsgReqValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = TestSendSystemMsgReqValidationError{}

// Validate checks the field values on TestSendSystemMsgResp with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *TestSendSystemMsgResp) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on TestSendSystemMsgResp with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// TestSendSystemMsgRespMultiError, or nil if none found.
func (m *TestSendSystemMsgResp) ValidateAll() error {
	return m.validate(true)
}

func (m *TestSendSystemMsgResp) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Code

	// no validation rules for Msg

	if len(errors) > 0 {
		return TestSendSystemMsgRespMultiError(errors)
	}

	return nil
}

// TestSendSystemMsgRespMultiError is an error wrapping multiple validation
// errors returned by TestSendSystemMsgResp.ValidateAll() if the designated
// constraints aren't met.
type TestSendSystemMsgRespMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m TestSendSystemMsgRespMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m TestSendSystemMsgRespMultiError) AllErrors() []error { return m }

// TestSendSystemMsgRespValidationError is the validation error returned by
// TestSendSystemMsgResp.Validate if the designated constraints aren't met.
type TestSendSystemMsgRespValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e TestSendSystemMsgRespValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e TestSendSystemMsgRespValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e TestSendSystemMsgRespValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e TestSendSystemMsgRespValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e TestSendSystemMsgRespValidationError) ErrorName() string {
	return "TestSendSystemMsgRespValidationError"
}

// Error satisfies the builtin error interface
func (e TestSendSystemMsgRespValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sTestSendSystemMsgResp.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = TestSendSystemMsgRespValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = TestSendSystemMsgRespValidationError{}

// Validate checks the field values on SystemMsg with the rules defined in the
// proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *SystemMsg) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on SystemMsg with the rules defined in
// the proto definition for this message. If any rules are violated, the
// result is a list of violation errors wrapped in SystemMsgMultiError, or nil
// if none found.
func (m *SystemMsg) ValidateAll() error {
	return m.validate(true)
}

func (m *SystemMsg) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetTitle()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, SystemMsgValidationError{
					field:  "Title",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, SystemMsgValidationError{
					field:  "Title",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetTitle()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return SystemMsgValidationError{
				field:  "Title",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for BgmUrl

	// no validation rules for JumpUrl

	if all {
		switch v := interface{}(m.GetContent()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, SystemMsgValidationError{
					field:  "Content",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, SystemMsgValidationError{
					field:  "Content",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetContent()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return SystemMsgValidationError{
				field:  "Content",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for Time

	if len(errors) > 0 {
		return SystemMsgMultiError(errors)
	}

	return nil
}

// SystemMsgMultiError is an error wrapping multiple validation errors returned
// by SystemMsg.ValidateAll() if the designated constraints aren't met.
type SystemMsgMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m SystemMsgMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m SystemMsgMultiError) AllErrors() []error { return m }

// SystemMsgValidationError is the validation error returned by
// SystemMsg.Validate if the designated constraints aren't met.
type SystemMsgValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e SystemMsgValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e SystemMsgValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e SystemMsgValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e SystemMsgValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e SystemMsgValidationError) ErrorName() string { return "SystemMsgValidationError" }

// Error satisfies the builtin error interface
func (e SystemMsgValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sSystemMsg.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = SystemMsgValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = SystemMsgValidationError{}

// Validate checks the field values on FollowMsg with the rules defined in the
// proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *FollowMsg) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on FollowMsg with the rules defined in
// the proto definition for this message. If any rules are violated, the
// result is a list of violation errors wrapped in FollowMsgMultiError, or nil
// if none found.
func (m *FollowMsg) ValidateAll() error {
	return m.validate(true)
}

func (m *FollowMsg) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetContent()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, FollowMsgValidationError{
					field:  "Content",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, FollowMsgValidationError{
					field:  "Content",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetContent()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return FollowMsgValidationError{
				field:  "Content",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetMember()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, FollowMsgValidationError{
					field:  "Member",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, FollowMsgValidationError{
					field:  "Member",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetMember()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return FollowMsgValidationError{
				field:  "Member",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for Time

	if len(errors) > 0 {
		return FollowMsgMultiError(errors)
	}

	return nil
}

// FollowMsgMultiError is an error wrapping multiple validation errors returned
// by FollowMsg.ValidateAll() if the designated constraints aren't met.
type FollowMsgMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m FollowMsgMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m FollowMsgMultiError) AllErrors() []error { return m }

// FollowMsgValidationError is the validation error returned by
// FollowMsg.Validate if the designated constraints aren't met.
type FollowMsgValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e FollowMsgValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e FollowMsgValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e FollowMsgValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e FollowMsgValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e FollowMsgValidationError) ErrorName() string { return "FollowMsgValidationError" }

// Error satisfies the builtin error interface
func (e FollowMsgValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sFollowMsg.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = FollowMsgValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = FollowMsgValidationError{}

// Validate checks the field values on CommentMsg with the rules defined in the
// proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *CommentMsg) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on CommentMsg with the rules defined in
// the proto definition for this message. If any rules are violated, the
// result is a list of violation errors wrapped in CommentMsgMultiError, or
// nil if none found.
func (m *CommentMsg) ValidateAll() error {
	return m.validate(true)
}

func (m *CommentMsg) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetContent()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, CommentMsgValidationError{
					field:  "Content",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, CommentMsgValidationError{
					field:  "Content",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetContent()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return CommentMsgValidationError{
				field:  "Content",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetMember()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, CommentMsgValidationError{
					field:  "Member",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, CommentMsgValidationError{
					field:  "Member",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetMember()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return CommentMsgValidationError{
				field:  "Member",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetComment()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, CommentMsgValidationError{
					field:  "Comment",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, CommentMsgValidationError{
					field:  "Comment",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetComment()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return CommentMsgValidationError{
				field:  "Comment",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetCommentReply()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, CommentMsgValidationError{
					field:  "CommentReply",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, CommentMsgValidationError{
					field:  "CommentReply",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetCommentReply()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return CommentMsgValidationError{
				field:  "CommentReply",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetScript()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, CommentMsgValidationError{
					field:  "Script",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, CommentMsgValidationError{
					field:  "Script",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetScript()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return CommentMsgValidationError{
				field:  "Script",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for Time

	if len(errors) > 0 {
		return CommentMsgMultiError(errors)
	}

	return nil
}

// CommentMsgMultiError is an error wrapping multiple validation errors
// returned by CommentMsg.ValidateAll() if the designated constraints aren't met.
type CommentMsgMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m CommentMsgMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m CommentMsgMultiError) AllErrors() []error { return m }

// CommentMsgValidationError is the validation error returned by
// CommentMsg.Validate if the designated constraints aren't met.
type CommentMsgValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e CommentMsgValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e CommentMsgValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e CommentMsgValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e CommentMsgValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e CommentMsgValidationError) ErrorName() string { return "CommentMsgValidationError" }

// Error satisfies the builtin error interface
func (e CommentMsgValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sCommentMsg.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = CommentMsgValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = CommentMsgValidationError{}

// Validate checks the field values on LikeMsg with the rules defined in the
// proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *LikeMsg) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on LikeMsg with the rules defined in the
// proto definition for this message. If any rules are violated, the result is
// a list of violation errors wrapped in LikeMsgMultiError, or nil if none found.
func (m *LikeMsg) ValidateAll() error {
	return m.validate(true)
}

func (m *LikeMsg) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetContent()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, LikeMsgValidationError{
					field:  "Content",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, LikeMsgValidationError{
					field:  "Content",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetContent()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return LikeMsgValidationError{
				field:  "Content",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetMember()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, LikeMsgValidationError{
					field:  "Member",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, LikeMsgValidationError{
					field:  "Member",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetMember()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return LikeMsgValidationError{
				field:  "Member",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetScript()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, LikeMsgValidationError{
					field:  "Script",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, LikeMsgValidationError{
					field:  "Script",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetScript()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return LikeMsgValidationError{
				field:  "Script",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetComment()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, LikeMsgValidationError{
					field:  "Comment",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, LikeMsgValidationError{
					field:  "Comment",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetComment()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return LikeMsgValidationError{
				field:  "Comment",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetDubbing()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, LikeMsgValidationError{
					field:  "Dubbing",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, LikeMsgValidationError{
					field:  "Dubbing",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetDubbing()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return LikeMsgValidationError{
				field:  "Dubbing",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for Time

	if len(errors) > 0 {
		return LikeMsgMultiError(errors)
	}

	return nil
}

// LikeMsgMultiError is an error wrapping multiple validation errors returned
// by LikeMsg.ValidateAll() if the designated constraints aren't met.
type LikeMsgMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m LikeMsgMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m LikeMsgMultiError) AllErrors() []error { return m }

// LikeMsgValidationError is the validation error returned by LikeMsg.Validate
// if the designated constraints aren't met.
type LikeMsgValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e LikeMsgValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e LikeMsgValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e LikeMsgValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e LikeMsgValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e LikeMsgValidationError) ErrorName() string { return "LikeMsgValidationError" }

// Error satisfies the builtin error interface
func (e LikeMsgValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sLikeMsg.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = LikeMsgValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = LikeMsgValidationError{}

// Validate checks the field values on DubbingMsg with the rules defined in the
// proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *DubbingMsg) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on DubbingMsg with the rules defined in
// the proto definition for this message. If any rules are violated, the
// result is a list of violation errors wrapped in DubbingMsgMultiError, or
// nil if none found.
func (m *DubbingMsg) ValidateAll() error {
	return m.validate(true)
}

func (m *DubbingMsg) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetContent()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, DubbingMsgValidationError{
					field:  "Content",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, DubbingMsgValidationError{
					field:  "Content",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetContent()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return DubbingMsgValidationError{
				field:  "Content",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetMember()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, DubbingMsgValidationError{
					field:  "Member",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, DubbingMsgValidationError{
					field:  "Member",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetMember()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return DubbingMsgValidationError{
				field:  "Member",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetScript()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, DubbingMsgValidationError{
					field:  "Script",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, DubbingMsgValidationError{
					field:  "Script",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetScript()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return DubbingMsgValidationError{
				field:  "Script",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetDubbing()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, DubbingMsgValidationError{
					field:  "Dubbing",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, DubbingMsgValidationError{
					field:  "Dubbing",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetDubbing()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return DubbingMsgValidationError{
				field:  "Dubbing",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for Time

	if len(errors) > 0 {
		return DubbingMsgMultiError(errors)
	}

	return nil
}

// DubbingMsgMultiError is an error wrapping multiple validation errors
// returned by DubbingMsg.ValidateAll() if the designated constraints aren't met.
type DubbingMsgMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m DubbingMsgMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m DubbingMsgMultiError) AllErrors() []error { return m }

// DubbingMsgValidationError is the validation error returned by
// DubbingMsg.Validate if the designated constraints aren't met.
type DubbingMsgValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e DubbingMsgValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e DubbingMsgValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e DubbingMsgValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e DubbingMsgValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e DubbingMsgValidationError) ErrorName() string { return "DubbingMsgValidationError" }

// Error satisfies the builtin error interface
func (e DubbingMsgValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sDubbingMsg.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = DubbingMsgValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = DubbingMsgValidationError{}

// Validate checks the field values on ReviewMsg with the rules defined in the
// proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *ReviewMsg) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on ReviewMsg with the rules defined in
// the proto definition for this message. If any rules are violated, the
// result is a list of violation errors wrapped in ReviewMsgMultiError, or nil
// if none found.
func (m *ReviewMsg) ValidateAll() error {
	return m.validate(true)
}

func (m *ReviewMsg) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetTitle()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, ReviewMsgValidationError{
					field:  "Title",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, ReviewMsgValidationError{
					field:  "Title",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetTitle()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return ReviewMsgValidationError{
				field:  "Title",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for BgmUrl

	// no validation rules for JumpUrl

	if all {
		switch v := interface{}(m.GetContent()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, ReviewMsgValidationError{
					field:  "Content",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, ReviewMsgValidationError{
					field:  "Content",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetContent()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return ReviewMsgValidationError{
				field:  "Content",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetScript()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, ReviewMsgValidationError{
					field:  "Script",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, ReviewMsgValidationError{
					field:  "Script",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetScript()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return ReviewMsgValidationError{
				field:  "Script",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return ReviewMsgMultiError(errors)
	}

	return nil
}

// ReviewMsgMultiError is an error wrapping multiple validation errors returned
// by ReviewMsg.ValidateAll() if the designated constraints aren't met.
type ReviewMsgMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m ReviewMsgMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m ReviewMsgMultiError) AllErrors() []error { return m }

// ReviewMsgValidationError is the validation error returned by
// ReviewMsg.Validate if the designated constraints aren't met.
type ReviewMsgValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e ReviewMsgValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e ReviewMsgValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e ReviewMsgValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e ReviewMsgValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e ReviewMsgValidationError) ErrorName() string { return "ReviewMsgValidationError" }

// Error satisfies the builtin error interface
func (e ReviewMsgValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sReviewMsg.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = ReviewMsgValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = ReviewMsgValidationError{}

// Validate checks the field values on CheckVersionReq with the rules defined
// in the proto definition for this message. If any rules are violated, the
// first error encountered is returned, or nil if there are no violations.
func (m *CheckVersionReq) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on CheckVersionReq with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// CheckVersionReqMultiError, or nil if none found.
func (m *CheckVersionReq) ValidateAll() error {
	return m.validate(true)
}

func (m *CheckVersionReq) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if len(errors) > 0 {
		return CheckVersionReqMultiError(errors)
	}

	return nil
}

// CheckVersionReqMultiError is an error wrapping multiple validation errors
// returned by CheckVersionReq.ValidateAll() if the designated constraints
// aren't met.
type CheckVersionReqMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m CheckVersionReqMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m CheckVersionReqMultiError) AllErrors() []error { return m }

// CheckVersionReqValidationError is the validation error returned by
// CheckVersionReq.Validate if the designated constraints aren't met.
type CheckVersionReqValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e CheckVersionReqValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e CheckVersionReqValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e CheckVersionReqValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e CheckVersionReqValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e CheckVersionReqValidationError) ErrorName() string { return "CheckVersionReqValidationError" }

// Error satisfies the builtin error interface
func (e CheckVersionReqValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sCheckVersionReq.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = CheckVersionReqValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = CheckVersionReqValidationError{}

// Validate checks the field values on CheckVersionResp with the rules defined
// in the proto definition for this message. If any rules are violated, the
// first error encountered is returned, or nil if there are no violations.
func (m *CheckVersionResp) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on CheckVersionResp with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// CheckVersionRespMultiError, or nil if none found.
func (m *CheckVersionResp) ValidateAll() error {
	return m.validate(true)
}

func (m *CheckVersionResp) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Code

	// no validation rules for Msg

	if all {
		switch v := interface{}(m.GetData()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, CheckVersionRespValidationError{
					field:  "Data",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, CheckVersionRespValidationError{
					field:  "Data",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetData()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return CheckVersionRespValidationError{
				field:  "Data",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return CheckVersionRespMultiError(errors)
	}

	return nil
}

// CheckVersionRespMultiError is an error wrapping multiple validation errors
// returned by CheckVersionResp.ValidateAll() if the designated constraints
// aren't met.
type CheckVersionRespMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m CheckVersionRespMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m CheckVersionRespMultiError) AllErrors() []error { return m }

// CheckVersionRespValidationError is the validation error returned by
// CheckVersionResp.Validate if the designated constraints aren't met.
type CheckVersionRespValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e CheckVersionRespValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e CheckVersionRespValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e CheckVersionRespValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e CheckVersionRespValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e CheckVersionRespValidationError) ErrorName() string { return "CheckVersionRespValidationError" }

// Error satisfies the builtin error interface
func (e CheckVersionRespValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sCheckVersionResp.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = CheckVersionRespValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = CheckVersionRespValidationError{}

// Validate checks the field values on GetApkUrlReq with the rules defined in
// the proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *GetApkUrlReq) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GetApkUrlReq with the rules defined
// in the proto definition for this message. If any rules are violated, the
// result is a list of violation errors wrapped in GetApkUrlReqMultiError, or
// nil if none found.
func (m *GetApkUrlReq) ValidateAll() error {
	return m.validate(true)
}

func (m *GetApkUrlReq) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if len(errors) > 0 {
		return GetApkUrlReqMultiError(errors)
	}

	return nil
}

// GetApkUrlReqMultiError is an error wrapping multiple validation errors
// returned by GetApkUrlReq.ValidateAll() if the designated constraints aren't met.
type GetApkUrlReqMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetApkUrlReqMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetApkUrlReqMultiError) AllErrors() []error { return m }

// GetApkUrlReqValidationError is the validation error returned by
// GetApkUrlReq.Validate if the designated constraints aren't met.
type GetApkUrlReqValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetApkUrlReqValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetApkUrlReqValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetApkUrlReqValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetApkUrlReqValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetApkUrlReqValidationError) ErrorName() string { return "GetApkUrlReqValidationError" }

// Error satisfies the builtin error interface
func (e GetApkUrlReqValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetApkUrlReq.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetApkUrlReqValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetApkUrlReqValidationError{}

// Validate checks the field values on GetApkUrlResp with the rules defined in
// the proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *GetApkUrlResp) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GetApkUrlResp with the rules defined
// in the proto definition for this message. If any rules are violated, the
// result is a list of violation errors wrapped in GetApkUrlRespMultiError, or
// nil if none found.
func (m *GetApkUrlResp) ValidateAll() error {
	return m.validate(true)
}

func (m *GetApkUrlResp) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Code

	// no validation rules for Msg

	if all {
		switch v := interface{}(m.GetData()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetApkUrlRespValidationError{
					field:  "Data",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetApkUrlRespValidationError{
					field:  "Data",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetData()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetApkUrlRespValidationError{
				field:  "Data",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return GetApkUrlRespMultiError(errors)
	}

	return nil
}

// GetApkUrlRespMultiError is an error wrapping multiple validation errors
// returned by GetApkUrlResp.ValidateAll() if the designated constraints
// aren't met.
type GetApkUrlRespMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetApkUrlRespMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetApkUrlRespMultiError) AllErrors() []error { return m }

// GetApkUrlRespValidationError is the validation error returned by
// GetApkUrlResp.Validate if the designated constraints aren't met.
type GetApkUrlRespValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetApkUrlRespValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetApkUrlRespValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetApkUrlRespValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetApkUrlRespValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetApkUrlRespValidationError) ErrorName() string { return "GetApkUrlRespValidationError" }

// Error satisfies the builtin error interface
func (e GetApkUrlRespValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetApkUrlResp.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetApkUrlRespValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetApkUrlRespValidationError{}

// Validate checks the field values on GetApkUrlRespData with the rules defined
// in the proto definition for this message. If any rules are violated, the
// first error encountered is returned, or nil if there are no violations.
func (m *GetApkUrlRespData) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GetApkUrlRespData with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// GetApkUrlRespDataMultiError, or nil if none found.
func (m *GetApkUrlRespData) ValidateAll() error {
	return m.validate(true)
}

func (m *GetApkUrlRespData) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Url

	if len(errors) > 0 {
		return GetApkUrlRespDataMultiError(errors)
	}

	return nil
}

// GetApkUrlRespDataMultiError is an error wrapping multiple validation errors
// returned by GetApkUrlRespData.ValidateAll() if the designated constraints
// aren't met.
type GetApkUrlRespDataMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetApkUrlRespDataMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetApkUrlRespDataMultiError) AllErrors() []error { return m }

// GetApkUrlRespDataValidationError is the validation error returned by
// GetApkUrlRespData.Validate if the designated constraints aren't met.
type GetApkUrlRespDataValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetApkUrlRespDataValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetApkUrlRespDataValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetApkUrlRespDataValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetApkUrlRespDataValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetApkUrlRespDataValidationError) ErrorName() string {
	return "GetApkUrlRespDataValidationError"
}

// Error satisfies the builtin error interface
func (e GetApkUrlRespDataValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetApkUrlRespData.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetApkUrlRespDataValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetApkUrlRespDataValidationError{}
