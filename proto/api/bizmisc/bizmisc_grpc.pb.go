// Code generated by protoc-gen-go-grpc. DO NOT EDIT.
// versions:
// - protoc-gen-go-grpc v1.5.1
// - protoc             v5.29.3
// source: bizmisc.proto

package bizmisc

import (
	context "context"
	grpc "google.golang.org/grpc"
	codes "google.golang.org/grpc/codes"
	status "google.golang.org/grpc/status"
)

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
// Requires gRPC-Go v1.64.0 or later.
const _ = grpc.SupportPackageIsVersion9

const (
	S_GetAliObjectId_FullMethodName          = "/vc.bizmisc.s/GetAliObjectId"
	S_GenerateAliOssSignature_FullMethodName = "/vc.bizmisc.s/GenerateAliOssSignature"
	S_GetChanToken_FullMethodName            = "/vc.bizmisc.s/GetChanToken"
	S_GetAsrAddr_FullMethodName              = "/vc.bizmisc.s/GetAsrAddr"
	S_GetVcPushAddr_FullMethodName           = "/vc.bizmisc.s/GetVcPushAddr"
	S_TestSendSecretaryMsg_FullMethodName    = "/vc.bizmisc.s/TestSendSecretaryMsg"
	S_TestSendSystemMsg_FullMethodName       = "/vc.bizmisc.s/TestSendSystemMsg"
	S_CheckVersion_FullMethodName            = "/vc.bizmisc.s/CheckVersion"
	S_GetOfficialApkUrl_FullMethodName       = "/vc.bizmisc.s/GetOfficialApkUrl"
)

// SClient is the client API for S service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://pkg.go.dev/google.golang.org/grpc/?tab=doc#ClientConn.NewStream.
type SClient interface {
	// 返回阿里云objectId
	GetAliObjectId(ctx context.Context, in *AliObjectIdReq, opts ...grpc.CallOption) (*AliObjectIdResp, error)
	// 获取阿里云oss签名
	GenerateAliOssSignature(ctx context.Context, in *AliOssSigatureReq, opts ...grpc.CallOption) (*AliOssSigatureResp, error)
	// 获取长连接token
	GetChanToken(ctx context.Context, in *GetChanTokenReq, opts ...grpc.CallOption) (*GetChanTokenResp, error)
	// ASR长连接地址
	GetAsrAddr(ctx context.Context, in *GetAsrAddrReq, opts ...grpc.CallOption) (*GetAsrAddrResp, error)
	// 获取vc推流长连接
	GetVcPushAddr(ctx context.Context, in *GetVcPushAddrReq, opts ...grpc.CallOption) (*GetVcPushAddrResp, error)
	// 内部测试接口
	TestSendSecretaryMsg(ctx context.Context, in *SendSecretaryMsgReq, opts ...grpc.CallOption) (*SendSecretaryMsgResp, error)
	// 系统消息测试接口
	TestSendSystemMsg(ctx context.Context, in *TestSendSystemMsgReq, opts ...grpc.CallOption) (*TestSendSystemMsgResp, error)
	// 检查版本更新
	CheckVersion(ctx context.Context, in *CheckVersionReq, opts ...grpc.CallOption) (*CheckVersionResp, error)
	// 获取APK下载地址
	GetOfficialApkUrl(ctx context.Context, in *GetApkUrlReq, opts ...grpc.CallOption) (*GetApkUrlResp, error)
}

type sClient struct {
	cc grpc.ClientConnInterface
}

func NewSClient(cc grpc.ClientConnInterface) SClient {
	return &sClient{cc}
}

func (c *sClient) GetAliObjectId(ctx context.Context, in *AliObjectIdReq, opts ...grpc.CallOption) (*AliObjectIdResp, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(AliObjectIdResp)
	err := c.cc.Invoke(ctx, S_GetAliObjectId_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *sClient) GenerateAliOssSignature(ctx context.Context, in *AliOssSigatureReq, opts ...grpc.CallOption) (*AliOssSigatureResp, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(AliOssSigatureResp)
	err := c.cc.Invoke(ctx, S_GenerateAliOssSignature_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *sClient) GetChanToken(ctx context.Context, in *GetChanTokenReq, opts ...grpc.CallOption) (*GetChanTokenResp, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(GetChanTokenResp)
	err := c.cc.Invoke(ctx, S_GetChanToken_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *sClient) GetAsrAddr(ctx context.Context, in *GetAsrAddrReq, opts ...grpc.CallOption) (*GetAsrAddrResp, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(GetAsrAddrResp)
	err := c.cc.Invoke(ctx, S_GetAsrAddr_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *sClient) GetVcPushAddr(ctx context.Context, in *GetVcPushAddrReq, opts ...grpc.CallOption) (*GetVcPushAddrResp, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(GetVcPushAddrResp)
	err := c.cc.Invoke(ctx, S_GetVcPushAddr_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *sClient) TestSendSecretaryMsg(ctx context.Context, in *SendSecretaryMsgReq, opts ...grpc.CallOption) (*SendSecretaryMsgResp, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(SendSecretaryMsgResp)
	err := c.cc.Invoke(ctx, S_TestSendSecretaryMsg_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *sClient) TestSendSystemMsg(ctx context.Context, in *TestSendSystemMsgReq, opts ...grpc.CallOption) (*TestSendSystemMsgResp, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(TestSendSystemMsgResp)
	err := c.cc.Invoke(ctx, S_TestSendSystemMsg_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *sClient) CheckVersion(ctx context.Context, in *CheckVersionReq, opts ...grpc.CallOption) (*CheckVersionResp, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(CheckVersionResp)
	err := c.cc.Invoke(ctx, S_CheckVersion_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *sClient) GetOfficialApkUrl(ctx context.Context, in *GetApkUrlReq, opts ...grpc.CallOption) (*GetApkUrlResp, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(GetApkUrlResp)
	err := c.cc.Invoke(ctx, S_GetOfficialApkUrl_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// SServer is the server API for S service.
// All implementations should embed UnimplementedSServer
// for forward compatibility.
type SServer interface {
	// 返回阿里云objectId
	GetAliObjectId(context.Context, *AliObjectIdReq) (*AliObjectIdResp, error)
	// 获取阿里云oss签名
	GenerateAliOssSignature(context.Context, *AliOssSigatureReq) (*AliOssSigatureResp, error)
	// 获取长连接token
	GetChanToken(context.Context, *GetChanTokenReq) (*GetChanTokenResp, error)
	// ASR长连接地址
	GetAsrAddr(context.Context, *GetAsrAddrReq) (*GetAsrAddrResp, error)
	// 获取vc推流长连接
	GetVcPushAddr(context.Context, *GetVcPushAddrReq) (*GetVcPushAddrResp, error)
	// 内部测试接口
	TestSendSecretaryMsg(context.Context, *SendSecretaryMsgReq) (*SendSecretaryMsgResp, error)
	// 系统消息测试接口
	TestSendSystemMsg(context.Context, *TestSendSystemMsgReq) (*TestSendSystemMsgResp, error)
	// 检查版本更新
	CheckVersion(context.Context, *CheckVersionReq) (*CheckVersionResp, error)
	// 获取APK下载地址
	GetOfficialApkUrl(context.Context, *GetApkUrlReq) (*GetApkUrlResp, error)
}

// UnimplementedSServer should be embedded to have
// forward compatible implementations.
//
// NOTE: this should be embedded by value instead of pointer to avoid a nil
// pointer dereference when methods are called.
type UnimplementedSServer struct{}

func (UnimplementedSServer) GetAliObjectId(context.Context, *AliObjectIdReq) (*AliObjectIdResp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetAliObjectId not implemented")
}
func (UnimplementedSServer) GenerateAliOssSignature(context.Context, *AliOssSigatureReq) (*AliOssSigatureResp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GenerateAliOssSignature not implemented")
}
func (UnimplementedSServer) GetChanToken(context.Context, *GetChanTokenReq) (*GetChanTokenResp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetChanToken not implemented")
}
func (UnimplementedSServer) GetAsrAddr(context.Context, *GetAsrAddrReq) (*GetAsrAddrResp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetAsrAddr not implemented")
}
func (UnimplementedSServer) GetVcPushAddr(context.Context, *GetVcPushAddrReq) (*GetVcPushAddrResp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetVcPushAddr not implemented")
}
func (UnimplementedSServer) TestSendSecretaryMsg(context.Context, *SendSecretaryMsgReq) (*SendSecretaryMsgResp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method TestSendSecretaryMsg not implemented")
}
func (UnimplementedSServer) TestSendSystemMsg(context.Context, *TestSendSystemMsgReq) (*TestSendSystemMsgResp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method TestSendSystemMsg not implemented")
}
func (UnimplementedSServer) CheckVersion(context.Context, *CheckVersionReq) (*CheckVersionResp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method CheckVersion not implemented")
}
func (UnimplementedSServer) GetOfficialApkUrl(context.Context, *GetApkUrlReq) (*GetApkUrlResp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetOfficialApkUrl not implemented")
}
func (UnimplementedSServer) testEmbeddedByValue() {}

// UnsafeSServer may be embedded to opt out of forward compatibility for this service.
// Use of this interface is not recommended, as added methods to SServer will
// result in compilation errors.
type UnsafeSServer interface {
	mustEmbedUnimplementedSServer()
}

func RegisterSServer(s grpc.ServiceRegistrar, srv SServer) {
	// If the following call pancis, it indicates UnimplementedSServer was
	// embedded by pointer and is nil.  This will cause panics if an
	// unimplemented method is ever invoked, so we test this at initialization
	// time to prevent it from happening at runtime later due to I/O.
	if t, ok := srv.(interface{ testEmbeddedByValue() }); ok {
		t.testEmbeddedByValue()
	}
	s.RegisterService(&S_ServiceDesc, srv)
}

func _S_GetAliObjectId_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(AliObjectIdReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(SServer).GetAliObjectId(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: S_GetAliObjectId_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(SServer).GetAliObjectId(ctx, req.(*AliObjectIdReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _S_GenerateAliOssSignature_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(AliOssSigatureReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(SServer).GenerateAliOssSignature(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: S_GenerateAliOssSignature_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(SServer).GenerateAliOssSignature(ctx, req.(*AliOssSigatureReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _S_GetChanToken_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetChanTokenReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(SServer).GetChanToken(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: S_GetChanToken_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(SServer).GetChanToken(ctx, req.(*GetChanTokenReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _S_GetAsrAddr_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetAsrAddrReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(SServer).GetAsrAddr(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: S_GetAsrAddr_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(SServer).GetAsrAddr(ctx, req.(*GetAsrAddrReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _S_GetVcPushAddr_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetVcPushAddrReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(SServer).GetVcPushAddr(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: S_GetVcPushAddr_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(SServer).GetVcPushAddr(ctx, req.(*GetVcPushAddrReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _S_TestSendSecretaryMsg_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(SendSecretaryMsgReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(SServer).TestSendSecretaryMsg(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: S_TestSendSecretaryMsg_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(SServer).TestSendSecretaryMsg(ctx, req.(*SendSecretaryMsgReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _S_TestSendSystemMsg_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(TestSendSystemMsgReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(SServer).TestSendSystemMsg(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: S_TestSendSystemMsg_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(SServer).TestSendSystemMsg(ctx, req.(*TestSendSystemMsgReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _S_CheckVersion_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(CheckVersionReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(SServer).CheckVersion(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: S_CheckVersion_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(SServer).CheckVersion(ctx, req.(*CheckVersionReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _S_GetOfficialApkUrl_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetApkUrlReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(SServer).GetOfficialApkUrl(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: S_GetOfficialApkUrl_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(SServer).GetOfficialApkUrl(ctx, req.(*GetApkUrlReq))
	}
	return interceptor(ctx, in, info, handler)
}

// S_ServiceDesc is the grpc.ServiceDesc for S service.
// It's only intended for direct use with grpc.RegisterService,
// and not to be introspected or modified (even as a copy)
var S_ServiceDesc = grpc.ServiceDesc{
	ServiceName: "vc.bizmisc.s",
	HandlerType: (*SServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "GetAliObjectId",
			Handler:    _S_GetAliObjectId_Handler,
		},
		{
			MethodName: "GenerateAliOssSignature",
			Handler:    _S_GenerateAliOssSignature_Handler,
		},
		{
			MethodName: "GetChanToken",
			Handler:    _S_GetChanToken_Handler,
		},
		{
			MethodName: "GetAsrAddr",
			Handler:    _S_GetAsrAddr_Handler,
		},
		{
			MethodName: "GetVcPushAddr",
			Handler:    _S_GetVcPushAddr_Handler,
		},
		{
			MethodName: "TestSendSecretaryMsg",
			Handler:    _S_TestSendSecretaryMsg_Handler,
		},
		{
			MethodName: "TestSendSystemMsg",
			Handler:    _S_TestSendSystemMsg_Handler,
		},
		{
			MethodName: "CheckVersion",
			Handler:    _S_CheckVersion_Handler,
		},
		{
			MethodName: "GetOfficialApkUrl",
			Handler:    _S_GetOfficialApkUrl_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "bizmisc.proto",
}
