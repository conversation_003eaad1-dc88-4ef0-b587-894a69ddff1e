syntax = "proto3";

package vc.bizmisc;
option go_package = "new-gitlab.xunlei.cn/vcproject/backends/proto/api/bizmisc;bizmisc";

import "google/api/annotations.proto";
import "protoc-gen-validate/validate/validate.proto";
import "google/api/field_behavior.proto";
import "svcchat/svcchat.proto";
import "svcscript/svcscript.proto";
import "svcconfig/svcconfig.proto";

message AliObjectIdReq {
  // 对象类型["image", "audio", "video", "data"]
  string type=1[(validate.rules).string={in:["image", "audio", "video", "data"]},(google.api.field_behavior) = REQUIRED];
  // 业务类型, 目前支持的类型如下：
  // type = image,  biz_type 类型支持： avatar, scenario_cover
  string biz_type=2[(validate.rules).string={min_bytes:1}, (google.api.field_behavior) = REQUIRED];
}

message AliObjectIdResp {
  int32 code=1;
  string msg=2;
  AliObjectIdRespData data=3;
}

message AliObjectIdRespData {
  string endpoint=1;
  string bucket=2;
  string object_key=3;
  string object_id=4;
}

message AliOssSigatureReq {
  // 对象类型["image", "audio", "video", "data"]
  string type=1[(validate.rules).string={in:["image", "audio", "video", "data"]},(google.api.field_behavior) = REQUIRED];
  // 签名内容
  string content=2[(validate.rules).string={min_bytes:1},(google.api.field_behavior) = REQUIRED];
}

message AliOssSigatureResp {
   int32 code=1;
   string msg=2;
   AliOssSigatureRespData data=3;
}

message AliOssSigatureRespData {
  string signature=1;
}

message GetChanTokenReq {
}

message GetChanTokenResp {
  int32 code=1;
  string msg=2;
  GetChanTokenRespData data=3;
}

message GetChanTokenRespData {
  //长连接token
  string ws_url = 1;
  //客户端发上行消息使用的通用topic
  string generalTopic = 2;
  // payload是否进行gzip压缩
  bool gzip = 3;
}

message RvcInfo {
  string rvc_name = 1;
  string seed_name = 2;
  string reference_audio_url = 3;
  string request_id = 4;
  string mid = 5;
  bool cascaded_use_rvc = 6;
  string f0_method = 7;
  float rms_mix_rate=8;
  float protect=9;
  float diffusion_steps = 10;
  float length_adjust = 11;
  float inference_cfg_rate = 12;
  int64 sample_rate = 13;  //采样率
  int32 bit_depth=14;   //采样的位深 16位，32位
  int32 channels=15;   //声道数
  int64 output_sample_rate=16; //输出采样率
  int32 output_bit_depth=17;  //输出位深
  int32 output_channels=18;   //输出声道
  int32 audio_milli_duration=19;  //录音时长，单位毫秒
  int32 chunk_ms = 20; // websocket上传音频块时长, 单位毫秒
}

message GetAsrAddrReq {
}

message GetAsrAddrResp {
  int32 code=1;
  string msg=2;
  GetAsrAddrRespData data=3;
}

message GetAsrAddrRespData {
  string addr = 1;
}

message GetVcPushAddrReq {
  int64 character_id = 1 [(validate.rules).int64.gt = 0, (google.api.field_behavior) = REQUIRED];
  int64 character_assets_id=2 [(validate.rules).int64.gt = 0, (google.api.field_behavior) = REQUIRED];  
  string scene =3[(validate.rules).string = {in:["comment", "dubbing", "signature"]}, (google.api.field_behavior) = REQUIRED];   // 场景, comment, dubbing, signature
}

message GetVcPushAddrResp {
  int32 code=1;
  string msg=2;
  GetVcPushAddrRespData data=3;
} 

message GetVcPushAddrRespData {
  string ws_addr = 1;
  // rvc配置信息
  RvcInfo rvc = 2;
}

message ScriptInfo {
  int64 script_id=1;
  string title=2;
  string cover=3;
}

message ScriptDetail {

}

message Comment {
  int64 comment_id=1;
  string text=2;
  string audio_url=3;
  int64 duration=4;
}

message CommentReply {
  int64 comment_id=1;
  string text=2;
  string audio_url=3;
  int64 duration=4;
}

message Dubbing {
  int64 dubbing_id=1;
  string audio_url=2;
  int64 duration=3;
}

message SendSecretaryMsgReq {
  int64 to_uid=1[(validate.rules).int64.gt=0];
  int32 content_type=2[(validate.rules).int32.gt=0];
  string content_image=3;
  string tpl=4;
  map<string, svcchat.TemplateItem> tpl_data=5;
  svcchat.SimpleMember member=6;
  ScriptInfo script=7;
  Comment comment=8;
  CommentReply comment_reply=9;
  Dubbing dubbing=10;
}

message SendSecretaryMsgResp  {
  int32 code=1;
  string msg=2;
}


message TestSendSystemMsgReq {
  int64 to_uid=1[(validate.rules).int64.gt=0];
  int32 content_type=2[(validate.rules).int32.gt=0];
  SystemMsg system_msg = 3;
  FollowMsg follow_msg = 4;
  CommentMsg comment_msg = 5;
  LikeMsg like_msg = 6;
  DubbingMsg dubbing_msg = 7;
  ReviewMsg review_msg = 8;
}

message TestSendSystemMsgResp {
  int32 code=1;
  string msg=2;
}

message SystemMsg {
  svcchat.TemplateMsg title = 1;
  string bgm_url = 2;
  string jump_url = 3;
  svcchat.TemplateMsg content = 4;
  int64 time = 5;
}

message FollowMsg {
  svcchat.TemplateMsg content = 1;
  svcchat.SimpleMember member=2;
  int64 time = 3;
}

message CommentMsg {
  svcchat.TemplateMsg content = 1;
  svcchat.SimpleMember member=2;
  Comment comment=3;
  CommentReply comment_reply=4;
  ScriptInfo script=5;
  int64 time = 6;
}

message LikeMsg {
  svcchat.TemplateMsg content = 1;
  svcchat.SimpleMember member=2;
  ScriptInfo script=3;
  Comment comment=4;
  Dubbing dubbing=5;
  int64 time = 6;
}

message DubbingMsg {
  svcchat.TemplateMsg content = 1;
  svcchat.SimpleMember member=2;
  ScriptInfo script=3;
  Dubbing dubbing=5;
  int64 time = 6;
}

message ReviewMsg {
  svcchat.TemplateMsg title = 1;
  string bgm_url = 2;
  string jump_url = 3;
  svcchat.TemplateMsg content = 4;
  svcscript.Script script=5;
}

// 检查版本请求
message CheckVersionReq {
    // 无需额外参数，从context中获取用户信息
}

// 检查版本响应
message CheckVersionResp {
  int32 code = 1;
  string msg = 2;
  svcconfig.CheckVersionData data = 3;
}

message GetApkUrlReq {

}

message GetApkUrlResp {
  int32 code = 1;
  string msg = 2;
  GetApkUrlRespData data = 3;
}

message GetApkUrlRespData {
  string url=1;
}

service s {
    // 返回阿里云objectId
    rpc GetAliObjectId(AliObjectIdReq) returns (AliObjectIdResp) {
      option (google.api.http) = {
        post: "/vc.bizmisc.s/v1/get_ali_object_id"
        body: "*"
      };
    }
    // 获取阿里云oss签名
    rpc GenerateAliOssSignature(AliOssSigatureReq) returns (AliOssSigatureResp) {
      option (google.api.http) = {
        post: "/vc.bizmisc.s/v1/generate_ali_oss_signature"
        body: "*"
      };
    }

    // 获取长连接token
    rpc GetChanToken(GetChanTokenReq) returns (GetChanTokenResp) {
      option (google.api.http) = {
        post: "/vc.bizmisc.s/v1/get_chan_token"
        body: "*"
      };
    }

    // ASR长连接地址
    rpc GetAsrAddr(GetAsrAddrReq) returns (GetAsrAddrResp) {
      option (google.api.http) = {
        post: "/vc.bizmisc.s/v1/get_asr_addr"
        body: "*"
      };
    }

    // 获取vc推流长连接
    rpc GetVcPushAddr(GetVcPushAddrReq) returns (GetVcPushAddrResp) {
      option (google.api.http) = {
        post: "/vc.bizmisc.s/v1/get_vc_push_addr"
        body: "*"
      };
    }

    // 内部测试接口
    rpc TestSendSecretaryMsg(SendSecretaryMsgReq) returns(SendSecretaryMsgResp) {
      option (google.api.http) = {
        post: "/vc.bizmisc.s/v1/send_secretary_msg"
        body: "*"
      };
    }

  // 系统消息测试接口
  rpc TestSendSystemMsg(TestSendSystemMsgReq) returns(TestSendSystemMsgResp) {
    option (google.api.http) = {
      post: "/vc.bizmisc.s/v1/send_system_msg"
      body: "*"
    };
  }

  // 检查版本更新
  rpc CheckVersion(CheckVersionReq) returns(CheckVersionResp) {
    option (google.api.http) = {
      post: "/vc.bizmisc.s/v1/check_version"
      body: "*"
    };
  }

  // 获取APK下载地址
  rpc GetOfficialApkUrl(GetApkUrlReq) returns (GetApkUrlResp) {
    option (google.api.http) = {
      get: "/vc.bizmisc.s/v1/get_official_apk_url"
    };
  }
}
