// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.36.6
// 	protoc        v5.29.3
// source: bizmisc.proto

package bizmisc

import (
	_ "github.com/envoyproxy/protoc-gen-validate/validate"
	_ "google.golang.org/genproto/googleapis/api/annotations"
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	svcchat "new-gitlab.xunlei.cn/vcproject/backends/proto/api/svcchat"
	svcconfig "new-gitlab.xunlei.cn/vcproject/backends/proto/api/svcconfig"
	svcscript "new-gitlab.xunlei.cn/vcproject/backends/proto/api/svcscript"
	reflect "reflect"
	sync "sync"
	unsafe "unsafe"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type AliObjectIdReq struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// 对象类型["image", "audio", "video", "data"]
	Type string `protobuf:"bytes,1,opt,name=type,proto3" json:"type,omitempty"`
	// 业务类型, 目前支持的类型如下：
	// type = image,  biz_type 类型支持： avatar, scenario_cover
	BizType       string `protobuf:"bytes,2,opt,name=biz_type,json=bizType,proto3" json:"biz_type,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *AliObjectIdReq) Reset() {
	*x = AliObjectIdReq{}
	mi := &file_bizmisc_proto_msgTypes[0]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *AliObjectIdReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*AliObjectIdReq) ProtoMessage() {}

func (x *AliObjectIdReq) ProtoReflect() protoreflect.Message {
	mi := &file_bizmisc_proto_msgTypes[0]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use AliObjectIdReq.ProtoReflect.Descriptor instead.
func (*AliObjectIdReq) Descriptor() ([]byte, []int) {
	return file_bizmisc_proto_rawDescGZIP(), []int{0}
}

func (x *AliObjectIdReq) GetType() string {
	if x != nil {
		return x.Type
	}
	return ""
}

func (x *AliObjectIdReq) GetBizType() string {
	if x != nil {
		return x.BizType
	}
	return ""
}

type AliObjectIdResp struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Code          int32                  `protobuf:"varint,1,opt,name=code,proto3" json:"code,omitempty"`
	Msg           string                 `protobuf:"bytes,2,opt,name=msg,proto3" json:"msg,omitempty"`
	Data          *AliObjectIdRespData   `protobuf:"bytes,3,opt,name=data,proto3" json:"data,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *AliObjectIdResp) Reset() {
	*x = AliObjectIdResp{}
	mi := &file_bizmisc_proto_msgTypes[1]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *AliObjectIdResp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*AliObjectIdResp) ProtoMessage() {}

func (x *AliObjectIdResp) ProtoReflect() protoreflect.Message {
	mi := &file_bizmisc_proto_msgTypes[1]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use AliObjectIdResp.ProtoReflect.Descriptor instead.
func (*AliObjectIdResp) Descriptor() ([]byte, []int) {
	return file_bizmisc_proto_rawDescGZIP(), []int{1}
}

func (x *AliObjectIdResp) GetCode() int32 {
	if x != nil {
		return x.Code
	}
	return 0
}

func (x *AliObjectIdResp) GetMsg() string {
	if x != nil {
		return x.Msg
	}
	return ""
}

func (x *AliObjectIdResp) GetData() *AliObjectIdRespData {
	if x != nil {
		return x.Data
	}
	return nil
}

type AliObjectIdRespData struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Endpoint      string                 `protobuf:"bytes,1,opt,name=endpoint,proto3" json:"endpoint,omitempty"`
	Bucket        string                 `protobuf:"bytes,2,opt,name=bucket,proto3" json:"bucket,omitempty"`
	ObjectKey     string                 `protobuf:"bytes,3,opt,name=object_key,json=objectKey,proto3" json:"object_key,omitempty"`
	ObjectId      string                 `protobuf:"bytes,4,opt,name=object_id,json=objectId,proto3" json:"object_id,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *AliObjectIdRespData) Reset() {
	*x = AliObjectIdRespData{}
	mi := &file_bizmisc_proto_msgTypes[2]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *AliObjectIdRespData) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*AliObjectIdRespData) ProtoMessage() {}

func (x *AliObjectIdRespData) ProtoReflect() protoreflect.Message {
	mi := &file_bizmisc_proto_msgTypes[2]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use AliObjectIdRespData.ProtoReflect.Descriptor instead.
func (*AliObjectIdRespData) Descriptor() ([]byte, []int) {
	return file_bizmisc_proto_rawDescGZIP(), []int{2}
}

func (x *AliObjectIdRespData) GetEndpoint() string {
	if x != nil {
		return x.Endpoint
	}
	return ""
}

func (x *AliObjectIdRespData) GetBucket() string {
	if x != nil {
		return x.Bucket
	}
	return ""
}

func (x *AliObjectIdRespData) GetObjectKey() string {
	if x != nil {
		return x.ObjectKey
	}
	return ""
}

func (x *AliObjectIdRespData) GetObjectId() string {
	if x != nil {
		return x.ObjectId
	}
	return ""
}

type AliOssSigatureReq struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// 对象类型["image", "audio", "video", "data"]
	Type string `protobuf:"bytes,1,opt,name=type,proto3" json:"type,omitempty"`
	// 签名内容
	Content       string `protobuf:"bytes,2,opt,name=content,proto3" json:"content,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *AliOssSigatureReq) Reset() {
	*x = AliOssSigatureReq{}
	mi := &file_bizmisc_proto_msgTypes[3]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *AliOssSigatureReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*AliOssSigatureReq) ProtoMessage() {}

func (x *AliOssSigatureReq) ProtoReflect() protoreflect.Message {
	mi := &file_bizmisc_proto_msgTypes[3]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use AliOssSigatureReq.ProtoReflect.Descriptor instead.
func (*AliOssSigatureReq) Descriptor() ([]byte, []int) {
	return file_bizmisc_proto_rawDescGZIP(), []int{3}
}

func (x *AliOssSigatureReq) GetType() string {
	if x != nil {
		return x.Type
	}
	return ""
}

func (x *AliOssSigatureReq) GetContent() string {
	if x != nil {
		return x.Content
	}
	return ""
}

type AliOssSigatureResp struct {
	state         protoimpl.MessageState  `protogen:"open.v1"`
	Code          int32                   `protobuf:"varint,1,opt,name=code,proto3" json:"code,omitempty"`
	Msg           string                  `protobuf:"bytes,2,opt,name=msg,proto3" json:"msg,omitempty"`
	Data          *AliOssSigatureRespData `protobuf:"bytes,3,opt,name=data,proto3" json:"data,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *AliOssSigatureResp) Reset() {
	*x = AliOssSigatureResp{}
	mi := &file_bizmisc_proto_msgTypes[4]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *AliOssSigatureResp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*AliOssSigatureResp) ProtoMessage() {}

func (x *AliOssSigatureResp) ProtoReflect() protoreflect.Message {
	mi := &file_bizmisc_proto_msgTypes[4]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use AliOssSigatureResp.ProtoReflect.Descriptor instead.
func (*AliOssSigatureResp) Descriptor() ([]byte, []int) {
	return file_bizmisc_proto_rawDescGZIP(), []int{4}
}

func (x *AliOssSigatureResp) GetCode() int32 {
	if x != nil {
		return x.Code
	}
	return 0
}

func (x *AliOssSigatureResp) GetMsg() string {
	if x != nil {
		return x.Msg
	}
	return ""
}

func (x *AliOssSigatureResp) GetData() *AliOssSigatureRespData {
	if x != nil {
		return x.Data
	}
	return nil
}

type AliOssSigatureRespData struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Signature     string                 `protobuf:"bytes,1,opt,name=signature,proto3" json:"signature,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *AliOssSigatureRespData) Reset() {
	*x = AliOssSigatureRespData{}
	mi := &file_bizmisc_proto_msgTypes[5]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *AliOssSigatureRespData) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*AliOssSigatureRespData) ProtoMessage() {}

func (x *AliOssSigatureRespData) ProtoReflect() protoreflect.Message {
	mi := &file_bizmisc_proto_msgTypes[5]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use AliOssSigatureRespData.ProtoReflect.Descriptor instead.
func (*AliOssSigatureRespData) Descriptor() ([]byte, []int) {
	return file_bizmisc_proto_rawDescGZIP(), []int{5}
}

func (x *AliOssSigatureRespData) GetSignature() string {
	if x != nil {
		return x.Signature
	}
	return ""
}

type GetChanTokenReq struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *GetChanTokenReq) Reset() {
	*x = GetChanTokenReq{}
	mi := &file_bizmisc_proto_msgTypes[6]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GetChanTokenReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetChanTokenReq) ProtoMessage() {}

func (x *GetChanTokenReq) ProtoReflect() protoreflect.Message {
	mi := &file_bizmisc_proto_msgTypes[6]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetChanTokenReq.ProtoReflect.Descriptor instead.
func (*GetChanTokenReq) Descriptor() ([]byte, []int) {
	return file_bizmisc_proto_rawDescGZIP(), []int{6}
}

type GetChanTokenResp struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Code          int32                  `protobuf:"varint,1,opt,name=code,proto3" json:"code,omitempty"`
	Msg           string                 `protobuf:"bytes,2,opt,name=msg,proto3" json:"msg,omitempty"`
	Data          *GetChanTokenRespData  `protobuf:"bytes,3,opt,name=data,proto3" json:"data,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *GetChanTokenResp) Reset() {
	*x = GetChanTokenResp{}
	mi := &file_bizmisc_proto_msgTypes[7]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GetChanTokenResp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetChanTokenResp) ProtoMessage() {}

func (x *GetChanTokenResp) ProtoReflect() protoreflect.Message {
	mi := &file_bizmisc_proto_msgTypes[7]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetChanTokenResp.ProtoReflect.Descriptor instead.
func (*GetChanTokenResp) Descriptor() ([]byte, []int) {
	return file_bizmisc_proto_rawDescGZIP(), []int{7}
}

func (x *GetChanTokenResp) GetCode() int32 {
	if x != nil {
		return x.Code
	}
	return 0
}

func (x *GetChanTokenResp) GetMsg() string {
	if x != nil {
		return x.Msg
	}
	return ""
}

func (x *GetChanTokenResp) GetData() *GetChanTokenRespData {
	if x != nil {
		return x.Data
	}
	return nil
}

type GetChanTokenRespData struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// 长连接token
	WsUrl string `protobuf:"bytes,1,opt,name=ws_url,json=wsUrl,proto3" json:"ws_url,omitempty"`
	// 客户端发上行消息使用的通用topic
	GeneralTopic string `protobuf:"bytes,2,opt,name=generalTopic,proto3" json:"generalTopic,omitempty"`
	// payload是否进行gzip压缩
	Gzip          bool `protobuf:"varint,3,opt,name=gzip,proto3" json:"gzip,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *GetChanTokenRespData) Reset() {
	*x = GetChanTokenRespData{}
	mi := &file_bizmisc_proto_msgTypes[8]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GetChanTokenRespData) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetChanTokenRespData) ProtoMessage() {}

func (x *GetChanTokenRespData) ProtoReflect() protoreflect.Message {
	mi := &file_bizmisc_proto_msgTypes[8]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetChanTokenRespData.ProtoReflect.Descriptor instead.
func (*GetChanTokenRespData) Descriptor() ([]byte, []int) {
	return file_bizmisc_proto_rawDescGZIP(), []int{8}
}

func (x *GetChanTokenRespData) GetWsUrl() string {
	if x != nil {
		return x.WsUrl
	}
	return ""
}

func (x *GetChanTokenRespData) GetGeneralTopic() string {
	if x != nil {
		return x.GeneralTopic
	}
	return ""
}

func (x *GetChanTokenRespData) GetGzip() bool {
	if x != nil {
		return x.Gzip
	}
	return false
}

type RvcInfo struct {
	state              protoimpl.MessageState `protogen:"open.v1"`
	RvcName            string                 `protobuf:"bytes,1,opt,name=rvc_name,json=rvcName,proto3" json:"rvc_name,omitempty"`
	SeedName           string                 `protobuf:"bytes,2,opt,name=seed_name,json=seedName,proto3" json:"seed_name,omitempty"`
	ReferenceAudioUrl  string                 `protobuf:"bytes,3,opt,name=reference_audio_url,json=referenceAudioUrl,proto3" json:"reference_audio_url,omitempty"`
	RequestId          string                 `protobuf:"bytes,4,opt,name=request_id,json=requestId,proto3" json:"request_id,omitempty"`
	Mid                string                 `protobuf:"bytes,5,opt,name=mid,proto3" json:"mid,omitempty"`
	CascadedUseRvc     bool                   `protobuf:"varint,6,opt,name=cascaded_use_rvc,json=cascadedUseRvc,proto3" json:"cascaded_use_rvc,omitempty"`
	F0Method           string                 `protobuf:"bytes,7,opt,name=f0_method,json=f0Method,proto3" json:"f0_method,omitempty"`
	RmsMixRate         float32                `protobuf:"fixed32,8,opt,name=rms_mix_rate,json=rmsMixRate,proto3" json:"rms_mix_rate,omitempty"`
	Protect            float32                `protobuf:"fixed32,9,opt,name=protect,proto3" json:"protect,omitempty"`
	DiffusionSteps     float32                `protobuf:"fixed32,10,opt,name=diffusion_steps,json=diffusionSteps,proto3" json:"diffusion_steps,omitempty"`
	LengthAdjust       float32                `protobuf:"fixed32,11,opt,name=length_adjust,json=lengthAdjust,proto3" json:"length_adjust,omitempty"`
	InferenceCfgRate   float32                `protobuf:"fixed32,12,opt,name=inference_cfg_rate,json=inferenceCfgRate,proto3" json:"inference_cfg_rate,omitempty"`
	SampleRate         int64                  `protobuf:"varint,13,opt,name=sample_rate,json=sampleRate,proto3" json:"sample_rate,omitempty"`                           //采样率
	BitDepth           int32                  `protobuf:"varint,14,opt,name=bit_depth,json=bitDepth,proto3" json:"bit_depth,omitempty"`                                 //采样的位深 16位，32位
	Channels           int32                  `protobuf:"varint,15,opt,name=channels,proto3" json:"channels,omitempty"`                                                 //声道数
	OutputSampleRate   int64                  `protobuf:"varint,16,opt,name=output_sample_rate,json=outputSampleRate,proto3" json:"output_sample_rate,omitempty"`       //输出采样率
	OutputBitDepth     int32                  `protobuf:"varint,17,opt,name=output_bit_depth,json=outputBitDepth,proto3" json:"output_bit_depth,omitempty"`             //输出位深
	OutputChannels     int32                  `protobuf:"varint,18,opt,name=output_channels,json=outputChannels,proto3" json:"output_channels,omitempty"`               //输出声道
	AudioMilliDuration int32                  `protobuf:"varint,19,opt,name=audio_milli_duration,json=audioMilliDuration,proto3" json:"audio_milli_duration,omitempty"` //录音时长，单位毫秒
	ChunkMs            int32                  `protobuf:"varint,20,opt,name=chunk_ms,json=chunkMs,proto3" json:"chunk_ms,omitempty"`                                    // websocket上传音频块时长, 单位毫秒
	unknownFields      protoimpl.UnknownFields
	sizeCache          protoimpl.SizeCache
}

func (x *RvcInfo) Reset() {
	*x = RvcInfo{}
	mi := &file_bizmisc_proto_msgTypes[9]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *RvcInfo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*RvcInfo) ProtoMessage() {}

func (x *RvcInfo) ProtoReflect() protoreflect.Message {
	mi := &file_bizmisc_proto_msgTypes[9]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use RvcInfo.ProtoReflect.Descriptor instead.
func (*RvcInfo) Descriptor() ([]byte, []int) {
	return file_bizmisc_proto_rawDescGZIP(), []int{9}
}

func (x *RvcInfo) GetRvcName() string {
	if x != nil {
		return x.RvcName
	}
	return ""
}

func (x *RvcInfo) GetSeedName() string {
	if x != nil {
		return x.SeedName
	}
	return ""
}

func (x *RvcInfo) GetReferenceAudioUrl() string {
	if x != nil {
		return x.ReferenceAudioUrl
	}
	return ""
}

func (x *RvcInfo) GetRequestId() string {
	if x != nil {
		return x.RequestId
	}
	return ""
}

func (x *RvcInfo) GetMid() string {
	if x != nil {
		return x.Mid
	}
	return ""
}

func (x *RvcInfo) GetCascadedUseRvc() bool {
	if x != nil {
		return x.CascadedUseRvc
	}
	return false
}

func (x *RvcInfo) GetF0Method() string {
	if x != nil {
		return x.F0Method
	}
	return ""
}

func (x *RvcInfo) GetRmsMixRate() float32 {
	if x != nil {
		return x.RmsMixRate
	}
	return 0
}

func (x *RvcInfo) GetProtect() float32 {
	if x != nil {
		return x.Protect
	}
	return 0
}

func (x *RvcInfo) GetDiffusionSteps() float32 {
	if x != nil {
		return x.DiffusionSteps
	}
	return 0
}

func (x *RvcInfo) GetLengthAdjust() float32 {
	if x != nil {
		return x.LengthAdjust
	}
	return 0
}

func (x *RvcInfo) GetInferenceCfgRate() float32 {
	if x != nil {
		return x.InferenceCfgRate
	}
	return 0
}

func (x *RvcInfo) GetSampleRate() int64 {
	if x != nil {
		return x.SampleRate
	}
	return 0
}

func (x *RvcInfo) GetBitDepth() int32 {
	if x != nil {
		return x.BitDepth
	}
	return 0
}

func (x *RvcInfo) GetChannels() int32 {
	if x != nil {
		return x.Channels
	}
	return 0
}

func (x *RvcInfo) GetOutputSampleRate() int64 {
	if x != nil {
		return x.OutputSampleRate
	}
	return 0
}

func (x *RvcInfo) GetOutputBitDepth() int32 {
	if x != nil {
		return x.OutputBitDepth
	}
	return 0
}

func (x *RvcInfo) GetOutputChannels() int32 {
	if x != nil {
		return x.OutputChannels
	}
	return 0
}

func (x *RvcInfo) GetAudioMilliDuration() int32 {
	if x != nil {
		return x.AudioMilliDuration
	}
	return 0
}

func (x *RvcInfo) GetChunkMs() int32 {
	if x != nil {
		return x.ChunkMs
	}
	return 0
}

type GetAsrAddrReq struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *GetAsrAddrReq) Reset() {
	*x = GetAsrAddrReq{}
	mi := &file_bizmisc_proto_msgTypes[10]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GetAsrAddrReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetAsrAddrReq) ProtoMessage() {}

func (x *GetAsrAddrReq) ProtoReflect() protoreflect.Message {
	mi := &file_bizmisc_proto_msgTypes[10]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetAsrAddrReq.ProtoReflect.Descriptor instead.
func (*GetAsrAddrReq) Descriptor() ([]byte, []int) {
	return file_bizmisc_proto_rawDescGZIP(), []int{10}
}

type GetAsrAddrResp struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Code          int32                  `protobuf:"varint,1,opt,name=code,proto3" json:"code,omitempty"`
	Msg           string                 `protobuf:"bytes,2,opt,name=msg,proto3" json:"msg,omitempty"`
	Data          *GetAsrAddrRespData    `protobuf:"bytes,3,opt,name=data,proto3" json:"data,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *GetAsrAddrResp) Reset() {
	*x = GetAsrAddrResp{}
	mi := &file_bizmisc_proto_msgTypes[11]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GetAsrAddrResp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetAsrAddrResp) ProtoMessage() {}

func (x *GetAsrAddrResp) ProtoReflect() protoreflect.Message {
	mi := &file_bizmisc_proto_msgTypes[11]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetAsrAddrResp.ProtoReflect.Descriptor instead.
func (*GetAsrAddrResp) Descriptor() ([]byte, []int) {
	return file_bizmisc_proto_rawDescGZIP(), []int{11}
}

func (x *GetAsrAddrResp) GetCode() int32 {
	if x != nil {
		return x.Code
	}
	return 0
}

func (x *GetAsrAddrResp) GetMsg() string {
	if x != nil {
		return x.Msg
	}
	return ""
}

func (x *GetAsrAddrResp) GetData() *GetAsrAddrRespData {
	if x != nil {
		return x.Data
	}
	return nil
}

type GetAsrAddrRespData struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Addr          string                 `protobuf:"bytes,1,opt,name=addr,proto3" json:"addr,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *GetAsrAddrRespData) Reset() {
	*x = GetAsrAddrRespData{}
	mi := &file_bizmisc_proto_msgTypes[12]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GetAsrAddrRespData) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetAsrAddrRespData) ProtoMessage() {}

func (x *GetAsrAddrRespData) ProtoReflect() protoreflect.Message {
	mi := &file_bizmisc_proto_msgTypes[12]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetAsrAddrRespData.ProtoReflect.Descriptor instead.
func (*GetAsrAddrRespData) Descriptor() ([]byte, []int) {
	return file_bizmisc_proto_rawDescGZIP(), []int{12}
}

func (x *GetAsrAddrRespData) GetAddr() string {
	if x != nil {
		return x.Addr
	}
	return ""
}

type GetVcPushAddrReq struct {
	state             protoimpl.MessageState `protogen:"open.v1"`
	CharacterId       int64                  `protobuf:"varint,1,opt,name=character_id,json=characterId,proto3" json:"character_id,omitempty"`
	CharacterAssetsId int64                  `protobuf:"varint,2,opt,name=character_assets_id,json=characterAssetsId,proto3" json:"character_assets_id,omitempty"`
	Scene             string                 `protobuf:"bytes,3,opt,name=scene,proto3" json:"scene,omitempty"` // 场景, comment, dubbing, signature
	unknownFields     protoimpl.UnknownFields
	sizeCache         protoimpl.SizeCache
}

func (x *GetVcPushAddrReq) Reset() {
	*x = GetVcPushAddrReq{}
	mi := &file_bizmisc_proto_msgTypes[13]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GetVcPushAddrReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetVcPushAddrReq) ProtoMessage() {}

func (x *GetVcPushAddrReq) ProtoReflect() protoreflect.Message {
	mi := &file_bizmisc_proto_msgTypes[13]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetVcPushAddrReq.ProtoReflect.Descriptor instead.
func (*GetVcPushAddrReq) Descriptor() ([]byte, []int) {
	return file_bizmisc_proto_rawDescGZIP(), []int{13}
}

func (x *GetVcPushAddrReq) GetCharacterId() int64 {
	if x != nil {
		return x.CharacterId
	}
	return 0
}

func (x *GetVcPushAddrReq) GetCharacterAssetsId() int64 {
	if x != nil {
		return x.CharacterAssetsId
	}
	return 0
}

func (x *GetVcPushAddrReq) GetScene() string {
	if x != nil {
		return x.Scene
	}
	return ""
}

type GetVcPushAddrResp struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Code          int32                  `protobuf:"varint,1,opt,name=code,proto3" json:"code,omitempty"`
	Msg           string                 `protobuf:"bytes,2,opt,name=msg,proto3" json:"msg,omitempty"`
	Data          *GetVcPushAddrRespData `protobuf:"bytes,3,opt,name=data,proto3" json:"data,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *GetVcPushAddrResp) Reset() {
	*x = GetVcPushAddrResp{}
	mi := &file_bizmisc_proto_msgTypes[14]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GetVcPushAddrResp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetVcPushAddrResp) ProtoMessage() {}

func (x *GetVcPushAddrResp) ProtoReflect() protoreflect.Message {
	mi := &file_bizmisc_proto_msgTypes[14]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetVcPushAddrResp.ProtoReflect.Descriptor instead.
func (*GetVcPushAddrResp) Descriptor() ([]byte, []int) {
	return file_bizmisc_proto_rawDescGZIP(), []int{14}
}

func (x *GetVcPushAddrResp) GetCode() int32 {
	if x != nil {
		return x.Code
	}
	return 0
}

func (x *GetVcPushAddrResp) GetMsg() string {
	if x != nil {
		return x.Msg
	}
	return ""
}

func (x *GetVcPushAddrResp) GetData() *GetVcPushAddrRespData {
	if x != nil {
		return x.Data
	}
	return nil
}

type GetVcPushAddrRespData struct {
	state  protoimpl.MessageState `protogen:"open.v1"`
	WsAddr string                 `protobuf:"bytes,1,opt,name=ws_addr,json=wsAddr,proto3" json:"ws_addr,omitempty"`
	// rvc配置信息
	Rvc           *RvcInfo `protobuf:"bytes,2,opt,name=rvc,proto3" json:"rvc,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *GetVcPushAddrRespData) Reset() {
	*x = GetVcPushAddrRespData{}
	mi := &file_bizmisc_proto_msgTypes[15]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GetVcPushAddrRespData) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetVcPushAddrRespData) ProtoMessage() {}

func (x *GetVcPushAddrRespData) ProtoReflect() protoreflect.Message {
	mi := &file_bizmisc_proto_msgTypes[15]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetVcPushAddrRespData.ProtoReflect.Descriptor instead.
func (*GetVcPushAddrRespData) Descriptor() ([]byte, []int) {
	return file_bizmisc_proto_rawDescGZIP(), []int{15}
}

func (x *GetVcPushAddrRespData) GetWsAddr() string {
	if x != nil {
		return x.WsAddr
	}
	return ""
}

func (x *GetVcPushAddrRespData) GetRvc() *RvcInfo {
	if x != nil {
		return x.Rvc
	}
	return nil
}

type ScriptInfo struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	ScriptId      int64                  `protobuf:"varint,1,opt,name=script_id,json=scriptId,proto3" json:"script_id,omitempty"`
	Title         string                 `protobuf:"bytes,2,opt,name=title,proto3" json:"title,omitempty"`
	Cover         string                 `protobuf:"bytes,3,opt,name=cover,proto3" json:"cover,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *ScriptInfo) Reset() {
	*x = ScriptInfo{}
	mi := &file_bizmisc_proto_msgTypes[16]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ScriptInfo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ScriptInfo) ProtoMessage() {}

func (x *ScriptInfo) ProtoReflect() protoreflect.Message {
	mi := &file_bizmisc_proto_msgTypes[16]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ScriptInfo.ProtoReflect.Descriptor instead.
func (*ScriptInfo) Descriptor() ([]byte, []int) {
	return file_bizmisc_proto_rawDescGZIP(), []int{16}
}

func (x *ScriptInfo) GetScriptId() int64 {
	if x != nil {
		return x.ScriptId
	}
	return 0
}

func (x *ScriptInfo) GetTitle() string {
	if x != nil {
		return x.Title
	}
	return ""
}

func (x *ScriptInfo) GetCover() string {
	if x != nil {
		return x.Cover
	}
	return ""
}

type ScriptDetail struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *ScriptDetail) Reset() {
	*x = ScriptDetail{}
	mi := &file_bizmisc_proto_msgTypes[17]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ScriptDetail) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ScriptDetail) ProtoMessage() {}

func (x *ScriptDetail) ProtoReflect() protoreflect.Message {
	mi := &file_bizmisc_proto_msgTypes[17]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ScriptDetail.ProtoReflect.Descriptor instead.
func (*ScriptDetail) Descriptor() ([]byte, []int) {
	return file_bizmisc_proto_rawDescGZIP(), []int{17}
}

type Comment struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	CommentId     int64                  `protobuf:"varint,1,opt,name=comment_id,json=commentId,proto3" json:"comment_id,omitempty"`
	Text          string                 `protobuf:"bytes,2,opt,name=text,proto3" json:"text,omitempty"`
	AudioUrl      string                 `protobuf:"bytes,3,opt,name=audio_url,json=audioUrl,proto3" json:"audio_url,omitempty"`
	Duration      int64                  `protobuf:"varint,4,opt,name=duration,proto3" json:"duration,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *Comment) Reset() {
	*x = Comment{}
	mi := &file_bizmisc_proto_msgTypes[18]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *Comment) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Comment) ProtoMessage() {}

func (x *Comment) ProtoReflect() protoreflect.Message {
	mi := &file_bizmisc_proto_msgTypes[18]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Comment.ProtoReflect.Descriptor instead.
func (*Comment) Descriptor() ([]byte, []int) {
	return file_bizmisc_proto_rawDescGZIP(), []int{18}
}

func (x *Comment) GetCommentId() int64 {
	if x != nil {
		return x.CommentId
	}
	return 0
}

func (x *Comment) GetText() string {
	if x != nil {
		return x.Text
	}
	return ""
}

func (x *Comment) GetAudioUrl() string {
	if x != nil {
		return x.AudioUrl
	}
	return ""
}

func (x *Comment) GetDuration() int64 {
	if x != nil {
		return x.Duration
	}
	return 0
}

type CommentReply struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	CommentId     int64                  `protobuf:"varint,1,opt,name=comment_id,json=commentId,proto3" json:"comment_id,omitempty"`
	Text          string                 `protobuf:"bytes,2,opt,name=text,proto3" json:"text,omitempty"`
	AudioUrl      string                 `protobuf:"bytes,3,opt,name=audio_url,json=audioUrl,proto3" json:"audio_url,omitempty"`
	Duration      int64                  `protobuf:"varint,4,opt,name=duration,proto3" json:"duration,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *CommentReply) Reset() {
	*x = CommentReply{}
	mi := &file_bizmisc_proto_msgTypes[19]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *CommentReply) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CommentReply) ProtoMessage() {}

func (x *CommentReply) ProtoReflect() protoreflect.Message {
	mi := &file_bizmisc_proto_msgTypes[19]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CommentReply.ProtoReflect.Descriptor instead.
func (*CommentReply) Descriptor() ([]byte, []int) {
	return file_bizmisc_proto_rawDescGZIP(), []int{19}
}

func (x *CommentReply) GetCommentId() int64 {
	if x != nil {
		return x.CommentId
	}
	return 0
}

func (x *CommentReply) GetText() string {
	if x != nil {
		return x.Text
	}
	return ""
}

func (x *CommentReply) GetAudioUrl() string {
	if x != nil {
		return x.AudioUrl
	}
	return ""
}

func (x *CommentReply) GetDuration() int64 {
	if x != nil {
		return x.Duration
	}
	return 0
}

type Dubbing struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	DubbingId     int64                  `protobuf:"varint,1,opt,name=dubbing_id,json=dubbingId,proto3" json:"dubbing_id,omitempty"`
	AudioUrl      string                 `protobuf:"bytes,2,opt,name=audio_url,json=audioUrl,proto3" json:"audio_url,omitempty"`
	Duration      int64                  `protobuf:"varint,3,opt,name=duration,proto3" json:"duration,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *Dubbing) Reset() {
	*x = Dubbing{}
	mi := &file_bizmisc_proto_msgTypes[20]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *Dubbing) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Dubbing) ProtoMessage() {}

func (x *Dubbing) ProtoReflect() protoreflect.Message {
	mi := &file_bizmisc_proto_msgTypes[20]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Dubbing.ProtoReflect.Descriptor instead.
func (*Dubbing) Descriptor() ([]byte, []int) {
	return file_bizmisc_proto_rawDescGZIP(), []int{20}
}

func (x *Dubbing) GetDubbingId() int64 {
	if x != nil {
		return x.DubbingId
	}
	return 0
}

func (x *Dubbing) GetAudioUrl() string {
	if x != nil {
		return x.AudioUrl
	}
	return ""
}

func (x *Dubbing) GetDuration() int64 {
	if x != nil {
		return x.Duration
	}
	return 0
}

type SendSecretaryMsgReq struct {
	state         protoimpl.MessageState           `protogen:"open.v1"`
	ToUid         int64                            `protobuf:"varint,1,opt,name=to_uid,json=toUid,proto3" json:"to_uid,omitempty"`
	ContentType   int32                            `protobuf:"varint,2,opt,name=content_type,json=contentType,proto3" json:"content_type,omitempty"`
	ContentImage  string                           `protobuf:"bytes,3,opt,name=content_image,json=contentImage,proto3" json:"content_image,omitempty"`
	Tpl           string                           `protobuf:"bytes,4,opt,name=tpl,proto3" json:"tpl,omitempty"`
	TplData       map[string]*svcchat.TemplateItem `protobuf:"bytes,5,rep,name=tpl_data,json=tplData,proto3" json:"tpl_data,omitempty" protobuf_key:"bytes,1,opt,name=key" protobuf_val:"bytes,2,opt,name=value"`
	Member        *svcchat.SimpleMember            `protobuf:"bytes,6,opt,name=member,proto3" json:"member,omitempty"`
	Script        *ScriptInfo                      `protobuf:"bytes,7,opt,name=script,proto3" json:"script,omitempty"`
	Comment       *Comment                         `protobuf:"bytes,8,opt,name=comment,proto3" json:"comment,omitempty"`
	CommentReply  *CommentReply                    `protobuf:"bytes,9,opt,name=comment_reply,json=commentReply,proto3" json:"comment_reply,omitempty"`
	Dubbing       *Dubbing                         `protobuf:"bytes,10,opt,name=dubbing,proto3" json:"dubbing,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *SendSecretaryMsgReq) Reset() {
	*x = SendSecretaryMsgReq{}
	mi := &file_bizmisc_proto_msgTypes[21]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *SendSecretaryMsgReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SendSecretaryMsgReq) ProtoMessage() {}

func (x *SendSecretaryMsgReq) ProtoReflect() protoreflect.Message {
	mi := &file_bizmisc_proto_msgTypes[21]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SendSecretaryMsgReq.ProtoReflect.Descriptor instead.
func (*SendSecretaryMsgReq) Descriptor() ([]byte, []int) {
	return file_bizmisc_proto_rawDescGZIP(), []int{21}
}

func (x *SendSecretaryMsgReq) GetToUid() int64 {
	if x != nil {
		return x.ToUid
	}
	return 0
}

func (x *SendSecretaryMsgReq) GetContentType() int32 {
	if x != nil {
		return x.ContentType
	}
	return 0
}

func (x *SendSecretaryMsgReq) GetContentImage() string {
	if x != nil {
		return x.ContentImage
	}
	return ""
}

func (x *SendSecretaryMsgReq) GetTpl() string {
	if x != nil {
		return x.Tpl
	}
	return ""
}

func (x *SendSecretaryMsgReq) GetTplData() map[string]*svcchat.TemplateItem {
	if x != nil {
		return x.TplData
	}
	return nil
}

func (x *SendSecretaryMsgReq) GetMember() *svcchat.SimpleMember {
	if x != nil {
		return x.Member
	}
	return nil
}

func (x *SendSecretaryMsgReq) GetScript() *ScriptInfo {
	if x != nil {
		return x.Script
	}
	return nil
}

func (x *SendSecretaryMsgReq) GetComment() *Comment {
	if x != nil {
		return x.Comment
	}
	return nil
}

func (x *SendSecretaryMsgReq) GetCommentReply() *CommentReply {
	if x != nil {
		return x.CommentReply
	}
	return nil
}

func (x *SendSecretaryMsgReq) GetDubbing() *Dubbing {
	if x != nil {
		return x.Dubbing
	}
	return nil
}

type SendSecretaryMsgResp struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Code          int32                  `protobuf:"varint,1,opt,name=code,proto3" json:"code,omitempty"`
	Msg           string                 `protobuf:"bytes,2,opt,name=msg,proto3" json:"msg,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *SendSecretaryMsgResp) Reset() {
	*x = SendSecretaryMsgResp{}
	mi := &file_bizmisc_proto_msgTypes[22]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *SendSecretaryMsgResp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SendSecretaryMsgResp) ProtoMessage() {}

func (x *SendSecretaryMsgResp) ProtoReflect() protoreflect.Message {
	mi := &file_bizmisc_proto_msgTypes[22]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SendSecretaryMsgResp.ProtoReflect.Descriptor instead.
func (*SendSecretaryMsgResp) Descriptor() ([]byte, []int) {
	return file_bizmisc_proto_rawDescGZIP(), []int{22}
}

func (x *SendSecretaryMsgResp) GetCode() int32 {
	if x != nil {
		return x.Code
	}
	return 0
}

func (x *SendSecretaryMsgResp) GetMsg() string {
	if x != nil {
		return x.Msg
	}
	return ""
}

type TestSendSystemMsgReq struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	ToUid         int64                  `protobuf:"varint,1,opt,name=to_uid,json=toUid,proto3" json:"to_uid,omitempty"`
	ContentType   int32                  `protobuf:"varint,2,opt,name=content_type,json=contentType,proto3" json:"content_type,omitempty"`
	SystemMsg     *SystemMsg             `protobuf:"bytes,3,opt,name=system_msg,json=systemMsg,proto3" json:"system_msg,omitempty"`
	FollowMsg     *FollowMsg             `protobuf:"bytes,4,opt,name=follow_msg,json=followMsg,proto3" json:"follow_msg,omitempty"`
	CommentMsg    *CommentMsg            `protobuf:"bytes,5,opt,name=comment_msg,json=commentMsg,proto3" json:"comment_msg,omitempty"`
	LikeMsg       *LikeMsg               `protobuf:"bytes,6,opt,name=like_msg,json=likeMsg,proto3" json:"like_msg,omitempty"`
	DubbingMsg    *DubbingMsg            `protobuf:"bytes,7,opt,name=dubbing_msg,json=dubbingMsg,proto3" json:"dubbing_msg,omitempty"`
	ReviewMsg     *ReviewMsg             `protobuf:"bytes,8,opt,name=review_msg,json=reviewMsg,proto3" json:"review_msg,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *TestSendSystemMsgReq) Reset() {
	*x = TestSendSystemMsgReq{}
	mi := &file_bizmisc_proto_msgTypes[23]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *TestSendSystemMsgReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*TestSendSystemMsgReq) ProtoMessage() {}

func (x *TestSendSystemMsgReq) ProtoReflect() protoreflect.Message {
	mi := &file_bizmisc_proto_msgTypes[23]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use TestSendSystemMsgReq.ProtoReflect.Descriptor instead.
func (*TestSendSystemMsgReq) Descriptor() ([]byte, []int) {
	return file_bizmisc_proto_rawDescGZIP(), []int{23}
}

func (x *TestSendSystemMsgReq) GetToUid() int64 {
	if x != nil {
		return x.ToUid
	}
	return 0
}

func (x *TestSendSystemMsgReq) GetContentType() int32 {
	if x != nil {
		return x.ContentType
	}
	return 0
}

func (x *TestSendSystemMsgReq) GetSystemMsg() *SystemMsg {
	if x != nil {
		return x.SystemMsg
	}
	return nil
}

func (x *TestSendSystemMsgReq) GetFollowMsg() *FollowMsg {
	if x != nil {
		return x.FollowMsg
	}
	return nil
}

func (x *TestSendSystemMsgReq) GetCommentMsg() *CommentMsg {
	if x != nil {
		return x.CommentMsg
	}
	return nil
}

func (x *TestSendSystemMsgReq) GetLikeMsg() *LikeMsg {
	if x != nil {
		return x.LikeMsg
	}
	return nil
}

func (x *TestSendSystemMsgReq) GetDubbingMsg() *DubbingMsg {
	if x != nil {
		return x.DubbingMsg
	}
	return nil
}

func (x *TestSendSystemMsgReq) GetReviewMsg() *ReviewMsg {
	if x != nil {
		return x.ReviewMsg
	}
	return nil
}

type TestSendSystemMsgResp struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Code          int32                  `protobuf:"varint,1,opt,name=code,proto3" json:"code,omitempty"`
	Msg           string                 `protobuf:"bytes,2,opt,name=msg,proto3" json:"msg,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *TestSendSystemMsgResp) Reset() {
	*x = TestSendSystemMsgResp{}
	mi := &file_bizmisc_proto_msgTypes[24]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *TestSendSystemMsgResp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*TestSendSystemMsgResp) ProtoMessage() {}

func (x *TestSendSystemMsgResp) ProtoReflect() protoreflect.Message {
	mi := &file_bizmisc_proto_msgTypes[24]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use TestSendSystemMsgResp.ProtoReflect.Descriptor instead.
func (*TestSendSystemMsgResp) Descriptor() ([]byte, []int) {
	return file_bizmisc_proto_rawDescGZIP(), []int{24}
}

func (x *TestSendSystemMsgResp) GetCode() int32 {
	if x != nil {
		return x.Code
	}
	return 0
}

func (x *TestSendSystemMsgResp) GetMsg() string {
	if x != nil {
		return x.Msg
	}
	return ""
}

type SystemMsg struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Title         *svcchat.TemplateMsg   `protobuf:"bytes,1,opt,name=title,proto3" json:"title,omitempty"`
	BgmUrl        string                 `protobuf:"bytes,2,opt,name=bgm_url,json=bgmUrl,proto3" json:"bgm_url,omitempty"`
	JumpUrl       string                 `protobuf:"bytes,3,opt,name=jump_url,json=jumpUrl,proto3" json:"jump_url,omitempty"`
	Content       *svcchat.TemplateMsg   `protobuf:"bytes,4,opt,name=content,proto3" json:"content,omitempty"`
	Time          int64                  `protobuf:"varint,5,opt,name=time,proto3" json:"time,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *SystemMsg) Reset() {
	*x = SystemMsg{}
	mi := &file_bizmisc_proto_msgTypes[25]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *SystemMsg) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SystemMsg) ProtoMessage() {}

func (x *SystemMsg) ProtoReflect() protoreflect.Message {
	mi := &file_bizmisc_proto_msgTypes[25]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SystemMsg.ProtoReflect.Descriptor instead.
func (*SystemMsg) Descriptor() ([]byte, []int) {
	return file_bizmisc_proto_rawDescGZIP(), []int{25}
}

func (x *SystemMsg) GetTitle() *svcchat.TemplateMsg {
	if x != nil {
		return x.Title
	}
	return nil
}

func (x *SystemMsg) GetBgmUrl() string {
	if x != nil {
		return x.BgmUrl
	}
	return ""
}

func (x *SystemMsg) GetJumpUrl() string {
	if x != nil {
		return x.JumpUrl
	}
	return ""
}

func (x *SystemMsg) GetContent() *svcchat.TemplateMsg {
	if x != nil {
		return x.Content
	}
	return nil
}

func (x *SystemMsg) GetTime() int64 {
	if x != nil {
		return x.Time
	}
	return 0
}

type FollowMsg struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Content       *svcchat.TemplateMsg   `protobuf:"bytes,1,opt,name=content,proto3" json:"content,omitempty"`
	Member        *svcchat.SimpleMember  `protobuf:"bytes,2,opt,name=member,proto3" json:"member,omitempty"`
	Time          int64                  `protobuf:"varint,3,opt,name=time,proto3" json:"time,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *FollowMsg) Reset() {
	*x = FollowMsg{}
	mi := &file_bizmisc_proto_msgTypes[26]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *FollowMsg) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*FollowMsg) ProtoMessage() {}

func (x *FollowMsg) ProtoReflect() protoreflect.Message {
	mi := &file_bizmisc_proto_msgTypes[26]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use FollowMsg.ProtoReflect.Descriptor instead.
func (*FollowMsg) Descriptor() ([]byte, []int) {
	return file_bizmisc_proto_rawDescGZIP(), []int{26}
}

func (x *FollowMsg) GetContent() *svcchat.TemplateMsg {
	if x != nil {
		return x.Content
	}
	return nil
}

func (x *FollowMsg) GetMember() *svcchat.SimpleMember {
	if x != nil {
		return x.Member
	}
	return nil
}

func (x *FollowMsg) GetTime() int64 {
	if x != nil {
		return x.Time
	}
	return 0
}

type CommentMsg struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Content       *svcchat.TemplateMsg   `protobuf:"bytes,1,opt,name=content,proto3" json:"content,omitempty"`
	Member        *svcchat.SimpleMember  `protobuf:"bytes,2,opt,name=member,proto3" json:"member,omitempty"`
	Comment       *Comment               `protobuf:"bytes,3,opt,name=comment,proto3" json:"comment,omitempty"`
	CommentReply  *CommentReply          `protobuf:"bytes,4,opt,name=comment_reply,json=commentReply,proto3" json:"comment_reply,omitempty"`
	Script        *ScriptInfo            `protobuf:"bytes,5,opt,name=script,proto3" json:"script,omitempty"`
	Time          int64                  `protobuf:"varint,6,opt,name=time,proto3" json:"time,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *CommentMsg) Reset() {
	*x = CommentMsg{}
	mi := &file_bizmisc_proto_msgTypes[27]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *CommentMsg) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CommentMsg) ProtoMessage() {}

func (x *CommentMsg) ProtoReflect() protoreflect.Message {
	mi := &file_bizmisc_proto_msgTypes[27]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CommentMsg.ProtoReflect.Descriptor instead.
func (*CommentMsg) Descriptor() ([]byte, []int) {
	return file_bizmisc_proto_rawDescGZIP(), []int{27}
}

func (x *CommentMsg) GetContent() *svcchat.TemplateMsg {
	if x != nil {
		return x.Content
	}
	return nil
}

func (x *CommentMsg) GetMember() *svcchat.SimpleMember {
	if x != nil {
		return x.Member
	}
	return nil
}

func (x *CommentMsg) GetComment() *Comment {
	if x != nil {
		return x.Comment
	}
	return nil
}

func (x *CommentMsg) GetCommentReply() *CommentReply {
	if x != nil {
		return x.CommentReply
	}
	return nil
}

func (x *CommentMsg) GetScript() *ScriptInfo {
	if x != nil {
		return x.Script
	}
	return nil
}

func (x *CommentMsg) GetTime() int64 {
	if x != nil {
		return x.Time
	}
	return 0
}

type LikeMsg struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Content       *svcchat.TemplateMsg   `protobuf:"bytes,1,opt,name=content,proto3" json:"content,omitempty"`
	Member        *svcchat.SimpleMember  `protobuf:"bytes,2,opt,name=member,proto3" json:"member,omitempty"`
	Script        *ScriptInfo            `protobuf:"bytes,3,opt,name=script,proto3" json:"script,omitempty"`
	Comment       *Comment               `protobuf:"bytes,4,opt,name=comment,proto3" json:"comment,omitempty"`
	Dubbing       *Dubbing               `protobuf:"bytes,5,opt,name=dubbing,proto3" json:"dubbing,omitempty"`
	Time          int64                  `protobuf:"varint,6,opt,name=time,proto3" json:"time,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *LikeMsg) Reset() {
	*x = LikeMsg{}
	mi := &file_bizmisc_proto_msgTypes[28]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *LikeMsg) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*LikeMsg) ProtoMessage() {}

func (x *LikeMsg) ProtoReflect() protoreflect.Message {
	mi := &file_bizmisc_proto_msgTypes[28]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use LikeMsg.ProtoReflect.Descriptor instead.
func (*LikeMsg) Descriptor() ([]byte, []int) {
	return file_bizmisc_proto_rawDescGZIP(), []int{28}
}

func (x *LikeMsg) GetContent() *svcchat.TemplateMsg {
	if x != nil {
		return x.Content
	}
	return nil
}

func (x *LikeMsg) GetMember() *svcchat.SimpleMember {
	if x != nil {
		return x.Member
	}
	return nil
}

func (x *LikeMsg) GetScript() *ScriptInfo {
	if x != nil {
		return x.Script
	}
	return nil
}

func (x *LikeMsg) GetComment() *Comment {
	if x != nil {
		return x.Comment
	}
	return nil
}

func (x *LikeMsg) GetDubbing() *Dubbing {
	if x != nil {
		return x.Dubbing
	}
	return nil
}

func (x *LikeMsg) GetTime() int64 {
	if x != nil {
		return x.Time
	}
	return 0
}

type DubbingMsg struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Content       *svcchat.TemplateMsg   `protobuf:"bytes,1,opt,name=content,proto3" json:"content,omitempty"`
	Member        *svcchat.SimpleMember  `protobuf:"bytes,2,opt,name=member,proto3" json:"member,omitempty"`
	Script        *ScriptInfo            `protobuf:"bytes,3,opt,name=script,proto3" json:"script,omitempty"`
	Dubbing       *Dubbing               `protobuf:"bytes,5,opt,name=dubbing,proto3" json:"dubbing,omitempty"`
	Time          int64                  `protobuf:"varint,6,opt,name=time,proto3" json:"time,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *DubbingMsg) Reset() {
	*x = DubbingMsg{}
	mi := &file_bizmisc_proto_msgTypes[29]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *DubbingMsg) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DubbingMsg) ProtoMessage() {}

func (x *DubbingMsg) ProtoReflect() protoreflect.Message {
	mi := &file_bizmisc_proto_msgTypes[29]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DubbingMsg.ProtoReflect.Descriptor instead.
func (*DubbingMsg) Descriptor() ([]byte, []int) {
	return file_bizmisc_proto_rawDescGZIP(), []int{29}
}

func (x *DubbingMsg) GetContent() *svcchat.TemplateMsg {
	if x != nil {
		return x.Content
	}
	return nil
}

func (x *DubbingMsg) GetMember() *svcchat.SimpleMember {
	if x != nil {
		return x.Member
	}
	return nil
}

func (x *DubbingMsg) GetScript() *ScriptInfo {
	if x != nil {
		return x.Script
	}
	return nil
}

func (x *DubbingMsg) GetDubbing() *Dubbing {
	if x != nil {
		return x.Dubbing
	}
	return nil
}

func (x *DubbingMsg) GetTime() int64 {
	if x != nil {
		return x.Time
	}
	return 0
}

type ReviewMsg struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Title         *svcchat.TemplateMsg   `protobuf:"bytes,1,opt,name=title,proto3" json:"title,omitempty"`
	BgmUrl        string                 `protobuf:"bytes,2,opt,name=bgm_url,json=bgmUrl,proto3" json:"bgm_url,omitempty"`
	JumpUrl       string                 `protobuf:"bytes,3,opt,name=jump_url,json=jumpUrl,proto3" json:"jump_url,omitempty"`
	Content       *svcchat.TemplateMsg   `protobuf:"bytes,4,opt,name=content,proto3" json:"content,omitempty"`
	Script        *svcscript.Script      `protobuf:"bytes,5,opt,name=script,proto3" json:"script,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *ReviewMsg) Reset() {
	*x = ReviewMsg{}
	mi := &file_bizmisc_proto_msgTypes[30]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ReviewMsg) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ReviewMsg) ProtoMessage() {}

func (x *ReviewMsg) ProtoReflect() protoreflect.Message {
	mi := &file_bizmisc_proto_msgTypes[30]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ReviewMsg.ProtoReflect.Descriptor instead.
func (*ReviewMsg) Descriptor() ([]byte, []int) {
	return file_bizmisc_proto_rawDescGZIP(), []int{30}
}

func (x *ReviewMsg) GetTitle() *svcchat.TemplateMsg {
	if x != nil {
		return x.Title
	}
	return nil
}

func (x *ReviewMsg) GetBgmUrl() string {
	if x != nil {
		return x.BgmUrl
	}
	return ""
}

func (x *ReviewMsg) GetJumpUrl() string {
	if x != nil {
		return x.JumpUrl
	}
	return ""
}

func (x *ReviewMsg) GetContent() *svcchat.TemplateMsg {
	if x != nil {
		return x.Content
	}
	return nil
}

func (x *ReviewMsg) GetScript() *svcscript.Script {
	if x != nil {
		return x.Script
	}
	return nil
}

// 检查版本请求
type CheckVersionReq struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *CheckVersionReq) Reset() {
	*x = CheckVersionReq{}
	mi := &file_bizmisc_proto_msgTypes[31]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *CheckVersionReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CheckVersionReq) ProtoMessage() {}

func (x *CheckVersionReq) ProtoReflect() protoreflect.Message {
	mi := &file_bizmisc_proto_msgTypes[31]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CheckVersionReq.ProtoReflect.Descriptor instead.
func (*CheckVersionReq) Descriptor() ([]byte, []int) {
	return file_bizmisc_proto_rawDescGZIP(), []int{31}
}

// 检查版本响应
type CheckVersionResp struct {
	state         protoimpl.MessageState      `protogen:"open.v1"`
	Code          int32                       `protobuf:"varint,1,opt,name=code,proto3" json:"code,omitempty"`
	Msg           string                      `protobuf:"bytes,2,opt,name=msg,proto3" json:"msg,omitempty"`
	Data          *svcconfig.CheckVersionData `protobuf:"bytes,3,opt,name=data,proto3" json:"data,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *CheckVersionResp) Reset() {
	*x = CheckVersionResp{}
	mi := &file_bizmisc_proto_msgTypes[32]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *CheckVersionResp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CheckVersionResp) ProtoMessage() {}

func (x *CheckVersionResp) ProtoReflect() protoreflect.Message {
	mi := &file_bizmisc_proto_msgTypes[32]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CheckVersionResp.ProtoReflect.Descriptor instead.
func (*CheckVersionResp) Descriptor() ([]byte, []int) {
	return file_bizmisc_proto_rawDescGZIP(), []int{32}
}

func (x *CheckVersionResp) GetCode() int32 {
	if x != nil {
		return x.Code
	}
	return 0
}

func (x *CheckVersionResp) GetMsg() string {
	if x != nil {
		return x.Msg
	}
	return ""
}

func (x *CheckVersionResp) GetData() *svcconfig.CheckVersionData {
	if x != nil {
		return x.Data
	}
	return nil
}

type GetApkUrlReq struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *GetApkUrlReq) Reset() {
	*x = GetApkUrlReq{}
	mi := &file_bizmisc_proto_msgTypes[33]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GetApkUrlReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetApkUrlReq) ProtoMessage() {}

func (x *GetApkUrlReq) ProtoReflect() protoreflect.Message {
	mi := &file_bizmisc_proto_msgTypes[33]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetApkUrlReq.ProtoReflect.Descriptor instead.
func (*GetApkUrlReq) Descriptor() ([]byte, []int) {
	return file_bizmisc_proto_rawDescGZIP(), []int{33}
}

type GetApkUrlResp struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Code          int32                  `protobuf:"varint,1,opt,name=code,proto3" json:"code,omitempty"`
	Msg           string                 `protobuf:"bytes,2,opt,name=msg,proto3" json:"msg,omitempty"`
	Data          *GetApkUrlRespData     `protobuf:"bytes,3,opt,name=data,proto3" json:"data,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *GetApkUrlResp) Reset() {
	*x = GetApkUrlResp{}
	mi := &file_bizmisc_proto_msgTypes[34]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GetApkUrlResp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetApkUrlResp) ProtoMessage() {}

func (x *GetApkUrlResp) ProtoReflect() protoreflect.Message {
	mi := &file_bizmisc_proto_msgTypes[34]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetApkUrlResp.ProtoReflect.Descriptor instead.
func (*GetApkUrlResp) Descriptor() ([]byte, []int) {
	return file_bizmisc_proto_rawDescGZIP(), []int{34}
}

func (x *GetApkUrlResp) GetCode() int32 {
	if x != nil {
		return x.Code
	}
	return 0
}

func (x *GetApkUrlResp) GetMsg() string {
	if x != nil {
		return x.Msg
	}
	return ""
}

func (x *GetApkUrlResp) GetData() *GetApkUrlRespData {
	if x != nil {
		return x.Data
	}
	return nil
}

type GetApkUrlRespData struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Url           string                 `protobuf:"bytes,1,opt,name=url,proto3" json:"url,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *GetApkUrlRespData) Reset() {
	*x = GetApkUrlRespData{}
	mi := &file_bizmisc_proto_msgTypes[35]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GetApkUrlRespData) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetApkUrlRespData) ProtoMessage() {}

func (x *GetApkUrlRespData) ProtoReflect() protoreflect.Message {
	mi := &file_bizmisc_proto_msgTypes[35]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetApkUrlRespData.ProtoReflect.Descriptor instead.
func (*GetApkUrlRespData) Descriptor() ([]byte, []int) {
	return file_bizmisc_proto_rawDescGZIP(), []int{35}
}

func (x *GetApkUrlRespData) GetUrl() string {
	if x != nil {
		return x.Url
	}
	return ""
}

var File_bizmisc_proto protoreflect.FileDescriptor

const file_bizmisc_proto_rawDesc = "" +
	"\n" +
	"\rbizmisc.proto\x12\n" +
	"vc.bizmisc\x1a\x1cgoogle/api/annotations.proto\x1a+protoc-gen-validate/validate/validate.proto\x1a\x1fgoogle/api/field_behavior.proto\x1a\x15svcchat/svcchat.proto\x1a\x19svcscript/svcscript.proto\x1a\x19svcconfig/svcconfig.proto\"r\n" +
	"\x0eAliObjectIdReq\x128\n" +
	"\x04type\x18\x01 \x01(\tB$\xe2A\x01\x02\xfaB\x1dr\x1bR\x05imageR\x05audioR\x05videoR\x04dataR\x04type\x12&\n" +
	"\bbiz_type\x18\x02 \x01(\tB\v\xe2A\x01\x02\xfaB\x04r\x02 \x01R\abizType\"l\n" +
	"\x0fAliObjectIdResp\x12\x12\n" +
	"\x04code\x18\x01 \x01(\x05R\x04code\x12\x10\n" +
	"\x03msg\x18\x02 \x01(\tR\x03msg\x123\n" +
	"\x04data\x18\x03 \x01(\v2\x1f.vc.bizmisc.AliObjectIdRespDataR\x04data\"\x85\x01\n" +
	"\x13AliObjectIdRespData\x12\x1a\n" +
	"\bendpoint\x18\x01 \x01(\tR\bendpoint\x12\x16\n" +
	"\x06bucket\x18\x02 \x01(\tR\x06bucket\x12\x1d\n" +
	"\n" +
	"object_key\x18\x03 \x01(\tR\tobjectKey\x12\x1b\n" +
	"\tobject_id\x18\x04 \x01(\tR\bobjectId\"t\n" +
	"\x11AliOssSigatureReq\x128\n" +
	"\x04type\x18\x01 \x01(\tB$\xe2A\x01\x02\xfaB\x1dr\x1bR\x05imageR\x05audioR\x05videoR\x04dataR\x04type\x12%\n" +
	"\acontent\x18\x02 \x01(\tB\v\xe2A\x01\x02\xfaB\x04r\x02 \x01R\acontent\"r\n" +
	"\x12AliOssSigatureResp\x12\x12\n" +
	"\x04code\x18\x01 \x01(\x05R\x04code\x12\x10\n" +
	"\x03msg\x18\x02 \x01(\tR\x03msg\x126\n" +
	"\x04data\x18\x03 \x01(\v2\".vc.bizmisc.AliOssSigatureRespDataR\x04data\"6\n" +
	"\x16AliOssSigatureRespData\x12\x1c\n" +
	"\tsignature\x18\x01 \x01(\tR\tsignature\"\x11\n" +
	"\x0fGetChanTokenReq\"n\n" +
	"\x10GetChanTokenResp\x12\x12\n" +
	"\x04code\x18\x01 \x01(\x05R\x04code\x12\x10\n" +
	"\x03msg\x18\x02 \x01(\tR\x03msg\x124\n" +
	"\x04data\x18\x03 \x01(\v2 .vc.bizmisc.GetChanTokenRespDataR\x04data\"e\n" +
	"\x14GetChanTokenRespData\x12\x15\n" +
	"\x06ws_url\x18\x01 \x01(\tR\x05wsUrl\x12\"\n" +
	"\fgeneralTopic\x18\x02 \x01(\tR\fgeneralTopic\x12\x12\n" +
	"\x04gzip\x18\x03 \x01(\bR\x04gzip\"\xc9\x05\n" +
	"\aRvcInfo\x12\x19\n" +
	"\brvc_name\x18\x01 \x01(\tR\arvcName\x12\x1b\n" +
	"\tseed_name\x18\x02 \x01(\tR\bseedName\x12.\n" +
	"\x13reference_audio_url\x18\x03 \x01(\tR\x11referenceAudioUrl\x12\x1d\n" +
	"\n" +
	"request_id\x18\x04 \x01(\tR\trequestId\x12\x10\n" +
	"\x03mid\x18\x05 \x01(\tR\x03mid\x12(\n" +
	"\x10cascaded_use_rvc\x18\x06 \x01(\bR\x0ecascadedUseRvc\x12\x1b\n" +
	"\tf0_method\x18\a \x01(\tR\bf0Method\x12 \n" +
	"\frms_mix_rate\x18\b \x01(\x02R\n" +
	"rmsMixRate\x12\x18\n" +
	"\aprotect\x18\t \x01(\x02R\aprotect\x12'\n" +
	"\x0fdiffusion_steps\x18\n" +
	" \x01(\x02R\x0ediffusionSteps\x12#\n" +
	"\rlength_adjust\x18\v \x01(\x02R\flengthAdjust\x12,\n" +
	"\x12inference_cfg_rate\x18\f \x01(\x02R\x10inferenceCfgRate\x12\x1f\n" +
	"\vsample_rate\x18\r \x01(\x03R\n" +
	"sampleRate\x12\x1b\n" +
	"\tbit_depth\x18\x0e \x01(\x05R\bbitDepth\x12\x1a\n" +
	"\bchannels\x18\x0f \x01(\x05R\bchannels\x12,\n" +
	"\x12output_sample_rate\x18\x10 \x01(\x03R\x10outputSampleRate\x12(\n" +
	"\x10output_bit_depth\x18\x11 \x01(\x05R\x0eoutputBitDepth\x12'\n" +
	"\x0foutput_channels\x18\x12 \x01(\x05R\x0eoutputChannels\x120\n" +
	"\x14audio_milli_duration\x18\x13 \x01(\x05R\x12audioMilliDuration\x12\x19\n" +
	"\bchunk_ms\x18\x14 \x01(\x05R\achunkMs\"\x0f\n" +
	"\rGetAsrAddrReq\"j\n" +
	"\x0eGetAsrAddrResp\x12\x12\n" +
	"\x04code\x18\x01 \x01(\x05R\x04code\x12\x10\n" +
	"\x03msg\x18\x02 \x01(\tR\x03msg\x122\n" +
	"\x04data\x18\x03 \x01(\v2\x1e.vc.bizmisc.GetAsrAddrRespDataR\x04data\"(\n" +
	"\x12GetAsrAddrRespData\x12\x12\n" +
	"\x04addr\x18\x01 \x01(\tR\x04addr\"\xbd\x01\n" +
	"\x10GetVcPushAddrReq\x12.\n" +
	"\fcharacter_id\x18\x01 \x01(\x03B\v\xe2A\x01\x02\xfaB\x04\"\x02 \x00R\vcharacterId\x12;\n" +
	"\x13character_assets_id\x18\x02 \x01(\x03B\v\xe2A\x01\x02\xfaB\x04\"\x02 \x00R\x11characterAssetsId\x12<\n" +
	"\x05scene\x18\x03 \x01(\tB&\xe2A\x01\x02\xfaB\x1fr\x1dR\acommentR\adubbingR\tsignatureR\x05scene\"p\n" +
	"\x11GetVcPushAddrResp\x12\x12\n" +
	"\x04code\x18\x01 \x01(\x05R\x04code\x12\x10\n" +
	"\x03msg\x18\x02 \x01(\tR\x03msg\x125\n" +
	"\x04data\x18\x03 \x01(\v2!.vc.bizmisc.GetVcPushAddrRespDataR\x04data\"W\n" +
	"\x15GetVcPushAddrRespData\x12\x17\n" +
	"\aws_addr\x18\x01 \x01(\tR\x06wsAddr\x12%\n" +
	"\x03rvc\x18\x02 \x01(\v2\x13.vc.bizmisc.RvcInfoR\x03rvc\"U\n" +
	"\n" +
	"ScriptInfo\x12\x1b\n" +
	"\tscript_id\x18\x01 \x01(\x03R\bscriptId\x12\x14\n" +
	"\x05title\x18\x02 \x01(\tR\x05title\x12\x14\n" +
	"\x05cover\x18\x03 \x01(\tR\x05cover\"\x0e\n" +
	"\fScriptDetail\"u\n" +
	"\aComment\x12\x1d\n" +
	"\n" +
	"comment_id\x18\x01 \x01(\x03R\tcommentId\x12\x12\n" +
	"\x04text\x18\x02 \x01(\tR\x04text\x12\x1b\n" +
	"\taudio_url\x18\x03 \x01(\tR\baudioUrl\x12\x1a\n" +
	"\bduration\x18\x04 \x01(\x03R\bduration\"z\n" +
	"\fCommentReply\x12\x1d\n" +
	"\n" +
	"comment_id\x18\x01 \x01(\x03R\tcommentId\x12\x12\n" +
	"\x04text\x18\x02 \x01(\tR\x04text\x12\x1b\n" +
	"\taudio_url\x18\x03 \x01(\tR\baudioUrl\x12\x1a\n" +
	"\bduration\x18\x04 \x01(\x03R\bduration\"a\n" +
	"\aDubbing\x12\x1d\n" +
	"\n" +
	"dubbing_id\x18\x01 \x01(\x03R\tdubbingId\x12\x1b\n" +
	"\taudio_url\x18\x02 \x01(\tR\baudioUrl\x12\x1a\n" +
	"\bduration\x18\x03 \x01(\x03R\bduration\"\xb6\x04\n" +
	"\x13SendSecretaryMsgReq\x12\x1e\n" +
	"\x06to_uid\x18\x01 \x01(\x03B\a\xfaB\x04\"\x02 \x00R\x05toUid\x12*\n" +
	"\fcontent_type\x18\x02 \x01(\x05B\a\xfaB\x04\x1a\x02 \x00R\vcontentType\x12#\n" +
	"\rcontent_image\x18\x03 \x01(\tR\fcontentImage\x12\x10\n" +
	"\x03tpl\x18\x04 \x01(\tR\x03tpl\x12G\n" +
	"\btpl_data\x18\x05 \x03(\v2,.vc.bizmisc.SendSecretaryMsgReq.TplDataEntryR\atplData\x120\n" +
	"\x06member\x18\x06 \x01(\v2\x18.vc.svcchat.SimpleMemberR\x06member\x12.\n" +
	"\x06script\x18\a \x01(\v2\x16.vc.bizmisc.ScriptInfoR\x06script\x12-\n" +
	"\acomment\x18\b \x01(\v2\x13.vc.bizmisc.CommentR\acomment\x12=\n" +
	"\rcomment_reply\x18\t \x01(\v2\x18.vc.bizmisc.CommentReplyR\fcommentReply\x12-\n" +
	"\adubbing\x18\n" +
	" \x01(\v2\x13.vc.bizmisc.DubbingR\adubbing\x1aT\n" +
	"\fTplDataEntry\x12\x10\n" +
	"\x03key\x18\x01 \x01(\tR\x03key\x12.\n" +
	"\x05value\x18\x02 \x01(\v2\x18.vc.svcchat.TemplateItemR\x05value:\x028\x01\"<\n" +
	"\x14SendSecretaryMsgResp\x12\x12\n" +
	"\x04code\x18\x01 \x01(\x05R\x04code\x12\x10\n" +
	"\x03msg\x18\x02 \x01(\tR\x03msg\"\xa6\x03\n" +
	"\x14TestSendSystemMsgReq\x12\x1e\n" +
	"\x06to_uid\x18\x01 \x01(\x03B\a\xfaB\x04\"\x02 \x00R\x05toUid\x12*\n" +
	"\fcontent_type\x18\x02 \x01(\x05B\a\xfaB\x04\x1a\x02 \x00R\vcontentType\x124\n" +
	"\n" +
	"system_msg\x18\x03 \x01(\v2\x15.vc.bizmisc.SystemMsgR\tsystemMsg\x124\n" +
	"\n" +
	"follow_msg\x18\x04 \x01(\v2\x15.vc.bizmisc.FollowMsgR\tfollowMsg\x127\n" +
	"\vcomment_msg\x18\x05 \x01(\v2\x16.vc.bizmisc.CommentMsgR\n" +
	"commentMsg\x12.\n" +
	"\blike_msg\x18\x06 \x01(\v2\x13.vc.bizmisc.LikeMsgR\alikeMsg\x127\n" +
	"\vdubbing_msg\x18\a \x01(\v2\x16.vc.bizmisc.DubbingMsgR\n" +
	"dubbingMsg\x124\n" +
	"\n" +
	"review_msg\x18\b \x01(\v2\x15.vc.bizmisc.ReviewMsgR\treviewMsg\"=\n" +
	"\x15TestSendSystemMsgResp\x12\x12\n" +
	"\x04code\x18\x01 \x01(\x05R\x04code\x12\x10\n" +
	"\x03msg\x18\x02 \x01(\tR\x03msg\"\xb5\x01\n" +
	"\tSystemMsg\x12-\n" +
	"\x05title\x18\x01 \x01(\v2\x17.vc.svcchat.TemplateMsgR\x05title\x12\x17\n" +
	"\abgm_url\x18\x02 \x01(\tR\x06bgmUrl\x12\x19\n" +
	"\bjump_url\x18\x03 \x01(\tR\ajumpUrl\x121\n" +
	"\acontent\x18\x04 \x01(\v2\x17.vc.svcchat.TemplateMsgR\acontent\x12\x12\n" +
	"\x04time\x18\x05 \x01(\x03R\x04time\"\x84\x01\n" +
	"\tFollowMsg\x121\n" +
	"\acontent\x18\x01 \x01(\v2\x17.vc.svcchat.TemplateMsgR\acontent\x120\n" +
	"\x06member\x18\x02 \x01(\v2\x18.vc.svcchat.SimpleMemberR\x06member\x12\x12\n" +
	"\x04time\x18\x03 \x01(\x03R\x04time\"\xa3\x02\n" +
	"\n" +
	"CommentMsg\x121\n" +
	"\acontent\x18\x01 \x01(\v2\x17.vc.svcchat.TemplateMsgR\acontent\x120\n" +
	"\x06member\x18\x02 \x01(\v2\x18.vc.svcchat.SimpleMemberR\x06member\x12-\n" +
	"\acomment\x18\x03 \x01(\v2\x13.vc.bizmisc.CommentR\acomment\x12=\n" +
	"\rcomment_reply\x18\x04 \x01(\v2\x18.vc.bizmisc.CommentReplyR\fcommentReply\x12.\n" +
	"\x06script\x18\x05 \x01(\v2\x16.vc.bizmisc.ScriptInfoR\x06script\x12\x12\n" +
	"\x04time\x18\x06 \x01(\x03R\x04time\"\x90\x02\n" +
	"\aLikeMsg\x121\n" +
	"\acontent\x18\x01 \x01(\v2\x17.vc.svcchat.TemplateMsgR\acontent\x120\n" +
	"\x06member\x18\x02 \x01(\v2\x18.vc.svcchat.SimpleMemberR\x06member\x12.\n" +
	"\x06script\x18\x03 \x01(\v2\x16.vc.bizmisc.ScriptInfoR\x06script\x12-\n" +
	"\acomment\x18\x04 \x01(\v2\x13.vc.bizmisc.CommentR\acomment\x12-\n" +
	"\adubbing\x18\x05 \x01(\v2\x13.vc.bizmisc.DubbingR\adubbing\x12\x12\n" +
	"\x04time\x18\x06 \x01(\x03R\x04time\"\xe4\x01\n" +
	"\n" +
	"DubbingMsg\x121\n" +
	"\acontent\x18\x01 \x01(\v2\x17.vc.svcchat.TemplateMsgR\acontent\x120\n" +
	"\x06member\x18\x02 \x01(\v2\x18.vc.svcchat.SimpleMemberR\x06member\x12.\n" +
	"\x06script\x18\x03 \x01(\v2\x16.vc.bizmisc.ScriptInfoR\x06script\x12-\n" +
	"\adubbing\x18\x05 \x01(\v2\x13.vc.bizmisc.DubbingR\adubbing\x12\x12\n" +
	"\x04time\x18\x06 \x01(\x03R\x04time\"\xcf\x01\n" +
	"\tReviewMsg\x12-\n" +
	"\x05title\x18\x01 \x01(\v2\x17.vc.svcchat.TemplateMsgR\x05title\x12\x17\n" +
	"\abgm_url\x18\x02 \x01(\tR\x06bgmUrl\x12\x19\n" +
	"\bjump_url\x18\x03 \x01(\tR\ajumpUrl\x121\n" +
	"\acontent\x18\x04 \x01(\v2\x17.vc.svcchat.TemplateMsgR\acontent\x12,\n" +
	"\x06script\x18\x05 \x01(\v2\x14.vc.svcscript.ScriptR\x06script\"\x11\n" +
	"\x0fCheckVersionReq\"l\n" +
	"\x10CheckVersionResp\x12\x12\n" +
	"\x04code\x18\x01 \x01(\x05R\x04code\x12\x10\n" +
	"\x03msg\x18\x02 \x01(\tR\x03msg\x122\n" +
	"\x04data\x18\x03 \x01(\v2\x1e.vc.svcconfig.CheckVersionDataR\x04data\"\x0e\n" +
	"\fGetApkUrlReq\"h\n" +
	"\rGetApkUrlResp\x12\x12\n" +
	"\x04code\x18\x01 \x01(\x05R\x04code\x12\x10\n" +
	"\x03msg\x18\x02 \x01(\tR\x03msg\x121\n" +
	"\x04data\x18\x03 \x01(\v2\x1d.vc.bizmisc.GetApkUrlRespDataR\x04data\"%\n" +
	"\x11GetApkUrlRespData\x12\x10\n" +
	"\x03url\x18\x01 \x01(\tR\x03url2\xf5\b\n" +
	"\x01s\x12x\n" +
	"\x0eGetAliObjectId\x12\x1a.vc.bizmisc.AliObjectIdReq\x1a\x1b.vc.bizmisc.AliObjectIdResp\"-\x82\xd3\xe4\x93\x02':\x01*\"\"/vc.bizmisc.s/v1/get_ali_object_id\x12\x90\x01\n" +
	"\x17GenerateAliOssSignature\x12\x1d.vc.bizmisc.AliOssSigatureReq\x1a\x1e.vc.bizmisc.AliOssSigatureResp\"6\x82\xd3\xe4\x93\x020:\x01*\"+/vc.bizmisc.s/v1/generate_ali_oss_signature\x12u\n" +
	"\fGetChanToken\x12\x1b.vc.bizmisc.GetChanTokenReq\x1a\x1c.vc.bizmisc.GetChanTokenResp\"*\x82\xd3\xe4\x93\x02$:\x01*\"\x1f/vc.bizmisc.s/v1/get_chan_token\x12m\n" +
	"\n" +
	"GetAsrAddr\x12\x19.vc.bizmisc.GetAsrAddrReq\x1a\x1a.vc.bizmisc.GetAsrAddrResp\"(\x82\xd3\xe4\x93\x02\":\x01*\"\x1d/vc.bizmisc.s/v1/get_asr_addr\x12z\n" +
	"\rGetVcPushAddr\x12\x1c.vc.bizmisc.GetVcPushAddrReq\x1a\x1d.vc.bizmisc.GetVcPushAddrResp\",\x82\xd3\xe4\x93\x02&:\x01*\"!/vc.bizmisc.s/v1/get_vc_push_addr\x12\x89\x01\n" +
	"\x14TestSendSecretaryMsg\x12\x1f.vc.bizmisc.SendSecretaryMsgReq\x1a .vc.bizmisc.SendSecretaryMsgResp\".\x82\xd3\xe4\x93\x02(:\x01*\"#/vc.bizmisc.s/v1/send_secretary_msg\x12\x85\x01\n" +
	"\x11TestSendSystemMsg\x12 .vc.bizmisc.TestSendSystemMsgReq\x1a!.vc.bizmisc.TestSendSystemMsgResp\"+\x82\xd3\xe4\x93\x02%:\x01*\" /vc.bizmisc.s/v1/send_system_msg\x12t\n" +
	"\fCheckVersion\x12\x1b.vc.bizmisc.CheckVersionReq\x1a\x1c.vc.bizmisc.CheckVersionResp\")\x82\xd3\xe4\x93\x02#:\x01*\"\x1e/vc.bizmisc.s/v1/check_version\x12w\n" +
	"\x11GetOfficialApkUrl\x12\x18.vc.bizmisc.GetApkUrlReq\x1a\x19.vc.bizmisc.GetApkUrlResp\"-\x82\xd3\xe4\x93\x02'\x12%/vc.bizmisc.s/v1/get_official_apk_urlBCZAnew-gitlab.xunlei.cn/vcproject/backends/proto/api/bizmisc;bizmiscb\x06proto3"

var (
	file_bizmisc_proto_rawDescOnce sync.Once
	file_bizmisc_proto_rawDescData []byte
)

func file_bizmisc_proto_rawDescGZIP() []byte {
	file_bizmisc_proto_rawDescOnce.Do(func() {
		file_bizmisc_proto_rawDescData = protoimpl.X.CompressGZIP(unsafe.Slice(unsafe.StringData(file_bizmisc_proto_rawDesc), len(file_bizmisc_proto_rawDesc)))
	})
	return file_bizmisc_proto_rawDescData
}

var file_bizmisc_proto_msgTypes = make([]protoimpl.MessageInfo, 37)
var file_bizmisc_proto_goTypes = []any{
	(*AliObjectIdReq)(nil),             // 0: vc.bizmisc.AliObjectIdReq
	(*AliObjectIdResp)(nil),            // 1: vc.bizmisc.AliObjectIdResp
	(*AliObjectIdRespData)(nil),        // 2: vc.bizmisc.AliObjectIdRespData
	(*AliOssSigatureReq)(nil),          // 3: vc.bizmisc.AliOssSigatureReq
	(*AliOssSigatureResp)(nil),         // 4: vc.bizmisc.AliOssSigatureResp
	(*AliOssSigatureRespData)(nil),     // 5: vc.bizmisc.AliOssSigatureRespData
	(*GetChanTokenReq)(nil),            // 6: vc.bizmisc.GetChanTokenReq
	(*GetChanTokenResp)(nil),           // 7: vc.bizmisc.GetChanTokenResp
	(*GetChanTokenRespData)(nil),       // 8: vc.bizmisc.GetChanTokenRespData
	(*RvcInfo)(nil),                    // 9: vc.bizmisc.RvcInfo
	(*GetAsrAddrReq)(nil),              // 10: vc.bizmisc.GetAsrAddrReq
	(*GetAsrAddrResp)(nil),             // 11: vc.bizmisc.GetAsrAddrResp
	(*GetAsrAddrRespData)(nil),         // 12: vc.bizmisc.GetAsrAddrRespData
	(*GetVcPushAddrReq)(nil),           // 13: vc.bizmisc.GetVcPushAddrReq
	(*GetVcPushAddrResp)(nil),          // 14: vc.bizmisc.GetVcPushAddrResp
	(*GetVcPushAddrRespData)(nil),      // 15: vc.bizmisc.GetVcPushAddrRespData
	(*ScriptInfo)(nil),                 // 16: vc.bizmisc.ScriptInfo
	(*ScriptDetail)(nil),               // 17: vc.bizmisc.ScriptDetail
	(*Comment)(nil),                    // 18: vc.bizmisc.Comment
	(*CommentReply)(nil),               // 19: vc.bizmisc.CommentReply
	(*Dubbing)(nil),                    // 20: vc.bizmisc.Dubbing
	(*SendSecretaryMsgReq)(nil),        // 21: vc.bizmisc.SendSecretaryMsgReq
	(*SendSecretaryMsgResp)(nil),       // 22: vc.bizmisc.SendSecretaryMsgResp
	(*TestSendSystemMsgReq)(nil),       // 23: vc.bizmisc.TestSendSystemMsgReq
	(*TestSendSystemMsgResp)(nil),      // 24: vc.bizmisc.TestSendSystemMsgResp
	(*SystemMsg)(nil),                  // 25: vc.bizmisc.SystemMsg
	(*FollowMsg)(nil),                  // 26: vc.bizmisc.FollowMsg
	(*CommentMsg)(nil),                 // 27: vc.bizmisc.CommentMsg
	(*LikeMsg)(nil),                    // 28: vc.bizmisc.LikeMsg
	(*DubbingMsg)(nil),                 // 29: vc.bizmisc.DubbingMsg
	(*ReviewMsg)(nil),                  // 30: vc.bizmisc.ReviewMsg
	(*CheckVersionReq)(nil),            // 31: vc.bizmisc.CheckVersionReq
	(*CheckVersionResp)(nil),           // 32: vc.bizmisc.CheckVersionResp
	(*GetApkUrlReq)(nil),               // 33: vc.bizmisc.GetApkUrlReq
	(*GetApkUrlResp)(nil),              // 34: vc.bizmisc.GetApkUrlResp
	(*GetApkUrlRespData)(nil),          // 35: vc.bizmisc.GetApkUrlRespData
	nil,                                // 36: vc.bizmisc.SendSecretaryMsgReq.TplDataEntry
	(*svcchat.SimpleMember)(nil),       // 37: vc.svcchat.SimpleMember
	(*svcchat.TemplateMsg)(nil),        // 38: vc.svcchat.TemplateMsg
	(*svcscript.Script)(nil),           // 39: vc.svcscript.Script
	(*svcconfig.CheckVersionData)(nil), // 40: vc.svcconfig.CheckVersionData
	(*svcchat.TemplateItem)(nil),       // 41: vc.svcchat.TemplateItem
}
var file_bizmisc_proto_depIdxs = []int32{
	2,  // 0: vc.bizmisc.AliObjectIdResp.data:type_name -> vc.bizmisc.AliObjectIdRespData
	5,  // 1: vc.bizmisc.AliOssSigatureResp.data:type_name -> vc.bizmisc.AliOssSigatureRespData
	8,  // 2: vc.bizmisc.GetChanTokenResp.data:type_name -> vc.bizmisc.GetChanTokenRespData
	12, // 3: vc.bizmisc.GetAsrAddrResp.data:type_name -> vc.bizmisc.GetAsrAddrRespData
	15, // 4: vc.bizmisc.GetVcPushAddrResp.data:type_name -> vc.bizmisc.GetVcPushAddrRespData
	9,  // 5: vc.bizmisc.GetVcPushAddrRespData.rvc:type_name -> vc.bizmisc.RvcInfo
	36, // 6: vc.bizmisc.SendSecretaryMsgReq.tpl_data:type_name -> vc.bizmisc.SendSecretaryMsgReq.TplDataEntry
	37, // 7: vc.bizmisc.SendSecretaryMsgReq.member:type_name -> vc.svcchat.SimpleMember
	16, // 8: vc.bizmisc.SendSecretaryMsgReq.script:type_name -> vc.bizmisc.ScriptInfo
	18, // 9: vc.bizmisc.SendSecretaryMsgReq.comment:type_name -> vc.bizmisc.Comment
	19, // 10: vc.bizmisc.SendSecretaryMsgReq.comment_reply:type_name -> vc.bizmisc.CommentReply
	20, // 11: vc.bizmisc.SendSecretaryMsgReq.dubbing:type_name -> vc.bizmisc.Dubbing
	25, // 12: vc.bizmisc.TestSendSystemMsgReq.system_msg:type_name -> vc.bizmisc.SystemMsg
	26, // 13: vc.bizmisc.TestSendSystemMsgReq.follow_msg:type_name -> vc.bizmisc.FollowMsg
	27, // 14: vc.bizmisc.TestSendSystemMsgReq.comment_msg:type_name -> vc.bizmisc.CommentMsg
	28, // 15: vc.bizmisc.TestSendSystemMsgReq.like_msg:type_name -> vc.bizmisc.LikeMsg
	29, // 16: vc.bizmisc.TestSendSystemMsgReq.dubbing_msg:type_name -> vc.bizmisc.DubbingMsg
	30, // 17: vc.bizmisc.TestSendSystemMsgReq.review_msg:type_name -> vc.bizmisc.ReviewMsg
	38, // 18: vc.bizmisc.SystemMsg.title:type_name -> vc.svcchat.TemplateMsg
	38, // 19: vc.bizmisc.SystemMsg.content:type_name -> vc.svcchat.TemplateMsg
	38, // 20: vc.bizmisc.FollowMsg.content:type_name -> vc.svcchat.TemplateMsg
	37, // 21: vc.bizmisc.FollowMsg.member:type_name -> vc.svcchat.SimpleMember
	38, // 22: vc.bizmisc.CommentMsg.content:type_name -> vc.svcchat.TemplateMsg
	37, // 23: vc.bizmisc.CommentMsg.member:type_name -> vc.svcchat.SimpleMember
	18, // 24: vc.bizmisc.CommentMsg.comment:type_name -> vc.bizmisc.Comment
	19, // 25: vc.bizmisc.CommentMsg.comment_reply:type_name -> vc.bizmisc.CommentReply
	16, // 26: vc.bizmisc.CommentMsg.script:type_name -> vc.bizmisc.ScriptInfo
	38, // 27: vc.bizmisc.LikeMsg.content:type_name -> vc.svcchat.TemplateMsg
	37, // 28: vc.bizmisc.LikeMsg.member:type_name -> vc.svcchat.SimpleMember
	16, // 29: vc.bizmisc.LikeMsg.script:type_name -> vc.bizmisc.ScriptInfo
	18, // 30: vc.bizmisc.LikeMsg.comment:type_name -> vc.bizmisc.Comment
	20, // 31: vc.bizmisc.LikeMsg.dubbing:type_name -> vc.bizmisc.Dubbing
	38, // 32: vc.bizmisc.DubbingMsg.content:type_name -> vc.svcchat.TemplateMsg
	37, // 33: vc.bizmisc.DubbingMsg.member:type_name -> vc.svcchat.SimpleMember
	16, // 34: vc.bizmisc.DubbingMsg.script:type_name -> vc.bizmisc.ScriptInfo
	20, // 35: vc.bizmisc.DubbingMsg.dubbing:type_name -> vc.bizmisc.Dubbing
	38, // 36: vc.bizmisc.ReviewMsg.title:type_name -> vc.svcchat.TemplateMsg
	38, // 37: vc.bizmisc.ReviewMsg.content:type_name -> vc.svcchat.TemplateMsg
	39, // 38: vc.bizmisc.ReviewMsg.script:type_name -> vc.svcscript.Script
	40, // 39: vc.bizmisc.CheckVersionResp.data:type_name -> vc.svcconfig.CheckVersionData
	35, // 40: vc.bizmisc.GetApkUrlResp.data:type_name -> vc.bizmisc.GetApkUrlRespData
	41, // 41: vc.bizmisc.SendSecretaryMsgReq.TplDataEntry.value:type_name -> vc.svcchat.TemplateItem
	0,  // 42: vc.bizmisc.s.GetAliObjectId:input_type -> vc.bizmisc.AliObjectIdReq
	3,  // 43: vc.bizmisc.s.GenerateAliOssSignature:input_type -> vc.bizmisc.AliOssSigatureReq
	6,  // 44: vc.bizmisc.s.GetChanToken:input_type -> vc.bizmisc.GetChanTokenReq
	10, // 45: vc.bizmisc.s.GetAsrAddr:input_type -> vc.bizmisc.GetAsrAddrReq
	13, // 46: vc.bizmisc.s.GetVcPushAddr:input_type -> vc.bizmisc.GetVcPushAddrReq
	21, // 47: vc.bizmisc.s.TestSendSecretaryMsg:input_type -> vc.bizmisc.SendSecretaryMsgReq
	23, // 48: vc.bizmisc.s.TestSendSystemMsg:input_type -> vc.bizmisc.TestSendSystemMsgReq
	31, // 49: vc.bizmisc.s.CheckVersion:input_type -> vc.bizmisc.CheckVersionReq
	33, // 50: vc.bizmisc.s.GetOfficialApkUrl:input_type -> vc.bizmisc.GetApkUrlReq
	1,  // 51: vc.bizmisc.s.GetAliObjectId:output_type -> vc.bizmisc.AliObjectIdResp
	4,  // 52: vc.bizmisc.s.GenerateAliOssSignature:output_type -> vc.bizmisc.AliOssSigatureResp
	7,  // 53: vc.bizmisc.s.GetChanToken:output_type -> vc.bizmisc.GetChanTokenResp
	11, // 54: vc.bizmisc.s.GetAsrAddr:output_type -> vc.bizmisc.GetAsrAddrResp
	14, // 55: vc.bizmisc.s.GetVcPushAddr:output_type -> vc.bizmisc.GetVcPushAddrResp
	22, // 56: vc.bizmisc.s.TestSendSecretaryMsg:output_type -> vc.bizmisc.SendSecretaryMsgResp
	24, // 57: vc.bizmisc.s.TestSendSystemMsg:output_type -> vc.bizmisc.TestSendSystemMsgResp
	32, // 58: vc.bizmisc.s.CheckVersion:output_type -> vc.bizmisc.CheckVersionResp
	34, // 59: vc.bizmisc.s.GetOfficialApkUrl:output_type -> vc.bizmisc.GetApkUrlResp
	51, // [51:60] is the sub-list for method output_type
	42, // [42:51] is the sub-list for method input_type
	42, // [42:42] is the sub-list for extension type_name
	42, // [42:42] is the sub-list for extension extendee
	0,  // [0:42] is the sub-list for field type_name
}

func init() { file_bizmisc_proto_init() }
func file_bizmisc_proto_init() {
	if File_bizmisc_proto != nil {
		return
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: unsafe.Slice(unsafe.StringData(file_bizmisc_proto_rawDesc), len(file_bizmisc_proto_rawDesc)),
			NumEnums:      0,
			NumMessages:   37,
			NumExtensions: 0,
			NumServices:   1,
		},
		GoTypes:           file_bizmisc_proto_goTypes,
		DependencyIndexes: file_bizmisc_proto_depIdxs,
		MessageInfos:      file_bizmisc_proto_msgTypes,
	}.Build()
	File_bizmisc_proto = out.File
	file_bizmisc_proto_goTypes = nil
	file_bizmisc_proto_depIdxs = nil
}
