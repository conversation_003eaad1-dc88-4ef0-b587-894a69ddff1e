// Code generated by protoc-gen-validate. DO NOT EDIT.
// source: svcchat.proto

package svcchat

import (
	"bytes"
	"errors"
	"fmt"
	"net"
	"net/mail"
	"net/url"
	"regexp"
	"sort"
	"strings"
	"time"
	"unicode/utf8"

	"google.golang.org/protobuf/types/known/anypb"
)

// ensure the imports are used
var (
	_ = bytes.MinRead
	_ = errors.New("")
	_ = fmt.Print
	_ = utf8.UTFMax
	_ = (*regexp.Regexp)(nil)
	_ = (*strings.Reader)(nil)
	_ = net.IPv4len
	_ = time.Duration(0)
	_ = (*url.URL)(nil)
	_ = (*mail.Address)(nil)
	_ = anypb.Any{}
	_ = sort.Sort
)

// Validate checks the field values on TemplateItem with the rules defined in
// the proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *TemplateItem) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on TemplateItem with the rules defined
// in the proto definition for this message. If any rules are violated, the
// result is a list of violation errors wrapped in TemplateItemMultiError, or
// nil if none found.
func (m *TemplateItem) ValidateAll() error {
	return m.validate(true)
}

func (m *TemplateItem) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Text

	// no validation rules for Color

	// no validation rules for Url

	// no validation rules for Type

	// no validation rules for Name

	// no validation rules for Ext

	// no validation rules for Underline

	// no validation rules for Size

	if len(errors) > 0 {
		return TemplateItemMultiError(errors)
	}

	return nil
}

// TemplateItemMultiError is an error wrapping multiple validation errors
// returned by TemplateItem.ValidateAll() if the designated constraints aren't met.
type TemplateItemMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m TemplateItemMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m TemplateItemMultiError) AllErrors() []error { return m }

// TemplateItemValidationError is the validation error returned by
// TemplateItem.Validate if the designated constraints aren't met.
type TemplateItemValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e TemplateItemValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e TemplateItemValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e TemplateItemValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e TemplateItemValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e TemplateItemValidationError) ErrorName() string { return "TemplateItemValidationError" }

// Error satisfies the builtin error interface
func (e TemplateItemValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sTemplateItem.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = TemplateItemValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = TemplateItemValidationError{}

// Validate checks the field values on TemplateMsg with the rules defined in
// the proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *TemplateMsg) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on TemplateMsg with the rules defined in
// the proto definition for this message. If any rules are violated, the
// result is a list of violation errors wrapped in TemplateMsgMultiError, or
// nil if none found.
func (m *TemplateMsg) ValidateAll() error {
	return m.validate(true)
}

func (m *TemplateMsg) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Tpl

	{
		sorted_keys := make([]string, len(m.GetData()))
		i := 0
		for key := range m.GetData() {
			sorted_keys[i] = key
			i++
		}
		sort.Slice(sorted_keys, func(i, j int) bool { return sorted_keys[i] < sorted_keys[j] })
		for _, key := range sorted_keys {
			val := m.GetData()[key]
			_ = val

			// no validation rules for Data[key]

			if all {
				switch v := interface{}(val).(type) {
				case interface{ ValidateAll() error }:
					if err := v.ValidateAll(); err != nil {
						errors = append(errors, TemplateMsgValidationError{
							field:  fmt.Sprintf("Data[%v]", key),
							reason: "embedded message failed validation",
							cause:  err,
						})
					}
				case interface{ Validate() error }:
					if err := v.Validate(); err != nil {
						errors = append(errors, TemplateMsgValidationError{
							field:  fmt.Sprintf("Data[%v]", key),
							reason: "embedded message failed validation",
							cause:  err,
						})
					}
				}
			} else if v, ok := interface{}(val).(interface{ Validate() error }); ok {
				if err := v.Validate(); err != nil {
					return TemplateMsgValidationError{
						field:  fmt.Sprintf("Data[%v]", key),
						reason: "embedded message failed validation",
						cause:  err,
					}
				}
			}

		}
	}

	// no validation rules for ImageUrl

	// no validation rules for Userid

	// no validation rules for TplColor

	// no validation rules for Type

	// no validation rules for Url

	if len(errors) > 0 {
		return TemplateMsgMultiError(errors)
	}

	return nil
}

// TemplateMsgMultiError is an error wrapping multiple validation errors
// returned by TemplateMsg.ValidateAll() if the designated constraints aren't met.
type TemplateMsgMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m TemplateMsgMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m TemplateMsgMultiError) AllErrors() []error { return m }

// TemplateMsgValidationError is the validation error returned by
// TemplateMsg.Validate if the designated constraints aren't met.
type TemplateMsgValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e TemplateMsgValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e TemplateMsgValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e TemplateMsgValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e TemplateMsgValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e TemplateMsgValidationError) ErrorName() string { return "TemplateMsgValidationError" }

// Error satisfies the builtin error interface
func (e TemplateMsgValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sTemplateMsg.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = TemplateMsgValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = TemplateMsgValidationError{}

// Validate checks the field values on SimpleMember with the rules defined in
// the proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *SimpleMember) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on SimpleMember with the rules defined
// in the proto definition for this message. If any rules are violated, the
// result is a list of violation errors wrapped in SimpleMemberMultiError, or
// nil if none found.
func (m *SimpleMember) ValidateAll() error {
	return m.validate(true)
}

func (m *SimpleMember) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for UserId

	// no validation rules for Avatar

	// no validation rules for Nick

	// no validation rules for Sex

	if len(errors) > 0 {
		return SimpleMemberMultiError(errors)
	}

	return nil
}

// SimpleMemberMultiError is an error wrapping multiple validation errors
// returned by SimpleMember.ValidateAll() if the designated constraints aren't met.
type SimpleMemberMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m SimpleMemberMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m SimpleMemberMultiError) AllErrors() []error { return m }

// SimpleMemberValidationError is the validation error returned by
// SimpleMember.Validate if the designated constraints aren't met.
type SimpleMemberValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e SimpleMemberValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e SimpleMemberValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e SimpleMemberValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e SimpleMemberValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e SimpleMemberValidationError) ErrorName() string { return "SimpleMemberValidationError" }

// Error satisfies the builtin error interface
func (e SimpleMemberValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sSimpleMember.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = SimpleMemberValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = SimpleMemberValidationError{}

// Validate checks the field values on ScriptInfo with the rules defined in the
// proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *ScriptInfo) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on ScriptInfo with the rules defined in
// the proto definition for this message. If any rules are violated, the
// result is a list of violation errors wrapped in ScriptInfoMultiError, or
// nil if none found.
func (m *ScriptInfo) ValidateAll() error {
	return m.validate(true)
}

func (m *ScriptInfo) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for ScriptId

	// no validation rules for Title

	// no validation rules for Cover

	if len(errors) > 0 {
		return ScriptInfoMultiError(errors)
	}

	return nil
}

// ScriptInfoMultiError is an error wrapping multiple validation errors
// returned by ScriptInfo.ValidateAll() if the designated constraints aren't met.
type ScriptInfoMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m ScriptInfoMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m ScriptInfoMultiError) AllErrors() []error { return m }

// ScriptInfoValidationError is the validation error returned by
// ScriptInfo.Validate if the designated constraints aren't met.
type ScriptInfoValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e ScriptInfoValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e ScriptInfoValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e ScriptInfoValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e ScriptInfoValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e ScriptInfoValidationError) ErrorName() string { return "ScriptInfoValidationError" }

// Error satisfies the builtin error interface
func (e ScriptInfoValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sScriptInfo.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = ScriptInfoValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = ScriptInfoValidationError{}

// Validate checks the field values on Comment with the rules defined in the
// proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *Comment) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on Comment with the rules defined in the
// proto definition for this message. If any rules are violated, the result is
// a list of violation errors wrapped in CommentMultiError, or nil if none found.
func (m *Comment) ValidateAll() error {
	return m.validate(true)
}

func (m *Comment) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for CommentId

	// no validation rules for Text

	// no validation rules for AudioUrl

	// no validation rules for Duration

	if len(errors) > 0 {
		return CommentMultiError(errors)
	}

	return nil
}

// CommentMultiError is an error wrapping multiple validation errors returned
// by Comment.ValidateAll() if the designated constraints aren't met.
type CommentMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m CommentMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m CommentMultiError) AllErrors() []error { return m }

// CommentValidationError is the validation error returned by Comment.Validate
// if the designated constraints aren't met.
type CommentValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e CommentValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e CommentValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e CommentValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e CommentValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e CommentValidationError) ErrorName() string { return "CommentValidationError" }

// Error satisfies the builtin error interface
func (e CommentValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sComment.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = CommentValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = CommentValidationError{}

// Validate checks the field values on CommentReply with the rules defined in
// the proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *CommentReply) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on CommentReply with the rules defined
// in the proto definition for this message. If any rules are violated, the
// result is a list of violation errors wrapped in CommentReplyMultiError, or
// nil if none found.
func (m *CommentReply) ValidateAll() error {
	return m.validate(true)
}

func (m *CommentReply) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for CommentId

	// no validation rules for Text

	// no validation rules for AudioUrl

	// no validation rules for Duration

	if len(errors) > 0 {
		return CommentReplyMultiError(errors)
	}

	return nil
}

// CommentReplyMultiError is an error wrapping multiple validation errors
// returned by CommentReply.ValidateAll() if the designated constraints aren't met.
type CommentReplyMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m CommentReplyMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m CommentReplyMultiError) AllErrors() []error { return m }

// CommentReplyValidationError is the validation error returned by
// CommentReply.Validate if the designated constraints aren't met.
type CommentReplyValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e CommentReplyValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e CommentReplyValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e CommentReplyValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e CommentReplyValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e CommentReplyValidationError) ErrorName() string { return "CommentReplyValidationError" }

// Error satisfies the builtin error interface
func (e CommentReplyValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sCommentReply.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = CommentReplyValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = CommentReplyValidationError{}

// Validate checks the field values on Dubbing with the rules defined in the
// proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *Dubbing) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on Dubbing with the rules defined in the
// proto definition for this message. If any rules are violated, the result is
// a list of violation errors wrapped in DubbingMultiError, or nil if none found.
func (m *Dubbing) ValidateAll() error {
	return m.validate(true)
}

func (m *Dubbing) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for DubbingId

	// no validation rules for AudioUrl

	// no validation rules for Duration

	// no validation rules for LikeCount

	// no validation rules for IsLiked

	if len(errors) > 0 {
		return DubbingMultiError(errors)
	}

	return nil
}

// DubbingMultiError is an error wrapping multiple validation errors returned
// by Dubbing.ValidateAll() if the designated constraints aren't met.
type DubbingMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m DubbingMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m DubbingMultiError) AllErrors() []error { return m }

// DubbingValidationError is the validation error returned by Dubbing.Validate
// if the designated constraints aren't met.
type DubbingValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e DubbingValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e DubbingValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e DubbingValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e DubbingValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e DubbingValidationError) ErrorName() string { return "DubbingValidationError" }

// Error satisfies the builtin error interface
func (e DubbingValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sDubbing.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = DubbingValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = DubbingValidationError{}

// Validate checks the field values on MsgHandleReq with the rules defined in
// the proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *MsgHandleReq) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on MsgHandleReq with the rules defined
// in the proto definition for this message. If any rules are violated, the
// result is a list of violation errors wrapped in MsgHandleReqMultiError, or
// nil if none found.
func (m *MsgHandleReq) ValidateAll() error {
	return m.validate(true)
}

func (m *MsgHandleReq) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetMsg()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, MsgHandleReqValidationError{
					field:  "Msg",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, MsgHandleReqValidationError{
					field:  "Msg",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetMsg()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return MsgHandleReqValidationError{
				field:  "Msg",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetBase()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, MsgHandleReqValidationError{
					field:  "Base",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, MsgHandleReqValidationError{
					field:  "Base",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetBase()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return MsgHandleReqValidationError{
				field:  "Base",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return MsgHandleReqMultiError(errors)
	}

	return nil
}

// MsgHandleReqMultiError is an error wrapping multiple validation errors
// returned by MsgHandleReq.ValidateAll() if the designated constraints aren't met.
type MsgHandleReqMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m MsgHandleReqMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m MsgHandleReqMultiError) AllErrors() []error { return m }

// MsgHandleReqValidationError is the validation error returned by
// MsgHandleReq.Validate if the designated constraints aren't met.
type MsgHandleReqValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e MsgHandleReqValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e MsgHandleReqValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e MsgHandleReqValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e MsgHandleReqValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e MsgHandleReqValidationError) ErrorName() string { return "MsgHandleReqValidationError" }

// Error satisfies the builtin error interface
func (e MsgHandleReqValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sMsgHandleReq.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = MsgHandleReqValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = MsgHandleReqValidationError{}

// Validate checks the field values on MsgHandleResp with the rules defined in
// the proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *MsgHandleResp) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on MsgHandleResp with the rules defined
// in the proto definition for this message. If any rules are violated, the
// result is a list of violation errors wrapped in MsgHandleRespMultiError, or
// nil if none found.
func (m *MsgHandleResp) ValidateAll() error {
	return m.validate(true)
}

func (m *MsgHandleResp) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetBase()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, MsgHandleRespValidationError{
					field:  "Base",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, MsgHandleRespValidationError{
					field:  "Base",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetBase()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return MsgHandleRespValidationError{
				field:  "Base",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for Exts

	if len(errors) > 0 {
		return MsgHandleRespMultiError(errors)
	}

	return nil
}

// MsgHandleRespMultiError is an error wrapping multiple validation errors
// returned by MsgHandleResp.ValidateAll() if the designated constraints
// aren't met.
type MsgHandleRespMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m MsgHandleRespMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m MsgHandleRespMultiError) AllErrors() []error { return m }

// MsgHandleRespValidationError is the validation error returned by
// MsgHandleResp.Validate if the designated constraints aren't met.
type MsgHandleRespValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e MsgHandleRespValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e MsgHandleRespValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e MsgHandleRespValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e MsgHandleRespValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e MsgHandleRespValidationError) ErrorName() string { return "MsgHandleRespValidationError" }

// Error satisfies the builtin error interface
func (e MsgHandleRespValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sMsgHandleResp.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = MsgHandleRespValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = MsgHandleRespValidationError{}
