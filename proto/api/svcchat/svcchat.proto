syntax = "proto3";

package vc.svcchat;
option go_package = "new-gitlab.xunlei.cn/vcproject/backends/proto/api/svcchat;svcchat";

//import "protoc-gen-validate/validate/validate.proto";
import "common/common.proto";
import "basemsgtransfer/basemsgtransfer.proto";


enum TemplateJumpType {
	jump_type_init = 0;
	jump_web = 1;  // 跳转h5
	jump_page = 2; // 跳转指定页面
	jump_update_app = 3;  // 跳转更新app
	jump_session_top = 4;  // 会话置顶
	jump_script = 5;  //跳转剧本
  jump_user_info = 6; //跳转用户信息
}

message TemplateItem {
	string text = 1;
	string color = 2;
	string url = 3;
	TemplateJumpType type = 4;
	string name = 5;
	string ext = 6;
	bool underline = 7;
	int32 size = 8;
}

message TemplateMsg {
	string tpl = 1;
	map<string, TemplateItem> data = 2;
	string image_url = 3;
	int64 userid = 4;
	string tpl_color = 5; // 文案颜色
  TemplateJumpType type = 6; //外部跳转类型
  string url = 7; //外部跳转地址
}

message SimpleMember {
	int64 user_id=1;
	string avatar=2;
	string nick=3;
	int32 sex=4;
}

message ScriptInfo {
  int64 script_id=1;
  string title=2;
  string cover=3;
}

message Comment {
  int64 comment_id=1;
  string text=2;
  string audio_url=3;
  int64 duration=4;
}

message CommentReply {
  int64 comment_id=1;
  string text=2;
  string audio_url=3;
  int64 duration=4;
}

message Dubbing {
  int64 dubbing_id=1;
  string audio_url=2;
  int64 duration=3;
  int32 like_count=4;
  bool is_liked=5;
}


message MsgHandleReq {
	basemsgtransfer.MsgData msg = 1;
	common.BaseParam base = 2;
}

message MsgHandleResp {
	common.SvcBaseResp base = 1;
	map<string, string> exts = 2;
}

service s {
	rpc SetUserOffline(common.SvcUseridReq) returns (common.SvcCommonResp) {}
	rpc MsgHandle(MsgHandleReq) returns  (MsgHandleResp) {}
}