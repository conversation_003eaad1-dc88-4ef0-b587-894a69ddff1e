// Code generated by protoc-gen-go-grpc. DO NOT EDIT.

package svcchat

import (
	context "context"
	grpc "google.golang.org/grpc"
	codes "google.golang.org/grpc/codes"
	status "google.golang.org/grpc/status"
	common "new-gitlab.xunlei.cn/vcproject/backends/proto/api/common"
)

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
// Requires gRPC-Go v1.32.0 or later.
const _ = grpc.SupportPackageIsVersion7

// SClient is the client API for S service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://pkg.go.dev/google.golang.org/grpc/?tab=doc#ClientConn.NewStream.
type SClient interface {
	SetUserOffline(ctx context.Context, in *common.SvcUseridReq, opts ...grpc.CallOption) (*common.SvcCommonResp, error)
	MsgHandle(ctx context.Context, in *MsgHandleReq, opts ...grpc.CallOption) (*MsgHandleResp, error)
}

type sClient struct {
	cc grpc.ClientConnInterface
}

func NewSClient(cc grpc.ClientConnInterface) SClient {
	return &sClient{cc}
}

func (c *sClient) SetUserOffline(ctx context.Context, in *common.SvcUseridReq, opts ...grpc.CallOption) (*common.SvcCommonResp, error) {
	out := new(common.SvcCommonResp)
	err := c.cc.Invoke(ctx, "/vc.svcchat.s/SetUserOffline", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *sClient) MsgHandle(ctx context.Context, in *MsgHandleReq, opts ...grpc.CallOption) (*MsgHandleResp, error) {
	out := new(MsgHandleResp)
	err := c.cc.Invoke(ctx, "/vc.svcchat.s/MsgHandle", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// SServer is the server API for S service.
// All implementations should embed UnimplementedSServer
// for forward compatibility
type SServer interface {
	SetUserOffline(context.Context, *common.SvcUseridReq) (*common.SvcCommonResp, error)
	MsgHandle(context.Context, *MsgHandleReq) (*MsgHandleResp, error)
}

// UnimplementedSServer should be embedded to have forward compatible implementations.
type UnimplementedSServer struct {
}

func (UnimplementedSServer) SetUserOffline(context.Context, *common.SvcUseridReq) (*common.SvcCommonResp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method SetUserOffline not implemented")
}
func (UnimplementedSServer) MsgHandle(context.Context, *MsgHandleReq) (*MsgHandleResp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method MsgHandle not implemented")
}

// UnsafeSServer may be embedded to opt out of forward compatibility for this service.
// Use of this interface is not recommended, as added methods to SServer will
// result in compilation errors.
type UnsafeSServer interface {
	mustEmbedUnimplementedSServer()
}

func RegisterSServer(s grpc.ServiceRegistrar, srv SServer) {
	s.RegisterService(&S_ServiceDesc, srv)
}

func _S_SetUserOffline_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(common.SvcUseridReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(SServer).SetUserOffline(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/vc.svcchat.s/SetUserOffline",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(SServer).SetUserOffline(ctx, req.(*common.SvcUseridReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _S_MsgHandle_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(MsgHandleReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(SServer).MsgHandle(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/vc.svcchat.s/MsgHandle",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(SServer).MsgHandle(ctx, req.(*MsgHandleReq))
	}
	return interceptor(ctx, in, info, handler)
}

// S_ServiceDesc is the grpc.ServiceDesc for S service.
// It's only intended for direct use with grpc.RegisterService,
// and not to be introspected or modified (even as a copy)
var S_ServiceDesc = grpc.ServiceDesc{
	ServiceName: "vc.svcchat.s",
	HandlerType: (*SServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "SetUserOffline",
			Handler:    _S_SetUserOffline_Handler,
		},
		{
			MethodName: "MsgHandle",
			Handler:    _S_MsgHandle_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "svcchat.proto",
}
