// Code generated by protoc-gen-go-grpc. DO NOT EDIT.

package bizaccount

import (
	context "context"
	grpc "google.golang.org/grpc"
	codes "google.golang.org/grpc/codes"
	status "google.golang.org/grpc/status"
	svcaccount "new-gitlab.xunlei.cn/vcproject/backends/proto/api/svcaccount"
)

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
// Requires gRPC-Go v1.32.0 or later.
const _ = grpc.SupportPackageIsVersion7

// SClient is the client API for S service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://pkg.go.dev/google.golang.org/grpc/?tab=doc#ClientConn.NewStream.
type SClient interface {
	// 获取验证码
	GetVerificationCode(ctx context.Context, in *svcaccount.GetVerificationCodeReq, opts ...grpc.CallOption) (*GetVerificationCodeResp, error)
	// 注册
	AccountSignUp(ctx context.Context, in *svcaccount.AccountSignUpReq, opts ...grpc.CallOption) (*AccountSignUpResp, error)
	// 登录
	AccountSignIn(ctx context.Context, in *svcaccount.AccountSignInReq, opts ...grpc.CallOption) (*AccountSignInResp, error)
	// 退出登录
	AccountSignOut(ctx context.Context, in *svcaccount.AccountSignOutReq, opts ...grpc.CallOption) (*AccountSignOutResp, error)
	// 刷新Token
	RefreshToken(ctx context.Context, in *svcaccount.RefreshTokenReq, opts ...grpc.CallOption) (*RefreshTokenResp, error)
	// 注销账号
	AccountDelete(ctx context.Context, in *svcaccount.AccountDeleteReq, opts ...grpc.CallOption) (*AccountDeleteResp, error)
	// 获取用户信息
	GetUserInfo(ctx context.Context, in *svcaccount.GetUserInfoReq, opts ...grpc.CallOption) (*GetUserInfoResp, error)
	// 更新用户信息
	UpdateUserInfo(ctx context.Context, in *svcaccount.UpdateUserInfoReq, opts ...grpc.CallOption) (*UpdateUserInfoResp, error)
	// 关注用户
	FollowUser(ctx context.Context, in *FollowUserReq, opts ...grpc.CallOption) (*FollowUserResp, error)
	// 取消关注用户
	UnfollowUser(ctx context.Context, in *UnfollowUserReq, opts ...grpc.CallOption) (*UnfollowUserResp, error)
	// 检查是否关注
	CheckFollowing(ctx context.Context, in *CheckFollowingReq, opts ...grpc.CallOption) (*CheckFollowingResp, error)
	// 获取关注的用户列表
	GetFollowingUsers(ctx context.Context, in *GetFollowingUsersReq, opts ...grpc.CallOption) (*GetFollowingUsersResp, error)
	// 获取粉丝列表
	GetFollowers(ctx context.Context, in *GetFollowersReq, opts ...grpc.CallOption) (*GetFollowersResp, error)
	// 批量检查是否关注
	BatchCheckFollowing(ctx context.Context, in *BatchCheckFollowingReq, opts ...grpc.CallOption) (*BatchCheckFollowingResp, error)
}

type sClient struct {
	cc grpc.ClientConnInterface
}

func NewSClient(cc grpc.ClientConnInterface) SClient {
	return &sClient{cc}
}

func (c *sClient) GetVerificationCode(ctx context.Context, in *svcaccount.GetVerificationCodeReq, opts ...grpc.CallOption) (*GetVerificationCodeResp, error) {
	out := new(GetVerificationCodeResp)
	err := c.cc.Invoke(ctx, "/vc.bizaccount.s/GetVerificationCode", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *sClient) AccountSignUp(ctx context.Context, in *svcaccount.AccountSignUpReq, opts ...grpc.CallOption) (*AccountSignUpResp, error) {
	out := new(AccountSignUpResp)
	err := c.cc.Invoke(ctx, "/vc.bizaccount.s/AccountSignUp", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *sClient) AccountSignIn(ctx context.Context, in *svcaccount.AccountSignInReq, opts ...grpc.CallOption) (*AccountSignInResp, error) {
	out := new(AccountSignInResp)
	err := c.cc.Invoke(ctx, "/vc.bizaccount.s/AccountSignIn", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *sClient) AccountSignOut(ctx context.Context, in *svcaccount.AccountSignOutReq, opts ...grpc.CallOption) (*AccountSignOutResp, error) {
	out := new(AccountSignOutResp)
	err := c.cc.Invoke(ctx, "/vc.bizaccount.s/AccountSignOut", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *sClient) RefreshToken(ctx context.Context, in *svcaccount.RefreshTokenReq, opts ...grpc.CallOption) (*RefreshTokenResp, error) {
	out := new(RefreshTokenResp)
	err := c.cc.Invoke(ctx, "/vc.bizaccount.s/RefreshToken", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *sClient) AccountDelete(ctx context.Context, in *svcaccount.AccountDeleteReq, opts ...grpc.CallOption) (*AccountDeleteResp, error) {
	out := new(AccountDeleteResp)
	err := c.cc.Invoke(ctx, "/vc.bizaccount.s/AccountDelete", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *sClient) GetUserInfo(ctx context.Context, in *svcaccount.GetUserInfoReq, opts ...grpc.CallOption) (*GetUserInfoResp, error) {
	out := new(GetUserInfoResp)
	err := c.cc.Invoke(ctx, "/vc.bizaccount.s/GetUserInfo", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *sClient) UpdateUserInfo(ctx context.Context, in *svcaccount.UpdateUserInfoReq, opts ...grpc.CallOption) (*UpdateUserInfoResp, error) {
	out := new(UpdateUserInfoResp)
	err := c.cc.Invoke(ctx, "/vc.bizaccount.s/UpdateUserInfo", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *sClient) FollowUser(ctx context.Context, in *FollowUserReq, opts ...grpc.CallOption) (*FollowUserResp, error) {
	out := new(FollowUserResp)
	err := c.cc.Invoke(ctx, "/vc.bizaccount.s/FollowUser", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *sClient) UnfollowUser(ctx context.Context, in *UnfollowUserReq, opts ...grpc.CallOption) (*UnfollowUserResp, error) {
	out := new(UnfollowUserResp)
	err := c.cc.Invoke(ctx, "/vc.bizaccount.s/UnfollowUser", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *sClient) CheckFollowing(ctx context.Context, in *CheckFollowingReq, opts ...grpc.CallOption) (*CheckFollowingResp, error) {
	out := new(CheckFollowingResp)
	err := c.cc.Invoke(ctx, "/vc.bizaccount.s/CheckFollowing", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *sClient) GetFollowingUsers(ctx context.Context, in *GetFollowingUsersReq, opts ...grpc.CallOption) (*GetFollowingUsersResp, error) {
	out := new(GetFollowingUsersResp)
	err := c.cc.Invoke(ctx, "/vc.bizaccount.s/GetFollowingUsers", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *sClient) GetFollowers(ctx context.Context, in *GetFollowersReq, opts ...grpc.CallOption) (*GetFollowersResp, error) {
	out := new(GetFollowersResp)
	err := c.cc.Invoke(ctx, "/vc.bizaccount.s/GetFollowers", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *sClient) BatchCheckFollowing(ctx context.Context, in *BatchCheckFollowingReq, opts ...grpc.CallOption) (*BatchCheckFollowingResp, error) {
	out := new(BatchCheckFollowingResp)
	err := c.cc.Invoke(ctx, "/vc.bizaccount.s/BatchCheckFollowing", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// SServer is the server API for S service.
// All implementations should embed UnimplementedSServer
// for forward compatibility
type SServer interface {
	// 获取验证码
	GetVerificationCode(context.Context, *svcaccount.GetVerificationCodeReq) (*GetVerificationCodeResp, error)
	// 注册
	AccountSignUp(context.Context, *svcaccount.AccountSignUpReq) (*AccountSignUpResp, error)
	// 登录
	AccountSignIn(context.Context, *svcaccount.AccountSignInReq) (*AccountSignInResp, error)
	// 退出登录
	AccountSignOut(context.Context, *svcaccount.AccountSignOutReq) (*AccountSignOutResp, error)
	// 刷新Token
	RefreshToken(context.Context, *svcaccount.RefreshTokenReq) (*RefreshTokenResp, error)
	// 注销账号
	AccountDelete(context.Context, *svcaccount.AccountDeleteReq) (*AccountDeleteResp, error)
	// 获取用户信息
	GetUserInfo(context.Context, *svcaccount.GetUserInfoReq) (*GetUserInfoResp, error)
	// 更新用户信息
	UpdateUserInfo(context.Context, *svcaccount.UpdateUserInfoReq) (*UpdateUserInfoResp, error)
	// 关注用户
	FollowUser(context.Context, *FollowUserReq) (*FollowUserResp, error)
	// 取消关注用户
	UnfollowUser(context.Context, *UnfollowUserReq) (*UnfollowUserResp, error)
	// 检查是否关注
	CheckFollowing(context.Context, *CheckFollowingReq) (*CheckFollowingResp, error)
	// 获取关注的用户列表
	GetFollowingUsers(context.Context, *GetFollowingUsersReq) (*GetFollowingUsersResp, error)
	// 获取粉丝列表
	GetFollowers(context.Context, *GetFollowersReq) (*GetFollowersResp, error)
	// 批量检查是否关注
	BatchCheckFollowing(context.Context, *BatchCheckFollowingReq) (*BatchCheckFollowingResp, error)
}

// UnimplementedSServer should be embedded to have forward compatible implementations.
type UnimplementedSServer struct {
}

func (UnimplementedSServer) GetVerificationCode(context.Context, *svcaccount.GetVerificationCodeReq) (*GetVerificationCodeResp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetVerificationCode not implemented")
}
func (UnimplementedSServer) AccountSignUp(context.Context, *svcaccount.AccountSignUpReq) (*AccountSignUpResp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method AccountSignUp not implemented")
}
func (UnimplementedSServer) AccountSignIn(context.Context, *svcaccount.AccountSignInReq) (*AccountSignInResp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method AccountSignIn not implemented")
}
func (UnimplementedSServer) AccountSignOut(context.Context, *svcaccount.AccountSignOutReq) (*AccountSignOutResp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method AccountSignOut not implemented")
}
func (UnimplementedSServer) RefreshToken(context.Context, *svcaccount.RefreshTokenReq) (*RefreshTokenResp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method RefreshToken not implemented")
}
func (UnimplementedSServer) AccountDelete(context.Context, *svcaccount.AccountDeleteReq) (*AccountDeleteResp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method AccountDelete not implemented")
}
func (UnimplementedSServer) GetUserInfo(context.Context, *svcaccount.GetUserInfoReq) (*GetUserInfoResp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetUserInfo not implemented")
}
func (UnimplementedSServer) UpdateUserInfo(context.Context, *svcaccount.UpdateUserInfoReq) (*UpdateUserInfoResp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method UpdateUserInfo not implemented")
}
func (UnimplementedSServer) FollowUser(context.Context, *FollowUserReq) (*FollowUserResp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method FollowUser not implemented")
}
func (UnimplementedSServer) UnfollowUser(context.Context, *UnfollowUserReq) (*UnfollowUserResp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method UnfollowUser not implemented")
}
func (UnimplementedSServer) CheckFollowing(context.Context, *CheckFollowingReq) (*CheckFollowingResp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method CheckFollowing not implemented")
}
func (UnimplementedSServer) GetFollowingUsers(context.Context, *GetFollowingUsersReq) (*GetFollowingUsersResp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetFollowingUsers not implemented")
}
func (UnimplementedSServer) GetFollowers(context.Context, *GetFollowersReq) (*GetFollowersResp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetFollowers not implemented")
}
func (UnimplementedSServer) BatchCheckFollowing(context.Context, *BatchCheckFollowingReq) (*BatchCheckFollowingResp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method BatchCheckFollowing not implemented")
}

// UnsafeSServer may be embedded to opt out of forward compatibility for this service.
// Use of this interface is not recommended, as added methods to SServer will
// result in compilation errors.
type UnsafeSServer interface {
	mustEmbedUnimplementedSServer()
}

func RegisterSServer(s grpc.ServiceRegistrar, srv SServer) {
	s.RegisterService(&S_ServiceDesc, srv)
}

func _S_GetVerificationCode_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(svcaccount.GetVerificationCodeReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(SServer).GetVerificationCode(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/vc.bizaccount.s/GetVerificationCode",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(SServer).GetVerificationCode(ctx, req.(*svcaccount.GetVerificationCodeReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _S_AccountSignUp_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(svcaccount.AccountSignUpReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(SServer).AccountSignUp(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/vc.bizaccount.s/AccountSignUp",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(SServer).AccountSignUp(ctx, req.(*svcaccount.AccountSignUpReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _S_AccountSignIn_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(svcaccount.AccountSignInReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(SServer).AccountSignIn(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/vc.bizaccount.s/AccountSignIn",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(SServer).AccountSignIn(ctx, req.(*svcaccount.AccountSignInReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _S_AccountSignOut_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(svcaccount.AccountSignOutReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(SServer).AccountSignOut(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/vc.bizaccount.s/AccountSignOut",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(SServer).AccountSignOut(ctx, req.(*svcaccount.AccountSignOutReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _S_RefreshToken_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(svcaccount.RefreshTokenReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(SServer).RefreshToken(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/vc.bizaccount.s/RefreshToken",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(SServer).RefreshToken(ctx, req.(*svcaccount.RefreshTokenReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _S_AccountDelete_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(svcaccount.AccountDeleteReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(SServer).AccountDelete(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/vc.bizaccount.s/AccountDelete",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(SServer).AccountDelete(ctx, req.(*svcaccount.AccountDeleteReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _S_GetUserInfo_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(svcaccount.GetUserInfoReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(SServer).GetUserInfo(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/vc.bizaccount.s/GetUserInfo",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(SServer).GetUserInfo(ctx, req.(*svcaccount.GetUserInfoReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _S_UpdateUserInfo_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(svcaccount.UpdateUserInfoReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(SServer).UpdateUserInfo(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/vc.bizaccount.s/UpdateUserInfo",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(SServer).UpdateUserInfo(ctx, req.(*svcaccount.UpdateUserInfoReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _S_FollowUser_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(FollowUserReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(SServer).FollowUser(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/vc.bizaccount.s/FollowUser",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(SServer).FollowUser(ctx, req.(*FollowUserReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _S_UnfollowUser_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(UnfollowUserReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(SServer).UnfollowUser(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/vc.bizaccount.s/UnfollowUser",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(SServer).UnfollowUser(ctx, req.(*UnfollowUserReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _S_CheckFollowing_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(CheckFollowingReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(SServer).CheckFollowing(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/vc.bizaccount.s/CheckFollowing",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(SServer).CheckFollowing(ctx, req.(*CheckFollowingReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _S_GetFollowingUsers_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetFollowingUsersReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(SServer).GetFollowingUsers(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/vc.bizaccount.s/GetFollowingUsers",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(SServer).GetFollowingUsers(ctx, req.(*GetFollowingUsersReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _S_GetFollowers_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetFollowersReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(SServer).GetFollowers(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/vc.bizaccount.s/GetFollowers",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(SServer).GetFollowers(ctx, req.(*GetFollowersReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _S_BatchCheckFollowing_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(BatchCheckFollowingReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(SServer).BatchCheckFollowing(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/vc.bizaccount.s/BatchCheckFollowing",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(SServer).BatchCheckFollowing(ctx, req.(*BatchCheckFollowingReq))
	}
	return interceptor(ctx, in, info, handler)
}

// S_ServiceDesc is the grpc.ServiceDesc for S service.
// It's only intended for direct use with grpc.RegisterService,
// and not to be introspected or modified (even as a copy)
var S_ServiceDesc = grpc.ServiceDesc{
	ServiceName: "vc.bizaccount.s",
	HandlerType: (*SServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "GetVerificationCode",
			Handler:    _S_GetVerificationCode_Handler,
		},
		{
			MethodName: "AccountSignUp",
			Handler:    _S_AccountSignUp_Handler,
		},
		{
			MethodName: "AccountSignIn",
			Handler:    _S_AccountSignIn_Handler,
		},
		{
			MethodName: "AccountSignOut",
			Handler:    _S_AccountSignOut_Handler,
		},
		{
			MethodName: "RefreshToken",
			Handler:    _S_RefreshToken_Handler,
		},
		{
			MethodName: "AccountDelete",
			Handler:    _S_AccountDelete_Handler,
		},
		{
			MethodName: "GetUserInfo",
			Handler:    _S_GetUserInfo_Handler,
		},
		{
			MethodName: "UpdateUserInfo",
			Handler:    _S_UpdateUserInfo_Handler,
		},
		{
			MethodName: "FollowUser",
			Handler:    _S_FollowUser_Handler,
		},
		{
			MethodName: "UnfollowUser",
			Handler:    _S_UnfollowUser_Handler,
		},
		{
			MethodName: "CheckFollowing",
			Handler:    _S_CheckFollowing_Handler,
		},
		{
			MethodName: "GetFollowingUsers",
			Handler:    _S_GetFollowingUsers_Handler,
		},
		{
			MethodName: "GetFollowers",
			Handler:    _S_GetFollowers_Handler,
		},
		{
			MethodName: "BatchCheckFollowing",
			Handler:    _S_BatchCheckFollowing_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "bizaccount.proto",
}
