// Code generated by protoc-gen-validate. DO NOT EDIT.
// source: bizaccount.proto

package bizaccount

import (
	"bytes"
	"errors"
	"fmt"
	"net"
	"net/mail"
	"net/url"
	"regexp"
	"sort"
	"strings"
	"time"
	"unicode/utf8"

	"google.golang.org/protobuf/types/known/anypb"
)

// ensure the imports are used
var (
	_ = bytes.MinRead
	_ = errors.New("")
	_ = fmt.Print
	_ = utf8.UTFMax
	_ = (*regexp.Regexp)(nil)
	_ = (*strings.Reader)(nil)
	_ = net.IPv4len
	_ = time.Duration(0)
	_ = (*url.URL)(nil)
	_ = (*mail.Address)(nil)
	_ = anypb.Any{}
	_ = sort.Sort
)

// Validate checks the field values on GetVerificationCodeResp with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *GetVerificationCodeResp) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GetVerificationCodeResp with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// GetVerificationCodeRespMultiError, or nil if none found.
func (m *GetVerificationCodeResp) ValidateAll() error {
	return m.validate(true)
}

func (m *GetVerificationCodeResp) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Code

	// no validation rules for Msg

	if all {
		switch v := interface{}(m.GetData()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetVerificationCodeRespValidationError{
					field:  "Data",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetVerificationCodeRespValidationError{
					field:  "Data",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetData()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetVerificationCodeRespValidationError{
				field:  "Data",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return GetVerificationCodeRespMultiError(errors)
	}

	return nil
}

// GetVerificationCodeRespMultiError is an error wrapping multiple validation
// errors returned by GetVerificationCodeResp.ValidateAll() if the designated
// constraints aren't met.
type GetVerificationCodeRespMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetVerificationCodeRespMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetVerificationCodeRespMultiError) AllErrors() []error { return m }

// GetVerificationCodeRespValidationError is the validation error returned by
// GetVerificationCodeResp.Validate if the designated constraints aren't met.
type GetVerificationCodeRespValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetVerificationCodeRespValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetVerificationCodeRespValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetVerificationCodeRespValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetVerificationCodeRespValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetVerificationCodeRespValidationError) ErrorName() string {
	return "GetVerificationCodeRespValidationError"
}

// Error satisfies the builtin error interface
func (e GetVerificationCodeRespValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetVerificationCodeResp.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetVerificationCodeRespValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetVerificationCodeRespValidationError{}

// Validate checks the field values on AccountSignUpResp with the rules defined
// in the proto definition for this message. If any rules are violated, the
// first error encountered is returned, or nil if there are no violations.
func (m *AccountSignUpResp) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on AccountSignUpResp with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// AccountSignUpRespMultiError, or nil if none found.
func (m *AccountSignUpResp) ValidateAll() error {
	return m.validate(true)
}

func (m *AccountSignUpResp) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Code

	// no validation rules for Msg

	if all {
		switch v := interface{}(m.GetData()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, AccountSignUpRespValidationError{
					field:  "Data",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, AccountSignUpRespValidationError{
					field:  "Data",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetData()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return AccountSignUpRespValidationError{
				field:  "Data",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return AccountSignUpRespMultiError(errors)
	}

	return nil
}

// AccountSignUpRespMultiError is an error wrapping multiple validation errors
// returned by AccountSignUpResp.ValidateAll() if the designated constraints
// aren't met.
type AccountSignUpRespMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m AccountSignUpRespMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m AccountSignUpRespMultiError) AllErrors() []error { return m }

// AccountSignUpRespValidationError is the validation error returned by
// AccountSignUpResp.Validate if the designated constraints aren't met.
type AccountSignUpRespValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e AccountSignUpRespValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e AccountSignUpRespValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e AccountSignUpRespValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e AccountSignUpRespValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e AccountSignUpRespValidationError) ErrorName() string {
	return "AccountSignUpRespValidationError"
}

// Error satisfies the builtin error interface
func (e AccountSignUpRespValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sAccountSignUpResp.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = AccountSignUpRespValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = AccountSignUpRespValidationError{}

// Validate checks the field values on AccountSignInResp with the rules defined
// in the proto definition for this message. If any rules are violated, the
// first error encountered is returned, or nil if there are no violations.
func (m *AccountSignInResp) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on AccountSignInResp with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// AccountSignInRespMultiError, or nil if none found.
func (m *AccountSignInResp) ValidateAll() error {
	return m.validate(true)
}

func (m *AccountSignInResp) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Code

	// no validation rules for Msg

	if all {
		switch v := interface{}(m.GetData()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, AccountSignInRespValidationError{
					field:  "Data",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, AccountSignInRespValidationError{
					field:  "Data",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetData()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return AccountSignInRespValidationError{
				field:  "Data",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return AccountSignInRespMultiError(errors)
	}

	return nil
}

// AccountSignInRespMultiError is an error wrapping multiple validation errors
// returned by AccountSignInResp.ValidateAll() if the designated constraints
// aren't met.
type AccountSignInRespMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m AccountSignInRespMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m AccountSignInRespMultiError) AllErrors() []error { return m }

// AccountSignInRespValidationError is the validation error returned by
// AccountSignInResp.Validate if the designated constraints aren't met.
type AccountSignInRespValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e AccountSignInRespValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e AccountSignInRespValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e AccountSignInRespValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e AccountSignInRespValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e AccountSignInRespValidationError) ErrorName() string {
	return "AccountSignInRespValidationError"
}

// Error satisfies the builtin error interface
func (e AccountSignInRespValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sAccountSignInResp.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = AccountSignInRespValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = AccountSignInRespValidationError{}

// Validate checks the field values on AccountSignOutResp with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *AccountSignOutResp) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on AccountSignOutResp with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// AccountSignOutRespMultiError, or nil if none found.
func (m *AccountSignOutResp) ValidateAll() error {
	return m.validate(true)
}

func (m *AccountSignOutResp) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Code

	// no validation rules for Msg

	if len(errors) > 0 {
		return AccountSignOutRespMultiError(errors)
	}

	return nil
}

// AccountSignOutRespMultiError is an error wrapping multiple validation errors
// returned by AccountSignOutResp.ValidateAll() if the designated constraints
// aren't met.
type AccountSignOutRespMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m AccountSignOutRespMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m AccountSignOutRespMultiError) AllErrors() []error { return m }

// AccountSignOutRespValidationError is the validation error returned by
// AccountSignOutResp.Validate if the designated constraints aren't met.
type AccountSignOutRespValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e AccountSignOutRespValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e AccountSignOutRespValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e AccountSignOutRespValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e AccountSignOutRespValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e AccountSignOutRespValidationError) ErrorName() string {
	return "AccountSignOutRespValidationError"
}

// Error satisfies the builtin error interface
func (e AccountSignOutRespValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sAccountSignOutResp.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = AccountSignOutRespValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = AccountSignOutRespValidationError{}

// Validate checks the field values on RefreshTokenResp with the rules defined
// in the proto definition for this message. If any rules are violated, the
// first error encountered is returned, or nil if there are no violations.
func (m *RefreshTokenResp) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on RefreshTokenResp with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// RefreshTokenRespMultiError, or nil if none found.
func (m *RefreshTokenResp) ValidateAll() error {
	return m.validate(true)
}

func (m *RefreshTokenResp) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Code

	// no validation rules for Msg

	if all {
		switch v := interface{}(m.GetData()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, RefreshTokenRespValidationError{
					field:  "Data",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, RefreshTokenRespValidationError{
					field:  "Data",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetData()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return RefreshTokenRespValidationError{
				field:  "Data",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return RefreshTokenRespMultiError(errors)
	}

	return nil
}

// RefreshTokenRespMultiError is an error wrapping multiple validation errors
// returned by RefreshTokenResp.ValidateAll() if the designated constraints
// aren't met.
type RefreshTokenRespMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m RefreshTokenRespMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m RefreshTokenRespMultiError) AllErrors() []error { return m }

// RefreshTokenRespValidationError is the validation error returned by
// RefreshTokenResp.Validate if the designated constraints aren't met.
type RefreshTokenRespValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e RefreshTokenRespValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e RefreshTokenRespValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e RefreshTokenRespValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e RefreshTokenRespValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e RefreshTokenRespValidationError) ErrorName() string { return "RefreshTokenRespValidationError" }

// Error satisfies the builtin error interface
func (e RefreshTokenRespValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sRefreshTokenResp.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = RefreshTokenRespValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = RefreshTokenRespValidationError{}

// Validate checks the field values on AccountDeleteResp with the rules defined
// in the proto definition for this message. If any rules are violated, the
// first error encountered is returned, or nil if there are no violations.
func (m *AccountDeleteResp) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on AccountDeleteResp with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// AccountDeleteRespMultiError, or nil if none found.
func (m *AccountDeleteResp) ValidateAll() error {
	return m.validate(true)
}

func (m *AccountDeleteResp) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Code

	// no validation rules for Msg

	if len(errors) > 0 {
		return AccountDeleteRespMultiError(errors)
	}

	return nil
}

// AccountDeleteRespMultiError is an error wrapping multiple validation errors
// returned by AccountDeleteResp.ValidateAll() if the designated constraints
// aren't met.
type AccountDeleteRespMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m AccountDeleteRespMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m AccountDeleteRespMultiError) AllErrors() []error { return m }

// AccountDeleteRespValidationError is the validation error returned by
// AccountDeleteResp.Validate if the designated constraints aren't met.
type AccountDeleteRespValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e AccountDeleteRespValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e AccountDeleteRespValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e AccountDeleteRespValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e AccountDeleteRespValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e AccountDeleteRespValidationError) ErrorName() string {
	return "AccountDeleteRespValidationError"
}

// Error satisfies the builtin error interface
func (e AccountDeleteRespValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sAccountDeleteResp.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = AccountDeleteRespValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = AccountDeleteRespValidationError{}

// Validate checks the field values on GetUserInfoResp with the rules defined
// in the proto definition for this message. If any rules are violated, the
// first error encountered is returned, or nil if there are no violations.
func (m *GetUserInfoResp) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GetUserInfoResp with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// GetUserInfoRespMultiError, or nil if none found.
func (m *GetUserInfoResp) ValidateAll() error {
	return m.validate(true)
}

func (m *GetUserInfoResp) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Code

	// no validation rules for Msg

	if all {
		switch v := interface{}(m.GetData()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetUserInfoRespValidationError{
					field:  "Data",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetUserInfoRespValidationError{
					field:  "Data",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetData()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetUserInfoRespValidationError{
				field:  "Data",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return GetUserInfoRespMultiError(errors)
	}

	return nil
}

// GetUserInfoRespMultiError is an error wrapping multiple validation errors
// returned by GetUserInfoResp.ValidateAll() if the designated constraints
// aren't met.
type GetUserInfoRespMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetUserInfoRespMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetUserInfoRespMultiError) AllErrors() []error { return m }

// GetUserInfoRespValidationError is the validation error returned by
// GetUserInfoResp.Validate if the designated constraints aren't met.
type GetUserInfoRespValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetUserInfoRespValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetUserInfoRespValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetUserInfoRespValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetUserInfoRespValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetUserInfoRespValidationError) ErrorName() string { return "GetUserInfoRespValidationError" }

// Error satisfies the builtin error interface
func (e GetUserInfoRespValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetUserInfoResp.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetUserInfoRespValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetUserInfoRespValidationError{}

// Validate checks the field values on UpdateUserInfoResp with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *UpdateUserInfoResp) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on UpdateUserInfoResp with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// UpdateUserInfoRespMultiError, or nil if none found.
func (m *UpdateUserInfoResp) ValidateAll() error {
	return m.validate(true)
}

func (m *UpdateUserInfoResp) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Code

	// no validation rules for Msg

	if len(errors) > 0 {
		return UpdateUserInfoRespMultiError(errors)
	}

	return nil
}

// UpdateUserInfoRespMultiError is an error wrapping multiple validation errors
// returned by UpdateUserInfoResp.ValidateAll() if the designated constraints
// aren't met.
type UpdateUserInfoRespMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m UpdateUserInfoRespMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m UpdateUserInfoRespMultiError) AllErrors() []error { return m }

// UpdateUserInfoRespValidationError is the validation error returned by
// UpdateUserInfoResp.Validate if the designated constraints aren't met.
type UpdateUserInfoRespValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e UpdateUserInfoRespValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e UpdateUserInfoRespValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e UpdateUserInfoRespValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e UpdateUserInfoRespValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e UpdateUserInfoRespValidationError) ErrorName() string {
	return "UpdateUserInfoRespValidationError"
}

// Error satisfies the builtin error interface
func (e UpdateUserInfoRespValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sUpdateUserInfoResp.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = UpdateUserInfoRespValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = UpdateUserInfoRespValidationError{}

// Validate checks the field values on FollowUserReq with the rules defined in
// the proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *FollowUserReq) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on FollowUserReq with the rules defined
// in the proto definition for this message. If any rules are violated, the
// result is a list of violation errors wrapped in FollowUserReqMultiError, or
// nil if none found.
func (m *FollowUserReq) ValidateAll() error {
	return m.validate(true)
}

func (m *FollowUserReq) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if m.GetFollowingId() <= 0 {
		err := FollowUserReqValidationError{
			field:  "FollowingId",
			reason: "value must be greater than 0",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if len(errors) > 0 {
		return FollowUserReqMultiError(errors)
	}

	return nil
}

// FollowUserReqMultiError is an error wrapping multiple validation errors
// returned by FollowUserReq.ValidateAll() if the designated constraints
// aren't met.
type FollowUserReqMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m FollowUserReqMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m FollowUserReqMultiError) AllErrors() []error { return m }

// FollowUserReqValidationError is the validation error returned by
// FollowUserReq.Validate if the designated constraints aren't met.
type FollowUserReqValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e FollowUserReqValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e FollowUserReqValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e FollowUserReqValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e FollowUserReqValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e FollowUserReqValidationError) ErrorName() string { return "FollowUserReqValidationError" }

// Error satisfies the builtin error interface
func (e FollowUserReqValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sFollowUserReq.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = FollowUserReqValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = FollowUserReqValidationError{}

// Validate checks the field values on FollowUserResp with the rules defined in
// the proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *FollowUserResp) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on FollowUserResp with the rules defined
// in the proto definition for this message. If any rules are violated, the
// result is a list of violation errors wrapped in FollowUserRespMultiError,
// or nil if none found.
func (m *FollowUserResp) ValidateAll() error {
	return m.validate(true)
}

func (m *FollowUserResp) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Code

	// no validation rules for Msg

	if len(errors) > 0 {
		return FollowUserRespMultiError(errors)
	}

	return nil
}

// FollowUserRespMultiError is an error wrapping multiple validation errors
// returned by FollowUserResp.ValidateAll() if the designated constraints
// aren't met.
type FollowUserRespMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m FollowUserRespMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m FollowUserRespMultiError) AllErrors() []error { return m }

// FollowUserRespValidationError is the validation error returned by
// FollowUserResp.Validate if the designated constraints aren't met.
type FollowUserRespValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e FollowUserRespValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e FollowUserRespValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e FollowUserRespValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e FollowUserRespValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e FollowUserRespValidationError) ErrorName() string { return "FollowUserRespValidationError" }

// Error satisfies the builtin error interface
func (e FollowUserRespValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sFollowUserResp.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = FollowUserRespValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = FollowUserRespValidationError{}

// Validate checks the field values on UnfollowUserReq with the rules defined
// in the proto definition for this message. If any rules are violated, the
// first error encountered is returned, or nil if there are no violations.
func (m *UnfollowUserReq) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on UnfollowUserReq with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// UnfollowUserReqMultiError, or nil if none found.
func (m *UnfollowUserReq) ValidateAll() error {
	return m.validate(true)
}

func (m *UnfollowUserReq) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if m.GetFollowingId() <= 0 {
		err := UnfollowUserReqValidationError{
			field:  "FollowingId",
			reason: "value must be greater than 0",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if len(errors) > 0 {
		return UnfollowUserReqMultiError(errors)
	}

	return nil
}

// UnfollowUserReqMultiError is an error wrapping multiple validation errors
// returned by UnfollowUserReq.ValidateAll() if the designated constraints
// aren't met.
type UnfollowUserReqMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m UnfollowUserReqMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m UnfollowUserReqMultiError) AllErrors() []error { return m }

// UnfollowUserReqValidationError is the validation error returned by
// UnfollowUserReq.Validate if the designated constraints aren't met.
type UnfollowUserReqValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e UnfollowUserReqValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e UnfollowUserReqValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e UnfollowUserReqValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e UnfollowUserReqValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e UnfollowUserReqValidationError) ErrorName() string { return "UnfollowUserReqValidationError" }

// Error satisfies the builtin error interface
func (e UnfollowUserReqValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sUnfollowUserReq.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = UnfollowUserReqValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = UnfollowUserReqValidationError{}

// Validate checks the field values on UnfollowUserResp with the rules defined
// in the proto definition for this message. If any rules are violated, the
// first error encountered is returned, or nil if there are no violations.
func (m *UnfollowUserResp) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on UnfollowUserResp with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// UnfollowUserRespMultiError, or nil if none found.
func (m *UnfollowUserResp) ValidateAll() error {
	return m.validate(true)
}

func (m *UnfollowUserResp) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Code

	// no validation rules for Msg

	if len(errors) > 0 {
		return UnfollowUserRespMultiError(errors)
	}

	return nil
}

// UnfollowUserRespMultiError is an error wrapping multiple validation errors
// returned by UnfollowUserResp.ValidateAll() if the designated constraints
// aren't met.
type UnfollowUserRespMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m UnfollowUserRespMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m UnfollowUserRespMultiError) AllErrors() []error { return m }

// UnfollowUserRespValidationError is the validation error returned by
// UnfollowUserResp.Validate if the designated constraints aren't met.
type UnfollowUserRespValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e UnfollowUserRespValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e UnfollowUserRespValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e UnfollowUserRespValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e UnfollowUserRespValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e UnfollowUserRespValidationError) ErrorName() string { return "UnfollowUserRespValidationError" }

// Error satisfies the builtin error interface
func (e UnfollowUserRespValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sUnfollowUserResp.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = UnfollowUserRespValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = UnfollowUserRespValidationError{}

// Validate checks the field values on CheckFollowingReq with the rules defined
// in the proto definition for this message. If any rules are violated, the
// first error encountered is returned, or nil if there are no violations.
func (m *CheckFollowingReq) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on CheckFollowingReq with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// CheckFollowingReqMultiError, or nil if none found.
func (m *CheckFollowingReq) ValidateAll() error {
	return m.validate(true)
}

func (m *CheckFollowingReq) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if m.GetFollowingId() <= 0 {
		err := CheckFollowingReqValidationError{
			field:  "FollowingId",
			reason: "value must be greater than 0",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if len(errors) > 0 {
		return CheckFollowingReqMultiError(errors)
	}

	return nil
}

// CheckFollowingReqMultiError is an error wrapping multiple validation errors
// returned by CheckFollowingReq.ValidateAll() if the designated constraints
// aren't met.
type CheckFollowingReqMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m CheckFollowingReqMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m CheckFollowingReqMultiError) AllErrors() []error { return m }

// CheckFollowingReqValidationError is the validation error returned by
// CheckFollowingReq.Validate if the designated constraints aren't met.
type CheckFollowingReqValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e CheckFollowingReqValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e CheckFollowingReqValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e CheckFollowingReqValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e CheckFollowingReqValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e CheckFollowingReqValidationError) ErrorName() string {
	return "CheckFollowingReqValidationError"
}

// Error satisfies the builtin error interface
func (e CheckFollowingReqValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sCheckFollowingReq.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = CheckFollowingReqValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = CheckFollowingReqValidationError{}

// Validate checks the field values on CheckFollowingResp with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *CheckFollowingResp) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on CheckFollowingResp with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// CheckFollowingRespMultiError, or nil if none found.
func (m *CheckFollowingResp) ValidateAll() error {
	return m.validate(true)
}

func (m *CheckFollowingResp) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Code

	// no validation rules for Msg

	// no validation rules for IsFollowing

	if len(errors) > 0 {
		return CheckFollowingRespMultiError(errors)
	}

	return nil
}

// CheckFollowingRespMultiError is an error wrapping multiple validation errors
// returned by CheckFollowingResp.ValidateAll() if the designated constraints
// aren't met.
type CheckFollowingRespMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m CheckFollowingRespMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m CheckFollowingRespMultiError) AllErrors() []error { return m }

// CheckFollowingRespValidationError is the validation error returned by
// CheckFollowingResp.Validate if the designated constraints aren't met.
type CheckFollowingRespValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e CheckFollowingRespValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e CheckFollowingRespValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e CheckFollowingRespValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e CheckFollowingRespValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e CheckFollowingRespValidationError) ErrorName() string {
	return "CheckFollowingRespValidationError"
}

// Error satisfies the builtin error interface
func (e CheckFollowingRespValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sCheckFollowingResp.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = CheckFollowingRespValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = CheckFollowingRespValidationError{}

// Validate checks the field values on GetFollowingUsersReq with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *GetFollowingUsersReq) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GetFollowingUsersReq with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// GetFollowingUsersReqMultiError, or nil if none found.
func (m *GetFollowingUsersReq) ValidateAll() error {
	return m.validate(true)
}

func (m *GetFollowingUsersReq) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Page

	// no validation rules for PageSize

	if len(errors) > 0 {
		return GetFollowingUsersReqMultiError(errors)
	}

	return nil
}

// GetFollowingUsersReqMultiError is an error wrapping multiple validation
// errors returned by GetFollowingUsersReq.ValidateAll() if the designated
// constraints aren't met.
type GetFollowingUsersReqMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetFollowingUsersReqMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetFollowingUsersReqMultiError) AllErrors() []error { return m }

// GetFollowingUsersReqValidationError is the validation error returned by
// GetFollowingUsersReq.Validate if the designated constraints aren't met.
type GetFollowingUsersReqValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetFollowingUsersReqValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetFollowingUsersReqValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetFollowingUsersReqValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetFollowingUsersReqValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetFollowingUsersReqValidationError) ErrorName() string {
	return "GetFollowingUsersReqValidationError"
}

// Error satisfies the builtin error interface
func (e GetFollowingUsersReqValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetFollowingUsersReq.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetFollowingUsersReqValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetFollowingUsersReqValidationError{}

// Validate checks the field values on GetFollowingUsersResp with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *GetFollowingUsersResp) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GetFollowingUsersResp with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// GetFollowingUsersRespMultiError, or nil if none found.
func (m *GetFollowingUsersResp) ValidateAll() error {
	return m.validate(true)
}

func (m *GetFollowingUsersResp) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Code

	// no validation rules for Msg

	if all {
		switch v := interface{}(m.GetData()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetFollowingUsersRespValidationError{
					field:  "Data",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetFollowingUsersRespValidationError{
					field:  "Data",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetData()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetFollowingUsersRespValidationError{
				field:  "Data",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return GetFollowingUsersRespMultiError(errors)
	}

	return nil
}

// GetFollowingUsersRespMultiError is an error wrapping multiple validation
// errors returned by GetFollowingUsersResp.ValidateAll() if the designated
// constraints aren't met.
type GetFollowingUsersRespMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetFollowingUsersRespMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetFollowingUsersRespMultiError) AllErrors() []error { return m }

// GetFollowingUsersRespValidationError is the validation error returned by
// GetFollowingUsersResp.Validate if the designated constraints aren't met.
type GetFollowingUsersRespValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetFollowingUsersRespValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetFollowingUsersRespValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetFollowingUsersRespValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetFollowingUsersRespValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetFollowingUsersRespValidationError) ErrorName() string {
	return "GetFollowingUsersRespValidationError"
}

// Error satisfies the builtin error interface
func (e GetFollowingUsersRespValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetFollowingUsersResp.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetFollowingUsersRespValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetFollowingUsersRespValidationError{}

// Validate checks the field values on GetFollowingUsersRespData with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *GetFollowingUsersRespData) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GetFollowingUsersRespData with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// GetFollowingUsersRespDataMultiError, or nil if none found.
func (m *GetFollowingUsersRespData) ValidateAll() error {
	return m.validate(true)
}

func (m *GetFollowingUsersRespData) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	for idx, item := range m.GetUsers() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, GetFollowingUsersRespDataValidationError{
						field:  fmt.Sprintf("Users[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, GetFollowingUsersRespDataValidationError{
						field:  fmt.Sprintf("Users[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return GetFollowingUsersRespDataValidationError{
					field:  fmt.Sprintf("Users[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	// no validation rules for Total

	if len(errors) > 0 {
		return GetFollowingUsersRespDataMultiError(errors)
	}

	return nil
}

// GetFollowingUsersRespDataMultiError is an error wrapping multiple validation
// errors returned by GetFollowingUsersRespData.ValidateAll() if the
// designated constraints aren't met.
type GetFollowingUsersRespDataMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetFollowingUsersRespDataMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetFollowingUsersRespDataMultiError) AllErrors() []error { return m }

// GetFollowingUsersRespDataValidationError is the validation error returned by
// GetFollowingUsersRespData.Validate if the designated constraints aren't met.
type GetFollowingUsersRespDataValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetFollowingUsersRespDataValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetFollowingUsersRespDataValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetFollowingUsersRespDataValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetFollowingUsersRespDataValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetFollowingUsersRespDataValidationError) ErrorName() string {
	return "GetFollowingUsersRespDataValidationError"
}

// Error satisfies the builtin error interface
func (e GetFollowingUsersRespDataValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetFollowingUsersRespData.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetFollowingUsersRespDataValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetFollowingUsersRespDataValidationError{}

// Validate checks the field values on GetFollowersReq with the rules defined
// in the proto definition for this message. If any rules are violated, the
// first error encountered is returned, or nil if there are no violations.
func (m *GetFollowersReq) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GetFollowersReq with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// GetFollowersReqMultiError, or nil if none found.
func (m *GetFollowersReq) ValidateAll() error {
	return m.validate(true)
}

func (m *GetFollowersReq) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Page

	// no validation rules for PageSize

	if len(errors) > 0 {
		return GetFollowersReqMultiError(errors)
	}

	return nil
}

// GetFollowersReqMultiError is an error wrapping multiple validation errors
// returned by GetFollowersReq.ValidateAll() if the designated constraints
// aren't met.
type GetFollowersReqMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetFollowersReqMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetFollowersReqMultiError) AllErrors() []error { return m }

// GetFollowersReqValidationError is the validation error returned by
// GetFollowersReq.Validate if the designated constraints aren't met.
type GetFollowersReqValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetFollowersReqValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetFollowersReqValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetFollowersReqValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetFollowersReqValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetFollowersReqValidationError) ErrorName() string { return "GetFollowersReqValidationError" }

// Error satisfies the builtin error interface
func (e GetFollowersReqValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetFollowersReq.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetFollowersReqValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetFollowersReqValidationError{}

// Validate checks the field values on GetFollowersResp with the rules defined
// in the proto definition for this message. If any rules are violated, the
// first error encountered is returned, or nil if there are no violations.
func (m *GetFollowersResp) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GetFollowersResp with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// GetFollowersRespMultiError, or nil if none found.
func (m *GetFollowersResp) ValidateAll() error {
	return m.validate(true)
}

func (m *GetFollowersResp) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Code

	// no validation rules for Msg

	if all {
		switch v := interface{}(m.GetData()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetFollowersRespValidationError{
					field:  "Data",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetFollowersRespValidationError{
					field:  "Data",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetData()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetFollowersRespValidationError{
				field:  "Data",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return GetFollowersRespMultiError(errors)
	}

	return nil
}

// GetFollowersRespMultiError is an error wrapping multiple validation errors
// returned by GetFollowersResp.ValidateAll() if the designated constraints
// aren't met.
type GetFollowersRespMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetFollowersRespMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetFollowersRespMultiError) AllErrors() []error { return m }

// GetFollowersRespValidationError is the validation error returned by
// GetFollowersResp.Validate if the designated constraints aren't met.
type GetFollowersRespValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetFollowersRespValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetFollowersRespValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetFollowersRespValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetFollowersRespValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetFollowersRespValidationError) ErrorName() string { return "GetFollowersRespValidationError" }

// Error satisfies the builtin error interface
func (e GetFollowersRespValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetFollowersResp.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetFollowersRespValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetFollowersRespValidationError{}

// Validate checks the field values on GetFollowersRespData with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *GetFollowersRespData) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GetFollowersRespData with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// GetFollowersRespDataMultiError, or nil if none found.
func (m *GetFollowersRespData) ValidateAll() error {
	return m.validate(true)
}

func (m *GetFollowersRespData) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	for idx, item := range m.GetUsers() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, GetFollowersRespDataValidationError{
						field:  fmt.Sprintf("Users[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, GetFollowersRespDataValidationError{
						field:  fmt.Sprintf("Users[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return GetFollowersRespDataValidationError{
					field:  fmt.Sprintf("Users[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	// no validation rules for Total

	if len(errors) > 0 {
		return GetFollowersRespDataMultiError(errors)
	}

	return nil
}

// GetFollowersRespDataMultiError is an error wrapping multiple validation
// errors returned by GetFollowersRespData.ValidateAll() if the designated
// constraints aren't met.
type GetFollowersRespDataMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetFollowersRespDataMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetFollowersRespDataMultiError) AllErrors() []error { return m }

// GetFollowersRespDataValidationError is the validation error returned by
// GetFollowersRespData.Validate if the designated constraints aren't met.
type GetFollowersRespDataValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetFollowersRespDataValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetFollowersRespDataValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetFollowersRespDataValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetFollowersRespDataValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetFollowersRespDataValidationError) ErrorName() string {
	return "GetFollowersRespDataValidationError"
}

// Error satisfies the builtin error interface
func (e GetFollowersRespDataValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetFollowersRespData.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetFollowersRespDataValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetFollowersRespDataValidationError{}

// Validate checks the field values on BatchCheckFollowingReq with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *BatchCheckFollowingReq) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on BatchCheckFollowingReq with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// BatchCheckFollowingReqMultiError, or nil if none found.
func (m *BatchCheckFollowingReq) ValidateAll() error {
	return m.validate(true)
}

func (m *BatchCheckFollowingReq) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if l := len(m.GetFollowingIds()); l < 1 || l > 1000 {
		err := BatchCheckFollowingReqValidationError{
			field:  "FollowingIds",
			reason: "value must contain between 1 and 1000 items, inclusive",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if len(errors) > 0 {
		return BatchCheckFollowingReqMultiError(errors)
	}

	return nil
}

// BatchCheckFollowingReqMultiError is an error wrapping multiple validation
// errors returned by BatchCheckFollowingReq.ValidateAll() if the designated
// constraints aren't met.
type BatchCheckFollowingReqMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m BatchCheckFollowingReqMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m BatchCheckFollowingReqMultiError) AllErrors() []error { return m }

// BatchCheckFollowingReqValidationError is the validation error returned by
// BatchCheckFollowingReq.Validate if the designated constraints aren't met.
type BatchCheckFollowingReqValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e BatchCheckFollowingReqValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e BatchCheckFollowingReqValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e BatchCheckFollowingReqValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e BatchCheckFollowingReqValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e BatchCheckFollowingReqValidationError) ErrorName() string {
	return "BatchCheckFollowingReqValidationError"
}

// Error satisfies the builtin error interface
func (e BatchCheckFollowingReqValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sBatchCheckFollowingReq.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = BatchCheckFollowingReqValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = BatchCheckFollowingReqValidationError{}

// Validate checks the field values on BatchCheckFollowingResp with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *BatchCheckFollowingResp) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on BatchCheckFollowingResp with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// BatchCheckFollowingRespMultiError, or nil if none found.
func (m *BatchCheckFollowingResp) ValidateAll() error {
	return m.validate(true)
}

func (m *BatchCheckFollowingResp) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Code

	// no validation rules for Msg

	if all {
		switch v := interface{}(m.GetData()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, BatchCheckFollowingRespValidationError{
					field:  "Data",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, BatchCheckFollowingRespValidationError{
					field:  "Data",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetData()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return BatchCheckFollowingRespValidationError{
				field:  "Data",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return BatchCheckFollowingRespMultiError(errors)
	}

	return nil
}

// BatchCheckFollowingRespMultiError is an error wrapping multiple validation
// errors returned by BatchCheckFollowingResp.ValidateAll() if the designated
// constraints aren't met.
type BatchCheckFollowingRespMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m BatchCheckFollowingRespMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m BatchCheckFollowingRespMultiError) AllErrors() []error { return m }

// BatchCheckFollowingRespValidationError is the validation error returned by
// BatchCheckFollowingResp.Validate if the designated constraints aren't met.
type BatchCheckFollowingRespValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e BatchCheckFollowingRespValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e BatchCheckFollowingRespValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e BatchCheckFollowingRespValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e BatchCheckFollowingRespValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e BatchCheckFollowingRespValidationError) ErrorName() string {
	return "BatchCheckFollowingRespValidationError"
}

// Error satisfies the builtin error interface
func (e BatchCheckFollowingRespValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sBatchCheckFollowingResp.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = BatchCheckFollowingRespValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = BatchCheckFollowingRespValidationError{}
