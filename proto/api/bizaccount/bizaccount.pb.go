// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.33.0
// 	protoc        v5.29.3
// source: bizaccount.proto

package bizaccount

import (
	_ "github.com/envoyproxy/protoc-gen-validate/validate"
	_ "google.golang.org/genproto/googleapis/api/annotations"
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	svcaccount "new-gitlab.xunlei.cn/vcproject/backends/proto/api/svcaccount"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type GetVerificationCodeResp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Code int32                                   `protobuf:"varint,1,opt,name=code,proto3" json:"code,omitempty"`
	Msg  string                                  `protobuf:"bytes,2,opt,name=msg,proto3" json:"msg,omitempty"`
	Data *svcaccount.GetVerificationCodeRespData `protobuf:"bytes,3,opt,name=data,proto3" json:"data,omitempty"`
}

func (x *GetVerificationCodeResp) Reset() {
	*x = GetVerificationCodeResp{}
	if protoimpl.UnsafeEnabled {
		mi := &file_bizaccount_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetVerificationCodeResp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetVerificationCodeResp) ProtoMessage() {}

func (x *GetVerificationCodeResp) ProtoReflect() protoreflect.Message {
	mi := &file_bizaccount_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetVerificationCodeResp.ProtoReflect.Descriptor instead.
func (*GetVerificationCodeResp) Descriptor() ([]byte, []int) {
	return file_bizaccount_proto_rawDescGZIP(), []int{0}
}

func (x *GetVerificationCodeResp) GetCode() int32 {
	if x != nil {
		return x.Code
	}
	return 0
}

func (x *GetVerificationCodeResp) GetMsg() string {
	if x != nil {
		return x.Msg
	}
	return ""
}

func (x *GetVerificationCodeResp) GetData() *svcaccount.GetVerificationCodeRespData {
	if x != nil {
		return x.Data
	}
	return nil
}

type AccountSignUpResp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Code int32                     `protobuf:"varint,1,opt,name=code,proto3" json:"code,omitempty"`
	Msg  string                    `protobuf:"bytes,2,opt,name=msg,proto3" json:"msg,omitempty"`
	Data *svcaccount.TokenRespData `protobuf:"bytes,3,opt,name=data,proto3" json:"data,omitempty"`
}

func (x *AccountSignUpResp) Reset() {
	*x = AccountSignUpResp{}
	if protoimpl.UnsafeEnabled {
		mi := &file_bizaccount_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *AccountSignUpResp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*AccountSignUpResp) ProtoMessage() {}

func (x *AccountSignUpResp) ProtoReflect() protoreflect.Message {
	mi := &file_bizaccount_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use AccountSignUpResp.ProtoReflect.Descriptor instead.
func (*AccountSignUpResp) Descriptor() ([]byte, []int) {
	return file_bizaccount_proto_rawDescGZIP(), []int{1}
}

func (x *AccountSignUpResp) GetCode() int32 {
	if x != nil {
		return x.Code
	}
	return 0
}

func (x *AccountSignUpResp) GetMsg() string {
	if x != nil {
		return x.Msg
	}
	return ""
}

func (x *AccountSignUpResp) GetData() *svcaccount.TokenRespData {
	if x != nil {
		return x.Data
	}
	return nil
}

type AccountSignInResp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Code int32                     `protobuf:"varint,1,opt,name=code,proto3" json:"code,omitempty"`
	Msg  string                    `protobuf:"bytes,2,opt,name=msg,proto3" json:"msg,omitempty"`
	Data *svcaccount.TokenRespData `protobuf:"bytes,3,opt,name=data,proto3" json:"data,omitempty"`
}

func (x *AccountSignInResp) Reset() {
	*x = AccountSignInResp{}
	if protoimpl.UnsafeEnabled {
		mi := &file_bizaccount_proto_msgTypes[2]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *AccountSignInResp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*AccountSignInResp) ProtoMessage() {}

func (x *AccountSignInResp) ProtoReflect() protoreflect.Message {
	mi := &file_bizaccount_proto_msgTypes[2]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use AccountSignInResp.ProtoReflect.Descriptor instead.
func (*AccountSignInResp) Descriptor() ([]byte, []int) {
	return file_bizaccount_proto_rawDescGZIP(), []int{2}
}

func (x *AccountSignInResp) GetCode() int32 {
	if x != nil {
		return x.Code
	}
	return 0
}

func (x *AccountSignInResp) GetMsg() string {
	if x != nil {
		return x.Msg
	}
	return ""
}

func (x *AccountSignInResp) GetData() *svcaccount.TokenRespData {
	if x != nil {
		return x.Data
	}
	return nil
}

type AccountSignOutResp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Code int32  `protobuf:"varint,1,opt,name=code,proto3" json:"code,omitempty"`
	Msg  string `protobuf:"bytes,2,opt,name=msg,proto3" json:"msg,omitempty"`
}

func (x *AccountSignOutResp) Reset() {
	*x = AccountSignOutResp{}
	if protoimpl.UnsafeEnabled {
		mi := &file_bizaccount_proto_msgTypes[3]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *AccountSignOutResp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*AccountSignOutResp) ProtoMessage() {}

func (x *AccountSignOutResp) ProtoReflect() protoreflect.Message {
	mi := &file_bizaccount_proto_msgTypes[3]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use AccountSignOutResp.ProtoReflect.Descriptor instead.
func (*AccountSignOutResp) Descriptor() ([]byte, []int) {
	return file_bizaccount_proto_rawDescGZIP(), []int{3}
}

func (x *AccountSignOutResp) GetCode() int32 {
	if x != nil {
		return x.Code
	}
	return 0
}

func (x *AccountSignOutResp) GetMsg() string {
	if x != nil {
		return x.Msg
	}
	return ""
}

type RefreshTokenResp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Code int32                     `protobuf:"varint,1,opt,name=code,proto3" json:"code,omitempty"`
	Msg  string                    `protobuf:"bytes,2,opt,name=msg,proto3" json:"msg,omitempty"`
	Data *svcaccount.TokenRespData `protobuf:"bytes,3,opt,name=data,proto3" json:"data,omitempty"`
}

func (x *RefreshTokenResp) Reset() {
	*x = RefreshTokenResp{}
	if protoimpl.UnsafeEnabled {
		mi := &file_bizaccount_proto_msgTypes[4]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *RefreshTokenResp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*RefreshTokenResp) ProtoMessage() {}

func (x *RefreshTokenResp) ProtoReflect() protoreflect.Message {
	mi := &file_bizaccount_proto_msgTypes[4]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use RefreshTokenResp.ProtoReflect.Descriptor instead.
func (*RefreshTokenResp) Descriptor() ([]byte, []int) {
	return file_bizaccount_proto_rawDescGZIP(), []int{4}
}

func (x *RefreshTokenResp) GetCode() int32 {
	if x != nil {
		return x.Code
	}
	return 0
}

func (x *RefreshTokenResp) GetMsg() string {
	if x != nil {
		return x.Msg
	}
	return ""
}

func (x *RefreshTokenResp) GetData() *svcaccount.TokenRespData {
	if x != nil {
		return x.Data
	}
	return nil
}

type AccountDeleteResp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Code int32  `protobuf:"varint,1,opt,name=code,proto3" json:"code,omitempty"`
	Msg  string `protobuf:"bytes,2,opt,name=msg,proto3" json:"msg,omitempty"`
}

func (x *AccountDeleteResp) Reset() {
	*x = AccountDeleteResp{}
	if protoimpl.UnsafeEnabled {
		mi := &file_bizaccount_proto_msgTypes[5]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *AccountDeleteResp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*AccountDeleteResp) ProtoMessage() {}

func (x *AccountDeleteResp) ProtoReflect() protoreflect.Message {
	mi := &file_bizaccount_proto_msgTypes[5]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use AccountDeleteResp.ProtoReflect.Descriptor instead.
func (*AccountDeleteResp) Descriptor() ([]byte, []int) {
	return file_bizaccount_proto_rawDescGZIP(), []int{5}
}

func (x *AccountDeleteResp) GetCode() int32 {
	if x != nil {
		return x.Code
	}
	return 0
}

func (x *AccountDeleteResp) GetMsg() string {
	if x != nil {
		return x.Msg
	}
	return ""
}

// 用户信息响应
type GetUserInfoResp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Code int32                `protobuf:"varint,1,opt,name=code,proto3" json:"code,omitempty"`
	Msg  string               `protobuf:"bytes,2,opt,name=msg,proto3" json:"msg,omitempty"`
	Data *svcaccount.UserInfo `protobuf:"bytes,3,opt,name=data,proto3" json:"data,omitempty"`
}

func (x *GetUserInfoResp) Reset() {
	*x = GetUserInfoResp{}
	if protoimpl.UnsafeEnabled {
		mi := &file_bizaccount_proto_msgTypes[6]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetUserInfoResp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetUserInfoResp) ProtoMessage() {}

func (x *GetUserInfoResp) ProtoReflect() protoreflect.Message {
	mi := &file_bizaccount_proto_msgTypes[6]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetUserInfoResp.ProtoReflect.Descriptor instead.
func (*GetUserInfoResp) Descriptor() ([]byte, []int) {
	return file_bizaccount_proto_rawDescGZIP(), []int{6}
}

func (x *GetUserInfoResp) GetCode() int32 {
	if x != nil {
		return x.Code
	}
	return 0
}

func (x *GetUserInfoResp) GetMsg() string {
	if x != nil {
		return x.Msg
	}
	return ""
}

func (x *GetUserInfoResp) GetData() *svcaccount.UserInfo {
	if x != nil {
		return x.Data
	}
	return nil
}

// 更新用户信息
type UpdateUserInfoResp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Code int32  `protobuf:"varint,1,opt,name=code,proto3" json:"code,omitempty"`
	Msg  string `protobuf:"bytes,2,opt,name=msg,proto3" json:"msg,omitempty"`
}

func (x *UpdateUserInfoResp) Reset() {
	*x = UpdateUserInfoResp{}
	if protoimpl.UnsafeEnabled {
		mi := &file_bizaccount_proto_msgTypes[7]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *UpdateUserInfoResp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UpdateUserInfoResp) ProtoMessage() {}

func (x *UpdateUserInfoResp) ProtoReflect() protoreflect.Message {
	mi := &file_bizaccount_proto_msgTypes[7]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UpdateUserInfoResp.ProtoReflect.Descriptor instead.
func (*UpdateUserInfoResp) Descriptor() ([]byte, []int) {
	return file_bizaccount_proto_rawDescGZIP(), []int{7}
}

func (x *UpdateUserInfoResp) GetCode() int32 {
	if x != nil {
		return x.Code
	}
	return 0
}

func (x *UpdateUserInfoResp) GetMsg() string {
	if x != nil {
		return x.Msg
	}
	return ""
}

// 关注用户请求
type FollowUserReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	FollowingId int64 `protobuf:"varint,1,opt,name=following_id,json=followingId,proto3" json:"following_id,omitempty"` // 被关注者ID
}

func (x *FollowUserReq) Reset() {
	*x = FollowUserReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_bizaccount_proto_msgTypes[8]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *FollowUserReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*FollowUserReq) ProtoMessage() {}

func (x *FollowUserReq) ProtoReflect() protoreflect.Message {
	mi := &file_bizaccount_proto_msgTypes[8]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use FollowUserReq.ProtoReflect.Descriptor instead.
func (*FollowUserReq) Descriptor() ([]byte, []int) {
	return file_bizaccount_proto_rawDescGZIP(), []int{8}
}

func (x *FollowUserReq) GetFollowingId() int64 {
	if x != nil {
		return x.FollowingId
	}
	return 0
}

// 关注用户响应
type FollowUserResp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Code int32  `protobuf:"varint,1,opt,name=code,proto3" json:"code,omitempty"`
	Msg  string `protobuf:"bytes,2,opt,name=msg,proto3" json:"msg,omitempty"`
}

func (x *FollowUserResp) Reset() {
	*x = FollowUserResp{}
	if protoimpl.UnsafeEnabled {
		mi := &file_bizaccount_proto_msgTypes[9]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *FollowUserResp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*FollowUserResp) ProtoMessage() {}

func (x *FollowUserResp) ProtoReflect() protoreflect.Message {
	mi := &file_bizaccount_proto_msgTypes[9]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use FollowUserResp.ProtoReflect.Descriptor instead.
func (*FollowUserResp) Descriptor() ([]byte, []int) {
	return file_bizaccount_proto_rawDescGZIP(), []int{9}
}

func (x *FollowUserResp) GetCode() int32 {
	if x != nil {
		return x.Code
	}
	return 0
}

func (x *FollowUserResp) GetMsg() string {
	if x != nil {
		return x.Msg
	}
	return ""
}

// 取消关注用户请求
type UnfollowUserReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	FollowingId int64 `protobuf:"varint,1,opt,name=following_id,json=followingId,proto3" json:"following_id,omitempty"` // 被关注者ID
}

func (x *UnfollowUserReq) Reset() {
	*x = UnfollowUserReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_bizaccount_proto_msgTypes[10]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *UnfollowUserReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UnfollowUserReq) ProtoMessage() {}

func (x *UnfollowUserReq) ProtoReflect() protoreflect.Message {
	mi := &file_bizaccount_proto_msgTypes[10]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UnfollowUserReq.ProtoReflect.Descriptor instead.
func (*UnfollowUserReq) Descriptor() ([]byte, []int) {
	return file_bizaccount_proto_rawDescGZIP(), []int{10}
}

func (x *UnfollowUserReq) GetFollowingId() int64 {
	if x != nil {
		return x.FollowingId
	}
	return 0
}

// 取消关注用户响应
type UnfollowUserResp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Code int32  `protobuf:"varint,1,opt,name=code,proto3" json:"code,omitempty"`
	Msg  string `protobuf:"bytes,2,opt,name=msg,proto3" json:"msg,omitempty"`
}

func (x *UnfollowUserResp) Reset() {
	*x = UnfollowUserResp{}
	if protoimpl.UnsafeEnabled {
		mi := &file_bizaccount_proto_msgTypes[11]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *UnfollowUserResp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UnfollowUserResp) ProtoMessage() {}

func (x *UnfollowUserResp) ProtoReflect() protoreflect.Message {
	mi := &file_bizaccount_proto_msgTypes[11]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UnfollowUserResp.ProtoReflect.Descriptor instead.
func (*UnfollowUserResp) Descriptor() ([]byte, []int) {
	return file_bizaccount_proto_rawDescGZIP(), []int{11}
}

func (x *UnfollowUserResp) GetCode() int32 {
	if x != nil {
		return x.Code
	}
	return 0
}

func (x *UnfollowUserResp) GetMsg() string {
	if x != nil {
		return x.Msg
	}
	return ""
}

// 检查是否关注请求
type CheckFollowingReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	FollowingId int64 `protobuf:"varint,1,opt,name=following_id,json=followingId,proto3" json:"following_id,omitempty"` // 被关注者ID
}

func (x *CheckFollowingReq) Reset() {
	*x = CheckFollowingReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_bizaccount_proto_msgTypes[12]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CheckFollowingReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CheckFollowingReq) ProtoMessage() {}

func (x *CheckFollowingReq) ProtoReflect() protoreflect.Message {
	mi := &file_bizaccount_proto_msgTypes[12]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CheckFollowingReq.ProtoReflect.Descriptor instead.
func (*CheckFollowingReq) Descriptor() ([]byte, []int) {
	return file_bizaccount_proto_rawDescGZIP(), []int{12}
}

func (x *CheckFollowingReq) GetFollowingId() int64 {
	if x != nil {
		return x.FollowingId
	}
	return 0
}

// 检查是否关注响应
type CheckFollowingResp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Code        int32  `protobuf:"varint,1,opt,name=code,proto3" json:"code,omitempty"`
	Msg         string `protobuf:"bytes,2,opt,name=msg,proto3" json:"msg,omitempty"`
	IsFollowing bool   `protobuf:"varint,3,opt,name=is_following,json=isFollowing,proto3" json:"is_following,omitempty"`
}

func (x *CheckFollowingResp) Reset() {
	*x = CheckFollowingResp{}
	if protoimpl.UnsafeEnabled {
		mi := &file_bizaccount_proto_msgTypes[13]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CheckFollowingResp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CheckFollowingResp) ProtoMessage() {}

func (x *CheckFollowingResp) ProtoReflect() protoreflect.Message {
	mi := &file_bizaccount_proto_msgTypes[13]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CheckFollowingResp.ProtoReflect.Descriptor instead.
func (*CheckFollowingResp) Descriptor() ([]byte, []int) {
	return file_bizaccount_proto_rawDescGZIP(), []int{13}
}

func (x *CheckFollowingResp) GetCode() int32 {
	if x != nil {
		return x.Code
	}
	return 0
}

func (x *CheckFollowingResp) GetMsg() string {
	if x != nil {
		return x.Msg
	}
	return ""
}

func (x *CheckFollowingResp) GetIsFollowing() bool {
	if x != nil {
		return x.IsFollowing
	}
	return false
}

// 获取关注的用户列表请求
type GetFollowingUsersReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Page     int32 `protobuf:"varint,1,opt,name=page,proto3" json:"page,omitempty"`                         // 页码
	PageSize int32 `protobuf:"varint,2,opt,name=page_size,json=pageSize,proto3" json:"page_size,omitempty"` // 每页数量
}

func (x *GetFollowingUsersReq) Reset() {
	*x = GetFollowingUsersReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_bizaccount_proto_msgTypes[14]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetFollowingUsersReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetFollowingUsersReq) ProtoMessage() {}

func (x *GetFollowingUsersReq) ProtoReflect() protoreflect.Message {
	mi := &file_bizaccount_proto_msgTypes[14]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetFollowingUsersReq.ProtoReflect.Descriptor instead.
func (*GetFollowingUsersReq) Descriptor() ([]byte, []int) {
	return file_bizaccount_proto_rawDescGZIP(), []int{14}
}

func (x *GetFollowingUsersReq) GetPage() int32 {
	if x != nil {
		return x.Page
	}
	return 0
}

func (x *GetFollowingUsersReq) GetPageSize() int32 {
	if x != nil {
		return x.PageSize
	}
	return 0
}

// 获取关注的用户列表响应
type GetFollowingUsersResp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Code int32                      `protobuf:"varint,1,opt,name=code,proto3" json:"code,omitempty"`
	Msg  string                     `protobuf:"bytes,2,opt,name=msg,proto3" json:"msg,omitempty"`
	Data *GetFollowingUsersRespData `protobuf:"bytes,3,opt,name=data,proto3" json:"data,omitempty"`
}

func (x *GetFollowingUsersResp) Reset() {
	*x = GetFollowingUsersResp{}
	if protoimpl.UnsafeEnabled {
		mi := &file_bizaccount_proto_msgTypes[15]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetFollowingUsersResp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetFollowingUsersResp) ProtoMessage() {}

func (x *GetFollowingUsersResp) ProtoReflect() protoreflect.Message {
	mi := &file_bizaccount_proto_msgTypes[15]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetFollowingUsersResp.ProtoReflect.Descriptor instead.
func (*GetFollowingUsersResp) Descriptor() ([]byte, []int) {
	return file_bizaccount_proto_rawDescGZIP(), []int{15}
}

func (x *GetFollowingUsersResp) GetCode() int32 {
	if x != nil {
		return x.Code
	}
	return 0
}

func (x *GetFollowingUsersResp) GetMsg() string {
	if x != nil {
		return x.Msg
	}
	return ""
}

func (x *GetFollowingUsersResp) GetData() *GetFollowingUsersRespData {
	if x != nil {
		return x.Data
	}
	return nil
}

// 获取关注的用户列表响应数据
type GetFollowingUsersRespData struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Users []*svcaccount.UserInfo `protobuf:"bytes,1,rep,name=users,proto3" json:"users,omitempty"`
	Total int64                  `protobuf:"varint,2,opt,name=total,proto3" json:"total,omitempty"`
}

func (x *GetFollowingUsersRespData) Reset() {
	*x = GetFollowingUsersRespData{}
	if protoimpl.UnsafeEnabled {
		mi := &file_bizaccount_proto_msgTypes[16]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetFollowingUsersRespData) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetFollowingUsersRespData) ProtoMessage() {}

func (x *GetFollowingUsersRespData) ProtoReflect() protoreflect.Message {
	mi := &file_bizaccount_proto_msgTypes[16]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetFollowingUsersRespData.ProtoReflect.Descriptor instead.
func (*GetFollowingUsersRespData) Descriptor() ([]byte, []int) {
	return file_bizaccount_proto_rawDescGZIP(), []int{16}
}

func (x *GetFollowingUsersRespData) GetUsers() []*svcaccount.UserInfo {
	if x != nil {
		return x.Users
	}
	return nil
}

func (x *GetFollowingUsersRespData) GetTotal() int64 {
	if x != nil {
		return x.Total
	}
	return 0
}

// 获取粉丝列表请求
type GetFollowersReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Page     int32 `protobuf:"varint,1,opt,name=page,proto3" json:"page,omitempty"`                         // 页码
	PageSize int32 `protobuf:"varint,2,opt,name=page_size,json=pageSize,proto3" json:"page_size,omitempty"` // 每页数量
}

func (x *GetFollowersReq) Reset() {
	*x = GetFollowersReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_bizaccount_proto_msgTypes[17]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetFollowersReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetFollowersReq) ProtoMessage() {}

func (x *GetFollowersReq) ProtoReflect() protoreflect.Message {
	mi := &file_bizaccount_proto_msgTypes[17]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetFollowersReq.ProtoReflect.Descriptor instead.
func (*GetFollowersReq) Descriptor() ([]byte, []int) {
	return file_bizaccount_proto_rawDescGZIP(), []int{17}
}

func (x *GetFollowersReq) GetPage() int32 {
	if x != nil {
		return x.Page
	}
	return 0
}

func (x *GetFollowersReq) GetPageSize() int32 {
	if x != nil {
		return x.PageSize
	}
	return 0
}

// 获取粉丝列表响应
type GetFollowersResp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Code int32                 `protobuf:"varint,1,opt,name=code,proto3" json:"code,omitempty"`
	Msg  string                `protobuf:"bytes,2,opt,name=msg,proto3" json:"msg,omitempty"`
	Data *GetFollowersRespData `protobuf:"bytes,3,opt,name=data,proto3" json:"data,omitempty"`
}

func (x *GetFollowersResp) Reset() {
	*x = GetFollowersResp{}
	if protoimpl.UnsafeEnabled {
		mi := &file_bizaccount_proto_msgTypes[18]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetFollowersResp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetFollowersResp) ProtoMessage() {}

func (x *GetFollowersResp) ProtoReflect() protoreflect.Message {
	mi := &file_bizaccount_proto_msgTypes[18]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetFollowersResp.ProtoReflect.Descriptor instead.
func (*GetFollowersResp) Descriptor() ([]byte, []int) {
	return file_bizaccount_proto_rawDescGZIP(), []int{18}
}

func (x *GetFollowersResp) GetCode() int32 {
	if x != nil {
		return x.Code
	}
	return 0
}

func (x *GetFollowersResp) GetMsg() string {
	if x != nil {
		return x.Msg
	}
	return ""
}

func (x *GetFollowersResp) GetData() *GetFollowersRespData {
	if x != nil {
		return x.Data
	}
	return nil
}

// 获取粉丝列表响应数据
type GetFollowersRespData struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Users []*svcaccount.UserInfo `protobuf:"bytes,1,rep,name=users,proto3" json:"users,omitempty"`
	Total int64                  `protobuf:"varint,2,opt,name=total,proto3" json:"total,omitempty"`
}

func (x *GetFollowersRespData) Reset() {
	*x = GetFollowersRespData{}
	if protoimpl.UnsafeEnabled {
		mi := &file_bizaccount_proto_msgTypes[19]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetFollowersRespData) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetFollowersRespData) ProtoMessage() {}

func (x *GetFollowersRespData) ProtoReflect() protoreflect.Message {
	mi := &file_bizaccount_proto_msgTypes[19]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetFollowersRespData.ProtoReflect.Descriptor instead.
func (*GetFollowersRespData) Descriptor() ([]byte, []int) {
	return file_bizaccount_proto_rawDescGZIP(), []int{19}
}

func (x *GetFollowersRespData) GetUsers() []*svcaccount.UserInfo {
	if x != nil {
		return x.Users
	}
	return nil
}

func (x *GetFollowersRespData) GetTotal() int64 {
	if x != nil {
		return x.Total
	}
	return 0
}

// 批量检查是否关注请求
type BatchCheckFollowingReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	FollowingIds []int64 `protobuf:"varint,1,rep,packed,name=following_ids,json=followingIds,proto3" json:"following_ids,omitempty"` // 被关注者ID列表
}

func (x *BatchCheckFollowingReq) Reset() {
	*x = BatchCheckFollowingReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_bizaccount_proto_msgTypes[20]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *BatchCheckFollowingReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BatchCheckFollowingReq) ProtoMessage() {}

func (x *BatchCheckFollowingReq) ProtoReflect() protoreflect.Message {
	mi := &file_bizaccount_proto_msgTypes[20]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BatchCheckFollowingReq.ProtoReflect.Descriptor instead.
func (*BatchCheckFollowingReq) Descriptor() ([]byte, []int) {
	return file_bizaccount_proto_rawDescGZIP(), []int{20}
}

func (x *BatchCheckFollowingReq) GetFollowingIds() []int64 {
	if x != nil {
		return x.FollowingIds
	}
	return nil
}

// 批量检查是否关注响应
type BatchCheckFollowingResp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Code int32                      `protobuf:"varint,1,opt,name=code,proto3" json:"code,omitempty"`
	Msg  string                     `protobuf:"bytes,2,opt,name=msg,proto3" json:"msg,omitempty"`
	Data *svcaccount.FollowDataItem `protobuf:"bytes,3,opt,name=data,proto3" json:"data,omitempty"` // key: 被关注者ID, value: FollowData
}

func (x *BatchCheckFollowingResp) Reset() {
	*x = BatchCheckFollowingResp{}
	if protoimpl.UnsafeEnabled {
		mi := &file_bizaccount_proto_msgTypes[21]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *BatchCheckFollowingResp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BatchCheckFollowingResp) ProtoMessage() {}

func (x *BatchCheckFollowingResp) ProtoReflect() protoreflect.Message {
	mi := &file_bizaccount_proto_msgTypes[21]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BatchCheckFollowingResp.ProtoReflect.Descriptor instead.
func (*BatchCheckFollowingResp) Descriptor() ([]byte, []int) {
	return file_bizaccount_proto_rawDescGZIP(), []int{21}
}

func (x *BatchCheckFollowingResp) GetCode() int32 {
	if x != nil {
		return x.Code
	}
	return 0
}

func (x *BatchCheckFollowingResp) GetMsg() string {
	if x != nil {
		return x.Msg
	}
	return ""
}

func (x *BatchCheckFollowingResp) GetData() *svcaccount.FollowDataItem {
	if x != nil {
		return x.Data
	}
	return nil
}

var File_bizaccount_proto protoreflect.FileDescriptor

var file_bizaccount_proto_rawDesc = []byte{
	0x0a, 0x10, 0x62, 0x69, 0x7a, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x2e, 0x70, 0x72, 0x6f,
	0x74, 0x6f, 0x12, 0x0d, 0x76, 0x63, 0x2e, 0x62, 0x69, 0x7a, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e,
	0x74, 0x1a, 0x1c, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2f, 0x61, 0x70, 0x69, 0x2f, 0x61, 0x6e,
	0x6e, 0x6f, 0x74, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a,
	0x1b, 0x73, 0x76, 0x63, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x2f, 0x73, 0x76, 0x63, 0x61,
	0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x2b, 0x70, 0x72,
	0x6f, 0x74, 0x6f, 0x63, 0x2d, 0x67, 0x65, 0x6e, 0x2d, 0x76, 0x61, 0x6c, 0x69, 0x64, 0x61, 0x74,
	0x65, 0x2f, 0x76, 0x61, 0x6c, 0x69, 0x64, 0x61, 0x74, 0x65, 0x2f, 0x76, 0x61, 0x6c, 0x69, 0x64,
	0x61, 0x74, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x1f, 0x67, 0x6f, 0x6f, 0x67, 0x6c,
	0x65, 0x2f, 0x61, 0x70, 0x69, 0x2f, 0x66, 0x69, 0x65, 0x6c, 0x64, 0x5f, 0x62, 0x65, 0x68, 0x61,
	0x76, 0x69, 0x6f, 0x72, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x22, 0x7f, 0x0a, 0x17, 0x47, 0x65,
	0x74, 0x56, 0x65, 0x72, 0x69, 0x66, 0x69, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x43, 0x6f, 0x64,
	0x65, 0x52, 0x65, 0x73, 0x70, 0x12, 0x12, 0x0a, 0x04, 0x63, 0x6f, 0x64, 0x65, 0x18, 0x01, 0x20,
	0x01, 0x28, 0x05, 0x52, 0x04, 0x63, 0x6f, 0x64, 0x65, 0x12, 0x10, 0x0a, 0x03, 0x6d, 0x73, 0x67,
	0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x03, 0x6d, 0x73, 0x67, 0x12, 0x3e, 0x0a, 0x04, 0x64,
	0x61, 0x74, 0x61, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x2a, 0x2e, 0x76, 0x63, 0x2e, 0x73,
	0x76, 0x63, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x2e, 0x47, 0x65, 0x74, 0x56, 0x65, 0x72,
	0x69, 0x66, 0x69, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x43, 0x6f, 0x64, 0x65, 0x52, 0x65, 0x73,
	0x70, 0x44, 0x61, 0x74, 0x61, 0x52, 0x04, 0x64, 0x61, 0x74, 0x61, 0x22, 0x6b, 0x0a, 0x11, 0x41,
	0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x53, 0x69, 0x67, 0x6e, 0x55, 0x70, 0x52, 0x65, 0x73, 0x70,
	0x12, 0x12, 0x0a, 0x04, 0x63, 0x6f, 0x64, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x05, 0x52, 0x04,
	0x63, 0x6f, 0x64, 0x65, 0x12, 0x10, 0x0a, 0x03, 0x6d, 0x73, 0x67, 0x18, 0x02, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x03, 0x6d, 0x73, 0x67, 0x12, 0x30, 0x0a, 0x04, 0x64, 0x61, 0x74, 0x61, 0x18, 0x03,
	0x20, 0x01, 0x28, 0x0b, 0x32, 0x1c, 0x2e, 0x76, 0x63, 0x2e, 0x73, 0x76, 0x63, 0x61, 0x63, 0x63,
	0x6f, 0x75, 0x6e, 0x74, 0x2e, 0x54, 0x6f, 0x6b, 0x65, 0x6e, 0x52, 0x65, 0x73, 0x70, 0x44, 0x61,
	0x74, 0x61, 0x52, 0x04, 0x64, 0x61, 0x74, 0x61, 0x22, 0x6b, 0x0a, 0x11, 0x41, 0x63, 0x63, 0x6f,
	0x75, 0x6e, 0x74, 0x53, 0x69, 0x67, 0x6e, 0x49, 0x6e, 0x52, 0x65, 0x73, 0x70, 0x12, 0x12, 0x0a,
	0x04, 0x63, 0x6f, 0x64, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x05, 0x52, 0x04, 0x63, 0x6f, 0x64,
	0x65, 0x12, 0x10, 0x0a, 0x03, 0x6d, 0x73, 0x67, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x03,
	0x6d, 0x73, 0x67, 0x12, 0x30, 0x0a, 0x04, 0x64, 0x61, 0x74, 0x61, 0x18, 0x03, 0x20, 0x01, 0x28,
	0x0b, 0x32, 0x1c, 0x2e, 0x76, 0x63, 0x2e, 0x73, 0x76, 0x63, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e,
	0x74, 0x2e, 0x54, 0x6f, 0x6b, 0x65, 0x6e, 0x52, 0x65, 0x73, 0x70, 0x44, 0x61, 0x74, 0x61, 0x52,
	0x04, 0x64, 0x61, 0x74, 0x61, 0x22, 0x3a, 0x0a, 0x12, 0x41, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74,
	0x53, 0x69, 0x67, 0x6e, 0x4f, 0x75, 0x74, 0x52, 0x65, 0x73, 0x70, 0x12, 0x12, 0x0a, 0x04, 0x63,
	0x6f, 0x64, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x05, 0x52, 0x04, 0x63, 0x6f, 0x64, 0x65, 0x12,
	0x10, 0x0a, 0x03, 0x6d, 0x73, 0x67, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x03, 0x6d, 0x73,
	0x67, 0x22, 0x6a, 0x0a, 0x10, 0x52, 0x65, 0x66, 0x72, 0x65, 0x73, 0x68, 0x54, 0x6f, 0x6b, 0x65,
	0x6e, 0x52, 0x65, 0x73, 0x70, 0x12, 0x12, 0x0a, 0x04, 0x63, 0x6f, 0x64, 0x65, 0x18, 0x01, 0x20,
	0x01, 0x28, 0x05, 0x52, 0x04, 0x63, 0x6f, 0x64, 0x65, 0x12, 0x10, 0x0a, 0x03, 0x6d, 0x73, 0x67,
	0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x03, 0x6d, 0x73, 0x67, 0x12, 0x30, 0x0a, 0x04, 0x64,
	0x61, 0x74, 0x61, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1c, 0x2e, 0x76, 0x63, 0x2e, 0x73,
	0x76, 0x63, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x2e, 0x54, 0x6f, 0x6b, 0x65, 0x6e, 0x52,
	0x65, 0x73, 0x70, 0x44, 0x61, 0x74, 0x61, 0x52, 0x04, 0x64, 0x61, 0x74, 0x61, 0x22, 0x39, 0x0a,
	0x11, 0x41, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x44, 0x65, 0x6c, 0x65, 0x74, 0x65, 0x52, 0x65,
	0x73, 0x70, 0x12, 0x12, 0x0a, 0x04, 0x63, 0x6f, 0x64, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x05,
	0x52, 0x04, 0x63, 0x6f, 0x64, 0x65, 0x12, 0x10, 0x0a, 0x03, 0x6d, 0x73, 0x67, 0x18, 0x02, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x03, 0x6d, 0x73, 0x67, 0x22, 0x64, 0x0a, 0x0f, 0x47, 0x65, 0x74, 0x55,
	0x73, 0x65, 0x72, 0x49, 0x6e, 0x66, 0x6f, 0x52, 0x65, 0x73, 0x70, 0x12, 0x12, 0x0a, 0x04, 0x63,
	0x6f, 0x64, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x05, 0x52, 0x04, 0x63, 0x6f, 0x64, 0x65, 0x12,
	0x10, 0x0a, 0x03, 0x6d, 0x73, 0x67, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x03, 0x6d, 0x73,
	0x67, 0x12, 0x2b, 0x0a, 0x04, 0x64, 0x61, 0x74, 0x61, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0b, 0x32,
	0x17, 0x2e, 0x76, 0x63, 0x2e, 0x73, 0x76, 0x63, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x2e,
	0x55, 0x73, 0x65, 0x72, 0x49, 0x6e, 0x66, 0x6f, 0x52, 0x04, 0x64, 0x61, 0x74, 0x61, 0x22, 0x3a,
	0x0a, 0x12, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x55, 0x73, 0x65, 0x72, 0x49, 0x6e, 0x66, 0x6f,
	0x52, 0x65, 0x73, 0x70, 0x12, 0x12, 0x0a, 0x04, 0x63, 0x6f, 0x64, 0x65, 0x18, 0x01, 0x20, 0x01,
	0x28, 0x05, 0x52, 0x04, 0x63, 0x6f, 0x64, 0x65, 0x12, 0x10, 0x0a, 0x03, 0x6d, 0x73, 0x67, 0x18,
	0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x03, 0x6d, 0x73, 0x67, 0x22, 0x3e, 0x0a, 0x0d, 0x46, 0x6f,
	0x6c, 0x6c, 0x6f, 0x77, 0x55, 0x73, 0x65, 0x72, 0x52, 0x65, 0x71, 0x12, 0x2d, 0x0a, 0x0c, 0x66,
	0x6f, 0x6c, 0x6c, 0x6f, 0x77, 0x69, 0x6e, 0x67, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28,
	0x03, 0x42, 0x0a, 0xe0, 0x41, 0x02, 0xfa, 0x42, 0x04, 0x22, 0x02, 0x20, 0x00, 0x52, 0x0b, 0x66,
	0x6f, 0x6c, 0x6c, 0x6f, 0x77, 0x69, 0x6e, 0x67, 0x49, 0x64, 0x22, 0x36, 0x0a, 0x0e, 0x46, 0x6f,
	0x6c, 0x6c, 0x6f, 0x77, 0x55, 0x73, 0x65, 0x72, 0x52, 0x65, 0x73, 0x70, 0x12, 0x12, 0x0a, 0x04,
	0x63, 0x6f, 0x64, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x05, 0x52, 0x04, 0x63, 0x6f, 0x64, 0x65,
	0x12, 0x10, 0x0a, 0x03, 0x6d, 0x73, 0x67, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x03, 0x6d,
	0x73, 0x67, 0x22, 0x40, 0x0a, 0x0f, 0x55, 0x6e, 0x66, 0x6f, 0x6c, 0x6c, 0x6f, 0x77, 0x55, 0x73,
	0x65, 0x72, 0x52, 0x65, 0x71, 0x12, 0x2d, 0x0a, 0x0c, 0x66, 0x6f, 0x6c, 0x6c, 0x6f, 0x77, 0x69,
	0x6e, 0x67, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x42, 0x0a, 0xe0, 0x41, 0x02,
	0xfa, 0x42, 0x04, 0x22, 0x02, 0x20, 0x00, 0x52, 0x0b, 0x66, 0x6f, 0x6c, 0x6c, 0x6f, 0x77, 0x69,
	0x6e, 0x67, 0x49, 0x64, 0x22, 0x38, 0x0a, 0x10, 0x55, 0x6e, 0x66, 0x6f, 0x6c, 0x6c, 0x6f, 0x77,
	0x55, 0x73, 0x65, 0x72, 0x52, 0x65, 0x73, 0x70, 0x12, 0x12, 0x0a, 0x04, 0x63, 0x6f, 0x64, 0x65,
	0x18, 0x01, 0x20, 0x01, 0x28, 0x05, 0x52, 0x04, 0x63, 0x6f, 0x64, 0x65, 0x12, 0x10, 0x0a, 0x03,
	0x6d, 0x73, 0x67, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x03, 0x6d, 0x73, 0x67, 0x22, 0x42,
	0x0a, 0x11, 0x43, 0x68, 0x65, 0x63, 0x6b, 0x46, 0x6f, 0x6c, 0x6c, 0x6f, 0x77, 0x69, 0x6e, 0x67,
	0x52, 0x65, 0x71, 0x12, 0x2d, 0x0a, 0x0c, 0x66, 0x6f, 0x6c, 0x6c, 0x6f, 0x77, 0x69, 0x6e, 0x67,
	0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x42, 0x0a, 0xe0, 0x41, 0x02, 0xfa, 0x42,
	0x04, 0x22, 0x02, 0x20, 0x00, 0x52, 0x0b, 0x66, 0x6f, 0x6c, 0x6c, 0x6f, 0x77, 0x69, 0x6e, 0x67,
	0x49, 0x64, 0x22, 0x5d, 0x0a, 0x12, 0x43, 0x68, 0x65, 0x63, 0x6b, 0x46, 0x6f, 0x6c, 0x6c, 0x6f,
	0x77, 0x69, 0x6e, 0x67, 0x52, 0x65, 0x73, 0x70, 0x12, 0x12, 0x0a, 0x04, 0x63, 0x6f, 0x64, 0x65,
	0x18, 0x01, 0x20, 0x01, 0x28, 0x05, 0x52, 0x04, 0x63, 0x6f, 0x64, 0x65, 0x12, 0x10, 0x0a, 0x03,
	0x6d, 0x73, 0x67, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x03, 0x6d, 0x73, 0x67, 0x12, 0x21,
	0x0a, 0x0c, 0x69, 0x73, 0x5f, 0x66, 0x6f, 0x6c, 0x6c, 0x6f, 0x77, 0x69, 0x6e, 0x67, 0x18, 0x03,
	0x20, 0x01, 0x28, 0x08, 0x52, 0x0b, 0x69, 0x73, 0x46, 0x6f, 0x6c, 0x6c, 0x6f, 0x77, 0x69, 0x6e,
	0x67, 0x22, 0x47, 0x0a, 0x14, 0x47, 0x65, 0x74, 0x46, 0x6f, 0x6c, 0x6c, 0x6f, 0x77, 0x69, 0x6e,
	0x67, 0x55, 0x73, 0x65, 0x72, 0x73, 0x52, 0x65, 0x71, 0x12, 0x12, 0x0a, 0x04, 0x70, 0x61, 0x67,
	0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x05, 0x52, 0x04, 0x70, 0x61, 0x67, 0x65, 0x12, 0x1b, 0x0a,
	0x09, 0x70, 0x61, 0x67, 0x65, 0x5f, 0x73, 0x69, 0x7a, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x05,
	0x52, 0x08, 0x70, 0x61, 0x67, 0x65, 0x53, 0x69, 0x7a, 0x65, 0x22, 0x7b, 0x0a, 0x15, 0x47, 0x65,
	0x74, 0x46, 0x6f, 0x6c, 0x6c, 0x6f, 0x77, 0x69, 0x6e, 0x67, 0x55, 0x73, 0x65, 0x72, 0x73, 0x52,
	0x65, 0x73, 0x70, 0x12, 0x12, 0x0a, 0x04, 0x63, 0x6f, 0x64, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28,
	0x05, 0x52, 0x04, 0x63, 0x6f, 0x64, 0x65, 0x12, 0x10, 0x0a, 0x03, 0x6d, 0x73, 0x67, 0x18, 0x02,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x03, 0x6d, 0x73, 0x67, 0x12, 0x3c, 0x0a, 0x04, 0x64, 0x61, 0x74,
	0x61, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x28, 0x2e, 0x76, 0x63, 0x2e, 0x62, 0x69, 0x7a,
	0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x2e, 0x47, 0x65, 0x74, 0x46, 0x6f, 0x6c, 0x6c, 0x6f,
	0x77, 0x69, 0x6e, 0x67, 0x55, 0x73, 0x65, 0x72, 0x73, 0x52, 0x65, 0x73, 0x70, 0x44, 0x61, 0x74,
	0x61, 0x52, 0x04, 0x64, 0x61, 0x74, 0x61, 0x22, 0x60, 0x0a, 0x19, 0x47, 0x65, 0x74, 0x46, 0x6f,
	0x6c, 0x6c, 0x6f, 0x77, 0x69, 0x6e, 0x67, 0x55, 0x73, 0x65, 0x72, 0x73, 0x52, 0x65, 0x73, 0x70,
	0x44, 0x61, 0x74, 0x61, 0x12, 0x2d, 0x0a, 0x05, 0x75, 0x73, 0x65, 0x72, 0x73, 0x18, 0x01, 0x20,
	0x03, 0x28, 0x0b, 0x32, 0x17, 0x2e, 0x76, 0x63, 0x2e, 0x73, 0x76, 0x63, 0x61, 0x63, 0x63, 0x6f,
	0x75, 0x6e, 0x74, 0x2e, 0x55, 0x73, 0x65, 0x72, 0x49, 0x6e, 0x66, 0x6f, 0x52, 0x05, 0x75, 0x73,
	0x65, 0x72, 0x73, 0x12, 0x14, 0x0a, 0x05, 0x74, 0x6f, 0x74, 0x61, 0x6c, 0x18, 0x02, 0x20, 0x01,
	0x28, 0x03, 0x52, 0x05, 0x74, 0x6f, 0x74, 0x61, 0x6c, 0x22, 0x42, 0x0a, 0x0f, 0x47, 0x65, 0x74,
	0x46, 0x6f, 0x6c, 0x6c, 0x6f, 0x77, 0x65, 0x72, 0x73, 0x52, 0x65, 0x71, 0x12, 0x12, 0x0a, 0x04,
	0x70, 0x61, 0x67, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x05, 0x52, 0x04, 0x70, 0x61, 0x67, 0x65,
	0x12, 0x1b, 0x0a, 0x09, 0x70, 0x61, 0x67, 0x65, 0x5f, 0x73, 0x69, 0x7a, 0x65, 0x18, 0x02, 0x20,
	0x01, 0x28, 0x05, 0x52, 0x08, 0x70, 0x61, 0x67, 0x65, 0x53, 0x69, 0x7a, 0x65, 0x22, 0x71, 0x0a,
	0x10, 0x47, 0x65, 0x74, 0x46, 0x6f, 0x6c, 0x6c, 0x6f, 0x77, 0x65, 0x72, 0x73, 0x52, 0x65, 0x73,
	0x70, 0x12, 0x12, 0x0a, 0x04, 0x63, 0x6f, 0x64, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x05, 0x52,
	0x04, 0x63, 0x6f, 0x64, 0x65, 0x12, 0x10, 0x0a, 0x03, 0x6d, 0x73, 0x67, 0x18, 0x02, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x03, 0x6d, 0x73, 0x67, 0x12, 0x37, 0x0a, 0x04, 0x64, 0x61, 0x74, 0x61, 0x18,
	0x03, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x23, 0x2e, 0x76, 0x63, 0x2e, 0x62, 0x69, 0x7a, 0x61, 0x63,
	0x63, 0x6f, 0x75, 0x6e, 0x74, 0x2e, 0x47, 0x65, 0x74, 0x46, 0x6f, 0x6c, 0x6c, 0x6f, 0x77, 0x65,
	0x72, 0x73, 0x52, 0x65, 0x73, 0x70, 0x44, 0x61, 0x74, 0x61, 0x52, 0x04, 0x64, 0x61, 0x74, 0x61,
	0x22, 0x5b, 0x0a, 0x14, 0x47, 0x65, 0x74, 0x46, 0x6f, 0x6c, 0x6c, 0x6f, 0x77, 0x65, 0x72, 0x73,
	0x52, 0x65, 0x73, 0x70, 0x44, 0x61, 0x74, 0x61, 0x12, 0x2d, 0x0a, 0x05, 0x75, 0x73, 0x65, 0x72,
	0x73, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x17, 0x2e, 0x76, 0x63, 0x2e, 0x73, 0x76, 0x63,
	0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x2e, 0x55, 0x73, 0x65, 0x72, 0x49, 0x6e, 0x66, 0x6f,
	0x52, 0x05, 0x75, 0x73, 0x65, 0x72, 0x73, 0x12, 0x14, 0x0a, 0x05, 0x74, 0x6f, 0x74, 0x61, 0x6c,
	0x18, 0x02, 0x20, 0x01, 0x28, 0x03, 0x52, 0x05, 0x74, 0x6f, 0x74, 0x61, 0x6c, 0x22, 0x4a, 0x0a,
	0x16, 0x42, 0x61, 0x74, 0x63, 0x68, 0x43, 0x68, 0x65, 0x63, 0x6b, 0x46, 0x6f, 0x6c, 0x6c, 0x6f,
	0x77, 0x69, 0x6e, 0x67, 0x52, 0x65, 0x71, 0x12, 0x30, 0x0a, 0x0d, 0x66, 0x6f, 0x6c, 0x6c, 0x6f,
	0x77, 0x69, 0x6e, 0x67, 0x5f, 0x69, 0x64, 0x73, 0x18, 0x01, 0x20, 0x03, 0x28, 0x03, 0x42, 0x0b,
	0xfa, 0x42, 0x08, 0x92, 0x01, 0x05, 0x08, 0x01, 0x10, 0xe8, 0x07, 0x52, 0x0c, 0x66, 0x6f, 0x6c,
	0x6c, 0x6f, 0x77, 0x69, 0x6e, 0x67, 0x49, 0x64, 0x73, 0x22, 0x72, 0x0a, 0x17, 0x42, 0x61, 0x74,
	0x63, 0x68, 0x43, 0x68, 0x65, 0x63, 0x6b, 0x46, 0x6f, 0x6c, 0x6c, 0x6f, 0x77, 0x69, 0x6e, 0x67,
	0x52, 0x65, 0x73, 0x70, 0x12, 0x12, 0x0a, 0x04, 0x63, 0x6f, 0x64, 0x65, 0x18, 0x01, 0x20, 0x01,
	0x28, 0x05, 0x52, 0x04, 0x63, 0x6f, 0x64, 0x65, 0x12, 0x10, 0x0a, 0x03, 0x6d, 0x73, 0x67, 0x18,
	0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x03, 0x6d, 0x73, 0x67, 0x12, 0x31, 0x0a, 0x04, 0x64, 0x61,
	0x74, 0x61, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1d, 0x2e, 0x76, 0x63, 0x2e, 0x73, 0x76,
	0x63, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x2e, 0x46, 0x6f, 0x6c, 0x6c, 0x6f, 0x77, 0x44,
	0x61, 0x74, 0x61, 0x49, 0x74, 0x65, 0x6d, 0x52, 0x04, 0x64, 0x61, 0x74, 0x61, 0x32, 0xba, 0x0e,
	0x0a, 0x01, 0x73, 0x12, 0x9a, 0x01, 0x0a, 0x13, 0x47, 0x65, 0x74, 0x56, 0x65, 0x72, 0x69, 0x66,
	0x69, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x43, 0x6f, 0x64, 0x65, 0x12, 0x25, 0x2e, 0x76, 0x63,
	0x2e, 0x73, 0x76, 0x63, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x2e, 0x47, 0x65, 0x74, 0x56,
	0x65, 0x72, 0x69, 0x66, 0x69, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x43, 0x6f, 0x64, 0x65, 0x52,
	0x65, 0x71, 0x1a, 0x26, 0x2e, 0x76, 0x63, 0x2e, 0x62, 0x69, 0x7a, 0x61, 0x63, 0x63, 0x6f, 0x75,
	0x6e, 0x74, 0x2e, 0x47, 0x65, 0x74, 0x56, 0x65, 0x72, 0x69, 0x66, 0x69, 0x63, 0x61, 0x74, 0x69,
	0x6f, 0x6e, 0x43, 0x6f, 0x64, 0x65, 0x52, 0x65, 0x73, 0x70, 0x22, 0x34, 0x82, 0xd3, 0xe4, 0x93,
	0x02, 0x2e, 0x3a, 0x01, 0x2a, 0x22, 0x29, 0x2f, 0x76, 0x63, 0x2e, 0x62, 0x69, 0x7a, 0x61, 0x63,
	0x63, 0x6f, 0x75, 0x6e, 0x74, 0x2e, 0x73, 0x2f, 0x76, 0x31, 0x2f, 0x67, 0x65, 0x74, 0x5f, 0x76,
	0x65, 0x72, 0x69, 0x66, 0x69, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x63, 0x6f, 0x64, 0x65,
	0x12, 0x79, 0x0a, 0x0d, 0x41, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x53, 0x69, 0x67, 0x6e, 0x55,
	0x70, 0x12, 0x1f, 0x2e, 0x76, 0x63, 0x2e, 0x73, 0x76, 0x63, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e,
	0x74, 0x2e, 0x41, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x53, 0x69, 0x67, 0x6e, 0x55, 0x70, 0x52,
	0x65, 0x71, 0x1a, 0x20, 0x2e, 0x76, 0x63, 0x2e, 0x62, 0x69, 0x7a, 0x61, 0x63, 0x63, 0x6f, 0x75,
	0x6e, 0x74, 0x2e, 0x41, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x53, 0x69, 0x67, 0x6e, 0x55, 0x70,
	0x52, 0x65, 0x73, 0x70, 0x22, 0x25, 0x82, 0xd3, 0xe4, 0x93, 0x02, 0x1f, 0x3a, 0x01, 0x2a, 0x22,
	0x1a, 0x2f, 0x76, 0x63, 0x2e, 0x62, 0x69, 0x7a, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x2e,
	0x73, 0x2f, 0x76, 0x31, 0x2f, 0x73, 0x69, 0x67, 0x6e, 0x75, 0x70, 0x12, 0x79, 0x0a, 0x0d, 0x41,
	0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x53, 0x69, 0x67, 0x6e, 0x49, 0x6e, 0x12, 0x1f, 0x2e, 0x76,
	0x63, 0x2e, 0x73, 0x76, 0x63, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x2e, 0x41, 0x63, 0x63,
	0x6f, 0x75, 0x6e, 0x74, 0x53, 0x69, 0x67, 0x6e, 0x49, 0x6e, 0x52, 0x65, 0x71, 0x1a, 0x20, 0x2e,
	0x76, 0x63, 0x2e, 0x62, 0x69, 0x7a, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x2e, 0x41, 0x63,
	0x63, 0x6f, 0x75, 0x6e, 0x74, 0x53, 0x69, 0x67, 0x6e, 0x49, 0x6e, 0x52, 0x65, 0x73, 0x70, 0x22,
	0x25, 0x82, 0xd3, 0xe4, 0x93, 0x02, 0x1f, 0x3a, 0x01, 0x2a, 0x22, 0x1a, 0x2f, 0x76, 0x63, 0x2e,
	0x62, 0x69, 0x7a, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x2e, 0x73, 0x2f, 0x76, 0x31, 0x2f,
	0x73, 0x69, 0x67, 0x6e, 0x69, 0x6e, 0x12, 0x7d, 0x0a, 0x0e, 0x41, 0x63, 0x63, 0x6f, 0x75, 0x6e,
	0x74, 0x53, 0x69, 0x67, 0x6e, 0x4f, 0x75, 0x74, 0x12, 0x20, 0x2e, 0x76, 0x63, 0x2e, 0x73, 0x76,
	0x63, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x2e, 0x41, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74,
	0x53, 0x69, 0x67, 0x6e, 0x4f, 0x75, 0x74, 0x52, 0x65, 0x71, 0x1a, 0x21, 0x2e, 0x76, 0x63, 0x2e,
	0x62, 0x69, 0x7a, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x2e, 0x41, 0x63, 0x63, 0x6f, 0x75,
	0x6e, 0x74, 0x53, 0x69, 0x67, 0x6e, 0x4f, 0x75, 0x74, 0x52, 0x65, 0x73, 0x70, 0x22, 0x26, 0x82,
	0xd3, 0xe4, 0x93, 0x02, 0x20, 0x3a, 0x01, 0x2a, 0x22, 0x1b, 0x2f, 0x76, 0x63, 0x2e, 0x62, 0x69,
	0x7a, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x2e, 0x73, 0x2f, 0x76, 0x31, 0x2f, 0x73, 0x69,
	0x67, 0x6e, 0x6f, 0x75, 0x74, 0x12, 0x7d, 0x0a, 0x0c, 0x52, 0x65, 0x66, 0x72, 0x65, 0x73, 0x68,
	0x54, 0x6f, 0x6b, 0x65, 0x6e, 0x12, 0x1e, 0x2e, 0x76, 0x63, 0x2e, 0x73, 0x76, 0x63, 0x61, 0x63,
	0x63, 0x6f, 0x75, 0x6e, 0x74, 0x2e, 0x52, 0x65, 0x66, 0x72, 0x65, 0x73, 0x68, 0x54, 0x6f, 0x6b,
	0x65, 0x6e, 0x52, 0x65, 0x71, 0x1a, 0x1f, 0x2e, 0x76, 0x63, 0x2e, 0x62, 0x69, 0x7a, 0x61, 0x63,
	0x63, 0x6f, 0x75, 0x6e, 0x74, 0x2e, 0x52, 0x65, 0x66, 0x72, 0x65, 0x73, 0x68, 0x54, 0x6f, 0x6b,
	0x65, 0x6e, 0x52, 0x65, 0x73, 0x70, 0x22, 0x2c, 0x82, 0xd3, 0xe4, 0x93, 0x02, 0x26, 0x3a, 0x01,
	0x2a, 0x22, 0x21, 0x2f, 0x76, 0x63, 0x2e, 0x62, 0x69, 0x7a, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e,
	0x74, 0x2e, 0x73, 0x2f, 0x76, 0x31, 0x2f, 0x72, 0x65, 0x66, 0x72, 0x65, 0x73, 0x68, 0x5f, 0x74,
	0x6f, 0x6b, 0x65, 0x6e, 0x12, 0x79, 0x0a, 0x0d, 0x41, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x44,
	0x65, 0x6c, 0x65, 0x74, 0x65, 0x12, 0x1f, 0x2e, 0x76, 0x63, 0x2e, 0x73, 0x76, 0x63, 0x61, 0x63,
	0x63, 0x6f, 0x75, 0x6e, 0x74, 0x2e, 0x41, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x44, 0x65, 0x6c,
	0x65, 0x74, 0x65, 0x52, 0x65, 0x71, 0x1a, 0x20, 0x2e, 0x76, 0x63, 0x2e, 0x62, 0x69, 0x7a, 0x61,
	0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x2e, 0x41, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x44, 0x65,
	0x6c, 0x65, 0x74, 0x65, 0x52, 0x65, 0x73, 0x70, 0x22, 0x25, 0x82, 0xd3, 0xe4, 0x93, 0x02, 0x1f,
	0x3a, 0x01, 0x2a, 0x22, 0x1a, 0x2f, 0x76, 0x63, 0x2e, 0x62, 0x69, 0x7a, 0x61, 0x63, 0x63, 0x6f,
	0x75, 0x6e, 0x74, 0x2e, 0x73, 0x2f, 0x76, 0x31, 0x2f, 0x64, 0x65, 0x6c, 0x65, 0x74, 0x65, 0x12,
	0x76, 0x0a, 0x0b, 0x47, 0x65, 0x74, 0x55, 0x73, 0x65, 0x72, 0x49, 0x6e, 0x66, 0x6f, 0x12, 0x1d,
	0x2e, 0x76, 0x63, 0x2e, 0x73, 0x76, 0x63, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x2e, 0x47,
	0x65, 0x74, 0x55, 0x73, 0x65, 0x72, 0x49, 0x6e, 0x66, 0x6f, 0x52, 0x65, 0x71, 0x1a, 0x1e, 0x2e,
	0x76, 0x63, 0x2e, 0x62, 0x69, 0x7a, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x2e, 0x47, 0x65,
	0x74, 0x55, 0x73, 0x65, 0x72, 0x49, 0x6e, 0x66, 0x6f, 0x52, 0x65, 0x73, 0x70, 0x22, 0x28, 0x82,
	0xd3, 0xe4, 0x93, 0x02, 0x22, 0x3a, 0x01, 0x2a, 0x22, 0x1d, 0x2f, 0x76, 0x63, 0x2e, 0x62, 0x69,
	0x7a, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x2e, 0x73, 0x2f, 0x76, 0x31, 0x2f, 0x75, 0x73,
	0x65, 0x72, 0x5f, 0x69, 0x6e, 0x66, 0x6f, 0x12, 0x81, 0x01, 0x0a, 0x0e, 0x55, 0x70, 0x64, 0x61,
	0x74, 0x65, 0x55, 0x73, 0x65, 0x72, 0x49, 0x6e, 0x66, 0x6f, 0x12, 0x20, 0x2e, 0x76, 0x63, 0x2e,
	0x73, 0x76, 0x63, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x2e, 0x55, 0x70, 0x64, 0x61, 0x74,
	0x65, 0x55, 0x73, 0x65, 0x72, 0x49, 0x6e, 0x66, 0x6f, 0x52, 0x65, 0x71, 0x1a, 0x21, 0x2e, 0x76,
	0x63, 0x2e, 0x62, 0x69, 0x7a, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x2e, 0x55, 0x70, 0x64,
	0x61, 0x74, 0x65, 0x55, 0x73, 0x65, 0x72, 0x49, 0x6e, 0x66, 0x6f, 0x52, 0x65, 0x73, 0x70, 0x22,
	0x2a, 0x82, 0xd3, 0xe4, 0x93, 0x02, 0x24, 0x3a, 0x01, 0x2a, 0x22, 0x1f, 0x2f, 0x76, 0x63, 0x2e,
	0x62, 0x69, 0x7a, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x2e, 0x73, 0x2f, 0x76, 0x31, 0x2f,
	0x75, 0x70, 0x64, 0x61, 0x74, 0x65, 0x5f, 0x75, 0x73, 0x65, 0x72, 0x12, 0x75, 0x0a, 0x0a, 0x46,
	0x6f, 0x6c, 0x6c, 0x6f, 0x77, 0x55, 0x73, 0x65, 0x72, 0x12, 0x1c, 0x2e, 0x76, 0x63, 0x2e, 0x62,
	0x69, 0x7a, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x2e, 0x46, 0x6f, 0x6c, 0x6c, 0x6f, 0x77,
	0x55, 0x73, 0x65, 0x72, 0x52, 0x65, 0x71, 0x1a, 0x1d, 0x2e, 0x76, 0x63, 0x2e, 0x62, 0x69, 0x7a,
	0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x2e, 0x46, 0x6f, 0x6c, 0x6c, 0x6f, 0x77, 0x55, 0x73,
	0x65, 0x72, 0x52, 0x65, 0x73, 0x70, 0x22, 0x2a, 0x82, 0xd3, 0xe4, 0x93, 0x02, 0x24, 0x3a, 0x01,
	0x2a, 0x22, 0x1f, 0x2f, 0x76, 0x63, 0x2e, 0x62, 0x69, 0x7a, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e,
	0x74, 0x2e, 0x73, 0x2f, 0x76, 0x31, 0x2f, 0x66, 0x6f, 0x6c, 0x6c, 0x6f, 0x77, 0x5f, 0x75, 0x73,
	0x65, 0x72, 0x12, 0x7d, 0x0a, 0x0c, 0x55, 0x6e, 0x66, 0x6f, 0x6c, 0x6c, 0x6f, 0x77, 0x55, 0x73,
	0x65, 0x72, 0x12, 0x1e, 0x2e, 0x76, 0x63, 0x2e, 0x62, 0x69, 0x7a, 0x61, 0x63, 0x63, 0x6f, 0x75,
	0x6e, 0x74, 0x2e, 0x55, 0x6e, 0x66, 0x6f, 0x6c, 0x6c, 0x6f, 0x77, 0x55, 0x73, 0x65, 0x72, 0x52,
	0x65, 0x71, 0x1a, 0x1f, 0x2e, 0x76, 0x63, 0x2e, 0x62, 0x69, 0x7a, 0x61, 0x63, 0x63, 0x6f, 0x75,
	0x6e, 0x74, 0x2e, 0x55, 0x6e, 0x66, 0x6f, 0x6c, 0x6c, 0x6f, 0x77, 0x55, 0x73, 0x65, 0x72, 0x52,
	0x65, 0x73, 0x70, 0x22, 0x2c, 0x82, 0xd3, 0xe4, 0x93, 0x02, 0x26, 0x3a, 0x01, 0x2a, 0x22, 0x21,
	0x2f, 0x76, 0x63, 0x2e, 0x62, 0x69, 0x7a, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x2e, 0x73,
	0x2f, 0x76, 0x31, 0x2f, 0x75, 0x6e, 0x66, 0x6f, 0x6c, 0x6c, 0x6f, 0x77, 0x5f, 0x75, 0x73, 0x65,
	0x72, 0x12, 0x85, 0x01, 0x0a, 0x0e, 0x43, 0x68, 0x65, 0x63, 0x6b, 0x46, 0x6f, 0x6c, 0x6c, 0x6f,
	0x77, 0x69, 0x6e, 0x67, 0x12, 0x20, 0x2e, 0x76, 0x63, 0x2e, 0x62, 0x69, 0x7a, 0x61, 0x63, 0x63,
	0x6f, 0x75, 0x6e, 0x74, 0x2e, 0x43, 0x68, 0x65, 0x63, 0x6b, 0x46, 0x6f, 0x6c, 0x6c, 0x6f, 0x77,
	0x69, 0x6e, 0x67, 0x52, 0x65, 0x71, 0x1a, 0x21, 0x2e, 0x76, 0x63, 0x2e, 0x62, 0x69, 0x7a, 0x61,
	0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x2e, 0x43, 0x68, 0x65, 0x63, 0x6b, 0x46, 0x6f, 0x6c, 0x6c,
	0x6f, 0x77, 0x69, 0x6e, 0x67, 0x52, 0x65, 0x73, 0x70, 0x22, 0x2e, 0x82, 0xd3, 0xe4, 0x93, 0x02,
	0x28, 0x3a, 0x01, 0x2a, 0x22, 0x23, 0x2f, 0x76, 0x63, 0x2e, 0x62, 0x69, 0x7a, 0x61, 0x63, 0x63,
	0x6f, 0x75, 0x6e, 0x74, 0x2e, 0x73, 0x2f, 0x76, 0x31, 0x2f, 0x63, 0x68, 0x65, 0x63, 0x6b, 0x5f,
	0x66, 0x6f, 0x6c, 0x6c, 0x6f, 0x77, 0x69, 0x6e, 0x67, 0x12, 0x92, 0x01, 0x0a, 0x11, 0x47, 0x65,
	0x74, 0x46, 0x6f, 0x6c, 0x6c, 0x6f, 0x77, 0x69, 0x6e, 0x67, 0x55, 0x73, 0x65, 0x72, 0x73, 0x12,
	0x23, 0x2e, 0x76, 0x63, 0x2e, 0x62, 0x69, 0x7a, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x2e,
	0x47, 0x65, 0x74, 0x46, 0x6f, 0x6c, 0x6c, 0x6f, 0x77, 0x69, 0x6e, 0x67, 0x55, 0x73, 0x65, 0x72,
	0x73, 0x52, 0x65, 0x71, 0x1a, 0x24, 0x2e, 0x76, 0x63, 0x2e, 0x62, 0x69, 0x7a, 0x61, 0x63, 0x63,
	0x6f, 0x75, 0x6e, 0x74, 0x2e, 0x47, 0x65, 0x74, 0x46, 0x6f, 0x6c, 0x6c, 0x6f, 0x77, 0x69, 0x6e,
	0x67, 0x55, 0x73, 0x65, 0x72, 0x73, 0x52, 0x65, 0x73, 0x70, 0x22, 0x32, 0x82, 0xd3, 0xe4, 0x93,
	0x02, 0x2c, 0x3a, 0x01, 0x2a, 0x22, 0x27, 0x2f, 0x76, 0x63, 0x2e, 0x62, 0x69, 0x7a, 0x61, 0x63,
	0x63, 0x6f, 0x75, 0x6e, 0x74, 0x2e, 0x73, 0x2f, 0x76, 0x31, 0x2f, 0x67, 0x65, 0x74, 0x5f, 0x66,
	0x6f, 0x6c, 0x6c, 0x6f, 0x77, 0x69, 0x6e, 0x67, 0x5f, 0x75, 0x73, 0x65, 0x72, 0x73, 0x12, 0x7d,
	0x0a, 0x0c, 0x47, 0x65, 0x74, 0x46, 0x6f, 0x6c, 0x6c, 0x6f, 0x77, 0x65, 0x72, 0x73, 0x12, 0x1e,
	0x2e, 0x76, 0x63, 0x2e, 0x62, 0x69, 0x7a, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x2e, 0x47,
	0x65, 0x74, 0x46, 0x6f, 0x6c, 0x6c, 0x6f, 0x77, 0x65, 0x72, 0x73, 0x52, 0x65, 0x71, 0x1a, 0x1f,
	0x2e, 0x76, 0x63, 0x2e, 0x62, 0x69, 0x7a, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x2e, 0x47,
	0x65, 0x74, 0x46, 0x6f, 0x6c, 0x6c, 0x6f, 0x77, 0x65, 0x72, 0x73, 0x52, 0x65, 0x73, 0x70, 0x22,
	0x2c, 0x82, 0xd3, 0xe4, 0x93, 0x02, 0x26, 0x3a, 0x01, 0x2a, 0x22, 0x21, 0x2f, 0x76, 0x63, 0x2e,
	0x62, 0x69, 0x7a, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x2e, 0x73, 0x2f, 0x76, 0x31, 0x2f,
	0x67, 0x65, 0x74, 0x5f, 0x66, 0x6f, 0x6c, 0x6c, 0x6f, 0x77, 0x65, 0x72, 0x73, 0x12, 0x9a, 0x01,
	0x0a, 0x13, 0x42, 0x61, 0x74, 0x63, 0x68, 0x43, 0x68, 0x65, 0x63, 0x6b, 0x46, 0x6f, 0x6c, 0x6c,
	0x6f, 0x77, 0x69, 0x6e, 0x67, 0x12, 0x25, 0x2e, 0x76, 0x63, 0x2e, 0x62, 0x69, 0x7a, 0x61, 0x63,
	0x63, 0x6f, 0x75, 0x6e, 0x74, 0x2e, 0x42, 0x61, 0x74, 0x63, 0x68, 0x43, 0x68, 0x65, 0x63, 0x6b,
	0x46, 0x6f, 0x6c, 0x6c, 0x6f, 0x77, 0x69, 0x6e, 0x67, 0x52, 0x65, 0x71, 0x1a, 0x26, 0x2e, 0x76,
	0x63, 0x2e, 0x62, 0x69, 0x7a, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x2e, 0x42, 0x61, 0x74,
	0x63, 0x68, 0x43, 0x68, 0x65, 0x63, 0x6b, 0x46, 0x6f, 0x6c, 0x6c, 0x6f, 0x77, 0x69, 0x6e, 0x67,
	0x52, 0x65, 0x73, 0x70, 0x22, 0x34, 0x82, 0xd3, 0xe4, 0x93, 0x02, 0x2e, 0x3a, 0x01, 0x2a, 0x22,
	0x29, 0x2f, 0x76, 0x63, 0x2e, 0x62, 0x69, 0x7a, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x2e,
	0x73, 0x2f, 0x76, 0x31, 0x2f, 0x62, 0x61, 0x74, 0x63, 0x68, 0x5f, 0x63, 0x68, 0x65, 0x63, 0x6b,
	0x5f, 0x66, 0x6f, 0x6c, 0x6c, 0x6f, 0x77, 0x69, 0x6e, 0x67, 0x42, 0x49, 0x5a, 0x47, 0x6e, 0x65,
	0x77, 0x2d, 0x67, 0x69, 0x74, 0x6c, 0x61, 0x62, 0x2e, 0x78, 0x75, 0x6e, 0x6c, 0x65, 0x69, 0x2e,
	0x63, 0x6e, 0x2f, 0x76, 0x63, 0x70, 0x72, 0x6f, 0x6a, 0x65, 0x63, 0x74, 0x2f, 0x62, 0x61, 0x63,
	0x6b, 0x65, 0x6e, 0x64, 0x73, 0x2f, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2f, 0x61, 0x70, 0x69, 0x2f,
	0x62, 0x69, 0x7a, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x3b, 0x62, 0x69, 0x7a, 0x61, 0x63,
	0x63, 0x6f, 0x75, 0x6e, 0x74, 0x62, 0x06, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_bizaccount_proto_rawDescOnce sync.Once
	file_bizaccount_proto_rawDescData = file_bizaccount_proto_rawDesc
)

func file_bizaccount_proto_rawDescGZIP() []byte {
	file_bizaccount_proto_rawDescOnce.Do(func() {
		file_bizaccount_proto_rawDescData = protoimpl.X.CompressGZIP(file_bizaccount_proto_rawDescData)
	})
	return file_bizaccount_proto_rawDescData
}

var file_bizaccount_proto_msgTypes = make([]protoimpl.MessageInfo, 22)
var file_bizaccount_proto_goTypes = []interface{}{
	(*GetVerificationCodeResp)(nil),                // 0: vc.bizaccount.GetVerificationCodeResp
	(*AccountSignUpResp)(nil),                      // 1: vc.bizaccount.AccountSignUpResp
	(*AccountSignInResp)(nil),                      // 2: vc.bizaccount.AccountSignInResp
	(*AccountSignOutResp)(nil),                     // 3: vc.bizaccount.AccountSignOutResp
	(*RefreshTokenResp)(nil),                       // 4: vc.bizaccount.RefreshTokenResp
	(*AccountDeleteResp)(nil),                      // 5: vc.bizaccount.AccountDeleteResp
	(*GetUserInfoResp)(nil),                        // 6: vc.bizaccount.GetUserInfoResp
	(*UpdateUserInfoResp)(nil),                     // 7: vc.bizaccount.UpdateUserInfoResp
	(*FollowUserReq)(nil),                          // 8: vc.bizaccount.FollowUserReq
	(*FollowUserResp)(nil),                         // 9: vc.bizaccount.FollowUserResp
	(*UnfollowUserReq)(nil),                        // 10: vc.bizaccount.UnfollowUserReq
	(*UnfollowUserResp)(nil),                       // 11: vc.bizaccount.UnfollowUserResp
	(*CheckFollowingReq)(nil),                      // 12: vc.bizaccount.CheckFollowingReq
	(*CheckFollowingResp)(nil),                     // 13: vc.bizaccount.CheckFollowingResp
	(*GetFollowingUsersReq)(nil),                   // 14: vc.bizaccount.GetFollowingUsersReq
	(*GetFollowingUsersResp)(nil),                  // 15: vc.bizaccount.GetFollowingUsersResp
	(*GetFollowingUsersRespData)(nil),              // 16: vc.bizaccount.GetFollowingUsersRespData
	(*GetFollowersReq)(nil),                        // 17: vc.bizaccount.GetFollowersReq
	(*GetFollowersResp)(nil),                       // 18: vc.bizaccount.GetFollowersResp
	(*GetFollowersRespData)(nil),                   // 19: vc.bizaccount.GetFollowersRespData
	(*BatchCheckFollowingReq)(nil),                 // 20: vc.bizaccount.BatchCheckFollowingReq
	(*BatchCheckFollowingResp)(nil),                // 21: vc.bizaccount.BatchCheckFollowingResp
	(*svcaccount.GetVerificationCodeRespData)(nil), // 22: vc.svcaccount.GetVerificationCodeRespData
	(*svcaccount.TokenRespData)(nil),               // 23: vc.svcaccount.TokenRespData
	(*svcaccount.UserInfo)(nil),                    // 24: vc.svcaccount.UserInfo
	(*svcaccount.FollowDataItem)(nil),              // 25: vc.svcaccount.FollowDataItem
	(*svcaccount.GetVerificationCodeReq)(nil),      // 26: vc.svcaccount.GetVerificationCodeReq
	(*svcaccount.AccountSignUpReq)(nil),            // 27: vc.svcaccount.AccountSignUpReq
	(*svcaccount.AccountSignInReq)(nil),            // 28: vc.svcaccount.AccountSignInReq
	(*svcaccount.AccountSignOutReq)(nil),           // 29: vc.svcaccount.AccountSignOutReq
	(*svcaccount.RefreshTokenReq)(nil),             // 30: vc.svcaccount.RefreshTokenReq
	(*svcaccount.AccountDeleteReq)(nil),            // 31: vc.svcaccount.AccountDeleteReq
	(*svcaccount.GetUserInfoReq)(nil),              // 32: vc.svcaccount.GetUserInfoReq
	(*svcaccount.UpdateUserInfoReq)(nil),           // 33: vc.svcaccount.UpdateUserInfoReq
}
var file_bizaccount_proto_depIdxs = []int32{
	22, // 0: vc.bizaccount.GetVerificationCodeResp.data:type_name -> vc.svcaccount.GetVerificationCodeRespData
	23, // 1: vc.bizaccount.AccountSignUpResp.data:type_name -> vc.svcaccount.TokenRespData
	23, // 2: vc.bizaccount.AccountSignInResp.data:type_name -> vc.svcaccount.TokenRespData
	23, // 3: vc.bizaccount.RefreshTokenResp.data:type_name -> vc.svcaccount.TokenRespData
	24, // 4: vc.bizaccount.GetUserInfoResp.data:type_name -> vc.svcaccount.UserInfo
	16, // 5: vc.bizaccount.GetFollowingUsersResp.data:type_name -> vc.bizaccount.GetFollowingUsersRespData
	24, // 6: vc.bizaccount.GetFollowingUsersRespData.users:type_name -> vc.svcaccount.UserInfo
	19, // 7: vc.bizaccount.GetFollowersResp.data:type_name -> vc.bizaccount.GetFollowersRespData
	24, // 8: vc.bizaccount.GetFollowersRespData.users:type_name -> vc.svcaccount.UserInfo
	25, // 9: vc.bizaccount.BatchCheckFollowingResp.data:type_name -> vc.svcaccount.FollowDataItem
	26, // 10: vc.bizaccount.s.GetVerificationCode:input_type -> vc.svcaccount.GetVerificationCodeReq
	27, // 11: vc.bizaccount.s.AccountSignUp:input_type -> vc.svcaccount.AccountSignUpReq
	28, // 12: vc.bizaccount.s.AccountSignIn:input_type -> vc.svcaccount.AccountSignInReq
	29, // 13: vc.bizaccount.s.AccountSignOut:input_type -> vc.svcaccount.AccountSignOutReq
	30, // 14: vc.bizaccount.s.RefreshToken:input_type -> vc.svcaccount.RefreshTokenReq
	31, // 15: vc.bizaccount.s.AccountDelete:input_type -> vc.svcaccount.AccountDeleteReq
	32, // 16: vc.bizaccount.s.GetUserInfo:input_type -> vc.svcaccount.GetUserInfoReq
	33, // 17: vc.bizaccount.s.UpdateUserInfo:input_type -> vc.svcaccount.UpdateUserInfoReq
	8,  // 18: vc.bizaccount.s.FollowUser:input_type -> vc.bizaccount.FollowUserReq
	10, // 19: vc.bizaccount.s.UnfollowUser:input_type -> vc.bizaccount.UnfollowUserReq
	12, // 20: vc.bizaccount.s.CheckFollowing:input_type -> vc.bizaccount.CheckFollowingReq
	14, // 21: vc.bizaccount.s.GetFollowingUsers:input_type -> vc.bizaccount.GetFollowingUsersReq
	17, // 22: vc.bizaccount.s.GetFollowers:input_type -> vc.bizaccount.GetFollowersReq
	20, // 23: vc.bizaccount.s.BatchCheckFollowing:input_type -> vc.bizaccount.BatchCheckFollowingReq
	0,  // 24: vc.bizaccount.s.GetVerificationCode:output_type -> vc.bizaccount.GetVerificationCodeResp
	1,  // 25: vc.bizaccount.s.AccountSignUp:output_type -> vc.bizaccount.AccountSignUpResp
	2,  // 26: vc.bizaccount.s.AccountSignIn:output_type -> vc.bizaccount.AccountSignInResp
	3,  // 27: vc.bizaccount.s.AccountSignOut:output_type -> vc.bizaccount.AccountSignOutResp
	4,  // 28: vc.bizaccount.s.RefreshToken:output_type -> vc.bizaccount.RefreshTokenResp
	5,  // 29: vc.bizaccount.s.AccountDelete:output_type -> vc.bizaccount.AccountDeleteResp
	6,  // 30: vc.bizaccount.s.GetUserInfo:output_type -> vc.bizaccount.GetUserInfoResp
	7,  // 31: vc.bizaccount.s.UpdateUserInfo:output_type -> vc.bizaccount.UpdateUserInfoResp
	9,  // 32: vc.bizaccount.s.FollowUser:output_type -> vc.bizaccount.FollowUserResp
	11, // 33: vc.bizaccount.s.UnfollowUser:output_type -> vc.bizaccount.UnfollowUserResp
	13, // 34: vc.bizaccount.s.CheckFollowing:output_type -> vc.bizaccount.CheckFollowingResp
	15, // 35: vc.bizaccount.s.GetFollowingUsers:output_type -> vc.bizaccount.GetFollowingUsersResp
	18, // 36: vc.bizaccount.s.GetFollowers:output_type -> vc.bizaccount.GetFollowersResp
	21, // 37: vc.bizaccount.s.BatchCheckFollowing:output_type -> vc.bizaccount.BatchCheckFollowingResp
	24, // [24:38] is the sub-list for method output_type
	10, // [10:24] is the sub-list for method input_type
	10, // [10:10] is the sub-list for extension type_name
	10, // [10:10] is the sub-list for extension extendee
	0,  // [0:10] is the sub-list for field type_name
}

func init() { file_bizaccount_proto_init() }
func file_bizaccount_proto_init() {
	if File_bizaccount_proto != nil {
		return
	}
	if !protoimpl.UnsafeEnabled {
		file_bizaccount_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetVerificationCodeResp); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_bizaccount_proto_msgTypes[1].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*AccountSignUpResp); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_bizaccount_proto_msgTypes[2].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*AccountSignInResp); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_bizaccount_proto_msgTypes[3].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*AccountSignOutResp); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_bizaccount_proto_msgTypes[4].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*RefreshTokenResp); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_bizaccount_proto_msgTypes[5].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*AccountDeleteResp); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_bizaccount_proto_msgTypes[6].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetUserInfoResp); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_bizaccount_proto_msgTypes[7].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*UpdateUserInfoResp); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_bizaccount_proto_msgTypes[8].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*FollowUserReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_bizaccount_proto_msgTypes[9].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*FollowUserResp); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_bizaccount_proto_msgTypes[10].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*UnfollowUserReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_bizaccount_proto_msgTypes[11].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*UnfollowUserResp); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_bizaccount_proto_msgTypes[12].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CheckFollowingReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_bizaccount_proto_msgTypes[13].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CheckFollowingResp); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_bizaccount_proto_msgTypes[14].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetFollowingUsersReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_bizaccount_proto_msgTypes[15].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetFollowingUsersResp); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_bizaccount_proto_msgTypes[16].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetFollowingUsersRespData); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_bizaccount_proto_msgTypes[17].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetFollowersReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_bizaccount_proto_msgTypes[18].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetFollowersResp); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_bizaccount_proto_msgTypes[19].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetFollowersRespData); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_bizaccount_proto_msgTypes[20].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*BatchCheckFollowingReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_bizaccount_proto_msgTypes[21].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*BatchCheckFollowingResp); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_bizaccount_proto_rawDesc,
			NumEnums:      0,
			NumMessages:   22,
			NumExtensions: 0,
			NumServices:   1,
		},
		GoTypes:           file_bizaccount_proto_goTypes,
		DependencyIndexes: file_bizaccount_proto_depIdxs,
		MessageInfos:      file_bizaccount_proto_msgTypes,
	}.Build()
	File_bizaccount_proto = out.File
	file_bizaccount_proto_rawDesc = nil
	file_bizaccount_proto_goTypes = nil
	file_bizaccount_proto_depIdxs = nil
}
