syntax = "proto3";

package vc.bizaccount;
option go_package = "new-gitlab.xunlei.cn/vcproject/backends/proto/api/bizaccount;bizaccount";

import "google/api/annotations.proto";
import "svcaccount/svcaccount.proto";
import "protoc-gen-validate/validate/validate.proto";
import "google/api/field_behavior.proto";

message GetVerificationCodeResp {
    int32 code =1;
    string msg=2;
    svcaccount.GetVerificationCodeRespData data = 3;
}

message AccountSignUpResp {
    int32 code =1;
    string msg=2;
    svcaccount.TokenRespData data = 3;
}


message AccountSignInResp {
    int32 code =1;
    string msg=2;
    svcaccount.TokenRespData data = 3;
}

message AccountSignOutResp {
    int32 code =1;
    string msg=2;
}

message RefreshTokenResp {
    int32 code = 1;
    string msg = 2;
    svcaccount.TokenRespData data = 3;
}

message AccountDeleteResp {
    int32 code = 1;
    string msg = 2;
}

// 用户信息响应
message GetUserInfoResp {
    int32 code = 1;
    string msg = 2;
    svcaccount.UserInfo data = 3;
}

// 更新用户信息
message UpdateUserInfoResp {
  int32 code = 1;
  string msg = 2;
}

// 关注用户请求
message FollowUserReq {
  int64 following_id = 1 [(validate.rules).int64.gt = 0,(google.api.field_behavior) = REQUIRED]; // 被关注者ID
}

// 关注用户响应
message FollowUserResp {
    int32 code = 1;
    string msg = 2;
}

// 取消关注用户请求
message UnfollowUserReq {
  int64 following_id = 1 [(validate.rules).int64.gt = 0,(google.api.field_behavior) = REQUIRED]; // 被关注者ID
}

// 取消关注用户响应
message UnfollowUserResp {
    int32 code = 1;
    string msg = 2;
}

// 检查是否关注请求
message CheckFollowingReq {
  int64 following_id = 1 [(validate.rules).int64.gt = 0,(google.api.field_behavior) = REQUIRED]; // 被关注者ID
}

// 检查是否关注响应
message CheckFollowingResp {
    int32 code = 1;
    string msg = 2;
    bool is_following = 3;
}

// 获取关注的用户列表请求
message GetFollowingUsersReq {
  int32 page = 1; // 页码
  int32 page_size = 2; // 每页数量
}

// 获取关注的用户列表响应
message GetFollowingUsersResp {
    int32 code = 1;
    string msg = 2;
    GetFollowingUsersRespData data = 3;
}

// 获取关注的用户列表响应数据
message GetFollowingUsersRespData {
    repeated svcaccount.UserInfo users = 1;
    int64 total = 2;
}

// 获取粉丝列表请求
message GetFollowersReq {
  int32 page = 1; // 页码
  int32 page_size = 2; // 每页数量
}

// 获取粉丝列表响应
message GetFollowersResp {
    int32 code = 1;
    string msg = 2;
    GetFollowersRespData data = 3;
}

// 获取粉丝列表响应数据
message GetFollowersRespData {
    repeated svcaccount.UserInfo users = 1;
    int64 total = 2;
}

// 批量检查是否关注请求
message BatchCheckFollowingReq {
    repeated int64 following_ids = 1 [(validate.rules).repeated = {min_items: 1, max_items: 1000}]; // 被关注者ID列表
}

// 批量检查是否关注响应
message BatchCheckFollowingResp {
    int32 code = 1;
    string msg = 2;
    svcaccount.FollowDataItem data = 3; // key: 被关注者ID, value: FollowData
}

service s {
    // 获取验证码
    rpc GetVerificationCode(svcaccount.GetVerificationCodeReq) returns (GetVerificationCodeResp) {
        option (google.api.http) = {
            post: "/vc.bizaccount.s/v1/get_verification_code"
            body: "*"
        };
    }

    // 注册
    rpc AccountSignUp(svcaccount.AccountSignUpReq) returns (AccountSignUpResp) {
        option (google.api.http) = {
            post: "/vc.bizaccount.s/v1/signup"
            body: "*"
        };
    }

    // 登录
    rpc AccountSignIn(svcaccount.AccountSignInReq) returns (AccountSignInResp) {
        option (google.api.http) = {
            post: "/vc.bizaccount.s/v1/signin"
            body: "*"
        };
    }

    // 退出登录
    rpc AccountSignOut(svcaccount.AccountSignOutReq) returns (AccountSignOutResp) {
        option (google.api.http) = {
            post: "/vc.bizaccount.s/v1/signout"
            body: "*"
        };
    }

    // 刷新Token
    rpc RefreshToken(svcaccount.RefreshTokenReq) returns (RefreshTokenResp) {
        option (google.api.http) = {
          post: "/vc.bizaccount.s/v1/refresh_token"
          body: "*"
        };
    }

    // 注销账号
    rpc AccountDelete(svcaccount.AccountDeleteReq) returns (AccountDeleteResp) {
        option (google.api.http) = {
            post: "/vc.bizaccount.s/v1/delete"
            body: "*"
        };
    }

    // 获取用户信息
    rpc GetUserInfo(svcaccount.GetUserInfoReq) returns (GetUserInfoResp) {
        option (google.api.http) = {
            post: "/vc.bizaccount.s/v1/user_info"
            body: "*"
        };
    }

    // 更新用户信息
    rpc UpdateUserInfo(svcaccount.UpdateUserInfoReq) returns (UpdateUserInfoResp) {
        option (google.api.http) = {
            post: "/vc.bizaccount.s/v1/update_user"
            body: "*"
        };
    }
    
    // 关注用户
    rpc FollowUser(FollowUserReq) returns (FollowUserResp) {
        option (google.api.http) = {
            post: "/vc.bizaccount.s/v1/follow_user"
            body: "*"
        };
    }

    // 取消关注用户
    rpc UnfollowUser(UnfollowUserReq) returns (UnfollowUserResp) {
        option (google.api.http) = {
            post: "/vc.bizaccount.s/v1/unfollow_user"
            body: "*"
        };
    }

    // 检查是否关注
    rpc CheckFollowing(CheckFollowingReq) returns (CheckFollowingResp) {
        option (google.api.http) = {
            post: "/vc.bizaccount.s/v1/check_following"
            body: "*"
        };
    }

    // 获取关注的用户列表
    rpc GetFollowingUsers(GetFollowingUsersReq) returns (GetFollowingUsersResp) {
        option (google.api.http) = {
            post: "/vc.bizaccount.s/v1/get_following_users"
            body: "*"
        };
    }

    // 获取粉丝列表
    rpc GetFollowers(GetFollowersReq) returns (GetFollowersResp) {
        option (google.api.http) = {
            post: "/vc.bizaccount.s/v1/get_followers"
            body: "*"
        };
    }

    // 批量检查是否关注
    rpc BatchCheckFollowing(BatchCheckFollowingReq) returns (BatchCheckFollowingResp) {
        option (google.api.http) = {
            post: "/vc.bizaccount.s/v1/batch_check_following"
            body: "*"
        };
    }
}
