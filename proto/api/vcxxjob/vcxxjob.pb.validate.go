// Code generated by protoc-gen-validate. DO NOT EDIT.
// source: vcxxjob.proto

package vcxxjob

import (
	"bytes"
	"errors"
	"fmt"
	"net"
	"net/mail"
	"net/url"
	"regexp"
	"sort"
	"strings"
	"time"
	"unicode/utf8"

	"google.golang.org/protobuf/types/known/anypb"
)

// ensure the imports are used
var (
	_ = bytes.MinRead
	_ = errors.New("")
	_ = fmt.Print
	_ = utf8.UTFMax
	_ = (*regexp.Regexp)(nil)
	_ = (*strings.Reader)(nil)
	_ = net.IPv4len
	_ = time.Duration(0)
	_ = (*url.URL)(nil)
	_ = (*mail.Address)(nil)
	_ = anypb.Any{}
	_ = sort.Sort
)

// Validate checks the field values on AsyncTTSReq with the rules defined in
// the proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *AsyncTTSReq) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on AsyncTTSReq with the rules defined in
// the proto definition for this message. If any rules are violated, the
// result is a list of violation errors wrapped in AsyncTTSReqMultiError, or
// nil if none found.
func (m *AsyncTTSReq) ValidateAll() error {
	return m.validate(true)
}

func (m *AsyncTTSReq) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for ReferAudioUrl

	// no validation rules for ReferAudioText

	if utf8.RuneCountInString(m.GetText()) < 1 {
		err := AsyncTTSReqValidationError{
			field:  "Text",
			reason: "value length must be at least 1 runes",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if _, ok := _AsyncTTSReq_TtsEngine_InLookup[m.GetTtsEngine()]; !ok {
		err := AsyncTTSReqValidationError{
			field:  "TtsEngine",
			reason: "value must be in list [llasa fish_speech]",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	// no validation rules for OutId

	// no validation rules for Source

	// no validation rules for Ext

	// no validation rules for UseVc

	// no validation rules for UseRvc

	if len(errors) > 0 {
		return AsyncTTSReqMultiError(errors)
	}

	return nil
}

// AsyncTTSReqMultiError is an error wrapping multiple validation errors
// returned by AsyncTTSReq.ValidateAll() if the designated constraints aren't met.
type AsyncTTSReqMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m AsyncTTSReqMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m AsyncTTSReqMultiError) AllErrors() []error { return m }

// AsyncTTSReqValidationError is the validation error returned by
// AsyncTTSReq.Validate if the designated constraints aren't met.
type AsyncTTSReqValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e AsyncTTSReqValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e AsyncTTSReqValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e AsyncTTSReqValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e AsyncTTSReqValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e AsyncTTSReqValidationError) ErrorName() string { return "AsyncTTSReqValidationError" }

// Error satisfies the builtin error interface
func (e AsyncTTSReqValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sAsyncTTSReq.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = AsyncTTSReqValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = AsyncTTSReqValidationError{}

var _AsyncTTSReq_TtsEngine_InLookup = map[string]struct{}{
	"llasa":       {},
	"fish_speech": {},
}

// Validate checks the field values on AsyncTTSResp with the rules defined in
// the proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *AsyncTTSResp) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on AsyncTTSResp with the rules defined
// in the proto definition for this message. If any rules are violated, the
// result is a list of violation errors wrapped in AsyncTTSRespMultiError, or
// nil if none found.
func (m *AsyncTTSResp) ValidateAll() error {
	return m.validate(true)
}

func (m *AsyncTTSResp) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetBase()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, AsyncTTSRespValidationError{
					field:  "Base",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, AsyncTTSRespValidationError{
					field:  "Base",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetBase()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return AsyncTTSRespValidationError{
				field:  "Base",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetData()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, AsyncTTSRespValidationError{
					field:  "Data",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, AsyncTTSRespValidationError{
					field:  "Data",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetData()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return AsyncTTSRespValidationError{
				field:  "Data",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return AsyncTTSRespMultiError(errors)
	}

	return nil
}

// AsyncTTSRespMultiError is an error wrapping multiple validation errors
// returned by AsyncTTSResp.ValidateAll() if the designated constraints aren't met.
type AsyncTTSRespMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m AsyncTTSRespMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m AsyncTTSRespMultiError) AllErrors() []error { return m }

// AsyncTTSRespValidationError is the validation error returned by
// AsyncTTSResp.Validate if the designated constraints aren't met.
type AsyncTTSRespValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e AsyncTTSRespValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e AsyncTTSRespValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e AsyncTTSRespValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e AsyncTTSRespValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e AsyncTTSRespValidationError) ErrorName() string { return "AsyncTTSRespValidationError" }

// Error satisfies the builtin error interface
func (e AsyncTTSRespValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sAsyncTTSResp.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = AsyncTTSRespValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = AsyncTTSRespValidationError{}

// Validate checks the field values on AsyncTTSRespData with the rules defined
// in the proto definition for this message. If any rules are violated, the
// first error encountered is returned, or nil if there are no violations.
func (m *AsyncTTSRespData) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on AsyncTTSRespData with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// AsyncTTSRespDataMultiError, or nil if none found.
func (m *AsyncTTSRespData) ValidateAll() error {
	return m.validate(true)
}

func (m *AsyncTTSRespData) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for TaskId

	if len(errors) > 0 {
		return AsyncTTSRespDataMultiError(errors)
	}

	return nil
}

// AsyncTTSRespDataMultiError is an error wrapping multiple validation errors
// returned by AsyncTTSRespData.ValidateAll() if the designated constraints
// aren't met.
type AsyncTTSRespDataMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m AsyncTTSRespDataMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m AsyncTTSRespDataMultiError) AllErrors() []error { return m }

// AsyncTTSRespDataValidationError is the validation error returned by
// AsyncTTSRespData.Validate if the designated constraints aren't met.
type AsyncTTSRespDataValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e AsyncTTSRespDataValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e AsyncTTSRespDataValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e AsyncTTSRespDataValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e AsyncTTSRespDataValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e AsyncTTSRespDataValidationError) ErrorName() string { return "AsyncTTSRespDataValidationError" }

// Error satisfies the builtin error interface
func (e AsyncTTSRespDataValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sAsyncTTSRespData.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = AsyncTTSRespDataValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = AsyncTTSRespDataValidationError{}

// Validate checks the field values on AsyncBatchTTSReq with the rules defined
// in the proto definition for this message. If any rules are violated, the
// first error encountered is returned, or nil if there are no violations.
func (m *AsyncBatchTTSReq) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on AsyncBatchTTSReq with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// AsyncBatchTTSReqMultiError, or nil if none found.
func (m *AsyncBatchTTSReq) ValidateAll() error {
	return m.validate(true)
}

func (m *AsyncBatchTTSReq) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	for idx, item := range m.GetList() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, AsyncBatchTTSReqValidationError{
						field:  fmt.Sprintf("List[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, AsyncBatchTTSReqValidationError{
						field:  fmt.Sprintf("List[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return AsyncBatchTTSReqValidationError{
					field:  fmt.Sprintf("List[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	if len(errors) > 0 {
		return AsyncBatchTTSReqMultiError(errors)
	}

	return nil
}

// AsyncBatchTTSReqMultiError is an error wrapping multiple validation errors
// returned by AsyncBatchTTSReq.ValidateAll() if the designated constraints
// aren't met.
type AsyncBatchTTSReqMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m AsyncBatchTTSReqMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m AsyncBatchTTSReqMultiError) AllErrors() []error { return m }

// AsyncBatchTTSReqValidationError is the validation error returned by
// AsyncBatchTTSReq.Validate if the designated constraints aren't met.
type AsyncBatchTTSReqValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e AsyncBatchTTSReqValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e AsyncBatchTTSReqValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e AsyncBatchTTSReqValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e AsyncBatchTTSReqValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e AsyncBatchTTSReqValidationError) ErrorName() string { return "AsyncBatchTTSReqValidationError" }

// Error satisfies the builtin error interface
func (e AsyncBatchTTSReqValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sAsyncBatchTTSReq.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = AsyncBatchTTSReqValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = AsyncBatchTTSReqValidationError{}

// Validate checks the field values on AsyncBatchTTSResp with the rules defined
// in the proto definition for this message. If any rules are violated, the
// first error encountered is returned, or nil if there are no violations.
func (m *AsyncBatchTTSResp) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on AsyncBatchTTSResp with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// AsyncBatchTTSRespMultiError, or nil if none found.
func (m *AsyncBatchTTSResp) ValidateAll() error {
	return m.validate(true)
}

func (m *AsyncBatchTTSResp) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetBase()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, AsyncBatchTTSRespValidationError{
					field:  "Base",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, AsyncBatchTTSRespValidationError{
					field:  "Base",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetBase()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return AsyncBatchTTSRespValidationError{
				field:  "Base",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetData()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, AsyncBatchTTSRespValidationError{
					field:  "Data",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, AsyncBatchTTSRespValidationError{
					field:  "Data",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetData()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return AsyncBatchTTSRespValidationError{
				field:  "Data",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return AsyncBatchTTSRespMultiError(errors)
	}

	return nil
}

// AsyncBatchTTSRespMultiError is an error wrapping multiple validation errors
// returned by AsyncBatchTTSResp.ValidateAll() if the designated constraints
// aren't met.
type AsyncBatchTTSRespMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m AsyncBatchTTSRespMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m AsyncBatchTTSRespMultiError) AllErrors() []error { return m }

// AsyncBatchTTSRespValidationError is the validation error returned by
// AsyncBatchTTSResp.Validate if the designated constraints aren't met.
type AsyncBatchTTSRespValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e AsyncBatchTTSRespValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e AsyncBatchTTSRespValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e AsyncBatchTTSRespValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e AsyncBatchTTSRespValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e AsyncBatchTTSRespValidationError) ErrorName() string {
	return "AsyncBatchTTSRespValidationError"
}

// Error satisfies the builtin error interface
func (e AsyncBatchTTSRespValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sAsyncBatchTTSResp.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = AsyncBatchTTSRespValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = AsyncBatchTTSRespValidationError{}

// Validate checks the field values on AsyncBatchTTSRespData with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *AsyncBatchTTSRespData) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on AsyncBatchTTSRespData with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// AsyncBatchTTSRespDataMultiError, or nil if none found.
func (m *AsyncBatchTTSRespData) ValidateAll() error {
	return m.validate(true)
}

func (m *AsyncBatchTTSRespData) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	for idx, item := range m.GetList() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, AsyncBatchTTSRespDataValidationError{
						field:  fmt.Sprintf("List[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, AsyncBatchTTSRespDataValidationError{
						field:  fmt.Sprintf("List[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return AsyncBatchTTSRespDataValidationError{
					field:  fmt.Sprintf("List[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	if len(errors) > 0 {
		return AsyncBatchTTSRespDataMultiError(errors)
	}

	return nil
}

// AsyncBatchTTSRespDataMultiError is an error wrapping multiple validation
// errors returned by AsyncBatchTTSRespData.ValidateAll() if the designated
// constraints aren't met.
type AsyncBatchTTSRespDataMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m AsyncBatchTTSRespDataMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m AsyncBatchTTSRespDataMultiError) AllErrors() []error { return m }

// AsyncBatchTTSRespDataValidationError is the validation error returned by
// AsyncBatchTTSRespData.Validate if the designated constraints aren't met.
type AsyncBatchTTSRespDataValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e AsyncBatchTTSRespDataValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e AsyncBatchTTSRespDataValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e AsyncBatchTTSRespDataValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e AsyncBatchTTSRespDataValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e AsyncBatchTTSRespDataValidationError) ErrorName() string {
	return "AsyncBatchTTSRespDataValidationError"
}

// Error satisfies the builtin error interface
func (e AsyncBatchTTSRespDataValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sAsyncBatchTTSRespData.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = AsyncBatchTTSRespDataValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = AsyncBatchTTSRespDataValidationError{}

// Validate checks the field values on AsyncBatchTTSRespDataItem with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *AsyncBatchTTSRespDataItem) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on AsyncBatchTTSRespDataItem with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// AsyncBatchTTSRespDataItemMultiError, or nil if none found.
func (m *AsyncBatchTTSRespDataItem) ValidateAll() error {
	return m.validate(true)
}

func (m *AsyncBatchTTSRespDataItem) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for TaskId

	// no validation rules for OutId

	if len(errors) > 0 {
		return AsyncBatchTTSRespDataItemMultiError(errors)
	}

	return nil
}

// AsyncBatchTTSRespDataItemMultiError is an error wrapping multiple validation
// errors returned by AsyncBatchTTSRespDataItem.ValidateAll() if the
// designated constraints aren't met.
type AsyncBatchTTSRespDataItemMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m AsyncBatchTTSRespDataItemMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m AsyncBatchTTSRespDataItemMultiError) AllErrors() []error { return m }

// AsyncBatchTTSRespDataItemValidationError is the validation error returned by
// AsyncBatchTTSRespDataItem.Validate if the designated constraints aren't met.
type AsyncBatchTTSRespDataItemValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e AsyncBatchTTSRespDataItemValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e AsyncBatchTTSRespDataItemValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e AsyncBatchTTSRespDataItemValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e AsyncBatchTTSRespDataItemValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e AsyncBatchTTSRespDataItemValidationError) ErrorName() string {
	return "AsyncBatchTTSRespDataItemValidationError"
}

// Error satisfies the builtin error interface
func (e AsyncBatchTTSRespDataItemValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sAsyncBatchTTSRespDataItem.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = AsyncBatchTTSRespDataItemValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = AsyncBatchTTSRespDataItemValidationError{}

// Validate checks the field values on UpdateVcTaskReq with the rules defined
// in the proto definition for this message. If any rules are violated, the
// first error encountered is returned, or nil if there are no violations.
func (m *UpdateVcTaskReq) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on UpdateVcTaskReq with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// UpdateVcTaskReqMultiError, or nil if none found.
func (m *UpdateVcTaskReq) ValidateAll() error {
	return m.validate(true)
}

func (m *UpdateVcTaskReq) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for TaskId

	// no validation rules for Status

	// no validation rules for TtsAudioUrl

	// no validation rules for TtsAudioDuration

	if len(errors) > 0 {
		return UpdateVcTaskReqMultiError(errors)
	}

	return nil
}

// UpdateVcTaskReqMultiError is an error wrapping multiple validation errors
// returned by UpdateVcTaskReq.ValidateAll() if the designated constraints
// aren't met.
type UpdateVcTaskReqMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m UpdateVcTaskReqMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m UpdateVcTaskReqMultiError) AllErrors() []error { return m }

// UpdateVcTaskReqValidationError is the validation error returned by
// UpdateVcTaskReq.Validate if the designated constraints aren't met.
type UpdateVcTaskReqValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e UpdateVcTaskReqValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e UpdateVcTaskReqValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e UpdateVcTaskReqValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e UpdateVcTaskReqValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e UpdateVcTaskReqValidationError) ErrorName() string { return "UpdateVcTaskReqValidationError" }

// Error satisfies the builtin error interface
func (e UpdateVcTaskReqValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sUpdateVcTaskReq.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = UpdateVcTaskReqValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = UpdateVcTaskReqValidationError{}

// Validate checks the field values on UpdateVcTaskResp with the rules defined
// in the proto definition for this message. If any rules are violated, the
// first error encountered is returned, or nil if there are no violations.
func (m *UpdateVcTaskResp) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on UpdateVcTaskResp with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// UpdateVcTaskRespMultiError, or nil if none found.
func (m *UpdateVcTaskResp) ValidateAll() error {
	return m.validate(true)
}

func (m *UpdateVcTaskResp) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetBase()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, UpdateVcTaskRespValidationError{
					field:  "Base",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, UpdateVcTaskRespValidationError{
					field:  "Base",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetBase()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return UpdateVcTaskRespValidationError{
				field:  "Base",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return UpdateVcTaskRespMultiError(errors)
	}

	return nil
}

// UpdateVcTaskRespMultiError is an error wrapping multiple validation errors
// returned by UpdateVcTaskResp.ValidateAll() if the designated constraints
// aren't met.
type UpdateVcTaskRespMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m UpdateVcTaskRespMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m UpdateVcTaskRespMultiError) AllErrors() []error { return m }

// UpdateVcTaskRespValidationError is the validation error returned by
// UpdateVcTaskResp.Validate if the designated constraints aren't met.
type UpdateVcTaskRespValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e UpdateVcTaskRespValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e UpdateVcTaskRespValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e UpdateVcTaskRespValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e UpdateVcTaskRespValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e UpdateVcTaskRespValidationError) ErrorName() string { return "UpdateVcTaskRespValidationError" }

// Error satisfies the builtin error interface
func (e UpdateVcTaskRespValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sUpdateVcTaskResp.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = UpdateVcTaskRespValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = UpdateVcTaskRespValidationError{}
