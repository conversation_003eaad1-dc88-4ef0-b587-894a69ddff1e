// Code generated by protoc-gen-go-grpc. DO NOT EDIT.
// versions:
// - protoc-gen-go-grpc v1.5.1
// - protoc             v5.29.3
// source: vcxxjob.proto

package vcxxjob

import (
	context "context"
	grpc "google.golang.org/grpc"
	codes "google.golang.org/grpc/codes"
	status "google.golang.org/grpc/status"
)

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
// Requires gRPC-Go v1.64.0 or later.
const _ = grpc.SupportPackageIsVersion9

const (
	S_AsyncTTS_FullMethodName      = "/vc.vcxxjob.s/AsyncTTS"
	S_AsyncBatchTTS_FullMethodName = "/vc.vcxxjob.s/AsyncBatchTTS"
	S_UpdateVcTask_FullMethodName  = "/vc.vcxxjob.s/UpdateVcTask"
)

// SClient is the client API for S service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://pkg.go.dev/google.golang.org/grpc/?tab=doc#ClientConn.NewStream.
type SClient interface {
	AsyncTTS(ctx context.Context, in *AsyncTTSReq, opts ...grpc.CallOption) (*AsyncTTSResp, error)
	AsyncBatchTTS(ctx context.Context, in *AsyncBatchTTSReq, opts ...grpc.CallOption) (*AsyncBatchTTSResp, error)
	UpdateVcTask(ctx context.Context, in *UpdateVcTaskReq, opts ...grpc.CallOption) (*UpdateVcTaskResp, error)
}

type sClient struct {
	cc grpc.ClientConnInterface
}

func NewSClient(cc grpc.ClientConnInterface) SClient {
	return &sClient{cc}
}

func (c *sClient) AsyncTTS(ctx context.Context, in *AsyncTTSReq, opts ...grpc.CallOption) (*AsyncTTSResp, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(AsyncTTSResp)
	err := c.cc.Invoke(ctx, S_AsyncTTS_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *sClient) AsyncBatchTTS(ctx context.Context, in *AsyncBatchTTSReq, opts ...grpc.CallOption) (*AsyncBatchTTSResp, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(AsyncBatchTTSResp)
	err := c.cc.Invoke(ctx, S_AsyncBatchTTS_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *sClient) UpdateVcTask(ctx context.Context, in *UpdateVcTaskReq, opts ...grpc.CallOption) (*UpdateVcTaskResp, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(UpdateVcTaskResp)
	err := c.cc.Invoke(ctx, S_UpdateVcTask_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// SServer is the server API for S service.
// All implementations should embed UnimplementedSServer
// for forward compatibility.
type SServer interface {
	AsyncTTS(context.Context, *AsyncTTSReq) (*AsyncTTSResp, error)
	AsyncBatchTTS(context.Context, *AsyncBatchTTSReq) (*AsyncBatchTTSResp, error)
	UpdateVcTask(context.Context, *UpdateVcTaskReq) (*UpdateVcTaskResp, error)
}

// UnimplementedSServer should be embedded to have
// forward compatible implementations.
//
// NOTE: this should be embedded by value instead of pointer to avoid a nil
// pointer dereference when methods are called.
type UnimplementedSServer struct{}

func (UnimplementedSServer) AsyncTTS(context.Context, *AsyncTTSReq) (*AsyncTTSResp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method AsyncTTS not implemented")
}
func (UnimplementedSServer) AsyncBatchTTS(context.Context, *AsyncBatchTTSReq) (*AsyncBatchTTSResp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method AsyncBatchTTS not implemented")
}
func (UnimplementedSServer) UpdateVcTask(context.Context, *UpdateVcTaskReq) (*UpdateVcTaskResp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method UpdateVcTask not implemented")
}
func (UnimplementedSServer) testEmbeddedByValue() {}

// UnsafeSServer may be embedded to opt out of forward compatibility for this service.
// Use of this interface is not recommended, as added methods to SServer will
// result in compilation errors.
type UnsafeSServer interface {
	mustEmbedUnimplementedSServer()
}

func RegisterSServer(s grpc.ServiceRegistrar, srv SServer) {
	// If the following call pancis, it indicates UnimplementedSServer was
	// embedded by pointer and is nil.  This will cause panics if an
	// unimplemented method is ever invoked, so we test this at initialization
	// time to prevent it from happening at runtime later due to I/O.
	if t, ok := srv.(interface{ testEmbeddedByValue() }); ok {
		t.testEmbeddedByValue()
	}
	s.RegisterService(&S_ServiceDesc, srv)
}

func _S_AsyncTTS_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(AsyncTTSReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(SServer).AsyncTTS(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: S_AsyncTTS_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(SServer).AsyncTTS(ctx, req.(*AsyncTTSReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _S_AsyncBatchTTS_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(AsyncBatchTTSReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(SServer).AsyncBatchTTS(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: S_AsyncBatchTTS_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(SServer).AsyncBatchTTS(ctx, req.(*AsyncBatchTTSReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _S_UpdateVcTask_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(UpdateVcTaskReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(SServer).UpdateVcTask(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: S_UpdateVcTask_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(SServer).UpdateVcTask(ctx, req.(*UpdateVcTaskReq))
	}
	return interceptor(ctx, in, info, handler)
}

// S_ServiceDesc is the grpc.ServiceDesc for S service.
// It's only intended for direct use with grpc.RegisterService,
// and not to be introspected or modified (even as a copy)
var S_ServiceDesc = grpc.ServiceDesc{
	ServiceName: "vc.vcxxjob.s",
	HandlerType: (*SServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "AsyncTTS",
			Handler:    _S_AsyncTTS_Handler,
		},
		{
			MethodName: "AsyncBatchTTS",
			Handler:    _S_AsyncBatchTTS_Handler,
		},
		{
			MethodName: "UpdateVcTask",
			Handler:    _S_UpdateVcTask_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "vcxxjob.proto",
}
