// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.36.6
// 	protoc        v5.29.3
// source: vcxxjob.proto

package vcxxjob

import (
	_ "github.com/envoyproxy/protoc-gen-validate/validate"
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	common "new-gitlab.xunlei.cn/vcproject/backends/proto/api/common"
	reflect "reflect"
	sync "sync"
	unsafe "unsafe"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type VcTaskStatus int32

const (
	VcTaskStatus_STATUS_UNSPECIFIED VcTaskStatus = 0 // 未指定
	VcTaskStatus_STATUS_PROCESSING  VcTaskStatus = 1 // 进行中
	VcTaskStatus_STATUS_COMPLETED   VcTaskStatus = 2 // 完成
	VcTaskStatus_STATUS_FAILED      VcTaskStatus = 3 // 失败
)

// Enum value maps for VcTaskStatus.
var (
	VcTaskStatus_name = map[int32]string{
		0: "STATUS_UNSPECIFIED",
		1: "STATUS_PROCESSING",
		2: "STATUS_COMPLETED",
		3: "STATUS_FAILED",
	}
	VcTaskStatus_value = map[string]int32{
		"STATUS_UNSPECIFIED": 0,
		"STATUS_PROCESSING":  1,
		"STATUS_COMPLETED":   2,
		"STATUS_FAILED":      3,
	}
)

func (x VcTaskStatus) Enum() *VcTaskStatus {
	p := new(VcTaskStatus)
	*p = x
	return p
}

func (x VcTaskStatus) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (VcTaskStatus) Descriptor() protoreflect.EnumDescriptor {
	return file_vcxxjob_proto_enumTypes[0].Descriptor()
}

func (VcTaskStatus) Type() protoreflect.EnumType {
	return &file_vcxxjob_proto_enumTypes[0]
}

func (x VcTaskStatus) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use VcTaskStatus.Descriptor instead.
func (VcTaskStatus) EnumDescriptor() ([]byte, []int) {
	return file_vcxxjob_proto_rawDescGZIP(), []int{0}
}

type AsyncTTSReq struct {
	state          protoimpl.MessageState `protogen:"open.v1"`
	ReferAudioUrl  string                 `protobuf:"bytes,1,opt,name=refer_audio_url,json=referAudioUrl,proto3" json:"refer_audio_url,omitempty"`
	ReferAudioText string                 `protobuf:"bytes,2,opt,name=refer_audio_text,json=referAudioText,proto3" json:"refer_audio_text,omitempty"`
	Text           string                 `protobuf:"bytes,3,opt,name=text,proto3" json:"text,omitempty"`
	TtsEngine      string                 `protobuf:"bytes,4,opt,name=tts_engine,json=ttsEngine,proto3" json:"tts_engine,omitempty"`
	OutId          string                 `protobuf:"bytes,5,opt,name=out_id,json=outId,proto3" json:"out_id,omitempty"`
	Source         int32                  `protobuf:"varint,6,opt,name=source,proto3" json:"source,omitempty"`
	Ext            string                 `protobuf:"bytes,7,opt,name=ext,proto3" json:"ext,omitempty"`
	UseVc          bool                   `protobuf:"varint,8,opt,name=use_vc,json=useVc,proto3" json:"use_vc,omitempty"`    // 是否使用变声
	UseRvc         bool                   `protobuf:"varint,9,opt,name=use_rvc,json=useRvc,proto3" json:"use_rvc,omitempty"` // 是否使用rvc变声
	unknownFields  protoimpl.UnknownFields
	sizeCache      protoimpl.SizeCache
}

func (x *AsyncTTSReq) Reset() {
	*x = AsyncTTSReq{}
	mi := &file_vcxxjob_proto_msgTypes[0]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *AsyncTTSReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*AsyncTTSReq) ProtoMessage() {}

func (x *AsyncTTSReq) ProtoReflect() protoreflect.Message {
	mi := &file_vcxxjob_proto_msgTypes[0]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use AsyncTTSReq.ProtoReflect.Descriptor instead.
func (*AsyncTTSReq) Descriptor() ([]byte, []int) {
	return file_vcxxjob_proto_rawDescGZIP(), []int{0}
}

func (x *AsyncTTSReq) GetReferAudioUrl() string {
	if x != nil {
		return x.ReferAudioUrl
	}
	return ""
}

func (x *AsyncTTSReq) GetReferAudioText() string {
	if x != nil {
		return x.ReferAudioText
	}
	return ""
}

func (x *AsyncTTSReq) GetText() string {
	if x != nil {
		return x.Text
	}
	return ""
}

func (x *AsyncTTSReq) GetTtsEngine() string {
	if x != nil {
		return x.TtsEngine
	}
	return ""
}

func (x *AsyncTTSReq) GetOutId() string {
	if x != nil {
		return x.OutId
	}
	return ""
}

func (x *AsyncTTSReq) GetSource() int32 {
	if x != nil {
		return x.Source
	}
	return 0
}

func (x *AsyncTTSReq) GetExt() string {
	if x != nil {
		return x.Ext
	}
	return ""
}

func (x *AsyncTTSReq) GetUseVc() bool {
	if x != nil {
		return x.UseVc
	}
	return false
}

func (x *AsyncTTSReq) GetUseRvc() bool {
	if x != nil {
		return x.UseRvc
	}
	return false
}

type AsyncTTSResp struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Base          *common.SvcBaseResp    `protobuf:"bytes,1,opt,name=base,proto3" json:"base,omitempty"`
	Data          *AsyncTTSRespData      `protobuf:"bytes,2,opt,name=data,proto3" json:"data,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *AsyncTTSResp) Reset() {
	*x = AsyncTTSResp{}
	mi := &file_vcxxjob_proto_msgTypes[1]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *AsyncTTSResp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*AsyncTTSResp) ProtoMessage() {}

func (x *AsyncTTSResp) ProtoReflect() protoreflect.Message {
	mi := &file_vcxxjob_proto_msgTypes[1]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use AsyncTTSResp.ProtoReflect.Descriptor instead.
func (*AsyncTTSResp) Descriptor() ([]byte, []int) {
	return file_vcxxjob_proto_rawDescGZIP(), []int{1}
}

func (x *AsyncTTSResp) GetBase() *common.SvcBaseResp {
	if x != nil {
		return x.Base
	}
	return nil
}

func (x *AsyncTTSResp) GetData() *AsyncTTSRespData {
	if x != nil {
		return x.Data
	}
	return nil
}

type AsyncTTSRespData struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	TaskId        string                 `protobuf:"bytes,1,opt,name=task_id,json=taskId,proto3" json:"task_id,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *AsyncTTSRespData) Reset() {
	*x = AsyncTTSRespData{}
	mi := &file_vcxxjob_proto_msgTypes[2]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *AsyncTTSRespData) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*AsyncTTSRespData) ProtoMessage() {}

func (x *AsyncTTSRespData) ProtoReflect() protoreflect.Message {
	mi := &file_vcxxjob_proto_msgTypes[2]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use AsyncTTSRespData.ProtoReflect.Descriptor instead.
func (*AsyncTTSRespData) Descriptor() ([]byte, []int) {
	return file_vcxxjob_proto_rawDescGZIP(), []int{2}
}

func (x *AsyncTTSRespData) GetTaskId() string {
	if x != nil {
		return x.TaskId
	}
	return ""
}

type AsyncBatchTTSReq struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	List          []*AsyncTTSReq         `protobuf:"bytes,1,rep,name=list,proto3" json:"list,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *AsyncBatchTTSReq) Reset() {
	*x = AsyncBatchTTSReq{}
	mi := &file_vcxxjob_proto_msgTypes[3]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *AsyncBatchTTSReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*AsyncBatchTTSReq) ProtoMessage() {}

func (x *AsyncBatchTTSReq) ProtoReflect() protoreflect.Message {
	mi := &file_vcxxjob_proto_msgTypes[3]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use AsyncBatchTTSReq.ProtoReflect.Descriptor instead.
func (*AsyncBatchTTSReq) Descriptor() ([]byte, []int) {
	return file_vcxxjob_proto_rawDescGZIP(), []int{3}
}

func (x *AsyncBatchTTSReq) GetList() []*AsyncTTSReq {
	if x != nil {
		return x.List
	}
	return nil
}

type AsyncBatchTTSResp struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Base          *common.SvcBaseResp    `protobuf:"bytes,1,opt,name=base,proto3" json:"base,omitempty"`
	Data          *AsyncBatchTTSRespData `protobuf:"bytes,2,opt,name=data,proto3" json:"data,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *AsyncBatchTTSResp) Reset() {
	*x = AsyncBatchTTSResp{}
	mi := &file_vcxxjob_proto_msgTypes[4]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *AsyncBatchTTSResp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*AsyncBatchTTSResp) ProtoMessage() {}

func (x *AsyncBatchTTSResp) ProtoReflect() protoreflect.Message {
	mi := &file_vcxxjob_proto_msgTypes[4]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use AsyncBatchTTSResp.ProtoReflect.Descriptor instead.
func (*AsyncBatchTTSResp) Descriptor() ([]byte, []int) {
	return file_vcxxjob_proto_rawDescGZIP(), []int{4}
}

func (x *AsyncBatchTTSResp) GetBase() *common.SvcBaseResp {
	if x != nil {
		return x.Base
	}
	return nil
}

func (x *AsyncBatchTTSResp) GetData() *AsyncBatchTTSRespData {
	if x != nil {
		return x.Data
	}
	return nil
}

type AsyncBatchTTSRespData struct {
	state         protoimpl.MessageState       `protogen:"open.v1"`
	List          []*AsyncBatchTTSRespDataItem `protobuf:"bytes,1,rep,name=list,proto3" json:"list,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *AsyncBatchTTSRespData) Reset() {
	*x = AsyncBatchTTSRespData{}
	mi := &file_vcxxjob_proto_msgTypes[5]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *AsyncBatchTTSRespData) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*AsyncBatchTTSRespData) ProtoMessage() {}

func (x *AsyncBatchTTSRespData) ProtoReflect() protoreflect.Message {
	mi := &file_vcxxjob_proto_msgTypes[5]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use AsyncBatchTTSRespData.ProtoReflect.Descriptor instead.
func (*AsyncBatchTTSRespData) Descriptor() ([]byte, []int) {
	return file_vcxxjob_proto_rawDescGZIP(), []int{5}
}

func (x *AsyncBatchTTSRespData) GetList() []*AsyncBatchTTSRespDataItem {
	if x != nil {
		return x.List
	}
	return nil
}

type AsyncBatchTTSRespDataItem struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	TaskId        string                 `protobuf:"bytes,1,opt,name=task_id,json=taskId,proto3" json:"task_id,omitempty"`
	OutId         string                 `protobuf:"bytes,2,opt,name=out_id,json=outId,proto3" json:"out_id,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *AsyncBatchTTSRespDataItem) Reset() {
	*x = AsyncBatchTTSRespDataItem{}
	mi := &file_vcxxjob_proto_msgTypes[6]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *AsyncBatchTTSRespDataItem) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*AsyncBatchTTSRespDataItem) ProtoMessage() {}

func (x *AsyncBatchTTSRespDataItem) ProtoReflect() protoreflect.Message {
	mi := &file_vcxxjob_proto_msgTypes[6]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use AsyncBatchTTSRespDataItem.ProtoReflect.Descriptor instead.
func (*AsyncBatchTTSRespDataItem) Descriptor() ([]byte, []int) {
	return file_vcxxjob_proto_rawDescGZIP(), []int{6}
}

func (x *AsyncBatchTTSRespDataItem) GetTaskId() string {
	if x != nil {
		return x.TaskId
	}
	return ""
}

func (x *AsyncBatchTTSRespDataItem) GetOutId() string {
	if x != nil {
		return x.OutId
	}
	return ""
}

type UpdateVcTaskReq struct {
	state            protoimpl.MessageState `protogen:"open.v1"`
	TaskId           string                 `protobuf:"bytes,1,opt,name=task_id,json=taskId,proto3" json:"task_id,omitempty"`
	Status           string                 `protobuf:"bytes,2,opt,name=status,proto3" json:"status,omitempty"`
	TtsAudioUrl      string                 `protobuf:"bytes,3,opt,name=tts_audio_url,json=ttsAudioUrl,proto3" json:"tts_audio_url,omitempty"`
	TtsAudioDuration int32                  `protobuf:"varint,4,opt,name=tts_audio_duration,json=ttsAudioDuration,proto3" json:"tts_audio_duration,omitempty"`
	unknownFields    protoimpl.UnknownFields
	sizeCache        protoimpl.SizeCache
}

func (x *UpdateVcTaskReq) Reset() {
	*x = UpdateVcTaskReq{}
	mi := &file_vcxxjob_proto_msgTypes[7]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *UpdateVcTaskReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UpdateVcTaskReq) ProtoMessage() {}

func (x *UpdateVcTaskReq) ProtoReflect() protoreflect.Message {
	mi := &file_vcxxjob_proto_msgTypes[7]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UpdateVcTaskReq.ProtoReflect.Descriptor instead.
func (*UpdateVcTaskReq) Descriptor() ([]byte, []int) {
	return file_vcxxjob_proto_rawDescGZIP(), []int{7}
}

func (x *UpdateVcTaskReq) GetTaskId() string {
	if x != nil {
		return x.TaskId
	}
	return ""
}

func (x *UpdateVcTaskReq) GetStatus() string {
	if x != nil {
		return x.Status
	}
	return ""
}

func (x *UpdateVcTaskReq) GetTtsAudioUrl() string {
	if x != nil {
		return x.TtsAudioUrl
	}
	return ""
}

func (x *UpdateVcTaskReq) GetTtsAudioDuration() int32 {
	if x != nil {
		return x.TtsAudioDuration
	}
	return 0
}

type UpdateVcTaskResp struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Base          *common.SvcBaseResp    `protobuf:"bytes,1,opt,name=base,proto3" json:"base,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *UpdateVcTaskResp) Reset() {
	*x = UpdateVcTaskResp{}
	mi := &file_vcxxjob_proto_msgTypes[8]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *UpdateVcTaskResp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UpdateVcTaskResp) ProtoMessage() {}

func (x *UpdateVcTaskResp) ProtoReflect() protoreflect.Message {
	mi := &file_vcxxjob_proto_msgTypes[8]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UpdateVcTaskResp.ProtoReflect.Descriptor instead.
func (*UpdateVcTaskResp) Descriptor() ([]byte, []int) {
	return file_vcxxjob_proto_rawDescGZIP(), []int{8}
}

func (x *UpdateVcTaskResp) GetBase() *common.SvcBaseResp {
	if x != nil {
		return x.Base
	}
	return nil
}

var File_vcxxjob_proto protoreflect.FileDescriptor

const file_vcxxjob_proto_rawDesc = "" +
	"\n" +
	"\rvcxxjob.proto\x12\n" +
	"vc.vcxxjob\x1a+protoc-gen-validate/validate/validate.proto\x1a\x13common/common.proto\"\xa7\x02\n" +
	"\vAsyncTTSReq\x12&\n" +
	"\x0frefer_audio_url\x18\x01 \x01(\tR\rreferAudioUrl\x12(\n" +
	"\x10refer_audio_text\x18\x02 \x01(\tR\x0ereferAudioText\x12\x1b\n" +
	"\x04text\x18\x03 \x01(\tB\a\xfaB\x04r\x02\x10\x01R\x04text\x128\n" +
	"\n" +
	"tts_engine\x18\x04 \x01(\tB\x19\xfaB\x16r\x14R\x05llasaR\vfish_speechR\tttsEngine\x12\x15\n" +
	"\x06out_id\x18\x05 \x01(\tR\x05outId\x12\x16\n" +
	"\x06source\x18\x06 \x01(\x05R\x06source\x12\x10\n" +
	"\x03ext\x18\a \x01(\tR\x03ext\x12\x15\n" +
	"\x06use_vc\x18\b \x01(\bR\x05useVc\x12\x17\n" +
	"\ause_rvc\x18\t \x01(\bR\x06useRvc\"i\n" +
	"\fAsyncTTSResp\x12'\n" +
	"\x04base\x18\x01 \x01(\v2\x13.common.SvcBaseRespR\x04base\x120\n" +
	"\x04data\x18\x02 \x01(\v2\x1c.vc.vcxxjob.AsyncTTSRespDataR\x04data\"+\n" +
	"\x10AsyncTTSRespData\x12\x17\n" +
	"\atask_id\x18\x01 \x01(\tR\x06taskId\"?\n" +
	"\x10AsyncBatchTTSReq\x12+\n" +
	"\x04list\x18\x01 \x03(\v2\x17.vc.vcxxjob.AsyncTTSReqR\x04list\"s\n" +
	"\x11AsyncBatchTTSResp\x12'\n" +
	"\x04base\x18\x01 \x01(\v2\x13.common.SvcBaseRespR\x04base\x125\n" +
	"\x04data\x18\x02 \x01(\v2!.vc.vcxxjob.AsyncBatchTTSRespDataR\x04data\"R\n" +
	"\x15AsyncBatchTTSRespData\x129\n" +
	"\x04list\x18\x01 \x03(\v2%.vc.vcxxjob.AsyncBatchTTSRespDataItemR\x04list\"K\n" +
	"\x19AsyncBatchTTSRespDataItem\x12\x17\n" +
	"\atask_id\x18\x01 \x01(\tR\x06taskId\x12\x15\n" +
	"\x06out_id\x18\x02 \x01(\tR\x05outId\"\x94\x01\n" +
	"\x0fUpdateVcTaskReq\x12\x17\n" +
	"\atask_id\x18\x01 \x01(\tR\x06taskId\x12\x16\n" +
	"\x06status\x18\x02 \x01(\tR\x06status\x12\"\n" +
	"\rtts_audio_url\x18\x03 \x01(\tR\vttsAudioUrl\x12,\n" +
	"\x12tts_audio_duration\x18\x04 \x01(\x05R\x10ttsAudioDuration\";\n" +
	"\x10UpdateVcTaskResp\x12'\n" +
	"\x04base\x18\x01 \x01(\v2\x13.common.SvcBaseRespR\x04base*f\n" +
	"\fVcTaskStatus\x12\x16\n" +
	"\x12STATUS_UNSPECIFIED\x10\x00\x12\x15\n" +
	"\x11STATUS_PROCESSING\x10\x01\x12\x14\n" +
	"\x10STATUS_COMPLETED\x10\x02\x12\x11\n" +
	"\rSTATUS_FAILED\x10\x032\xe1\x01\n" +
	"\x01s\x12?\n" +
	"\bAsyncTTS\x12\x17.vc.vcxxjob.AsyncTTSReq\x1a\x18.vc.vcxxjob.AsyncTTSResp\"\x00\x12N\n" +
	"\rAsyncBatchTTS\x12\x1c.vc.vcxxjob.AsyncBatchTTSReq\x1a\x1d.vc.vcxxjob.AsyncBatchTTSResp\"\x00\x12K\n" +
	"\fUpdateVcTask\x12\x1b.vc.vcxxjob.UpdateVcTaskReq\x1a\x1c.vc.vcxxjob.UpdateVcTaskResp\"\x00BCZAnew-gitlab.xunlei.cn/vcproject/backends/proto/api/vcxxjob;vcxxjobb\x06proto3"

var (
	file_vcxxjob_proto_rawDescOnce sync.Once
	file_vcxxjob_proto_rawDescData []byte
)

func file_vcxxjob_proto_rawDescGZIP() []byte {
	file_vcxxjob_proto_rawDescOnce.Do(func() {
		file_vcxxjob_proto_rawDescData = protoimpl.X.CompressGZIP(unsafe.Slice(unsafe.StringData(file_vcxxjob_proto_rawDesc), len(file_vcxxjob_proto_rawDesc)))
	})
	return file_vcxxjob_proto_rawDescData
}

var file_vcxxjob_proto_enumTypes = make([]protoimpl.EnumInfo, 1)
var file_vcxxjob_proto_msgTypes = make([]protoimpl.MessageInfo, 9)
var file_vcxxjob_proto_goTypes = []any{
	(VcTaskStatus)(0),                 // 0: vc.vcxxjob.VcTaskStatus
	(*AsyncTTSReq)(nil),               // 1: vc.vcxxjob.AsyncTTSReq
	(*AsyncTTSResp)(nil),              // 2: vc.vcxxjob.AsyncTTSResp
	(*AsyncTTSRespData)(nil),          // 3: vc.vcxxjob.AsyncTTSRespData
	(*AsyncBatchTTSReq)(nil),          // 4: vc.vcxxjob.AsyncBatchTTSReq
	(*AsyncBatchTTSResp)(nil),         // 5: vc.vcxxjob.AsyncBatchTTSResp
	(*AsyncBatchTTSRespData)(nil),     // 6: vc.vcxxjob.AsyncBatchTTSRespData
	(*AsyncBatchTTSRespDataItem)(nil), // 7: vc.vcxxjob.AsyncBatchTTSRespDataItem
	(*UpdateVcTaskReq)(nil),           // 8: vc.vcxxjob.UpdateVcTaskReq
	(*UpdateVcTaskResp)(nil),          // 9: vc.vcxxjob.UpdateVcTaskResp
	(*common.SvcBaseResp)(nil),        // 10: common.SvcBaseResp
}
var file_vcxxjob_proto_depIdxs = []int32{
	10, // 0: vc.vcxxjob.AsyncTTSResp.base:type_name -> common.SvcBaseResp
	3,  // 1: vc.vcxxjob.AsyncTTSResp.data:type_name -> vc.vcxxjob.AsyncTTSRespData
	1,  // 2: vc.vcxxjob.AsyncBatchTTSReq.list:type_name -> vc.vcxxjob.AsyncTTSReq
	10, // 3: vc.vcxxjob.AsyncBatchTTSResp.base:type_name -> common.SvcBaseResp
	6,  // 4: vc.vcxxjob.AsyncBatchTTSResp.data:type_name -> vc.vcxxjob.AsyncBatchTTSRespData
	7,  // 5: vc.vcxxjob.AsyncBatchTTSRespData.list:type_name -> vc.vcxxjob.AsyncBatchTTSRespDataItem
	10, // 6: vc.vcxxjob.UpdateVcTaskResp.base:type_name -> common.SvcBaseResp
	1,  // 7: vc.vcxxjob.s.AsyncTTS:input_type -> vc.vcxxjob.AsyncTTSReq
	4,  // 8: vc.vcxxjob.s.AsyncBatchTTS:input_type -> vc.vcxxjob.AsyncBatchTTSReq
	8,  // 9: vc.vcxxjob.s.UpdateVcTask:input_type -> vc.vcxxjob.UpdateVcTaskReq
	2,  // 10: vc.vcxxjob.s.AsyncTTS:output_type -> vc.vcxxjob.AsyncTTSResp
	5,  // 11: vc.vcxxjob.s.AsyncBatchTTS:output_type -> vc.vcxxjob.AsyncBatchTTSResp
	9,  // 12: vc.vcxxjob.s.UpdateVcTask:output_type -> vc.vcxxjob.UpdateVcTaskResp
	10, // [10:13] is the sub-list for method output_type
	7,  // [7:10] is the sub-list for method input_type
	7,  // [7:7] is the sub-list for extension type_name
	7,  // [7:7] is the sub-list for extension extendee
	0,  // [0:7] is the sub-list for field type_name
}

func init() { file_vcxxjob_proto_init() }
func file_vcxxjob_proto_init() {
	if File_vcxxjob_proto != nil {
		return
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: unsafe.Slice(unsafe.StringData(file_vcxxjob_proto_rawDesc), len(file_vcxxjob_proto_rawDesc)),
			NumEnums:      1,
			NumMessages:   9,
			NumExtensions: 0,
			NumServices:   1,
		},
		GoTypes:           file_vcxxjob_proto_goTypes,
		DependencyIndexes: file_vcxxjob_proto_depIdxs,
		EnumInfos:         file_vcxxjob_proto_enumTypes,
		MessageInfos:      file_vcxxjob_proto_msgTypes,
	}.Build()
	File_vcxxjob_proto = out.File
	file_vcxxjob_proto_goTypes = nil
	file_vcxxjob_proto_depIdxs = nil
}
