syntax = "proto3";

package vc.vcxxjob;
option go_package = "new-gitlab.xunlei.cn/vcproject/backends/proto/api/vcxxjob;vcxxjob";

import "protoc-gen-validate/validate/validate.proto";
import "common/common.proto";


enum VcTaskStatus {
    STATUS_UNSPECIFIED = 0;     // 未指定
    STATUS_PROCESSING = 1;         // 进行中
    STATUS_COMPLETED = 2;   // 完成
    STATUS_FAILED = 3;   // 失败
}

message AsyncTTSReq {
    string refer_audio_url=1;
    string refer_audio_text=2;
    string text=3[(validate.rules).string.min_len=1];
    string tts_engine=4[(validate.rules).string = {in:["llasa","fish_speech"]}];
    string out_id=5;
    int32 source=6;
    string ext=7;
    bool use_vc=8;  // 是否使用变声
    bool use_rvc=9;  // 是否使用rvc变声
}

message AsyncTTSResp {
    common.SvcBaseResp base=1;
    AsyncTTSRespData data=2;
}

message AsyncTTSRespData {
    string task_id=1;
}

message AsyncBatchTTSReq {
    repeated AsyncTTSReq list=1;
}

message AsyncBatchTTSResp {
    common.SvcBaseResp base=1;
    AsyncBatchTTSRespData data=2;
}

message AsyncBatchTTSRespData {
   repeated AsyncBatchTTSRespDataItem list=1;
}

message AsyncBatchTTSRespDataItem {
    string task_id=1;
    string out_id=2;
}

message UpdateVcTaskReq {
    string task_id=1;
    string status=2;
    string tts_audio_url=3;
    int32 tts_audio_duration=4;
}

message UpdateVcTaskResp {
    common.SvcBaseResp base=1;
}


service s {
    rpc AsyncTTS(AsyncTTSReq) returns (AsyncTTSResp) {}

    rpc AsyncBatchTTS(AsyncBatchTTSReq) returns (AsyncBatchTTSResp) {}

    rpc UpdateVcTask(UpdateVcTaskReq) returns (UpdateVcTaskResp) {}
}
