syntax = "proto3";

package common;
option go_package = "new-gitlab.xunlei.cn/vcproject/backends/proto/api/common;common";

import "google/protobuf/any.proto";
import "protoc-gen-validate/validate/validate.proto";

enum AuditResult {
    // 默认，不使用
    unknown = 0;
    // 通过
    pass = 1;
    // 拒绝
    reject = 2;
    // 审核中
    review = 3;
    // 伪发送 (命中了拒绝并且一级标签是广告)
    fake_send = 4;
}

// ASRStatus ASR识别状态
enum ASRStatus {
  ASR_STATUS_PROCESSING = 0;  // 识别中
  ASR_STATUS_SUCCESS = 1;     // 识别成功
  ASR_STATUS_FAILED = 2;      // 识别失败
  ASR_STATUS_TIMEOUT = 3;     // 识别超时
}


message TestReq{
    uint32 i = 1;
    string data = 2;
}
  
message TestResp{
    int32 code = 1;
    //错误码
    string msg= 2;
    google.protobuf.Any data = 3;
}

message BaseParam {
    //app 包名/域名
    string app = 1[(validate.rules).string={min_bytes:1, max_bytes:64}];
    //app_ver 版本号
    string av = 2[(validate.rules).string={min_bytes:1, max_bytes:32}];
    //dev_type 操作系统 1=android, 2=ios, 3=web
    int32 dt = 3[(validate.rules).int32={in:[1,2,3]}];
    //dev_id 设备标识ID，唯一
    string did = 4[(validate.rules).string={min_bytes:1, max_bytes:64}];
    //net_type 网络类型 1: Wi-Fi 2: 2G或3G 3: 4G 4: 其他
    int32 nt = 5;
    //channel 安装渠道
    string ch = 6;
    //model 机型
    string md = 7;
    //os 安卓系统版本/IOS系统版本/UA
    string os = 8;
    //timestamp 时间戳（毫秒）
    int64 ts = 9;
    //IP
    string ip = 10;
    // imei
    string imei = 11;
    // oaid
    string oaid = 12;
    //brand 品牌
    string bd = 13;
    //iOS广告标识符
    string idfa = 14;
    //vpn 1开始，0关闭
    string vpn = 15;
    //user agent
    string ua = 16;
    //signature 请求签名
    string signature = 18;
    //数美ID
    string smid = 19;
    //android ID
    string aid = 20;
  }

message SvcBaseResp {
    //错误码
    int32 code = 1;
    //错误信息
    string msg = 2;
}

message BaseResp {
    //错误码
    int32 code = 1;
    //错误信息
    string msg = 2;
}

message SvcCommonResp {
    SvcBaseResp base = 1;
}

message BizBaseResp {
    //错误码
    int32 code = 1;
    //错误信息
    string msg = 2;
    google.protobuf.Any data=3;
}

message ErrorDetail {
    int32 code = 1;
    string message = 2;
}

message SvcUseridReq {
    int64 userid = 1;
    string last_index = 2;
}


message SvcUseridsReq {
    repeated int64 userids = 1;
}

enum AuditSubResult {
  // 默认，不使用
  audit_sub_unknown = 0;
  // 联系方式拦截 男用户亲密的等级过低
  audit_sub_male_lianxifangshi = 1;
}

// shield 结果
enum ShieldResult {
  shield_unknown = 0; // 默认，不使用
  shield_pass = 1; //通过
  shield_reject=2; //拒绝
  shield_review=3; //
}