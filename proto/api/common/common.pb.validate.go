// Code generated by protoc-gen-validate. DO NOT EDIT.
// source: common.proto

package common

import (
	"bytes"
	"errors"
	"fmt"
	"net"
	"net/mail"
	"net/url"
	"regexp"
	"sort"
	"strings"
	"time"
	"unicode/utf8"

	"google.golang.org/protobuf/types/known/anypb"
)

// ensure the imports are used
var (
	_ = bytes.MinRead
	_ = errors.New("")
	_ = fmt.Print
	_ = utf8.UTFMax
	_ = (*regexp.Regexp)(nil)
	_ = (*strings.Reader)(nil)
	_ = net.IPv4len
	_ = time.Duration(0)
	_ = (*url.URL)(nil)
	_ = (*mail.Address)(nil)
	_ = anypb.Any{}
	_ = sort.Sort
)

// Validate checks the field values on TestReq with the rules defined in the
// proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *TestReq) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on TestReq with the rules defined in the
// proto definition for this message. If any rules are violated, the result is
// a list of violation errors wrapped in TestReqMultiError, or nil if none found.
func (m *TestReq) ValidateAll() error {
	return m.validate(true)
}

func (m *TestReq) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for I

	// no validation rules for Data

	if len(errors) > 0 {
		return TestReqMultiError(errors)
	}

	return nil
}

// TestReqMultiError is an error wrapping multiple validation errors returned
// by TestReq.ValidateAll() if the designated constraints aren't met.
type TestReqMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m TestReqMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m TestReqMultiError) AllErrors() []error { return m }

// TestReqValidationError is the validation error returned by TestReq.Validate
// if the designated constraints aren't met.
type TestReqValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e TestReqValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e TestReqValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e TestReqValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e TestReqValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e TestReqValidationError) ErrorName() string { return "TestReqValidationError" }

// Error satisfies the builtin error interface
func (e TestReqValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sTestReq.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = TestReqValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = TestReqValidationError{}

// Validate checks the field values on TestResp with the rules defined in the
// proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *TestResp) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on TestResp with the rules defined in
// the proto definition for this message. If any rules are violated, the
// result is a list of violation errors wrapped in TestRespMultiError, or nil
// if none found.
func (m *TestResp) ValidateAll() error {
	return m.validate(true)
}

func (m *TestResp) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Code

	// no validation rules for Msg

	if all {
		switch v := interface{}(m.GetData()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, TestRespValidationError{
					field:  "Data",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, TestRespValidationError{
					field:  "Data",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetData()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return TestRespValidationError{
				field:  "Data",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return TestRespMultiError(errors)
	}

	return nil
}

// TestRespMultiError is an error wrapping multiple validation errors returned
// by TestResp.ValidateAll() if the designated constraints aren't met.
type TestRespMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m TestRespMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m TestRespMultiError) AllErrors() []error { return m }

// TestRespValidationError is the validation error returned by
// TestResp.Validate if the designated constraints aren't met.
type TestRespValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e TestRespValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e TestRespValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e TestRespValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e TestRespValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e TestRespValidationError) ErrorName() string { return "TestRespValidationError" }

// Error satisfies the builtin error interface
func (e TestRespValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sTestResp.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = TestRespValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = TestRespValidationError{}

// Validate checks the field values on BaseParam with the rules defined in the
// proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *BaseParam) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on BaseParam with the rules defined in
// the proto definition for this message. If any rules are violated, the
// result is a list of violation errors wrapped in BaseParamMultiError, or nil
// if none found.
func (m *BaseParam) ValidateAll() error {
	return m.validate(true)
}

func (m *BaseParam) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if l := len(m.GetApp()); l < 1 || l > 64 {
		err := BaseParamValidationError{
			field:  "App",
			reason: "value length must be between 1 and 64 bytes, inclusive",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if l := len(m.GetAv()); l < 1 || l > 32 {
		err := BaseParamValidationError{
			field:  "Av",
			reason: "value length must be between 1 and 32 bytes, inclusive",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if _, ok := _BaseParam_Dt_InLookup[m.GetDt()]; !ok {
		err := BaseParamValidationError{
			field:  "Dt",
			reason: "value must be in list [1 2 3]",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if l := len(m.GetDid()); l < 1 || l > 64 {
		err := BaseParamValidationError{
			field:  "Did",
			reason: "value length must be between 1 and 64 bytes, inclusive",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	// no validation rules for Nt

	// no validation rules for Ch

	// no validation rules for Md

	// no validation rules for Os

	// no validation rules for Ts

	// no validation rules for Ip

	// no validation rules for Imei

	// no validation rules for Oaid

	// no validation rules for Bd

	// no validation rules for Idfa

	// no validation rules for Vpn

	// no validation rules for Ua

	// no validation rules for Signature

	// no validation rules for Smid

	// no validation rules for Aid

	if len(errors) > 0 {
		return BaseParamMultiError(errors)
	}

	return nil
}

// BaseParamMultiError is an error wrapping multiple validation errors returned
// by BaseParam.ValidateAll() if the designated constraints aren't met.
type BaseParamMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m BaseParamMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m BaseParamMultiError) AllErrors() []error { return m }

// BaseParamValidationError is the validation error returned by
// BaseParam.Validate if the designated constraints aren't met.
type BaseParamValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e BaseParamValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e BaseParamValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e BaseParamValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e BaseParamValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e BaseParamValidationError) ErrorName() string { return "BaseParamValidationError" }

// Error satisfies the builtin error interface
func (e BaseParamValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sBaseParam.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = BaseParamValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = BaseParamValidationError{}

var _BaseParam_Dt_InLookup = map[int32]struct{}{
	1: {},
	2: {},
	3: {},
}

// Validate checks the field values on SvcBaseResp with the rules defined in
// the proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *SvcBaseResp) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on SvcBaseResp with the rules defined in
// the proto definition for this message. If any rules are violated, the
// result is a list of violation errors wrapped in SvcBaseRespMultiError, or
// nil if none found.
func (m *SvcBaseResp) ValidateAll() error {
	return m.validate(true)
}

func (m *SvcBaseResp) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Code

	// no validation rules for Msg

	if len(errors) > 0 {
		return SvcBaseRespMultiError(errors)
	}

	return nil
}

// SvcBaseRespMultiError is an error wrapping multiple validation errors
// returned by SvcBaseResp.ValidateAll() if the designated constraints aren't met.
type SvcBaseRespMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m SvcBaseRespMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m SvcBaseRespMultiError) AllErrors() []error { return m }

// SvcBaseRespValidationError is the validation error returned by
// SvcBaseResp.Validate if the designated constraints aren't met.
type SvcBaseRespValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e SvcBaseRespValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e SvcBaseRespValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e SvcBaseRespValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e SvcBaseRespValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e SvcBaseRespValidationError) ErrorName() string { return "SvcBaseRespValidationError" }

// Error satisfies the builtin error interface
func (e SvcBaseRespValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sSvcBaseResp.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = SvcBaseRespValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = SvcBaseRespValidationError{}

// Validate checks the field values on BaseResp with the rules defined in the
// proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *BaseResp) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on BaseResp with the rules defined in
// the proto definition for this message. If any rules are violated, the
// result is a list of violation errors wrapped in BaseRespMultiError, or nil
// if none found.
func (m *BaseResp) ValidateAll() error {
	return m.validate(true)
}

func (m *BaseResp) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Code

	// no validation rules for Msg

	if len(errors) > 0 {
		return BaseRespMultiError(errors)
	}

	return nil
}

// BaseRespMultiError is an error wrapping multiple validation errors returned
// by BaseResp.ValidateAll() if the designated constraints aren't met.
type BaseRespMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m BaseRespMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m BaseRespMultiError) AllErrors() []error { return m }

// BaseRespValidationError is the validation error returned by
// BaseResp.Validate if the designated constraints aren't met.
type BaseRespValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e BaseRespValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e BaseRespValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e BaseRespValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e BaseRespValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e BaseRespValidationError) ErrorName() string { return "BaseRespValidationError" }

// Error satisfies the builtin error interface
func (e BaseRespValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sBaseResp.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = BaseRespValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = BaseRespValidationError{}

// Validate checks the field values on SvcCommonResp with the rules defined in
// the proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *SvcCommonResp) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on SvcCommonResp with the rules defined
// in the proto definition for this message. If any rules are violated, the
// result is a list of violation errors wrapped in SvcCommonRespMultiError, or
// nil if none found.
func (m *SvcCommonResp) ValidateAll() error {
	return m.validate(true)
}

func (m *SvcCommonResp) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetBase()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, SvcCommonRespValidationError{
					field:  "Base",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, SvcCommonRespValidationError{
					field:  "Base",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetBase()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return SvcCommonRespValidationError{
				field:  "Base",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return SvcCommonRespMultiError(errors)
	}

	return nil
}

// SvcCommonRespMultiError is an error wrapping multiple validation errors
// returned by SvcCommonResp.ValidateAll() if the designated constraints
// aren't met.
type SvcCommonRespMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m SvcCommonRespMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m SvcCommonRespMultiError) AllErrors() []error { return m }

// SvcCommonRespValidationError is the validation error returned by
// SvcCommonResp.Validate if the designated constraints aren't met.
type SvcCommonRespValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e SvcCommonRespValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e SvcCommonRespValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e SvcCommonRespValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e SvcCommonRespValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e SvcCommonRespValidationError) ErrorName() string { return "SvcCommonRespValidationError" }

// Error satisfies the builtin error interface
func (e SvcCommonRespValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sSvcCommonResp.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = SvcCommonRespValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = SvcCommonRespValidationError{}

// Validate checks the field values on BizBaseResp with the rules defined in
// the proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *BizBaseResp) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on BizBaseResp with the rules defined in
// the proto definition for this message. If any rules are violated, the
// result is a list of violation errors wrapped in BizBaseRespMultiError, or
// nil if none found.
func (m *BizBaseResp) ValidateAll() error {
	return m.validate(true)
}

func (m *BizBaseResp) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Code

	// no validation rules for Msg

	if all {
		switch v := interface{}(m.GetData()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, BizBaseRespValidationError{
					field:  "Data",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, BizBaseRespValidationError{
					field:  "Data",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetData()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return BizBaseRespValidationError{
				field:  "Data",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return BizBaseRespMultiError(errors)
	}

	return nil
}

// BizBaseRespMultiError is an error wrapping multiple validation errors
// returned by BizBaseResp.ValidateAll() if the designated constraints aren't met.
type BizBaseRespMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m BizBaseRespMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m BizBaseRespMultiError) AllErrors() []error { return m }

// BizBaseRespValidationError is the validation error returned by
// BizBaseResp.Validate if the designated constraints aren't met.
type BizBaseRespValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e BizBaseRespValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e BizBaseRespValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e BizBaseRespValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e BizBaseRespValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e BizBaseRespValidationError) ErrorName() string { return "BizBaseRespValidationError" }

// Error satisfies the builtin error interface
func (e BizBaseRespValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sBizBaseResp.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = BizBaseRespValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = BizBaseRespValidationError{}

// Validate checks the field values on ErrorDetail with the rules defined in
// the proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *ErrorDetail) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on ErrorDetail with the rules defined in
// the proto definition for this message. If any rules are violated, the
// result is a list of violation errors wrapped in ErrorDetailMultiError, or
// nil if none found.
func (m *ErrorDetail) ValidateAll() error {
	return m.validate(true)
}

func (m *ErrorDetail) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Code

	// no validation rules for Message

	if len(errors) > 0 {
		return ErrorDetailMultiError(errors)
	}

	return nil
}

// ErrorDetailMultiError is an error wrapping multiple validation errors
// returned by ErrorDetail.ValidateAll() if the designated constraints aren't met.
type ErrorDetailMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m ErrorDetailMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m ErrorDetailMultiError) AllErrors() []error { return m }

// ErrorDetailValidationError is the validation error returned by
// ErrorDetail.Validate if the designated constraints aren't met.
type ErrorDetailValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e ErrorDetailValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e ErrorDetailValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e ErrorDetailValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e ErrorDetailValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e ErrorDetailValidationError) ErrorName() string { return "ErrorDetailValidationError" }

// Error satisfies the builtin error interface
func (e ErrorDetailValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sErrorDetail.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = ErrorDetailValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = ErrorDetailValidationError{}

// Validate checks the field values on SvcUseridReq with the rules defined in
// the proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *SvcUseridReq) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on SvcUseridReq with the rules defined
// in the proto definition for this message. If any rules are violated, the
// result is a list of violation errors wrapped in SvcUseridReqMultiError, or
// nil if none found.
func (m *SvcUseridReq) ValidateAll() error {
	return m.validate(true)
}

func (m *SvcUseridReq) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Userid

	// no validation rules for LastIndex

	if len(errors) > 0 {
		return SvcUseridReqMultiError(errors)
	}

	return nil
}

// SvcUseridReqMultiError is an error wrapping multiple validation errors
// returned by SvcUseridReq.ValidateAll() if the designated constraints aren't met.
type SvcUseridReqMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m SvcUseridReqMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m SvcUseridReqMultiError) AllErrors() []error { return m }

// SvcUseridReqValidationError is the validation error returned by
// SvcUseridReq.Validate if the designated constraints aren't met.
type SvcUseridReqValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e SvcUseridReqValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e SvcUseridReqValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e SvcUseridReqValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e SvcUseridReqValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e SvcUseridReqValidationError) ErrorName() string { return "SvcUseridReqValidationError" }

// Error satisfies the builtin error interface
func (e SvcUseridReqValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sSvcUseridReq.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = SvcUseridReqValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = SvcUseridReqValidationError{}

// Validate checks the field values on SvcUseridsReq with the rules defined in
// the proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *SvcUseridsReq) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on SvcUseridsReq with the rules defined
// in the proto definition for this message. If any rules are violated, the
// result is a list of violation errors wrapped in SvcUseridsReqMultiError, or
// nil if none found.
func (m *SvcUseridsReq) ValidateAll() error {
	return m.validate(true)
}

func (m *SvcUseridsReq) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if len(errors) > 0 {
		return SvcUseridsReqMultiError(errors)
	}

	return nil
}

// SvcUseridsReqMultiError is an error wrapping multiple validation errors
// returned by SvcUseridsReq.ValidateAll() if the designated constraints
// aren't met.
type SvcUseridsReqMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m SvcUseridsReqMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m SvcUseridsReqMultiError) AllErrors() []error { return m }

// SvcUseridsReqValidationError is the validation error returned by
// SvcUseridsReq.Validate if the designated constraints aren't met.
type SvcUseridsReqValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e SvcUseridsReqValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e SvcUseridsReqValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e SvcUseridsReqValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e SvcUseridsReqValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e SvcUseridsReqValidationError) ErrorName() string { return "SvcUseridsReqValidationError" }

// Error satisfies the builtin error interface
func (e SvcUseridsReqValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sSvcUseridsReq.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = SvcUseridsReqValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = SvcUseridsReqValidationError{}
