syntax = "proto3";

package vc.basemsgtransfer;
option go_package = "new-gitlab.xunlei.cn/vcproject/backends/proto/api/basemsgtransfer;basemsgtransfer";

import "protoc-gen-validate/validate/validate.proto";
import "google/api/field_behavior.proto";
import "common/common.proto";

enum SessionType {
    SessionTypeUnknown = 0;
    SessionTypeChat = 1;
    SessionTypeGroup = 2;
    SessionTypeVoiceRoom = 3;
    SessionTypeSystem = 4;
}

// ContentType 前三位为业务大类型,三位之后为业务细分的子类型，可以按需拓展
enum ContentType {
    Invalid = 0;  // 不使用
    Text = 101;   // 文本
    Image = 102;  // 图片
    Voice = 103;  // 语音
    Video = 104;  // 视频
    Gift = 105;   // 礼物
    Custom = 110; // 保留字段

    // 150 外显富文本
    RichTextNormal = 150;
    //关注信息
    Follow = 151;
    //评论
    Comment = 152;
    CommentScript = 15201; //评论剧本
    CommentReply = 15202;  //回复评论
    //点赞
    Like = 153;
    LikeScript = 15301;  //点赞剧本
    LikeComment= 15302; //点赞评论
    LikeDubbing= 15303; // 配音点赞
    // 作品配音
    Dubbing = 154;
    // 审核剧本
    Review = 155;
    ReviewScript = 15501; // 审核剧本
}

enum MsgFromEnum {
    // 普通消息,需要Ack
    MsgFromIm = 0;
    // 实时消息，不需要客户端ack
    MsgFromRealTime = 1;
    // 普通消息,需要Ack,用来标记客户端不入库
    MsgFromIm2 = 2;
  }


message OfflinePushInfo {
    string title = 1;
    string desc = 2;
    string ext = 3;
    string IOSPushSound = 4;
    bool IOSBadgeCount = 5;
    string signalInfo = 6;
}

message AtInfo {
    int64 atUserId = 1;
    string text = 2;
    string style = 3;
}

// 发送和接收的消息结构体，type=2或type=4，对data进行json_decode后消息结构体
message MsgData {
    // 客户端消息的唯一ID
    string localId = 1 [ (google.api.field_behavior) = REQUIRED ];
    // 服务端生成消息的唯一ID
    string msgId = 2;
    // 会话ID，单聊：s_xxx,群聊:g_xxx,超级群sg_xxx,系统通知:n_xxx
    string sessionId = 3 [ (google.api.field_behavior) = REQUIRED ];
    int64 from = 4;
    int64 to = 5 [ (google.api.field_behavior) = REQUIRED ];
    // 群聊ID
    int64 groupId = 6;
    // 0:IM消息;1:实时消息
    uint32 msgFrom = 7;
    // 1 单聊，2 群聊，3 语音房， 4 系统通知 5: 朋友圈
    int32 sessionType = 8 [ (google.api.field_behavior) = REQUIRED ];
    // 消息内容类型：Text:101,Image:102,Voice:103,Video:104,Custom:110,RealtimeVoice:115,RealTimeVideo:116
    int32 contentType = 9 [ (google.api.field_behavior) = REQUIRED ];
    // 消息内容，一个json根据contentType解码到不同的结构体，见下面
    string content = 10 [ (google.api.field_behavior) = REQUIRED ];
    // 消息序号
    int64 seq = 16;
    // 发送时间
    int64 sendTime = 17;
    // 消息创建时间
    int64 createTime = 18 [ (google.api.field_behavior) = REQUIRED ];
    // 一个map,不配置默认为true。
    // key如下 history:是否推送历史消息
    // persistent：是否永久存储消息
    // offlinePush：离线是否走第三方推送
    map<string, bool> options = 19;
    // 第三方推送配置
    OfflinePushInfo offlinePushInfo = 20;
    // @用户列表
    // repeated int64 atUserIdList = 21;
    repeated AtInfo atList = 21;
    bool upgrade = 22; // 如果是 true，客户端不支持，则提示升级。 否则不显示
    map<string, string> exts = 23; // 扩展字段
    bool SysMsg = 24; //是否系统消息
    int64 receiver = 35 [(google.api.field_behavior) = REQUIRED] ; //消息接受方
  }

message SendMsgReq { 
    repeated MsgData msgs = 1[(validate.rules).repeated = {min_items: 1},(google.api.field_behavior) = REQUIRED];
}

message SendMsgResp {
    common.SvcBaseResp base = 1; 
    repeated string msgids=2;
}

message MulticastRealTimeMsgReq {
    repeated MsgData msgs = 1;
    string topic = 2;
}

service s {
    rpc SendMsg(SendMsgReq) returns (SendMsgResp) {}

    rpc SendRealTimeMsg(SendMsgReq) returns (SendMsgResp) {}
    
    rpc MulticastRealTimeMsg(MulticastRealTimeMsgReq) returns (SendMsgResp) {}
}