// Code generated by protoc-gen-validate. DO NOT EDIT.
// source: basemsgtransfer.proto

package basemsgtransfer

import (
	"bytes"
	"errors"
	"fmt"
	"net"
	"net/mail"
	"net/url"
	"regexp"
	"sort"
	"strings"
	"time"
	"unicode/utf8"

	"google.golang.org/protobuf/types/known/anypb"
)

// ensure the imports are used
var (
	_ = bytes.MinRead
	_ = errors.New("")
	_ = fmt.Print
	_ = utf8.UTFMax
	_ = (*regexp.Regexp)(nil)
	_ = (*strings.Reader)(nil)
	_ = net.IPv4len
	_ = time.Duration(0)
	_ = (*url.URL)(nil)
	_ = (*mail.Address)(nil)
	_ = anypb.Any{}
	_ = sort.Sort
)

// Validate checks the field values on OfflinePushInfo with the rules defined
// in the proto definition for this message. If any rules are violated, the
// first error encountered is returned, or nil if there are no violations.
func (m *OfflinePushInfo) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on OfflinePushInfo with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// OfflinePushInfoMultiError, or nil if none found.
func (m *OfflinePushInfo) ValidateAll() error {
	return m.validate(true)
}

func (m *OfflinePushInfo) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Title

	// no validation rules for Desc

	// no validation rules for Ext

	// no validation rules for IOSPushSound

	// no validation rules for IOSBadgeCount

	// no validation rules for SignalInfo

	if len(errors) > 0 {
		return OfflinePushInfoMultiError(errors)
	}

	return nil
}

// OfflinePushInfoMultiError is an error wrapping multiple validation errors
// returned by OfflinePushInfo.ValidateAll() if the designated constraints
// aren't met.
type OfflinePushInfoMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m OfflinePushInfoMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m OfflinePushInfoMultiError) AllErrors() []error { return m }

// OfflinePushInfoValidationError is the validation error returned by
// OfflinePushInfo.Validate if the designated constraints aren't met.
type OfflinePushInfoValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e OfflinePushInfoValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e OfflinePushInfoValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e OfflinePushInfoValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e OfflinePushInfoValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e OfflinePushInfoValidationError) ErrorName() string { return "OfflinePushInfoValidationError" }

// Error satisfies the builtin error interface
func (e OfflinePushInfoValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sOfflinePushInfo.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = OfflinePushInfoValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = OfflinePushInfoValidationError{}

// Validate checks the field values on AtInfo with the rules defined in the
// proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *AtInfo) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on AtInfo with the rules defined in the
// proto definition for this message. If any rules are violated, the result is
// a list of violation errors wrapped in AtInfoMultiError, or nil if none found.
func (m *AtInfo) ValidateAll() error {
	return m.validate(true)
}

func (m *AtInfo) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for AtUserId

	// no validation rules for Text

	// no validation rules for Style

	if len(errors) > 0 {
		return AtInfoMultiError(errors)
	}

	return nil
}

// AtInfoMultiError is an error wrapping multiple validation errors returned by
// AtInfo.ValidateAll() if the designated constraints aren't met.
type AtInfoMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m AtInfoMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m AtInfoMultiError) AllErrors() []error { return m }

// AtInfoValidationError is the validation error returned by AtInfo.Validate if
// the designated constraints aren't met.
type AtInfoValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e AtInfoValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e AtInfoValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e AtInfoValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e AtInfoValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e AtInfoValidationError) ErrorName() string { return "AtInfoValidationError" }

// Error satisfies the builtin error interface
func (e AtInfoValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sAtInfo.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = AtInfoValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = AtInfoValidationError{}

// Validate checks the field values on MsgData with the rules defined in the
// proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *MsgData) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on MsgData with the rules defined in the
// proto definition for this message. If any rules are violated, the result is
// a list of violation errors wrapped in MsgDataMultiError, or nil if none found.
func (m *MsgData) ValidateAll() error {
	return m.validate(true)
}

func (m *MsgData) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for LocalId

	// no validation rules for MsgId

	// no validation rules for SessionId

	// no validation rules for From

	// no validation rules for To

	// no validation rules for GroupId

	// no validation rules for MsgFrom

	// no validation rules for SessionType

	// no validation rules for ContentType

	// no validation rules for Content

	// no validation rules for Seq

	// no validation rules for SendTime

	// no validation rules for CreateTime

	// no validation rules for Options

	if all {
		switch v := interface{}(m.GetOfflinePushInfo()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, MsgDataValidationError{
					field:  "OfflinePushInfo",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, MsgDataValidationError{
					field:  "OfflinePushInfo",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetOfflinePushInfo()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return MsgDataValidationError{
				field:  "OfflinePushInfo",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	for idx, item := range m.GetAtList() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, MsgDataValidationError{
						field:  fmt.Sprintf("AtList[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, MsgDataValidationError{
						field:  fmt.Sprintf("AtList[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return MsgDataValidationError{
					field:  fmt.Sprintf("AtList[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	// no validation rules for Upgrade

	// no validation rules for Exts

	// no validation rules for SysMsg

	// no validation rules for Receiver

	if len(errors) > 0 {
		return MsgDataMultiError(errors)
	}

	return nil
}

// MsgDataMultiError is an error wrapping multiple validation errors returned
// by MsgData.ValidateAll() if the designated constraints aren't met.
type MsgDataMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m MsgDataMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m MsgDataMultiError) AllErrors() []error { return m }

// MsgDataValidationError is the validation error returned by MsgData.Validate
// if the designated constraints aren't met.
type MsgDataValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e MsgDataValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e MsgDataValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e MsgDataValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e MsgDataValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e MsgDataValidationError) ErrorName() string { return "MsgDataValidationError" }

// Error satisfies the builtin error interface
func (e MsgDataValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sMsgData.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = MsgDataValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = MsgDataValidationError{}

// Validate checks the field values on SendMsgReq with the rules defined in the
// proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *SendMsgReq) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on SendMsgReq with the rules defined in
// the proto definition for this message. If any rules are violated, the
// result is a list of violation errors wrapped in SendMsgReqMultiError, or
// nil if none found.
func (m *SendMsgReq) ValidateAll() error {
	return m.validate(true)
}

func (m *SendMsgReq) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if len(m.GetMsgs()) < 1 {
		err := SendMsgReqValidationError{
			field:  "Msgs",
			reason: "value must contain at least 1 item(s)",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	for idx, item := range m.GetMsgs() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, SendMsgReqValidationError{
						field:  fmt.Sprintf("Msgs[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, SendMsgReqValidationError{
						field:  fmt.Sprintf("Msgs[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return SendMsgReqValidationError{
					field:  fmt.Sprintf("Msgs[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	if len(errors) > 0 {
		return SendMsgReqMultiError(errors)
	}

	return nil
}

// SendMsgReqMultiError is an error wrapping multiple validation errors
// returned by SendMsgReq.ValidateAll() if the designated constraints aren't met.
type SendMsgReqMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m SendMsgReqMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m SendMsgReqMultiError) AllErrors() []error { return m }

// SendMsgReqValidationError is the validation error returned by
// SendMsgReq.Validate if the designated constraints aren't met.
type SendMsgReqValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e SendMsgReqValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e SendMsgReqValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e SendMsgReqValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e SendMsgReqValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e SendMsgReqValidationError) ErrorName() string { return "SendMsgReqValidationError" }

// Error satisfies the builtin error interface
func (e SendMsgReqValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sSendMsgReq.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = SendMsgReqValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = SendMsgReqValidationError{}

// Validate checks the field values on SendMsgResp with the rules defined in
// the proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *SendMsgResp) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on SendMsgResp with the rules defined in
// the proto definition for this message. If any rules are violated, the
// result is a list of violation errors wrapped in SendMsgRespMultiError, or
// nil if none found.
func (m *SendMsgResp) ValidateAll() error {
	return m.validate(true)
}

func (m *SendMsgResp) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetBase()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, SendMsgRespValidationError{
					field:  "Base",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, SendMsgRespValidationError{
					field:  "Base",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetBase()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return SendMsgRespValidationError{
				field:  "Base",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return SendMsgRespMultiError(errors)
	}

	return nil
}

// SendMsgRespMultiError is an error wrapping multiple validation errors
// returned by SendMsgResp.ValidateAll() if the designated constraints aren't met.
type SendMsgRespMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m SendMsgRespMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m SendMsgRespMultiError) AllErrors() []error { return m }

// SendMsgRespValidationError is the validation error returned by
// SendMsgResp.Validate if the designated constraints aren't met.
type SendMsgRespValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e SendMsgRespValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e SendMsgRespValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e SendMsgRespValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e SendMsgRespValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e SendMsgRespValidationError) ErrorName() string { return "SendMsgRespValidationError" }

// Error satisfies the builtin error interface
func (e SendMsgRespValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sSendMsgResp.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = SendMsgRespValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = SendMsgRespValidationError{}

// Validate checks the field values on MulticastRealTimeMsgReq with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *MulticastRealTimeMsgReq) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on MulticastRealTimeMsgReq with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// MulticastRealTimeMsgReqMultiError, or nil if none found.
func (m *MulticastRealTimeMsgReq) ValidateAll() error {
	return m.validate(true)
}

func (m *MulticastRealTimeMsgReq) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	for idx, item := range m.GetMsgs() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, MulticastRealTimeMsgReqValidationError{
						field:  fmt.Sprintf("Msgs[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, MulticastRealTimeMsgReqValidationError{
						field:  fmt.Sprintf("Msgs[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return MulticastRealTimeMsgReqValidationError{
					field:  fmt.Sprintf("Msgs[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	// no validation rules for Topic

	if len(errors) > 0 {
		return MulticastRealTimeMsgReqMultiError(errors)
	}

	return nil
}

// MulticastRealTimeMsgReqMultiError is an error wrapping multiple validation
// errors returned by MulticastRealTimeMsgReq.ValidateAll() if the designated
// constraints aren't met.
type MulticastRealTimeMsgReqMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m MulticastRealTimeMsgReqMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m MulticastRealTimeMsgReqMultiError) AllErrors() []error { return m }

// MulticastRealTimeMsgReqValidationError is the validation error returned by
// MulticastRealTimeMsgReq.Validate if the designated constraints aren't met.
type MulticastRealTimeMsgReqValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e MulticastRealTimeMsgReqValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e MulticastRealTimeMsgReqValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e MulticastRealTimeMsgReqValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e MulticastRealTimeMsgReqValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e MulticastRealTimeMsgReqValidationError) ErrorName() string {
	return "MulticastRealTimeMsgReqValidationError"
}

// Error satisfies the builtin error interface
func (e MulticastRealTimeMsgReqValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sMulticastRealTimeMsgReq.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = MulticastRealTimeMsgReqValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = MulticastRealTimeMsgReqValidationError{}
