// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.33.0
// 	protoc        v5.29.3
// source: basemsgtransfer.proto

package basemsgtransfer

import (
	_ "github.com/envoyproxy/protoc-gen-validate/validate"
	_ "google.golang.org/genproto/googleapis/api/annotations"
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	common "new-gitlab.xunlei.cn/vcproject/backends/proto/api/common"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type SessionType int32

const (
	SessionType_SessionTypeUnknown   SessionType = 0
	SessionType_SessionTypeChat      SessionType = 1
	SessionType_SessionTypeGroup     SessionType = 2
	SessionType_SessionTypeVoiceRoom SessionType = 3
	SessionType_SessionTypeSystem    SessionType = 4
)

// Enum value maps for SessionType.
var (
	SessionType_name = map[int32]string{
		0: "SessionTypeUnknown",
		1: "SessionTypeChat",
		2: "SessionTypeGroup",
		3: "SessionTypeVoiceRoom",
		4: "SessionTypeSystem",
	}
	SessionType_value = map[string]int32{
		"SessionTypeUnknown":   0,
		"SessionTypeChat":      1,
		"SessionTypeGroup":     2,
		"SessionTypeVoiceRoom": 3,
		"SessionTypeSystem":    4,
	}
)

func (x SessionType) Enum() *SessionType {
	p := new(SessionType)
	*p = x
	return p
}

func (x SessionType) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (SessionType) Descriptor() protoreflect.EnumDescriptor {
	return file_basemsgtransfer_proto_enumTypes[0].Descriptor()
}

func (SessionType) Type() protoreflect.EnumType {
	return &file_basemsgtransfer_proto_enumTypes[0]
}

func (x SessionType) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use SessionType.Descriptor instead.
func (SessionType) EnumDescriptor() ([]byte, []int) {
	return file_basemsgtransfer_proto_rawDescGZIP(), []int{0}
}

// ContentType 前三位为业务大类型,三位之后为业务细分的子类型，可以按需拓展
type ContentType int32

const (
	ContentType_Invalid ContentType = 0   // 不使用
	ContentType_Text    ContentType = 101 // 文本
	ContentType_Image   ContentType = 102 // 图片
	ContentType_Voice   ContentType = 103 // 语音
	ContentType_Video   ContentType = 104 // 视频
	ContentType_Gift    ContentType = 105 // 礼物
	ContentType_Custom  ContentType = 110 // 保留字段
	// 150 外显富文本
	ContentType_RichTextNormal ContentType = 150
	// 关注信息
	ContentType_Follow ContentType = 151
	// 评论
	ContentType_Comment       ContentType = 152
	ContentType_CommentScript ContentType = 15201 //评论剧本
	ContentType_CommentReply  ContentType = 15202 //回复评论
	// 点赞
	ContentType_Like        ContentType = 153
	ContentType_LikeScript  ContentType = 15301 //点赞剧本
	ContentType_LikeComment ContentType = 15302 //点赞评论
	ContentType_LikeDubbing ContentType = 15303 // 配音点赞
	// 作品配音
	ContentType_Dubbing ContentType = 154
	// 审核剧本
	ContentType_Review       ContentType = 155
	ContentType_ReviewScript ContentType = 15501 // 审核剧本
)

// Enum value maps for ContentType.
var (
	ContentType_name = map[int32]string{
		0:     "Invalid",
		101:   "Text",
		102:   "Image",
		103:   "Voice",
		104:   "Video",
		105:   "Gift",
		110:   "Custom",
		150:   "RichTextNormal",
		151:   "Follow",
		152:   "Comment",
		15201: "CommentScript",
		15202: "CommentReply",
		153:   "Like",
		15301: "LikeScript",
		15302: "LikeComment",
		15303: "LikeDubbing",
		154:   "Dubbing",
		155:   "Review",
		15501: "ReviewScript",
	}
	ContentType_value = map[string]int32{
		"Invalid":        0,
		"Text":           101,
		"Image":          102,
		"Voice":          103,
		"Video":          104,
		"Gift":           105,
		"Custom":         110,
		"RichTextNormal": 150,
		"Follow":         151,
		"Comment":        152,
		"CommentScript":  15201,
		"CommentReply":   15202,
		"Like":           153,
		"LikeScript":     15301,
		"LikeComment":    15302,
		"LikeDubbing":    15303,
		"Dubbing":        154,
		"Review":         155,
		"ReviewScript":   15501,
	}
)

func (x ContentType) Enum() *ContentType {
	p := new(ContentType)
	*p = x
	return p
}

func (x ContentType) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (ContentType) Descriptor() protoreflect.EnumDescriptor {
	return file_basemsgtransfer_proto_enumTypes[1].Descriptor()
}

func (ContentType) Type() protoreflect.EnumType {
	return &file_basemsgtransfer_proto_enumTypes[1]
}

func (x ContentType) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use ContentType.Descriptor instead.
func (ContentType) EnumDescriptor() ([]byte, []int) {
	return file_basemsgtransfer_proto_rawDescGZIP(), []int{1}
}

type MsgFromEnum int32

const (
	// 普通消息,需要Ack
	MsgFromEnum_MsgFromIm MsgFromEnum = 0
	// 实时消息，不需要客户端ack
	MsgFromEnum_MsgFromRealTime MsgFromEnum = 1
	// 普通消息,需要Ack,用来标记客户端不入库
	MsgFromEnum_MsgFromIm2 MsgFromEnum = 2
)

// Enum value maps for MsgFromEnum.
var (
	MsgFromEnum_name = map[int32]string{
		0: "MsgFromIm",
		1: "MsgFromRealTime",
		2: "MsgFromIm2",
	}
	MsgFromEnum_value = map[string]int32{
		"MsgFromIm":       0,
		"MsgFromRealTime": 1,
		"MsgFromIm2":      2,
	}
)

func (x MsgFromEnum) Enum() *MsgFromEnum {
	p := new(MsgFromEnum)
	*p = x
	return p
}

func (x MsgFromEnum) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (MsgFromEnum) Descriptor() protoreflect.EnumDescriptor {
	return file_basemsgtransfer_proto_enumTypes[2].Descriptor()
}

func (MsgFromEnum) Type() protoreflect.EnumType {
	return &file_basemsgtransfer_proto_enumTypes[2]
}

func (x MsgFromEnum) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use MsgFromEnum.Descriptor instead.
func (MsgFromEnum) EnumDescriptor() ([]byte, []int) {
	return file_basemsgtransfer_proto_rawDescGZIP(), []int{2}
}

type OfflinePushInfo struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Title         string `protobuf:"bytes,1,opt,name=title,proto3" json:"title,omitempty"`
	Desc          string `protobuf:"bytes,2,opt,name=desc,proto3" json:"desc,omitempty"`
	Ext           string `protobuf:"bytes,3,opt,name=ext,proto3" json:"ext,omitempty"`
	IOSPushSound  string `protobuf:"bytes,4,opt,name=IOSPushSound,proto3" json:"IOSPushSound,omitempty"`
	IOSBadgeCount bool   `protobuf:"varint,5,opt,name=IOSBadgeCount,proto3" json:"IOSBadgeCount,omitempty"`
	SignalInfo    string `protobuf:"bytes,6,opt,name=signalInfo,proto3" json:"signalInfo,omitempty"`
}

func (x *OfflinePushInfo) Reset() {
	*x = OfflinePushInfo{}
	if protoimpl.UnsafeEnabled {
		mi := &file_basemsgtransfer_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *OfflinePushInfo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*OfflinePushInfo) ProtoMessage() {}

func (x *OfflinePushInfo) ProtoReflect() protoreflect.Message {
	mi := &file_basemsgtransfer_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use OfflinePushInfo.ProtoReflect.Descriptor instead.
func (*OfflinePushInfo) Descriptor() ([]byte, []int) {
	return file_basemsgtransfer_proto_rawDescGZIP(), []int{0}
}

func (x *OfflinePushInfo) GetTitle() string {
	if x != nil {
		return x.Title
	}
	return ""
}

func (x *OfflinePushInfo) GetDesc() string {
	if x != nil {
		return x.Desc
	}
	return ""
}

func (x *OfflinePushInfo) GetExt() string {
	if x != nil {
		return x.Ext
	}
	return ""
}

func (x *OfflinePushInfo) GetIOSPushSound() string {
	if x != nil {
		return x.IOSPushSound
	}
	return ""
}

func (x *OfflinePushInfo) GetIOSBadgeCount() bool {
	if x != nil {
		return x.IOSBadgeCount
	}
	return false
}

func (x *OfflinePushInfo) GetSignalInfo() string {
	if x != nil {
		return x.SignalInfo
	}
	return ""
}

type AtInfo struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	AtUserId int64  `protobuf:"varint,1,opt,name=atUserId,proto3" json:"atUserId,omitempty"`
	Text     string `protobuf:"bytes,2,opt,name=text,proto3" json:"text,omitempty"`
	Style    string `protobuf:"bytes,3,opt,name=style,proto3" json:"style,omitempty"`
}

func (x *AtInfo) Reset() {
	*x = AtInfo{}
	if protoimpl.UnsafeEnabled {
		mi := &file_basemsgtransfer_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *AtInfo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*AtInfo) ProtoMessage() {}

func (x *AtInfo) ProtoReflect() protoreflect.Message {
	mi := &file_basemsgtransfer_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use AtInfo.ProtoReflect.Descriptor instead.
func (*AtInfo) Descriptor() ([]byte, []int) {
	return file_basemsgtransfer_proto_rawDescGZIP(), []int{1}
}

func (x *AtInfo) GetAtUserId() int64 {
	if x != nil {
		return x.AtUserId
	}
	return 0
}

func (x *AtInfo) GetText() string {
	if x != nil {
		return x.Text
	}
	return ""
}

func (x *AtInfo) GetStyle() string {
	if x != nil {
		return x.Style
	}
	return ""
}

// 发送和接收的消息结构体，type=2或type=4，对data进行json_decode后消息结构体
type MsgData struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// 客户端消息的唯一ID
	LocalId string `protobuf:"bytes,1,opt,name=localId,proto3" json:"localId,omitempty"`
	// 服务端生成消息的唯一ID
	MsgId string `protobuf:"bytes,2,opt,name=msgId,proto3" json:"msgId,omitempty"`
	// 会话ID，单聊：s_xxx,群聊:g_xxx,超级群sg_xxx,系统通知:n_xxx
	SessionId string `protobuf:"bytes,3,opt,name=sessionId,proto3" json:"sessionId,omitempty"`
	From      int64  `protobuf:"varint,4,opt,name=from,proto3" json:"from,omitempty"`
	To        int64  `protobuf:"varint,5,opt,name=to,proto3" json:"to,omitempty"`
	// 群聊ID
	GroupId int64 `protobuf:"varint,6,opt,name=groupId,proto3" json:"groupId,omitempty"`
	// 0:IM消息;1:实时消息
	MsgFrom uint32 `protobuf:"varint,7,opt,name=msgFrom,proto3" json:"msgFrom,omitempty"`
	// 1 单聊，2 群聊，3 语音房， 4 系统通知 5: 朋友圈
	SessionType int32 `protobuf:"varint,8,opt,name=sessionType,proto3" json:"sessionType,omitempty"`
	// 消息内容类型：Text:101,Image:102,Voice:103,Video:104,Custom:110,RealtimeVoice:115,RealTimeVideo:116
	ContentType int32 `protobuf:"varint,9,opt,name=contentType,proto3" json:"contentType,omitempty"`
	// 消息内容，一个json根据contentType解码到不同的结构体，见下面
	Content string `protobuf:"bytes,10,opt,name=content,proto3" json:"content,omitempty"`
	// 消息序号
	Seq int64 `protobuf:"varint,16,opt,name=seq,proto3" json:"seq,omitempty"`
	// 发送时间
	SendTime int64 `protobuf:"varint,17,opt,name=sendTime,proto3" json:"sendTime,omitempty"`
	// 消息创建时间
	CreateTime int64 `protobuf:"varint,18,opt,name=createTime,proto3" json:"createTime,omitempty"`
	// 一个map,不配置默认为true。
	// key如下 history:是否推送历史消息
	// persistent：是否永久存储消息
	// offlinePush：离线是否走第三方推送
	Options map[string]bool `protobuf:"bytes,19,rep,name=options,proto3" json:"options,omitempty" protobuf_key:"bytes,1,opt,name=key,proto3" protobuf_val:"varint,2,opt,name=value,proto3"`
	// 第三方推送配置
	OfflinePushInfo *OfflinePushInfo `protobuf:"bytes,20,opt,name=offlinePushInfo,proto3" json:"offlinePushInfo,omitempty"`
	// @用户列表
	// repeated int64 atUserIdList = 21;
	AtList   []*AtInfo         `protobuf:"bytes,21,rep,name=atList,proto3" json:"atList,omitempty"`
	Upgrade  bool              `protobuf:"varint,22,opt,name=upgrade,proto3" json:"upgrade,omitempty"`                                                                                  // 如果是 true，客户端不支持，则提示升级。 否则不显示
	Exts     map[string]string `protobuf:"bytes,23,rep,name=exts,proto3" json:"exts,omitempty" protobuf_key:"bytes,1,opt,name=key,proto3" protobuf_val:"bytes,2,opt,name=value,proto3"` // 扩展字段
	SysMsg   bool              `protobuf:"varint,24,opt,name=SysMsg,proto3" json:"SysMsg,omitempty"`                                                                                    //是否系统消息
	Receiver int64             `protobuf:"varint,35,opt,name=receiver,proto3" json:"receiver,omitempty"`                                                                                //消息接受方
}

func (x *MsgData) Reset() {
	*x = MsgData{}
	if protoimpl.UnsafeEnabled {
		mi := &file_basemsgtransfer_proto_msgTypes[2]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *MsgData) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*MsgData) ProtoMessage() {}

func (x *MsgData) ProtoReflect() protoreflect.Message {
	mi := &file_basemsgtransfer_proto_msgTypes[2]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use MsgData.ProtoReflect.Descriptor instead.
func (*MsgData) Descriptor() ([]byte, []int) {
	return file_basemsgtransfer_proto_rawDescGZIP(), []int{2}
}

func (x *MsgData) GetLocalId() string {
	if x != nil {
		return x.LocalId
	}
	return ""
}

func (x *MsgData) GetMsgId() string {
	if x != nil {
		return x.MsgId
	}
	return ""
}

func (x *MsgData) GetSessionId() string {
	if x != nil {
		return x.SessionId
	}
	return ""
}

func (x *MsgData) GetFrom() int64 {
	if x != nil {
		return x.From
	}
	return 0
}

func (x *MsgData) GetTo() int64 {
	if x != nil {
		return x.To
	}
	return 0
}

func (x *MsgData) GetGroupId() int64 {
	if x != nil {
		return x.GroupId
	}
	return 0
}

func (x *MsgData) GetMsgFrom() uint32 {
	if x != nil {
		return x.MsgFrom
	}
	return 0
}

func (x *MsgData) GetSessionType() int32 {
	if x != nil {
		return x.SessionType
	}
	return 0
}

func (x *MsgData) GetContentType() int32 {
	if x != nil {
		return x.ContentType
	}
	return 0
}

func (x *MsgData) GetContent() string {
	if x != nil {
		return x.Content
	}
	return ""
}

func (x *MsgData) GetSeq() int64 {
	if x != nil {
		return x.Seq
	}
	return 0
}

func (x *MsgData) GetSendTime() int64 {
	if x != nil {
		return x.SendTime
	}
	return 0
}

func (x *MsgData) GetCreateTime() int64 {
	if x != nil {
		return x.CreateTime
	}
	return 0
}

func (x *MsgData) GetOptions() map[string]bool {
	if x != nil {
		return x.Options
	}
	return nil
}

func (x *MsgData) GetOfflinePushInfo() *OfflinePushInfo {
	if x != nil {
		return x.OfflinePushInfo
	}
	return nil
}

func (x *MsgData) GetAtList() []*AtInfo {
	if x != nil {
		return x.AtList
	}
	return nil
}

func (x *MsgData) GetUpgrade() bool {
	if x != nil {
		return x.Upgrade
	}
	return false
}

func (x *MsgData) GetExts() map[string]string {
	if x != nil {
		return x.Exts
	}
	return nil
}

func (x *MsgData) GetSysMsg() bool {
	if x != nil {
		return x.SysMsg
	}
	return false
}

func (x *MsgData) GetReceiver() int64 {
	if x != nil {
		return x.Receiver
	}
	return 0
}

type SendMsgReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Msgs []*MsgData `protobuf:"bytes,1,rep,name=msgs,proto3" json:"msgs,omitempty"`
}

func (x *SendMsgReq) Reset() {
	*x = SendMsgReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_basemsgtransfer_proto_msgTypes[3]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *SendMsgReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SendMsgReq) ProtoMessage() {}

func (x *SendMsgReq) ProtoReflect() protoreflect.Message {
	mi := &file_basemsgtransfer_proto_msgTypes[3]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SendMsgReq.ProtoReflect.Descriptor instead.
func (*SendMsgReq) Descriptor() ([]byte, []int) {
	return file_basemsgtransfer_proto_rawDescGZIP(), []int{3}
}

func (x *SendMsgReq) GetMsgs() []*MsgData {
	if x != nil {
		return x.Msgs
	}
	return nil
}

type SendMsgResp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Base   *common.SvcBaseResp `protobuf:"bytes,1,opt,name=base,proto3" json:"base,omitempty"`
	Msgids []string            `protobuf:"bytes,2,rep,name=msgids,proto3" json:"msgids,omitempty"`
}

func (x *SendMsgResp) Reset() {
	*x = SendMsgResp{}
	if protoimpl.UnsafeEnabled {
		mi := &file_basemsgtransfer_proto_msgTypes[4]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *SendMsgResp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SendMsgResp) ProtoMessage() {}

func (x *SendMsgResp) ProtoReflect() protoreflect.Message {
	mi := &file_basemsgtransfer_proto_msgTypes[4]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SendMsgResp.ProtoReflect.Descriptor instead.
func (*SendMsgResp) Descriptor() ([]byte, []int) {
	return file_basemsgtransfer_proto_rawDescGZIP(), []int{4}
}

func (x *SendMsgResp) GetBase() *common.SvcBaseResp {
	if x != nil {
		return x.Base
	}
	return nil
}

func (x *SendMsgResp) GetMsgids() []string {
	if x != nil {
		return x.Msgids
	}
	return nil
}

type MulticastRealTimeMsgReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Msgs  []*MsgData `protobuf:"bytes,1,rep,name=msgs,proto3" json:"msgs,omitempty"`
	Topic string     `protobuf:"bytes,2,opt,name=topic,proto3" json:"topic,omitempty"`
}

func (x *MulticastRealTimeMsgReq) Reset() {
	*x = MulticastRealTimeMsgReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_basemsgtransfer_proto_msgTypes[5]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *MulticastRealTimeMsgReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*MulticastRealTimeMsgReq) ProtoMessage() {}

func (x *MulticastRealTimeMsgReq) ProtoReflect() protoreflect.Message {
	mi := &file_basemsgtransfer_proto_msgTypes[5]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use MulticastRealTimeMsgReq.ProtoReflect.Descriptor instead.
func (*MulticastRealTimeMsgReq) Descriptor() ([]byte, []int) {
	return file_basemsgtransfer_proto_rawDescGZIP(), []int{5}
}

func (x *MulticastRealTimeMsgReq) GetMsgs() []*MsgData {
	if x != nil {
		return x.Msgs
	}
	return nil
}

func (x *MulticastRealTimeMsgReq) GetTopic() string {
	if x != nil {
		return x.Topic
	}
	return ""
}

var File_basemsgtransfer_proto protoreflect.FileDescriptor

var file_basemsgtransfer_proto_rawDesc = []byte{
	0x0a, 0x15, 0x62, 0x61, 0x73, 0x65, 0x6d, 0x73, 0x67, 0x74, 0x72, 0x61, 0x6e, 0x73, 0x66, 0x65,
	0x72, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x12, 0x12, 0x76, 0x63, 0x2e, 0x62, 0x61, 0x73, 0x65,
	0x6d, 0x73, 0x67, 0x74, 0x72, 0x61, 0x6e, 0x73, 0x66, 0x65, 0x72, 0x1a, 0x2b, 0x70, 0x72, 0x6f,
	0x74, 0x6f, 0x63, 0x2d, 0x67, 0x65, 0x6e, 0x2d, 0x76, 0x61, 0x6c, 0x69, 0x64, 0x61, 0x74, 0x65,
	0x2f, 0x76, 0x61, 0x6c, 0x69, 0x64, 0x61, 0x74, 0x65, 0x2f, 0x76, 0x61, 0x6c, 0x69, 0x64, 0x61,
	0x74, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x1f, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65,
	0x2f, 0x61, 0x70, 0x69, 0x2f, 0x66, 0x69, 0x65, 0x6c, 0x64, 0x5f, 0x62, 0x65, 0x68, 0x61, 0x76,
	0x69, 0x6f, 0x72, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x13, 0x63, 0x6f, 0x6d, 0x6d, 0x6f,
	0x6e, 0x2f, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x22, 0xb7,
	0x01, 0x0a, 0x0f, 0x4f, 0x66, 0x66, 0x6c, 0x69, 0x6e, 0x65, 0x50, 0x75, 0x73, 0x68, 0x49, 0x6e,
	0x66, 0x6f, 0x12, 0x14, 0x0a, 0x05, 0x74, 0x69, 0x74, 0x6c, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x05, 0x74, 0x69, 0x74, 0x6c, 0x65, 0x12, 0x12, 0x0a, 0x04, 0x64, 0x65, 0x73, 0x63,
	0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x64, 0x65, 0x73, 0x63, 0x12, 0x10, 0x0a, 0x03,
	0x65, 0x78, 0x74, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x03, 0x65, 0x78, 0x74, 0x12, 0x22,
	0x0a, 0x0c, 0x49, 0x4f, 0x53, 0x50, 0x75, 0x73, 0x68, 0x53, 0x6f, 0x75, 0x6e, 0x64, 0x18, 0x04,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x0c, 0x49, 0x4f, 0x53, 0x50, 0x75, 0x73, 0x68, 0x53, 0x6f, 0x75,
	0x6e, 0x64, 0x12, 0x24, 0x0a, 0x0d, 0x49, 0x4f, 0x53, 0x42, 0x61, 0x64, 0x67, 0x65, 0x43, 0x6f,
	0x75, 0x6e, 0x74, 0x18, 0x05, 0x20, 0x01, 0x28, 0x08, 0x52, 0x0d, 0x49, 0x4f, 0x53, 0x42, 0x61,
	0x64, 0x67, 0x65, 0x43, 0x6f, 0x75, 0x6e, 0x74, 0x12, 0x1e, 0x0a, 0x0a, 0x73, 0x69, 0x67, 0x6e,
	0x61, 0x6c, 0x49, 0x6e, 0x66, 0x6f, 0x18, 0x06, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0a, 0x73, 0x69,
	0x67, 0x6e, 0x61, 0x6c, 0x49, 0x6e, 0x66, 0x6f, 0x22, 0x4e, 0x0a, 0x06, 0x41, 0x74, 0x49, 0x6e,
	0x66, 0x6f, 0x12, 0x1a, 0x0a, 0x08, 0x61, 0x74, 0x55, 0x73, 0x65, 0x72, 0x49, 0x64, 0x18, 0x01,
	0x20, 0x01, 0x28, 0x03, 0x52, 0x08, 0x61, 0x74, 0x55, 0x73, 0x65, 0x72, 0x49, 0x64, 0x12, 0x12,
	0x0a, 0x04, 0x74, 0x65, 0x78, 0x74, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x74, 0x65,
	0x78, 0x74, 0x12, 0x14, 0x0a, 0x05, 0x73, 0x74, 0x79, 0x6c, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x05, 0x73, 0x74, 0x79, 0x6c, 0x65, 0x22, 0xc8, 0x06, 0x0a, 0x07, 0x4d, 0x73, 0x67,
	0x44, 0x61, 0x74, 0x61, 0x12, 0x1d, 0x0a, 0x07, 0x6c, 0x6f, 0x63, 0x61, 0x6c, 0x49, 0x64, 0x18,
	0x01, 0x20, 0x01, 0x28, 0x09, 0x42, 0x03, 0xe0, 0x41, 0x02, 0x52, 0x07, 0x6c, 0x6f, 0x63, 0x61,
	0x6c, 0x49, 0x64, 0x12, 0x14, 0x0a, 0x05, 0x6d, 0x73, 0x67, 0x49, 0x64, 0x18, 0x02, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x05, 0x6d, 0x73, 0x67, 0x49, 0x64, 0x12, 0x21, 0x0a, 0x09, 0x73, 0x65, 0x73,
	0x73, 0x69, 0x6f, 0x6e, 0x49, 0x64, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x42, 0x03, 0xe0, 0x41,
	0x02, 0x52, 0x09, 0x73, 0x65, 0x73, 0x73, 0x69, 0x6f, 0x6e, 0x49, 0x64, 0x12, 0x12, 0x0a, 0x04,
	0x66, 0x72, 0x6f, 0x6d, 0x18, 0x04, 0x20, 0x01, 0x28, 0x03, 0x52, 0x04, 0x66, 0x72, 0x6f, 0x6d,
	0x12, 0x13, 0x0a, 0x02, 0x74, 0x6f, 0x18, 0x05, 0x20, 0x01, 0x28, 0x03, 0x42, 0x03, 0xe0, 0x41,
	0x02, 0x52, 0x02, 0x74, 0x6f, 0x12, 0x18, 0x0a, 0x07, 0x67, 0x72, 0x6f, 0x75, 0x70, 0x49, 0x64,
	0x18, 0x06, 0x20, 0x01, 0x28, 0x03, 0x52, 0x07, 0x67, 0x72, 0x6f, 0x75, 0x70, 0x49, 0x64, 0x12,
	0x18, 0x0a, 0x07, 0x6d, 0x73, 0x67, 0x46, 0x72, 0x6f, 0x6d, 0x18, 0x07, 0x20, 0x01, 0x28, 0x0d,
	0x52, 0x07, 0x6d, 0x73, 0x67, 0x46, 0x72, 0x6f, 0x6d, 0x12, 0x25, 0x0a, 0x0b, 0x73, 0x65, 0x73,
	0x73, 0x69, 0x6f, 0x6e, 0x54, 0x79, 0x70, 0x65, 0x18, 0x08, 0x20, 0x01, 0x28, 0x05, 0x42, 0x03,
	0xe0, 0x41, 0x02, 0x52, 0x0b, 0x73, 0x65, 0x73, 0x73, 0x69, 0x6f, 0x6e, 0x54, 0x79, 0x70, 0x65,
	0x12, 0x25, 0x0a, 0x0b, 0x63, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x74, 0x54, 0x79, 0x70, 0x65, 0x18,
	0x09, 0x20, 0x01, 0x28, 0x05, 0x42, 0x03, 0xe0, 0x41, 0x02, 0x52, 0x0b, 0x63, 0x6f, 0x6e, 0x74,
	0x65, 0x6e, 0x74, 0x54, 0x79, 0x70, 0x65, 0x12, 0x1d, 0x0a, 0x07, 0x63, 0x6f, 0x6e, 0x74, 0x65,
	0x6e, 0x74, 0x18, 0x0a, 0x20, 0x01, 0x28, 0x09, 0x42, 0x03, 0xe0, 0x41, 0x02, 0x52, 0x07, 0x63,
	0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x74, 0x12, 0x10, 0x0a, 0x03, 0x73, 0x65, 0x71, 0x18, 0x10, 0x20,
	0x01, 0x28, 0x03, 0x52, 0x03, 0x73, 0x65, 0x71, 0x12, 0x1a, 0x0a, 0x08, 0x73, 0x65, 0x6e, 0x64,
	0x54, 0x69, 0x6d, 0x65, 0x18, 0x11, 0x20, 0x01, 0x28, 0x03, 0x52, 0x08, 0x73, 0x65, 0x6e, 0x64,
	0x54, 0x69, 0x6d, 0x65, 0x12, 0x23, 0x0a, 0x0a, 0x63, 0x72, 0x65, 0x61, 0x74, 0x65, 0x54, 0x69,
	0x6d, 0x65, 0x18, 0x12, 0x20, 0x01, 0x28, 0x03, 0x42, 0x03, 0xe0, 0x41, 0x02, 0x52, 0x0a, 0x63,
	0x72, 0x65, 0x61, 0x74, 0x65, 0x54, 0x69, 0x6d, 0x65, 0x12, 0x42, 0x0a, 0x07, 0x6f, 0x70, 0x74,
	0x69, 0x6f, 0x6e, 0x73, 0x18, 0x13, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x28, 0x2e, 0x76, 0x63, 0x2e,
	0x62, 0x61, 0x73, 0x65, 0x6d, 0x73, 0x67, 0x74, 0x72, 0x61, 0x6e, 0x73, 0x66, 0x65, 0x72, 0x2e,
	0x4d, 0x73, 0x67, 0x44, 0x61, 0x74, 0x61, 0x2e, 0x4f, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x45,
	0x6e, 0x74, 0x72, 0x79, 0x52, 0x07, 0x6f, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x12, 0x4d, 0x0a,
	0x0f, 0x6f, 0x66, 0x66, 0x6c, 0x69, 0x6e, 0x65, 0x50, 0x75, 0x73, 0x68, 0x49, 0x6e, 0x66, 0x6f,
	0x18, 0x14, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x23, 0x2e, 0x76, 0x63, 0x2e, 0x62, 0x61, 0x73, 0x65,
	0x6d, 0x73, 0x67, 0x74, 0x72, 0x61, 0x6e, 0x73, 0x66, 0x65, 0x72, 0x2e, 0x4f, 0x66, 0x66, 0x6c,
	0x69, 0x6e, 0x65, 0x50, 0x75, 0x73, 0x68, 0x49, 0x6e, 0x66, 0x6f, 0x52, 0x0f, 0x6f, 0x66, 0x66,
	0x6c, 0x69, 0x6e, 0x65, 0x50, 0x75, 0x73, 0x68, 0x49, 0x6e, 0x66, 0x6f, 0x12, 0x32, 0x0a, 0x06,
	0x61, 0x74, 0x4c, 0x69, 0x73, 0x74, 0x18, 0x15, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x76,
	0x63, 0x2e, 0x62, 0x61, 0x73, 0x65, 0x6d, 0x73, 0x67, 0x74, 0x72, 0x61, 0x6e, 0x73, 0x66, 0x65,
	0x72, 0x2e, 0x41, 0x74, 0x49, 0x6e, 0x66, 0x6f, 0x52, 0x06, 0x61, 0x74, 0x4c, 0x69, 0x73, 0x74,
	0x12, 0x18, 0x0a, 0x07, 0x75, 0x70, 0x67, 0x72, 0x61, 0x64, 0x65, 0x18, 0x16, 0x20, 0x01, 0x28,
	0x08, 0x52, 0x07, 0x75, 0x70, 0x67, 0x72, 0x61, 0x64, 0x65, 0x12, 0x39, 0x0a, 0x04, 0x65, 0x78,
	0x74, 0x73, 0x18, 0x17, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x25, 0x2e, 0x76, 0x63, 0x2e, 0x62, 0x61,
	0x73, 0x65, 0x6d, 0x73, 0x67, 0x74, 0x72, 0x61, 0x6e, 0x73, 0x66, 0x65, 0x72, 0x2e, 0x4d, 0x73,
	0x67, 0x44, 0x61, 0x74, 0x61, 0x2e, 0x45, 0x78, 0x74, 0x73, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x52,
	0x04, 0x65, 0x78, 0x74, 0x73, 0x12, 0x16, 0x0a, 0x06, 0x53, 0x79, 0x73, 0x4d, 0x73, 0x67, 0x18,
	0x18, 0x20, 0x01, 0x28, 0x08, 0x52, 0x06, 0x53, 0x79, 0x73, 0x4d, 0x73, 0x67, 0x12, 0x1f, 0x0a,
	0x08, 0x72, 0x65, 0x63, 0x65, 0x69, 0x76, 0x65, 0x72, 0x18, 0x23, 0x20, 0x01, 0x28, 0x03, 0x42,
	0x03, 0xe0, 0x41, 0x02, 0x52, 0x08, 0x72, 0x65, 0x63, 0x65, 0x69, 0x76, 0x65, 0x72, 0x1a, 0x3a,
	0x0a, 0x0c, 0x4f, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x12, 0x10,
	0x0a, 0x03, 0x6b, 0x65, 0x79, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x03, 0x6b, 0x65, 0x79,
	0x12, 0x14, 0x0a, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x08, 0x52,
	0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x3a, 0x02, 0x38, 0x01, 0x1a, 0x37, 0x0a, 0x09, 0x45, 0x78,
	0x74, 0x73, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x12, 0x10, 0x0a, 0x03, 0x6b, 0x65, 0x79, 0x18, 0x01,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x03, 0x6b, 0x65, 0x79, 0x12, 0x14, 0x0a, 0x05, 0x76, 0x61, 0x6c,
	0x75, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x3a,
	0x02, 0x38, 0x01, 0x22, 0x4a, 0x0a, 0x0a, 0x53, 0x65, 0x6e, 0x64, 0x4d, 0x73, 0x67, 0x52, 0x65,
	0x71, 0x12, 0x3c, 0x0a, 0x04, 0x6d, 0x73, 0x67, 0x73, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32,
	0x1b, 0x2e, 0x76, 0x63, 0x2e, 0x62, 0x61, 0x73, 0x65, 0x6d, 0x73, 0x67, 0x74, 0x72, 0x61, 0x6e,
	0x73, 0x66, 0x65, 0x72, 0x2e, 0x4d, 0x73, 0x67, 0x44, 0x61, 0x74, 0x61, 0x42, 0x0b, 0xe0, 0x41,
	0x02, 0xfa, 0x42, 0x05, 0x92, 0x01, 0x02, 0x08, 0x01, 0x52, 0x04, 0x6d, 0x73, 0x67, 0x73, 0x22,
	0x4e, 0x0a, 0x0b, 0x53, 0x65, 0x6e, 0x64, 0x4d, 0x73, 0x67, 0x52, 0x65, 0x73, 0x70, 0x12, 0x27,
	0x0a, 0x04, 0x62, 0x61, 0x73, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x13, 0x2e, 0x63,
	0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x53, 0x76, 0x63, 0x42, 0x61, 0x73, 0x65, 0x52, 0x65, 0x73,
	0x70, 0x52, 0x04, 0x62, 0x61, 0x73, 0x65, 0x12, 0x16, 0x0a, 0x06, 0x6d, 0x73, 0x67, 0x69, 0x64,
	0x73, 0x18, 0x02, 0x20, 0x03, 0x28, 0x09, 0x52, 0x06, 0x6d, 0x73, 0x67, 0x69, 0x64, 0x73, 0x22,
	0x60, 0x0a, 0x17, 0x4d, 0x75, 0x6c, 0x74, 0x69, 0x63, 0x61, 0x73, 0x74, 0x52, 0x65, 0x61, 0x6c,
	0x54, 0x69, 0x6d, 0x65, 0x4d, 0x73, 0x67, 0x52, 0x65, 0x71, 0x12, 0x2f, 0x0a, 0x04, 0x6d, 0x73,
	0x67, 0x73, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x1b, 0x2e, 0x76, 0x63, 0x2e, 0x62, 0x61,
	0x73, 0x65, 0x6d, 0x73, 0x67, 0x74, 0x72, 0x61, 0x6e, 0x73, 0x66, 0x65, 0x72, 0x2e, 0x4d, 0x73,
	0x67, 0x44, 0x61, 0x74, 0x61, 0x52, 0x04, 0x6d, 0x73, 0x67, 0x73, 0x12, 0x14, 0x0a, 0x05, 0x74,
	0x6f, 0x70, 0x69, 0x63, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x74, 0x6f, 0x70, 0x69,
	0x63, 0x2a, 0x81, 0x01, 0x0a, 0x0b, 0x53, 0x65, 0x73, 0x73, 0x69, 0x6f, 0x6e, 0x54, 0x79, 0x70,
	0x65, 0x12, 0x16, 0x0a, 0x12, 0x53, 0x65, 0x73, 0x73, 0x69, 0x6f, 0x6e, 0x54, 0x79, 0x70, 0x65,
	0x55, 0x6e, 0x6b, 0x6e, 0x6f, 0x77, 0x6e, 0x10, 0x00, 0x12, 0x13, 0x0a, 0x0f, 0x53, 0x65, 0x73,
	0x73, 0x69, 0x6f, 0x6e, 0x54, 0x79, 0x70, 0x65, 0x43, 0x68, 0x61, 0x74, 0x10, 0x01, 0x12, 0x14,
	0x0a, 0x10, 0x53, 0x65, 0x73, 0x73, 0x69, 0x6f, 0x6e, 0x54, 0x79, 0x70, 0x65, 0x47, 0x72, 0x6f,
	0x75, 0x70, 0x10, 0x02, 0x12, 0x18, 0x0a, 0x14, 0x53, 0x65, 0x73, 0x73, 0x69, 0x6f, 0x6e, 0x54,
	0x79, 0x70, 0x65, 0x56, 0x6f, 0x69, 0x63, 0x65, 0x52, 0x6f, 0x6f, 0x6d, 0x10, 0x03, 0x12, 0x15,
	0x0a, 0x11, 0x53, 0x65, 0x73, 0x73, 0x69, 0x6f, 0x6e, 0x54, 0x79, 0x70, 0x65, 0x53, 0x79, 0x73,
	0x74, 0x65, 0x6d, 0x10, 0x04, 0x2a, 0xa0, 0x02, 0x0a, 0x0b, 0x43, 0x6f, 0x6e, 0x74, 0x65, 0x6e,
	0x74, 0x54, 0x79, 0x70, 0x65, 0x12, 0x0b, 0x0a, 0x07, 0x49, 0x6e, 0x76, 0x61, 0x6c, 0x69, 0x64,
	0x10, 0x00, 0x12, 0x08, 0x0a, 0x04, 0x54, 0x65, 0x78, 0x74, 0x10, 0x65, 0x12, 0x09, 0x0a, 0x05,
	0x49, 0x6d, 0x61, 0x67, 0x65, 0x10, 0x66, 0x12, 0x09, 0x0a, 0x05, 0x56, 0x6f, 0x69, 0x63, 0x65,
	0x10, 0x67, 0x12, 0x09, 0x0a, 0x05, 0x56, 0x69, 0x64, 0x65, 0x6f, 0x10, 0x68, 0x12, 0x08, 0x0a,
	0x04, 0x47, 0x69, 0x66, 0x74, 0x10, 0x69, 0x12, 0x0a, 0x0a, 0x06, 0x43, 0x75, 0x73, 0x74, 0x6f,
	0x6d, 0x10, 0x6e, 0x12, 0x13, 0x0a, 0x0e, 0x52, 0x69, 0x63, 0x68, 0x54, 0x65, 0x78, 0x74, 0x4e,
	0x6f, 0x72, 0x6d, 0x61, 0x6c, 0x10, 0x96, 0x01, 0x12, 0x0b, 0x0a, 0x06, 0x46, 0x6f, 0x6c, 0x6c,
	0x6f, 0x77, 0x10, 0x97, 0x01, 0x12, 0x0c, 0x0a, 0x07, 0x43, 0x6f, 0x6d, 0x6d, 0x65, 0x6e, 0x74,
	0x10, 0x98, 0x01, 0x12, 0x12, 0x0a, 0x0d, 0x43, 0x6f, 0x6d, 0x6d, 0x65, 0x6e, 0x74, 0x53, 0x63,
	0x72, 0x69, 0x70, 0x74, 0x10, 0xe1, 0x76, 0x12, 0x11, 0x0a, 0x0c, 0x43, 0x6f, 0x6d, 0x6d, 0x65,
	0x6e, 0x74, 0x52, 0x65, 0x70, 0x6c, 0x79, 0x10, 0xe2, 0x76, 0x12, 0x09, 0x0a, 0x04, 0x4c, 0x69,
	0x6b, 0x65, 0x10, 0x99, 0x01, 0x12, 0x0f, 0x0a, 0x0a, 0x4c, 0x69, 0x6b, 0x65, 0x53, 0x63, 0x72,
	0x69, 0x70, 0x74, 0x10, 0xc5, 0x77, 0x12, 0x10, 0x0a, 0x0b, 0x4c, 0x69, 0x6b, 0x65, 0x43, 0x6f,
	0x6d, 0x6d, 0x65, 0x6e, 0x74, 0x10, 0xc6, 0x77, 0x12, 0x10, 0x0a, 0x0b, 0x4c, 0x69, 0x6b, 0x65,
	0x44, 0x75, 0x62, 0x62, 0x69, 0x6e, 0x67, 0x10, 0xc7, 0x77, 0x12, 0x0c, 0x0a, 0x07, 0x44, 0x75,
	0x62, 0x62, 0x69, 0x6e, 0x67, 0x10, 0x9a, 0x01, 0x12, 0x0b, 0x0a, 0x06, 0x52, 0x65, 0x76, 0x69,
	0x65, 0x77, 0x10, 0x9b, 0x01, 0x12, 0x11, 0x0a, 0x0c, 0x52, 0x65, 0x76, 0x69, 0x65, 0x77, 0x53,
	0x63, 0x72, 0x69, 0x70, 0x74, 0x10, 0x8d, 0x79, 0x2a, 0x41, 0x0a, 0x0b, 0x4d, 0x73, 0x67, 0x46,
	0x72, 0x6f, 0x6d, 0x45, 0x6e, 0x75, 0x6d, 0x12, 0x0d, 0x0a, 0x09, 0x4d, 0x73, 0x67, 0x46, 0x72,
	0x6f, 0x6d, 0x49, 0x6d, 0x10, 0x00, 0x12, 0x13, 0x0a, 0x0f, 0x4d, 0x73, 0x67, 0x46, 0x72, 0x6f,
	0x6d, 0x52, 0x65, 0x61, 0x6c, 0x54, 0x69, 0x6d, 0x65, 0x10, 0x01, 0x12, 0x0e, 0x0a, 0x0a, 0x4d,
	0x73, 0x67, 0x46, 0x72, 0x6f, 0x6d, 0x49, 0x6d, 0x32, 0x10, 0x02, 0x32, 0x8f, 0x02, 0x0a, 0x01,
	0x73, 0x12, 0x4c, 0x0a, 0x07, 0x53, 0x65, 0x6e, 0x64, 0x4d, 0x73, 0x67, 0x12, 0x1e, 0x2e, 0x76,
	0x63, 0x2e, 0x62, 0x61, 0x73, 0x65, 0x6d, 0x73, 0x67, 0x74, 0x72, 0x61, 0x6e, 0x73, 0x66, 0x65,
	0x72, 0x2e, 0x53, 0x65, 0x6e, 0x64, 0x4d, 0x73, 0x67, 0x52, 0x65, 0x71, 0x1a, 0x1f, 0x2e, 0x76,
	0x63, 0x2e, 0x62, 0x61, 0x73, 0x65, 0x6d, 0x73, 0x67, 0x74, 0x72, 0x61, 0x6e, 0x73, 0x66, 0x65,
	0x72, 0x2e, 0x53, 0x65, 0x6e, 0x64, 0x4d, 0x73, 0x67, 0x52, 0x65, 0x73, 0x70, 0x22, 0x00, 0x12,
	0x54, 0x0a, 0x0f, 0x53, 0x65, 0x6e, 0x64, 0x52, 0x65, 0x61, 0x6c, 0x54, 0x69, 0x6d, 0x65, 0x4d,
	0x73, 0x67, 0x12, 0x1e, 0x2e, 0x76, 0x63, 0x2e, 0x62, 0x61, 0x73, 0x65, 0x6d, 0x73, 0x67, 0x74,
	0x72, 0x61, 0x6e, 0x73, 0x66, 0x65, 0x72, 0x2e, 0x53, 0x65, 0x6e, 0x64, 0x4d, 0x73, 0x67, 0x52,
	0x65, 0x71, 0x1a, 0x1f, 0x2e, 0x76, 0x63, 0x2e, 0x62, 0x61, 0x73, 0x65, 0x6d, 0x73, 0x67, 0x74,
	0x72, 0x61, 0x6e, 0x73, 0x66, 0x65, 0x72, 0x2e, 0x53, 0x65, 0x6e, 0x64, 0x4d, 0x73, 0x67, 0x52,
	0x65, 0x73, 0x70, 0x22, 0x00, 0x12, 0x66, 0x0a, 0x14, 0x4d, 0x75, 0x6c, 0x74, 0x69, 0x63, 0x61,
	0x73, 0x74, 0x52, 0x65, 0x61, 0x6c, 0x54, 0x69, 0x6d, 0x65, 0x4d, 0x73, 0x67, 0x12, 0x2b, 0x2e,
	0x76, 0x63, 0x2e, 0x62, 0x61, 0x73, 0x65, 0x6d, 0x73, 0x67, 0x74, 0x72, 0x61, 0x6e, 0x73, 0x66,
	0x65, 0x72, 0x2e, 0x4d, 0x75, 0x6c, 0x74, 0x69, 0x63, 0x61, 0x73, 0x74, 0x52, 0x65, 0x61, 0x6c,
	0x54, 0x69, 0x6d, 0x65, 0x4d, 0x73, 0x67, 0x52, 0x65, 0x71, 0x1a, 0x1f, 0x2e, 0x76, 0x63, 0x2e,
	0x62, 0x61, 0x73, 0x65, 0x6d, 0x73, 0x67, 0x74, 0x72, 0x61, 0x6e, 0x73, 0x66, 0x65, 0x72, 0x2e,
	0x53, 0x65, 0x6e, 0x64, 0x4d, 0x73, 0x67, 0x52, 0x65, 0x73, 0x70, 0x22, 0x00, 0x42, 0x53, 0x5a,
	0x51, 0x6e, 0x65, 0x77, 0x2d, 0x67, 0x69, 0x74, 0x6c, 0x61, 0x62, 0x2e, 0x78, 0x75, 0x6e, 0x6c,
	0x65, 0x69, 0x2e, 0x63, 0x6e, 0x2f, 0x76, 0x63, 0x70, 0x72, 0x6f, 0x6a, 0x65, 0x63, 0x74, 0x2f,
	0x62, 0x61, 0x63, 0x6b, 0x65, 0x6e, 0x64, 0x73, 0x2f, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2f, 0x61,
	0x70, 0x69, 0x2f, 0x62, 0x61, 0x73, 0x65, 0x6d, 0x73, 0x67, 0x74, 0x72, 0x61, 0x6e, 0x73, 0x66,
	0x65, 0x72, 0x3b, 0x62, 0x61, 0x73, 0x65, 0x6d, 0x73, 0x67, 0x74, 0x72, 0x61, 0x6e, 0x73, 0x66,
	0x65, 0x72, 0x62, 0x06, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_basemsgtransfer_proto_rawDescOnce sync.Once
	file_basemsgtransfer_proto_rawDescData = file_basemsgtransfer_proto_rawDesc
)

func file_basemsgtransfer_proto_rawDescGZIP() []byte {
	file_basemsgtransfer_proto_rawDescOnce.Do(func() {
		file_basemsgtransfer_proto_rawDescData = protoimpl.X.CompressGZIP(file_basemsgtransfer_proto_rawDescData)
	})
	return file_basemsgtransfer_proto_rawDescData
}

var file_basemsgtransfer_proto_enumTypes = make([]protoimpl.EnumInfo, 3)
var file_basemsgtransfer_proto_msgTypes = make([]protoimpl.MessageInfo, 8)
var file_basemsgtransfer_proto_goTypes = []interface{}{
	(SessionType)(0),                // 0: vc.basemsgtransfer.SessionType
	(ContentType)(0),                // 1: vc.basemsgtransfer.ContentType
	(MsgFromEnum)(0),                // 2: vc.basemsgtransfer.MsgFromEnum
	(*OfflinePushInfo)(nil),         // 3: vc.basemsgtransfer.OfflinePushInfo
	(*AtInfo)(nil),                  // 4: vc.basemsgtransfer.AtInfo
	(*MsgData)(nil),                 // 5: vc.basemsgtransfer.MsgData
	(*SendMsgReq)(nil),              // 6: vc.basemsgtransfer.SendMsgReq
	(*SendMsgResp)(nil),             // 7: vc.basemsgtransfer.SendMsgResp
	(*MulticastRealTimeMsgReq)(nil), // 8: vc.basemsgtransfer.MulticastRealTimeMsgReq
	nil,                             // 9: vc.basemsgtransfer.MsgData.OptionsEntry
	nil,                             // 10: vc.basemsgtransfer.MsgData.ExtsEntry
	(*common.SvcBaseResp)(nil),      // 11: common.SvcBaseResp
}
var file_basemsgtransfer_proto_depIdxs = []int32{
	9,  // 0: vc.basemsgtransfer.MsgData.options:type_name -> vc.basemsgtransfer.MsgData.OptionsEntry
	3,  // 1: vc.basemsgtransfer.MsgData.offlinePushInfo:type_name -> vc.basemsgtransfer.OfflinePushInfo
	4,  // 2: vc.basemsgtransfer.MsgData.atList:type_name -> vc.basemsgtransfer.AtInfo
	10, // 3: vc.basemsgtransfer.MsgData.exts:type_name -> vc.basemsgtransfer.MsgData.ExtsEntry
	5,  // 4: vc.basemsgtransfer.SendMsgReq.msgs:type_name -> vc.basemsgtransfer.MsgData
	11, // 5: vc.basemsgtransfer.SendMsgResp.base:type_name -> common.SvcBaseResp
	5,  // 6: vc.basemsgtransfer.MulticastRealTimeMsgReq.msgs:type_name -> vc.basemsgtransfer.MsgData
	6,  // 7: vc.basemsgtransfer.s.SendMsg:input_type -> vc.basemsgtransfer.SendMsgReq
	6,  // 8: vc.basemsgtransfer.s.SendRealTimeMsg:input_type -> vc.basemsgtransfer.SendMsgReq
	8,  // 9: vc.basemsgtransfer.s.MulticastRealTimeMsg:input_type -> vc.basemsgtransfer.MulticastRealTimeMsgReq
	7,  // 10: vc.basemsgtransfer.s.SendMsg:output_type -> vc.basemsgtransfer.SendMsgResp
	7,  // 11: vc.basemsgtransfer.s.SendRealTimeMsg:output_type -> vc.basemsgtransfer.SendMsgResp
	7,  // 12: vc.basemsgtransfer.s.MulticastRealTimeMsg:output_type -> vc.basemsgtransfer.SendMsgResp
	10, // [10:13] is the sub-list for method output_type
	7,  // [7:10] is the sub-list for method input_type
	7,  // [7:7] is the sub-list for extension type_name
	7,  // [7:7] is the sub-list for extension extendee
	0,  // [0:7] is the sub-list for field type_name
}

func init() { file_basemsgtransfer_proto_init() }
func file_basemsgtransfer_proto_init() {
	if File_basemsgtransfer_proto != nil {
		return
	}
	if !protoimpl.UnsafeEnabled {
		file_basemsgtransfer_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*OfflinePushInfo); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_basemsgtransfer_proto_msgTypes[1].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*AtInfo); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_basemsgtransfer_proto_msgTypes[2].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*MsgData); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_basemsgtransfer_proto_msgTypes[3].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*SendMsgReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_basemsgtransfer_proto_msgTypes[4].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*SendMsgResp); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_basemsgtransfer_proto_msgTypes[5].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*MulticastRealTimeMsgReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_basemsgtransfer_proto_rawDesc,
			NumEnums:      3,
			NumMessages:   8,
			NumExtensions: 0,
			NumServices:   1,
		},
		GoTypes:           file_basemsgtransfer_proto_goTypes,
		DependencyIndexes: file_basemsgtransfer_proto_depIdxs,
		EnumInfos:         file_basemsgtransfer_proto_enumTypes,
		MessageInfos:      file_basemsgtransfer_proto_msgTypes,
	}.Build()
	File_basemsgtransfer_proto = out.File
	file_basemsgtransfer_proto_rawDesc = nil
	file_basemsgtransfer_proto_goTypes = nil
	file_basemsgtransfer_proto_depIdxs = nil
}
