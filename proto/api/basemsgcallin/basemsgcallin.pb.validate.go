// Code generated by protoc-gen-validate. DO NOT EDIT.
// source: basemsgcallin.proto

package basemsgcallin

import (
	"bytes"
	"errors"
	"fmt"
	"net"
	"net/mail"
	"net/url"
	"regexp"
	"sort"
	"strings"
	"time"
	"unicode/utf8"

	"google.golang.org/protobuf/types/known/anypb"
)

// ensure the imports are used
var (
	_ = bytes.MinRead
	_ = errors.New("")
	_ = fmt.Print
	_ = utf8.UTFMax
	_ = (*regexp.Regexp)(nil)
	_ = (*strings.Reader)(nil)
	_ = net.IPv4len
	_ = time.Duration(0)
	_ = (*url.URL)(nil)
	_ = (*mail.Address)(nil)
	_ = anypb.Any{}
	_ = sort.Sort
)

// Validate checks the field values on TestReq with the rules defined in the
// proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *TestReq) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on TestReq with the rules defined in the
// proto definition for this message. If any rules are violated, the result is
// a list of violation errors wrapped in TestReqMultiError, or nil if none found.
func (m *TestReq) ValidateAll() error {
	return m.validate(true)
}

func (m *TestReq) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if len(errors) > 0 {
		return TestReqMultiError(errors)
	}

	return nil
}

// TestReqMultiError is an error wrapping multiple validation errors returned
// by TestReq.ValidateAll() if the designated constraints aren't met.
type TestReqMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m TestReqMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m TestReqMultiError) AllErrors() []error { return m }

// TestReqValidationError is the validation error returned by TestReq.Validate
// if the designated constraints aren't met.
type TestReqValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e TestReqValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e TestReqValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e TestReqValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e TestReqValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e TestReqValidationError) ErrorName() string { return "TestReqValidationError" }

// Error satisfies the builtin error interface
func (e TestReqValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sTestReq.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = TestReqValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = TestReqValidationError{}

// Validate checks the field values on TestRsp with the rules defined in the
// proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *TestRsp) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on TestRsp with the rules defined in the
// proto definition for this message. If any rules are violated, the result is
// a list of violation errors wrapped in TestRspMultiError, or nil if none found.
func (m *TestRsp) ValidateAll() error {
	return m.validate(true)
}

func (m *TestRsp) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Result

	// no validation rules for Message

	if len(errors) > 0 {
		return TestRspMultiError(errors)
	}

	return nil
}

// TestRspMultiError is an error wrapping multiple validation errors returned
// by TestRsp.ValidateAll() if the designated constraints aren't met.
type TestRspMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m TestRspMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m TestRspMultiError) AllErrors() []error { return m }

// TestRspValidationError is the validation error returned by TestRsp.Validate
// if the designated constraints aren't met.
type TestRspValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e TestRspValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e TestRspValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e TestRspValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e TestRspValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e TestRspValidationError) ErrorName() string { return "TestRspValidationError" }

// Error satisfies the builtin error interface
func (e TestRspValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sTestRsp.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = TestRspValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = TestRspValidationError{}

// Validate checks the field values on GetMsgConnTokenReq with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *GetMsgConnTokenReq) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GetMsgConnTokenReq with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// GetMsgConnTokenReqMultiError, or nil if none found.
func (m *GetMsgConnTokenReq) ValidateAll() error {
	return m.validate(true)
}

func (m *GetMsgConnTokenReq) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for PlatformId

	// no validation rules for TimeStamp

	// no validation rules for NonceStr

	// no validation rules for Sign

	// no validation rules for UserId

	// no validation rules for DeviceId

	// no validation rules for ExtendData

	if len(errors) > 0 {
		return GetMsgConnTokenReqMultiError(errors)
	}

	return nil
}

// GetMsgConnTokenReqMultiError is an error wrapping multiple validation errors
// returned by GetMsgConnTokenReq.ValidateAll() if the designated constraints
// aren't met.
type GetMsgConnTokenReqMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetMsgConnTokenReqMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetMsgConnTokenReqMultiError) AllErrors() []error { return m }

// GetMsgConnTokenReqValidationError is the validation error returned by
// GetMsgConnTokenReq.Validate if the designated constraints aren't met.
type GetMsgConnTokenReqValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetMsgConnTokenReqValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetMsgConnTokenReqValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetMsgConnTokenReqValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetMsgConnTokenReqValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetMsgConnTokenReqValidationError) ErrorName() string {
	return "GetMsgConnTokenReqValidationError"
}

// Error satisfies the builtin error interface
func (e GetMsgConnTokenReqValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetMsgConnTokenReq.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetMsgConnTokenReqValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetMsgConnTokenReqValidationError{}

// Validate checks the field values on GetMsgConnTokenRsp with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *GetMsgConnTokenRsp) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GetMsgConnTokenRsp with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// GetMsgConnTokenRspMultiError, or nil if none found.
func (m *GetMsgConnTokenRsp) ValidateAll() error {
	return m.validate(true)
}

func (m *GetMsgConnTokenRsp) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetBase()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetMsgConnTokenRspValidationError{
					field:  "Base",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetMsgConnTokenRspValidationError{
					field:  "Base",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetBase()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetMsgConnTokenRspValidationError{
				field:  "Base",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetData()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetMsgConnTokenRspValidationError{
					field:  "Data",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetMsgConnTokenRspValidationError{
					field:  "Data",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetData()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetMsgConnTokenRspValidationError{
				field:  "Data",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return GetMsgConnTokenRspMultiError(errors)
	}

	return nil
}

// GetMsgConnTokenRspMultiError is an error wrapping multiple validation errors
// returned by GetMsgConnTokenRsp.ValidateAll() if the designated constraints
// aren't met.
type GetMsgConnTokenRspMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetMsgConnTokenRspMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetMsgConnTokenRspMultiError) AllErrors() []error { return m }

// GetMsgConnTokenRspValidationError is the validation error returned by
// GetMsgConnTokenRsp.Validate if the designated constraints aren't met.
type GetMsgConnTokenRspValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetMsgConnTokenRspValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetMsgConnTokenRspValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetMsgConnTokenRspValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetMsgConnTokenRspValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetMsgConnTokenRspValidationError) ErrorName() string {
	return "GetMsgConnTokenRspValidationError"
}

// Error satisfies the builtin error interface
func (e GetMsgConnTokenRspValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetMsgConnTokenRsp.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetMsgConnTokenRspValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetMsgConnTokenRspValidationError{}

// Validate checks the field values on MsgTokenData with the rules defined in
// the proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *MsgTokenData) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on MsgTokenData with the rules defined
// in the proto definition for this message. If any rules are violated, the
// result is a list of violation errors wrapped in MsgTokenDataMultiError, or
// nil if none found.
func (m *MsgTokenData) ValidateAll() error {
	return m.validate(true)
}

func (m *MsgTokenData) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Token

	if all {
		switch v := interface{}(m.GetMasterAddr()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, MsgTokenDataValidationError{
					field:  "MasterAddr",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, MsgTokenDataValidationError{
					field:  "MasterAddr",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetMasterAddr()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return MsgTokenDataValidationError{
				field:  "MasterAddr",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetSlaveAddr()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, MsgTokenDataValidationError{
					field:  "SlaveAddr",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, MsgTokenDataValidationError{
					field:  "SlaveAddr",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetSlaveAddr()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return MsgTokenDataValidationError{
				field:  "SlaveAddr",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for GeneralTopic

	// no validation rules for KickTopic

	if len(errors) > 0 {
		return MsgTokenDataMultiError(errors)
	}

	return nil
}

// MsgTokenDataMultiError is an error wrapping multiple validation errors
// returned by MsgTokenData.ValidateAll() if the designated constraints aren't met.
type MsgTokenDataMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m MsgTokenDataMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m MsgTokenDataMultiError) AllErrors() []error { return m }

// MsgTokenDataValidationError is the validation error returned by
// MsgTokenData.Validate if the designated constraints aren't met.
type MsgTokenDataValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e MsgTokenDataValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e MsgTokenDataValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e MsgTokenDataValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e MsgTokenDataValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e MsgTokenDataValidationError) ErrorName() string { return "MsgTokenDataValidationError" }

// Error satisfies the builtin error interface
func (e MsgTokenDataValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sMsgTokenData.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = MsgTokenDataValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = MsgTokenDataValidationError{}

// Validate checks the field values on MsgAddrData with the rules defined in
// the proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *MsgAddrData) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on MsgAddrData with the rules defined in
// the proto definition for this message. If any rules are violated, the
// result is a list of violation errors wrapped in MsgAddrDataMultiError, or
// nil if none found.
func (m *MsgAddrData) ValidateAll() error {
	return m.validate(true)
}

func (m *MsgAddrData) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Host

	// no validation rules for Port

	if len(errors) > 0 {
		return MsgAddrDataMultiError(errors)
	}

	return nil
}

// MsgAddrDataMultiError is an error wrapping multiple validation errors
// returned by MsgAddrData.ValidateAll() if the designated constraints aren't met.
type MsgAddrDataMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m MsgAddrDataMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m MsgAddrDataMultiError) AllErrors() []error { return m }

// MsgAddrDataValidationError is the validation error returned by
// MsgAddrData.Validate if the designated constraints aren't met.
type MsgAddrDataValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e MsgAddrDataValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e MsgAddrDataValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e MsgAddrDataValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e MsgAddrDataValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e MsgAddrDataValidationError) ErrorName() string { return "MsgAddrDataValidationError" }

// Error satisfies the builtin error interface
func (e MsgAddrDataValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sMsgAddrData.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = MsgAddrDataValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = MsgAddrDataValidationError{}

// Validate checks the field values on PublishMsgReq with the rules defined in
// the proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *PublishMsgReq) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on PublishMsgReq with the rules defined
// in the proto definition for this message. If any rules are violated, the
// result is a list of violation errors wrapped in PublishMsgReqMultiError, or
// nil if none found.
func (m *PublishMsgReq) ValidateAll() error {
	return m.validate(true)
}

func (m *PublishMsgReq) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for PlatformId

	// no validation rules for TimeStamp

	// no validation rules for NonceStr

	// no validation rules for Sign

	// no validation rules for Payload

	// no validation rules for Type

	// no validation rules for TargetJson

	if len(errors) > 0 {
		return PublishMsgReqMultiError(errors)
	}

	return nil
}

// PublishMsgReqMultiError is an error wrapping multiple validation errors
// returned by PublishMsgReq.ValidateAll() if the designated constraints
// aren't met.
type PublishMsgReqMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m PublishMsgReqMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m PublishMsgReqMultiError) AllErrors() []error { return m }

// PublishMsgReqValidationError is the validation error returned by
// PublishMsgReq.Validate if the designated constraints aren't met.
type PublishMsgReqValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e PublishMsgReqValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e PublishMsgReqValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e PublishMsgReqValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e PublishMsgReqValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e PublishMsgReqValidationError) ErrorName() string { return "PublishMsgReqValidationError" }

// Error satisfies the builtin error interface
func (e PublishMsgReqValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sPublishMsgReq.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = PublishMsgReqValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = PublishMsgReqValidationError{}

// Validate checks the field values on PublishTargetInfo with the rules defined
// in the proto definition for this message. If any rules are violated, the
// first error encountered is returned, or nil if there are no violations.
func (m *PublishTargetInfo) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on PublishTargetInfo with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// PublishTargetInfoMultiError, or nil if none found.
func (m *PublishTargetInfo) ValidateAll() error {
	return m.validate(true)
}

func (m *PublishTargetInfo) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for UserId

	// no validation rules for DeviceId

	// no validation rules for Topic

	// no validation rules for BroadcastRate

	for idx, item := range m.GetLimitList() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, PublishTargetInfoValidationError{
						field:  fmt.Sprintf("LimitList[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, PublishTargetInfoValidationError{
						field:  fmt.Sprintf("LimitList[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return PublishTargetInfoValidationError{
					field:  fmt.Sprintf("LimitList[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	if len(errors) > 0 {
		return PublishTargetInfoMultiError(errors)
	}

	return nil
}

// PublishTargetInfoMultiError is an error wrapping multiple validation errors
// returned by PublishTargetInfo.ValidateAll() if the designated constraints
// aren't met.
type PublishTargetInfoMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m PublishTargetInfoMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m PublishTargetInfoMultiError) AllErrors() []error { return m }

// PublishTargetInfoValidationError is the validation error returned by
// PublishTargetInfo.Validate if the designated constraints aren't met.
type PublishTargetInfoValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e PublishTargetInfoValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e PublishTargetInfoValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e PublishTargetInfoValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e PublishTargetInfoValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e PublishTargetInfoValidationError) ErrorName() string {
	return "PublishTargetInfoValidationError"
}

// Error satisfies the builtin error interface
func (e PublishTargetInfoValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sPublishTargetInfo.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = PublishTargetInfoValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = PublishTargetInfoValidationError{}

// Validate checks the field values on ClientLimit with the rules defined in
// the proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *ClientLimit) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on ClientLimit with the rules defined in
// the proto definition for this message. If any rules are violated, the
// result is a list of violation errors wrapped in ClientLimitMultiError, or
// nil if none found.
func (m *ClientLimit) ValidateAll() error {
	return m.validate(true)
}

func (m *ClientLimit) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for LimitType

	// no validation rules for ClientId

	// no validation rules for VersionCompare

	// no validation rules for ClientVersion

	if len(errors) > 0 {
		return ClientLimitMultiError(errors)
	}

	return nil
}

// ClientLimitMultiError is an error wrapping multiple validation errors
// returned by ClientLimit.ValidateAll() if the designated constraints aren't met.
type ClientLimitMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m ClientLimitMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m ClientLimitMultiError) AllErrors() []error { return m }

// ClientLimitValidationError is the validation error returned by
// ClientLimit.Validate if the designated constraints aren't met.
type ClientLimitValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e ClientLimitValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e ClientLimitValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e ClientLimitValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e ClientLimitValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e ClientLimitValidationError) ErrorName() string { return "ClientLimitValidationError" }

// Error satisfies the builtin error interface
func (e ClientLimitValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sClientLimit.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = ClientLimitValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = ClientLimitValidationError{}

// Validate checks the field values on PublishMsgRsp with the rules defined in
// the proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *PublishMsgRsp) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on PublishMsgRsp with the rules defined
// in the proto definition for this message. If any rules are violated, the
// result is a list of violation errors wrapped in PublishMsgRspMultiError, or
// nil if none found.
func (m *PublishMsgRsp) ValidateAll() error {
	return m.validate(true)
}

func (m *PublishMsgRsp) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetBase()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, PublishMsgRspValidationError{
					field:  "Base",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, PublishMsgRspValidationError{
					field:  "Base",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetBase()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return PublishMsgRspValidationError{
				field:  "Base",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return PublishMsgRspMultiError(errors)
	}

	return nil
}

// PublishMsgRspMultiError is an error wrapping multiple validation errors
// returned by PublishMsgRsp.ValidateAll() if the designated constraints
// aren't met.
type PublishMsgRspMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m PublishMsgRspMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m PublishMsgRspMultiError) AllErrors() []error { return m }

// PublishMsgRspValidationError is the validation error returned by
// PublishMsgRsp.Validate if the designated constraints aren't met.
type PublishMsgRspValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e PublishMsgRspValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e PublishMsgRspValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e PublishMsgRspValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e PublishMsgRspValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e PublishMsgRspValidationError) ErrorName() string { return "PublishMsgRspValidationError" }

// Error satisfies the builtin error interface
func (e PublishMsgRspValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sPublishMsgRsp.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = PublishMsgRspValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = PublishMsgRspValidationError{}

// Validate checks the field values on SubscribeTopicReq with the rules defined
// in the proto definition for this message. If any rules are violated, the
// first error encountered is returned, or nil if there are no violations.
func (m *SubscribeTopicReq) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on SubscribeTopicReq with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// SubscribeTopicReqMultiError, or nil if none found.
func (m *SubscribeTopicReq) ValidateAll() error {
	return m.validate(true)
}

func (m *SubscribeTopicReq) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for PlatformId

	// no validation rules for TimeStamp

	// no validation rules for NonceStr

	// no validation rules for Sign

	// no validation rules for UserId

	// no validation rules for DeviceId

	// no validation rules for Topics

	if len(errors) > 0 {
		return SubscribeTopicReqMultiError(errors)
	}

	return nil
}

// SubscribeTopicReqMultiError is an error wrapping multiple validation errors
// returned by SubscribeTopicReq.ValidateAll() if the designated constraints
// aren't met.
type SubscribeTopicReqMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m SubscribeTopicReqMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m SubscribeTopicReqMultiError) AllErrors() []error { return m }

// SubscribeTopicReqValidationError is the validation error returned by
// SubscribeTopicReq.Validate if the designated constraints aren't met.
type SubscribeTopicReqValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e SubscribeTopicReqValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e SubscribeTopicReqValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e SubscribeTopicReqValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e SubscribeTopicReqValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e SubscribeTopicReqValidationError) ErrorName() string {
	return "SubscribeTopicReqValidationError"
}

// Error satisfies the builtin error interface
func (e SubscribeTopicReqValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sSubscribeTopicReq.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = SubscribeTopicReqValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = SubscribeTopicReqValidationError{}

// Validate checks the field values on SubscribeTopicRsp with the rules defined
// in the proto definition for this message. If any rules are violated, the
// first error encountered is returned, or nil if there are no violations.
func (m *SubscribeTopicRsp) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on SubscribeTopicRsp with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// SubscribeTopicRspMultiError, or nil if none found.
func (m *SubscribeTopicRsp) ValidateAll() error {
	return m.validate(true)
}

func (m *SubscribeTopicRsp) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetBase()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, SubscribeTopicRspValidationError{
					field:  "Base",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, SubscribeTopicRspValidationError{
					field:  "Base",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetBase()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return SubscribeTopicRspValidationError{
				field:  "Base",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return SubscribeTopicRspMultiError(errors)
	}

	return nil
}

// SubscribeTopicRspMultiError is an error wrapping multiple validation errors
// returned by SubscribeTopicRsp.ValidateAll() if the designated constraints
// aren't met.
type SubscribeTopicRspMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m SubscribeTopicRspMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m SubscribeTopicRspMultiError) AllErrors() []error { return m }

// SubscribeTopicRspValidationError is the validation error returned by
// SubscribeTopicRsp.Validate if the designated constraints aren't met.
type SubscribeTopicRspValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e SubscribeTopicRspValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e SubscribeTopicRspValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e SubscribeTopicRspValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e SubscribeTopicRspValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e SubscribeTopicRspValidationError) ErrorName() string {
	return "SubscribeTopicRspValidationError"
}

// Error satisfies the builtin error interface
func (e SubscribeTopicRspValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sSubscribeTopicRsp.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = SubscribeTopicRspValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = SubscribeTopicRspValidationError{}

// Validate checks the field values on UnSubscribeTopicReq with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *UnSubscribeTopicReq) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on UnSubscribeTopicReq with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// UnSubscribeTopicReqMultiError, or nil if none found.
func (m *UnSubscribeTopicReq) ValidateAll() error {
	return m.validate(true)
}

func (m *UnSubscribeTopicReq) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for PlatformId

	// no validation rules for TimeStamp

	// no validation rules for NonceStr

	// no validation rules for Sign

	// no validation rules for UserId

	// no validation rules for DeviceId

	// no validation rules for Topics

	if len(errors) > 0 {
		return UnSubscribeTopicReqMultiError(errors)
	}

	return nil
}

// UnSubscribeTopicReqMultiError is an error wrapping multiple validation
// errors returned by UnSubscribeTopicReq.ValidateAll() if the designated
// constraints aren't met.
type UnSubscribeTopicReqMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m UnSubscribeTopicReqMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m UnSubscribeTopicReqMultiError) AllErrors() []error { return m }

// UnSubscribeTopicReqValidationError is the validation error returned by
// UnSubscribeTopicReq.Validate if the designated constraints aren't met.
type UnSubscribeTopicReqValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e UnSubscribeTopicReqValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e UnSubscribeTopicReqValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e UnSubscribeTopicReqValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e UnSubscribeTopicReqValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e UnSubscribeTopicReqValidationError) ErrorName() string {
	return "UnSubscribeTopicReqValidationError"
}

// Error satisfies the builtin error interface
func (e UnSubscribeTopicReqValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sUnSubscribeTopicReq.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = UnSubscribeTopicReqValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = UnSubscribeTopicReqValidationError{}

// Validate checks the field values on UnSubscribeTopicRsp with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *UnSubscribeTopicRsp) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on UnSubscribeTopicRsp with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// UnSubscribeTopicRspMultiError, or nil if none found.
func (m *UnSubscribeTopicRsp) ValidateAll() error {
	return m.validate(true)
}

func (m *UnSubscribeTopicRsp) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetBase()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, UnSubscribeTopicRspValidationError{
					field:  "Base",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, UnSubscribeTopicRspValidationError{
					field:  "Base",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetBase()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return UnSubscribeTopicRspValidationError{
				field:  "Base",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return UnSubscribeTopicRspMultiError(errors)
	}

	return nil
}

// UnSubscribeTopicRspMultiError is an error wrapping multiple validation
// errors returned by UnSubscribeTopicRsp.ValidateAll() if the designated
// constraints aren't met.
type UnSubscribeTopicRspMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m UnSubscribeTopicRspMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m UnSubscribeTopicRspMultiError) AllErrors() []error { return m }

// UnSubscribeTopicRspValidationError is the validation error returned by
// UnSubscribeTopicRsp.Validate if the designated constraints aren't met.
type UnSubscribeTopicRspValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e UnSubscribeTopicRspValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e UnSubscribeTopicRspValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e UnSubscribeTopicRspValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e UnSubscribeTopicRspValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e UnSubscribeTopicRspValidationError) ErrorName() string {
	return "UnSubscribeTopicRspValidationError"
}

// Error satisfies the builtin error interface
func (e UnSubscribeTopicRspValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sUnSubscribeTopicRsp.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = UnSubscribeTopicRspValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = UnSubscribeTopicRspValidationError{}

// Validate checks the field values on UsersTopicsReq with the rules defined in
// the proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *UsersTopicsReq) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on UsersTopicsReq with the rules defined
// in the proto definition for this message. If any rules are violated, the
// result is a list of violation errors wrapped in UsersTopicsReqMultiError,
// or nil if none found.
func (m *UsersTopicsReq) ValidateAll() error {
	return m.validate(true)
}

func (m *UsersTopicsReq) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for PlatformId

	// no validation rules for TimeStamp

	// no validation rules for NonceStr

	// no validation rules for Sign

	// no validation rules for UserDevices

	if len(errors) > 0 {
		return UsersTopicsReqMultiError(errors)
	}

	return nil
}

// UsersTopicsReqMultiError is an error wrapping multiple validation errors
// returned by UsersTopicsReq.ValidateAll() if the designated constraints
// aren't met.
type UsersTopicsReqMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m UsersTopicsReqMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m UsersTopicsReqMultiError) AllErrors() []error { return m }

// UsersTopicsReqValidationError is the validation error returned by
// UsersTopicsReq.Validate if the designated constraints aren't met.
type UsersTopicsReqValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e UsersTopicsReqValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e UsersTopicsReqValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e UsersTopicsReqValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e UsersTopicsReqValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e UsersTopicsReqValidationError) ErrorName() string { return "UsersTopicsReqValidationError" }

// Error satisfies the builtin error interface
func (e UsersTopicsReqValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sUsersTopicsReq.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = UsersTopicsReqValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = UsersTopicsReqValidationError{}

// Validate checks the field values on UserDevice with the rules defined in the
// proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *UserDevice) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on UserDevice with the rules defined in
// the proto definition for this message. If any rules are violated, the
// result is a list of violation errors wrapped in UserDeviceMultiError, or
// nil if none found.
func (m *UserDevice) ValidateAll() error {
	return m.validate(true)
}

func (m *UserDevice) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for UserId

	// no validation rules for DeviceId

	if len(errors) > 0 {
		return UserDeviceMultiError(errors)
	}

	return nil
}

// UserDeviceMultiError is an error wrapping multiple validation errors
// returned by UserDevice.ValidateAll() if the designated constraints aren't met.
type UserDeviceMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m UserDeviceMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m UserDeviceMultiError) AllErrors() []error { return m }

// UserDeviceValidationError is the validation error returned by
// UserDevice.Validate if the designated constraints aren't met.
type UserDeviceValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e UserDeviceValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e UserDeviceValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e UserDeviceValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e UserDeviceValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e UserDeviceValidationError) ErrorName() string { return "UserDeviceValidationError" }

// Error satisfies the builtin error interface
func (e UserDeviceValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sUserDevice.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = UserDeviceValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = UserDeviceValidationError{}

// Validate checks the field values on UsersTopicsRsp with the rules defined in
// the proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *UsersTopicsRsp) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on UsersTopicsRsp with the rules defined
// in the proto definition for this message. If any rules are violated, the
// result is a list of violation errors wrapped in UsersTopicsRspMultiError,
// or nil if none found.
func (m *UsersTopicsRsp) ValidateAll() error {
	return m.validate(true)
}

func (m *UsersTopicsRsp) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetBase()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, UsersTopicsRspValidationError{
					field:  "Base",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, UsersTopicsRspValidationError{
					field:  "Base",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetBase()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return UsersTopicsRspValidationError{
				field:  "Base",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetData()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, UsersTopicsRspValidationError{
					field:  "Data",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, UsersTopicsRspValidationError{
					field:  "Data",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetData()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return UsersTopicsRspValidationError{
				field:  "Data",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return UsersTopicsRspMultiError(errors)
	}

	return nil
}

// UsersTopicsRspMultiError is an error wrapping multiple validation errors
// returned by UsersTopicsRsp.ValidateAll() if the designated constraints
// aren't met.
type UsersTopicsRspMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m UsersTopicsRspMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m UsersTopicsRspMultiError) AllErrors() []error { return m }

// UsersTopicsRspValidationError is the validation error returned by
// UsersTopicsRsp.Validate if the designated constraints aren't met.
type UsersTopicsRspValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e UsersTopicsRspValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e UsersTopicsRspValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e UsersTopicsRspValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e UsersTopicsRspValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e UsersTopicsRspValidationError) ErrorName() string { return "UsersTopicsRspValidationError" }

// Error satisfies the builtin error interface
func (e UsersTopicsRspValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sUsersTopicsRsp.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = UsersTopicsRspValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = UsersTopicsRspValidationError{}

// Validate checks the field values on UsersTopicsData with the rules defined
// in the proto definition for this message. If any rules are violated, the
// first error encountered is returned, or nil if there are no violations.
func (m *UsersTopicsData) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on UsersTopicsData with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// UsersTopicsDataMultiError, or nil if none found.
func (m *UsersTopicsData) ValidateAll() error {
	return m.validate(true)
}

func (m *UsersTopicsData) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	{
		sorted_keys := make([]string, len(m.GetUserTopics()))
		i := 0
		for key := range m.GetUserTopics() {
			sorted_keys[i] = key
			i++
		}
		sort.Slice(sorted_keys, func(i, j int) bool { return sorted_keys[i] < sorted_keys[j] })
		for _, key := range sorted_keys {
			val := m.GetUserTopics()[key]
			_ = val

			// no validation rules for UserTopics[key]

			if all {
				switch v := interface{}(val).(type) {
				case interface{ ValidateAll() error }:
					if err := v.ValidateAll(); err != nil {
						errors = append(errors, UsersTopicsDataValidationError{
							field:  fmt.Sprintf("UserTopics[%v]", key),
							reason: "embedded message failed validation",
							cause:  err,
						})
					}
				case interface{ Validate() error }:
					if err := v.Validate(); err != nil {
						errors = append(errors, UsersTopicsDataValidationError{
							field:  fmt.Sprintf("UserTopics[%v]", key),
							reason: "embedded message failed validation",
							cause:  err,
						})
					}
				}
			} else if v, ok := interface{}(val).(interface{ Validate() error }); ok {
				if err := v.Validate(); err != nil {
					return UsersTopicsDataValidationError{
						field:  fmt.Sprintf("UserTopics[%v]", key),
						reason: "embedded message failed validation",
						cause:  err,
					}
				}
			}

		}
	}

	if len(errors) > 0 {
		return UsersTopicsDataMultiError(errors)
	}

	return nil
}

// UsersTopicsDataMultiError is an error wrapping multiple validation errors
// returned by UsersTopicsData.ValidateAll() if the designated constraints
// aren't met.
type UsersTopicsDataMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m UsersTopicsDataMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m UsersTopicsDataMultiError) AllErrors() []error { return m }

// UsersTopicsDataValidationError is the validation error returned by
// UsersTopicsData.Validate if the designated constraints aren't met.
type UsersTopicsDataValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e UsersTopicsDataValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e UsersTopicsDataValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e UsersTopicsDataValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e UsersTopicsDataValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e UsersTopicsDataValidationError) ErrorName() string { return "UsersTopicsDataValidationError" }

// Error satisfies the builtin error interface
func (e UsersTopicsDataValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sUsersTopicsData.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = UsersTopicsDataValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = UsersTopicsDataValidationError{}
