// Code generated by protoc-gen-validate. DO NOT EDIT.
// source: bizactivity.proto

package bizactivity

import (
	"bytes"
	"errors"
	"fmt"
	"net"
	"net/mail"
	"net/url"
	"regexp"
	"sort"
	"strings"
	"time"
	"unicode/utf8"

	"google.golang.org/protobuf/types/known/anypb"
)

// ensure the imports are used
var (
	_ = bytes.MinRead
	_ = errors.New("")
	_ = fmt.Print
	_ = utf8.UTFMax
	_ = (*regexp.Regexp)(nil)
	_ = (*strings.Reader)(nil)
	_ = net.IPv4len
	_ = time.Duration(0)
	_ = (*url.URL)(nil)
	_ = (*mail.Address)(nil)
	_ = anypb.Any{}
	_ = sort.Sort
)

// Validate checks the field values on DailyCheckInReq with the rules defined
// in the proto definition for this message. If any rules are violated, the
// first error encountered is returned, or nil if there are no violations.
func (m *DailyCheckInReq) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on DailyCheckInReq with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// DailyCheckInReqMultiError, or nil if none found.
func (m *DailyCheckInReq) ValidateAll() error {
	return m.validate(true)
}

func (m *DailyCheckInReq) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if len(errors) > 0 {
		return DailyCheckInReqMultiError(errors)
	}

	return nil
}

// DailyCheckInReqMultiError is an error wrapping multiple validation errors
// returned by DailyCheckInReq.ValidateAll() if the designated constraints
// aren't met.
type DailyCheckInReqMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m DailyCheckInReqMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m DailyCheckInReqMultiError) AllErrors() []error { return m }

// DailyCheckInReqValidationError is the validation error returned by
// DailyCheckInReq.Validate if the designated constraints aren't met.
type DailyCheckInReqValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e DailyCheckInReqValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e DailyCheckInReqValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e DailyCheckInReqValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e DailyCheckInReqValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e DailyCheckInReqValidationError) ErrorName() string { return "DailyCheckInReqValidationError" }

// Error satisfies the builtin error interface
func (e DailyCheckInReqValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sDailyCheckInReq.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = DailyCheckInReqValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = DailyCheckInReqValidationError{}

// Validate checks the field values on DailyCheckInResp with the rules defined
// in the proto definition for this message. If any rules are violated, the
// first error encountered is returned, or nil if there are no violations.
func (m *DailyCheckInResp) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on DailyCheckInResp with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// DailyCheckInRespMultiError, or nil if none found.
func (m *DailyCheckInResp) ValidateAll() error {
	return m.validate(true)
}

func (m *DailyCheckInResp) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Code

	// no validation rules for Msg

	if all {
		switch v := interface{}(m.GetData()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, DailyCheckInRespValidationError{
					field:  "Data",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, DailyCheckInRespValidationError{
					field:  "Data",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetData()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return DailyCheckInRespValidationError{
				field:  "Data",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return DailyCheckInRespMultiError(errors)
	}

	return nil
}

// DailyCheckInRespMultiError is an error wrapping multiple validation errors
// returned by DailyCheckInResp.ValidateAll() if the designated constraints
// aren't met.
type DailyCheckInRespMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m DailyCheckInRespMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m DailyCheckInRespMultiError) AllErrors() []error { return m }

// DailyCheckInRespValidationError is the validation error returned by
// DailyCheckInResp.Validate if the designated constraints aren't met.
type DailyCheckInRespValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e DailyCheckInRespValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e DailyCheckInRespValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e DailyCheckInRespValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e DailyCheckInRespValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e DailyCheckInRespValidationError) ErrorName() string { return "DailyCheckInRespValidationError" }

// Error satisfies the builtin error interface
func (e DailyCheckInRespValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sDailyCheckInResp.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = DailyCheckInRespValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = DailyCheckInRespValidationError{}
