// Code generated by protoc-gen-go-grpc. DO NOT EDIT.

package bizactivity

import (
	context "context"
	grpc "google.golang.org/grpc"
	codes "google.golang.org/grpc/codes"
	status "google.golang.org/grpc/status"
)

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
// Requires gRPC-Go v1.32.0 or later.
const _ = grpc.SupportPackageIsVersion7

// SClient is the client API for S service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://pkg.go.dev/google.golang.org/grpc/?tab=doc#ClientConn.NewStream.
type SClient interface {
	// 每日签到（获取状态并签到）
	DailyCheckIn(ctx context.Context, in *DailyCheckInReq, opts ...grpc.CallOption) (*DailyCheckInResp, error)
}

type sClient struct {
	cc grpc.ClientConnInterface
}

func NewSClient(cc grpc.ClientConnInterface) SClient {
	return &sClient{cc}
}

func (c *sClient) DailyCheckIn(ctx context.Context, in *DailyCheckInReq, opts ...grpc.CallOption) (*DailyCheckInResp, error) {
	out := new(DailyCheckInResp)
	err := c.cc.Invoke(ctx, "/vc.bizactivity.s/DailyCheckIn", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// SServer is the server API for S service.
// All implementations should embed UnimplementedSServer
// for forward compatibility
type SServer interface {
	// 每日签到（获取状态并签到）
	DailyCheckIn(context.Context, *DailyCheckInReq) (*DailyCheckInResp, error)
}

// UnimplementedSServer should be embedded to have forward compatible implementations.
type UnimplementedSServer struct {
}

func (UnimplementedSServer) DailyCheckIn(context.Context, *DailyCheckInReq) (*DailyCheckInResp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method DailyCheckIn not implemented")
}

// UnsafeSServer may be embedded to opt out of forward compatibility for this service.
// Use of this interface is not recommended, as added methods to SServer will
// result in compilation errors.
type UnsafeSServer interface {
	mustEmbedUnimplementedSServer()
}

func RegisterSServer(s grpc.ServiceRegistrar, srv SServer) {
	s.RegisterService(&S_ServiceDesc, srv)
}

func _S_DailyCheckIn_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(DailyCheckInReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(SServer).DailyCheckIn(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/vc.bizactivity.s/DailyCheckIn",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(SServer).DailyCheckIn(ctx, req.(*DailyCheckInReq))
	}
	return interceptor(ctx, in, info, handler)
}

// S_ServiceDesc is the grpc.ServiceDesc for S service.
// It's only intended for direct use with grpc.RegisterService,
// and not to be introspected or modified (even as a copy)
var S_ServiceDesc = grpc.ServiceDesc{
	ServiceName: "vc.bizactivity.s",
	HandlerType: (*SServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "DailyCheckIn",
			Handler:    _S_DailyCheckIn_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "bizactivity.proto",
}
