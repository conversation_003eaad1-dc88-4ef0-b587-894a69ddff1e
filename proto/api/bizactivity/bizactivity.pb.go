// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.33.0
// 	protoc        v5.29.3
// source: bizactivity.proto

package bizactivity

import (
	_ "google.golang.org/genproto/googleapis/api/annotations"
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	svcactivity "new-gitlab.xunlei.cn/vcproject/backends/proto/api/svcactivity"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type DailyCheckInReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields
}

func (x *DailyCheckInReq) Reset() {
	*x = DailyCheckInReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_bizactivity_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *DailyCheckInReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DailyCheckInReq) ProtoMessage() {}

func (x *DailyCheckInReq) ProtoReflect() protoreflect.Message {
	mi := &file_bizactivity_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DailyCheckInReq.ProtoReflect.Descriptor instead.
func (*DailyCheckInReq) Descriptor() ([]byte, []int) {
	return file_bizactivity_proto_rawDescGZIP(), []int{0}
}

type DailyCheckInResp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Code int32                             `protobuf:"varint,1,opt,name=code,proto3" json:"code,omitempty"`
	Msg  string                            `protobuf:"bytes,2,opt,name=msg,proto3" json:"msg,omitempty"`
	Data *svcactivity.DailyCheckInRespData `protobuf:"bytes,3,opt,name=data,proto3" json:"data,omitempty"`
}

func (x *DailyCheckInResp) Reset() {
	*x = DailyCheckInResp{}
	if protoimpl.UnsafeEnabled {
		mi := &file_bizactivity_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *DailyCheckInResp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DailyCheckInResp) ProtoMessage() {}

func (x *DailyCheckInResp) ProtoReflect() protoreflect.Message {
	mi := &file_bizactivity_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DailyCheckInResp.ProtoReflect.Descriptor instead.
func (*DailyCheckInResp) Descriptor() ([]byte, []int) {
	return file_bizactivity_proto_rawDescGZIP(), []int{1}
}

func (x *DailyCheckInResp) GetCode() int32 {
	if x != nil {
		return x.Code
	}
	return 0
}

func (x *DailyCheckInResp) GetMsg() string {
	if x != nil {
		return x.Msg
	}
	return ""
}

func (x *DailyCheckInResp) GetData() *svcactivity.DailyCheckInRespData {
	if x != nil {
		return x.Data
	}
	return nil
}

var File_bizactivity_proto protoreflect.FileDescriptor

var file_bizactivity_proto_rawDesc = []byte{
	0x0a, 0x11, 0x62, 0x69, 0x7a, 0x61, 0x63, 0x74, 0x69, 0x76, 0x69, 0x74, 0x79, 0x2e, 0x70, 0x72,
	0x6f, 0x74, 0x6f, 0x12, 0x0e, 0x76, 0x63, 0x2e, 0x62, 0x69, 0x7a, 0x61, 0x63, 0x74, 0x69, 0x76,
	0x69, 0x74, 0x79, 0x1a, 0x1c, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2f, 0x61, 0x70, 0x69, 0x2f,
	0x61, 0x6e, 0x6e, 0x6f, 0x74, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x2e, 0x70, 0x72, 0x6f, 0x74,
	0x6f, 0x1a, 0x1d, 0x73, 0x76, 0x63, 0x61, 0x63, 0x74, 0x69, 0x76, 0x69, 0x74, 0x79, 0x2f, 0x73,
	0x76, 0x63, 0x61, 0x63, 0x74, 0x69, 0x76, 0x69, 0x74, 0x79, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f,
	0x22, 0x11, 0x0a, 0x0f, 0x44, 0x61, 0x69, 0x6c, 0x79, 0x43, 0x68, 0x65, 0x63, 0x6b, 0x49, 0x6e,
	0x52, 0x65, 0x71, 0x22, 0x72, 0x0a, 0x10, 0x44, 0x61, 0x69, 0x6c, 0x79, 0x43, 0x68, 0x65, 0x63,
	0x6b, 0x49, 0x6e, 0x52, 0x65, 0x73, 0x70, 0x12, 0x12, 0x0a, 0x04, 0x63, 0x6f, 0x64, 0x65, 0x18,
	0x01, 0x20, 0x01, 0x28, 0x05, 0x52, 0x04, 0x63, 0x6f, 0x64, 0x65, 0x12, 0x10, 0x0a, 0x03, 0x6d,
	0x73, 0x67, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x03, 0x6d, 0x73, 0x67, 0x12, 0x38, 0x0a,
	0x04, 0x64, 0x61, 0x74, 0x61, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x24, 0x2e, 0x76, 0x63,
	0x2e, 0x73, 0x76, 0x63, 0x61, 0x63, 0x74, 0x69, 0x76, 0x69, 0x74, 0x79, 0x2e, 0x44, 0x61, 0x69,
	0x6c, 0x79, 0x43, 0x68, 0x65, 0x63, 0x6b, 0x49, 0x6e, 0x52, 0x65, 0x73, 0x70, 0x44, 0x61, 0x74,
	0x61, 0x52, 0x04, 0x64, 0x61, 0x74, 0x61, 0x32, 0x87, 0x01, 0x0a, 0x01, 0x73, 0x12, 0x81, 0x01,
	0x0a, 0x0c, 0x44, 0x61, 0x69, 0x6c, 0x79, 0x43, 0x68, 0x65, 0x63, 0x6b, 0x49, 0x6e, 0x12, 0x1f,
	0x2e, 0x76, 0x63, 0x2e, 0x62, 0x69, 0x7a, 0x61, 0x63, 0x74, 0x69, 0x76, 0x69, 0x74, 0x79, 0x2e,
	0x44, 0x61, 0x69, 0x6c, 0x79, 0x43, 0x68, 0x65, 0x63, 0x6b, 0x49, 0x6e, 0x52, 0x65, 0x71, 0x1a,
	0x20, 0x2e, 0x76, 0x63, 0x2e, 0x62, 0x69, 0x7a, 0x61, 0x63, 0x74, 0x69, 0x76, 0x69, 0x74, 0x79,
	0x2e, 0x44, 0x61, 0x69, 0x6c, 0x79, 0x43, 0x68, 0x65, 0x63, 0x6b, 0x49, 0x6e, 0x52, 0x65, 0x73,
	0x70, 0x22, 0x2e, 0x82, 0xd3, 0xe4, 0x93, 0x02, 0x28, 0x3a, 0x01, 0x2a, 0x22, 0x23, 0x2f, 0x76,
	0x63, 0x2e, 0x62, 0x69, 0x7a, 0x61, 0x63, 0x74, 0x69, 0x76, 0x69, 0x74, 0x79, 0x2e, 0x73, 0x2f,
	0x76, 0x31, 0x2f, 0x64, 0x61, 0x69, 0x6c, 0x79, 0x5f, 0x63, 0x68, 0x65, 0x63, 0x6b, 0x5f, 0x69,
	0x6e, 0x42, 0x4b, 0x5a, 0x49, 0x6e, 0x65, 0x77, 0x2d, 0x67, 0x69, 0x74, 0x6c, 0x61, 0x62, 0x2e,
	0x78, 0x75, 0x6e, 0x6c, 0x65, 0x69, 0x2e, 0x63, 0x6e, 0x2f, 0x76, 0x63, 0x70, 0x72, 0x6f, 0x6a,
	0x65, 0x63, 0x74, 0x2f, 0x62, 0x61, 0x63, 0x6b, 0x65, 0x6e, 0x64, 0x73, 0x2f, 0x70, 0x72, 0x6f,
	0x74, 0x6f, 0x2f, 0x61, 0x70, 0x69, 0x2f, 0x62, 0x69, 0x7a, 0x61, 0x63, 0x74, 0x69, 0x76, 0x69,
	0x74, 0x79, 0x3b, 0x62, 0x69, 0x7a, 0x61, 0x63, 0x74, 0x69, 0x76, 0x69, 0x74, 0x79, 0x62, 0x06,
	0x70, 0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_bizactivity_proto_rawDescOnce sync.Once
	file_bizactivity_proto_rawDescData = file_bizactivity_proto_rawDesc
)

func file_bizactivity_proto_rawDescGZIP() []byte {
	file_bizactivity_proto_rawDescOnce.Do(func() {
		file_bizactivity_proto_rawDescData = protoimpl.X.CompressGZIP(file_bizactivity_proto_rawDescData)
	})
	return file_bizactivity_proto_rawDescData
}

var file_bizactivity_proto_msgTypes = make([]protoimpl.MessageInfo, 2)
var file_bizactivity_proto_goTypes = []interface{}{
	(*DailyCheckInReq)(nil),                  // 0: vc.bizactivity.DailyCheckInReq
	(*DailyCheckInResp)(nil),                 // 1: vc.bizactivity.DailyCheckInResp
	(*svcactivity.DailyCheckInRespData)(nil), // 2: vc.svcactivity.DailyCheckInRespData
}
var file_bizactivity_proto_depIdxs = []int32{
	2, // 0: vc.bizactivity.DailyCheckInResp.data:type_name -> vc.svcactivity.DailyCheckInRespData
	0, // 1: vc.bizactivity.s.DailyCheckIn:input_type -> vc.bizactivity.DailyCheckInReq
	1, // 2: vc.bizactivity.s.DailyCheckIn:output_type -> vc.bizactivity.DailyCheckInResp
	2, // [2:3] is the sub-list for method output_type
	1, // [1:2] is the sub-list for method input_type
	1, // [1:1] is the sub-list for extension type_name
	1, // [1:1] is the sub-list for extension extendee
	0, // [0:1] is the sub-list for field type_name
}

func init() { file_bizactivity_proto_init() }
func file_bizactivity_proto_init() {
	if File_bizactivity_proto != nil {
		return
	}
	if !protoimpl.UnsafeEnabled {
		file_bizactivity_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*DailyCheckInReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_bizactivity_proto_msgTypes[1].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*DailyCheckInResp); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_bizactivity_proto_rawDesc,
			NumEnums:      0,
			NumMessages:   2,
			NumExtensions: 0,
			NumServices:   1,
		},
		GoTypes:           file_bizactivity_proto_goTypes,
		DependencyIndexes: file_bizactivity_proto_depIdxs,
		MessageInfos:      file_bizactivity_proto_msgTypes,
	}.Build()
	File_bizactivity_proto = out.File
	file_bizactivity_proto_rawDesc = nil
	file_bizactivity_proto_goTypes = nil
	file_bizactivity_proto_depIdxs = nil
}
