syntax = "proto3";

package vc.bizactivity;
option go_package = "new-gitlab.xunlei.cn/vcproject/backends/proto/api/bizactivity;bizactivity";

import "google/api/annotations.proto";
import "svcactivity/svcactivity.proto";

message DailyCheckInReq {
}

message DailyCheckInResp {
    int32 code =1;
    string msg=2;
    svcactivity.DailyCheckInRespData data=3;
}

service s {
    // 每日签到（获取状态并签到）
    rpc DailyCheckIn(DailyCheckInReq) returns (DailyCheckInResp) {
        option (google.api.http) = {
            post: "/vc.bizactivity.s/v1/daily_check_in"
            body: "*"
        };
    }
}