// Code generated by protoc-gen-go-grpc. DO NOT EDIT.

package bizscript

import (
	context "context"
	grpc "google.golang.org/grpc"
	codes "google.golang.org/grpc/codes"
	status "google.golang.org/grpc/status"
)

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
// Requires gRPC-Go v1.32.0 or later.
const _ = grpc.SupportPackageIsVersion7

// SClient is the client API for S service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://pkg.go.dev/google.golang.org/grpc/?tab=doc#ClientConn.NewStream.
type SClient interface {
	// 角色列表
	GetCharacterList(ctx context.Context, in *GetCharacterListReq, opts ...grpc.CallOption) (*GetCharacterListResp, error)
	// 查询角色
	SearchCharacters(ctx context.Context, in *SearchCharactersReq, opts ...grpc.CallOption) (*SearchCharactersResp, error)
	// 设置用户与角色关联
	SetUserCharacter(ctx context.Context, in *SetUserCharacterReq, opts ...grpc.CallOption) (*SetUserCharacterResp, error)
	// 获取用户角色列表
	GetUserCharacters(ctx context.Context, in *GetUserCharactersReq, opts ...grpc.CallOption) (*GetUserCharactersResp, error)
	// 批量检查角色可用性
	BatchCheckCharacterAvailability(ctx context.Context, in *BatchCheckCharacterAvailabilityReq, opts ...grpc.CallOption) (*BatchCheckCharacterAvailabilityResp, error)
	// 获取话题列表
	GetTopicList(ctx context.Context, in *GetTopicListReq, opts ...grpc.CallOption) (*GetTopicListResp, error)
	// 创建剧本
	CreateScript(ctx context.Context, in *CreateScriptReq, opts ...grpc.CallOption) (*CreateScriptResp, error)
	// 删除剧本
	DeleteScript(ctx context.Context, in *DeleteScriptReq, opts ...grpc.CallOption) (*DeleteScriptResp, error)
	// 获取剧本列表/话题聚合页
	GetScriptList(ctx context.Context, in *GetScriptListReq, opts ...grpc.CallOption) (*GetScriptListResp, error)
	// 获取剧本详情
	GetScriptDetail(ctx context.Context, in *GetScriptDetailReq, opts ...grpc.CallOption) (*GetScriptDetailResp, error)
	// 搜索
	Search(ctx context.Context, in *SearchReq, opts ...grpc.CallOption) (*SearchResp, error)
	// 分享剧本
	ShareScript(ctx context.Context, in *ShareScriptReq, opts ...grpc.CallOption) (*ShareScriptResp, error)
	// 获取台词配音全部列表
	GetLineDubbingAll(ctx context.Context, in *GetLineDubbingAllReq, opts ...grpc.CallOption) (*GetLineDubbingAllResp, error)
	// 获取台词配音简略列表
	GetLineDubbingSimple(ctx context.Context, in *GetLineDubbingSimpleReq, opts ...grpc.CallOption) (*GetLineDubbingSimpleResp, error)
	// 获取剧本所有台词的第一条配音请求
	GetScriptFirstDubbing(ctx context.Context, in *GetScriptFirstDubbingReq, opts ...grpc.CallOption) (*GetScriptFirstDubbingResp, error)
	// 批量获取剧本所有台词的第一条配音请求
	BatchGetScriptFirstDubbing(ctx context.Context, in *BatchGetScriptFirstDubbingReq, opts ...grpc.CallOption) (*BatchGetScriptFirstDubbingResp, error)
	// 配音
	CreateDubbing(ctx context.Context, in *CreateDubbingReq, opts ...grpc.CallOption) (*CreateDubbingResp, error)
	// 删除配音记录
	DeleteDubbingRecord(ctx context.Context, in *DeleteDubbingRecordReq, opts ...grpc.CallOption) (*DeleteDubbingRecordResp, error)
	// 删除配音
	DeleteDubbing(ctx context.Context, in *DeleteDubbingReq, opts ...grpc.CallOption) (*DeleteDubbingResp, error)
	// 设置配音置顶状态
	SetDubbingTop(ctx context.Context, in *SetDubbingTopReq, opts ...grpc.CallOption) (*SetDubbingTopResp, error)
	// 获取评论列表
	GetCommentList(ctx context.Context, in *GetCommentListReq, opts ...grpc.CallOption) (*GetCommentListResp, error)
	// 获取评论回复列表
	GetCommentReplies(ctx context.Context, in *GetCommentRepliesReq, opts ...grpc.CallOption) (*GetCommentRepliesResp, error)
	// 评论/回复
	CreateComment(ctx context.Context, in *CreateCommentReq, opts ...grpc.CallOption) (*CreateCommentResp, error)
	// 设置评论置顶状态
	SetCommentTop(ctx context.Context, in *SetCommentTopReq, opts ...grpc.CallOption) (*SetCommentTopResp, error)
	// 删除评论
	DeleteComment(ctx context.Context, in *DeleteCommentReq, opts ...grpc.CallOption) (*DeleteCommentResp, error)
	// 获取评论ASR
	GetCommentAsr(ctx context.Context, in *GetCommentASRReq, opts ...grpc.CallOption) (*GetCommentASRResp, error)
	// 点赞
	Like(ctx context.Context, in *LikeReq, opts ...grpc.CallOption) (*LikeResp, error)
	// 取消点赞
	Unlike(ctx context.Context, in *UnlikeReq, opts ...grpc.CallOption) (*UnlikeResp, error)
	// 批量获取点赞数
	BatchGetLikes(ctx context.Context, in *BatchGetLikesReq, opts ...grpc.CallOption) (*BatchGetLikesResp, error)
	// 获取举报原因列表
	GetReportReasons(ctx context.Context, in *GetReportReasonsReq, opts ...grpc.CallOption) (*GetReportReasonsResp, error)
	// 举报
	Report(ctx context.Context, in *ReportReq, opts ...grpc.CallOption) (*ReportResp, error)
	// 获取IP列表
	GetIPList(ctx context.Context, in *GetIPListReq, opts ...grpc.CallOption) (*GetIPListResp, error)
	// 获取用户统计数据
	GetUserStats(ctx context.Context, in *GetUserStatsReq, opts ...grpc.CallOption) (*GetUserStatsResp, error)
	// 获取用户剧本列表
	GetUserScriptLists(ctx context.Context, in *GetUserScriptListsReq, opts ...grpc.CallOption) (*GetUserScriptListsResp, error)
	// 获取封面列表
	GetCoverList(ctx context.Context, in *GetCoverListReq, opts ...grpc.CallOption) (*GetCoverListResp, error)
	// 生成剧本台词
	GenerateScriptLines(ctx context.Context, in *GenerateScriptLinesReq, opts ...grpc.CallOption) (*GenerateScriptLinesResp, error)
}

type sClient struct {
	cc grpc.ClientConnInterface
}

func NewSClient(cc grpc.ClientConnInterface) SClient {
	return &sClient{cc}
}

func (c *sClient) GetCharacterList(ctx context.Context, in *GetCharacterListReq, opts ...grpc.CallOption) (*GetCharacterListResp, error) {
	out := new(GetCharacterListResp)
	err := c.cc.Invoke(ctx, "/vc.bizscript.s/GetCharacterList", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *sClient) SearchCharacters(ctx context.Context, in *SearchCharactersReq, opts ...grpc.CallOption) (*SearchCharactersResp, error) {
	out := new(SearchCharactersResp)
	err := c.cc.Invoke(ctx, "/vc.bizscript.s/SearchCharacters", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *sClient) SetUserCharacter(ctx context.Context, in *SetUserCharacterReq, opts ...grpc.CallOption) (*SetUserCharacterResp, error) {
	out := new(SetUserCharacterResp)
	err := c.cc.Invoke(ctx, "/vc.bizscript.s/SetUserCharacter", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *sClient) GetUserCharacters(ctx context.Context, in *GetUserCharactersReq, opts ...grpc.CallOption) (*GetUserCharactersResp, error) {
	out := new(GetUserCharactersResp)
	err := c.cc.Invoke(ctx, "/vc.bizscript.s/GetUserCharacters", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *sClient) BatchCheckCharacterAvailability(ctx context.Context, in *BatchCheckCharacterAvailabilityReq, opts ...grpc.CallOption) (*BatchCheckCharacterAvailabilityResp, error) {
	out := new(BatchCheckCharacterAvailabilityResp)
	err := c.cc.Invoke(ctx, "/vc.bizscript.s/BatchCheckCharacterAvailability", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *sClient) GetTopicList(ctx context.Context, in *GetTopicListReq, opts ...grpc.CallOption) (*GetTopicListResp, error) {
	out := new(GetTopicListResp)
	err := c.cc.Invoke(ctx, "/vc.bizscript.s/GetTopicList", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *sClient) CreateScript(ctx context.Context, in *CreateScriptReq, opts ...grpc.CallOption) (*CreateScriptResp, error) {
	out := new(CreateScriptResp)
	err := c.cc.Invoke(ctx, "/vc.bizscript.s/CreateScript", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *sClient) DeleteScript(ctx context.Context, in *DeleteScriptReq, opts ...grpc.CallOption) (*DeleteScriptResp, error) {
	out := new(DeleteScriptResp)
	err := c.cc.Invoke(ctx, "/vc.bizscript.s/DeleteScript", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *sClient) GetScriptList(ctx context.Context, in *GetScriptListReq, opts ...grpc.CallOption) (*GetScriptListResp, error) {
	out := new(GetScriptListResp)
	err := c.cc.Invoke(ctx, "/vc.bizscript.s/GetScriptList", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *sClient) GetScriptDetail(ctx context.Context, in *GetScriptDetailReq, opts ...grpc.CallOption) (*GetScriptDetailResp, error) {
	out := new(GetScriptDetailResp)
	err := c.cc.Invoke(ctx, "/vc.bizscript.s/GetScriptDetail", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *sClient) Search(ctx context.Context, in *SearchReq, opts ...grpc.CallOption) (*SearchResp, error) {
	out := new(SearchResp)
	err := c.cc.Invoke(ctx, "/vc.bizscript.s/Search", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *sClient) ShareScript(ctx context.Context, in *ShareScriptReq, opts ...grpc.CallOption) (*ShareScriptResp, error) {
	out := new(ShareScriptResp)
	err := c.cc.Invoke(ctx, "/vc.bizscript.s/ShareScript", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *sClient) GetLineDubbingAll(ctx context.Context, in *GetLineDubbingAllReq, opts ...grpc.CallOption) (*GetLineDubbingAllResp, error) {
	out := new(GetLineDubbingAllResp)
	err := c.cc.Invoke(ctx, "/vc.bizscript.s/GetLineDubbingAll", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *sClient) GetLineDubbingSimple(ctx context.Context, in *GetLineDubbingSimpleReq, opts ...grpc.CallOption) (*GetLineDubbingSimpleResp, error) {
	out := new(GetLineDubbingSimpleResp)
	err := c.cc.Invoke(ctx, "/vc.bizscript.s/GetLineDubbingSimple", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *sClient) GetScriptFirstDubbing(ctx context.Context, in *GetScriptFirstDubbingReq, opts ...grpc.CallOption) (*GetScriptFirstDubbingResp, error) {
	out := new(GetScriptFirstDubbingResp)
	err := c.cc.Invoke(ctx, "/vc.bizscript.s/GetScriptFirstDubbing", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *sClient) BatchGetScriptFirstDubbing(ctx context.Context, in *BatchGetScriptFirstDubbingReq, opts ...grpc.CallOption) (*BatchGetScriptFirstDubbingResp, error) {
	out := new(BatchGetScriptFirstDubbingResp)
	err := c.cc.Invoke(ctx, "/vc.bizscript.s/BatchGetScriptFirstDubbing", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *sClient) CreateDubbing(ctx context.Context, in *CreateDubbingReq, opts ...grpc.CallOption) (*CreateDubbingResp, error) {
	out := new(CreateDubbingResp)
	err := c.cc.Invoke(ctx, "/vc.bizscript.s/CreateDubbing", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *sClient) DeleteDubbingRecord(ctx context.Context, in *DeleteDubbingRecordReq, opts ...grpc.CallOption) (*DeleteDubbingRecordResp, error) {
	out := new(DeleteDubbingRecordResp)
	err := c.cc.Invoke(ctx, "/vc.bizscript.s/DeleteDubbingRecord", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *sClient) DeleteDubbing(ctx context.Context, in *DeleteDubbingReq, opts ...grpc.CallOption) (*DeleteDubbingResp, error) {
	out := new(DeleteDubbingResp)
	err := c.cc.Invoke(ctx, "/vc.bizscript.s/DeleteDubbing", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *sClient) SetDubbingTop(ctx context.Context, in *SetDubbingTopReq, opts ...grpc.CallOption) (*SetDubbingTopResp, error) {
	out := new(SetDubbingTopResp)
	err := c.cc.Invoke(ctx, "/vc.bizscript.s/SetDubbingTop", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *sClient) GetCommentList(ctx context.Context, in *GetCommentListReq, opts ...grpc.CallOption) (*GetCommentListResp, error) {
	out := new(GetCommentListResp)
	err := c.cc.Invoke(ctx, "/vc.bizscript.s/GetCommentList", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *sClient) GetCommentReplies(ctx context.Context, in *GetCommentRepliesReq, opts ...grpc.CallOption) (*GetCommentRepliesResp, error) {
	out := new(GetCommentRepliesResp)
	err := c.cc.Invoke(ctx, "/vc.bizscript.s/GetCommentReplies", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *sClient) CreateComment(ctx context.Context, in *CreateCommentReq, opts ...grpc.CallOption) (*CreateCommentResp, error) {
	out := new(CreateCommentResp)
	err := c.cc.Invoke(ctx, "/vc.bizscript.s/CreateComment", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *sClient) SetCommentTop(ctx context.Context, in *SetCommentTopReq, opts ...grpc.CallOption) (*SetCommentTopResp, error) {
	out := new(SetCommentTopResp)
	err := c.cc.Invoke(ctx, "/vc.bizscript.s/SetCommentTop", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *sClient) DeleteComment(ctx context.Context, in *DeleteCommentReq, opts ...grpc.CallOption) (*DeleteCommentResp, error) {
	out := new(DeleteCommentResp)
	err := c.cc.Invoke(ctx, "/vc.bizscript.s/DeleteComment", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *sClient) GetCommentAsr(ctx context.Context, in *GetCommentASRReq, opts ...grpc.CallOption) (*GetCommentASRResp, error) {
	out := new(GetCommentASRResp)
	err := c.cc.Invoke(ctx, "/vc.bizscript.s/GetCommentAsr", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *sClient) Like(ctx context.Context, in *LikeReq, opts ...grpc.CallOption) (*LikeResp, error) {
	out := new(LikeResp)
	err := c.cc.Invoke(ctx, "/vc.bizscript.s/Like", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *sClient) Unlike(ctx context.Context, in *UnlikeReq, opts ...grpc.CallOption) (*UnlikeResp, error) {
	out := new(UnlikeResp)
	err := c.cc.Invoke(ctx, "/vc.bizscript.s/Unlike", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *sClient) BatchGetLikes(ctx context.Context, in *BatchGetLikesReq, opts ...grpc.CallOption) (*BatchGetLikesResp, error) {
	out := new(BatchGetLikesResp)
	err := c.cc.Invoke(ctx, "/vc.bizscript.s/BatchGetLikes", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *sClient) GetReportReasons(ctx context.Context, in *GetReportReasonsReq, opts ...grpc.CallOption) (*GetReportReasonsResp, error) {
	out := new(GetReportReasonsResp)
	err := c.cc.Invoke(ctx, "/vc.bizscript.s/GetReportReasons", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *sClient) Report(ctx context.Context, in *ReportReq, opts ...grpc.CallOption) (*ReportResp, error) {
	out := new(ReportResp)
	err := c.cc.Invoke(ctx, "/vc.bizscript.s/Report", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *sClient) GetIPList(ctx context.Context, in *GetIPListReq, opts ...grpc.CallOption) (*GetIPListResp, error) {
	out := new(GetIPListResp)
	err := c.cc.Invoke(ctx, "/vc.bizscript.s/GetIPList", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *sClient) GetUserStats(ctx context.Context, in *GetUserStatsReq, opts ...grpc.CallOption) (*GetUserStatsResp, error) {
	out := new(GetUserStatsResp)
	err := c.cc.Invoke(ctx, "/vc.bizscript.s/GetUserStats", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *sClient) GetUserScriptLists(ctx context.Context, in *GetUserScriptListsReq, opts ...grpc.CallOption) (*GetUserScriptListsResp, error) {
	out := new(GetUserScriptListsResp)
	err := c.cc.Invoke(ctx, "/vc.bizscript.s/GetUserScriptLists", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *sClient) GetCoverList(ctx context.Context, in *GetCoverListReq, opts ...grpc.CallOption) (*GetCoverListResp, error) {
	out := new(GetCoverListResp)
	err := c.cc.Invoke(ctx, "/vc.bizscript.s/GetCoverList", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *sClient) GenerateScriptLines(ctx context.Context, in *GenerateScriptLinesReq, opts ...grpc.CallOption) (*GenerateScriptLinesResp, error) {
	out := new(GenerateScriptLinesResp)
	err := c.cc.Invoke(ctx, "/vc.bizscript.s/GenerateScriptLines", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// SServer is the server API for S service.
// All implementations should embed UnimplementedSServer
// for forward compatibility
type SServer interface {
	// 角色列表
	GetCharacterList(context.Context, *GetCharacterListReq) (*GetCharacterListResp, error)
	// 查询角色
	SearchCharacters(context.Context, *SearchCharactersReq) (*SearchCharactersResp, error)
	// 设置用户与角色关联
	SetUserCharacter(context.Context, *SetUserCharacterReq) (*SetUserCharacterResp, error)
	// 获取用户角色列表
	GetUserCharacters(context.Context, *GetUserCharactersReq) (*GetUserCharactersResp, error)
	// 批量检查角色可用性
	BatchCheckCharacterAvailability(context.Context, *BatchCheckCharacterAvailabilityReq) (*BatchCheckCharacterAvailabilityResp, error)
	// 获取话题列表
	GetTopicList(context.Context, *GetTopicListReq) (*GetTopicListResp, error)
	// 创建剧本
	CreateScript(context.Context, *CreateScriptReq) (*CreateScriptResp, error)
	// 删除剧本
	DeleteScript(context.Context, *DeleteScriptReq) (*DeleteScriptResp, error)
	// 获取剧本列表/话题聚合页
	GetScriptList(context.Context, *GetScriptListReq) (*GetScriptListResp, error)
	// 获取剧本详情
	GetScriptDetail(context.Context, *GetScriptDetailReq) (*GetScriptDetailResp, error)
	// 搜索
	Search(context.Context, *SearchReq) (*SearchResp, error)
	// 分享剧本
	ShareScript(context.Context, *ShareScriptReq) (*ShareScriptResp, error)
	// 获取台词配音全部列表
	GetLineDubbingAll(context.Context, *GetLineDubbingAllReq) (*GetLineDubbingAllResp, error)
	// 获取台词配音简略列表
	GetLineDubbingSimple(context.Context, *GetLineDubbingSimpleReq) (*GetLineDubbingSimpleResp, error)
	// 获取剧本所有台词的第一条配音请求
	GetScriptFirstDubbing(context.Context, *GetScriptFirstDubbingReq) (*GetScriptFirstDubbingResp, error)
	// 批量获取剧本所有台词的第一条配音请求
	BatchGetScriptFirstDubbing(context.Context, *BatchGetScriptFirstDubbingReq) (*BatchGetScriptFirstDubbingResp, error)
	// 配音
	CreateDubbing(context.Context, *CreateDubbingReq) (*CreateDubbingResp, error)
	// 删除配音记录
	DeleteDubbingRecord(context.Context, *DeleteDubbingRecordReq) (*DeleteDubbingRecordResp, error)
	// 删除配音
	DeleteDubbing(context.Context, *DeleteDubbingReq) (*DeleteDubbingResp, error)
	// 设置配音置顶状态
	SetDubbingTop(context.Context, *SetDubbingTopReq) (*SetDubbingTopResp, error)
	// 获取评论列表
	GetCommentList(context.Context, *GetCommentListReq) (*GetCommentListResp, error)
	// 获取评论回复列表
	GetCommentReplies(context.Context, *GetCommentRepliesReq) (*GetCommentRepliesResp, error)
	// 评论/回复
	CreateComment(context.Context, *CreateCommentReq) (*CreateCommentResp, error)
	// 设置评论置顶状态
	SetCommentTop(context.Context, *SetCommentTopReq) (*SetCommentTopResp, error)
	// 删除评论
	DeleteComment(context.Context, *DeleteCommentReq) (*DeleteCommentResp, error)
	// 获取评论ASR
	GetCommentAsr(context.Context, *GetCommentASRReq) (*GetCommentASRResp, error)
	// 点赞
	Like(context.Context, *LikeReq) (*LikeResp, error)
	// 取消点赞
	Unlike(context.Context, *UnlikeReq) (*UnlikeResp, error)
	// 批量获取点赞数
	BatchGetLikes(context.Context, *BatchGetLikesReq) (*BatchGetLikesResp, error)
	// 获取举报原因列表
	GetReportReasons(context.Context, *GetReportReasonsReq) (*GetReportReasonsResp, error)
	// 举报
	Report(context.Context, *ReportReq) (*ReportResp, error)
	// 获取IP列表
	GetIPList(context.Context, *GetIPListReq) (*GetIPListResp, error)
	// 获取用户统计数据
	GetUserStats(context.Context, *GetUserStatsReq) (*GetUserStatsResp, error)
	// 获取用户剧本列表
	GetUserScriptLists(context.Context, *GetUserScriptListsReq) (*GetUserScriptListsResp, error)
	// 获取封面列表
	GetCoverList(context.Context, *GetCoverListReq) (*GetCoverListResp, error)
	// 生成剧本台词
	GenerateScriptLines(context.Context, *GenerateScriptLinesReq) (*GenerateScriptLinesResp, error)
}

// UnimplementedSServer should be embedded to have forward compatible implementations.
type UnimplementedSServer struct {
}

func (UnimplementedSServer) GetCharacterList(context.Context, *GetCharacterListReq) (*GetCharacterListResp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetCharacterList not implemented")
}
func (UnimplementedSServer) SearchCharacters(context.Context, *SearchCharactersReq) (*SearchCharactersResp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method SearchCharacters not implemented")
}
func (UnimplementedSServer) SetUserCharacter(context.Context, *SetUserCharacterReq) (*SetUserCharacterResp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method SetUserCharacter not implemented")
}
func (UnimplementedSServer) GetUserCharacters(context.Context, *GetUserCharactersReq) (*GetUserCharactersResp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetUserCharacters not implemented")
}
func (UnimplementedSServer) BatchCheckCharacterAvailability(context.Context, *BatchCheckCharacterAvailabilityReq) (*BatchCheckCharacterAvailabilityResp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method BatchCheckCharacterAvailability not implemented")
}
func (UnimplementedSServer) GetTopicList(context.Context, *GetTopicListReq) (*GetTopicListResp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetTopicList not implemented")
}
func (UnimplementedSServer) CreateScript(context.Context, *CreateScriptReq) (*CreateScriptResp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method CreateScript not implemented")
}
func (UnimplementedSServer) DeleteScript(context.Context, *DeleteScriptReq) (*DeleteScriptResp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method DeleteScript not implemented")
}
func (UnimplementedSServer) GetScriptList(context.Context, *GetScriptListReq) (*GetScriptListResp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetScriptList not implemented")
}
func (UnimplementedSServer) GetScriptDetail(context.Context, *GetScriptDetailReq) (*GetScriptDetailResp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetScriptDetail not implemented")
}
func (UnimplementedSServer) Search(context.Context, *SearchReq) (*SearchResp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method Search not implemented")
}
func (UnimplementedSServer) ShareScript(context.Context, *ShareScriptReq) (*ShareScriptResp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ShareScript not implemented")
}
func (UnimplementedSServer) GetLineDubbingAll(context.Context, *GetLineDubbingAllReq) (*GetLineDubbingAllResp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetLineDubbingAll not implemented")
}
func (UnimplementedSServer) GetLineDubbingSimple(context.Context, *GetLineDubbingSimpleReq) (*GetLineDubbingSimpleResp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetLineDubbingSimple not implemented")
}
func (UnimplementedSServer) GetScriptFirstDubbing(context.Context, *GetScriptFirstDubbingReq) (*GetScriptFirstDubbingResp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetScriptFirstDubbing not implemented")
}
func (UnimplementedSServer) BatchGetScriptFirstDubbing(context.Context, *BatchGetScriptFirstDubbingReq) (*BatchGetScriptFirstDubbingResp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method BatchGetScriptFirstDubbing not implemented")
}
func (UnimplementedSServer) CreateDubbing(context.Context, *CreateDubbingReq) (*CreateDubbingResp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method CreateDubbing not implemented")
}
func (UnimplementedSServer) DeleteDubbingRecord(context.Context, *DeleteDubbingRecordReq) (*DeleteDubbingRecordResp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method DeleteDubbingRecord not implemented")
}
func (UnimplementedSServer) DeleteDubbing(context.Context, *DeleteDubbingReq) (*DeleteDubbingResp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method DeleteDubbing not implemented")
}
func (UnimplementedSServer) SetDubbingTop(context.Context, *SetDubbingTopReq) (*SetDubbingTopResp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method SetDubbingTop not implemented")
}
func (UnimplementedSServer) GetCommentList(context.Context, *GetCommentListReq) (*GetCommentListResp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetCommentList not implemented")
}
func (UnimplementedSServer) GetCommentReplies(context.Context, *GetCommentRepliesReq) (*GetCommentRepliesResp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetCommentReplies not implemented")
}
func (UnimplementedSServer) CreateComment(context.Context, *CreateCommentReq) (*CreateCommentResp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method CreateComment not implemented")
}
func (UnimplementedSServer) SetCommentTop(context.Context, *SetCommentTopReq) (*SetCommentTopResp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method SetCommentTop not implemented")
}
func (UnimplementedSServer) DeleteComment(context.Context, *DeleteCommentReq) (*DeleteCommentResp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method DeleteComment not implemented")
}
func (UnimplementedSServer) GetCommentAsr(context.Context, *GetCommentASRReq) (*GetCommentASRResp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetCommentAsr not implemented")
}
func (UnimplementedSServer) Like(context.Context, *LikeReq) (*LikeResp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method Like not implemented")
}
func (UnimplementedSServer) Unlike(context.Context, *UnlikeReq) (*UnlikeResp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method Unlike not implemented")
}
func (UnimplementedSServer) BatchGetLikes(context.Context, *BatchGetLikesReq) (*BatchGetLikesResp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method BatchGetLikes not implemented")
}
func (UnimplementedSServer) GetReportReasons(context.Context, *GetReportReasonsReq) (*GetReportReasonsResp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetReportReasons not implemented")
}
func (UnimplementedSServer) Report(context.Context, *ReportReq) (*ReportResp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method Report not implemented")
}
func (UnimplementedSServer) GetIPList(context.Context, *GetIPListReq) (*GetIPListResp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetIPList not implemented")
}
func (UnimplementedSServer) GetUserStats(context.Context, *GetUserStatsReq) (*GetUserStatsResp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetUserStats not implemented")
}
func (UnimplementedSServer) GetUserScriptLists(context.Context, *GetUserScriptListsReq) (*GetUserScriptListsResp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetUserScriptLists not implemented")
}
func (UnimplementedSServer) GetCoverList(context.Context, *GetCoverListReq) (*GetCoverListResp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetCoverList not implemented")
}
func (UnimplementedSServer) GenerateScriptLines(context.Context, *GenerateScriptLinesReq) (*GenerateScriptLinesResp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GenerateScriptLines not implemented")
}

// UnsafeSServer may be embedded to opt out of forward compatibility for this service.
// Use of this interface is not recommended, as added methods to SServer will
// result in compilation errors.
type UnsafeSServer interface {
	mustEmbedUnimplementedSServer()
}

func RegisterSServer(s grpc.ServiceRegistrar, srv SServer) {
	s.RegisterService(&S_ServiceDesc, srv)
}

func _S_GetCharacterList_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetCharacterListReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(SServer).GetCharacterList(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/vc.bizscript.s/GetCharacterList",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(SServer).GetCharacterList(ctx, req.(*GetCharacterListReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _S_SearchCharacters_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(SearchCharactersReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(SServer).SearchCharacters(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/vc.bizscript.s/SearchCharacters",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(SServer).SearchCharacters(ctx, req.(*SearchCharactersReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _S_SetUserCharacter_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(SetUserCharacterReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(SServer).SetUserCharacter(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/vc.bizscript.s/SetUserCharacter",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(SServer).SetUserCharacter(ctx, req.(*SetUserCharacterReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _S_GetUserCharacters_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetUserCharactersReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(SServer).GetUserCharacters(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/vc.bizscript.s/GetUserCharacters",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(SServer).GetUserCharacters(ctx, req.(*GetUserCharactersReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _S_BatchCheckCharacterAvailability_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(BatchCheckCharacterAvailabilityReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(SServer).BatchCheckCharacterAvailability(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/vc.bizscript.s/BatchCheckCharacterAvailability",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(SServer).BatchCheckCharacterAvailability(ctx, req.(*BatchCheckCharacterAvailabilityReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _S_GetTopicList_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetTopicListReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(SServer).GetTopicList(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/vc.bizscript.s/GetTopicList",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(SServer).GetTopicList(ctx, req.(*GetTopicListReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _S_CreateScript_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(CreateScriptReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(SServer).CreateScript(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/vc.bizscript.s/CreateScript",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(SServer).CreateScript(ctx, req.(*CreateScriptReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _S_DeleteScript_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(DeleteScriptReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(SServer).DeleteScript(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/vc.bizscript.s/DeleteScript",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(SServer).DeleteScript(ctx, req.(*DeleteScriptReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _S_GetScriptList_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetScriptListReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(SServer).GetScriptList(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/vc.bizscript.s/GetScriptList",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(SServer).GetScriptList(ctx, req.(*GetScriptListReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _S_GetScriptDetail_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetScriptDetailReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(SServer).GetScriptDetail(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/vc.bizscript.s/GetScriptDetail",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(SServer).GetScriptDetail(ctx, req.(*GetScriptDetailReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _S_Search_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(SearchReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(SServer).Search(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/vc.bizscript.s/Search",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(SServer).Search(ctx, req.(*SearchReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _S_ShareScript_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ShareScriptReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(SServer).ShareScript(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/vc.bizscript.s/ShareScript",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(SServer).ShareScript(ctx, req.(*ShareScriptReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _S_GetLineDubbingAll_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetLineDubbingAllReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(SServer).GetLineDubbingAll(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/vc.bizscript.s/GetLineDubbingAll",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(SServer).GetLineDubbingAll(ctx, req.(*GetLineDubbingAllReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _S_GetLineDubbingSimple_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetLineDubbingSimpleReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(SServer).GetLineDubbingSimple(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/vc.bizscript.s/GetLineDubbingSimple",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(SServer).GetLineDubbingSimple(ctx, req.(*GetLineDubbingSimpleReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _S_GetScriptFirstDubbing_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetScriptFirstDubbingReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(SServer).GetScriptFirstDubbing(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/vc.bizscript.s/GetScriptFirstDubbing",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(SServer).GetScriptFirstDubbing(ctx, req.(*GetScriptFirstDubbingReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _S_BatchGetScriptFirstDubbing_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(BatchGetScriptFirstDubbingReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(SServer).BatchGetScriptFirstDubbing(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/vc.bizscript.s/BatchGetScriptFirstDubbing",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(SServer).BatchGetScriptFirstDubbing(ctx, req.(*BatchGetScriptFirstDubbingReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _S_CreateDubbing_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(CreateDubbingReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(SServer).CreateDubbing(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/vc.bizscript.s/CreateDubbing",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(SServer).CreateDubbing(ctx, req.(*CreateDubbingReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _S_DeleteDubbingRecord_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(DeleteDubbingRecordReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(SServer).DeleteDubbingRecord(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/vc.bizscript.s/DeleteDubbingRecord",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(SServer).DeleteDubbingRecord(ctx, req.(*DeleteDubbingRecordReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _S_DeleteDubbing_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(DeleteDubbingReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(SServer).DeleteDubbing(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/vc.bizscript.s/DeleteDubbing",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(SServer).DeleteDubbing(ctx, req.(*DeleteDubbingReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _S_SetDubbingTop_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(SetDubbingTopReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(SServer).SetDubbingTop(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/vc.bizscript.s/SetDubbingTop",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(SServer).SetDubbingTop(ctx, req.(*SetDubbingTopReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _S_GetCommentList_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetCommentListReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(SServer).GetCommentList(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/vc.bizscript.s/GetCommentList",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(SServer).GetCommentList(ctx, req.(*GetCommentListReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _S_GetCommentReplies_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetCommentRepliesReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(SServer).GetCommentReplies(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/vc.bizscript.s/GetCommentReplies",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(SServer).GetCommentReplies(ctx, req.(*GetCommentRepliesReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _S_CreateComment_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(CreateCommentReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(SServer).CreateComment(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/vc.bizscript.s/CreateComment",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(SServer).CreateComment(ctx, req.(*CreateCommentReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _S_SetCommentTop_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(SetCommentTopReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(SServer).SetCommentTop(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/vc.bizscript.s/SetCommentTop",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(SServer).SetCommentTop(ctx, req.(*SetCommentTopReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _S_DeleteComment_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(DeleteCommentReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(SServer).DeleteComment(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/vc.bizscript.s/DeleteComment",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(SServer).DeleteComment(ctx, req.(*DeleteCommentReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _S_GetCommentAsr_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetCommentASRReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(SServer).GetCommentAsr(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/vc.bizscript.s/GetCommentAsr",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(SServer).GetCommentAsr(ctx, req.(*GetCommentASRReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _S_Like_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(LikeReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(SServer).Like(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/vc.bizscript.s/Like",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(SServer).Like(ctx, req.(*LikeReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _S_Unlike_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(UnlikeReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(SServer).Unlike(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/vc.bizscript.s/Unlike",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(SServer).Unlike(ctx, req.(*UnlikeReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _S_BatchGetLikes_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(BatchGetLikesReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(SServer).BatchGetLikes(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/vc.bizscript.s/BatchGetLikes",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(SServer).BatchGetLikes(ctx, req.(*BatchGetLikesReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _S_GetReportReasons_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetReportReasonsReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(SServer).GetReportReasons(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/vc.bizscript.s/GetReportReasons",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(SServer).GetReportReasons(ctx, req.(*GetReportReasonsReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _S_Report_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ReportReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(SServer).Report(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/vc.bizscript.s/Report",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(SServer).Report(ctx, req.(*ReportReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _S_GetIPList_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetIPListReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(SServer).GetIPList(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/vc.bizscript.s/GetIPList",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(SServer).GetIPList(ctx, req.(*GetIPListReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _S_GetUserStats_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetUserStatsReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(SServer).GetUserStats(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/vc.bizscript.s/GetUserStats",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(SServer).GetUserStats(ctx, req.(*GetUserStatsReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _S_GetUserScriptLists_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetUserScriptListsReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(SServer).GetUserScriptLists(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/vc.bizscript.s/GetUserScriptLists",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(SServer).GetUserScriptLists(ctx, req.(*GetUserScriptListsReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _S_GetCoverList_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetCoverListReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(SServer).GetCoverList(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/vc.bizscript.s/GetCoverList",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(SServer).GetCoverList(ctx, req.(*GetCoverListReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _S_GenerateScriptLines_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GenerateScriptLinesReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(SServer).GenerateScriptLines(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/vc.bizscript.s/GenerateScriptLines",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(SServer).GenerateScriptLines(ctx, req.(*GenerateScriptLinesReq))
	}
	return interceptor(ctx, in, info, handler)
}

// S_ServiceDesc is the grpc.ServiceDesc for S service.
// It's only intended for direct use with grpc.RegisterService,
// and not to be introspected or modified (even as a copy)
var S_ServiceDesc = grpc.ServiceDesc{
	ServiceName: "vc.bizscript.s",
	HandlerType: (*SServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "GetCharacterList",
			Handler:    _S_GetCharacterList_Handler,
		},
		{
			MethodName: "SearchCharacters",
			Handler:    _S_SearchCharacters_Handler,
		},
		{
			MethodName: "SetUserCharacter",
			Handler:    _S_SetUserCharacter_Handler,
		},
		{
			MethodName: "GetUserCharacters",
			Handler:    _S_GetUserCharacters_Handler,
		},
		{
			MethodName: "BatchCheckCharacterAvailability",
			Handler:    _S_BatchCheckCharacterAvailability_Handler,
		},
		{
			MethodName: "GetTopicList",
			Handler:    _S_GetTopicList_Handler,
		},
		{
			MethodName: "CreateScript",
			Handler:    _S_CreateScript_Handler,
		},
		{
			MethodName: "DeleteScript",
			Handler:    _S_DeleteScript_Handler,
		},
		{
			MethodName: "GetScriptList",
			Handler:    _S_GetScriptList_Handler,
		},
		{
			MethodName: "GetScriptDetail",
			Handler:    _S_GetScriptDetail_Handler,
		},
		{
			MethodName: "Search",
			Handler:    _S_Search_Handler,
		},
		{
			MethodName: "ShareScript",
			Handler:    _S_ShareScript_Handler,
		},
		{
			MethodName: "GetLineDubbingAll",
			Handler:    _S_GetLineDubbingAll_Handler,
		},
		{
			MethodName: "GetLineDubbingSimple",
			Handler:    _S_GetLineDubbingSimple_Handler,
		},
		{
			MethodName: "GetScriptFirstDubbing",
			Handler:    _S_GetScriptFirstDubbing_Handler,
		},
		{
			MethodName: "BatchGetScriptFirstDubbing",
			Handler:    _S_BatchGetScriptFirstDubbing_Handler,
		},
		{
			MethodName: "CreateDubbing",
			Handler:    _S_CreateDubbing_Handler,
		},
		{
			MethodName: "DeleteDubbingRecord",
			Handler:    _S_DeleteDubbingRecord_Handler,
		},
		{
			MethodName: "DeleteDubbing",
			Handler:    _S_DeleteDubbing_Handler,
		},
		{
			MethodName: "SetDubbingTop",
			Handler:    _S_SetDubbingTop_Handler,
		},
		{
			MethodName: "GetCommentList",
			Handler:    _S_GetCommentList_Handler,
		},
		{
			MethodName: "GetCommentReplies",
			Handler:    _S_GetCommentReplies_Handler,
		},
		{
			MethodName: "CreateComment",
			Handler:    _S_CreateComment_Handler,
		},
		{
			MethodName: "SetCommentTop",
			Handler:    _S_SetCommentTop_Handler,
		},
		{
			MethodName: "DeleteComment",
			Handler:    _S_DeleteComment_Handler,
		},
		{
			MethodName: "GetCommentAsr",
			Handler:    _S_GetCommentAsr_Handler,
		},
		{
			MethodName: "Like",
			Handler:    _S_Like_Handler,
		},
		{
			MethodName: "Unlike",
			Handler:    _S_Unlike_Handler,
		},
		{
			MethodName: "BatchGetLikes",
			Handler:    _S_BatchGetLikes_Handler,
		},
		{
			MethodName: "GetReportReasons",
			Handler:    _S_GetReportReasons_Handler,
		},
		{
			MethodName: "Report",
			Handler:    _S_Report_Handler,
		},
		{
			MethodName: "GetIPList",
			Handler:    _S_GetIPList_Handler,
		},
		{
			MethodName: "GetUserStats",
			Handler:    _S_GetUserStats_Handler,
		},
		{
			MethodName: "GetUserScriptLists",
			Handler:    _S_GetUserScriptLists_Handler,
		},
		{
			MethodName: "GetCoverList",
			Handler:    _S_GetCoverList_Handler,
		},
		{
			MethodName: "GenerateScriptLines",
			Handler:    _S_GenerateScriptLines_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "bizscript.proto",
}
