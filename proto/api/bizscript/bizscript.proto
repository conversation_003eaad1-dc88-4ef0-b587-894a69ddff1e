syntax = "proto3";

package vc.bizscript;

import "google/api/annotations.proto";
import "protoc-gen-validate/validate/validate.proto";
import "google/api/field_behavior.proto";
import "svcscript/svcscript.proto";

option go_package = "new-gitlab.xunlei.cn/vcproject/backends/proto/api/bizscript";

// 剧本业务服务接口
service s {
  // 角色列表
  rpc GetCharacterList(GetCharacterListReq) returns (GetCharacterListResp) {
    option (google.api.http) = {
      post: "/vc.bizscript.s/v1/character/list"
      body: "*"
    };
  }

  // 查询角色
  rpc SearchCharacters(SearchCharactersReq) returns (SearchCharactersResp) {
    option (google.api.http) = {
      post: "/vc.bizscript.s/v1/character/search"
      body: "*"
    };
  }

  // 设置用户与角色关联
  rpc SetUserCharacter(SetUserCharacterReq) returns (SetUserCharacterResp) {
    option (google.api.http) = {
      post: "/vc.bizscript.s/v1/character/set"
      body: "*"
    };
  }

  // 获取用户角色列表
  rpc GetUserCharacters(GetUserCharactersReq) returns (GetUserCharactersResp) {
    option (google.api.http) = {
      post: "/vc.bizscript.s/v1/character/user"
      body: "*"
    };
  }

  // 批量检查角色可用性
  rpc BatchCheckCharacterAvailability(BatchCheckCharacterAvailabilityReq) returns (BatchCheckCharacterAvailabilityResp) {
    option (google.api.http) = {
      post: "/vc.bizscript.s/v1/character/batch_check_availability"
      body: "*"
    };
  }

  // 获取话题列表
  rpc GetTopicList(GetTopicListReq) returns (GetTopicListResp) {
    option (google.api.http) = {
      post: "/vc.bizscript.s/v1/topic/list"
      body: "*"
    };
  }

  // 创建剧本
  rpc CreateScript(CreateScriptReq) returns (CreateScriptResp) {
    option (google.api.http) = {
      post: "/vc.bizscript.s/v1/script/create"
      body: "*"
    };
  }

  // 删除剧本
  rpc DeleteScript(DeleteScriptReq) returns (DeleteScriptResp) {
    option (google.api.http) = {
      post: "/vc.bizscript.s/v1/script/delete"
      body: "*"
    };
  }

  // 获取剧本列表/话题聚合页
  rpc GetScriptList(GetScriptListReq) returns (GetScriptListResp) {
    option (google.api.http) = {
      post: "/vc.bizscript.s/v1/script/list"
      body: "*"
    };
  }

  // 获取剧本详情
  rpc GetScriptDetail(GetScriptDetailReq) returns (GetScriptDetailResp) {
    option (google.api.http) = {
      post: "/vc.bizscript.s/v1/script/detail"
      body: "*"
    };
  }

  // 搜索
  rpc Search(SearchReq) returns (SearchResp) {
    option (google.api.http) = {
      post: "/vc.bizscript.s/v1/script/search"
      body: "*"
    };
  }

  // 分享剧本
  rpc ShareScript(ShareScriptReq) returns (ShareScriptResp) {
    option (google.api.http) = {
      post: "/vc.bizscript.s/v1/script/share"
      body: "*"
    };
  }

  // 获取台词配音全部列表
  rpc GetLineDubbingAll(GetLineDubbingAllReq) returns (GetLineDubbingAllResp) {
    option (google.api.http) = {
      post: "/vc.bizscript.s/v1/dubbing/all"
      body: "*"
    };
  }

  // 获取台词配音简略列表
  rpc GetLineDubbingSimple(GetLineDubbingSimpleReq) returns (GetLineDubbingSimpleResp) {
    option (google.api.http) = {
      post: "/vc.bizscript.s/v1/dubbing/simple"
      body: "*"
    };
  }

  // 获取剧本所有台词的第一条配音请求
  rpc GetScriptFirstDubbing(GetScriptFirstDubbingReq) returns (GetScriptFirstDubbingResp) {
    option (google.api.http) = {
      post: "/vc.bizscript.s/v1/dubbing/all_first"
      body: "*"
    };
  }

  // 批量获取剧本所有台词的第一条配音请求
  rpc BatchGetScriptFirstDubbing(BatchGetScriptFirstDubbingReq) returns (BatchGetScriptFirstDubbingResp) {
    option (google.api.http) = {
      post: "/vc.bizscript.s/v1/dubbing/batch_all_first"
      body: "*"
    };
  }

  // 配音
  rpc CreateDubbing(CreateDubbingReq) returns (CreateDubbingResp) {
    option (google.api.http) = {
      post: "/vc.bizscript.s/v1/dubbing/create"
      body: "*"
    };
  }

  // 删除配音记录
  rpc DeleteDubbingRecord(DeleteDubbingRecordReq) returns (DeleteDubbingRecordResp) {
    option (google.api.http) = {
      post: "/vc.bizscript.s/v1/dubbing/delete_record"
      body: "*"
    };
  }

  // 删除配音
  rpc DeleteDubbing(DeleteDubbingReq) returns (DeleteDubbingResp) {
    option (google.api.http) = {
      post: "/vc.bizscript.s/v1/dubbing/delete"
      body: "*"
    };
  }

  // 设置配音置顶状态
  rpc SetDubbingTop(SetDubbingTopReq) returns (SetDubbingTopResp) {
    option (google.api.http) = {
      post: "/vc.bizscript.s/v1/dubbing/top"
      body: "*"
    };
  }

  // 获取评论列表
  rpc GetCommentList(GetCommentListReq) returns (GetCommentListResp) {
    option (google.api.http) = {
      post: "/vc.bizscript.s/v1/comment/list"
      body: "*"
    };
  }

  // 获取评论回复列表
  rpc GetCommentReplies(GetCommentRepliesReq) returns (GetCommentRepliesResp) {
    option (google.api.http) = {
      post: "/vc.bizscript.s/v1/comment/replies"
      body: "*"
    };
  }

  // 评论/回复
  rpc CreateComment(CreateCommentReq) returns (CreateCommentResp) {
    option (google.api.http) = {
      post: "/vc.bizscript.s/v1/comment/create"
      body: "*"
    };
  }

  // 设置评论置顶状态
  rpc SetCommentTop(SetCommentTopReq) returns (SetCommentTopResp) {
    option (google.api.http) = {
      post: "/vc.bizscript.s/v1/comment/top"
      body: "*"
    };
  }

  // 删除评论
  rpc DeleteComment(DeleteCommentReq) returns (DeleteCommentResp) {
    option (google.api.http) = {
      post: "/vc.bizscript.s/v1/comment/delete"
      body: "*"
    };
  }

  // 获取评论ASR
  rpc GetCommentAsr(GetCommentASRReq) returns (GetCommentASRResp) {
    option (google.api.http) = {
      post: "/vc.bizscript.s/v1/comment/get_asr"
      body: "*"
    };
  }

  // 点赞
  rpc Like(LikeReq) returns (LikeResp) {
    option (google.api.http) = {
      post: "/vc.bizscript.s/v1/like"
      body: "*"
    };
  }

  // 取消点赞
  rpc Unlike(UnlikeReq) returns (UnlikeResp) {
    option (google.api.http) = {
      post: "/vc.bizscript.s/v1/unlike"
      body: "*"
    };
  }

  // 批量获取点赞数
  rpc BatchGetLikes(BatchGetLikesReq) returns (BatchGetLikesResp) {
    option (google.api.http) = {
      post: "/vc.bizscript.s/v1/like/batch_get_likes"
      body: "*"
    };
  }

  // 获取举报原因列表
  rpc GetReportReasons(GetReportReasonsReq) returns (GetReportReasonsResp) {
    option (google.api.http) = {
      post: "/vc.bizscript.s/v1/report/reasons"
      body: "*"
    };
  }

  // 举报
  rpc Report(ReportReq) returns (ReportResp) {
    option (google.api.http) = {
      post: "/vc.bizscript.s/v1/report"
      body: "*"
    };
  }

  // 获取IP列表
  rpc GetIPList(GetIPListReq) returns (GetIPListResp) {
    option (google.api.http) = {
      post: "/vc.bizscript.s/v1/ip/list"
      body: "*"
    };
  }
  
  // 获取用户统计数据
  rpc GetUserStats(GetUserStatsReq) returns (GetUserStatsResp) {
    option (google.api.http) = {
      post: "/vc.bizscript.s/v1/user/stats"
      body: "*"
    };
  }
  
  // 获取用户剧本列表
  rpc GetUserScriptLists(GetUserScriptListsReq) returns (GetUserScriptListsResp) {
    option (google.api.http) = {
      post: "/vc.bizscript.s/v1/user/script_lists"
      body: "*"
    };
  }

  // 获取封面列表
  rpc GetCoverList(GetCoverListReq) returns (GetCoverListResp) {
    option (google.api.http) = {
      post: "/vc.bizscript.s/v1/cover/list"
      body: "*"
    };
  }

  // 生成剧本台词
  rpc GenerateScriptLines(GenerateScriptLinesReq) returns (GenerateScriptLinesResp) {
    option (google.api.http) = {
      post: "/vc.bizscript.s/v1/line/generate"
      body: "*"
    };
  }
}

// 获取角色列表请求
message GetCharacterListReq {
  int32 page = 1;     // 页码
  int32 page_size = 2; // 每页数量
  int64 ip_id = 3;     // IP ID，可选过滤
}

// 获取角色列表响应
message GetCharacterListResp {
  int32 code = 1;                                     // 错误码
  string msg = 2;                                     // 错误信息
  vc.svcscript.GetCharacterListRespData data = 3;     // 响应数据
}

// 搜索角色请求
message SearchCharactersReq {
  string keyword = 1 [(validate.rules).string.min_len = 1, (google.api.field_behavior) = REQUIRED]; // 搜索关键词
  int32 page = 2;           // 页码
  int32 page_size = 3;      // 每页数量
}

// 搜索角色响应
message SearchCharactersResp {
  int32 code = 1;                                     // 错误码
  string msg = 2;                                     // 错误信息
  vc.svcscript.GetCharacterListRespData data = 3;     // 响应数据
}

// 设置用户角色请求
message SetUserCharacterReq {
  int64 character_id = 1 [(validate.rules).int64.gt = 0, (google.api.field_behavior) = REQUIRED]; // 角色ID
  int64 character_asset_id = 2 [(validate.rules).int64.gt = 0, (google.api.field_behavior) = REQUIRED];     // 角色资源包ID，默认为0表示使用默认资源包
}

// 设置用户角色响应
message SetUserCharacterResp {
  int32 code = 1;                                     // 错误码
  string msg = 2;                                     // 错误信息
}

// 获取用户角色请求
message GetUserCharactersReq {}

// 获取用户角色响应
message GetUserCharactersResp {
  int32 code = 1;                                     // 错误码
  string msg = 2;                                     // 错误信息
  vc.svcscript.GetUserCharactersRespData data = 3;    // 响应数据
}

// 批量检查角色可用性请求
message BatchCheckCharacterAvailabilityReq {
  repeated int64 character_ids = 1 [(validate.rules).repeated = {min_items: 1, max_items: 100}, (google.api.field_behavior) = REQUIRED]; // 角色ID列表，最多100个
}

// 批量检查角色可用性响应
message BatchCheckCharacterAvailabilityResp {
  int32 code = 1;                                     // 错误码
  string msg = 2;                                     // 错误信息
  vc.svcscript.BatchCheckCharacterAvailabilityRespData data = 3; // 响应数据
}

// 获取话题列表请求
message GetTopicListReq {
  int32 page = 1;     // 页码
  int32 page_size = 2; // 每页数量
  svcscript.TOPIC_SCENE scene = 3 [(validate.rules).enum.defined_only = true]; // 场景
}

// 获取话题列表响应
message GetTopicListResp {
  int32 code = 1;                                     // 错误码
  string msg = 2;                                     // 错误信息
  vc.svcscript.GetTopicListRespData data = 3;         // 响应数据
}

// 创建剧本请求
message CreateScriptReq {
  string title = 1 [(validate.rules).string.min_len = 1,(google.api.field_behavior) = REQUIRED];         // 标题
  string cover = 2 [(validate.rules).string.min_len = 1,(google.api.field_behavior) = REQUIRED];         // 封面地址
  repeated svcscript.ScriptCharacterAsset character_asset_ids = 3 [(validate.rules).repeated = {min_items: 1, max_items: 6},(google.api.field_behavior) = REQUIRED];   // 角色ID列表,最多六个
  repeated string topic_names = 4 [(validate.rules).repeated = {min_items: 1},(google.api.field_behavior) = REQUIRED];                  // 话题ID列表
  repeated vc.svcscript.CreateLineReq lines = 5 [(validate.rules).repeated = {min_items: 1},(google.api.field_behavior) = REQUIRED];    // 台词列表
  string bgm_url = 6;             // 背景音乐地址
  int32 bgm_duration = 7;         // 背景音乐时长（毫秒）
  string theme_color = 8;         // 主题色
}

// 创建剧本响应
message CreateScriptResp {
  int32 code = 1;                                     // 错误码
  string msg = 2;                                     // 错误信息
  vc.svcscript.CreateScriptRespData data = 3;         // 响应数据
}

message DeleteScriptReq {
  int64 script_id = 1 [(validate.rules).int64.gt = 0, (google.api.field_behavior) = REQUIRED]; //剧本ID
}

message DeleteScriptResp {
  int32 code = 1;                                     // 错误码
  string msg = 2;                                     // 错误信息
}

// 获取剧本列表请求
message GetScriptListReq {
  int64 topic_id = 1;                         // 话题ID，必须，不能为0
  int32 page = 2;                             // 页码，从1开始
  int32 page_size = 3;                        // 每页数量，默认20
  bool is_aggregation = 4;                    // 是否话题聚合页
}

// 获取剧本列表响应
message GetScriptListResp {
  int32 code = 1;                                     // 错误码
  string msg = 2;                                     // 错误信息
  vc.svcscript.GetScriptListRespData data = 3;        // 响应数据
}

// 获取剧本详情请求
message GetScriptDetailReq {
  int64 script_id = 1 [(validate.rules).int64.gt = 0,(google.api.field_behavior) = REQUIRED]; // 剧本ID
}

// 获取剧本详情响应
message GetScriptDetailResp {
  int32 code = 1;                                     // 错误码
  string msg = 2;                                     // 错误信息
  vc.svcscript.Script data = 3;                       // 剧本详情
}

// 搜索剧本请求
message SearchReq {
  string keyword = 1 [(validate.rules).string.min_len = 1,(google.api.field_behavior) = REQUIRED];                  // 搜索关键词
  vc.svcscript.SearchType search_type = 2 [(validate.rules).enum.defined_only = true,(google.api.field_behavior) = REQUIRED];                                   // 搜索类型: 1=剧本搜索, 2=用户搜索, 3=话题搜索
  int32 page = 3;                            // 页码，从1开始
  int32 page_size = 4;                       // 每页数量，默认20
}

// 搜索剧本响应
message SearchResp {
  int32 code = 1;                                     // 错误码
  string msg = 2;                                     // 错误信息
  vc.svcscript.SearchRespData data = 3;        // 响应数据
}

// 关注剧本请求
message FollowScriptReq {
  int64 script_id = 1 [(validate.rules).int64.gt = 0,(google.api.field_behavior) = REQUIRED]; // 剧本ID
}

// 关注剧本响应
message FollowScriptResp {
  int32 code = 1;                                     // 错误码
  string msg = 2;                                     // 错误信息
}

// 取消关注剧本请求
message UnfollowScriptReq {
  int64 script_id = 1 [(validate.rules).int64.gt = 0,(google.api.field_behavior) = REQUIRED]; // 剧本ID
}

// 取消关注剧本响应
message UnfollowScriptResp {
  int32 code = 1;                                     // 错误码
  string msg = 2;                                     // 错误信息
}

// 分享剧本请求
message ShareScriptReq {
  int64 script_id = 1 [(validate.rules).int64.gt = 0,(google.api.field_behavior) = REQUIRED]; // 剧本ID
  string platform = 2;                                // 分享平台
}

// 分享剧本响应
message ShareScriptResp {
  int32 code = 1;                                     // 错误码
  string msg = 2;                                     // 错误信息
}

// 获取台词完整配音列表请求
message GetLineDubbingAllReq {
  int64 script_id = 1 [(validate.rules).int64.gt = 0,(google.api.field_behavior) = REQUIRED];   // 剧本ID
  int64 line_id = 2 [(validate.rules).int64.gt = 0,(google.api.field_behavior) = REQUIRED];     // 台词ID
  int32 page = 3;      // 页码
  int32 page_size = 4; // 每页数量
}

// 获取台词配音列表响应
message GetLineDubbingAllResp {
  int32 code = 1;                                     // 错误码
  string msg = 2;                                     // 错误信息
  vc.svcscript.GetLineDubbingsRespData data = 3;    // 响应数据
}

// 获取台词简略配音简略列表请求 (默认返回6条，如page_size小于6取page_size,大于6取6条）
message GetLineDubbingSimpleReq {
  int64 script_id = 1 [(validate.rules).int64.gt = 0,(google.api.field_behavior) = REQUIRED];   // 剧本ID
  int64 dubbing_record_id = 2;    // 配音记录ID（批次ID），可选，用于将指定批次的配音置顶显示
}

// 获取台词配音简略列表响应
message GetLineDubbingSimpleResp {
  int32 code = 1;                                     // 错误码
  string msg = 2;                                     // 错误信息
  vc.svcscript.GetLineDubbingsSimpleRespData data = 3;    // 响应数据
}

// 获取剧本所有台词的第一条配音请求
message GetScriptFirstDubbingReq {
  int64 script_id = 1 [(validate.rules).int64.gt = 0,(google.api.field_behavior) = REQUIRED];   // 剧本ID
}

// 获取剧本所有台词的第一条配音响应
message GetScriptFirstDubbingResp {
  int32 code = 1;                                     // 错误码
  string msg = 2;                                     // 错误信息
  vc.svcscript.GetScriptFirstDubbingData data = 3;    // 响应数据
}

// 批量获取剧本所有台词的第一条配音请求
message BatchGetScriptFirstDubbingReq {
  repeated int64 script_ids = 1 [(validate.rules).repeated = {min_items: 1, max_items: 50}, (google.api.field_behavior) = REQUIRED]; // 剧本ID列表，最多50个
}

// 批量获取剧本所有台词的第一条配音响应
message BatchGetScriptFirstDubbingResp {
  int32 code = 1;                                     // 错误码
  string msg = 2;                                     // 错误信息
  vc.svcscript.BatchGetScriptFirstDubbingData data = 3;    // 响应数据
}

// 创建配音请求
message CreateDubbingReq {
  int64 script_id = 1 [(validate.rules).int64.gt = 0,(google.api.field_behavior) = REQUIRED];           // 剧本ID
  map<int64, svcscript.LineDubbings> line_dubbings = 2;                   // 台词配音列表 map<lineID, LineDubbings>
}

// 创建配音响应
message CreateDubbingResp {
  int32 code = 1;                                     // 错误码
  string msg = 2;                                     // 错误信息
  vc.svcscript.CreateDubbingRespData data = 3;      // 响应数据
}

// 删除配音记录请求
message DeleteDubbingRecordReq {
  int64 dubbing_record_id = 1 [(validate.rules).int64.gt = 0, (google.api.field_behavior) = REQUIRED];  // 配音记录ID
}

// 删除配音记录响应
message DeleteDubbingRecordResp {
  int32 code = 1;                                     // 错误码
  string msg = 2;                                     // 错误信息
}

// 获取评论列表请求
message GetCommentListReq {
  vc.svcscript.CommentType comment_type = 1 [(google.api.field_behavior) = REQUIRED];                  // 评论类型: 1=剧本评论, 2=配音评论
  int64 script_id = 2 [(google.api.field_behavior) = REQUIRED];                                        // 剧本ID，当comment_type=1时必填
  int64 dubbing_id = 3;                                    // 配音ID，当comment_type=2时必填
  int64 parent_id = 4;                                        // 父评论ID，获取二级评论时填写，默认为0表示获取一级评论
  int32 page = 5;             // 页码，从1开始
  int32 page_size = 6;        // 每页数量，默认20
}

// 获取评论列表响应
message GetCommentListResp {
  int32 code = 1;                                     // 错误码
  string msg = 2;                                     // 错误信息
  vc.svcscript.GetCommentListRespData data = 3;       // 响应数据
}

// 获取评论回复列表请求
message GetCommentRepliesReq {
  int64 parent_id = 1 [(validate.rules).int64.gt = 0,(google.api.field_behavior) = REQUIRED];        // 父评论ID，指定要获取哪个评论的回复
  int32 page = 2;             // 页码，从1开始
  int32 page_size = 3;        // 每页数量，默认20
}

// 获取评论回复列表响应
message GetCommentRepliesResp {
  int32 code = 1;                                                   // 错误码
  string msg = 2;                                                   // 错误信息
  vc.svcscript.GetCommentListRespData data = 3;                     // 响应数据
}

// 创建评论请求
message CreateCommentReq {
  vc.svcscript.CommentType comment_type = 1 [(validate.rules).enum.defined_only = true,(google.api.field_behavior) = REQUIRED];  // 评论类型: 1=剧本评论, 2=配音评论
  int64 parent_id = 2;                                                        // 父评论ID，回复评论时填写，默认为0表示创建一级评论
  int64 script_id = 3;                                                        // 剧本ID，当comment_type=1时必填
  int64 dubbing_id = 4;                                                    // 配音ID，当comment_type=2时必填
  int64 character_id = 5;                                                  // 角色ID，用户选择的评论角色
  int64 character_asset_id = 6;                                                                                      //角色资源ID
  vc.svcscript.ContentType content_type = 7 [(google.api.field_behavior) = REQUIRED];                                // 内容类型: 1=文本, 2=语音
  string content = 8;                                                         // 评论内容，当content_type=1时必填
  string voice_url = 9;                                                       // 语音地址，当content_type=2时必填
  int32 voice_duration = 10;                                                   // 语音时长（毫秒），当content_type=2时必填
  string original_voice_url = 12;                                 // 原声地址 当content_type=2时必填
  int32 original_voice_duration = 13;                             // 原声时长（毫秒）
  string svc_voice_url = 14;                                      // 服务端变卖语音地址 当content_type=2时必填
  int32 svc_voice_duration = 15;                                  // 服务端变卖语音时长（毫秒）
}

// 创建评论响应
message CreateCommentResp {
  int32 code = 1;                                     // 错误码
  string msg = 2;                                     // 错误信息
  vc.svcscript.CreateCommentRespData data = 3;        // 响应数据
}

// 设置评论置顶状态请求
message SetCommentTopReq {
  int64 comment_id = 1 [(validate.rules).int64.gt = 0,(google.api.field_behavior) = REQUIRED]; // 评论ID
  bool is_top = 2 [(google.api.field_behavior) = REQUIRED]; // 是否置顶
}

// 设置评论置顶状态响应
message SetCommentTopResp {
  int32 code = 1;                                     // 错误码
  string msg = 2;                                     // 错误信息
}

// 删除评论请求
message DeleteCommentReq {
  int64 comment_id = 1 [(validate.rules).int64.gt = 0,(google.api.field_behavior) = REQUIRED]; // 评论ID
}

// 删除评论响应
message DeleteCommentResp {
  int32 code = 1;                                     // 错误码
  string msg = 2;                                     // 错误信息
}

// 获取评论ASR请求
message GetCommentASRReq {
  int64 comment_id = 1 [(validate.rules).int64.gt = 0,(google.api.field_behavior) = REQUIRED]; // 评论ID
}

// 获取评论ASR响应
message GetCommentASRResp {
  int32 code = 1;                                     // 错误码
  string msg = 2;                                     // 错误信息
  vc.svcscript.GetCommentASRRespData data = 3;        // 响应数据
}

// 点赞请求
message LikeReq {
  vc.svcscript.LikeType like_type = 1 [(validate.rules).enum.defined_only = true,(google.api.field_behavior) = REQUIRED];        // 点赞类型: 1=剧本点赞, 2=配音点赞, 3=评论点赞
  int64 target_id = 2 [(validate.rules).int64.gt = 0];                        // 目标ID，根据like_type对应剧本ID、配音ID或评论ID
}

// 点赞响应
message LikeResp {
  int32 code = 1;                                     // 错误码
  string msg = 2;                                     // 错误信息
}

// 取消点赞请求
message UnlikeReq {
  vc.svcscript.LikeType like_type = 1 [(validate.rules).enum.defined_only = true,(google.api.field_behavior) = REQUIRED];        // 点赞类型: 1=剧本点赞, 2=配音点赞, 3=评论点赞
  int64 target_id = 2 [(validate.rules).int64.gt = 0];                        // 目标ID，根据like_type对应剧本ID、配音ID或评论ID
}

// 取消点赞响应
message UnlikeResp {
  int32 code = 1;                                     // 错误码
  string msg = 2;                                     // 错误信息
}

// 批量获取点赞请求
message BatchGetLikesReq {
  vc.svcscript.LikeType like_type = 1 [(validate.rules).enum.defined_only = true,(google.api.field_behavior) = REQUIRED];        // 点赞类型: 1=剧本点赞, 2=配音点赞, 3=评论点赞
  repeated int64 target_ids = 2 [(validate.rules).repeated = {min_items: 1},(google.api.field_behavior) = REQUIRED];             // 目标ID列表
}

// 批量获取点赞响应
message BatchGetLikesResp {
  int32 code = 1;                                     // 错误码
  string msg = 2;                                     // 错误信息
  map<int64, int32> data = 3;                         // 点赞数 map<targetID, likes>
}

// 获取举报原因列表请求
message GetReportReasonsReq {
  vc.svcscript.ReportType report_type = 1 [(validate.rules).enum.defined_only = true,(google.api.field_behavior) = REQUIRED];    // 举报类型: 1=剧本举报, 2=配音举报, 3=评论举报
}

// 获取举报原因列表响应
message GetReportReasonsResp {
  int32 code = 1;                                     // 错误码
  string msg = 2;                                     // 错误信息
  vc.svcscript.GetReportReasonsRespData data = 3;     // 响应数据
}

// 举报内容请求
message ReportReq {
  vc.svcscript.ReportType report_type = 1 [(validate.rules).enum.defined_only = true,(google.api.field_behavior) = REQUIRED];    // 举报类型: 1=剧本举报, 2=配音举报, 3=评论举报
  int64 target_id = 2 [(validate.rules).int64.gt = 0,(google.api.field_behavior) = REQUIRED];                        // 目标ID，根据report_type对应剧本ID、配音ID或评论ID
  int32 reason_id = 3;                                                        // 原因ID，对应举报原因列表中的ID，自定义原因时可为0
  string reason = 4;                                                          // 原因内容，自定义原因时填写
  string content = 5;                                                         // 举报内容摘要（标题/评论内容/配音内容）
}

// 举报内容响应
message ReportResp {
  int32 code = 1;                                     // 错误码
  string msg = 2;                                     // 错误信息
}

// 获取IP列表请求
message GetIPListReq {
  int32 page = 1;     // 页码
  int32 page_size = 2; // 每页数量
}

// 获取IP列表响应
message GetIPListResp {
  int32 code = 1;                                     // 错误码
  string msg = 2;                                     // 错误信息
  vc.svcscript.GetIPListRespData data = 3;            // 响应数据
}

// 获取用户统计数据请求
message GetUserStatsReq {
  int64 user_id = 1;  // 目标用户id，可选，默认当前登录用户
}

// 获取用户统计数据响应
message GetUserStatsResp {
  int32 code = 1;                                     // 错误码
  string msg = 2;                                     // 错误信息
  vc.svcscript.GetUserStatsRespData data = 3;         // 响应数据
}

// 获取用户剧本列表请求
message GetUserScriptListsReq {
  vc.svcscript.GetUserScriptListsReq.ListType list_type = 1 [(google.api.field_behavior) = REQUIRED];       // 列表类型
  int64 user_id = 2;               // 用户ID, 可选，默认当前用户
  int32 page = 3;                  // 页码
  int32 page_size = 4;             // 每页数量
}

// 获取用户剧本列表响应
message GetUserScriptListsResp {
  int32 code = 1;                                     // 错误码
  string msg = 2;                                     // 错误信息
  vc.svcscript.GetUserScriptListsRespData data = 3;   // 响应数据
}

// 获取封面列表请求
message GetCoverListReq {
  int32 page = 1;                // 页码
  int32 page_size = 2;           // 每页数量
  vc.svcscript.CoverType type = 3;                               // 封面类型
}

// 获取封面列表响应
message GetCoverListResp {
  int32 code = 1;                                                // 错误码
  string msg = 2;                                                // 错误信息
  vc.svcscript.GetCoverListRespData data = 3;                    // 响应数据
}

// 生成剧本台词请求
message GenerateScriptLinesReq {
  repeated int64 character_ids = 1 [(validate.rules).repeated = {min_items: 1},(google.api.field_behavior) = REQUIRED];       // 角色id列表
}

// 生成剧本台词响应
message GenerateScriptLinesResp {
  int32 code = 1;                                               // 错误码
  string msg = 2;                                               // 错误信息
  GenerateScriptLinesRespData data = 3;                         // 响应数据
}

// 生成剧本台词响应数据
message GenerateScriptLinesRespData {
  repeated vc.svcscript.Line lines = 1;                         // 生成的台词列表
  map<int64, vc.svcscript.Character> characters = 2;        // 相关角色列表 map<CharacterID, Character>
}

// 删除配音请求
message DeleteDubbingReq {
  int64 dubbing_id = 1 [(validate.rules).int64.gt = 0, (google.api.field_behavior) = REQUIRED]; // 配音ID
}

// 删除配音响应
message DeleteDubbingResp {
  int32 code = 1;                                     // 错误码
  string msg = 2;                                     // 错误信息
}

// 设置配音置顶状态请求
message SetDubbingTopReq {
  int64 dubbing_id = 1 [(validate.rules).int64.gt = 0, (google.api.field_behavior) = REQUIRED]; // 配音ID
  bool is_top = 2; // 是否置顶
}

// 设置配音置顶状态响应
message SetDubbingTopResp {
  int32 code = 1;                                     // 错误码
  string msg = 2;                                     // 错误信息
}