// Code generated by protoc-gen-validate. DO NOT EDIT.
// source: bizscript.proto

package bizscript

import (
	"bytes"
	"errors"
	"fmt"
	"net"
	"net/mail"
	"net/url"
	"regexp"
	"sort"
	"strings"
	"time"
	"unicode/utf8"

	"google.golang.org/protobuf/types/known/anypb"

	svcscript "new-gitlab.xunlei.cn/vcproject/backends/proto/api/svcscript"
)

// ensure the imports are used
var (
	_ = bytes.MinRead
	_ = errors.New("")
	_ = fmt.Print
	_ = utf8.UTFMax
	_ = (*regexp.Regexp)(nil)
	_ = (*strings.Reader)(nil)
	_ = net.IPv4len
	_ = time.Duration(0)
	_ = (*url.URL)(nil)
	_ = (*mail.Address)(nil)
	_ = anypb.Any{}
	_ = sort.Sort

	_ = svcscript.TOPIC_SCENE(0)
)

// Validate checks the field values on GetCharacterListReq with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *GetCharacterListReq) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GetCharacterListReq with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// GetCharacterListReqMultiError, or nil if none found.
func (m *GetCharacterListReq) ValidateAll() error {
	return m.validate(true)
}

func (m *GetCharacterListReq) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Page

	// no validation rules for PageSize

	// no validation rules for IpId

	if len(errors) > 0 {
		return GetCharacterListReqMultiError(errors)
	}

	return nil
}

// GetCharacterListReqMultiError is an error wrapping multiple validation
// errors returned by GetCharacterListReq.ValidateAll() if the designated
// constraints aren't met.
type GetCharacterListReqMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetCharacterListReqMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetCharacterListReqMultiError) AllErrors() []error { return m }

// GetCharacterListReqValidationError is the validation error returned by
// GetCharacterListReq.Validate if the designated constraints aren't met.
type GetCharacterListReqValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetCharacterListReqValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetCharacterListReqValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetCharacterListReqValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetCharacterListReqValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetCharacterListReqValidationError) ErrorName() string {
	return "GetCharacterListReqValidationError"
}

// Error satisfies the builtin error interface
func (e GetCharacterListReqValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetCharacterListReq.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetCharacterListReqValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetCharacterListReqValidationError{}

// Validate checks the field values on GetCharacterListResp with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *GetCharacterListResp) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GetCharacterListResp with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// GetCharacterListRespMultiError, or nil if none found.
func (m *GetCharacterListResp) ValidateAll() error {
	return m.validate(true)
}

func (m *GetCharacterListResp) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Code

	// no validation rules for Msg

	if all {
		switch v := interface{}(m.GetData()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetCharacterListRespValidationError{
					field:  "Data",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetCharacterListRespValidationError{
					field:  "Data",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetData()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetCharacterListRespValidationError{
				field:  "Data",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return GetCharacterListRespMultiError(errors)
	}

	return nil
}

// GetCharacterListRespMultiError is an error wrapping multiple validation
// errors returned by GetCharacterListResp.ValidateAll() if the designated
// constraints aren't met.
type GetCharacterListRespMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetCharacterListRespMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetCharacterListRespMultiError) AllErrors() []error { return m }

// GetCharacterListRespValidationError is the validation error returned by
// GetCharacterListResp.Validate if the designated constraints aren't met.
type GetCharacterListRespValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetCharacterListRespValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetCharacterListRespValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetCharacterListRespValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetCharacterListRespValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetCharacterListRespValidationError) ErrorName() string {
	return "GetCharacterListRespValidationError"
}

// Error satisfies the builtin error interface
func (e GetCharacterListRespValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetCharacterListResp.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetCharacterListRespValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetCharacterListRespValidationError{}

// Validate checks the field values on SearchCharactersReq with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *SearchCharactersReq) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on SearchCharactersReq with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// SearchCharactersReqMultiError, or nil if none found.
func (m *SearchCharactersReq) ValidateAll() error {
	return m.validate(true)
}

func (m *SearchCharactersReq) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if utf8.RuneCountInString(m.GetKeyword()) < 1 {
		err := SearchCharactersReqValidationError{
			field:  "Keyword",
			reason: "value length must be at least 1 runes",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	// no validation rules for Page

	// no validation rules for PageSize

	if len(errors) > 0 {
		return SearchCharactersReqMultiError(errors)
	}

	return nil
}

// SearchCharactersReqMultiError is an error wrapping multiple validation
// errors returned by SearchCharactersReq.ValidateAll() if the designated
// constraints aren't met.
type SearchCharactersReqMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m SearchCharactersReqMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m SearchCharactersReqMultiError) AllErrors() []error { return m }

// SearchCharactersReqValidationError is the validation error returned by
// SearchCharactersReq.Validate if the designated constraints aren't met.
type SearchCharactersReqValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e SearchCharactersReqValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e SearchCharactersReqValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e SearchCharactersReqValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e SearchCharactersReqValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e SearchCharactersReqValidationError) ErrorName() string {
	return "SearchCharactersReqValidationError"
}

// Error satisfies the builtin error interface
func (e SearchCharactersReqValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sSearchCharactersReq.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = SearchCharactersReqValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = SearchCharactersReqValidationError{}

// Validate checks the field values on SearchCharactersResp with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *SearchCharactersResp) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on SearchCharactersResp with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// SearchCharactersRespMultiError, or nil if none found.
func (m *SearchCharactersResp) ValidateAll() error {
	return m.validate(true)
}

func (m *SearchCharactersResp) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Code

	// no validation rules for Msg

	if all {
		switch v := interface{}(m.GetData()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, SearchCharactersRespValidationError{
					field:  "Data",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, SearchCharactersRespValidationError{
					field:  "Data",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetData()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return SearchCharactersRespValidationError{
				field:  "Data",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return SearchCharactersRespMultiError(errors)
	}

	return nil
}

// SearchCharactersRespMultiError is an error wrapping multiple validation
// errors returned by SearchCharactersResp.ValidateAll() if the designated
// constraints aren't met.
type SearchCharactersRespMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m SearchCharactersRespMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m SearchCharactersRespMultiError) AllErrors() []error { return m }

// SearchCharactersRespValidationError is the validation error returned by
// SearchCharactersResp.Validate if the designated constraints aren't met.
type SearchCharactersRespValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e SearchCharactersRespValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e SearchCharactersRespValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e SearchCharactersRespValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e SearchCharactersRespValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e SearchCharactersRespValidationError) ErrorName() string {
	return "SearchCharactersRespValidationError"
}

// Error satisfies the builtin error interface
func (e SearchCharactersRespValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sSearchCharactersResp.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = SearchCharactersRespValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = SearchCharactersRespValidationError{}

// Validate checks the field values on SetUserCharacterReq with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *SetUserCharacterReq) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on SetUserCharacterReq with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// SetUserCharacterReqMultiError, or nil if none found.
func (m *SetUserCharacterReq) ValidateAll() error {
	return m.validate(true)
}

func (m *SetUserCharacterReq) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if m.GetCharacterId() <= 0 {
		err := SetUserCharacterReqValidationError{
			field:  "CharacterId",
			reason: "value must be greater than 0",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if m.GetCharacterAssetId() <= 0 {
		err := SetUserCharacterReqValidationError{
			field:  "CharacterAssetId",
			reason: "value must be greater than 0",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if len(errors) > 0 {
		return SetUserCharacterReqMultiError(errors)
	}

	return nil
}

// SetUserCharacterReqMultiError is an error wrapping multiple validation
// errors returned by SetUserCharacterReq.ValidateAll() if the designated
// constraints aren't met.
type SetUserCharacterReqMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m SetUserCharacterReqMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m SetUserCharacterReqMultiError) AllErrors() []error { return m }

// SetUserCharacterReqValidationError is the validation error returned by
// SetUserCharacterReq.Validate if the designated constraints aren't met.
type SetUserCharacterReqValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e SetUserCharacterReqValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e SetUserCharacterReqValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e SetUserCharacterReqValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e SetUserCharacterReqValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e SetUserCharacterReqValidationError) ErrorName() string {
	return "SetUserCharacterReqValidationError"
}

// Error satisfies the builtin error interface
func (e SetUserCharacterReqValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sSetUserCharacterReq.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = SetUserCharacterReqValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = SetUserCharacterReqValidationError{}

// Validate checks the field values on SetUserCharacterResp with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *SetUserCharacterResp) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on SetUserCharacterResp with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// SetUserCharacterRespMultiError, or nil if none found.
func (m *SetUserCharacterResp) ValidateAll() error {
	return m.validate(true)
}

func (m *SetUserCharacterResp) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Code

	// no validation rules for Msg

	if len(errors) > 0 {
		return SetUserCharacterRespMultiError(errors)
	}

	return nil
}

// SetUserCharacterRespMultiError is an error wrapping multiple validation
// errors returned by SetUserCharacterResp.ValidateAll() if the designated
// constraints aren't met.
type SetUserCharacterRespMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m SetUserCharacterRespMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m SetUserCharacterRespMultiError) AllErrors() []error { return m }

// SetUserCharacterRespValidationError is the validation error returned by
// SetUserCharacterResp.Validate if the designated constraints aren't met.
type SetUserCharacterRespValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e SetUserCharacterRespValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e SetUserCharacterRespValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e SetUserCharacterRespValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e SetUserCharacterRespValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e SetUserCharacterRespValidationError) ErrorName() string {
	return "SetUserCharacterRespValidationError"
}

// Error satisfies the builtin error interface
func (e SetUserCharacterRespValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sSetUserCharacterResp.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = SetUserCharacterRespValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = SetUserCharacterRespValidationError{}

// Validate checks the field values on GetUserCharactersReq with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *GetUserCharactersReq) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GetUserCharactersReq with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// GetUserCharactersReqMultiError, or nil if none found.
func (m *GetUserCharactersReq) ValidateAll() error {
	return m.validate(true)
}

func (m *GetUserCharactersReq) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if len(errors) > 0 {
		return GetUserCharactersReqMultiError(errors)
	}

	return nil
}

// GetUserCharactersReqMultiError is an error wrapping multiple validation
// errors returned by GetUserCharactersReq.ValidateAll() if the designated
// constraints aren't met.
type GetUserCharactersReqMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetUserCharactersReqMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetUserCharactersReqMultiError) AllErrors() []error { return m }

// GetUserCharactersReqValidationError is the validation error returned by
// GetUserCharactersReq.Validate if the designated constraints aren't met.
type GetUserCharactersReqValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetUserCharactersReqValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetUserCharactersReqValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetUserCharactersReqValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetUserCharactersReqValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetUserCharactersReqValidationError) ErrorName() string {
	return "GetUserCharactersReqValidationError"
}

// Error satisfies the builtin error interface
func (e GetUserCharactersReqValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetUserCharactersReq.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetUserCharactersReqValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetUserCharactersReqValidationError{}

// Validate checks the field values on GetUserCharactersResp with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *GetUserCharactersResp) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GetUserCharactersResp with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// GetUserCharactersRespMultiError, or nil if none found.
func (m *GetUserCharactersResp) ValidateAll() error {
	return m.validate(true)
}

func (m *GetUserCharactersResp) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Code

	// no validation rules for Msg

	if all {
		switch v := interface{}(m.GetData()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetUserCharactersRespValidationError{
					field:  "Data",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetUserCharactersRespValidationError{
					field:  "Data",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetData()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetUserCharactersRespValidationError{
				field:  "Data",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return GetUserCharactersRespMultiError(errors)
	}

	return nil
}

// GetUserCharactersRespMultiError is an error wrapping multiple validation
// errors returned by GetUserCharactersResp.ValidateAll() if the designated
// constraints aren't met.
type GetUserCharactersRespMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetUserCharactersRespMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetUserCharactersRespMultiError) AllErrors() []error { return m }

// GetUserCharactersRespValidationError is the validation error returned by
// GetUserCharactersResp.Validate if the designated constraints aren't met.
type GetUserCharactersRespValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetUserCharactersRespValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetUserCharactersRespValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetUserCharactersRespValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetUserCharactersRespValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetUserCharactersRespValidationError) ErrorName() string {
	return "GetUserCharactersRespValidationError"
}

// Error satisfies the builtin error interface
func (e GetUserCharactersRespValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetUserCharactersResp.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetUserCharactersRespValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetUserCharactersRespValidationError{}

// Validate checks the field values on BatchCheckCharacterAvailabilityReq with
// the rules defined in the proto definition for this message. If any rules
// are violated, the first error encountered is returned, or nil if there are
// no violations.
func (m *BatchCheckCharacterAvailabilityReq) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on BatchCheckCharacterAvailabilityReq
// with the rules defined in the proto definition for this message. If any
// rules are violated, the result is a list of violation errors wrapped in
// BatchCheckCharacterAvailabilityReqMultiError, or nil if none found.
func (m *BatchCheckCharacterAvailabilityReq) ValidateAll() error {
	return m.validate(true)
}

func (m *BatchCheckCharacterAvailabilityReq) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if l := len(m.GetCharacterIds()); l < 1 || l > 100 {
		err := BatchCheckCharacterAvailabilityReqValidationError{
			field:  "CharacterIds",
			reason: "value must contain between 1 and 100 items, inclusive",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if len(errors) > 0 {
		return BatchCheckCharacterAvailabilityReqMultiError(errors)
	}

	return nil
}

// BatchCheckCharacterAvailabilityReqMultiError is an error wrapping multiple
// validation errors returned by
// BatchCheckCharacterAvailabilityReq.ValidateAll() if the designated
// constraints aren't met.
type BatchCheckCharacterAvailabilityReqMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m BatchCheckCharacterAvailabilityReqMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m BatchCheckCharacterAvailabilityReqMultiError) AllErrors() []error { return m }

// BatchCheckCharacterAvailabilityReqValidationError is the validation error
// returned by BatchCheckCharacterAvailabilityReq.Validate if the designated
// constraints aren't met.
type BatchCheckCharacterAvailabilityReqValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e BatchCheckCharacterAvailabilityReqValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e BatchCheckCharacterAvailabilityReqValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e BatchCheckCharacterAvailabilityReqValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e BatchCheckCharacterAvailabilityReqValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e BatchCheckCharacterAvailabilityReqValidationError) ErrorName() string {
	return "BatchCheckCharacterAvailabilityReqValidationError"
}

// Error satisfies the builtin error interface
func (e BatchCheckCharacterAvailabilityReqValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sBatchCheckCharacterAvailabilityReq.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = BatchCheckCharacterAvailabilityReqValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = BatchCheckCharacterAvailabilityReqValidationError{}

// Validate checks the field values on BatchCheckCharacterAvailabilityResp with
// the rules defined in the proto definition for this message. If any rules
// are violated, the first error encountered is returned, or nil if there are
// no violations.
func (m *BatchCheckCharacterAvailabilityResp) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on BatchCheckCharacterAvailabilityResp
// with the rules defined in the proto definition for this message. If any
// rules are violated, the result is a list of violation errors wrapped in
// BatchCheckCharacterAvailabilityRespMultiError, or nil if none found.
func (m *BatchCheckCharacterAvailabilityResp) ValidateAll() error {
	return m.validate(true)
}

func (m *BatchCheckCharacterAvailabilityResp) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Code

	// no validation rules for Msg

	if all {
		switch v := interface{}(m.GetData()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, BatchCheckCharacterAvailabilityRespValidationError{
					field:  "Data",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, BatchCheckCharacterAvailabilityRespValidationError{
					field:  "Data",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetData()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return BatchCheckCharacterAvailabilityRespValidationError{
				field:  "Data",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return BatchCheckCharacterAvailabilityRespMultiError(errors)
	}

	return nil
}

// BatchCheckCharacterAvailabilityRespMultiError is an error wrapping multiple
// validation errors returned by
// BatchCheckCharacterAvailabilityResp.ValidateAll() if the designated
// constraints aren't met.
type BatchCheckCharacterAvailabilityRespMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m BatchCheckCharacterAvailabilityRespMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m BatchCheckCharacterAvailabilityRespMultiError) AllErrors() []error { return m }

// BatchCheckCharacterAvailabilityRespValidationError is the validation error
// returned by BatchCheckCharacterAvailabilityResp.Validate if the designated
// constraints aren't met.
type BatchCheckCharacterAvailabilityRespValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e BatchCheckCharacterAvailabilityRespValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e BatchCheckCharacterAvailabilityRespValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e BatchCheckCharacterAvailabilityRespValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e BatchCheckCharacterAvailabilityRespValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e BatchCheckCharacterAvailabilityRespValidationError) ErrorName() string {
	return "BatchCheckCharacterAvailabilityRespValidationError"
}

// Error satisfies the builtin error interface
func (e BatchCheckCharacterAvailabilityRespValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sBatchCheckCharacterAvailabilityResp.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = BatchCheckCharacterAvailabilityRespValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = BatchCheckCharacterAvailabilityRespValidationError{}

// Validate checks the field values on GetTopicListReq with the rules defined
// in the proto definition for this message. If any rules are violated, the
// first error encountered is returned, or nil if there are no violations.
func (m *GetTopicListReq) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GetTopicListReq with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// GetTopicListReqMultiError, or nil if none found.
func (m *GetTopicListReq) ValidateAll() error {
	return m.validate(true)
}

func (m *GetTopicListReq) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Page

	// no validation rules for PageSize

	if _, ok := svcscript.TOPIC_SCENE_name[int32(m.GetScene())]; !ok {
		err := GetTopicListReqValidationError{
			field:  "Scene",
			reason: "value must be one of the defined enum values",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if len(errors) > 0 {
		return GetTopicListReqMultiError(errors)
	}

	return nil
}

// GetTopicListReqMultiError is an error wrapping multiple validation errors
// returned by GetTopicListReq.ValidateAll() if the designated constraints
// aren't met.
type GetTopicListReqMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetTopicListReqMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetTopicListReqMultiError) AllErrors() []error { return m }

// GetTopicListReqValidationError is the validation error returned by
// GetTopicListReq.Validate if the designated constraints aren't met.
type GetTopicListReqValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetTopicListReqValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetTopicListReqValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetTopicListReqValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetTopicListReqValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetTopicListReqValidationError) ErrorName() string { return "GetTopicListReqValidationError" }

// Error satisfies the builtin error interface
func (e GetTopicListReqValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetTopicListReq.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetTopicListReqValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetTopicListReqValidationError{}

// Validate checks the field values on GetTopicListResp with the rules defined
// in the proto definition for this message. If any rules are violated, the
// first error encountered is returned, or nil if there are no violations.
func (m *GetTopicListResp) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GetTopicListResp with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// GetTopicListRespMultiError, or nil if none found.
func (m *GetTopicListResp) ValidateAll() error {
	return m.validate(true)
}

func (m *GetTopicListResp) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Code

	// no validation rules for Msg

	if all {
		switch v := interface{}(m.GetData()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetTopicListRespValidationError{
					field:  "Data",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetTopicListRespValidationError{
					field:  "Data",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetData()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetTopicListRespValidationError{
				field:  "Data",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return GetTopicListRespMultiError(errors)
	}

	return nil
}

// GetTopicListRespMultiError is an error wrapping multiple validation errors
// returned by GetTopicListResp.ValidateAll() if the designated constraints
// aren't met.
type GetTopicListRespMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetTopicListRespMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetTopicListRespMultiError) AllErrors() []error { return m }

// GetTopicListRespValidationError is the validation error returned by
// GetTopicListResp.Validate if the designated constraints aren't met.
type GetTopicListRespValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetTopicListRespValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetTopicListRespValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetTopicListRespValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetTopicListRespValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetTopicListRespValidationError) ErrorName() string { return "GetTopicListRespValidationError" }

// Error satisfies the builtin error interface
func (e GetTopicListRespValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetTopicListResp.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetTopicListRespValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetTopicListRespValidationError{}

// Validate checks the field values on CreateScriptReq with the rules defined
// in the proto definition for this message. If any rules are violated, the
// first error encountered is returned, or nil if there are no violations.
func (m *CreateScriptReq) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on CreateScriptReq with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// CreateScriptReqMultiError, or nil if none found.
func (m *CreateScriptReq) ValidateAll() error {
	return m.validate(true)
}

func (m *CreateScriptReq) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if utf8.RuneCountInString(m.GetTitle()) < 1 {
		err := CreateScriptReqValidationError{
			field:  "Title",
			reason: "value length must be at least 1 runes",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if utf8.RuneCountInString(m.GetCover()) < 1 {
		err := CreateScriptReqValidationError{
			field:  "Cover",
			reason: "value length must be at least 1 runes",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if l := len(m.GetCharacterAssetIds()); l < 1 || l > 6 {
		err := CreateScriptReqValidationError{
			field:  "CharacterAssetIds",
			reason: "value must contain between 1 and 6 items, inclusive",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	for idx, item := range m.GetCharacterAssetIds() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, CreateScriptReqValidationError{
						field:  fmt.Sprintf("CharacterAssetIds[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, CreateScriptReqValidationError{
						field:  fmt.Sprintf("CharacterAssetIds[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return CreateScriptReqValidationError{
					field:  fmt.Sprintf("CharacterAssetIds[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	if len(m.GetTopicNames()) < 1 {
		err := CreateScriptReqValidationError{
			field:  "TopicNames",
			reason: "value must contain at least 1 item(s)",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if len(m.GetLines()) < 1 {
		err := CreateScriptReqValidationError{
			field:  "Lines",
			reason: "value must contain at least 1 item(s)",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	for idx, item := range m.GetLines() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, CreateScriptReqValidationError{
						field:  fmt.Sprintf("Lines[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, CreateScriptReqValidationError{
						field:  fmt.Sprintf("Lines[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return CreateScriptReqValidationError{
					field:  fmt.Sprintf("Lines[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	// no validation rules for BgmUrl

	// no validation rules for BgmDuration

	// no validation rules for ThemeColor

	if len(errors) > 0 {
		return CreateScriptReqMultiError(errors)
	}

	return nil
}

// CreateScriptReqMultiError is an error wrapping multiple validation errors
// returned by CreateScriptReq.ValidateAll() if the designated constraints
// aren't met.
type CreateScriptReqMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m CreateScriptReqMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m CreateScriptReqMultiError) AllErrors() []error { return m }

// CreateScriptReqValidationError is the validation error returned by
// CreateScriptReq.Validate if the designated constraints aren't met.
type CreateScriptReqValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e CreateScriptReqValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e CreateScriptReqValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e CreateScriptReqValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e CreateScriptReqValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e CreateScriptReqValidationError) ErrorName() string { return "CreateScriptReqValidationError" }

// Error satisfies the builtin error interface
func (e CreateScriptReqValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sCreateScriptReq.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = CreateScriptReqValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = CreateScriptReqValidationError{}

// Validate checks the field values on CreateScriptResp with the rules defined
// in the proto definition for this message. If any rules are violated, the
// first error encountered is returned, or nil if there are no violations.
func (m *CreateScriptResp) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on CreateScriptResp with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// CreateScriptRespMultiError, or nil if none found.
func (m *CreateScriptResp) ValidateAll() error {
	return m.validate(true)
}

func (m *CreateScriptResp) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Code

	// no validation rules for Msg

	if all {
		switch v := interface{}(m.GetData()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, CreateScriptRespValidationError{
					field:  "Data",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, CreateScriptRespValidationError{
					field:  "Data",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetData()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return CreateScriptRespValidationError{
				field:  "Data",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return CreateScriptRespMultiError(errors)
	}

	return nil
}

// CreateScriptRespMultiError is an error wrapping multiple validation errors
// returned by CreateScriptResp.ValidateAll() if the designated constraints
// aren't met.
type CreateScriptRespMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m CreateScriptRespMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m CreateScriptRespMultiError) AllErrors() []error { return m }

// CreateScriptRespValidationError is the validation error returned by
// CreateScriptResp.Validate if the designated constraints aren't met.
type CreateScriptRespValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e CreateScriptRespValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e CreateScriptRespValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e CreateScriptRespValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e CreateScriptRespValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e CreateScriptRespValidationError) ErrorName() string { return "CreateScriptRespValidationError" }

// Error satisfies the builtin error interface
func (e CreateScriptRespValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sCreateScriptResp.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = CreateScriptRespValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = CreateScriptRespValidationError{}

// Validate checks the field values on DeleteScriptReq with the rules defined
// in the proto definition for this message. If any rules are violated, the
// first error encountered is returned, or nil if there are no violations.
func (m *DeleteScriptReq) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on DeleteScriptReq with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// DeleteScriptReqMultiError, or nil if none found.
func (m *DeleteScriptReq) ValidateAll() error {
	return m.validate(true)
}

func (m *DeleteScriptReq) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if m.GetScriptId() <= 0 {
		err := DeleteScriptReqValidationError{
			field:  "ScriptId",
			reason: "value must be greater than 0",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if len(errors) > 0 {
		return DeleteScriptReqMultiError(errors)
	}

	return nil
}

// DeleteScriptReqMultiError is an error wrapping multiple validation errors
// returned by DeleteScriptReq.ValidateAll() if the designated constraints
// aren't met.
type DeleteScriptReqMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m DeleteScriptReqMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m DeleteScriptReqMultiError) AllErrors() []error { return m }

// DeleteScriptReqValidationError is the validation error returned by
// DeleteScriptReq.Validate if the designated constraints aren't met.
type DeleteScriptReqValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e DeleteScriptReqValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e DeleteScriptReqValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e DeleteScriptReqValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e DeleteScriptReqValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e DeleteScriptReqValidationError) ErrorName() string { return "DeleteScriptReqValidationError" }

// Error satisfies the builtin error interface
func (e DeleteScriptReqValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sDeleteScriptReq.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = DeleteScriptReqValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = DeleteScriptReqValidationError{}

// Validate checks the field values on DeleteScriptResp with the rules defined
// in the proto definition for this message. If any rules are violated, the
// first error encountered is returned, or nil if there are no violations.
func (m *DeleteScriptResp) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on DeleteScriptResp with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// DeleteScriptRespMultiError, or nil if none found.
func (m *DeleteScriptResp) ValidateAll() error {
	return m.validate(true)
}

func (m *DeleteScriptResp) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Code

	// no validation rules for Msg

	if len(errors) > 0 {
		return DeleteScriptRespMultiError(errors)
	}

	return nil
}

// DeleteScriptRespMultiError is an error wrapping multiple validation errors
// returned by DeleteScriptResp.ValidateAll() if the designated constraints
// aren't met.
type DeleteScriptRespMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m DeleteScriptRespMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m DeleteScriptRespMultiError) AllErrors() []error { return m }

// DeleteScriptRespValidationError is the validation error returned by
// DeleteScriptResp.Validate if the designated constraints aren't met.
type DeleteScriptRespValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e DeleteScriptRespValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e DeleteScriptRespValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e DeleteScriptRespValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e DeleteScriptRespValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e DeleteScriptRespValidationError) ErrorName() string { return "DeleteScriptRespValidationError" }

// Error satisfies the builtin error interface
func (e DeleteScriptRespValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sDeleteScriptResp.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = DeleteScriptRespValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = DeleteScriptRespValidationError{}

// Validate checks the field values on GetScriptListReq with the rules defined
// in the proto definition for this message. If any rules are violated, the
// first error encountered is returned, or nil if there are no violations.
func (m *GetScriptListReq) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GetScriptListReq with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// GetScriptListReqMultiError, or nil if none found.
func (m *GetScriptListReq) ValidateAll() error {
	return m.validate(true)
}

func (m *GetScriptListReq) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for TopicId

	// no validation rules for Page

	// no validation rules for PageSize

	// no validation rules for IsAggregation

	if len(errors) > 0 {
		return GetScriptListReqMultiError(errors)
	}

	return nil
}

// GetScriptListReqMultiError is an error wrapping multiple validation errors
// returned by GetScriptListReq.ValidateAll() if the designated constraints
// aren't met.
type GetScriptListReqMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetScriptListReqMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetScriptListReqMultiError) AllErrors() []error { return m }

// GetScriptListReqValidationError is the validation error returned by
// GetScriptListReq.Validate if the designated constraints aren't met.
type GetScriptListReqValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetScriptListReqValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetScriptListReqValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetScriptListReqValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetScriptListReqValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetScriptListReqValidationError) ErrorName() string { return "GetScriptListReqValidationError" }

// Error satisfies the builtin error interface
func (e GetScriptListReqValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetScriptListReq.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetScriptListReqValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetScriptListReqValidationError{}

// Validate checks the field values on GetScriptListResp with the rules defined
// in the proto definition for this message. If any rules are violated, the
// first error encountered is returned, or nil if there are no violations.
func (m *GetScriptListResp) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GetScriptListResp with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// GetScriptListRespMultiError, or nil if none found.
func (m *GetScriptListResp) ValidateAll() error {
	return m.validate(true)
}

func (m *GetScriptListResp) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Code

	// no validation rules for Msg

	if all {
		switch v := interface{}(m.GetData()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetScriptListRespValidationError{
					field:  "Data",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetScriptListRespValidationError{
					field:  "Data",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetData()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetScriptListRespValidationError{
				field:  "Data",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return GetScriptListRespMultiError(errors)
	}

	return nil
}

// GetScriptListRespMultiError is an error wrapping multiple validation errors
// returned by GetScriptListResp.ValidateAll() if the designated constraints
// aren't met.
type GetScriptListRespMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetScriptListRespMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetScriptListRespMultiError) AllErrors() []error { return m }

// GetScriptListRespValidationError is the validation error returned by
// GetScriptListResp.Validate if the designated constraints aren't met.
type GetScriptListRespValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetScriptListRespValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetScriptListRespValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetScriptListRespValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetScriptListRespValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetScriptListRespValidationError) ErrorName() string {
	return "GetScriptListRespValidationError"
}

// Error satisfies the builtin error interface
func (e GetScriptListRespValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetScriptListResp.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetScriptListRespValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetScriptListRespValidationError{}

// Validate checks the field values on GetScriptDetailReq with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *GetScriptDetailReq) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GetScriptDetailReq with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// GetScriptDetailReqMultiError, or nil if none found.
func (m *GetScriptDetailReq) ValidateAll() error {
	return m.validate(true)
}

func (m *GetScriptDetailReq) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if m.GetScriptId() <= 0 {
		err := GetScriptDetailReqValidationError{
			field:  "ScriptId",
			reason: "value must be greater than 0",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if len(errors) > 0 {
		return GetScriptDetailReqMultiError(errors)
	}

	return nil
}

// GetScriptDetailReqMultiError is an error wrapping multiple validation errors
// returned by GetScriptDetailReq.ValidateAll() if the designated constraints
// aren't met.
type GetScriptDetailReqMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetScriptDetailReqMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetScriptDetailReqMultiError) AllErrors() []error { return m }

// GetScriptDetailReqValidationError is the validation error returned by
// GetScriptDetailReq.Validate if the designated constraints aren't met.
type GetScriptDetailReqValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetScriptDetailReqValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetScriptDetailReqValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetScriptDetailReqValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetScriptDetailReqValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetScriptDetailReqValidationError) ErrorName() string {
	return "GetScriptDetailReqValidationError"
}

// Error satisfies the builtin error interface
func (e GetScriptDetailReqValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetScriptDetailReq.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetScriptDetailReqValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetScriptDetailReqValidationError{}

// Validate checks the field values on GetScriptDetailResp with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *GetScriptDetailResp) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GetScriptDetailResp with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// GetScriptDetailRespMultiError, or nil if none found.
func (m *GetScriptDetailResp) ValidateAll() error {
	return m.validate(true)
}

func (m *GetScriptDetailResp) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Code

	// no validation rules for Msg

	if all {
		switch v := interface{}(m.GetData()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetScriptDetailRespValidationError{
					field:  "Data",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetScriptDetailRespValidationError{
					field:  "Data",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetData()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetScriptDetailRespValidationError{
				field:  "Data",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return GetScriptDetailRespMultiError(errors)
	}

	return nil
}

// GetScriptDetailRespMultiError is an error wrapping multiple validation
// errors returned by GetScriptDetailResp.ValidateAll() if the designated
// constraints aren't met.
type GetScriptDetailRespMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetScriptDetailRespMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetScriptDetailRespMultiError) AllErrors() []error { return m }

// GetScriptDetailRespValidationError is the validation error returned by
// GetScriptDetailResp.Validate if the designated constraints aren't met.
type GetScriptDetailRespValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetScriptDetailRespValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetScriptDetailRespValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetScriptDetailRespValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetScriptDetailRespValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetScriptDetailRespValidationError) ErrorName() string {
	return "GetScriptDetailRespValidationError"
}

// Error satisfies the builtin error interface
func (e GetScriptDetailRespValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetScriptDetailResp.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetScriptDetailRespValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetScriptDetailRespValidationError{}

// Validate checks the field values on SearchReq with the rules defined in the
// proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *SearchReq) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on SearchReq with the rules defined in
// the proto definition for this message. If any rules are violated, the
// result is a list of violation errors wrapped in SearchReqMultiError, or nil
// if none found.
func (m *SearchReq) ValidateAll() error {
	return m.validate(true)
}

func (m *SearchReq) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if utf8.RuneCountInString(m.GetKeyword()) < 1 {
		err := SearchReqValidationError{
			field:  "Keyword",
			reason: "value length must be at least 1 runes",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if _, ok := svcscript.SearchType_name[int32(m.GetSearchType())]; !ok {
		err := SearchReqValidationError{
			field:  "SearchType",
			reason: "value must be one of the defined enum values",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	// no validation rules for Page

	// no validation rules for PageSize

	if len(errors) > 0 {
		return SearchReqMultiError(errors)
	}

	return nil
}

// SearchReqMultiError is an error wrapping multiple validation errors returned
// by SearchReq.ValidateAll() if the designated constraints aren't met.
type SearchReqMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m SearchReqMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m SearchReqMultiError) AllErrors() []error { return m }

// SearchReqValidationError is the validation error returned by
// SearchReq.Validate if the designated constraints aren't met.
type SearchReqValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e SearchReqValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e SearchReqValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e SearchReqValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e SearchReqValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e SearchReqValidationError) ErrorName() string { return "SearchReqValidationError" }

// Error satisfies the builtin error interface
func (e SearchReqValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sSearchReq.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = SearchReqValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = SearchReqValidationError{}

// Validate checks the field values on SearchResp with the rules defined in the
// proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *SearchResp) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on SearchResp with the rules defined in
// the proto definition for this message. If any rules are violated, the
// result is a list of violation errors wrapped in SearchRespMultiError, or
// nil if none found.
func (m *SearchResp) ValidateAll() error {
	return m.validate(true)
}

func (m *SearchResp) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Code

	// no validation rules for Msg

	if all {
		switch v := interface{}(m.GetData()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, SearchRespValidationError{
					field:  "Data",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, SearchRespValidationError{
					field:  "Data",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetData()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return SearchRespValidationError{
				field:  "Data",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return SearchRespMultiError(errors)
	}

	return nil
}

// SearchRespMultiError is an error wrapping multiple validation errors
// returned by SearchResp.ValidateAll() if the designated constraints aren't met.
type SearchRespMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m SearchRespMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m SearchRespMultiError) AllErrors() []error { return m }

// SearchRespValidationError is the validation error returned by
// SearchResp.Validate if the designated constraints aren't met.
type SearchRespValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e SearchRespValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e SearchRespValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e SearchRespValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e SearchRespValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e SearchRespValidationError) ErrorName() string { return "SearchRespValidationError" }

// Error satisfies the builtin error interface
func (e SearchRespValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sSearchResp.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = SearchRespValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = SearchRespValidationError{}

// Validate checks the field values on FollowScriptReq with the rules defined
// in the proto definition for this message. If any rules are violated, the
// first error encountered is returned, or nil if there are no violations.
func (m *FollowScriptReq) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on FollowScriptReq with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// FollowScriptReqMultiError, or nil if none found.
func (m *FollowScriptReq) ValidateAll() error {
	return m.validate(true)
}

func (m *FollowScriptReq) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if m.GetScriptId() <= 0 {
		err := FollowScriptReqValidationError{
			field:  "ScriptId",
			reason: "value must be greater than 0",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if len(errors) > 0 {
		return FollowScriptReqMultiError(errors)
	}

	return nil
}

// FollowScriptReqMultiError is an error wrapping multiple validation errors
// returned by FollowScriptReq.ValidateAll() if the designated constraints
// aren't met.
type FollowScriptReqMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m FollowScriptReqMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m FollowScriptReqMultiError) AllErrors() []error { return m }

// FollowScriptReqValidationError is the validation error returned by
// FollowScriptReq.Validate if the designated constraints aren't met.
type FollowScriptReqValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e FollowScriptReqValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e FollowScriptReqValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e FollowScriptReqValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e FollowScriptReqValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e FollowScriptReqValidationError) ErrorName() string { return "FollowScriptReqValidationError" }

// Error satisfies the builtin error interface
func (e FollowScriptReqValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sFollowScriptReq.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = FollowScriptReqValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = FollowScriptReqValidationError{}

// Validate checks the field values on FollowScriptResp with the rules defined
// in the proto definition for this message. If any rules are violated, the
// first error encountered is returned, or nil if there are no violations.
func (m *FollowScriptResp) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on FollowScriptResp with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// FollowScriptRespMultiError, or nil if none found.
func (m *FollowScriptResp) ValidateAll() error {
	return m.validate(true)
}

func (m *FollowScriptResp) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Code

	// no validation rules for Msg

	if len(errors) > 0 {
		return FollowScriptRespMultiError(errors)
	}

	return nil
}

// FollowScriptRespMultiError is an error wrapping multiple validation errors
// returned by FollowScriptResp.ValidateAll() if the designated constraints
// aren't met.
type FollowScriptRespMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m FollowScriptRespMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m FollowScriptRespMultiError) AllErrors() []error { return m }

// FollowScriptRespValidationError is the validation error returned by
// FollowScriptResp.Validate if the designated constraints aren't met.
type FollowScriptRespValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e FollowScriptRespValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e FollowScriptRespValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e FollowScriptRespValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e FollowScriptRespValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e FollowScriptRespValidationError) ErrorName() string { return "FollowScriptRespValidationError" }

// Error satisfies the builtin error interface
func (e FollowScriptRespValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sFollowScriptResp.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = FollowScriptRespValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = FollowScriptRespValidationError{}

// Validate checks the field values on UnfollowScriptReq with the rules defined
// in the proto definition for this message. If any rules are violated, the
// first error encountered is returned, or nil if there are no violations.
func (m *UnfollowScriptReq) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on UnfollowScriptReq with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// UnfollowScriptReqMultiError, or nil if none found.
func (m *UnfollowScriptReq) ValidateAll() error {
	return m.validate(true)
}

func (m *UnfollowScriptReq) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if m.GetScriptId() <= 0 {
		err := UnfollowScriptReqValidationError{
			field:  "ScriptId",
			reason: "value must be greater than 0",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if len(errors) > 0 {
		return UnfollowScriptReqMultiError(errors)
	}

	return nil
}

// UnfollowScriptReqMultiError is an error wrapping multiple validation errors
// returned by UnfollowScriptReq.ValidateAll() if the designated constraints
// aren't met.
type UnfollowScriptReqMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m UnfollowScriptReqMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m UnfollowScriptReqMultiError) AllErrors() []error { return m }

// UnfollowScriptReqValidationError is the validation error returned by
// UnfollowScriptReq.Validate if the designated constraints aren't met.
type UnfollowScriptReqValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e UnfollowScriptReqValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e UnfollowScriptReqValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e UnfollowScriptReqValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e UnfollowScriptReqValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e UnfollowScriptReqValidationError) ErrorName() string {
	return "UnfollowScriptReqValidationError"
}

// Error satisfies the builtin error interface
func (e UnfollowScriptReqValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sUnfollowScriptReq.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = UnfollowScriptReqValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = UnfollowScriptReqValidationError{}

// Validate checks the field values on UnfollowScriptResp with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *UnfollowScriptResp) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on UnfollowScriptResp with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// UnfollowScriptRespMultiError, or nil if none found.
func (m *UnfollowScriptResp) ValidateAll() error {
	return m.validate(true)
}

func (m *UnfollowScriptResp) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Code

	// no validation rules for Msg

	if len(errors) > 0 {
		return UnfollowScriptRespMultiError(errors)
	}

	return nil
}

// UnfollowScriptRespMultiError is an error wrapping multiple validation errors
// returned by UnfollowScriptResp.ValidateAll() if the designated constraints
// aren't met.
type UnfollowScriptRespMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m UnfollowScriptRespMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m UnfollowScriptRespMultiError) AllErrors() []error { return m }

// UnfollowScriptRespValidationError is the validation error returned by
// UnfollowScriptResp.Validate if the designated constraints aren't met.
type UnfollowScriptRespValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e UnfollowScriptRespValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e UnfollowScriptRespValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e UnfollowScriptRespValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e UnfollowScriptRespValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e UnfollowScriptRespValidationError) ErrorName() string {
	return "UnfollowScriptRespValidationError"
}

// Error satisfies the builtin error interface
func (e UnfollowScriptRespValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sUnfollowScriptResp.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = UnfollowScriptRespValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = UnfollowScriptRespValidationError{}

// Validate checks the field values on ShareScriptReq with the rules defined in
// the proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *ShareScriptReq) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on ShareScriptReq with the rules defined
// in the proto definition for this message. If any rules are violated, the
// result is a list of violation errors wrapped in ShareScriptReqMultiError,
// or nil if none found.
func (m *ShareScriptReq) ValidateAll() error {
	return m.validate(true)
}

func (m *ShareScriptReq) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if m.GetScriptId() <= 0 {
		err := ShareScriptReqValidationError{
			field:  "ScriptId",
			reason: "value must be greater than 0",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	// no validation rules for Platform

	if len(errors) > 0 {
		return ShareScriptReqMultiError(errors)
	}

	return nil
}

// ShareScriptReqMultiError is an error wrapping multiple validation errors
// returned by ShareScriptReq.ValidateAll() if the designated constraints
// aren't met.
type ShareScriptReqMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m ShareScriptReqMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m ShareScriptReqMultiError) AllErrors() []error { return m }

// ShareScriptReqValidationError is the validation error returned by
// ShareScriptReq.Validate if the designated constraints aren't met.
type ShareScriptReqValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e ShareScriptReqValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e ShareScriptReqValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e ShareScriptReqValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e ShareScriptReqValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e ShareScriptReqValidationError) ErrorName() string { return "ShareScriptReqValidationError" }

// Error satisfies the builtin error interface
func (e ShareScriptReqValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sShareScriptReq.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = ShareScriptReqValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = ShareScriptReqValidationError{}

// Validate checks the field values on ShareScriptResp with the rules defined
// in the proto definition for this message. If any rules are violated, the
// first error encountered is returned, or nil if there are no violations.
func (m *ShareScriptResp) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on ShareScriptResp with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// ShareScriptRespMultiError, or nil if none found.
func (m *ShareScriptResp) ValidateAll() error {
	return m.validate(true)
}

func (m *ShareScriptResp) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Code

	// no validation rules for Msg

	if len(errors) > 0 {
		return ShareScriptRespMultiError(errors)
	}

	return nil
}

// ShareScriptRespMultiError is an error wrapping multiple validation errors
// returned by ShareScriptResp.ValidateAll() if the designated constraints
// aren't met.
type ShareScriptRespMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m ShareScriptRespMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m ShareScriptRespMultiError) AllErrors() []error { return m }

// ShareScriptRespValidationError is the validation error returned by
// ShareScriptResp.Validate if the designated constraints aren't met.
type ShareScriptRespValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e ShareScriptRespValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e ShareScriptRespValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e ShareScriptRespValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e ShareScriptRespValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e ShareScriptRespValidationError) ErrorName() string { return "ShareScriptRespValidationError" }

// Error satisfies the builtin error interface
func (e ShareScriptRespValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sShareScriptResp.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = ShareScriptRespValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = ShareScriptRespValidationError{}

// Validate checks the field values on GetLineDubbingAllReq with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *GetLineDubbingAllReq) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GetLineDubbingAllReq with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// GetLineDubbingAllReqMultiError, or nil if none found.
func (m *GetLineDubbingAllReq) ValidateAll() error {
	return m.validate(true)
}

func (m *GetLineDubbingAllReq) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if m.GetScriptId() <= 0 {
		err := GetLineDubbingAllReqValidationError{
			field:  "ScriptId",
			reason: "value must be greater than 0",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if m.GetLineId() <= 0 {
		err := GetLineDubbingAllReqValidationError{
			field:  "LineId",
			reason: "value must be greater than 0",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	// no validation rules for Page

	// no validation rules for PageSize

	if len(errors) > 0 {
		return GetLineDubbingAllReqMultiError(errors)
	}

	return nil
}

// GetLineDubbingAllReqMultiError is an error wrapping multiple validation
// errors returned by GetLineDubbingAllReq.ValidateAll() if the designated
// constraints aren't met.
type GetLineDubbingAllReqMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetLineDubbingAllReqMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetLineDubbingAllReqMultiError) AllErrors() []error { return m }

// GetLineDubbingAllReqValidationError is the validation error returned by
// GetLineDubbingAllReq.Validate if the designated constraints aren't met.
type GetLineDubbingAllReqValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetLineDubbingAllReqValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetLineDubbingAllReqValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetLineDubbingAllReqValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetLineDubbingAllReqValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetLineDubbingAllReqValidationError) ErrorName() string {
	return "GetLineDubbingAllReqValidationError"
}

// Error satisfies the builtin error interface
func (e GetLineDubbingAllReqValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetLineDubbingAllReq.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetLineDubbingAllReqValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetLineDubbingAllReqValidationError{}

// Validate checks the field values on GetLineDubbingAllResp with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *GetLineDubbingAllResp) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GetLineDubbingAllResp with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// GetLineDubbingAllRespMultiError, or nil if none found.
func (m *GetLineDubbingAllResp) ValidateAll() error {
	return m.validate(true)
}

func (m *GetLineDubbingAllResp) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Code

	// no validation rules for Msg

	if all {
		switch v := interface{}(m.GetData()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetLineDubbingAllRespValidationError{
					field:  "Data",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetLineDubbingAllRespValidationError{
					field:  "Data",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetData()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetLineDubbingAllRespValidationError{
				field:  "Data",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return GetLineDubbingAllRespMultiError(errors)
	}

	return nil
}

// GetLineDubbingAllRespMultiError is an error wrapping multiple validation
// errors returned by GetLineDubbingAllResp.ValidateAll() if the designated
// constraints aren't met.
type GetLineDubbingAllRespMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetLineDubbingAllRespMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetLineDubbingAllRespMultiError) AllErrors() []error { return m }

// GetLineDubbingAllRespValidationError is the validation error returned by
// GetLineDubbingAllResp.Validate if the designated constraints aren't met.
type GetLineDubbingAllRespValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetLineDubbingAllRespValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetLineDubbingAllRespValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetLineDubbingAllRespValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetLineDubbingAllRespValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetLineDubbingAllRespValidationError) ErrorName() string {
	return "GetLineDubbingAllRespValidationError"
}

// Error satisfies the builtin error interface
func (e GetLineDubbingAllRespValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetLineDubbingAllResp.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetLineDubbingAllRespValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetLineDubbingAllRespValidationError{}

// Validate checks the field values on GetLineDubbingSimpleReq with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *GetLineDubbingSimpleReq) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GetLineDubbingSimpleReq with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// GetLineDubbingSimpleReqMultiError, or nil if none found.
func (m *GetLineDubbingSimpleReq) ValidateAll() error {
	return m.validate(true)
}

func (m *GetLineDubbingSimpleReq) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if m.GetScriptId() <= 0 {
		err := GetLineDubbingSimpleReqValidationError{
			field:  "ScriptId",
			reason: "value must be greater than 0",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	// no validation rules for DubbingRecordId

	if len(errors) > 0 {
		return GetLineDubbingSimpleReqMultiError(errors)
	}

	return nil
}

// GetLineDubbingSimpleReqMultiError is an error wrapping multiple validation
// errors returned by GetLineDubbingSimpleReq.ValidateAll() if the designated
// constraints aren't met.
type GetLineDubbingSimpleReqMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetLineDubbingSimpleReqMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetLineDubbingSimpleReqMultiError) AllErrors() []error { return m }

// GetLineDubbingSimpleReqValidationError is the validation error returned by
// GetLineDubbingSimpleReq.Validate if the designated constraints aren't met.
type GetLineDubbingSimpleReqValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetLineDubbingSimpleReqValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetLineDubbingSimpleReqValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetLineDubbingSimpleReqValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetLineDubbingSimpleReqValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetLineDubbingSimpleReqValidationError) ErrorName() string {
	return "GetLineDubbingSimpleReqValidationError"
}

// Error satisfies the builtin error interface
func (e GetLineDubbingSimpleReqValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetLineDubbingSimpleReq.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetLineDubbingSimpleReqValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetLineDubbingSimpleReqValidationError{}

// Validate checks the field values on GetLineDubbingSimpleResp with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *GetLineDubbingSimpleResp) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GetLineDubbingSimpleResp with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// GetLineDubbingSimpleRespMultiError, or nil if none found.
func (m *GetLineDubbingSimpleResp) ValidateAll() error {
	return m.validate(true)
}

func (m *GetLineDubbingSimpleResp) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Code

	// no validation rules for Msg

	if all {
		switch v := interface{}(m.GetData()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetLineDubbingSimpleRespValidationError{
					field:  "Data",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetLineDubbingSimpleRespValidationError{
					field:  "Data",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetData()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetLineDubbingSimpleRespValidationError{
				field:  "Data",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return GetLineDubbingSimpleRespMultiError(errors)
	}

	return nil
}

// GetLineDubbingSimpleRespMultiError is an error wrapping multiple validation
// errors returned by GetLineDubbingSimpleResp.ValidateAll() if the designated
// constraints aren't met.
type GetLineDubbingSimpleRespMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetLineDubbingSimpleRespMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetLineDubbingSimpleRespMultiError) AllErrors() []error { return m }

// GetLineDubbingSimpleRespValidationError is the validation error returned by
// GetLineDubbingSimpleResp.Validate if the designated constraints aren't met.
type GetLineDubbingSimpleRespValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetLineDubbingSimpleRespValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetLineDubbingSimpleRespValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetLineDubbingSimpleRespValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetLineDubbingSimpleRespValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetLineDubbingSimpleRespValidationError) ErrorName() string {
	return "GetLineDubbingSimpleRespValidationError"
}

// Error satisfies the builtin error interface
func (e GetLineDubbingSimpleRespValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetLineDubbingSimpleResp.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetLineDubbingSimpleRespValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetLineDubbingSimpleRespValidationError{}

// Validate checks the field values on GetScriptFirstDubbingReq with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *GetScriptFirstDubbingReq) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GetScriptFirstDubbingReq with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// GetScriptFirstDubbingReqMultiError, or nil if none found.
func (m *GetScriptFirstDubbingReq) ValidateAll() error {
	return m.validate(true)
}

func (m *GetScriptFirstDubbingReq) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if m.GetScriptId() <= 0 {
		err := GetScriptFirstDubbingReqValidationError{
			field:  "ScriptId",
			reason: "value must be greater than 0",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if len(errors) > 0 {
		return GetScriptFirstDubbingReqMultiError(errors)
	}

	return nil
}

// GetScriptFirstDubbingReqMultiError is an error wrapping multiple validation
// errors returned by GetScriptFirstDubbingReq.ValidateAll() if the designated
// constraints aren't met.
type GetScriptFirstDubbingReqMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetScriptFirstDubbingReqMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetScriptFirstDubbingReqMultiError) AllErrors() []error { return m }

// GetScriptFirstDubbingReqValidationError is the validation error returned by
// GetScriptFirstDubbingReq.Validate if the designated constraints aren't met.
type GetScriptFirstDubbingReqValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetScriptFirstDubbingReqValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetScriptFirstDubbingReqValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetScriptFirstDubbingReqValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetScriptFirstDubbingReqValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetScriptFirstDubbingReqValidationError) ErrorName() string {
	return "GetScriptFirstDubbingReqValidationError"
}

// Error satisfies the builtin error interface
func (e GetScriptFirstDubbingReqValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetScriptFirstDubbingReq.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetScriptFirstDubbingReqValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetScriptFirstDubbingReqValidationError{}

// Validate checks the field values on GetScriptFirstDubbingResp with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *GetScriptFirstDubbingResp) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GetScriptFirstDubbingResp with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// GetScriptFirstDubbingRespMultiError, or nil if none found.
func (m *GetScriptFirstDubbingResp) ValidateAll() error {
	return m.validate(true)
}

func (m *GetScriptFirstDubbingResp) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Code

	// no validation rules for Msg

	if all {
		switch v := interface{}(m.GetData()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetScriptFirstDubbingRespValidationError{
					field:  "Data",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetScriptFirstDubbingRespValidationError{
					field:  "Data",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetData()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetScriptFirstDubbingRespValidationError{
				field:  "Data",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return GetScriptFirstDubbingRespMultiError(errors)
	}

	return nil
}

// GetScriptFirstDubbingRespMultiError is an error wrapping multiple validation
// errors returned by GetScriptFirstDubbingResp.ValidateAll() if the
// designated constraints aren't met.
type GetScriptFirstDubbingRespMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetScriptFirstDubbingRespMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetScriptFirstDubbingRespMultiError) AllErrors() []error { return m }

// GetScriptFirstDubbingRespValidationError is the validation error returned by
// GetScriptFirstDubbingResp.Validate if the designated constraints aren't met.
type GetScriptFirstDubbingRespValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetScriptFirstDubbingRespValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetScriptFirstDubbingRespValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetScriptFirstDubbingRespValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetScriptFirstDubbingRespValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetScriptFirstDubbingRespValidationError) ErrorName() string {
	return "GetScriptFirstDubbingRespValidationError"
}

// Error satisfies the builtin error interface
func (e GetScriptFirstDubbingRespValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetScriptFirstDubbingResp.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetScriptFirstDubbingRespValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetScriptFirstDubbingRespValidationError{}

// Validate checks the field values on BatchGetScriptFirstDubbingReq with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *BatchGetScriptFirstDubbingReq) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on BatchGetScriptFirstDubbingReq with
// the rules defined in the proto definition for this message. If any rules
// are violated, the result is a list of violation errors wrapped in
// BatchGetScriptFirstDubbingReqMultiError, or nil if none found.
func (m *BatchGetScriptFirstDubbingReq) ValidateAll() error {
	return m.validate(true)
}

func (m *BatchGetScriptFirstDubbingReq) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if l := len(m.GetScriptIds()); l < 1 || l > 50 {
		err := BatchGetScriptFirstDubbingReqValidationError{
			field:  "ScriptIds",
			reason: "value must contain between 1 and 50 items, inclusive",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if len(errors) > 0 {
		return BatchGetScriptFirstDubbingReqMultiError(errors)
	}

	return nil
}

// BatchGetScriptFirstDubbingReqMultiError is an error wrapping multiple
// validation errors returned by BatchGetScriptFirstDubbingReq.ValidateAll()
// if the designated constraints aren't met.
type BatchGetScriptFirstDubbingReqMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m BatchGetScriptFirstDubbingReqMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m BatchGetScriptFirstDubbingReqMultiError) AllErrors() []error { return m }

// BatchGetScriptFirstDubbingReqValidationError is the validation error
// returned by BatchGetScriptFirstDubbingReq.Validate if the designated
// constraints aren't met.
type BatchGetScriptFirstDubbingReqValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e BatchGetScriptFirstDubbingReqValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e BatchGetScriptFirstDubbingReqValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e BatchGetScriptFirstDubbingReqValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e BatchGetScriptFirstDubbingReqValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e BatchGetScriptFirstDubbingReqValidationError) ErrorName() string {
	return "BatchGetScriptFirstDubbingReqValidationError"
}

// Error satisfies the builtin error interface
func (e BatchGetScriptFirstDubbingReqValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sBatchGetScriptFirstDubbingReq.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = BatchGetScriptFirstDubbingReqValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = BatchGetScriptFirstDubbingReqValidationError{}

// Validate checks the field values on BatchGetScriptFirstDubbingResp with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *BatchGetScriptFirstDubbingResp) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on BatchGetScriptFirstDubbingResp with
// the rules defined in the proto definition for this message. If any rules
// are violated, the result is a list of violation errors wrapped in
// BatchGetScriptFirstDubbingRespMultiError, or nil if none found.
func (m *BatchGetScriptFirstDubbingResp) ValidateAll() error {
	return m.validate(true)
}

func (m *BatchGetScriptFirstDubbingResp) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Code

	// no validation rules for Msg

	if all {
		switch v := interface{}(m.GetData()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, BatchGetScriptFirstDubbingRespValidationError{
					field:  "Data",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, BatchGetScriptFirstDubbingRespValidationError{
					field:  "Data",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetData()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return BatchGetScriptFirstDubbingRespValidationError{
				field:  "Data",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return BatchGetScriptFirstDubbingRespMultiError(errors)
	}

	return nil
}

// BatchGetScriptFirstDubbingRespMultiError is an error wrapping multiple
// validation errors returned by BatchGetScriptFirstDubbingResp.ValidateAll()
// if the designated constraints aren't met.
type BatchGetScriptFirstDubbingRespMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m BatchGetScriptFirstDubbingRespMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m BatchGetScriptFirstDubbingRespMultiError) AllErrors() []error { return m }

// BatchGetScriptFirstDubbingRespValidationError is the validation error
// returned by BatchGetScriptFirstDubbingResp.Validate if the designated
// constraints aren't met.
type BatchGetScriptFirstDubbingRespValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e BatchGetScriptFirstDubbingRespValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e BatchGetScriptFirstDubbingRespValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e BatchGetScriptFirstDubbingRespValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e BatchGetScriptFirstDubbingRespValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e BatchGetScriptFirstDubbingRespValidationError) ErrorName() string {
	return "BatchGetScriptFirstDubbingRespValidationError"
}

// Error satisfies the builtin error interface
func (e BatchGetScriptFirstDubbingRespValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sBatchGetScriptFirstDubbingResp.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = BatchGetScriptFirstDubbingRespValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = BatchGetScriptFirstDubbingRespValidationError{}

// Validate checks the field values on CreateDubbingReq with the rules defined
// in the proto definition for this message. If any rules are violated, the
// first error encountered is returned, or nil if there are no violations.
func (m *CreateDubbingReq) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on CreateDubbingReq with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// CreateDubbingReqMultiError, or nil if none found.
func (m *CreateDubbingReq) ValidateAll() error {
	return m.validate(true)
}

func (m *CreateDubbingReq) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if m.GetScriptId() <= 0 {
		err := CreateDubbingReqValidationError{
			field:  "ScriptId",
			reason: "value must be greater than 0",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	{
		sorted_keys := make([]int64, len(m.GetLineDubbings()))
		i := 0
		for key := range m.GetLineDubbings() {
			sorted_keys[i] = key
			i++
		}
		sort.Slice(sorted_keys, func(i, j int) bool { return sorted_keys[i] < sorted_keys[j] })
		for _, key := range sorted_keys {
			val := m.GetLineDubbings()[key]
			_ = val

			// no validation rules for LineDubbings[key]

			if all {
				switch v := interface{}(val).(type) {
				case interface{ ValidateAll() error }:
					if err := v.ValidateAll(); err != nil {
						errors = append(errors, CreateDubbingReqValidationError{
							field:  fmt.Sprintf("LineDubbings[%v]", key),
							reason: "embedded message failed validation",
							cause:  err,
						})
					}
				case interface{ Validate() error }:
					if err := v.Validate(); err != nil {
						errors = append(errors, CreateDubbingReqValidationError{
							field:  fmt.Sprintf("LineDubbings[%v]", key),
							reason: "embedded message failed validation",
							cause:  err,
						})
					}
				}
			} else if v, ok := interface{}(val).(interface{ Validate() error }); ok {
				if err := v.Validate(); err != nil {
					return CreateDubbingReqValidationError{
						field:  fmt.Sprintf("LineDubbings[%v]", key),
						reason: "embedded message failed validation",
						cause:  err,
					}
				}
			}

		}
	}

	if len(errors) > 0 {
		return CreateDubbingReqMultiError(errors)
	}

	return nil
}

// CreateDubbingReqMultiError is an error wrapping multiple validation errors
// returned by CreateDubbingReq.ValidateAll() if the designated constraints
// aren't met.
type CreateDubbingReqMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m CreateDubbingReqMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m CreateDubbingReqMultiError) AllErrors() []error { return m }

// CreateDubbingReqValidationError is the validation error returned by
// CreateDubbingReq.Validate if the designated constraints aren't met.
type CreateDubbingReqValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e CreateDubbingReqValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e CreateDubbingReqValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e CreateDubbingReqValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e CreateDubbingReqValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e CreateDubbingReqValidationError) ErrorName() string { return "CreateDubbingReqValidationError" }

// Error satisfies the builtin error interface
func (e CreateDubbingReqValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sCreateDubbingReq.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = CreateDubbingReqValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = CreateDubbingReqValidationError{}

// Validate checks the field values on CreateDubbingResp with the rules defined
// in the proto definition for this message. If any rules are violated, the
// first error encountered is returned, or nil if there are no violations.
func (m *CreateDubbingResp) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on CreateDubbingResp with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// CreateDubbingRespMultiError, or nil if none found.
func (m *CreateDubbingResp) ValidateAll() error {
	return m.validate(true)
}

func (m *CreateDubbingResp) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Code

	// no validation rules for Msg

	if all {
		switch v := interface{}(m.GetData()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, CreateDubbingRespValidationError{
					field:  "Data",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, CreateDubbingRespValidationError{
					field:  "Data",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetData()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return CreateDubbingRespValidationError{
				field:  "Data",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return CreateDubbingRespMultiError(errors)
	}

	return nil
}

// CreateDubbingRespMultiError is an error wrapping multiple validation errors
// returned by CreateDubbingResp.ValidateAll() if the designated constraints
// aren't met.
type CreateDubbingRespMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m CreateDubbingRespMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m CreateDubbingRespMultiError) AllErrors() []error { return m }

// CreateDubbingRespValidationError is the validation error returned by
// CreateDubbingResp.Validate if the designated constraints aren't met.
type CreateDubbingRespValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e CreateDubbingRespValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e CreateDubbingRespValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e CreateDubbingRespValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e CreateDubbingRespValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e CreateDubbingRespValidationError) ErrorName() string {
	return "CreateDubbingRespValidationError"
}

// Error satisfies the builtin error interface
func (e CreateDubbingRespValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sCreateDubbingResp.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = CreateDubbingRespValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = CreateDubbingRespValidationError{}

// Validate checks the field values on DeleteDubbingRecordReq with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *DeleteDubbingRecordReq) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on DeleteDubbingRecordReq with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// DeleteDubbingRecordReqMultiError, or nil if none found.
func (m *DeleteDubbingRecordReq) ValidateAll() error {
	return m.validate(true)
}

func (m *DeleteDubbingRecordReq) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if m.GetDubbingRecordId() <= 0 {
		err := DeleteDubbingRecordReqValidationError{
			field:  "DubbingRecordId",
			reason: "value must be greater than 0",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if len(errors) > 0 {
		return DeleteDubbingRecordReqMultiError(errors)
	}

	return nil
}

// DeleteDubbingRecordReqMultiError is an error wrapping multiple validation
// errors returned by DeleteDubbingRecordReq.ValidateAll() if the designated
// constraints aren't met.
type DeleteDubbingRecordReqMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m DeleteDubbingRecordReqMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m DeleteDubbingRecordReqMultiError) AllErrors() []error { return m }

// DeleteDubbingRecordReqValidationError is the validation error returned by
// DeleteDubbingRecordReq.Validate if the designated constraints aren't met.
type DeleteDubbingRecordReqValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e DeleteDubbingRecordReqValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e DeleteDubbingRecordReqValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e DeleteDubbingRecordReqValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e DeleteDubbingRecordReqValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e DeleteDubbingRecordReqValidationError) ErrorName() string {
	return "DeleteDubbingRecordReqValidationError"
}

// Error satisfies the builtin error interface
func (e DeleteDubbingRecordReqValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sDeleteDubbingRecordReq.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = DeleteDubbingRecordReqValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = DeleteDubbingRecordReqValidationError{}

// Validate checks the field values on DeleteDubbingRecordResp with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *DeleteDubbingRecordResp) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on DeleteDubbingRecordResp with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// DeleteDubbingRecordRespMultiError, or nil if none found.
func (m *DeleteDubbingRecordResp) ValidateAll() error {
	return m.validate(true)
}

func (m *DeleteDubbingRecordResp) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Code

	// no validation rules for Msg

	if len(errors) > 0 {
		return DeleteDubbingRecordRespMultiError(errors)
	}

	return nil
}

// DeleteDubbingRecordRespMultiError is an error wrapping multiple validation
// errors returned by DeleteDubbingRecordResp.ValidateAll() if the designated
// constraints aren't met.
type DeleteDubbingRecordRespMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m DeleteDubbingRecordRespMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m DeleteDubbingRecordRespMultiError) AllErrors() []error { return m }

// DeleteDubbingRecordRespValidationError is the validation error returned by
// DeleteDubbingRecordResp.Validate if the designated constraints aren't met.
type DeleteDubbingRecordRespValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e DeleteDubbingRecordRespValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e DeleteDubbingRecordRespValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e DeleteDubbingRecordRespValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e DeleteDubbingRecordRespValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e DeleteDubbingRecordRespValidationError) ErrorName() string {
	return "DeleteDubbingRecordRespValidationError"
}

// Error satisfies the builtin error interface
func (e DeleteDubbingRecordRespValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sDeleteDubbingRecordResp.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = DeleteDubbingRecordRespValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = DeleteDubbingRecordRespValidationError{}

// Validate checks the field values on GetCommentListReq with the rules defined
// in the proto definition for this message. If any rules are violated, the
// first error encountered is returned, or nil if there are no violations.
func (m *GetCommentListReq) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GetCommentListReq with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// GetCommentListReqMultiError, or nil if none found.
func (m *GetCommentListReq) ValidateAll() error {
	return m.validate(true)
}

func (m *GetCommentListReq) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for CommentType

	// no validation rules for ScriptId

	// no validation rules for DubbingId

	// no validation rules for ParentId

	// no validation rules for Page

	// no validation rules for PageSize

	if len(errors) > 0 {
		return GetCommentListReqMultiError(errors)
	}

	return nil
}

// GetCommentListReqMultiError is an error wrapping multiple validation errors
// returned by GetCommentListReq.ValidateAll() if the designated constraints
// aren't met.
type GetCommentListReqMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetCommentListReqMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetCommentListReqMultiError) AllErrors() []error { return m }

// GetCommentListReqValidationError is the validation error returned by
// GetCommentListReq.Validate if the designated constraints aren't met.
type GetCommentListReqValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetCommentListReqValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetCommentListReqValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetCommentListReqValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetCommentListReqValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetCommentListReqValidationError) ErrorName() string {
	return "GetCommentListReqValidationError"
}

// Error satisfies the builtin error interface
func (e GetCommentListReqValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetCommentListReq.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetCommentListReqValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetCommentListReqValidationError{}

// Validate checks the field values on GetCommentListResp with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *GetCommentListResp) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GetCommentListResp with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// GetCommentListRespMultiError, or nil if none found.
func (m *GetCommentListResp) ValidateAll() error {
	return m.validate(true)
}

func (m *GetCommentListResp) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Code

	// no validation rules for Msg

	if all {
		switch v := interface{}(m.GetData()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetCommentListRespValidationError{
					field:  "Data",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetCommentListRespValidationError{
					field:  "Data",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetData()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetCommentListRespValidationError{
				field:  "Data",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return GetCommentListRespMultiError(errors)
	}

	return nil
}

// GetCommentListRespMultiError is an error wrapping multiple validation errors
// returned by GetCommentListResp.ValidateAll() if the designated constraints
// aren't met.
type GetCommentListRespMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetCommentListRespMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetCommentListRespMultiError) AllErrors() []error { return m }

// GetCommentListRespValidationError is the validation error returned by
// GetCommentListResp.Validate if the designated constraints aren't met.
type GetCommentListRespValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetCommentListRespValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetCommentListRespValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetCommentListRespValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetCommentListRespValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetCommentListRespValidationError) ErrorName() string {
	return "GetCommentListRespValidationError"
}

// Error satisfies the builtin error interface
func (e GetCommentListRespValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetCommentListResp.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetCommentListRespValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetCommentListRespValidationError{}

// Validate checks the field values on GetCommentRepliesReq with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *GetCommentRepliesReq) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GetCommentRepliesReq with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// GetCommentRepliesReqMultiError, or nil if none found.
func (m *GetCommentRepliesReq) ValidateAll() error {
	return m.validate(true)
}

func (m *GetCommentRepliesReq) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if m.GetParentId() <= 0 {
		err := GetCommentRepliesReqValidationError{
			field:  "ParentId",
			reason: "value must be greater than 0",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	// no validation rules for Page

	// no validation rules for PageSize

	if len(errors) > 0 {
		return GetCommentRepliesReqMultiError(errors)
	}

	return nil
}

// GetCommentRepliesReqMultiError is an error wrapping multiple validation
// errors returned by GetCommentRepliesReq.ValidateAll() if the designated
// constraints aren't met.
type GetCommentRepliesReqMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetCommentRepliesReqMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetCommentRepliesReqMultiError) AllErrors() []error { return m }

// GetCommentRepliesReqValidationError is the validation error returned by
// GetCommentRepliesReq.Validate if the designated constraints aren't met.
type GetCommentRepliesReqValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetCommentRepliesReqValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetCommentRepliesReqValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetCommentRepliesReqValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetCommentRepliesReqValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetCommentRepliesReqValidationError) ErrorName() string {
	return "GetCommentRepliesReqValidationError"
}

// Error satisfies the builtin error interface
func (e GetCommentRepliesReqValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetCommentRepliesReq.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetCommentRepliesReqValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetCommentRepliesReqValidationError{}

// Validate checks the field values on GetCommentRepliesResp with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *GetCommentRepliesResp) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GetCommentRepliesResp with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// GetCommentRepliesRespMultiError, or nil if none found.
func (m *GetCommentRepliesResp) ValidateAll() error {
	return m.validate(true)
}

func (m *GetCommentRepliesResp) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Code

	// no validation rules for Msg

	if all {
		switch v := interface{}(m.GetData()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetCommentRepliesRespValidationError{
					field:  "Data",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetCommentRepliesRespValidationError{
					field:  "Data",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetData()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetCommentRepliesRespValidationError{
				field:  "Data",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return GetCommentRepliesRespMultiError(errors)
	}

	return nil
}

// GetCommentRepliesRespMultiError is an error wrapping multiple validation
// errors returned by GetCommentRepliesResp.ValidateAll() if the designated
// constraints aren't met.
type GetCommentRepliesRespMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetCommentRepliesRespMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetCommentRepliesRespMultiError) AllErrors() []error { return m }

// GetCommentRepliesRespValidationError is the validation error returned by
// GetCommentRepliesResp.Validate if the designated constraints aren't met.
type GetCommentRepliesRespValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetCommentRepliesRespValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetCommentRepliesRespValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetCommentRepliesRespValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetCommentRepliesRespValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetCommentRepliesRespValidationError) ErrorName() string {
	return "GetCommentRepliesRespValidationError"
}

// Error satisfies the builtin error interface
func (e GetCommentRepliesRespValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetCommentRepliesResp.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetCommentRepliesRespValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetCommentRepliesRespValidationError{}

// Validate checks the field values on CreateCommentReq with the rules defined
// in the proto definition for this message. If any rules are violated, the
// first error encountered is returned, or nil if there are no violations.
func (m *CreateCommentReq) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on CreateCommentReq with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// CreateCommentReqMultiError, or nil if none found.
func (m *CreateCommentReq) ValidateAll() error {
	return m.validate(true)
}

func (m *CreateCommentReq) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if _, ok := svcscript.CommentType_name[int32(m.GetCommentType())]; !ok {
		err := CreateCommentReqValidationError{
			field:  "CommentType",
			reason: "value must be one of the defined enum values",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	// no validation rules for ParentId

	// no validation rules for ScriptId

	// no validation rules for DubbingId

	// no validation rules for CharacterId

	// no validation rules for CharacterAssetId

	// no validation rules for ContentType

	// no validation rules for Content

	// no validation rules for VoiceUrl

	// no validation rules for VoiceDuration

	// no validation rules for OriginalVoiceUrl

	// no validation rules for OriginalVoiceDuration

	// no validation rules for SvcVoiceUrl

	// no validation rules for SvcVoiceDuration

	if len(errors) > 0 {
		return CreateCommentReqMultiError(errors)
	}

	return nil
}

// CreateCommentReqMultiError is an error wrapping multiple validation errors
// returned by CreateCommentReq.ValidateAll() if the designated constraints
// aren't met.
type CreateCommentReqMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m CreateCommentReqMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m CreateCommentReqMultiError) AllErrors() []error { return m }

// CreateCommentReqValidationError is the validation error returned by
// CreateCommentReq.Validate if the designated constraints aren't met.
type CreateCommentReqValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e CreateCommentReqValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e CreateCommentReqValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e CreateCommentReqValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e CreateCommentReqValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e CreateCommentReqValidationError) ErrorName() string { return "CreateCommentReqValidationError" }

// Error satisfies the builtin error interface
func (e CreateCommentReqValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sCreateCommentReq.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = CreateCommentReqValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = CreateCommentReqValidationError{}

// Validate checks the field values on CreateCommentResp with the rules defined
// in the proto definition for this message. If any rules are violated, the
// first error encountered is returned, or nil if there are no violations.
func (m *CreateCommentResp) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on CreateCommentResp with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// CreateCommentRespMultiError, or nil if none found.
func (m *CreateCommentResp) ValidateAll() error {
	return m.validate(true)
}

func (m *CreateCommentResp) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Code

	// no validation rules for Msg

	if all {
		switch v := interface{}(m.GetData()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, CreateCommentRespValidationError{
					field:  "Data",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, CreateCommentRespValidationError{
					field:  "Data",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetData()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return CreateCommentRespValidationError{
				field:  "Data",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return CreateCommentRespMultiError(errors)
	}

	return nil
}

// CreateCommentRespMultiError is an error wrapping multiple validation errors
// returned by CreateCommentResp.ValidateAll() if the designated constraints
// aren't met.
type CreateCommentRespMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m CreateCommentRespMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m CreateCommentRespMultiError) AllErrors() []error { return m }

// CreateCommentRespValidationError is the validation error returned by
// CreateCommentResp.Validate if the designated constraints aren't met.
type CreateCommentRespValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e CreateCommentRespValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e CreateCommentRespValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e CreateCommentRespValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e CreateCommentRespValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e CreateCommentRespValidationError) ErrorName() string {
	return "CreateCommentRespValidationError"
}

// Error satisfies the builtin error interface
func (e CreateCommentRespValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sCreateCommentResp.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = CreateCommentRespValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = CreateCommentRespValidationError{}

// Validate checks the field values on SetCommentTopReq with the rules defined
// in the proto definition for this message. If any rules are violated, the
// first error encountered is returned, or nil if there are no violations.
func (m *SetCommentTopReq) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on SetCommentTopReq with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// SetCommentTopReqMultiError, or nil if none found.
func (m *SetCommentTopReq) ValidateAll() error {
	return m.validate(true)
}

func (m *SetCommentTopReq) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if m.GetCommentId() <= 0 {
		err := SetCommentTopReqValidationError{
			field:  "CommentId",
			reason: "value must be greater than 0",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	// no validation rules for IsTop

	if len(errors) > 0 {
		return SetCommentTopReqMultiError(errors)
	}

	return nil
}

// SetCommentTopReqMultiError is an error wrapping multiple validation errors
// returned by SetCommentTopReq.ValidateAll() if the designated constraints
// aren't met.
type SetCommentTopReqMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m SetCommentTopReqMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m SetCommentTopReqMultiError) AllErrors() []error { return m }

// SetCommentTopReqValidationError is the validation error returned by
// SetCommentTopReq.Validate if the designated constraints aren't met.
type SetCommentTopReqValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e SetCommentTopReqValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e SetCommentTopReqValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e SetCommentTopReqValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e SetCommentTopReqValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e SetCommentTopReqValidationError) ErrorName() string { return "SetCommentTopReqValidationError" }

// Error satisfies the builtin error interface
func (e SetCommentTopReqValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sSetCommentTopReq.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = SetCommentTopReqValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = SetCommentTopReqValidationError{}

// Validate checks the field values on SetCommentTopResp with the rules defined
// in the proto definition for this message. If any rules are violated, the
// first error encountered is returned, or nil if there are no violations.
func (m *SetCommentTopResp) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on SetCommentTopResp with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// SetCommentTopRespMultiError, or nil if none found.
func (m *SetCommentTopResp) ValidateAll() error {
	return m.validate(true)
}

func (m *SetCommentTopResp) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Code

	// no validation rules for Msg

	if len(errors) > 0 {
		return SetCommentTopRespMultiError(errors)
	}

	return nil
}

// SetCommentTopRespMultiError is an error wrapping multiple validation errors
// returned by SetCommentTopResp.ValidateAll() if the designated constraints
// aren't met.
type SetCommentTopRespMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m SetCommentTopRespMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m SetCommentTopRespMultiError) AllErrors() []error { return m }

// SetCommentTopRespValidationError is the validation error returned by
// SetCommentTopResp.Validate if the designated constraints aren't met.
type SetCommentTopRespValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e SetCommentTopRespValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e SetCommentTopRespValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e SetCommentTopRespValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e SetCommentTopRespValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e SetCommentTopRespValidationError) ErrorName() string {
	return "SetCommentTopRespValidationError"
}

// Error satisfies the builtin error interface
func (e SetCommentTopRespValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sSetCommentTopResp.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = SetCommentTopRespValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = SetCommentTopRespValidationError{}

// Validate checks the field values on DeleteCommentReq with the rules defined
// in the proto definition for this message. If any rules are violated, the
// first error encountered is returned, or nil if there are no violations.
func (m *DeleteCommentReq) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on DeleteCommentReq with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// DeleteCommentReqMultiError, or nil if none found.
func (m *DeleteCommentReq) ValidateAll() error {
	return m.validate(true)
}

func (m *DeleteCommentReq) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if m.GetCommentId() <= 0 {
		err := DeleteCommentReqValidationError{
			field:  "CommentId",
			reason: "value must be greater than 0",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if len(errors) > 0 {
		return DeleteCommentReqMultiError(errors)
	}

	return nil
}

// DeleteCommentReqMultiError is an error wrapping multiple validation errors
// returned by DeleteCommentReq.ValidateAll() if the designated constraints
// aren't met.
type DeleteCommentReqMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m DeleteCommentReqMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m DeleteCommentReqMultiError) AllErrors() []error { return m }

// DeleteCommentReqValidationError is the validation error returned by
// DeleteCommentReq.Validate if the designated constraints aren't met.
type DeleteCommentReqValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e DeleteCommentReqValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e DeleteCommentReqValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e DeleteCommentReqValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e DeleteCommentReqValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e DeleteCommentReqValidationError) ErrorName() string { return "DeleteCommentReqValidationError" }

// Error satisfies the builtin error interface
func (e DeleteCommentReqValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sDeleteCommentReq.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = DeleteCommentReqValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = DeleteCommentReqValidationError{}

// Validate checks the field values on DeleteCommentResp with the rules defined
// in the proto definition for this message. If any rules are violated, the
// first error encountered is returned, or nil if there are no violations.
func (m *DeleteCommentResp) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on DeleteCommentResp with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// DeleteCommentRespMultiError, or nil if none found.
func (m *DeleteCommentResp) ValidateAll() error {
	return m.validate(true)
}

func (m *DeleteCommentResp) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Code

	// no validation rules for Msg

	if len(errors) > 0 {
		return DeleteCommentRespMultiError(errors)
	}

	return nil
}

// DeleteCommentRespMultiError is an error wrapping multiple validation errors
// returned by DeleteCommentResp.ValidateAll() if the designated constraints
// aren't met.
type DeleteCommentRespMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m DeleteCommentRespMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m DeleteCommentRespMultiError) AllErrors() []error { return m }

// DeleteCommentRespValidationError is the validation error returned by
// DeleteCommentResp.Validate if the designated constraints aren't met.
type DeleteCommentRespValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e DeleteCommentRespValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e DeleteCommentRespValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e DeleteCommentRespValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e DeleteCommentRespValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e DeleteCommentRespValidationError) ErrorName() string {
	return "DeleteCommentRespValidationError"
}

// Error satisfies the builtin error interface
func (e DeleteCommentRespValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sDeleteCommentResp.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = DeleteCommentRespValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = DeleteCommentRespValidationError{}

// Validate checks the field values on GetCommentASRReq with the rules defined
// in the proto definition for this message. If any rules are violated, the
// first error encountered is returned, or nil if there are no violations.
func (m *GetCommentASRReq) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GetCommentASRReq with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// GetCommentASRReqMultiError, or nil if none found.
func (m *GetCommentASRReq) ValidateAll() error {
	return m.validate(true)
}

func (m *GetCommentASRReq) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if m.GetCommentId() <= 0 {
		err := GetCommentASRReqValidationError{
			field:  "CommentId",
			reason: "value must be greater than 0",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if len(errors) > 0 {
		return GetCommentASRReqMultiError(errors)
	}

	return nil
}

// GetCommentASRReqMultiError is an error wrapping multiple validation errors
// returned by GetCommentASRReq.ValidateAll() if the designated constraints
// aren't met.
type GetCommentASRReqMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetCommentASRReqMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetCommentASRReqMultiError) AllErrors() []error { return m }

// GetCommentASRReqValidationError is the validation error returned by
// GetCommentASRReq.Validate if the designated constraints aren't met.
type GetCommentASRReqValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetCommentASRReqValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetCommentASRReqValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetCommentASRReqValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetCommentASRReqValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetCommentASRReqValidationError) ErrorName() string { return "GetCommentASRReqValidationError" }

// Error satisfies the builtin error interface
func (e GetCommentASRReqValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetCommentASRReq.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetCommentASRReqValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetCommentASRReqValidationError{}

// Validate checks the field values on GetCommentASRResp with the rules defined
// in the proto definition for this message. If any rules are violated, the
// first error encountered is returned, or nil if there are no violations.
func (m *GetCommentASRResp) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GetCommentASRResp with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// GetCommentASRRespMultiError, or nil if none found.
func (m *GetCommentASRResp) ValidateAll() error {
	return m.validate(true)
}

func (m *GetCommentASRResp) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Code

	// no validation rules for Msg

	if all {
		switch v := interface{}(m.GetData()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetCommentASRRespValidationError{
					field:  "Data",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetCommentASRRespValidationError{
					field:  "Data",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetData()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetCommentASRRespValidationError{
				field:  "Data",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return GetCommentASRRespMultiError(errors)
	}

	return nil
}

// GetCommentASRRespMultiError is an error wrapping multiple validation errors
// returned by GetCommentASRResp.ValidateAll() if the designated constraints
// aren't met.
type GetCommentASRRespMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetCommentASRRespMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetCommentASRRespMultiError) AllErrors() []error { return m }

// GetCommentASRRespValidationError is the validation error returned by
// GetCommentASRResp.Validate if the designated constraints aren't met.
type GetCommentASRRespValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetCommentASRRespValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetCommentASRRespValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetCommentASRRespValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetCommentASRRespValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetCommentASRRespValidationError) ErrorName() string {
	return "GetCommentASRRespValidationError"
}

// Error satisfies the builtin error interface
func (e GetCommentASRRespValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetCommentASRResp.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetCommentASRRespValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetCommentASRRespValidationError{}

// Validate checks the field values on LikeReq with the rules defined in the
// proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *LikeReq) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on LikeReq with the rules defined in the
// proto definition for this message. If any rules are violated, the result is
// a list of violation errors wrapped in LikeReqMultiError, or nil if none found.
func (m *LikeReq) ValidateAll() error {
	return m.validate(true)
}

func (m *LikeReq) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if _, ok := svcscript.LikeType_name[int32(m.GetLikeType())]; !ok {
		err := LikeReqValidationError{
			field:  "LikeType",
			reason: "value must be one of the defined enum values",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if m.GetTargetId() <= 0 {
		err := LikeReqValidationError{
			field:  "TargetId",
			reason: "value must be greater than 0",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if len(errors) > 0 {
		return LikeReqMultiError(errors)
	}

	return nil
}

// LikeReqMultiError is an error wrapping multiple validation errors returned
// by LikeReq.ValidateAll() if the designated constraints aren't met.
type LikeReqMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m LikeReqMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m LikeReqMultiError) AllErrors() []error { return m }

// LikeReqValidationError is the validation error returned by LikeReq.Validate
// if the designated constraints aren't met.
type LikeReqValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e LikeReqValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e LikeReqValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e LikeReqValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e LikeReqValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e LikeReqValidationError) ErrorName() string { return "LikeReqValidationError" }

// Error satisfies the builtin error interface
func (e LikeReqValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sLikeReq.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = LikeReqValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = LikeReqValidationError{}

// Validate checks the field values on LikeResp with the rules defined in the
// proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *LikeResp) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on LikeResp with the rules defined in
// the proto definition for this message. If any rules are violated, the
// result is a list of violation errors wrapped in LikeRespMultiError, or nil
// if none found.
func (m *LikeResp) ValidateAll() error {
	return m.validate(true)
}

func (m *LikeResp) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Code

	// no validation rules for Msg

	if len(errors) > 0 {
		return LikeRespMultiError(errors)
	}

	return nil
}

// LikeRespMultiError is an error wrapping multiple validation errors returned
// by LikeResp.ValidateAll() if the designated constraints aren't met.
type LikeRespMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m LikeRespMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m LikeRespMultiError) AllErrors() []error { return m }

// LikeRespValidationError is the validation error returned by
// LikeResp.Validate if the designated constraints aren't met.
type LikeRespValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e LikeRespValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e LikeRespValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e LikeRespValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e LikeRespValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e LikeRespValidationError) ErrorName() string { return "LikeRespValidationError" }

// Error satisfies the builtin error interface
func (e LikeRespValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sLikeResp.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = LikeRespValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = LikeRespValidationError{}

// Validate checks the field values on UnlikeReq with the rules defined in the
// proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *UnlikeReq) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on UnlikeReq with the rules defined in
// the proto definition for this message. If any rules are violated, the
// result is a list of violation errors wrapped in UnlikeReqMultiError, or nil
// if none found.
func (m *UnlikeReq) ValidateAll() error {
	return m.validate(true)
}

func (m *UnlikeReq) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if _, ok := svcscript.LikeType_name[int32(m.GetLikeType())]; !ok {
		err := UnlikeReqValidationError{
			field:  "LikeType",
			reason: "value must be one of the defined enum values",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if m.GetTargetId() <= 0 {
		err := UnlikeReqValidationError{
			field:  "TargetId",
			reason: "value must be greater than 0",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if len(errors) > 0 {
		return UnlikeReqMultiError(errors)
	}

	return nil
}

// UnlikeReqMultiError is an error wrapping multiple validation errors returned
// by UnlikeReq.ValidateAll() if the designated constraints aren't met.
type UnlikeReqMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m UnlikeReqMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m UnlikeReqMultiError) AllErrors() []error { return m }

// UnlikeReqValidationError is the validation error returned by
// UnlikeReq.Validate if the designated constraints aren't met.
type UnlikeReqValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e UnlikeReqValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e UnlikeReqValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e UnlikeReqValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e UnlikeReqValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e UnlikeReqValidationError) ErrorName() string { return "UnlikeReqValidationError" }

// Error satisfies the builtin error interface
func (e UnlikeReqValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sUnlikeReq.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = UnlikeReqValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = UnlikeReqValidationError{}

// Validate checks the field values on UnlikeResp with the rules defined in the
// proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *UnlikeResp) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on UnlikeResp with the rules defined in
// the proto definition for this message. If any rules are violated, the
// result is a list of violation errors wrapped in UnlikeRespMultiError, or
// nil if none found.
func (m *UnlikeResp) ValidateAll() error {
	return m.validate(true)
}

func (m *UnlikeResp) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Code

	// no validation rules for Msg

	if len(errors) > 0 {
		return UnlikeRespMultiError(errors)
	}

	return nil
}

// UnlikeRespMultiError is an error wrapping multiple validation errors
// returned by UnlikeResp.ValidateAll() if the designated constraints aren't met.
type UnlikeRespMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m UnlikeRespMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m UnlikeRespMultiError) AllErrors() []error { return m }

// UnlikeRespValidationError is the validation error returned by
// UnlikeResp.Validate if the designated constraints aren't met.
type UnlikeRespValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e UnlikeRespValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e UnlikeRespValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e UnlikeRespValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e UnlikeRespValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e UnlikeRespValidationError) ErrorName() string { return "UnlikeRespValidationError" }

// Error satisfies the builtin error interface
func (e UnlikeRespValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sUnlikeResp.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = UnlikeRespValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = UnlikeRespValidationError{}

// Validate checks the field values on BatchGetLikesReq with the rules defined
// in the proto definition for this message. If any rules are violated, the
// first error encountered is returned, or nil if there are no violations.
func (m *BatchGetLikesReq) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on BatchGetLikesReq with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// BatchGetLikesReqMultiError, or nil if none found.
func (m *BatchGetLikesReq) ValidateAll() error {
	return m.validate(true)
}

func (m *BatchGetLikesReq) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if _, ok := svcscript.LikeType_name[int32(m.GetLikeType())]; !ok {
		err := BatchGetLikesReqValidationError{
			field:  "LikeType",
			reason: "value must be one of the defined enum values",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if len(m.GetTargetIds()) < 1 {
		err := BatchGetLikesReqValidationError{
			field:  "TargetIds",
			reason: "value must contain at least 1 item(s)",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if len(errors) > 0 {
		return BatchGetLikesReqMultiError(errors)
	}

	return nil
}

// BatchGetLikesReqMultiError is an error wrapping multiple validation errors
// returned by BatchGetLikesReq.ValidateAll() if the designated constraints
// aren't met.
type BatchGetLikesReqMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m BatchGetLikesReqMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m BatchGetLikesReqMultiError) AllErrors() []error { return m }

// BatchGetLikesReqValidationError is the validation error returned by
// BatchGetLikesReq.Validate if the designated constraints aren't met.
type BatchGetLikesReqValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e BatchGetLikesReqValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e BatchGetLikesReqValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e BatchGetLikesReqValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e BatchGetLikesReqValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e BatchGetLikesReqValidationError) ErrorName() string { return "BatchGetLikesReqValidationError" }

// Error satisfies the builtin error interface
func (e BatchGetLikesReqValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sBatchGetLikesReq.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = BatchGetLikesReqValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = BatchGetLikesReqValidationError{}

// Validate checks the field values on BatchGetLikesResp with the rules defined
// in the proto definition for this message. If any rules are violated, the
// first error encountered is returned, or nil if there are no violations.
func (m *BatchGetLikesResp) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on BatchGetLikesResp with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// BatchGetLikesRespMultiError, or nil if none found.
func (m *BatchGetLikesResp) ValidateAll() error {
	return m.validate(true)
}

func (m *BatchGetLikesResp) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Code

	// no validation rules for Msg

	// no validation rules for Data

	if len(errors) > 0 {
		return BatchGetLikesRespMultiError(errors)
	}

	return nil
}

// BatchGetLikesRespMultiError is an error wrapping multiple validation errors
// returned by BatchGetLikesResp.ValidateAll() if the designated constraints
// aren't met.
type BatchGetLikesRespMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m BatchGetLikesRespMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m BatchGetLikesRespMultiError) AllErrors() []error { return m }

// BatchGetLikesRespValidationError is the validation error returned by
// BatchGetLikesResp.Validate if the designated constraints aren't met.
type BatchGetLikesRespValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e BatchGetLikesRespValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e BatchGetLikesRespValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e BatchGetLikesRespValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e BatchGetLikesRespValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e BatchGetLikesRespValidationError) ErrorName() string {
	return "BatchGetLikesRespValidationError"
}

// Error satisfies the builtin error interface
func (e BatchGetLikesRespValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sBatchGetLikesResp.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = BatchGetLikesRespValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = BatchGetLikesRespValidationError{}

// Validate checks the field values on GetReportReasonsReq with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *GetReportReasonsReq) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GetReportReasonsReq with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// GetReportReasonsReqMultiError, or nil if none found.
func (m *GetReportReasonsReq) ValidateAll() error {
	return m.validate(true)
}

func (m *GetReportReasonsReq) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if _, ok := svcscript.ReportType_name[int32(m.GetReportType())]; !ok {
		err := GetReportReasonsReqValidationError{
			field:  "ReportType",
			reason: "value must be one of the defined enum values",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if len(errors) > 0 {
		return GetReportReasonsReqMultiError(errors)
	}

	return nil
}

// GetReportReasonsReqMultiError is an error wrapping multiple validation
// errors returned by GetReportReasonsReq.ValidateAll() if the designated
// constraints aren't met.
type GetReportReasonsReqMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetReportReasonsReqMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetReportReasonsReqMultiError) AllErrors() []error { return m }

// GetReportReasonsReqValidationError is the validation error returned by
// GetReportReasonsReq.Validate if the designated constraints aren't met.
type GetReportReasonsReqValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetReportReasonsReqValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetReportReasonsReqValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetReportReasonsReqValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetReportReasonsReqValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetReportReasonsReqValidationError) ErrorName() string {
	return "GetReportReasonsReqValidationError"
}

// Error satisfies the builtin error interface
func (e GetReportReasonsReqValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetReportReasonsReq.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetReportReasonsReqValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetReportReasonsReqValidationError{}

// Validate checks the field values on GetReportReasonsResp with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *GetReportReasonsResp) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GetReportReasonsResp with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// GetReportReasonsRespMultiError, or nil if none found.
func (m *GetReportReasonsResp) ValidateAll() error {
	return m.validate(true)
}

func (m *GetReportReasonsResp) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Code

	// no validation rules for Msg

	if all {
		switch v := interface{}(m.GetData()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetReportReasonsRespValidationError{
					field:  "Data",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetReportReasonsRespValidationError{
					field:  "Data",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetData()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetReportReasonsRespValidationError{
				field:  "Data",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return GetReportReasonsRespMultiError(errors)
	}

	return nil
}

// GetReportReasonsRespMultiError is an error wrapping multiple validation
// errors returned by GetReportReasonsResp.ValidateAll() if the designated
// constraints aren't met.
type GetReportReasonsRespMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetReportReasonsRespMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetReportReasonsRespMultiError) AllErrors() []error { return m }

// GetReportReasonsRespValidationError is the validation error returned by
// GetReportReasonsResp.Validate if the designated constraints aren't met.
type GetReportReasonsRespValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetReportReasonsRespValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetReportReasonsRespValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetReportReasonsRespValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetReportReasonsRespValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetReportReasonsRespValidationError) ErrorName() string {
	return "GetReportReasonsRespValidationError"
}

// Error satisfies the builtin error interface
func (e GetReportReasonsRespValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetReportReasonsResp.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetReportReasonsRespValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetReportReasonsRespValidationError{}

// Validate checks the field values on ReportReq with the rules defined in the
// proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *ReportReq) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on ReportReq with the rules defined in
// the proto definition for this message. If any rules are violated, the
// result is a list of violation errors wrapped in ReportReqMultiError, or nil
// if none found.
func (m *ReportReq) ValidateAll() error {
	return m.validate(true)
}

func (m *ReportReq) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if _, ok := svcscript.ReportType_name[int32(m.GetReportType())]; !ok {
		err := ReportReqValidationError{
			field:  "ReportType",
			reason: "value must be one of the defined enum values",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if m.GetTargetId() <= 0 {
		err := ReportReqValidationError{
			field:  "TargetId",
			reason: "value must be greater than 0",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	// no validation rules for ReasonId

	// no validation rules for Reason

	// no validation rules for Content

	if len(errors) > 0 {
		return ReportReqMultiError(errors)
	}

	return nil
}

// ReportReqMultiError is an error wrapping multiple validation errors returned
// by ReportReq.ValidateAll() if the designated constraints aren't met.
type ReportReqMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m ReportReqMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m ReportReqMultiError) AllErrors() []error { return m }

// ReportReqValidationError is the validation error returned by
// ReportReq.Validate if the designated constraints aren't met.
type ReportReqValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e ReportReqValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e ReportReqValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e ReportReqValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e ReportReqValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e ReportReqValidationError) ErrorName() string { return "ReportReqValidationError" }

// Error satisfies the builtin error interface
func (e ReportReqValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sReportReq.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = ReportReqValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = ReportReqValidationError{}

// Validate checks the field values on ReportResp with the rules defined in the
// proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *ReportResp) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on ReportResp with the rules defined in
// the proto definition for this message. If any rules are violated, the
// result is a list of violation errors wrapped in ReportRespMultiError, or
// nil if none found.
func (m *ReportResp) ValidateAll() error {
	return m.validate(true)
}

func (m *ReportResp) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Code

	// no validation rules for Msg

	if len(errors) > 0 {
		return ReportRespMultiError(errors)
	}

	return nil
}

// ReportRespMultiError is an error wrapping multiple validation errors
// returned by ReportResp.ValidateAll() if the designated constraints aren't met.
type ReportRespMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m ReportRespMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m ReportRespMultiError) AllErrors() []error { return m }

// ReportRespValidationError is the validation error returned by
// ReportResp.Validate if the designated constraints aren't met.
type ReportRespValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e ReportRespValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e ReportRespValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e ReportRespValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e ReportRespValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e ReportRespValidationError) ErrorName() string { return "ReportRespValidationError" }

// Error satisfies the builtin error interface
func (e ReportRespValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sReportResp.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = ReportRespValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = ReportRespValidationError{}

// Validate checks the field values on GetIPListReq with the rules defined in
// the proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *GetIPListReq) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GetIPListReq with the rules defined
// in the proto definition for this message. If any rules are violated, the
// result is a list of violation errors wrapped in GetIPListReqMultiError, or
// nil if none found.
func (m *GetIPListReq) ValidateAll() error {
	return m.validate(true)
}

func (m *GetIPListReq) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Page

	// no validation rules for PageSize

	if len(errors) > 0 {
		return GetIPListReqMultiError(errors)
	}

	return nil
}

// GetIPListReqMultiError is an error wrapping multiple validation errors
// returned by GetIPListReq.ValidateAll() if the designated constraints aren't met.
type GetIPListReqMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetIPListReqMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetIPListReqMultiError) AllErrors() []error { return m }

// GetIPListReqValidationError is the validation error returned by
// GetIPListReq.Validate if the designated constraints aren't met.
type GetIPListReqValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetIPListReqValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetIPListReqValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetIPListReqValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetIPListReqValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetIPListReqValidationError) ErrorName() string { return "GetIPListReqValidationError" }

// Error satisfies the builtin error interface
func (e GetIPListReqValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetIPListReq.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetIPListReqValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetIPListReqValidationError{}

// Validate checks the field values on GetIPListResp with the rules defined in
// the proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *GetIPListResp) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GetIPListResp with the rules defined
// in the proto definition for this message. If any rules are violated, the
// result is a list of violation errors wrapped in GetIPListRespMultiError, or
// nil if none found.
func (m *GetIPListResp) ValidateAll() error {
	return m.validate(true)
}

func (m *GetIPListResp) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Code

	// no validation rules for Msg

	if all {
		switch v := interface{}(m.GetData()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetIPListRespValidationError{
					field:  "Data",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetIPListRespValidationError{
					field:  "Data",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetData()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetIPListRespValidationError{
				field:  "Data",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return GetIPListRespMultiError(errors)
	}

	return nil
}

// GetIPListRespMultiError is an error wrapping multiple validation errors
// returned by GetIPListResp.ValidateAll() if the designated constraints
// aren't met.
type GetIPListRespMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetIPListRespMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetIPListRespMultiError) AllErrors() []error { return m }

// GetIPListRespValidationError is the validation error returned by
// GetIPListResp.Validate if the designated constraints aren't met.
type GetIPListRespValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetIPListRespValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetIPListRespValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetIPListRespValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetIPListRespValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetIPListRespValidationError) ErrorName() string { return "GetIPListRespValidationError" }

// Error satisfies the builtin error interface
func (e GetIPListRespValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetIPListResp.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetIPListRespValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetIPListRespValidationError{}

// Validate checks the field values on GetUserStatsReq with the rules defined
// in the proto definition for this message. If any rules are violated, the
// first error encountered is returned, or nil if there are no violations.
func (m *GetUserStatsReq) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GetUserStatsReq with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// GetUserStatsReqMultiError, or nil if none found.
func (m *GetUserStatsReq) ValidateAll() error {
	return m.validate(true)
}

func (m *GetUserStatsReq) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for UserId

	if len(errors) > 0 {
		return GetUserStatsReqMultiError(errors)
	}

	return nil
}

// GetUserStatsReqMultiError is an error wrapping multiple validation errors
// returned by GetUserStatsReq.ValidateAll() if the designated constraints
// aren't met.
type GetUserStatsReqMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetUserStatsReqMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetUserStatsReqMultiError) AllErrors() []error { return m }

// GetUserStatsReqValidationError is the validation error returned by
// GetUserStatsReq.Validate if the designated constraints aren't met.
type GetUserStatsReqValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetUserStatsReqValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetUserStatsReqValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetUserStatsReqValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetUserStatsReqValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetUserStatsReqValidationError) ErrorName() string { return "GetUserStatsReqValidationError" }

// Error satisfies the builtin error interface
func (e GetUserStatsReqValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetUserStatsReq.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetUserStatsReqValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetUserStatsReqValidationError{}

// Validate checks the field values on GetUserStatsResp with the rules defined
// in the proto definition for this message. If any rules are violated, the
// first error encountered is returned, or nil if there are no violations.
func (m *GetUserStatsResp) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GetUserStatsResp with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// GetUserStatsRespMultiError, or nil if none found.
func (m *GetUserStatsResp) ValidateAll() error {
	return m.validate(true)
}

func (m *GetUserStatsResp) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Code

	// no validation rules for Msg

	if all {
		switch v := interface{}(m.GetData()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetUserStatsRespValidationError{
					field:  "Data",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetUserStatsRespValidationError{
					field:  "Data",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetData()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetUserStatsRespValidationError{
				field:  "Data",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return GetUserStatsRespMultiError(errors)
	}

	return nil
}

// GetUserStatsRespMultiError is an error wrapping multiple validation errors
// returned by GetUserStatsResp.ValidateAll() if the designated constraints
// aren't met.
type GetUserStatsRespMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetUserStatsRespMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetUserStatsRespMultiError) AllErrors() []error { return m }

// GetUserStatsRespValidationError is the validation error returned by
// GetUserStatsResp.Validate if the designated constraints aren't met.
type GetUserStatsRespValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetUserStatsRespValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetUserStatsRespValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetUserStatsRespValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetUserStatsRespValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetUserStatsRespValidationError) ErrorName() string { return "GetUserStatsRespValidationError" }

// Error satisfies the builtin error interface
func (e GetUserStatsRespValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetUserStatsResp.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetUserStatsRespValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetUserStatsRespValidationError{}

// Validate checks the field values on GetUserScriptListsReq with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *GetUserScriptListsReq) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GetUserScriptListsReq with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// GetUserScriptListsReqMultiError, or nil if none found.
func (m *GetUserScriptListsReq) ValidateAll() error {
	return m.validate(true)
}

func (m *GetUserScriptListsReq) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for ListType

	// no validation rules for UserId

	// no validation rules for Page

	// no validation rules for PageSize

	if len(errors) > 0 {
		return GetUserScriptListsReqMultiError(errors)
	}

	return nil
}

// GetUserScriptListsReqMultiError is an error wrapping multiple validation
// errors returned by GetUserScriptListsReq.ValidateAll() if the designated
// constraints aren't met.
type GetUserScriptListsReqMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetUserScriptListsReqMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetUserScriptListsReqMultiError) AllErrors() []error { return m }

// GetUserScriptListsReqValidationError is the validation error returned by
// GetUserScriptListsReq.Validate if the designated constraints aren't met.
type GetUserScriptListsReqValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetUserScriptListsReqValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetUserScriptListsReqValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetUserScriptListsReqValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetUserScriptListsReqValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetUserScriptListsReqValidationError) ErrorName() string {
	return "GetUserScriptListsReqValidationError"
}

// Error satisfies the builtin error interface
func (e GetUserScriptListsReqValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetUserScriptListsReq.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetUserScriptListsReqValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetUserScriptListsReqValidationError{}

// Validate checks the field values on GetUserScriptListsResp with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *GetUserScriptListsResp) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GetUserScriptListsResp with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// GetUserScriptListsRespMultiError, or nil if none found.
func (m *GetUserScriptListsResp) ValidateAll() error {
	return m.validate(true)
}

func (m *GetUserScriptListsResp) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Code

	// no validation rules for Msg

	if all {
		switch v := interface{}(m.GetData()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetUserScriptListsRespValidationError{
					field:  "Data",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetUserScriptListsRespValidationError{
					field:  "Data",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetData()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetUserScriptListsRespValidationError{
				field:  "Data",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return GetUserScriptListsRespMultiError(errors)
	}

	return nil
}

// GetUserScriptListsRespMultiError is an error wrapping multiple validation
// errors returned by GetUserScriptListsResp.ValidateAll() if the designated
// constraints aren't met.
type GetUserScriptListsRespMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetUserScriptListsRespMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetUserScriptListsRespMultiError) AllErrors() []error { return m }

// GetUserScriptListsRespValidationError is the validation error returned by
// GetUserScriptListsResp.Validate if the designated constraints aren't met.
type GetUserScriptListsRespValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetUserScriptListsRespValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetUserScriptListsRespValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetUserScriptListsRespValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetUserScriptListsRespValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetUserScriptListsRespValidationError) ErrorName() string {
	return "GetUserScriptListsRespValidationError"
}

// Error satisfies the builtin error interface
func (e GetUserScriptListsRespValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetUserScriptListsResp.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetUserScriptListsRespValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetUserScriptListsRespValidationError{}

// Validate checks the field values on GetCoverListReq with the rules defined
// in the proto definition for this message. If any rules are violated, the
// first error encountered is returned, or nil if there are no violations.
func (m *GetCoverListReq) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GetCoverListReq with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// GetCoverListReqMultiError, or nil if none found.
func (m *GetCoverListReq) ValidateAll() error {
	return m.validate(true)
}

func (m *GetCoverListReq) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Page

	// no validation rules for PageSize

	// no validation rules for Type

	if len(errors) > 0 {
		return GetCoverListReqMultiError(errors)
	}

	return nil
}

// GetCoverListReqMultiError is an error wrapping multiple validation errors
// returned by GetCoverListReq.ValidateAll() if the designated constraints
// aren't met.
type GetCoverListReqMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetCoverListReqMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetCoverListReqMultiError) AllErrors() []error { return m }

// GetCoverListReqValidationError is the validation error returned by
// GetCoverListReq.Validate if the designated constraints aren't met.
type GetCoverListReqValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetCoverListReqValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetCoverListReqValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetCoverListReqValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetCoverListReqValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetCoverListReqValidationError) ErrorName() string { return "GetCoverListReqValidationError" }

// Error satisfies the builtin error interface
func (e GetCoverListReqValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetCoverListReq.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetCoverListReqValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetCoverListReqValidationError{}

// Validate checks the field values on GetCoverListResp with the rules defined
// in the proto definition for this message. If any rules are violated, the
// first error encountered is returned, or nil if there are no violations.
func (m *GetCoverListResp) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GetCoverListResp with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// GetCoverListRespMultiError, or nil if none found.
func (m *GetCoverListResp) ValidateAll() error {
	return m.validate(true)
}

func (m *GetCoverListResp) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Code

	// no validation rules for Msg

	if all {
		switch v := interface{}(m.GetData()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetCoverListRespValidationError{
					field:  "Data",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetCoverListRespValidationError{
					field:  "Data",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetData()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetCoverListRespValidationError{
				field:  "Data",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return GetCoverListRespMultiError(errors)
	}

	return nil
}

// GetCoverListRespMultiError is an error wrapping multiple validation errors
// returned by GetCoverListResp.ValidateAll() if the designated constraints
// aren't met.
type GetCoverListRespMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetCoverListRespMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetCoverListRespMultiError) AllErrors() []error { return m }

// GetCoverListRespValidationError is the validation error returned by
// GetCoverListResp.Validate if the designated constraints aren't met.
type GetCoverListRespValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetCoverListRespValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetCoverListRespValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetCoverListRespValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetCoverListRespValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetCoverListRespValidationError) ErrorName() string { return "GetCoverListRespValidationError" }

// Error satisfies the builtin error interface
func (e GetCoverListRespValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetCoverListResp.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetCoverListRespValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetCoverListRespValidationError{}

// Validate checks the field values on GenerateScriptLinesReq with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *GenerateScriptLinesReq) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GenerateScriptLinesReq with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// GenerateScriptLinesReqMultiError, or nil if none found.
func (m *GenerateScriptLinesReq) ValidateAll() error {
	return m.validate(true)
}

func (m *GenerateScriptLinesReq) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if len(m.GetCharacterIds()) < 1 {
		err := GenerateScriptLinesReqValidationError{
			field:  "CharacterIds",
			reason: "value must contain at least 1 item(s)",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if len(errors) > 0 {
		return GenerateScriptLinesReqMultiError(errors)
	}

	return nil
}

// GenerateScriptLinesReqMultiError is an error wrapping multiple validation
// errors returned by GenerateScriptLinesReq.ValidateAll() if the designated
// constraints aren't met.
type GenerateScriptLinesReqMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GenerateScriptLinesReqMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GenerateScriptLinesReqMultiError) AllErrors() []error { return m }

// GenerateScriptLinesReqValidationError is the validation error returned by
// GenerateScriptLinesReq.Validate if the designated constraints aren't met.
type GenerateScriptLinesReqValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GenerateScriptLinesReqValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GenerateScriptLinesReqValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GenerateScriptLinesReqValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GenerateScriptLinesReqValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GenerateScriptLinesReqValidationError) ErrorName() string {
	return "GenerateScriptLinesReqValidationError"
}

// Error satisfies the builtin error interface
func (e GenerateScriptLinesReqValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGenerateScriptLinesReq.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GenerateScriptLinesReqValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GenerateScriptLinesReqValidationError{}

// Validate checks the field values on GenerateScriptLinesResp with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *GenerateScriptLinesResp) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GenerateScriptLinesResp with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// GenerateScriptLinesRespMultiError, or nil if none found.
func (m *GenerateScriptLinesResp) ValidateAll() error {
	return m.validate(true)
}

func (m *GenerateScriptLinesResp) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Code

	// no validation rules for Msg

	if all {
		switch v := interface{}(m.GetData()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GenerateScriptLinesRespValidationError{
					field:  "Data",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GenerateScriptLinesRespValidationError{
					field:  "Data",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetData()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GenerateScriptLinesRespValidationError{
				field:  "Data",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return GenerateScriptLinesRespMultiError(errors)
	}

	return nil
}

// GenerateScriptLinesRespMultiError is an error wrapping multiple validation
// errors returned by GenerateScriptLinesResp.ValidateAll() if the designated
// constraints aren't met.
type GenerateScriptLinesRespMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GenerateScriptLinesRespMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GenerateScriptLinesRespMultiError) AllErrors() []error { return m }

// GenerateScriptLinesRespValidationError is the validation error returned by
// GenerateScriptLinesResp.Validate if the designated constraints aren't met.
type GenerateScriptLinesRespValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GenerateScriptLinesRespValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GenerateScriptLinesRespValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GenerateScriptLinesRespValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GenerateScriptLinesRespValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GenerateScriptLinesRespValidationError) ErrorName() string {
	return "GenerateScriptLinesRespValidationError"
}

// Error satisfies the builtin error interface
func (e GenerateScriptLinesRespValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGenerateScriptLinesResp.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GenerateScriptLinesRespValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GenerateScriptLinesRespValidationError{}

// Validate checks the field values on GenerateScriptLinesRespData with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *GenerateScriptLinesRespData) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GenerateScriptLinesRespData with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// GenerateScriptLinesRespDataMultiError, or nil if none found.
func (m *GenerateScriptLinesRespData) ValidateAll() error {
	return m.validate(true)
}

func (m *GenerateScriptLinesRespData) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	for idx, item := range m.GetLines() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, GenerateScriptLinesRespDataValidationError{
						field:  fmt.Sprintf("Lines[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, GenerateScriptLinesRespDataValidationError{
						field:  fmt.Sprintf("Lines[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return GenerateScriptLinesRespDataValidationError{
					field:  fmt.Sprintf("Lines[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	{
		sorted_keys := make([]int64, len(m.GetCharacters()))
		i := 0
		for key := range m.GetCharacters() {
			sorted_keys[i] = key
			i++
		}
		sort.Slice(sorted_keys, func(i, j int) bool { return sorted_keys[i] < sorted_keys[j] })
		for _, key := range sorted_keys {
			val := m.GetCharacters()[key]
			_ = val

			// no validation rules for Characters[key]

			if all {
				switch v := interface{}(val).(type) {
				case interface{ ValidateAll() error }:
					if err := v.ValidateAll(); err != nil {
						errors = append(errors, GenerateScriptLinesRespDataValidationError{
							field:  fmt.Sprintf("Characters[%v]", key),
							reason: "embedded message failed validation",
							cause:  err,
						})
					}
				case interface{ Validate() error }:
					if err := v.Validate(); err != nil {
						errors = append(errors, GenerateScriptLinesRespDataValidationError{
							field:  fmt.Sprintf("Characters[%v]", key),
							reason: "embedded message failed validation",
							cause:  err,
						})
					}
				}
			} else if v, ok := interface{}(val).(interface{ Validate() error }); ok {
				if err := v.Validate(); err != nil {
					return GenerateScriptLinesRespDataValidationError{
						field:  fmt.Sprintf("Characters[%v]", key),
						reason: "embedded message failed validation",
						cause:  err,
					}
				}
			}

		}
	}

	if len(errors) > 0 {
		return GenerateScriptLinesRespDataMultiError(errors)
	}

	return nil
}

// GenerateScriptLinesRespDataMultiError is an error wrapping multiple
// validation errors returned by GenerateScriptLinesRespData.ValidateAll() if
// the designated constraints aren't met.
type GenerateScriptLinesRespDataMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GenerateScriptLinesRespDataMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GenerateScriptLinesRespDataMultiError) AllErrors() []error { return m }

// GenerateScriptLinesRespDataValidationError is the validation error returned
// by GenerateScriptLinesRespData.Validate if the designated constraints
// aren't met.
type GenerateScriptLinesRespDataValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GenerateScriptLinesRespDataValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GenerateScriptLinesRespDataValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GenerateScriptLinesRespDataValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GenerateScriptLinesRespDataValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GenerateScriptLinesRespDataValidationError) ErrorName() string {
	return "GenerateScriptLinesRespDataValidationError"
}

// Error satisfies the builtin error interface
func (e GenerateScriptLinesRespDataValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGenerateScriptLinesRespData.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GenerateScriptLinesRespDataValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GenerateScriptLinesRespDataValidationError{}

// Validate checks the field values on DeleteDubbingReq with the rules defined
// in the proto definition for this message. If any rules are violated, the
// first error encountered is returned, or nil if there are no violations.
func (m *DeleteDubbingReq) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on DeleteDubbingReq with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// DeleteDubbingReqMultiError, or nil if none found.
func (m *DeleteDubbingReq) ValidateAll() error {
	return m.validate(true)
}

func (m *DeleteDubbingReq) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if m.GetDubbingId() <= 0 {
		err := DeleteDubbingReqValidationError{
			field:  "DubbingId",
			reason: "value must be greater than 0",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if len(errors) > 0 {
		return DeleteDubbingReqMultiError(errors)
	}

	return nil
}

// DeleteDubbingReqMultiError is an error wrapping multiple validation errors
// returned by DeleteDubbingReq.ValidateAll() if the designated constraints
// aren't met.
type DeleteDubbingReqMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m DeleteDubbingReqMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m DeleteDubbingReqMultiError) AllErrors() []error { return m }

// DeleteDubbingReqValidationError is the validation error returned by
// DeleteDubbingReq.Validate if the designated constraints aren't met.
type DeleteDubbingReqValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e DeleteDubbingReqValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e DeleteDubbingReqValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e DeleteDubbingReqValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e DeleteDubbingReqValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e DeleteDubbingReqValidationError) ErrorName() string { return "DeleteDubbingReqValidationError" }

// Error satisfies the builtin error interface
func (e DeleteDubbingReqValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sDeleteDubbingReq.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = DeleteDubbingReqValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = DeleteDubbingReqValidationError{}

// Validate checks the field values on DeleteDubbingResp with the rules defined
// in the proto definition for this message. If any rules are violated, the
// first error encountered is returned, or nil if there are no violations.
func (m *DeleteDubbingResp) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on DeleteDubbingResp with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// DeleteDubbingRespMultiError, or nil if none found.
func (m *DeleteDubbingResp) ValidateAll() error {
	return m.validate(true)
}

func (m *DeleteDubbingResp) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Code

	// no validation rules for Msg

	if len(errors) > 0 {
		return DeleteDubbingRespMultiError(errors)
	}

	return nil
}

// DeleteDubbingRespMultiError is an error wrapping multiple validation errors
// returned by DeleteDubbingResp.ValidateAll() if the designated constraints
// aren't met.
type DeleteDubbingRespMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m DeleteDubbingRespMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m DeleteDubbingRespMultiError) AllErrors() []error { return m }

// DeleteDubbingRespValidationError is the validation error returned by
// DeleteDubbingResp.Validate if the designated constraints aren't met.
type DeleteDubbingRespValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e DeleteDubbingRespValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e DeleteDubbingRespValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e DeleteDubbingRespValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e DeleteDubbingRespValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e DeleteDubbingRespValidationError) ErrorName() string {
	return "DeleteDubbingRespValidationError"
}

// Error satisfies the builtin error interface
func (e DeleteDubbingRespValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sDeleteDubbingResp.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = DeleteDubbingRespValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = DeleteDubbingRespValidationError{}

// Validate checks the field values on SetDubbingTopReq with the rules defined
// in the proto definition for this message. If any rules are violated, the
// first error encountered is returned, or nil if there are no violations.
func (m *SetDubbingTopReq) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on SetDubbingTopReq with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// SetDubbingTopReqMultiError, or nil if none found.
func (m *SetDubbingTopReq) ValidateAll() error {
	return m.validate(true)
}

func (m *SetDubbingTopReq) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if m.GetDubbingId() <= 0 {
		err := SetDubbingTopReqValidationError{
			field:  "DubbingId",
			reason: "value must be greater than 0",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	// no validation rules for IsTop

	if len(errors) > 0 {
		return SetDubbingTopReqMultiError(errors)
	}

	return nil
}

// SetDubbingTopReqMultiError is an error wrapping multiple validation errors
// returned by SetDubbingTopReq.ValidateAll() if the designated constraints
// aren't met.
type SetDubbingTopReqMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m SetDubbingTopReqMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m SetDubbingTopReqMultiError) AllErrors() []error { return m }

// SetDubbingTopReqValidationError is the validation error returned by
// SetDubbingTopReq.Validate if the designated constraints aren't met.
type SetDubbingTopReqValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e SetDubbingTopReqValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e SetDubbingTopReqValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e SetDubbingTopReqValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e SetDubbingTopReqValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e SetDubbingTopReqValidationError) ErrorName() string { return "SetDubbingTopReqValidationError" }

// Error satisfies the builtin error interface
func (e SetDubbingTopReqValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sSetDubbingTopReq.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = SetDubbingTopReqValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = SetDubbingTopReqValidationError{}

// Validate checks the field values on SetDubbingTopResp with the rules defined
// in the proto definition for this message. If any rules are violated, the
// first error encountered is returned, or nil if there are no violations.
func (m *SetDubbingTopResp) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on SetDubbingTopResp with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// SetDubbingTopRespMultiError, or nil if none found.
func (m *SetDubbingTopResp) ValidateAll() error {
	return m.validate(true)
}

func (m *SetDubbingTopResp) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Code

	// no validation rules for Msg

	if len(errors) > 0 {
		return SetDubbingTopRespMultiError(errors)
	}

	return nil
}

// SetDubbingTopRespMultiError is an error wrapping multiple validation errors
// returned by SetDubbingTopResp.ValidateAll() if the designated constraints
// aren't met.
type SetDubbingTopRespMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m SetDubbingTopRespMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m SetDubbingTopRespMultiError) AllErrors() []error { return m }

// SetDubbingTopRespValidationError is the validation error returned by
// SetDubbingTopResp.Validate if the designated constraints aren't met.
type SetDubbingTopRespValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e SetDubbingTopRespValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e SetDubbingTopRespValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e SetDubbingTopRespValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e SetDubbingTopRespValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e SetDubbingTopRespValidationError) ErrorName() string {
	return "SetDubbingTopRespValidationError"
}

// Error satisfies the builtin error interface
func (e SetDubbingTopRespValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sSetDubbingTopResp.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = SetDubbingTopRespValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = SetDubbingTopRespValidationError{}
