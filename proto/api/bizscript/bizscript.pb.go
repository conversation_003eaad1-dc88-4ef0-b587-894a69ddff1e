// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.33.0
// 	protoc        v5.29.3
// source: bizscript.proto

package bizscript

import (
	_ "github.com/envoyproxy/protoc-gen-validate/validate"
	_ "google.golang.org/genproto/googleapis/api/annotations"
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	svcscript "new-gitlab.xunlei.cn/vcproject/backends/proto/api/svcscript"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

// 获取角色列表请求
type GetCharacterListReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Page     int32 `protobuf:"varint,1,opt,name=page,proto3" json:"page,omitempty"`                         // 页码
	PageSize int32 `protobuf:"varint,2,opt,name=page_size,json=pageSize,proto3" json:"page_size,omitempty"` // 每页数量
	IpId     int64 `protobuf:"varint,3,opt,name=ip_id,json=ipId,proto3" json:"ip_id,omitempty"`             // IP ID，可选过滤
}

func (x *GetCharacterListReq) Reset() {
	*x = GetCharacterListReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_bizscript_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetCharacterListReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetCharacterListReq) ProtoMessage() {}

func (x *GetCharacterListReq) ProtoReflect() protoreflect.Message {
	mi := &file_bizscript_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetCharacterListReq.ProtoReflect.Descriptor instead.
func (*GetCharacterListReq) Descriptor() ([]byte, []int) {
	return file_bizscript_proto_rawDescGZIP(), []int{0}
}

func (x *GetCharacterListReq) GetPage() int32 {
	if x != nil {
		return x.Page
	}
	return 0
}

func (x *GetCharacterListReq) GetPageSize() int32 {
	if x != nil {
		return x.PageSize
	}
	return 0
}

func (x *GetCharacterListReq) GetIpId() int64 {
	if x != nil {
		return x.IpId
	}
	return 0
}

// 获取角色列表响应
type GetCharacterListResp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Code int32                               `protobuf:"varint,1,opt,name=code,proto3" json:"code,omitempty"` // 错误码
	Msg  string                              `protobuf:"bytes,2,opt,name=msg,proto3" json:"msg,omitempty"`    // 错误信息
	Data *svcscript.GetCharacterListRespData `protobuf:"bytes,3,opt,name=data,proto3" json:"data,omitempty"`  // 响应数据
}

func (x *GetCharacterListResp) Reset() {
	*x = GetCharacterListResp{}
	if protoimpl.UnsafeEnabled {
		mi := &file_bizscript_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetCharacterListResp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetCharacterListResp) ProtoMessage() {}

func (x *GetCharacterListResp) ProtoReflect() protoreflect.Message {
	mi := &file_bizscript_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetCharacterListResp.ProtoReflect.Descriptor instead.
func (*GetCharacterListResp) Descriptor() ([]byte, []int) {
	return file_bizscript_proto_rawDescGZIP(), []int{1}
}

func (x *GetCharacterListResp) GetCode() int32 {
	if x != nil {
		return x.Code
	}
	return 0
}

func (x *GetCharacterListResp) GetMsg() string {
	if x != nil {
		return x.Msg
	}
	return ""
}

func (x *GetCharacterListResp) GetData() *svcscript.GetCharacterListRespData {
	if x != nil {
		return x.Data
	}
	return nil
}

// 搜索角色请求
type SearchCharactersReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Keyword  string `protobuf:"bytes,1,opt,name=keyword,proto3" json:"keyword,omitempty"`                    // 搜索关键词
	Page     int32  `protobuf:"varint,2,opt,name=page,proto3" json:"page,omitempty"`                         // 页码
	PageSize int32  `protobuf:"varint,3,opt,name=page_size,json=pageSize,proto3" json:"page_size,omitempty"` // 每页数量
}

func (x *SearchCharactersReq) Reset() {
	*x = SearchCharactersReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_bizscript_proto_msgTypes[2]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *SearchCharactersReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SearchCharactersReq) ProtoMessage() {}

func (x *SearchCharactersReq) ProtoReflect() protoreflect.Message {
	mi := &file_bizscript_proto_msgTypes[2]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SearchCharactersReq.ProtoReflect.Descriptor instead.
func (*SearchCharactersReq) Descriptor() ([]byte, []int) {
	return file_bizscript_proto_rawDescGZIP(), []int{2}
}

func (x *SearchCharactersReq) GetKeyword() string {
	if x != nil {
		return x.Keyword
	}
	return ""
}

func (x *SearchCharactersReq) GetPage() int32 {
	if x != nil {
		return x.Page
	}
	return 0
}

func (x *SearchCharactersReq) GetPageSize() int32 {
	if x != nil {
		return x.PageSize
	}
	return 0
}

// 搜索角色响应
type SearchCharactersResp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Code int32                               `protobuf:"varint,1,opt,name=code,proto3" json:"code,omitempty"` // 错误码
	Msg  string                              `protobuf:"bytes,2,opt,name=msg,proto3" json:"msg,omitempty"`    // 错误信息
	Data *svcscript.GetCharacterListRespData `protobuf:"bytes,3,opt,name=data,proto3" json:"data,omitempty"`  // 响应数据
}

func (x *SearchCharactersResp) Reset() {
	*x = SearchCharactersResp{}
	if protoimpl.UnsafeEnabled {
		mi := &file_bizscript_proto_msgTypes[3]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *SearchCharactersResp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SearchCharactersResp) ProtoMessage() {}

func (x *SearchCharactersResp) ProtoReflect() protoreflect.Message {
	mi := &file_bizscript_proto_msgTypes[3]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SearchCharactersResp.ProtoReflect.Descriptor instead.
func (*SearchCharactersResp) Descriptor() ([]byte, []int) {
	return file_bizscript_proto_rawDescGZIP(), []int{3}
}

func (x *SearchCharactersResp) GetCode() int32 {
	if x != nil {
		return x.Code
	}
	return 0
}

func (x *SearchCharactersResp) GetMsg() string {
	if x != nil {
		return x.Msg
	}
	return ""
}

func (x *SearchCharactersResp) GetData() *svcscript.GetCharacterListRespData {
	if x != nil {
		return x.Data
	}
	return nil
}

// 设置用户角色请求
type SetUserCharacterReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	CharacterId      int64 `protobuf:"varint,1,opt,name=character_id,json=characterId,proto3" json:"character_id,omitempty"`                  // 角色ID
	CharacterAssetId int64 `protobuf:"varint,2,opt,name=character_asset_id,json=characterAssetId,proto3" json:"character_asset_id,omitempty"` // 角色资源包ID，默认为0表示使用默认资源包
}

func (x *SetUserCharacterReq) Reset() {
	*x = SetUserCharacterReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_bizscript_proto_msgTypes[4]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *SetUserCharacterReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SetUserCharacterReq) ProtoMessage() {}

func (x *SetUserCharacterReq) ProtoReflect() protoreflect.Message {
	mi := &file_bizscript_proto_msgTypes[4]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SetUserCharacterReq.ProtoReflect.Descriptor instead.
func (*SetUserCharacterReq) Descriptor() ([]byte, []int) {
	return file_bizscript_proto_rawDescGZIP(), []int{4}
}

func (x *SetUserCharacterReq) GetCharacterId() int64 {
	if x != nil {
		return x.CharacterId
	}
	return 0
}

func (x *SetUserCharacterReq) GetCharacterAssetId() int64 {
	if x != nil {
		return x.CharacterAssetId
	}
	return 0
}

// 设置用户角色响应
type SetUserCharacterResp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Code int32  `protobuf:"varint,1,opt,name=code,proto3" json:"code,omitempty"` // 错误码
	Msg  string `protobuf:"bytes,2,opt,name=msg,proto3" json:"msg,omitempty"`    // 错误信息
}

func (x *SetUserCharacterResp) Reset() {
	*x = SetUserCharacterResp{}
	if protoimpl.UnsafeEnabled {
		mi := &file_bizscript_proto_msgTypes[5]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *SetUserCharacterResp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SetUserCharacterResp) ProtoMessage() {}

func (x *SetUserCharacterResp) ProtoReflect() protoreflect.Message {
	mi := &file_bizscript_proto_msgTypes[5]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SetUserCharacterResp.ProtoReflect.Descriptor instead.
func (*SetUserCharacterResp) Descriptor() ([]byte, []int) {
	return file_bizscript_proto_rawDescGZIP(), []int{5}
}

func (x *SetUserCharacterResp) GetCode() int32 {
	if x != nil {
		return x.Code
	}
	return 0
}

func (x *SetUserCharacterResp) GetMsg() string {
	if x != nil {
		return x.Msg
	}
	return ""
}

// 获取用户角色请求
type GetUserCharactersReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields
}

func (x *GetUserCharactersReq) Reset() {
	*x = GetUserCharactersReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_bizscript_proto_msgTypes[6]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetUserCharactersReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetUserCharactersReq) ProtoMessage() {}

func (x *GetUserCharactersReq) ProtoReflect() protoreflect.Message {
	mi := &file_bizscript_proto_msgTypes[6]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetUserCharactersReq.ProtoReflect.Descriptor instead.
func (*GetUserCharactersReq) Descriptor() ([]byte, []int) {
	return file_bizscript_proto_rawDescGZIP(), []int{6}
}

// 获取用户角色响应
type GetUserCharactersResp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Code int32                                `protobuf:"varint,1,opt,name=code,proto3" json:"code,omitempty"` // 错误码
	Msg  string                               `protobuf:"bytes,2,opt,name=msg,proto3" json:"msg,omitempty"`    // 错误信息
	Data *svcscript.GetUserCharactersRespData `protobuf:"bytes,3,opt,name=data,proto3" json:"data,omitempty"`  // 响应数据
}

func (x *GetUserCharactersResp) Reset() {
	*x = GetUserCharactersResp{}
	if protoimpl.UnsafeEnabled {
		mi := &file_bizscript_proto_msgTypes[7]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetUserCharactersResp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetUserCharactersResp) ProtoMessage() {}

func (x *GetUserCharactersResp) ProtoReflect() protoreflect.Message {
	mi := &file_bizscript_proto_msgTypes[7]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetUserCharactersResp.ProtoReflect.Descriptor instead.
func (*GetUserCharactersResp) Descriptor() ([]byte, []int) {
	return file_bizscript_proto_rawDescGZIP(), []int{7}
}

func (x *GetUserCharactersResp) GetCode() int32 {
	if x != nil {
		return x.Code
	}
	return 0
}

func (x *GetUserCharactersResp) GetMsg() string {
	if x != nil {
		return x.Msg
	}
	return ""
}

func (x *GetUserCharactersResp) GetData() *svcscript.GetUserCharactersRespData {
	if x != nil {
		return x.Data
	}
	return nil
}

// 批量检查角色可用性请求
type BatchCheckCharacterAvailabilityReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	CharacterIds []int64 `protobuf:"varint,1,rep,packed,name=character_ids,json=characterIds,proto3" json:"character_ids,omitempty"` // 角色ID列表，最多100个
}

func (x *BatchCheckCharacterAvailabilityReq) Reset() {
	*x = BatchCheckCharacterAvailabilityReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_bizscript_proto_msgTypes[8]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *BatchCheckCharacterAvailabilityReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BatchCheckCharacterAvailabilityReq) ProtoMessage() {}

func (x *BatchCheckCharacterAvailabilityReq) ProtoReflect() protoreflect.Message {
	mi := &file_bizscript_proto_msgTypes[8]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BatchCheckCharacterAvailabilityReq.ProtoReflect.Descriptor instead.
func (*BatchCheckCharacterAvailabilityReq) Descriptor() ([]byte, []int) {
	return file_bizscript_proto_rawDescGZIP(), []int{8}
}

func (x *BatchCheckCharacterAvailabilityReq) GetCharacterIds() []int64 {
	if x != nil {
		return x.CharacterIds
	}
	return nil
}

// 批量检查角色可用性响应
type BatchCheckCharacterAvailabilityResp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Code int32                                              `protobuf:"varint,1,opt,name=code,proto3" json:"code,omitempty"` // 错误码
	Msg  string                                             `protobuf:"bytes,2,opt,name=msg,proto3" json:"msg,omitempty"`    // 错误信息
	Data *svcscript.BatchCheckCharacterAvailabilityRespData `protobuf:"bytes,3,opt,name=data,proto3" json:"data,omitempty"`  // 响应数据
}

func (x *BatchCheckCharacterAvailabilityResp) Reset() {
	*x = BatchCheckCharacterAvailabilityResp{}
	if protoimpl.UnsafeEnabled {
		mi := &file_bizscript_proto_msgTypes[9]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *BatchCheckCharacterAvailabilityResp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BatchCheckCharacterAvailabilityResp) ProtoMessage() {}

func (x *BatchCheckCharacterAvailabilityResp) ProtoReflect() protoreflect.Message {
	mi := &file_bizscript_proto_msgTypes[9]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BatchCheckCharacterAvailabilityResp.ProtoReflect.Descriptor instead.
func (*BatchCheckCharacterAvailabilityResp) Descriptor() ([]byte, []int) {
	return file_bizscript_proto_rawDescGZIP(), []int{9}
}

func (x *BatchCheckCharacterAvailabilityResp) GetCode() int32 {
	if x != nil {
		return x.Code
	}
	return 0
}

func (x *BatchCheckCharacterAvailabilityResp) GetMsg() string {
	if x != nil {
		return x.Msg
	}
	return ""
}

func (x *BatchCheckCharacterAvailabilityResp) GetData() *svcscript.BatchCheckCharacterAvailabilityRespData {
	if x != nil {
		return x.Data
	}
	return nil
}

// 获取话题列表请求
type GetTopicListReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Page     int32                 `protobuf:"varint,1,opt,name=page,proto3" json:"page,omitempty"`                                 // 页码
	PageSize int32                 `protobuf:"varint,2,opt,name=page_size,json=pageSize,proto3" json:"page_size,omitempty"`         // 每页数量
	Scene    svcscript.TOPIC_SCENE `protobuf:"varint,3,opt,name=scene,proto3,enum=vc.svcscript.TOPIC_SCENE" json:"scene,omitempty"` // 场景
}

func (x *GetTopicListReq) Reset() {
	*x = GetTopicListReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_bizscript_proto_msgTypes[10]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetTopicListReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetTopicListReq) ProtoMessage() {}

func (x *GetTopicListReq) ProtoReflect() protoreflect.Message {
	mi := &file_bizscript_proto_msgTypes[10]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetTopicListReq.ProtoReflect.Descriptor instead.
func (*GetTopicListReq) Descriptor() ([]byte, []int) {
	return file_bizscript_proto_rawDescGZIP(), []int{10}
}

func (x *GetTopicListReq) GetPage() int32 {
	if x != nil {
		return x.Page
	}
	return 0
}

func (x *GetTopicListReq) GetPageSize() int32 {
	if x != nil {
		return x.PageSize
	}
	return 0
}

func (x *GetTopicListReq) GetScene() svcscript.TOPIC_SCENE {
	if x != nil {
		return x.Scene
	}
	return svcscript.TOPIC_SCENE(0)
}

// 获取话题列表响应
type GetTopicListResp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Code int32                           `protobuf:"varint,1,opt,name=code,proto3" json:"code,omitempty"` // 错误码
	Msg  string                          `protobuf:"bytes,2,opt,name=msg,proto3" json:"msg,omitempty"`    // 错误信息
	Data *svcscript.GetTopicListRespData `protobuf:"bytes,3,opt,name=data,proto3" json:"data,omitempty"`  // 响应数据
}

func (x *GetTopicListResp) Reset() {
	*x = GetTopicListResp{}
	if protoimpl.UnsafeEnabled {
		mi := &file_bizscript_proto_msgTypes[11]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetTopicListResp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetTopicListResp) ProtoMessage() {}

func (x *GetTopicListResp) ProtoReflect() protoreflect.Message {
	mi := &file_bizscript_proto_msgTypes[11]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetTopicListResp.ProtoReflect.Descriptor instead.
func (*GetTopicListResp) Descriptor() ([]byte, []int) {
	return file_bizscript_proto_rawDescGZIP(), []int{11}
}

func (x *GetTopicListResp) GetCode() int32 {
	if x != nil {
		return x.Code
	}
	return 0
}

func (x *GetTopicListResp) GetMsg() string {
	if x != nil {
		return x.Msg
	}
	return ""
}

func (x *GetTopicListResp) GetData() *svcscript.GetTopicListRespData {
	if x != nil {
		return x.Data
	}
	return nil
}

// 创建剧本请求
type CreateScriptReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Title             string                            `protobuf:"bytes,1,opt,name=title,proto3" json:"title,omitempty"`                                                    // 标题
	Cover             string                            `protobuf:"bytes,2,opt,name=cover,proto3" json:"cover,omitempty"`                                                    // 封面地址
	CharacterAssetIds []*svcscript.ScriptCharacterAsset `protobuf:"bytes,3,rep,name=character_asset_ids,json=characterAssetIds,proto3" json:"character_asset_ids,omitempty"` // 角色ID列表,最多六个
	TopicNames        []string                          `protobuf:"bytes,4,rep,name=topic_names,json=topicNames,proto3" json:"topic_names,omitempty"`                        // 话题ID列表
	Lines             []*svcscript.CreateLineReq        `protobuf:"bytes,5,rep,name=lines,proto3" json:"lines,omitempty"`                                                    // 台词列表
	BgmUrl            string                            `protobuf:"bytes,6,opt,name=bgm_url,json=bgmUrl,proto3" json:"bgm_url,omitempty"`                                    // 背景音乐地址
	BgmDuration       int32                             `protobuf:"varint,7,opt,name=bgm_duration,json=bgmDuration,proto3" json:"bgm_duration,omitempty"`                    // 背景音乐时长（毫秒）
	ThemeColor        string                            `protobuf:"bytes,8,opt,name=theme_color,json=themeColor,proto3" json:"theme_color,omitempty"`                        // 主题色
}

func (x *CreateScriptReq) Reset() {
	*x = CreateScriptReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_bizscript_proto_msgTypes[12]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CreateScriptReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CreateScriptReq) ProtoMessage() {}

func (x *CreateScriptReq) ProtoReflect() protoreflect.Message {
	mi := &file_bizscript_proto_msgTypes[12]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CreateScriptReq.ProtoReflect.Descriptor instead.
func (*CreateScriptReq) Descriptor() ([]byte, []int) {
	return file_bizscript_proto_rawDescGZIP(), []int{12}
}

func (x *CreateScriptReq) GetTitle() string {
	if x != nil {
		return x.Title
	}
	return ""
}

func (x *CreateScriptReq) GetCover() string {
	if x != nil {
		return x.Cover
	}
	return ""
}

func (x *CreateScriptReq) GetCharacterAssetIds() []*svcscript.ScriptCharacterAsset {
	if x != nil {
		return x.CharacterAssetIds
	}
	return nil
}

func (x *CreateScriptReq) GetTopicNames() []string {
	if x != nil {
		return x.TopicNames
	}
	return nil
}

func (x *CreateScriptReq) GetLines() []*svcscript.CreateLineReq {
	if x != nil {
		return x.Lines
	}
	return nil
}

func (x *CreateScriptReq) GetBgmUrl() string {
	if x != nil {
		return x.BgmUrl
	}
	return ""
}

func (x *CreateScriptReq) GetBgmDuration() int32 {
	if x != nil {
		return x.BgmDuration
	}
	return 0
}

func (x *CreateScriptReq) GetThemeColor() string {
	if x != nil {
		return x.ThemeColor
	}
	return ""
}

// 创建剧本响应
type CreateScriptResp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Code int32                           `protobuf:"varint,1,opt,name=code,proto3" json:"code,omitempty"` // 错误码
	Msg  string                          `protobuf:"bytes,2,opt,name=msg,proto3" json:"msg,omitempty"`    // 错误信息
	Data *svcscript.CreateScriptRespData `protobuf:"bytes,3,opt,name=data,proto3" json:"data,omitempty"`  // 响应数据
}

func (x *CreateScriptResp) Reset() {
	*x = CreateScriptResp{}
	if protoimpl.UnsafeEnabled {
		mi := &file_bizscript_proto_msgTypes[13]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CreateScriptResp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CreateScriptResp) ProtoMessage() {}

func (x *CreateScriptResp) ProtoReflect() protoreflect.Message {
	mi := &file_bizscript_proto_msgTypes[13]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CreateScriptResp.ProtoReflect.Descriptor instead.
func (*CreateScriptResp) Descriptor() ([]byte, []int) {
	return file_bizscript_proto_rawDescGZIP(), []int{13}
}

func (x *CreateScriptResp) GetCode() int32 {
	if x != nil {
		return x.Code
	}
	return 0
}

func (x *CreateScriptResp) GetMsg() string {
	if x != nil {
		return x.Msg
	}
	return ""
}

func (x *CreateScriptResp) GetData() *svcscript.CreateScriptRespData {
	if x != nil {
		return x.Data
	}
	return nil
}

type DeleteScriptReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	ScriptId int64 `protobuf:"varint,1,opt,name=script_id,json=scriptId,proto3" json:"script_id,omitempty"` //剧本ID
}

func (x *DeleteScriptReq) Reset() {
	*x = DeleteScriptReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_bizscript_proto_msgTypes[14]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *DeleteScriptReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DeleteScriptReq) ProtoMessage() {}

func (x *DeleteScriptReq) ProtoReflect() protoreflect.Message {
	mi := &file_bizscript_proto_msgTypes[14]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DeleteScriptReq.ProtoReflect.Descriptor instead.
func (*DeleteScriptReq) Descriptor() ([]byte, []int) {
	return file_bizscript_proto_rawDescGZIP(), []int{14}
}

func (x *DeleteScriptReq) GetScriptId() int64 {
	if x != nil {
		return x.ScriptId
	}
	return 0
}

type DeleteScriptResp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Code int32  `protobuf:"varint,1,opt,name=code,proto3" json:"code,omitempty"` // 错误码
	Msg  string `protobuf:"bytes,2,opt,name=msg,proto3" json:"msg,omitempty"`    // 错误信息
}

func (x *DeleteScriptResp) Reset() {
	*x = DeleteScriptResp{}
	if protoimpl.UnsafeEnabled {
		mi := &file_bizscript_proto_msgTypes[15]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *DeleteScriptResp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DeleteScriptResp) ProtoMessage() {}

func (x *DeleteScriptResp) ProtoReflect() protoreflect.Message {
	mi := &file_bizscript_proto_msgTypes[15]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DeleteScriptResp.ProtoReflect.Descriptor instead.
func (*DeleteScriptResp) Descriptor() ([]byte, []int) {
	return file_bizscript_proto_rawDescGZIP(), []int{15}
}

func (x *DeleteScriptResp) GetCode() int32 {
	if x != nil {
		return x.Code
	}
	return 0
}

func (x *DeleteScriptResp) GetMsg() string {
	if x != nil {
		return x.Msg
	}
	return ""
}

// 获取剧本列表请求
type GetScriptListReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	TopicId       int64 `protobuf:"varint,1,opt,name=topic_id,json=topicId,proto3" json:"topic_id,omitempty"`                   // 话题ID，必须，不能为0
	Page          int32 `protobuf:"varint,2,opt,name=page,proto3" json:"page,omitempty"`                                        // 页码，从1开始
	PageSize      int32 `protobuf:"varint,3,opt,name=page_size,json=pageSize,proto3" json:"page_size,omitempty"`                // 每页数量，默认20
	IsAggregation bool  `protobuf:"varint,4,opt,name=is_aggregation,json=isAggregation,proto3" json:"is_aggregation,omitempty"` // 是否话题聚合页
}

func (x *GetScriptListReq) Reset() {
	*x = GetScriptListReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_bizscript_proto_msgTypes[16]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetScriptListReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetScriptListReq) ProtoMessage() {}

func (x *GetScriptListReq) ProtoReflect() protoreflect.Message {
	mi := &file_bizscript_proto_msgTypes[16]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetScriptListReq.ProtoReflect.Descriptor instead.
func (*GetScriptListReq) Descriptor() ([]byte, []int) {
	return file_bizscript_proto_rawDescGZIP(), []int{16}
}

func (x *GetScriptListReq) GetTopicId() int64 {
	if x != nil {
		return x.TopicId
	}
	return 0
}

func (x *GetScriptListReq) GetPage() int32 {
	if x != nil {
		return x.Page
	}
	return 0
}

func (x *GetScriptListReq) GetPageSize() int32 {
	if x != nil {
		return x.PageSize
	}
	return 0
}

func (x *GetScriptListReq) GetIsAggregation() bool {
	if x != nil {
		return x.IsAggregation
	}
	return false
}

// 获取剧本列表响应
type GetScriptListResp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Code int32                            `protobuf:"varint,1,opt,name=code,proto3" json:"code,omitempty"` // 错误码
	Msg  string                           `protobuf:"bytes,2,opt,name=msg,proto3" json:"msg,omitempty"`    // 错误信息
	Data *svcscript.GetScriptListRespData `protobuf:"bytes,3,opt,name=data,proto3" json:"data,omitempty"`  // 响应数据
}

func (x *GetScriptListResp) Reset() {
	*x = GetScriptListResp{}
	if protoimpl.UnsafeEnabled {
		mi := &file_bizscript_proto_msgTypes[17]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetScriptListResp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetScriptListResp) ProtoMessage() {}

func (x *GetScriptListResp) ProtoReflect() protoreflect.Message {
	mi := &file_bizscript_proto_msgTypes[17]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetScriptListResp.ProtoReflect.Descriptor instead.
func (*GetScriptListResp) Descriptor() ([]byte, []int) {
	return file_bizscript_proto_rawDescGZIP(), []int{17}
}

func (x *GetScriptListResp) GetCode() int32 {
	if x != nil {
		return x.Code
	}
	return 0
}

func (x *GetScriptListResp) GetMsg() string {
	if x != nil {
		return x.Msg
	}
	return ""
}

func (x *GetScriptListResp) GetData() *svcscript.GetScriptListRespData {
	if x != nil {
		return x.Data
	}
	return nil
}

// 获取剧本详情请求
type GetScriptDetailReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	ScriptId int64 `protobuf:"varint,1,opt,name=script_id,json=scriptId,proto3" json:"script_id,omitempty"` // 剧本ID
}

func (x *GetScriptDetailReq) Reset() {
	*x = GetScriptDetailReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_bizscript_proto_msgTypes[18]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetScriptDetailReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetScriptDetailReq) ProtoMessage() {}

func (x *GetScriptDetailReq) ProtoReflect() protoreflect.Message {
	mi := &file_bizscript_proto_msgTypes[18]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetScriptDetailReq.ProtoReflect.Descriptor instead.
func (*GetScriptDetailReq) Descriptor() ([]byte, []int) {
	return file_bizscript_proto_rawDescGZIP(), []int{18}
}

func (x *GetScriptDetailReq) GetScriptId() int64 {
	if x != nil {
		return x.ScriptId
	}
	return 0
}

// 获取剧本详情响应
type GetScriptDetailResp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Code int32             `protobuf:"varint,1,opt,name=code,proto3" json:"code,omitempty"` // 错误码
	Msg  string            `protobuf:"bytes,2,opt,name=msg,proto3" json:"msg,omitempty"`    // 错误信息
	Data *svcscript.Script `protobuf:"bytes,3,opt,name=data,proto3" json:"data,omitempty"`  // 剧本详情
}

func (x *GetScriptDetailResp) Reset() {
	*x = GetScriptDetailResp{}
	if protoimpl.UnsafeEnabled {
		mi := &file_bizscript_proto_msgTypes[19]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetScriptDetailResp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetScriptDetailResp) ProtoMessage() {}

func (x *GetScriptDetailResp) ProtoReflect() protoreflect.Message {
	mi := &file_bizscript_proto_msgTypes[19]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetScriptDetailResp.ProtoReflect.Descriptor instead.
func (*GetScriptDetailResp) Descriptor() ([]byte, []int) {
	return file_bizscript_proto_rawDescGZIP(), []int{19}
}

func (x *GetScriptDetailResp) GetCode() int32 {
	if x != nil {
		return x.Code
	}
	return 0
}

func (x *GetScriptDetailResp) GetMsg() string {
	if x != nil {
		return x.Msg
	}
	return ""
}

func (x *GetScriptDetailResp) GetData() *svcscript.Script {
	if x != nil {
		return x.Data
	}
	return nil
}

// 搜索剧本请求
type SearchReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Keyword    string               `protobuf:"bytes,1,opt,name=keyword,proto3" json:"keyword,omitempty"`                                                       // 搜索关键词
	SearchType svcscript.SearchType `protobuf:"varint,2,opt,name=search_type,json=searchType,proto3,enum=vc.svcscript.SearchType" json:"search_type,omitempty"` // 搜索类型: 1=剧本搜索, 2=用户搜索, 3=话题搜索
	Page       int32                `protobuf:"varint,3,opt,name=page,proto3" json:"page,omitempty"`                                                            // 页码，从1开始
	PageSize   int32                `protobuf:"varint,4,opt,name=page_size,json=pageSize,proto3" json:"page_size,omitempty"`                                    // 每页数量，默认20
}

func (x *SearchReq) Reset() {
	*x = SearchReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_bizscript_proto_msgTypes[20]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *SearchReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SearchReq) ProtoMessage() {}

func (x *SearchReq) ProtoReflect() protoreflect.Message {
	mi := &file_bizscript_proto_msgTypes[20]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SearchReq.ProtoReflect.Descriptor instead.
func (*SearchReq) Descriptor() ([]byte, []int) {
	return file_bizscript_proto_rawDescGZIP(), []int{20}
}

func (x *SearchReq) GetKeyword() string {
	if x != nil {
		return x.Keyword
	}
	return ""
}

func (x *SearchReq) GetSearchType() svcscript.SearchType {
	if x != nil {
		return x.SearchType
	}
	return svcscript.SearchType(0)
}

func (x *SearchReq) GetPage() int32 {
	if x != nil {
		return x.Page
	}
	return 0
}

func (x *SearchReq) GetPageSize() int32 {
	if x != nil {
		return x.PageSize
	}
	return 0
}

// 搜索剧本响应
type SearchResp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Code int32                     `protobuf:"varint,1,opt,name=code,proto3" json:"code,omitempty"` // 错误码
	Msg  string                    `protobuf:"bytes,2,opt,name=msg,proto3" json:"msg,omitempty"`    // 错误信息
	Data *svcscript.SearchRespData `protobuf:"bytes,3,opt,name=data,proto3" json:"data,omitempty"`  // 响应数据
}

func (x *SearchResp) Reset() {
	*x = SearchResp{}
	if protoimpl.UnsafeEnabled {
		mi := &file_bizscript_proto_msgTypes[21]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *SearchResp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SearchResp) ProtoMessage() {}

func (x *SearchResp) ProtoReflect() protoreflect.Message {
	mi := &file_bizscript_proto_msgTypes[21]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SearchResp.ProtoReflect.Descriptor instead.
func (*SearchResp) Descriptor() ([]byte, []int) {
	return file_bizscript_proto_rawDescGZIP(), []int{21}
}

func (x *SearchResp) GetCode() int32 {
	if x != nil {
		return x.Code
	}
	return 0
}

func (x *SearchResp) GetMsg() string {
	if x != nil {
		return x.Msg
	}
	return ""
}

func (x *SearchResp) GetData() *svcscript.SearchRespData {
	if x != nil {
		return x.Data
	}
	return nil
}

// 关注剧本请求
type FollowScriptReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	ScriptId int64 `protobuf:"varint,1,opt,name=script_id,json=scriptId,proto3" json:"script_id,omitempty"` // 剧本ID
}

func (x *FollowScriptReq) Reset() {
	*x = FollowScriptReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_bizscript_proto_msgTypes[22]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *FollowScriptReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*FollowScriptReq) ProtoMessage() {}

func (x *FollowScriptReq) ProtoReflect() protoreflect.Message {
	mi := &file_bizscript_proto_msgTypes[22]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use FollowScriptReq.ProtoReflect.Descriptor instead.
func (*FollowScriptReq) Descriptor() ([]byte, []int) {
	return file_bizscript_proto_rawDescGZIP(), []int{22}
}

func (x *FollowScriptReq) GetScriptId() int64 {
	if x != nil {
		return x.ScriptId
	}
	return 0
}

// 关注剧本响应
type FollowScriptResp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Code int32  `protobuf:"varint,1,opt,name=code,proto3" json:"code,omitempty"` // 错误码
	Msg  string `protobuf:"bytes,2,opt,name=msg,proto3" json:"msg,omitempty"`    // 错误信息
}

func (x *FollowScriptResp) Reset() {
	*x = FollowScriptResp{}
	if protoimpl.UnsafeEnabled {
		mi := &file_bizscript_proto_msgTypes[23]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *FollowScriptResp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*FollowScriptResp) ProtoMessage() {}

func (x *FollowScriptResp) ProtoReflect() protoreflect.Message {
	mi := &file_bizscript_proto_msgTypes[23]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use FollowScriptResp.ProtoReflect.Descriptor instead.
func (*FollowScriptResp) Descriptor() ([]byte, []int) {
	return file_bizscript_proto_rawDescGZIP(), []int{23}
}

func (x *FollowScriptResp) GetCode() int32 {
	if x != nil {
		return x.Code
	}
	return 0
}

func (x *FollowScriptResp) GetMsg() string {
	if x != nil {
		return x.Msg
	}
	return ""
}

// 取消关注剧本请求
type UnfollowScriptReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	ScriptId int64 `protobuf:"varint,1,opt,name=script_id,json=scriptId,proto3" json:"script_id,omitempty"` // 剧本ID
}

func (x *UnfollowScriptReq) Reset() {
	*x = UnfollowScriptReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_bizscript_proto_msgTypes[24]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *UnfollowScriptReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UnfollowScriptReq) ProtoMessage() {}

func (x *UnfollowScriptReq) ProtoReflect() protoreflect.Message {
	mi := &file_bizscript_proto_msgTypes[24]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UnfollowScriptReq.ProtoReflect.Descriptor instead.
func (*UnfollowScriptReq) Descriptor() ([]byte, []int) {
	return file_bizscript_proto_rawDescGZIP(), []int{24}
}

func (x *UnfollowScriptReq) GetScriptId() int64 {
	if x != nil {
		return x.ScriptId
	}
	return 0
}

// 取消关注剧本响应
type UnfollowScriptResp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Code int32  `protobuf:"varint,1,opt,name=code,proto3" json:"code,omitempty"` // 错误码
	Msg  string `protobuf:"bytes,2,opt,name=msg,proto3" json:"msg,omitempty"`    // 错误信息
}

func (x *UnfollowScriptResp) Reset() {
	*x = UnfollowScriptResp{}
	if protoimpl.UnsafeEnabled {
		mi := &file_bizscript_proto_msgTypes[25]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *UnfollowScriptResp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UnfollowScriptResp) ProtoMessage() {}

func (x *UnfollowScriptResp) ProtoReflect() protoreflect.Message {
	mi := &file_bizscript_proto_msgTypes[25]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UnfollowScriptResp.ProtoReflect.Descriptor instead.
func (*UnfollowScriptResp) Descriptor() ([]byte, []int) {
	return file_bizscript_proto_rawDescGZIP(), []int{25}
}

func (x *UnfollowScriptResp) GetCode() int32 {
	if x != nil {
		return x.Code
	}
	return 0
}

func (x *UnfollowScriptResp) GetMsg() string {
	if x != nil {
		return x.Msg
	}
	return ""
}

// 分享剧本请求
type ShareScriptReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	ScriptId int64  `protobuf:"varint,1,opt,name=script_id,json=scriptId,proto3" json:"script_id,omitempty"` // 剧本ID
	Platform string `protobuf:"bytes,2,opt,name=platform,proto3" json:"platform,omitempty"`                  // 分享平台
}

func (x *ShareScriptReq) Reset() {
	*x = ShareScriptReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_bizscript_proto_msgTypes[26]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ShareScriptReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ShareScriptReq) ProtoMessage() {}

func (x *ShareScriptReq) ProtoReflect() protoreflect.Message {
	mi := &file_bizscript_proto_msgTypes[26]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ShareScriptReq.ProtoReflect.Descriptor instead.
func (*ShareScriptReq) Descriptor() ([]byte, []int) {
	return file_bizscript_proto_rawDescGZIP(), []int{26}
}

func (x *ShareScriptReq) GetScriptId() int64 {
	if x != nil {
		return x.ScriptId
	}
	return 0
}

func (x *ShareScriptReq) GetPlatform() string {
	if x != nil {
		return x.Platform
	}
	return ""
}

// 分享剧本响应
type ShareScriptResp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Code int32  `protobuf:"varint,1,opt,name=code,proto3" json:"code,omitempty"` // 错误码
	Msg  string `protobuf:"bytes,2,opt,name=msg,proto3" json:"msg,omitempty"`    // 错误信息
}

func (x *ShareScriptResp) Reset() {
	*x = ShareScriptResp{}
	if protoimpl.UnsafeEnabled {
		mi := &file_bizscript_proto_msgTypes[27]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ShareScriptResp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ShareScriptResp) ProtoMessage() {}

func (x *ShareScriptResp) ProtoReflect() protoreflect.Message {
	mi := &file_bizscript_proto_msgTypes[27]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ShareScriptResp.ProtoReflect.Descriptor instead.
func (*ShareScriptResp) Descriptor() ([]byte, []int) {
	return file_bizscript_proto_rawDescGZIP(), []int{27}
}

func (x *ShareScriptResp) GetCode() int32 {
	if x != nil {
		return x.Code
	}
	return 0
}

func (x *ShareScriptResp) GetMsg() string {
	if x != nil {
		return x.Msg
	}
	return ""
}

// 获取台词完整配音列表请求
type GetLineDubbingAllReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	ScriptId int64 `protobuf:"varint,1,opt,name=script_id,json=scriptId,proto3" json:"script_id,omitempty"` // 剧本ID
	LineId   int64 `protobuf:"varint,2,opt,name=line_id,json=lineId,proto3" json:"line_id,omitempty"`       // 台词ID
	Page     int32 `protobuf:"varint,3,opt,name=page,proto3" json:"page,omitempty"`                         // 页码
	PageSize int32 `protobuf:"varint,4,opt,name=page_size,json=pageSize,proto3" json:"page_size,omitempty"` // 每页数量
}

func (x *GetLineDubbingAllReq) Reset() {
	*x = GetLineDubbingAllReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_bizscript_proto_msgTypes[28]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetLineDubbingAllReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetLineDubbingAllReq) ProtoMessage() {}

func (x *GetLineDubbingAllReq) ProtoReflect() protoreflect.Message {
	mi := &file_bizscript_proto_msgTypes[28]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetLineDubbingAllReq.ProtoReflect.Descriptor instead.
func (*GetLineDubbingAllReq) Descriptor() ([]byte, []int) {
	return file_bizscript_proto_rawDescGZIP(), []int{28}
}

func (x *GetLineDubbingAllReq) GetScriptId() int64 {
	if x != nil {
		return x.ScriptId
	}
	return 0
}

func (x *GetLineDubbingAllReq) GetLineId() int64 {
	if x != nil {
		return x.LineId
	}
	return 0
}

func (x *GetLineDubbingAllReq) GetPage() int32 {
	if x != nil {
		return x.Page
	}
	return 0
}

func (x *GetLineDubbingAllReq) GetPageSize() int32 {
	if x != nil {
		return x.PageSize
	}
	return 0
}

// 获取台词配音列表响应
type GetLineDubbingAllResp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Code int32                              `protobuf:"varint,1,opt,name=code,proto3" json:"code,omitempty"` // 错误码
	Msg  string                             `protobuf:"bytes,2,opt,name=msg,proto3" json:"msg,omitempty"`    // 错误信息
	Data *svcscript.GetLineDubbingsRespData `protobuf:"bytes,3,opt,name=data,proto3" json:"data,omitempty"`  // 响应数据
}

func (x *GetLineDubbingAllResp) Reset() {
	*x = GetLineDubbingAllResp{}
	if protoimpl.UnsafeEnabled {
		mi := &file_bizscript_proto_msgTypes[29]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetLineDubbingAllResp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetLineDubbingAllResp) ProtoMessage() {}

func (x *GetLineDubbingAllResp) ProtoReflect() protoreflect.Message {
	mi := &file_bizscript_proto_msgTypes[29]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetLineDubbingAllResp.ProtoReflect.Descriptor instead.
func (*GetLineDubbingAllResp) Descriptor() ([]byte, []int) {
	return file_bizscript_proto_rawDescGZIP(), []int{29}
}

func (x *GetLineDubbingAllResp) GetCode() int32 {
	if x != nil {
		return x.Code
	}
	return 0
}

func (x *GetLineDubbingAllResp) GetMsg() string {
	if x != nil {
		return x.Msg
	}
	return ""
}

func (x *GetLineDubbingAllResp) GetData() *svcscript.GetLineDubbingsRespData {
	if x != nil {
		return x.Data
	}
	return nil
}

// 获取台词简略配音简略列表请求 (默认返回6条，如page_size小于6取page_size,大于6取6条）
type GetLineDubbingSimpleReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	ScriptId        int64 `protobuf:"varint,1,opt,name=script_id,json=scriptId,proto3" json:"script_id,omitempty"`                        // 剧本ID
	DubbingRecordId int64 `protobuf:"varint,2,opt,name=dubbing_record_id,json=dubbingRecordId,proto3" json:"dubbing_record_id,omitempty"` // 配音记录ID（批次ID），可选，用于将指定批次的配音置顶显示
}

func (x *GetLineDubbingSimpleReq) Reset() {
	*x = GetLineDubbingSimpleReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_bizscript_proto_msgTypes[30]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetLineDubbingSimpleReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetLineDubbingSimpleReq) ProtoMessage() {}

func (x *GetLineDubbingSimpleReq) ProtoReflect() protoreflect.Message {
	mi := &file_bizscript_proto_msgTypes[30]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetLineDubbingSimpleReq.ProtoReflect.Descriptor instead.
func (*GetLineDubbingSimpleReq) Descriptor() ([]byte, []int) {
	return file_bizscript_proto_rawDescGZIP(), []int{30}
}

func (x *GetLineDubbingSimpleReq) GetScriptId() int64 {
	if x != nil {
		return x.ScriptId
	}
	return 0
}

func (x *GetLineDubbingSimpleReq) GetDubbingRecordId() int64 {
	if x != nil {
		return x.DubbingRecordId
	}
	return 0
}

// 获取台词配音简略列表响应
type GetLineDubbingSimpleResp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Code int32                                    `protobuf:"varint,1,opt,name=code,proto3" json:"code,omitempty"` // 错误码
	Msg  string                                   `protobuf:"bytes,2,opt,name=msg,proto3" json:"msg,omitempty"`    // 错误信息
	Data *svcscript.GetLineDubbingsSimpleRespData `protobuf:"bytes,3,opt,name=data,proto3" json:"data,omitempty"`  // 响应数据
}

func (x *GetLineDubbingSimpleResp) Reset() {
	*x = GetLineDubbingSimpleResp{}
	if protoimpl.UnsafeEnabled {
		mi := &file_bizscript_proto_msgTypes[31]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetLineDubbingSimpleResp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetLineDubbingSimpleResp) ProtoMessage() {}

func (x *GetLineDubbingSimpleResp) ProtoReflect() protoreflect.Message {
	mi := &file_bizscript_proto_msgTypes[31]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetLineDubbingSimpleResp.ProtoReflect.Descriptor instead.
func (*GetLineDubbingSimpleResp) Descriptor() ([]byte, []int) {
	return file_bizscript_proto_rawDescGZIP(), []int{31}
}

func (x *GetLineDubbingSimpleResp) GetCode() int32 {
	if x != nil {
		return x.Code
	}
	return 0
}

func (x *GetLineDubbingSimpleResp) GetMsg() string {
	if x != nil {
		return x.Msg
	}
	return ""
}

func (x *GetLineDubbingSimpleResp) GetData() *svcscript.GetLineDubbingsSimpleRespData {
	if x != nil {
		return x.Data
	}
	return nil
}

// 获取剧本所有台词的第一条配音请求
type GetScriptFirstDubbingReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	ScriptId int64 `protobuf:"varint,1,opt,name=script_id,json=scriptId,proto3" json:"script_id,omitempty"` // 剧本ID
}

func (x *GetScriptFirstDubbingReq) Reset() {
	*x = GetScriptFirstDubbingReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_bizscript_proto_msgTypes[32]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetScriptFirstDubbingReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetScriptFirstDubbingReq) ProtoMessage() {}

func (x *GetScriptFirstDubbingReq) ProtoReflect() protoreflect.Message {
	mi := &file_bizscript_proto_msgTypes[32]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetScriptFirstDubbingReq.ProtoReflect.Descriptor instead.
func (*GetScriptFirstDubbingReq) Descriptor() ([]byte, []int) {
	return file_bizscript_proto_rawDescGZIP(), []int{32}
}

func (x *GetScriptFirstDubbingReq) GetScriptId() int64 {
	if x != nil {
		return x.ScriptId
	}
	return 0
}

// 获取剧本所有台词的第一条配音响应
type GetScriptFirstDubbingResp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Code int32                                `protobuf:"varint,1,opt,name=code,proto3" json:"code,omitempty"` // 错误码
	Msg  string                               `protobuf:"bytes,2,opt,name=msg,proto3" json:"msg,omitempty"`    // 错误信息
	Data *svcscript.GetScriptFirstDubbingData `protobuf:"bytes,3,opt,name=data,proto3" json:"data,omitempty"`  // 响应数据
}

func (x *GetScriptFirstDubbingResp) Reset() {
	*x = GetScriptFirstDubbingResp{}
	if protoimpl.UnsafeEnabled {
		mi := &file_bizscript_proto_msgTypes[33]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetScriptFirstDubbingResp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetScriptFirstDubbingResp) ProtoMessage() {}

func (x *GetScriptFirstDubbingResp) ProtoReflect() protoreflect.Message {
	mi := &file_bizscript_proto_msgTypes[33]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetScriptFirstDubbingResp.ProtoReflect.Descriptor instead.
func (*GetScriptFirstDubbingResp) Descriptor() ([]byte, []int) {
	return file_bizscript_proto_rawDescGZIP(), []int{33}
}

func (x *GetScriptFirstDubbingResp) GetCode() int32 {
	if x != nil {
		return x.Code
	}
	return 0
}

func (x *GetScriptFirstDubbingResp) GetMsg() string {
	if x != nil {
		return x.Msg
	}
	return ""
}

func (x *GetScriptFirstDubbingResp) GetData() *svcscript.GetScriptFirstDubbingData {
	if x != nil {
		return x.Data
	}
	return nil
}

// 批量获取剧本所有台词的第一条配音请求
type BatchGetScriptFirstDubbingReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	ScriptIds []int64 `protobuf:"varint,1,rep,packed,name=script_ids,json=scriptIds,proto3" json:"script_ids,omitempty"` // 剧本ID列表，最多50个
}

func (x *BatchGetScriptFirstDubbingReq) Reset() {
	*x = BatchGetScriptFirstDubbingReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_bizscript_proto_msgTypes[34]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *BatchGetScriptFirstDubbingReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BatchGetScriptFirstDubbingReq) ProtoMessage() {}

func (x *BatchGetScriptFirstDubbingReq) ProtoReflect() protoreflect.Message {
	mi := &file_bizscript_proto_msgTypes[34]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BatchGetScriptFirstDubbingReq.ProtoReflect.Descriptor instead.
func (*BatchGetScriptFirstDubbingReq) Descriptor() ([]byte, []int) {
	return file_bizscript_proto_rawDescGZIP(), []int{34}
}

func (x *BatchGetScriptFirstDubbingReq) GetScriptIds() []int64 {
	if x != nil {
		return x.ScriptIds
	}
	return nil
}

// 批量获取剧本所有台词的第一条配音响应
type BatchGetScriptFirstDubbingResp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Code int32                                     `protobuf:"varint,1,opt,name=code,proto3" json:"code,omitempty"` // 错误码
	Msg  string                                    `protobuf:"bytes,2,opt,name=msg,proto3" json:"msg,omitempty"`    // 错误信息
	Data *svcscript.BatchGetScriptFirstDubbingData `protobuf:"bytes,3,opt,name=data,proto3" json:"data,omitempty"`  // 响应数据
}

func (x *BatchGetScriptFirstDubbingResp) Reset() {
	*x = BatchGetScriptFirstDubbingResp{}
	if protoimpl.UnsafeEnabled {
		mi := &file_bizscript_proto_msgTypes[35]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *BatchGetScriptFirstDubbingResp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BatchGetScriptFirstDubbingResp) ProtoMessage() {}

func (x *BatchGetScriptFirstDubbingResp) ProtoReflect() protoreflect.Message {
	mi := &file_bizscript_proto_msgTypes[35]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BatchGetScriptFirstDubbingResp.ProtoReflect.Descriptor instead.
func (*BatchGetScriptFirstDubbingResp) Descriptor() ([]byte, []int) {
	return file_bizscript_proto_rawDescGZIP(), []int{35}
}

func (x *BatchGetScriptFirstDubbingResp) GetCode() int32 {
	if x != nil {
		return x.Code
	}
	return 0
}

func (x *BatchGetScriptFirstDubbingResp) GetMsg() string {
	if x != nil {
		return x.Msg
	}
	return ""
}

func (x *BatchGetScriptFirstDubbingResp) GetData() *svcscript.BatchGetScriptFirstDubbingData {
	if x != nil {
		return x.Data
	}
	return nil
}

// 创建配音请求
type CreateDubbingReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	ScriptId     int64                             `protobuf:"varint,1,opt,name=script_id,json=scriptId,proto3" json:"script_id,omitempty"`                                                                                                     // 剧本ID
	LineDubbings map[int64]*svcscript.LineDubbings `protobuf:"bytes,2,rep,name=line_dubbings,json=lineDubbings,proto3" json:"line_dubbings,omitempty" protobuf_key:"varint,1,opt,name=key,proto3" protobuf_val:"bytes,2,opt,name=value,proto3"` // 台词配音列表 map<lineID, LineDubbings>
}

func (x *CreateDubbingReq) Reset() {
	*x = CreateDubbingReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_bizscript_proto_msgTypes[36]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CreateDubbingReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CreateDubbingReq) ProtoMessage() {}

func (x *CreateDubbingReq) ProtoReflect() protoreflect.Message {
	mi := &file_bizscript_proto_msgTypes[36]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CreateDubbingReq.ProtoReflect.Descriptor instead.
func (*CreateDubbingReq) Descriptor() ([]byte, []int) {
	return file_bizscript_proto_rawDescGZIP(), []int{36}
}

func (x *CreateDubbingReq) GetScriptId() int64 {
	if x != nil {
		return x.ScriptId
	}
	return 0
}

func (x *CreateDubbingReq) GetLineDubbings() map[int64]*svcscript.LineDubbings {
	if x != nil {
		return x.LineDubbings
	}
	return nil
}

// 创建配音响应
type CreateDubbingResp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Code int32                            `protobuf:"varint,1,opt,name=code,proto3" json:"code,omitempty"` // 错误码
	Msg  string                           `protobuf:"bytes,2,opt,name=msg,proto3" json:"msg,omitempty"`    // 错误信息
	Data *svcscript.CreateDubbingRespData `protobuf:"bytes,3,opt,name=data,proto3" json:"data,omitempty"`  // 响应数据
}

func (x *CreateDubbingResp) Reset() {
	*x = CreateDubbingResp{}
	if protoimpl.UnsafeEnabled {
		mi := &file_bizscript_proto_msgTypes[37]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CreateDubbingResp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CreateDubbingResp) ProtoMessage() {}

func (x *CreateDubbingResp) ProtoReflect() protoreflect.Message {
	mi := &file_bizscript_proto_msgTypes[37]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CreateDubbingResp.ProtoReflect.Descriptor instead.
func (*CreateDubbingResp) Descriptor() ([]byte, []int) {
	return file_bizscript_proto_rawDescGZIP(), []int{37}
}

func (x *CreateDubbingResp) GetCode() int32 {
	if x != nil {
		return x.Code
	}
	return 0
}

func (x *CreateDubbingResp) GetMsg() string {
	if x != nil {
		return x.Msg
	}
	return ""
}

func (x *CreateDubbingResp) GetData() *svcscript.CreateDubbingRespData {
	if x != nil {
		return x.Data
	}
	return nil
}

// 删除配音记录请求
type DeleteDubbingRecordReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	DubbingRecordId int64 `protobuf:"varint,1,opt,name=dubbing_record_id,json=dubbingRecordId,proto3" json:"dubbing_record_id,omitempty"` // 配音记录ID
}

func (x *DeleteDubbingRecordReq) Reset() {
	*x = DeleteDubbingRecordReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_bizscript_proto_msgTypes[38]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *DeleteDubbingRecordReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DeleteDubbingRecordReq) ProtoMessage() {}

func (x *DeleteDubbingRecordReq) ProtoReflect() protoreflect.Message {
	mi := &file_bizscript_proto_msgTypes[38]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DeleteDubbingRecordReq.ProtoReflect.Descriptor instead.
func (*DeleteDubbingRecordReq) Descriptor() ([]byte, []int) {
	return file_bizscript_proto_rawDescGZIP(), []int{38}
}

func (x *DeleteDubbingRecordReq) GetDubbingRecordId() int64 {
	if x != nil {
		return x.DubbingRecordId
	}
	return 0
}

// 删除配音记录响应
type DeleteDubbingRecordResp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Code int32  `protobuf:"varint,1,opt,name=code,proto3" json:"code,omitempty"` // 错误码
	Msg  string `protobuf:"bytes,2,opt,name=msg,proto3" json:"msg,omitempty"`    // 错误信息
}

func (x *DeleteDubbingRecordResp) Reset() {
	*x = DeleteDubbingRecordResp{}
	if protoimpl.UnsafeEnabled {
		mi := &file_bizscript_proto_msgTypes[39]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *DeleteDubbingRecordResp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DeleteDubbingRecordResp) ProtoMessage() {}

func (x *DeleteDubbingRecordResp) ProtoReflect() protoreflect.Message {
	mi := &file_bizscript_proto_msgTypes[39]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DeleteDubbingRecordResp.ProtoReflect.Descriptor instead.
func (*DeleteDubbingRecordResp) Descriptor() ([]byte, []int) {
	return file_bizscript_proto_rawDescGZIP(), []int{39}
}

func (x *DeleteDubbingRecordResp) GetCode() int32 {
	if x != nil {
		return x.Code
	}
	return 0
}

func (x *DeleteDubbingRecordResp) GetMsg() string {
	if x != nil {
		return x.Msg
	}
	return ""
}

// 获取评论列表请求
type GetCommentListReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	CommentType svcscript.CommentType `protobuf:"varint,1,opt,name=comment_type,json=commentType,proto3,enum=vc.svcscript.CommentType" json:"comment_type,omitempty"` // 评论类型: 1=剧本评论, 2=配音评论
	ScriptId    int64                 `protobuf:"varint,2,opt,name=script_id,json=scriptId,proto3" json:"script_id,omitempty"`                                        // 剧本ID，当comment_type=1时必填
	DubbingId   int64                 `protobuf:"varint,3,opt,name=dubbing_id,json=dubbingId,proto3" json:"dubbing_id,omitempty"`                                     // 配音ID，当comment_type=2时必填
	ParentId    int64                 `protobuf:"varint,4,opt,name=parent_id,json=parentId,proto3" json:"parent_id,omitempty"`                                        // 父评论ID，获取二级评论时填写，默认为0表示获取一级评论
	Page        int32                 `protobuf:"varint,5,opt,name=page,proto3" json:"page,omitempty"`                                                                // 页码，从1开始
	PageSize    int32                 `protobuf:"varint,6,opt,name=page_size,json=pageSize,proto3" json:"page_size,omitempty"`                                        // 每页数量，默认20
}

func (x *GetCommentListReq) Reset() {
	*x = GetCommentListReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_bizscript_proto_msgTypes[40]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetCommentListReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetCommentListReq) ProtoMessage() {}

func (x *GetCommentListReq) ProtoReflect() protoreflect.Message {
	mi := &file_bizscript_proto_msgTypes[40]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetCommentListReq.ProtoReflect.Descriptor instead.
func (*GetCommentListReq) Descriptor() ([]byte, []int) {
	return file_bizscript_proto_rawDescGZIP(), []int{40}
}

func (x *GetCommentListReq) GetCommentType() svcscript.CommentType {
	if x != nil {
		return x.CommentType
	}
	return svcscript.CommentType(0)
}

func (x *GetCommentListReq) GetScriptId() int64 {
	if x != nil {
		return x.ScriptId
	}
	return 0
}

func (x *GetCommentListReq) GetDubbingId() int64 {
	if x != nil {
		return x.DubbingId
	}
	return 0
}

func (x *GetCommentListReq) GetParentId() int64 {
	if x != nil {
		return x.ParentId
	}
	return 0
}

func (x *GetCommentListReq) GetPage() int32 {
	if x != nil {
		return x.Page
	}
	return 0
}

func (x *GetCommentListReq) GetPageSize() int32 {
	if x != nil {
		return x.PageSize
	}
	return 0
}

// 获取评论列表响应
type GetCommentListResp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Code int32                             `protobuf:"varint,1,opt,name=code,proto3" json:"code,omitempty"` // 错误码
	Msg  string                            `protobuf:"bytes,2,opt,name=msg,proto3" json:"msg,omitempty"`    // 错误信息
	Data *svcscript.GetCommentListRespData `protobuf:"bytes,3,opt,name=data,proto3" json:"data,omitempty"`  // 响应数据
}

func (x *GetCommentListResp) Reset() {
	*x = GetCommentListResp{}
	if protoimpl.UnsafeEnabled {
		mi := &file_bizscript_proto_msgTypes[41]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetCommentListResp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetCommentListResp) ProtoMessage() {}

func (x *GetCommentListResp) ProtoReflect() protoreflect.Message {
	mi := &file_bizscript_proto_msgTypes[41]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetCommentListResp.ProtoReflect.Descriptor instead.
func (*GetCommentListResp) Descriptor() ([]byte, []int) {
	return file_bizscript_proto_rawDescGZIP(), []int{41}
}

func (x *GetCommentListResp) GetCode() int32 {
	if x != nil {
		return x.Code
	}
	return 0
}

func (x *GetCommentListResp) GetMsg() string {
	if x != nil {
		return x.Msg
	}
	return ""
}

func (x *GetCommentListResp) GetData() *svcscript.GetCommentListRespData {
	if x != nil {
		return x.Data
	}
	return nil
}

// 获取评论回复列表请求
type GetCommentRepliesReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	ParentId int64 `protobuf:"varint,1,opt,name=parent_id,json=parentId,proto3" json:"parent_id,omitempty"` // 父评论ID，指定要获取哪个评论的回复
	Page     int32 `protobuf:"varint,2,opt,name=page,proto3" json:"page,omitempty"`                         // 页码，从1开始
	PageSize int32 `protobuf:"varint,3,opt,name=page_size,json=pageSize,proto3" json:"page_size,omitempty"` // 每页数量，默认20
}

func (x *GetCommentRepliesReq) Reset() {
	*x = GetCommentRepliesReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_bizscript_proto_msgTypes[42]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetCommentRepliesReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetCommentRepliesReq) ProtoMessage() {}

func (x *GetCommentRepliesReq) ProtoReflect() protoreflect.Message {
	mi := &file_bizscript_proto_msgTypes[42]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetCommentRepliesReq.ProtoReflect.Descriptor instead.
func (*GetCommentRepliesReq) Descriptor() ([]byte, []int) {
	return file_bizscript_proto_rawDescGZIP(), []int{42}
}

func (x *GetCommentRepliesReq) GetParentId() int64 {
	if x != nil {
		return x.ParentId
	}
	return 0
}

func (x *GetCommentRepliesReq) GetPage() int32 {
	if x != nil {
		return x.Page
	}
	return 0
}

func (x *GetCommentRepliesReq) GetPageSize() int32 {
	if x != nil {
		return x.PageSize
	}
	return 0
}

// 获取评论回复列表响应
type GetCommentRepliesResp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Code int32                             `protobuf:"varint,1,opt,name=code,proto3" json:"code,omitempty"` // 错误码
	Msg  string                            `protobuf:"bytes,2,opt,name=msg,proto3" json:"msg,omitempty"`    // 错误信息
	Data *svcscript.GetCommentListRespData `protobuf:"bytes,3,opt,name=data,proto3" json:"data,omitempty"`  // 响应数据
}

func (x *GetCommentRepliesResp) Reset() {
	*x = GetCommentRepliesResp{}
	if protoimpl.UnsafeEnabled {
		mi := &file_bizscript_proto_msgTypes[43]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetCommentRepliesResp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetCommentRepliesResp) ProtoMessage() {}

func (x *GetCommentRepliesResp) ProtoReflect() protoreflect.Message {
	mi := &file_bizscript_proto_msgTypes[43]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetCommentRepliesResp.ProtoReflect.Descriptor instead.
func (*GetCommentRepliesResp) Descriptor() ([]byte, []int) {
	return file_bizscript_proto_rawDescGZIP(), []int{43}
}

func (x *GetCommentRepliesResp) GetCode() int32 {
	if x != nil {
		return x.Code
	}
	return 0
}

func (x *GetCommentRepliesResp) GetMsg() string {
	if x != nil {
		return x.Msg
	}
	return ""
}

func (x *GetCommentRepliesResp) GetData() *svcscript.GetCommentListRespData {
	if x != nil {
		return x.Data
	}
	return nil
}

// 创建评论请求
type CreateCommentReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	CommentType           svcscript.CommentType `protobuf:"varint,1,opt,name=comment_type,json=commentType,proto3,enum=vc.svcscript.CommentType" json:"comment_type,omitempty"`    // 评论类型: 1=剧本评论, 2=配音评论
	ParentId              int64                 `protobuf:"varint,2,opt,name=parent_id,json=parentId,proto3" json:"parent_id,omitempty"`                                           // 父评论ID，回复评论时填写，默认为0表示创建一级评论
	ScriptId              int64                 `protobuf:"varint,3,opt,name=script_id,json=scriptId,proto3" json:"script_id,omitempty"`                                           // 剧本ID，当comment_type=1时必填
	DubbingId             int64                 `protobuf:"varint,4,opt,name=dubbing_id,json=dubbingId,proto3" json:"dubbing_id,omitempty"`                                        // 配音ID，当comment_type=2时必填
	CharacterId           int64                 `protobuf:"varint,5,opt,name=character_id,json=characterId,proto3" json:"character_id,omitempty"`                                  // 角色ID，用户选择的评论角色
	CharacterAssetId      int64                 `protobuf:"varint,6,opt,name=character_asset_id,json=characterAssetId,proto3" json:"character_asset_id,omitempty"`                 //角色资源ID
	ContentType           svcscript.ContentType `protobuf:"varint,7,opt,name=content_type,json=contentType,proto3,enum=vc.svcscript.ContentType" json:"content_type,omitempty"`    // 内容类型: 1=文本, 2=语音
	Content               string                `protobuf:"bytes,8,opt,name=content,proto3" json:"content,omitempty"`                                                              // 评论内容，当content_type=1时必填
	VoiceUrl              string                `protobuf:"bytes,9,opt,name=voice_url,json=voiceUrl,proto3" json:"voice_url,omitempty"`                                            // 语音地址，当content_type=2时必填
	VoiceDuration         int32                 `protobuf:"varint,10,opt,name=voice_duration,json=voiceDuration,proto3" json:"voice_duration,omitempty"`                           // 语音时长（毫秒），当content_type=2时必填
	OriginalVoiceUrl      string                `protobuf:"bytes,12,opt,name=original_voice_url,json=originalVoiceUrl,proto3" json:"original_voice_url,omitempty"`                 // 原声地址 当content_type=2时必填
	OriginalVoiceDuration int32                 `protobuf:"varint,13,opt,name=original_voice_duration,json=originalVoiceDuration,proto3" json:"original_voice_duration,omitempty"` // 原声时长（毫秒）
	SvcVoiceUrl           string                `protobuf:"bytes,14,opt,name=svc_voice_url,json=svcVoiceUrl,proto3" json:"svc_voice_url,omitempty"`                                // 服务端变卖语音地址 当content_type=2时必填
	SvcVoiceDuration      int32                 `protobuf:"varint,15,opt,name=svc_voice_duration,json=svcVoiceDuration,proto3" json:"svc_voice_duration,omitempty"`                // 服务端变卖语音时长（毫秒）
}

func (x *CreateCommentReq) Reset() {
	*x = CreateCommentReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_bizscript_proto_msgTypes[44]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CreateCommentReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CreateCommentReq) ProtoMessage() {}

func (x *CreateCommentReq) ProtoReflect() protoreflect.Message {
	mi := &file_bizscript_proto_msgTypes[44]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CreateCommentReq.ProtoReflect.Descriptor instead.
func (*CreateCommentReq) Descriptor() ([]byte, []int) {
	return file_bizscript_proto_rawDescGZIP(), []int{44}
}

func (x *CreateCommentReq) GetCommentType() svcscript.CommentType {
	if x != nil {
		return x.CommentType
	}
	return svcscript.CommentType(0)
}

func (x *CreateCommentReq) GetParentId() int64 {
	if x != nil {
		return x.ParentId
	}
	return 0
}

func (x *CreateCommentReq) GetScriptId() int64 {
	if x != nil {
		return x.ScriptId
	}
	return 0
}

func (x *CreateCommentReq) GetDubbingId() int64 {
	if x != nil {
		return x.DubbingId
	}
	return 0
}

func (x *CreateCommentReq) GetCharacterId() int64 {
	if x != nil {
		return x.CharacterId
	}
	return 0
}

func (x *CreateCommentReq) GetCharacterAssetId() int64 {
	if x != nil {
		return x.CharacterAssetId
	}
	return 0
}

func (x *CreateCommentReq) GetContentType() svcscript.ContentType {
	if x != nil {
		return x.ContentType
	}
	return svcscript.ContentType(0)
}

func (x *CreateCommentReq) GetContent() string {
	if x != nil {
		return x.Content
	}
	return ""
}

func (x *CreateCommentReq) GetVoiceUrl() string {
	if x != nil {
		return x.VoiceUrl
	}
	return ""
}

func (x *CreateCommentReq) GetVoiceDuration() int32 {
	if x != nil {
		return x.VoiceDuration
	}
	return 0
}

func (x *CreateCommentReq) GetOriginalVoiceUrl() string {
	if x != nil {
		return x.OriginalVoiceUrl
	}
	return ""
}

func (x *CreateCommentReq) GetOriginalVoiceDuration() int32 {
	if x != nil {
		return x.OriginalVoiceDuration
	}
	return 0
}

func (x *CreateCommentReq) GetSvcVoiceUrl() string {
	if x != nil {
		return x.SvcVoiceUrl
	}
	return ""
}

func (x *CreateCommentReq) GetSvcVoiceDuration() int32 {
	if x != nil {
		return x.SvcVoiceDuration
	}
	return 0
}

// 创建评论响应
type CreateCommentResp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Code int32                            `protobuf:"varint,1,opt,name=code,proto3" json:"code,omitempty"` // 错误码
	Msg  string                           `protobuf:"bytes,2,opt,name=msg,proto3" json:"msg,omitempty"`    // 错误信息
	Data *svcscript.CreateCommentRespData `protobuf:"bytes,3,opt,name=data,proto3" json:"data,omitempty"`  // 响应数据
}

func (x *CreateCommentResp) Reset() {
	*x = CreateCommentResp{}
	if protoimpl.UnsafeEnabled {
		mi := &file_bizscript_proto_msgTypes[45]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CreateCommentResp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CreateCommentResp) ProtoMessage() {}

func (x *CreateCommentResp) ProtoReflect() protoreflect.Message {
	mi := &file_bizscript_proto_msgTypes[45]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CreateCommentResp.ProtoReflect.Descriptor instead.
func (*CreateCommentResp) Descriptor() ([]byte, []int) {
	return file_bizscript_proto_rawDescGZIP(), []int{45}
}

func (x *CreateCommentResp) GetCode() int32 {
	if x != nil {
		return x.Code
	}
	return 0
}

func (x *CreateCommentResp) GetMsg() string {
	if x != nil {
		return x.Msg
	}
	return ""
}

func (x *CreateCommentResp) GetData() *svcscript.CreateCommentRespData {
	if x != nil {
		return x.Data
	}
	return nil
}

// 设置评论置顶状态请求
type SetCommentTopReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	CommentId int64 `protobuf:"varint,1,opt,name=comment_id,json=commentId,proto3" json:"comment_id,omitempty"` // 评论ID
	IsTop     bool  `protobuf:"varint,2,opt,name=is_top,json=isTop,proto3" json:"is_top,omitempty"`             // 是否置顶
}

func (x *SetCommentTopReq) Reset() {
	*x = SetCommentTopReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_bizscript_proto_msgTypes[46]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *SetCommentTopReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SetCommentTopReq) ProtoMessage() {}

func (x *SetCommentTopReq) ProtoReflect() protoreflect.Message {
	mi := &file_bizscript_proto_msgTypes[46]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SetCommentTopReq.ProtoReflect.Descriptor instead.
func (*SetCommentTopReq) Descriptor() ([]byte, []int) {
	return file_bizscript_proto_rawDescGZIP(), []int{46}
}

func (x *SetCommentTopReq) GetCommentId() int64 {
	if x != nil {
		return x.CommentId
	}
	return 0
}

func (x *SetCommentTopReq) GetIsTop() bool {
	if x != nil {
		return x.IsTop
	}
	return false
}

// 设置评论置顶状态响应
type SetCommentTopResp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Code int32  `protobuf:"varint,1,opt,name=code,proto3" json:"code,omitempty"` // 错误码
	Msg  string `protobuf:"bytes,2,opt,name=msg,proto3" json:"msg,omitempty"`    // 错误信息
}

func (x *SetCommentTopResp) Reset() {
	*x = SetCommentTopResp{}
	if protoimpl.UnsafeEnabled {
		mi := &file_bizscript_proto_msgTypes[47]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *SetCommentTopResp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SetCommentTopResp) ProtoMessage() {}

func (x *SetCommentTopResp) ProtoReflect() protoreflect.Message {
	mi := &file_bizscript_proto_msgTypes[47]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SetCommentTopResp.ProtoReflect.Descriptor instead.
func (*SetCommentTopResp) Descriptor() ([]byte, []int) {
	return file_bizscript_proto_rawDescGZIP(), []int{47}
}

func (x *SetCommentTopResp) GetCode() int32 {
	if x != nil {
		return x.Code
	}
	return 0
}

func (x *SetCommentTopResp) GetMsg() string {
	if x != nil {
		return x.Msg
	}
	return ""
}

// 删除评论请求
type DeleteCommentReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	CommentId int64 `protobuf:"varint,1,opt,name=comment_id,json=commentId,proto3" json:"comment_id,omitempty"` // 评论ID
}

func (x *DeleteCommentReq) Reset() {
	*x = DeleteCommentReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_bizscript_proto_msgTypes[48]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *DeleteCommentReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DeleteCommentReq) ProtoMessage() {}

func (x *DeleteCommentReq) ProtoReflect() protoreflect.Message {
	mi := &file_bizscript_proto_msgTypes[48]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DeleteCommentReq.ProtoReflect.Descriptor instead.
func (*DeleteCommentReq) Descriptor() ([]byte, []int) {
	return file_bizscript_proto_rawDescGZIP(), []int{48}
}

func (x *DeleteCommentReq) GetCommentId() int64 {
	if x != nil {
		return x.CommentId
	}
	return 0
}

// 删除评论响应
type DeleteCommentResp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Code int32  `protobuf:"varint,1,opt,name=code,proto3" json:"code,omitempty"` // 错误码
	Msg  string `protobuf:"bytes,2,opt,name=msg,proto3" json:"msg,omitempty"`    // 错误信息
}

func (x *DeleteCommentResp) Reset() {
	*x = DeleteCommentResp{}
	if protoimpl.UnsafeEnabled {
		mi := &file_bizscript_proto_msgTypes[49]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *DeleteCommentResp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DeleteCommentResp) ProtoMessage() {}

func (x *DeleteCommentResp) ProtoReflect() protoreflect.Message {
	mi := &file_bizscript_proto_msgTypes[49]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DeleteCommentResp.ProtoReflect.Descriptor instead.
func (*DeleteCommentResp) Descriptor() ([]byte, []int) {
	return file_bizscript_proto_rawDescGZIP(), []int{49}
}

func (x *DeleteCommentResp) GetCode() int32 {
	if x != nil {
		return x.Code
	}
	return 0
}

func (x *DeleteCommentResp) GetMsg() string {
	if x != nil {
		return x.Msg
	}
	return ""
}

// 获取评论ASR请求
type GetCommentASRReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	CommentId int64 `protobuf:"varint,1,opt,name=comment_id,json=commentId,proto3" json:"comment_id,omitempty"` // 评论ID
}

func (x *GetCommentASRReq) Reset() {
	*x = GetCommentASRReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_bizscript_proto_msgTypes[50]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetCommentASRReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetCommentASRReq) ProtoMessage() {}

func (x *GetCommentASRReq) ProtoReflect() protoreflect.Message {
	mi := &file_bizscript_proto_msgTypes[50]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetCommentASRReq.ProtoReflect.Descriptor instead.
func (*GetCommentASRReq) Descriptor() ([]byte, []int) {
	return file_bizscript_proto_rawDescGZIP(), []int{50}
}

func (x *GetCommentASRReq) GetCommentId() int64 {
	if x != nil {
		return x.CommentId
	}
	return 0
}

// 获取评论ASR响应
type GetCommentASRResp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Code int32                            `protobuf:"varint,1,opt,name=code,proto3" json:"code,omitempty"` // 错误码
	Msg  string                           `protobuf:"bytes,2,opt,name=msg,proto3" json:"msg,omitempty"`    // 错误信息
	Data *svcscript.GetCommentASRRespData `protobuf:"bytes,3,opt,name=data,proto3" json:"data,omitempty"`  // 响应数据
}

func (x *GetCommentASRResp) Reset() {
	*x = GetCommentASRResp{}
	if protoimpl.UnsafeEnabled {
		mi := &file_bizscript_proto_msgTypes[51]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetCommentASRResp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetCommentASRResp) ProtoMessage() {}

func (x *GetCommentASRResp) ProtoReflect() protoreflect.Message {
	mi := &file_bizscript_proto_msgTypes[51]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetCommentASRResp.ProtoReflect.Descriptor instead.
func (*GetCommentASRResp) Descriptor() ([]byte, []int) {
	return file_bizscript_proto_rawDescGZIP(), []int{51}
}

func (x *GetCommentASRResp) GetCode() int32 {
	if x != nil {
		return x.Code
	}
	return 0
}

func (x *GetCommentASRResp) GetMsg() string {
	if x != nil {
		return x.Msg
	}
	return ""
}

func (x *GetCommentASRResp) GetData() *svcscript.GetCommentASRRespData {
	if x != nil {
		return x.Data
	}
	return nil
}

// 点赞请求
type LikeReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	LikeType svcscript.LikeType `protobuf:"varint,1,opt,name=like_type,json=likeType,proto3,enum=vc.svcscript.LikeType" json:"like_type,omitempty"` // 点赞类型: 1=剧本点赞, 2=配音点赞, 3=评论点赞
	TargetId int64              `protobuf:"varint,2,opt,name=target_id,json=targetId,proto3" json:"target_id,omitempty"`                            // 目标ID，根据like_type对应剧本ID、配音ID或评论ID
}

func (x *LikeReq) Reset() {
	*x = LikeReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_bizscript_proto_msgTypes[52]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *LikeReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*LikeReq) ProtoMessage() {}

func (x *LikeReq) ProtoReflect() protoreflect.Message {
	mi := &file_bizscript_proto_msgTypes[52]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use LikeReq.ProtoReflect.Descriptor instead.
func (*LikeReq) Descriptor() ([]byte, []int) {
	return file_bizscript_proto_rawDescGZIP(), []int{52}
}

func (x *LikeReq) GetLikeType() svcscript.LikeType {
	if x != nil {
		return x.LikeType
	}
	return svcscript.LikeType(0)
}

func (x *LikeReq) GetTargetId() int64 {
	if x != nil {
		return x.TargetId
	}
	return 0
}

// 点赞响应
type LikeResp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Code int32  `protobuf:"varint,1,opt,name=code,proto3" json:"code,omitempty"` // 错误码
	Msg  string `protobuf:"bytes,2,opt,name=msg,proto3" json:"msg,omitempty"`    // 错误信息
}

func (x *LikeResp) Reset() {
	*x = LikeResp{}
	if protoimpl.UnsafeEnabled {
		mi := &file_bizscript_proto_msgTypes[53]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *LikeResp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*LikeResp) ProtoMessage() {}

func (x *LikeResp) ProtoReflect() protoreflect.Message {
	mi := &file_bizscript_proto_msgTypes[53]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use LikeResp.ProtoReflect.Descriptor instead.
func (*LikeResp) Descriptor() ([]byte, []int) {
	return file_bizscript_proto_rawDescGZIP(), []int{53}
}

func (x *LikeResp) GetCode() int32 {
	if x != nil {
		return x.Code
	}
	return 0
}

func (x *LikeResp) GetMsg() string {
	if x != nil {
		return x.Msg
	}
	return ""
}

// 取消点赞请求
type UnlikeReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	LikeType svcscript.LikeType `protobuf:"varint,1,opt,name=like_type,json=likeType,proto3,enum=vc.svcscript.LikeType" json:"like_type,omitempty"` // 点赞类型: 1=剧本点赞, 2=配音点赞, 3=评论点赞
	TargetId int64              `protobuf:"varint,2,opt,name=target_id,json=targetId,proto3" json:"target_id,omitempty"`                            // 目标ID，根据like_type对应剧本ID、配音ID或评论ID
}

func (x *UnlikeReq) Reset() {
	*x = UnlikeReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_bizscript_proto_msgTypes[54]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *UnlikeReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UnlikeReq) ProtoMessage() {}

func (x *UnlikeReq) ProtoReflect() protoreflect.Message {
	mi := &file_bizscript_proto_msgTypes[54]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UnlikeReq.ProtoReflect.Descriptor instead.
func (*UnlikeReq) Descriptor() ([]byte, []int) {
	return file_bizscript_proto_rawDescGZIP(), []int{54}
}

func (x *UnlikeReq) GetLikeType() svcscript.LikeType {
	if x != nil {
		return x.LikeType
	}
	return svcscript.LikeType(0)
}

func (x *UnlikeReq) GetTargetId() int64 {
	if x != nil {
		return x.TargetId
	}
	return 0
}

// 取消点赞响应
type UnlikeResp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Code int32  `protobuf:"varint,1,opt,name=code,proto3" json:"code,omitempty"` // 错误码
	Msg  string `protobuf:"bytes,2,opt,name=msg,proto3" json:"msg,omitempty"`    // 错误信息
}

func (x *UnlikeResp) Reset() {
	*x = UnlikeResp{}
	if protoimpl.UnsafeEnabled {
		mi := &file_bizscript_proto_msgTypes[55]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *UnlikeResp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UnlikeResp) ProtoMessage() {}

func (x *UnlikeResp) ProtoReflect() protoreflect.Message {
	mi := &file_bizscript_proto_msgTypes[55]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UnlikeResp.ProtoReflect.Descriptor instead.
func (*UnlikeResp) Descriptor() ([]byte, []int) {
	return file_bizscript_proto_rawDescGZIP(), []int{55}
}

func (x *UnlikeResp) GetCode() int32 {
	if x != nil {
		return x.Code
	}
	return 0
}

func (x *UnlikeResp) GetMsg() string {
	if x != nil {
		return x.Msg
	}
	return ""
}

// 批量获取点赞请求
type BatchGetLikesReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	LikeType  svcscript.LikeType `protobuf:"varint,1,opt,name=like_type,json=likeType,proto3,enum=vc.svcscript.LikeType" json:"like_type,omitempty"` // 点赞类型: 1=剧本点赞, 2=配音点赞, 3=评论点赞
	TargetIds []int64            `protobuf:"varint,2,rep,packed,name=target_ids,json=targetIds,proto3" json:"target_ids,omitempty"`                  // 目标ID列表
}

func (x *BatchGetLikesReq) Reset() {
	*x = BatchGetLikesReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_bizscript_proto_msgTypes[56]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *BatchGetLikesReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BatchGetLikesReq) ProtoMessage() {}

func (x *BatchGetLikesReq) ProtoReflect() protoreflect.Message {
	mi := &file_bizscript_proto_msgTypes[56]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BatchGetLikesReq.ProtoReflect.Descriptor instead.
func (*BatchGetLikesReq) Descriptor() ([]byte, []int) {
	return file_bizscript_proto_rawDescGZIP(), []int{56}
}

func (x *BatchGetLikesReq) GetLikeType() svcscript.LikeType {
	if x != nil {
		return x.LikeType
	}
	return svcscript.LikeType(0)
}

func (x *BatchGetLikesReq) GetTargetIds() []int64 {
	if x != nil {
		return x.TargetIds
	}
	return nil
}

// 批量获取点赞响应
type BatchGetLikesResp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Code int32           `protobuf:"varint,1,opt,name=code,proto3" json:"code,omitempty"`                                                                                          // 错误码
	Msg  string          `protobuf:"bytes,2,opt,name=msg,proto3" json:"msg,omitempty"`                                                                                             // 错误信息
	Data map[int64]int32 `protobuf:"bytes,3,rep,name=data,proto3" json:"data,omitempty" protobuf_key:"varint,1,opt,name=key,proto3" protobuf_val:"varint,2,opt,name=value,proto3"` // 点赞数 map<targetID, likes>
}

func (x *BatchGetLikesResp) Reset() {
	*x = BatchGetLikesResp{}
	if protoimpl.UnsafeEnabled {
		mi := &file_bizscript_proto_msgTypes[57]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *BatchGetLikesResp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BatchGetLikesResp) ProtoMessage() {}

func (x *BatchGetLikesResp) ProtoReflect() protoreflect.Message {
	mi := &file_bizscript_proto_msgTypes[57]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BatchGetLikesResp.ProtoReflect.Descriptor instead.
func (*BatchGetLikesResp) Descriptor() ([]byte, []int) {
	return file_bizscript_proto_rawDescGZIP(), []int{57}
}

func (x *BatchGetLikesResp) GetCode() int32 {
	if x != nil {
		return x.Code
	}
	return 0
}

func (x *BatchGetLikesResp) GetMsg() string {
	if x != nil {
		return x.Msg
	}
	return ""
}

func (x *BatchGetLikesResp) GetData() map[int64]int32 {
	if x != nil {
		return x.Data
	}
	return nil
}

// 获取举报原因列表请求
type GetReportReasonsReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	ReportType svcscript.ReportType `protobuf:"varint,1,opt,name=report_type,json=reportType,proto3,enum=vc.svcscript.ReportType" json:"report_type,omitempty"` // 举报类型: 1=剧本举报, 2=配音举报, 3=评论举报
}

func (x *GetReportReasonsReq) Reset() {
	*x = GetReportReasonsReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_bizscript_proto_msgTypes[58]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetReportReasonsReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetReportReasonsReq) ProtoMessage() {}

func (x *GetReportReasonsReq) ProtoReflect() protoreflect.Message {
	mi := &file_bizscript_proto_msgTypes[58]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetReportReasonsReq.ProtoReflect.Descriptor instead.
func (*GetReportReasonsReq) Descriptor() ([]byte, []int) {
	return file_bizscript_proto_rawDescGZIP(), []int{58}
}

func (x *GetReportReasonsReq) GetReportType() svcscript.ReportType {
	if x != nil {
		return x.ReportType
	}
	return svcscript.ReportType(0)
}

// 获取举报原因列表响应
type GetReportReasonsResp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Code int32                               `protobuf:"varint,1,opt,name=code,proto3" json:"code,omitempty"` // 错误码
	Msg  string                              `protobuf:"bytes,2,opt,name=msg,proto3" json:"msg,omitempty"`    // 错误信息
	Data *svcscript.GetReportReasonsRespData `protobuf:"bytes,3,opt,name=data,proto3" json:"data,omitempty"`  // 响应数据
}

func (x *GetReportReasonsResp) Reset() {
	*x = GetReportReasonsResp{}
	if protoimpl.UnsafeEnabled {
		mi := &file_bizscript_proto_msgTypes[59]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetReportReasonsResp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetReportReasonsResp) ProtoMessage() {}

func (x *GetReportReasonsResp) ProtoReflect() protoreflect.Message {
	mi := &file_bizscript_proto_msgTypes[59]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetReportReasonsResp.ProtoReflect.Descriptor instead.
func (*GetReportReasonsResp) Descriptor() ([]byte, []int) {
	return file_bizscript_proto_rawDescGZIP(), []int{59}
}

func (x *GetReportReasonsResp) GetCode() int32 {
	if x != nil {
		return x.Code
	}
	return 0
}

func (x *GetReportReasonsResp) GetMsg() string {
	if x != nil {
		return x.Msg
	}
	return ""
}

func (x *GetReportReasonsResp) GetData() *svcscript.GetReportReasonsRespData {
	if x != nil {
		return x.Data
	}
	return nil
}

// 举报内容请求
type ReportReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	ReportType svcscript.ReportType `protobuf:"varint,1,opt,name=report_type,json=reportType,proto3,enum=vc.svcscript.ReportType" json:"report_type,omitempty"` // 举报类型: 1=剧本举报, 2=配音举报, 3=评论举报
	TargetId   int64                `protobuf:"varint,2,opt,name=target_id,json=targetId,proto3" json:"target_id,omitempty"`                                    // 目标ID，根据report_type对应剧本ID、配音ID或评论ID
	ReasonId   int32                `protobuf:"varint,3,opt,name=reason_id,json=reasonId,proto3" json:"reason_id,omitempty"`                                    // 原因ID，对应举报原因列表中的ID，自定义原因时可为0
	Reason     string               `protobuf:"bytes,4,opt,name=reason,proto3" json:"reason,omitempty"`                                                         // 原因内容，自定义原因时填写
	Content    string               `protobuf:"bytes,5,opt,name=content,proto3" json:"content,omitempty"`                                                       // 举报内容摘要（标题/评论内容/配音内容）
}

func (x *ReportReq) Reset() {
	*x = ReportReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_bizscript_proto_msgTypes[60]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ReportReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ReportReq) ProtoMessage() {}

func (x *ReportReq) ProtoReflect() protoreflect.Message {
	mi := &file_bizscript_proto_msgTypes[60]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ReportReq.ProtoReflect.Descriptor instead.
func (*ReportReq) Descriptor() ([]byte, []int) {
	return file_bizscript_proto_rawDescGZIP(), []int{60}
}

func (x *ReportReq) GetReportType() svcscript.ReportType {
	if x != nil {
		return x.ReportType
	}
	return svcscript.ReportType(0)
}

func (x *ReportReq) GetTargetId() int64 {
	if x != nil {
		return x.TargetId
	}
	return 0
}

func (x *ReportReq) GetReasonId() int32 {
	if x != nil {
		return x.ReasonId
	}
	return 0
}

func (x *ReportReq) GetReason() string {
	if x != nil {
		return x.Reason
	}
	return ""
}

func (x *ReportReq) GetContent() string {
	if x != nil {
		return x.Content
	}
	return ""
}

// 举报内容响应
type ReportResp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Code int32  `protobuf:"varint,1,opt,name=code,proto3" json:"code,omitempty"` // 错误码
	Msg  string `protobuf:"bytes,2,opt,name=msg,proto3" json:"msg,omitempty"`    // 错误信息
}

func (x *ReportResp) Reset() {
	*x = ReportResp{}
	if protoimpl.UnsafeEnabled {
		mi := &file_bizscript_proto_msgTypes[61]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ReportResp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ReportResp) ProtoMessage() {}

func (x *ReportResp) ProtoReflect() protoreflect.Message {
	mi := &file_bizscript_proto_msgTypes[61]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ReportResp.ProtoReflect.Descriptor instead.
func (*ReportResp) Descriptor() ([]byte, []int) {
	return file_bizscript_proto_rawDescGZIP(), []int{61}
}

func (x *ReportResp) GetCode() int32 {
	if x != nil {
		return x.Code
	}
	return 0
}

func (x *ReportResp) GetMsg() string {
	if x != nil {
		return x.Msg
	}
	return ""
}

// 获取IP列表请求
type GetIPListReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Page     int32 `protobuf:"varint,1,opt,name=page,proto3" json:"page,omitempty"`                         // 页码
	PageSize int32 `protobuf:"varint,2,opt,name=page_size,json=pageSize,proto3" json:"page_size,omitempty"` // 每页数量
}

func (x *GetIPListReq) Reset() {
	*x = GetIPListReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_bizscript_proto_msgTypes[62]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetIPListReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetIPListReq) ProtoMessage() {}

func (x *GetIPListReq) ProtoReflect() protoreflect.Message {
	mi := &file_bizscript_proto_msgTypes[62]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetIPListReq.ProtoReflect.Descriptor instead.
func (*GetIPListReq) Descriptor() ([]byte, []int) {
	return file_bizscript_proto_rawDescGZIP(), []int{62}
}

func (x *GetIPListReq) GetPage() int32 {
	if x != nil {
		return x.Page
	}
	return 0
}

func (x *GetIPListReq) GetPageSize() int32 {
	if x != nil {
		return x.PageSize
	}
	return 0
}

// 获取IP列表响应
type GetIPListResp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Code int32                        `protobuf:"varint,1,opt,name=code,proto3" json:"code,omitempty"` // 错误码
	Msg  string                       `protobuf:"bytes,2,opt,name=msg,proto3" json:"msg,omitempty"`    // 错误信息
	Data *svcscript.GetIPListRespData `protobuf:"bytes,3,opt,name=data,proto3" json:"data,omitempty"`  // 响应数据
}

func (x *GetIPListResp) Reset() {
	*x = GetIPListResp{}
	if protoimpl.UnsafeEnabled {
		mi := &file_bizscript_proto_msgTypes[63]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetIPListResp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetIPListResp) ProtoMessage() {}

func (x *GetIPListResp) ProtoReflect() protoreflect.Message {
	mi := &file_bizscript_proto_msgTypes[63]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetIPListResp.ProtoReflect.Descriptor instead.
func (*GetIPListResp) Descriptor() ([]byte, []int) {
	return file_bizscript_proto_rawDescGZIP(), []int{63}
}

func (x *GetIPListResp) GetCode() int32 {
	if x != nil {
		return x.Code
	}
	return 0
}

func (x *GetIPListResp) GetMsg() string {
	if x != nil {
		return x.Msg
	}
	return ""
}

func (x *GetIPListResp) GetData() *svcscript.GetIPListRespData {
	if x != nil {
		return x.Data
	}
	return nil
}

// 获取用户统计数据请求
type GetUserStatsReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	UserId int64 `protobuf:"varint,1,opt,name=user_id,json=userId,proto3" json:"user_id,omitempty"` // 目标用户id，可选，默认当前登录用户
}

func (x *GetUserStatsReq) Reset() {
	*x = GetUserStatsReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_bizscript_proto_msgTypes[64]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetUserStatsReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetUserStatsReq) ProtoMessage() {}

func (x *GetUserStatsReq) ProtoReflect() protoreflect.Message {
	mi := &file_bizscript_proto_msgTypes[64]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetUserStatsReq.ProtoReflect.Descriptor instead.
func (*GetUserStatsReq) Descriptor() ([]byte, []int) {
	return file_bizscript_proto_rawDescGZIP(), []int{64}
}

func (x *GetUserStatsReq) GetUserId() int64 {
	if x != nil {
		return x.UserId
	}
	return 0
}

// 获取用户统计数据响应
type GetUserStatsResp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Code int32                           `protobuf:"varint,1,opt,name=code,proto3" json:"code,omitempty"` // 错误码
	Msg  string                          `protobuf:"bytes,2,opt,name=msg,proto3" json:"msg,omitempty"`    // 错误信息
	Data *svcscript.GetUserStatsRespData `protobuf:"bytes,3,opt,name=data,proto3" json:"data,omitempty"`  // 响应数据
}

func (x *GetUserStatsResp) Reset() {
	*x = GetUserStatsResp{}
	if protoimpl.UnsafeEnabled {
		mi := &file_bizscript_proto_msgTypes[65]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetUserStatsResp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetUserStatsResp) ProtoMessage() {}

func (x *GetUserStatsResp) ProtoReflect() protoreflect.Message {
	mi := &file_bizscript_proto_msgTypes[65]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetUserStatsResp.ProtoReflect.Descriptor instead.
func (*GetUserStatsResp) Descriptor() ([]byte, []int) {
	return file_bizscript_proto_rawDescGZIP(), []int{65}
}

func (x *GetUserStatsResp) GetCode() int32 {
	if x != nil {
		return x.Code
	}
	return 0
}

func (x *GetUserStatsResp) GetMsg() string {
	if x != nil {
		return x.Msg
	}
	return ""
}

func (x *GetUserStatsResp) GetData() *svcscript.GetUserStatsRespData {
	if x != nil {
		return x.Data
	}
	return nil
}

// 获取用户剧本列表请求
type GetUserScriptListsReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	ListType svcscript.GetUserScriptListsReq_ListType `protobuf:"varint,1,opt,name=list_type,json=listType,proto3,enum=vc.svcscript.GetUserScriptListsReq_ListType" json:"list_type,omitempty"` // 列表类型
	UserId   int64                                    `protobuf:"varint,2,opt,name=user_id,json=userId,proto3" json:"user_id,omitempty"`                                                        // 用户ID, 可选，默认当前用户
	Page     int32                                    `protobuf:"varint,3,opt,name=page,proto3" json:"page,omitempty"`                                                                          // 页码
	PageSize int32                                    `protobuf:"varint,4,opt,name=page_size,json=pageSize,proto3" json:"page_size,omitempty"`                                                  // 每页数量
}

func (x *GetUserScriptListsReq) Reset() {
	*x = GetUserScriptListsReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_bizscript_proto_msgTypes[66]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetUserScriptListsReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetUserScriptListsReq) ProtoMessage() {}

func (x *GetUserScriptListsReq) ProtoReflect() protoreflect.Message {
	mi := &file_bizscript_proto_msgTypes[66]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetUserScriptListsReq.ProtoReflect.Descriptor instead.
func (*GetUserScriptListsReq) Descriptor() ([]byte, []int) {
	return file_bizscript_proto_rawDescGZIP(), []int{66}
}

func (x *GetUserScriptListsReq) GetListType() svcscript.GetUserScriptListsReq_ListType {
	if x != nil {
		return x.ListType
	}
	return svcscript.GetUserScriptListsReq_ListType(0)
}

func (x *GetUserScriptListsReq) GetUserId() int64 {
	if x != nil {
		return x.UserId
	}
	return 0
}

func (x *GetUserScriptListsReq) GetPage() int32 {
	if x != nil {
		return x.Page
	}
	return 0
}

func (x *GetUserScriptListsReq) GetPageSize() int32 {
	if x != nil {
		return x.PageSize
	}
	return 0
}

// 获取用户剧本列表响应
type GetUserScriptListsResp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Code int32                                 `protobuf:"varint,1,opt,name=code,proto3" json:"code,omitempty"` // 错误码
	Msg  string                                `protobuf:"bytes,2,opt,name=msg,proto3" json:"msg,omitempty"`    // 错误信息
	Data *svcscript.GetUserScriptListsRespData `protobuf:"bytes,3,opt,name=data,proto3" json:"data,omitempty"`  // 响应数据
}

func (x *GetUserScriptListsResp) Reset() {
	*x = GetUserScriptListsResp{}
	if protoimpl.UnsafeEnabled {
		mi := &file_bizscript_proto_msgTypes[67]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetUserScriptListsResp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetUserScriptListsResp) ProtoMessage() {}

func (x *GetUserScriptListsResp) ProtoReflect() protoreflect.Message {
	mi := &file_bizscript_proto_msgTypes[67]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetUserScriptListsResp.ProtoReflect.Descriptor instead.
func (*GetUserScriptListsResp) Descriptor() ([]byte, []int) {
	return file_bizscript_proto_rawDescGZIP(), []int{67}
}

func (x *GetUserScriptListsResp) GetCode() int32 {
	if x != nil {
		return x.Code
	}
	return 0
}

func (x *GetUserScriptListsResp) GetMsg() string {
	if x != nil {
		return x.Msg
	}
	return ""
}

func (x *GetUserScriptListsResp) GetData() *svcscript.GetUserScriptListsRespData {
	if x != nil {
		return x.Data
	}
	return nil
}

// 获取封面列表请求
type GetCoverListReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Page     int32               `protobuf:"varint,1,opt,name=page,proto3" json:"page,omitempty"`                             // 页码
	PageSize int32               `protobuf:"varint,2,opt,name=page_size,json=pageSize,proto3" json:"page_size,omitempty"`     // 每页数量
	Type     svcscript.CoverType `protobuf:"varint,3,opt,name=type,proto3,enum=vc.svcscript.CoverType" json:"type,omitempty"` // 封面类型
}

func (x *GetCoverListReq) Reset() {
	*x = GetCoverListReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_bizscript_proto_msgTypes[68]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetCoverListReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetCoverListReq) ProtoMessage() {}

func (x *GetCoverListReq) ProtoReflect() protoreflect.Message {
	mi := &file_bizscript_proto_msgTypes[68]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetCoverListReq.ProtoReflect.Descriptor instead.
func (*GetCoverListReq) Descriptor() ([]byte, []int) {
	return file_bizscript_proto_rawDescGZIP(), []int{68}
}

func (x *GetCoverListReq) GetPage() int32 {
	if x != nil {
		return x.Page
	}
	return 0
}

func (x *GetCoverListReq) GetPageSize() int32 {
	if x != nil {
		return x.PageSize
	}
	return 0
}

func (x *GetCoverListReq) GetType() svcscript.CoverType {
	if x != nil {
		return x.Type
	}
	return svcscript.CoverType(0)
}

// 获取封面列表响应
type GetCoverListResp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Code int32                           `protobuf:"varint,1,opt,name=code,proto3" json:"code,omitempty"` // 错误码
	Msg  string                          `protobuf:"bytes,2,opt,name=msg,proto3" json:"msg,omitempty"`    // 错误信息
	Data *svcscript.GetCoverListRespData `protobuf:"bytes,3,opt,name=data,proto3" json:"data,omitempty"`  // 响应数据
}

func (x *GetCoverListResp) Reset() {
	*x = GetCoverListResp{}
	if protoimpl.UnsafeEnabled {
		mi := &file_bizscript_proto_msgTypes[69]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetCoverListResp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetCoverListResp) ProtoMessage() {}

func (x *GetCoverListResp) ProtoReflect() protoreflect.Message {
	mi := &file_bizscript_proto_msgTypes[69]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetCoverListResp.ProtoReflect.Descriptor instead.
func (*GetCoverListResp) Descriptor() ([]byte, []int) {
	return file_bizscript_proto_rawDescGZIP(), []int{69}
}

func (x *GetCoverListResp) GetCode() int32 {
	if x != nil {
		return x.Code
	}
	return 0
}

func (x *GetCoverListResp) GetMsg() string {
	if x != nil {
		return x.Msg
	}
	return ""
}

func (x *GetCoverListResp) GetData() *svcscript.GetCoverListRespData {
	if x != nil {
		return x.Data
	}
	return nil
}

// 生成剧本台词请求
type GenerateScriptLinesReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	CharacterIds []int64 `protobuf:"varint,1,rep,packed,name=character_ids,json=characterIds,proto3" json:"character_ids,omitempty"` // 角色id列表
}

func (x *GenerateScriptLinesReq) Reset() {
	*x = GenerateScriptLinesReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_bizscript_proto_msgTypes[70]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GenerateScriptLinesReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GenerateScriptLinesReq) ProtoMessage() {}

func (x *GenerateScriptLinesReq) ProtoReflect() protoreflect.Message {
	mi := &file_bizscript_proto_msgTypes[70]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GenerateScriptLinesReq.ProtoReflect.Descriptor instead.
func (*GenerateScriptLinesReq) Descriptor() ([]byte, []int) {
	return file_bizscript_proto_rawDescGZIP(), []int{70}
}

func (x *GenerateScriptLinesReq) GetCharacterIds() []int64 {
	if x != nil {
		return x.CharacterIds
	}
	return nil
}

// 生成剧本台词响应
type GenerateScriptLinesResp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Code int32                        `protobuf:"varint,1,opt,name=code,proto3" json:"code,omitempty"` // 错误码
	Msg  string                       `protobuf:"bytes,2,opt,name=msg,proto3" json:"msg,omitempty"`    // 错误信息
	Data *GenerateScriptLinesRespData `protobuf:"bytes,3,opt,name=data,proto3" json:"data,omitempty"`  // 响应数据
}

func (x *GenerateScriptLinesResp) Reset() {
	*x = GenerateScriptLinesResp{}
	if protoimpl.UnsafeEnabled {
		mi := &file_bizscript_proto_msgTypes[71]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GenerateScriptLinesResp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GenerateScriptLinesResp) ProtoMessage() {}

func (x *GenerateScriptLinesResp) ProtoReflect() protoreflect.Message {
	mi := &file_bizscript_proto_msgTypes[71]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GenerateScriptLinesResp.ProtoReflect.Descriptor instead.
func (*GenerateScriptLinesResp) Descriptor() ([]byte, []int) {
	return file_bizscript_proto_rawDescGZIP(), []int{71}
}

func (x *GenerateScriptLinesResp) GetCode() int32 {
	if x != nil {
		return x.Code
	}
	return 0
}

func (x *GenerateScriptLinesResp) GetMsg() string {
	if x != nil {
		return x.Msg
	}
	return ""
}

func (x *GenerateScriptLinesResp) GetData() *GenerateScriptLinesRespData {
	if x != nil {
		return x.Data
	}
	return nil
}

// 生成剧本台词响应数据
type GenerateScriptLinesRespData struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Lines      []*svcscript.Line              `protobuf:"bytes,1,rep,name=lines,proto3" json:"lines,omitempty"`                                                                                                    // 生成的台词列表
	Characters map[int64]*svcscript.Character `protobuf:"bytes,2,rep,name=characters,proto3" json:"characters,omitempty" protobuf_key:"varint,1,opt,name=key,proto3" protobuf_val:"bytes,2,opt,name=value,proto3"` // 相关角色列表 map<CharacterID, Character>
}

func (x *GenerateScriptLinesRespData) Reset() {
	*x = GenerateScriptLinesRespData{}
	if protoimpl.UnsafeEnabled {
		mi := &file_bizscript_proto_msgTypes[72]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GenerateScriptLinesRespData) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GenerateScriptLinesRespData) ProtoMessage() {}

func (x *GenerateScriptLinesRespData) ProtoReflect() protoreflect.Message {
	mi := &file_bizscript_proto_msgTypes[72]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GenerateScriptLinesRespData.ProtoReflect.Descriptor instead.
func (*GenerateScriptLinesRespData) Descriptor() ([]byte, []int) {
	return file_bizscript_proto_rawDescGZIP(), []int{72}
}

func (x *GenerateScriptLinesRespData) GetLines() []*svcscript.Line {
	if x != nil {
		return x.Lines
	}
	return nil
}

func (x *GenerateScriptLinesRespData) GetCharacters() map[int64]*svcscript.Character {
	if x != nil {
		return x.Characters
	}
	return nil
}

// 删除配音请求
type DeleteDubbingReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	DubbingId int64 `protobuf:"varint,1,opt,name=dubbing_id,json=dubbingId,proto3" json:"dubbing_id,omitempty"` // 配音ID
}

func (x *DeleteDubbingReq) Reset() {
	*x = DeleteDubbingReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_bizscript_proto_msgTypes[73]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *DeleteDubbingReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DeleteDubbingReq) ProtoMessage() {}

func (x *DeleteDubbingReq) ProtoReflect() protoreflect.Message {
	mi := &file_bizscript_proto_msgTypes[73]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DeleteDubbingReq.ProtoReflect.Descriptor instead.
func (*DeleteDubbingReq) Descriptor() ([]byte, []int) {
	return file_bizscript_proto_rawDescGZIP(), []int{73}
}

func (x *DeleteDubbingReq) GetDubbingId() int64 {
	if x != nil {
		return x.DubbingId
	}
	return 0
}

// 删除配音响应
type DeleteDubbingResp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Code int32  `protobuf:"varint,1,opt,name=code,proto3" json:"code,omitempty"` // 错误码
	Msg  string `protobuf:"bytes,2,opt,name=msg,proto3" json:"msg,omitempty"`    // 错误信息
}

func (x *DeleteDubbingResp) Reset() {
	*x = DeleteDubbingResp{}
	if protoimpl.UnsafeEnabled {
		mi := &file_bizscript_proto_msgTypes[74]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *DeleteDubbingResp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DeleteDubbingResp) ProtoMessage() {}

func (x *DeleteDubbingResp) ProtoReflect() protoreflect.Message {
	mi := &file_bizscript_proto_msgTypes[74]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DeleteDubbingResp.ProtoReflect.Descriptor instead.
func (*DeleteDubbingResp) Descriptor() ([]byte, []int) {
	return file_bizscript_proto_rawDescGZIP(), []int{74}
}

func (x *DeleteDubbingResp) GetCode() int32 {
	if x != nil {
		return x.Code
	}
	return 0
}

func (x *DeleteDubbingResp) GetMsg() string {
	if x != nil {
		return x.Msg
	}
	return ""
}

// 设置配音置顶状态请求
type SetDubbingTopReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	DubbingId int64 `protobuf:"varint,1,opt,name=dubbing_id,json=dubbingId,proto3" json:"dubbing_id,omitempty"` // 配音ID
	IsTop     bool  `protobuf:"varint,2,opt,name=is_top,json=isTop,proto3" json:"is_top,omitempty"`             // 是否置顶
}

func (x *SetDubbingTopReq) Reset() {
	*x = SetDubbingTopReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_bizscript_proto_msgTypes[75]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *SetDubbingTopReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SetDubbingTopReq) ProtoMessage() {}

func (x *SetDubbingTopReq) ProtoReflect() protoreflect.Message {
	mi := &file_bizscript_proto_msgTypes[75]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SetDubbingTopReq.ProtoReflect.Descriptor instead.
func (*SetDubbingTopReq) Descriptor() ([]byte, []int) {
	return file_bizscript_proto_rawDescGZIP(), []int{75}
}

func (x *SetDubbingTopReq) GetDubbingId() int64 {
	if x != nil {
		return x.DubbingId
	}
	return 0
}

func (x *SetDubbingTopReq) GetIsTop() bool {
	if x != nil {
		return x.IsTop
	}
	return false
}

// 设置配音置顶状态响应
type SetDubbingTopResp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Code int32  `protobuf:"varint,1,opt,name=code,proto3" json:"code,omitempty"` // 错误码
	Msg  string `protobuf:"bytes,2,opt,name=msg,proto3" json:"msg,omitempty"`    // 错误信息
}

func (x *SetDubbingTopResp) Reset() {
	*x = SetDubbingTopResp{}
	if protoimpl.UnsafeEnabled {
		mi := &file_bizscript_proto_msgTypes[76]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *SetDubbingTopResp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SetDubbingTopResp) ProtoMessage() {}

func (x *SetDubbingTopResp) ProtoReflect() protoreflect.Message {
	mi := &file_bizscript_proto_msgTypes[76]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SetDubbingTopResp.ProtoReflect.Descriptor instead.
func (*SetDubbingTopResp) Descriptor() ([]byte, []int) {
	return file_bizscript_proto_rawDescGZIP(), []int{76}
}

func (x *SetDubbingTopResp) GetCode() int32 {
	if x != nil {
		return x.Code
	}
	return 0
}

func (x *SetDubbingTopResp) GetMsg() string {
	if x != nil {
		return x.Msg
	}
	return ""
}

var File_bizscript_proto protoreflect.FileDescriptor

var file_bizscript_proto_rawDesc = []byte{
	0x0a, 0x0f, 0x62, 0x69, 0x7a, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x2e, 0x70, 0x72, 0x6f, 0x74,
	0x6f, 0x12, 0x0c, 0x76, 0x63, 0x2e, 0x62, 0x69, 0x7a, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x1a,
	0x1c, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2f, 0x61, 0x70, 0x69, 0x2f, 0x61, 0x6e, 0x6e, 0x6f,
	0x74, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x2b, 0x70,
	0x72, 0x6f, 0x74, 0x6f, 0x63, 0x2d, 0x67, 0x65, 0x6e, 0x2d, 0x76, 0x61, 0x6c, 0x69, 0x64, 0x61,
	0x74, 0x65, 0x2f, 0x76, 0x61, 0x6c, 0x69, 0x64, 0x61, 0x74, 0x65, 0x2f, 0x76, 0x61, 0x6c, 0x69,
	0x64, 0x61, 0x74, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x1f, 0x67, 0x6f, 0x6f, 0x67,
	0x6c, 0x65, 0x2f, 0x61, 0x70, 0x69, 0x2f, 0x66, 0x69, 0x65, 0x6c, 0x64, 0x5f, 0x62, 0x65, 0x68,
	0x61, 0x76, 0x69, 0x6f, 0x72, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x19, 0x73, 0x76, 0x63,
	0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x2f, 0x73, 0x76, 0x63, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74,
	0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x22, 0x5b, 0x0a, 0x13, 0x47, 0x65, 0x74, 0x43, 0x68, 0x61,
	0x72, 0x61, 0x63, 0x74, 0x65, 0x72, 0x4c, 0x69, 0x73, 0x74, 0x52, 0x65, 0x71, 0x12, 0x12, 0x0a,
	0x04, 0x70, 0x61, 0x67, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x05, 0x52, 0x04, 0x70, 0x61, 0x67,
	0x65, 0x12, 0x1b, 0x0a, 0x09, 0x70, 0x61, 0x67, 0x65, 0x5f, 0x73, 0x69, 0x7a, 0x65, 0x18, 0x02,
	0x20, 0x01, 0x28, 0x05, 0x52, 0x08, 0x70, 0x61, 0x67, 0x65, 0x53, 0x69, 0x7a, 0x65, 0x12, 0x13,
	0x0a, 0x05, 0x69, 0x70, 0x5f, 0x69, 0x64, 0x18, 0x03, 0x20, 0x01, 0x28, 0x03, 0x52, 0x04, 0x69,
	0x70, 0x49, 0x64, 0x22, 0x78, 0x0a, 0x14, 0x47, 0x65, 0x74, 0x43, 0x68, 0x61, 0x72, 0x61, 0x63,
	0x74, 0x65, 0x72, 0x4c, 0x69, 0x73, 0x74, 0x52, 0x65, 0x73, 0x70, 0x12, 0x12, 0x0a, 0x04, 0x63,
	0x6f, 0x64, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x05, 0x52, 0x04, 0x63, 0x6f, 0x64, 0x65, 0x12,
	0x10, 0x0a, 0x03, 0x6d, 0x73, 0x67, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x03, 0x6d, 0x73,
	0x67, 0x12, 0x3a, 0x0a, 0x04, 0x64, 0x61, 0x74, 0x61, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0b, 0x32,
	0x26, 0x2e, 0x76, 0x63, 0x2e, 0x73, 0x76, 0x63, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x2e, 0x47,
	0x65, 0x74, 0x43, 0x68, 0x61, 0x72, 0x61, 0x63, 0x74, 0x65, 0x72, 0x4c, 0x69, 0x73, 0x74, 0x52,
	0x65, 0x73, 0x70, 0x44, 0x61, 0x74, 0x61, 0x52, 0x04, 0x64, 0x61, 0x74, 0x61, 0x22, 0x6c, 0x0a,
	0x13, 0x53, 0x65, 0x61, 0x72, 0x63, 0x68, 0x43, 0x68, 0x61, 0x72, 0x61, 0x63, 0x74, 0x65, 0x72,
	0x73, 0x52, 0x65, 0x71, 0x12, 0x24, 0x0a, 0x07, 0x6b, 0x65, 0x79, 0x77, 0x6f, 0x72, 0x64, 0x18,
	0x01, 0x20, 0x01, 0x28, 0x09, 0x42, 0x0a, 0xe0, 0x41, 0x02, 0xfa, 0x42, 0x04, 0x72, 0x02, 0x10,
	0x01, 0x52, 0x07, 0x6b, 0x65, 0x79, 0x77, 0x6f, 0x72, 0x64, 0x12, 0x12, 0x0a, 0x04, 0x70, 0x61,
	0x67, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x05, 0x52, 0x04, 0x70, 0x61, 0x67, 0x65, 0x12, 0x1b,
	0x0a, 0x09, 0x70, 0x61, 0x67, 0x65, 0x5f, 0x73, 0x69, 0x7a, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28,
	0x05, 0x52, 0x08, 0x70, 0x61, 0x67, 0x65, 0x53, 0x69, 0x7a, 0x65, 0x22, 0x78, 0x0a, 0x14, 0x53,
	0x65, 0x61, 0x72, 0x63, 0x68, 0x43, 0x68, 0x61, 0x72, 0x61, 0x63, 0x74, 0x65, 0x72, 0x73, 0x52,
	0x65, 0x73, 0x70, 0x12, 0x12, 0x0a, 0x04, 0x63, 0x6f, 0x64, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28,
	0x05, 0x52, 0x04, 0x63, 0x6f, 0x64, 0x65, 0x12, 0x10, 0x0a, 0x03, 0x6d, 0x73, 0x67, 0x18, 0x02,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x03, 0x6d, 0x73, 0x67, 0x12, 0x3a, 0x0a, 0x04, 0x64, 0x61, 0x74,
	0x61, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x26, 0x2e, 0x76, 0x63, 0x2e, 0x73, 0x76, 0x63,
	0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x2e, 0x47, 0x65, 0x74, 0x43, 0x68, 0x61, 0x72, 0x61, 0x63,
	0x74, 0x65, 0x72, 0x4c, 0x69, 0x73, 0x74, 0x52, 0x65, 0x73, 0x70, 0x44, 0x61, 0x74, 0x61, 0x52,
	0x04, 0x64, 0x61, 0x74, 0x61, 0x22, 0x7e, 0x0a, 0x13, 0x53, 0x65, 0x74, 0x55, 0x73, 0x65, 0x72,
	0x43, 0x68, 0x61, 0x72, 0x61, 0x63, 0x74, 0x65, 0x72, 0x52, 0x65, 0x71, 0x12, 0x2d, 0x0a, 0x0c,
	0x63, 0x68, 0x61, 0x72, 0x61, 0x63, 0x74, 0x65, 0x72, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01,
	0x28, 0x03, 0x42, 0x0a, 0xe0, 0x41, 0x02, 0xfa, 0x42, 0x04, 0x22, 0x02, 0x20, 0x00, 0x52, 0x0b,
	0x63, 0x68, 0x61, 0x72, 0x61, 0x63, 0x74, 0x65, 0x72, 0x49, 0x64, 0x12, 0x38, 0x0a, 0x12, 0x63,
	0x68, 0x61, 0x72, 0x61, 0x63, 0x74, 0x65, 0x72, 0x5f, 0x61, 0x73, 0x73, 0x65, 0x74, 0x5f, 0x69,
	0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x03, 0x42, 0x0a, 0xe0, 0x41, 0x02, 0xfa, 0x42, 0x04, 0x22,
	0x02, 0x20, 0x00, 0x52, 0x10, 0x63, 0x68, 0x61, 0x72, 0x61, 0x63, 0x74, 0x65, 0x72, 0x41, 0x73,
	0x73, 0x65, 0x74, 0x49, 0x64, 0x22, 0x3c, 0x0a, 0x14, 0x53, 0x65, 0x74, 0x55, 0x73, 0x65, 0x72,
	0x43, 0x68, 0x61, 0x72, 0x61, 0x63, 0x74, 0x65, 0x72, 0x52, 0x65, 0x73, 0x70, 0x12, 0x12, 0x0a,
	0x04, 0x63, 0x6f, 0x64, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x05, 0x52, 0x04, 0x63, 0x6f, 0x64,
	0x65, 0x12, 0x10, 0x0a, 0x03, 0x6d, 0x73, 0x67, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x03,
	0x6d, 0x73, 0x67, 0x22, 0x16, 0x0a, 0x14, 0x47, 0x65, 0x74, 0x55, 0x73, 0x65, 0x72, 0x43, 0x68,
	0x61, 0x72, 0x61, 0x63, 0x74, 0x65, 0x72, 0x73, 0x52, 0x65, 0x71, 0x22, 0x7a, 0x0a, 0x15, 0x47,
	0x65, 0x74, 0x55, 0x73, 0x65, 0x72, 0x43, 0x68, 0x61, 0x72, 0x61, 0x63, 0x74, 0x65, 0x72, 0x73,
	0x52, 0x65, 0x73, 0x70, 0x12, 0x12, 0x0a, 0x04, 0x63, 0x6f, 0x64, 0x65, 0x18, 0x01, 0x20, 0x01,
	0x28, 0x05, 0x52, 0x04, 0x63, 0x6f, 0x64, 0x65, 0x12, 0x10, 0x0a, 0x03, 0x6d, 0x73, 0x67, 0x18,
	0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x03, 0x6d, 0x73, 0x67, 0x12, 0x3b, 0x0a, 0x04, 0x64, 0x61,
	0x74, 0x61, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x27, 0x2e, 0x76, 0x63, 0x2e, 0x73, 0x76,
	0x63, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x2e, 0x47, 0x65, 0x74, 0x55, 0x73, 0x65, 0x72, 0x43,
	0x68, 0x61, 0x72, 0x61, 0x63, 0x74, 0x65, 0x72, 0x73, 0x52, 0x65, 0x73, 0x70, 0x44, 0x61, 0x74,
	0x61, 0x52, 0x04, 0x64, 0x61, 0x74, 0x61, 0x22, 0x58, 0x0a, 0x22, 0x42, 0x61, 0x74, 0x63, 0x68,
	0x43, 0x68, 0x65, 0x63, 0x6b, 0x43, 0x68, 0x61, 0x72, 0x61, 0x63, 0x74, 0x65, 0x72, 0x41, 0x76,
	0x61, 0x69, 0x6c, 0x61, 0x62, 0x69, 0x6c, 0x69, 0x74, 0x79, 0x52, 0x65, 0x71, 0x12, 0x32, 0x0a,
	0x0d, 0x63, 0x68, 0x61, 0x72, 0x61, 0x63, 0x74, 0x65, 0x72, 0x5f, 0x69, 0x64, 0x73, 0x18, 0x01,
	0x20, 0x03, 0x28, 0x03, 0x42, 0x0d, 0xe0, 0x41, 0x02, 0xfa, 0x42, 0x07, 0x92, 0x01, 0x04, 0x08,
	0x01, 0x10, 0x64, 0x52, 0x0c, 0x63, 0x68, 0x61, 0x72, 0x61, 0x63, 0x74, 0x65, 0x72, 0x49, 0x64,
	0x73, 0x22, 0x96, 0x01, 0x0a, 0x23, 0x42, 0x61, 0x74, 0x63, 0x68, 0x43, 0x68, 0x65, 0x63, 0x6b,
	0x43, 0x68, 0x61, 0x72, 0x61, 0x63, 0x74, 0x65, 0x72, 0x41, 0x76, 0x61, 0x69, 0x6c, 0x61, 0x62,
	0x69, 0x6c, 0x69, 0x74, 0x79, 0x52, 0x65, 0x73, 0x70, 0x12, 0x12, 0x0a, 0x04, 0x63, 0x6f, 0x64,
	0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x05, 0x52, 0x04, 0x63, 0x6f, 0x64, 0x65, 0x12, 0x10, 0x0a,
	0x03, 0x6d, 0x73, 0x67, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x03, 0x6d, 0x73, 0x67, 0x12,
	0x49, 0x0a, 0x04, 0x64, 0x61, 0x74, 0x61, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x35, 0x2e,
	0x76, 0x63, 0x2e, 0x73, 0x76, 0x63, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x2e, 0x42, 0x61, 0x74,
	0x63, 0x68, 0x43, 0x68, 0x65, 0x63, 0x6b, 0x43, 0x68, 0x61, 0x72, 0x61, 0x63, 0x74, 0x65, 0x72,
	0x41, 0x76, 0x61, 0x69, 0x6c, 0x61, 0x62, 0x69, 0x6c, 0x69, 0x74, 0x79, 0x52, 0x65, 0x73, 0x70,
	0x44, 0x61, 0x74, 0x61, 0x52, 0x04, 0x64, 0x61, 0x74, 0x61, 0x22, 0x7d, 0x0a, 0x0f, 0x47, 0x65,
	0x74, 0x54, 0x6f, 0x70, 0x69, 0x63, 0x4c, 0x69, 0x73, 0x74, 0x52, 0x65, 0x71, 0x12, 0x12, 0x0a,
	0x04, 0x70, 0x61, 0x67, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x05, 0x52, 0x04, 0x70, 0x61, 0x67,
	0x65, 0x12, 0x1b, 0x0a, 0x09, 0x70, 0x61, 0x67, 0x65, 0x5f, 0x73, 0x69, 0x7a, 0x65, 0x18, 0x02,
	0x20, 0x01, 0x28, 0x05, 0x52, 0x08, 0x70, 0x61, 0x67, 0x65, 0x53, 0x69, 0x7a, 0x65, 0x12, 0x39,
	0x0a, 0x05, 0x73, 0x63, 0x65, 0x6e, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x19, 0x2e,
	0x76, 0x63, 0x2e, 0x73, 0x76, 0x63, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x2e, 0x54, 0x4f, 0x50,
	0x49, 0x43, 0x5f, 0x53, 0x43, 0x45, 0x4e, 0x45, 0x42, 0x08, 0xfa, 0x42, 0x05, 0x82, 0x01, 0x02,
	0x10, 0x01, 0x52, 0x05, 0x73, 0x63, 0x65, 0x6e, 0x65, 0x22, 0x70, 0x0a, 0x10, 0x47, 0x65, 0x74,
	0x54, 0x6f, 0x70, 0x69, 0x63, 0x4c, 0x69, 0x73, 0x74, 0x52, 0x65, 0x73, 0x70, 0x12, 0x12, 0x0a,
	0x04, 0x63, 0x6f, 0x64, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x05, 0x52, 0x04, 0x63, 0x6f, 0x64,
	0x65, 0x12, 0x10, 0x0a, 0x03, 0x6d, 0x73, 0x67, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x03,
	0x6d, 0x73, 0x67, 0x12, 0x36, 0x0a, 0x04, 0x64, 0x61, 0x74, 0x61, 0x18, 0x03, 0x20, 0x01, 0x28,
	0x0b, 0x32, 0x22, 0x2e, 0x76, 0x63, 0x2e, 0x73, 0x76, 0x63, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74,
	0x2e, 0x47, 0x65, 0x74, 0x54, 0x6f, 0x70, 0x69, 0x63, 0x4c, 0x69, 0x73, 0x74, 0x52, 0x65, 0x73,
	0x70, 0x44, 0x61, 0x74, 0x61, 0x52, 0x04, 0x64, 0x61, 0x74, 0x61, 0x22, 0x83, 0x03, 0x0a, 0x0f,
	0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x53, 0x63, 0x72, 0x69, 0x70, 0x74, 0x52, 0x65, 0x71, 0x12,
	0x20, 0x0a, 0x05, 0x74, 0x69, 0x74, 0x6c, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x42, 0x0a,
	0xe0, 0x41, 0x02, 0xfa, 0x42, 0x04, 0x72, 0x02, 0x10, 0x01, 0x52, 0x05, 0x74, 0x69, 0x74, 0x6c,
	0x65, 0x12, 0x20, 0x0a, 0x05, 0x63, 0x6f, 0x76, 0x65, 0x72, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09,
	0x42, 0x0a, 0xe0, 0x41, 0x02, 0xfa, 0x42, 0x04, 0x72, 0x02, 0x10, 0x01, 0x52, 0x05, 0x63, 0x6f,
	0x76, 0x65, 0x72, 0x12, 0x61, 0x0a, 0x13, 0x63, 0x68, 0x61, 0x72, 0x61, 0x63, 0x74, 0x65, 0x72,
	0x5f, 0x61, 0x73, 0x73, 0x65, 0x74, 0x5f, 0x69, 0x64, 0x73, 0x18, 0x03, 0x20, 0x03, 0x28, 0x0b,
	0x32, 0x22, 0x2e, 0x76, 0x63, 0x2e, 0x73, 0x76, 0x63, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x2e,
	0x53, 0x63, 0x72, 0x69, 0x70, 0x74, 0x43, 0x68, 0x61, 0x72, 0x61, 0x63, 0x74, 0x65, 0x72, 0x41,
	0x73, 0x73, 0x65, 0x74, 0x42, 0x0d, 0xe0, 0x41, 0x02, 0xfa, 0x42, 0x07, 0x92, 0x01, 0x04, 0x08,
	0x01, 0x10, 0x06, 0x52, 0x11, 0x63, 0x68, 0x61, 0x72, 0x61, 0x63, 0x74, 0x65, 0x72, 0x41, 0x73,
	0x73, 0x65, 0x74, 0x49, 0x64, 0x73, 0x12, 0x2c, 0x0a, 0x0b, 0x74, 0x6f, 0x70, 0x69, 0x63, 0x5f,
	0x6e, 0x61, 0x6d, 0x65, 0x73, 0x18, 0x04, 0x20, 0x03, 0x28, 0x09, 0x42, 0x0b, 0xe0, 0x41, 0x02,
	0xfa, 0x42, 0x05, 0x92, 0x01, 0x02, 0x08, 0x01, 0x52, 0x0a, 0x74, 0x6f, 0x70, 0x69, 0x63, 0x4e,
	0x61, 0x6d, 0x65, 0x73, 0x12, 0x3e, 0x0a, 0x05, 0x6c, 0x69, 0x6e, 0x65, 0x73, 0x18, 0x05, 0x20,
	0x03, 0x28, 0x0b, 0x32, 0x1b, 0x2e, 0x76, 0x63, 0x2e, 0x73, 0x76, 0x63, 0x73, 0x63, 0x72, 0x69,
	0x70, 0x74, 0x2e, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x4c, 0x69, 0x6e, 0x65, 0x52, 0x65, 0x71,
	0x42, 0x0b, 0xe0, 0x41, 0x02, 0xfa, 0x42, 0x05, 0x92, 0x01, 0x02, 0x08, 0x01, 0x52, 0x05, 0x6c,
	0x69, 0x6e, 0x65, 0x73, 0x12, 0x17, 0x0a, 0x07, 0x62, 0x67, 0x6d, 0x5f, 0x75, 0x72, 0x6c, 0x18,
	0x06, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x62, 0x67, 0x6d, 0x55, 0x72, 0x6c, 0x12, 0x21, 0x0a,
	0x0c, 0x62, 0x67, 0x6d, 0x5f, 0x64, 0x75, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x18, 0x07, 0x20,
	0x01, 0x28, 0x05, 0x52, 0x0b, 0x62, 0x67, 0x6d, 0x44, 0x75, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e,
	0x12, 0x1f, 0x0a, 0x0b, 0x74, 0x68, 0x65, 0x6d, 0x65, 0x5f, 0x63, 0x6f, 0x6c, 0x6f, 0x72, 0x18,
	0x08, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0a, 0x74, 0x68, 0x65, 0x6d, 0x65, 0x43, 0x6f, 0x6c, 0x6f,
	0x72, 0x22, 0x70, 0x0a, 0x10, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x53, 0x63, 0x72, 0x69, 0x70,
	0x74, 0x52, 0x65, 0x73, 0x70, 0x12, 0x12, 0x0a, 0x04, 0x63, 0x6f, 0x64, 0x65, 0x18, 0x01, 0x20,
	0x01, 0x28, 0x05, 0x52, 0x04, 0x63, 0x6f, 0x64, 0x65, 0x12, 0x10, 0x0a, 0x03, 0x6d, 0x73, 0x67,
	0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x03, 0x6d, 0x73, 0x67, 0x12, 0x36, 0x0a, 0x04, 0x64,
	0x61, 0x74, 0x61, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x22, 0x2e, 0x76, 0x63, 0x2e, 0x73,
	0x76, 0x63, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x2e, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x53,
	0x63, 0x72, 0x69, 0x70, 0x74, 0x52, 0x65, 0x73, 0x70, 0x44, 0x61, 0x74, 0x61, 0x52, 0x04, 0x64,
	0x61, 0x74, 0x61, 0x22, 0x3a, 0x0a, 0x0f, 0x44, 0x65, 0x6c, 0x65, 0x74, 0x65, 0x53, 0x63, 0x72,
	0x69, 0x70, 0x74, 0x52, 0x65, 0x71, 0x12, 0x27, 0x0a, 0x09, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74,
	0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x42, 0x0a, 0xe0, 0x41, 0x02, 0xfa, 0x42,
	0x04, 0x22, 0x02, 0x20, 0x00, 0x52, 0x08, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x49, 0x64, 0x22,
	0x38, 0x0a, 0x10, 0x44, 0x65, 0x6c, 0x65, 0x74, 0x65, 0x53, 0x63, 0x72, 0x69, 0x70, 0x74, 0x52,
	0x65, 0x73, 0x70, 0x12, 0x12, 0x0a, 0x04, 0x63, 0x6f, 0x64, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28,
	0x05, 0x52, 0x04, 0x63, 0x6f, 0x64, 0x65, 0x12, 0x10, 0x0a, 0x03, 0x6d, 0x73, 0x67, 0x18, 0x02,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x03, 0x6d, 0x73, 0x67, 0x22, 0x85, 0x01, 0x0a, 0x10, 0x47, 0x65,
	0x74, 0x53, 0x63, 0x72, 0x69, 0x70, 0x74, 0x4c, 0x69, 0x73, 0x74, 0x52, 0x65, 0x71, 0x12, 0x19,
	0x0a, 0x08, 0x74, 0x6f, 0x70, 0x69, 0x63, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03,
	0x52, 0x07, 0x74, 0x6f, 0x70, 0x69, 0x63, 0x49, 0x64, 0x12, 0x12, 0x0a, 0x04, 0x70, 0x61, 0x67,
	0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x05, 0x52, 0x04, 0x70, 0x61, 0x67, 0x65, 0x12, 0x1b, 0x0a,
	0x09, 0x70, 0x61, 0x67, 0x65, 0x5f, 0x73, 0x69, 0x7a, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x05,
	0x52, 0x08, 0x70, 0x61, 0x67, 0x65, 0x53, 0x69, 0x7a, 0x65, 0x12, 0x25, 0x0a, 0x0e, 0x69, 0x73,
	0x5f, 0x61, 0x67, 0x67, 0x72, 0x65, 0x67, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x18, 0x04, 0x20, 0x01,
	0x28, 0x08, 0x52, 0x0d, 0x69, 0x73, 0x41, 0x67, 0x67, 0x72, 0x65, 0x67, 0x61, 0x74, 0x69, 0x6f,
	0x6e, 0x22, 0x72, 0x0a, 0x11, 0x47, 0x65, 0x74, 0x53, 0x63, 0x72, 0x69, 0x70, 0x74, 0x4c, 0x69,
	0x73, 0x74, 0x52, 0x65, 0x73, 0x70, 0x12, 0x12, 0x0a, 0x04, 0x63, 0x6f, 0x64, 0x65, 0x18, 0x01,
	0x20, 0x01, 0x28, 0x05, 0x52, 0x04, 0x63, 0x6f, 0x64, 0x65, 0x12, 0x10, 0x0a, 0x03, 0x6d, 0x73,
	0x67, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x03, 0x6d, 0x73, 0x67, 0x12, 0x37, 0x0a, 0x04,
	0x64, 0x61, 0x74, 0x61, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x23, 0x2e, 0x76, 0x63, 0x2e,
	0x73, 0x76, 0x63, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x2e, 0x47, 0x65, 0x74, 0x53, 0x63, 0x72,
	0x69, 0x70, 0x74, 0x4c, 0x69, 0x73, 0x74, 0x52, 0x65, 0x73, 0x70, 0x44, 0x61, 0x74, 0x61, 0x52,
	0x04, 0x64, 0x61, 0x74, 0x61, 0x22, 0x3d, 0x0a, 0x12, 0x47, 0x65, 0x74, 0x53, 0x63, 0x72, 0x69,
	0x70, 0x74, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x52, 0x65, 0x71, 0x12, 0x27, 0x0a, 0x09, 0x73,
	0x63, 0x72, 0x69, 0x70, 0x74, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x42, 0x0a,
	0xe0, 0x41, 0x02, 0xfa, 0x42, 0x04, 0x22, 0x02, 0x20, 0x00, 0x52, 0x08, 0x73, 0x63, 0x72, 0x69,
	0x70, 0x74, 0x49, 0x64, 0x22, 0x65, 0x0a, 0x13, 0x47, 0x65, 0x74, 0x53, 0x63, 0x72, 0x69, 0x70,
	0x74, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x52, 0x65, 0x73, 0x70, 0x12, 0x12, 0x0a, 0x04, 0x63,
	0x6f, 0x64, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x05, 0x52, 0x04, 0x63, 0x6f, 0x64, 0x65, 0x12,
	0x10, 0x0a, 0x03, 0x6d, 0x73, 0x67, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x03, 0x6d, 0x73,
	0x67, 0x12, 0x28, 0x0a, 0x04, 0x64, 0x61, 0x74, 0x61, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0b, 0x32,
	0x14, 0x2e, 0x76, 0x63, 0x2e, 0x73, 0x76, 0x63, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x2e, 0x53,
	0x63, 0x72, 0x69, 0x70, 0x74, 0x52, 0x04, 0x64, 0x61, 0x74, 0x61, 0x22, 0xaa, 0x01, 0x0a, 0x09,
	0x53, 0x65, 0x61, 0x72, 0x63, 0x68, 0x52, 0x65, 0x71, 0x12, 0x24, 0x0a, 0x07, 0x6b, 0x65, 0x79,
	0x77, 0x6f, 0x72, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x42, 0x0a, 0xe0, 0x41, 0x02, 0xfa,
	0x42, 0x04, 0x72, 0x02, 0x10, 0x01, 0x52, 0x07, 0x6b, 0x65, 0x79, 0x77, 0x6f, 0x72, 0x64, 0x12,
	0x46, 0x0a, 0x0b, 0x73, 0x65, 0x61, 0x72, 0x63, 0x68, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x18, 0x02,
	0x20, 0x01, 0x28, 0x0e, 0x32, 0x18, 0x2e, 0x76, 0x63, 0x2e, 0x73, 0x76, 0x63, 0x73, 0x63, 0x72,
	0x69, 0x70, 0x74, 0x2e, 0x53, 0x65, 0x61, 0x72, 0x63, 0x68, 0x54, 0x79, 0x70, 0x65, 0x42, 0x0b,
	0xe0, 0x41, 0x02, 0xfa, 0x42, 0x05, 0x82, 0x01, 0x02, 0x10, 0x01, 0x52, 0x0a, 0x73, 0x65, 0x61,
	0x72, 0x63, 0x68, 0x54, 0x79, 0x70, 0x65, 0x12, 0x12, 0x0a, 0x04, 0x70, 0x61, 0x67, 0x65, 0x18,
	0x03, 0x20, 0x01, 0x28, 0x05, 0x52, 0x04, 0x70, 0x61, 0x67, 0x65, 0x12, 0x1b, 0x0a, 0x09, 0x70,
	0x61, 0x67, 0x65, 0x5f, 0x73, 0x69, 0x7a, 0x65, 0x18, 0x04, 0x20, 0x01, 0x28, 0x05, 0x52, 0x08,
	0x70, 0x61, 0x67, 0x65, 0x53, 0x69, 0x7a, 0x65, 0x22, 0x64, 0x0a, 0x0a, 0x53, 0x65, 0x61, 0x72,
	0x63, 0x68, 0x52, 0x65, 0x73, 0x70, 0x12, 0x12, 0x0a, 0x04, 0x63, 0x6f, 0x64, 0x65, 0x18, 0x01,
	0x20, 0x01, 0x28, 0x05, 0x52, 0x04, 0x63, 0x6f, 0x64, 0x65, 0x12, 0x10, 0x0a, 0x03, 0x6d, 0x73,
	0x67, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x03, 0x6d, 0x73, 0x67, 0x12, 0x30, 0x0a, 0x04,
	0x64, 0x61, 0x74, 0x61, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1c, 0x2e, 0x76, 0x63, 0x2e,
	0x73, 0x76, 0x63, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x2e, 0x53, 0x65, 0x61, 0x72, 0x63, 0x68,
	0x52, 0x65, 0x73, 0x70, 0x44, 0x61, 0x74, 0x61, 0x52, 0x04, 0x64, 0x61, 0x74, 0x61, 0x22, 0x3a,
	0x0a, 0x0f, 0x46, 0x6f, 0x6c, 0x6c, 0x6f, 0x77, 0x53, 0x63, 0x72, 0x69, 0x70, 0x74, 0x52, 0x65,
	0x71, 0x12, 0x27, 0x0a, 0x09, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x5f, 0x69, 0x64, 0x18, 0x01,
	0x20, 0x01, 0x28, 0x03, 0x42, 0x0a, 0xe0, 0x41, 0x02, 0xfa, 0x42, 0x04, 0x22, 0x02, 0x20, 0x00,
	0x52, 0x08, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x49, 0x64, 0x22, 0x38, 0x0a, 0x10, 0x46, 0x6f,
	0x6c, 0x6c, 0x6f, 0x77, 0x53, 0x63, 0x72, 0x69, 0x70, 0x74, 0x52, 0x65, 0x73, 0x70, 0x12, 0x12,
	0x0a, 0x04, 0x63, 0x6f, 0x64, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x05, 0x52, 0x04, 0x63, 0x6f,
	0x64, 0x65, 0x12, 0x10, 0x0a, 0x03, 0x6d, 0x73, 0x67, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x03, 0x6d, 0x73, 0x67, 0x22, 0x3c, 0x0a, 0x11, 0x55, 0x6e, 0x66, 0x6f, 0x6c, 0x6c, 0x6f, 0x77,
	0x53, 0x63, 0x72, 0x69, 0x70, 0x74, 0x52, 0x65, 0x71, 0x12, 0x27, 0x0a, 0x09, 0x73, 0x63, 0x72,
	0x69, 0x70, 0x74, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x42, 0x0a, 0xe0, 0x41,
	0x02, 0xfa, 0x42, 0x04, 0x22, 0x02, 0x20, 0x00, 0x52, 0x08, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74,
	0x49, 0x64, 0x22, 0x3a, 0x0a, 0x12, 0x55, 0x6e, 0x66, 0x6f, 0x6c, 0x6c, 0x6f, 0x77, 0x53, 0x63,
	0x72, 0x69, 0x70, 0x74, 0x52, 0x65, 0x73, 0x70, 0x12, 0x12, 0x0a, 0x04, 0x63, 0x6f, 0x64, 0x65,
	0x18, 0x01, 0x20, 0x01, 0x28, 0x05, 0x52, 0x04, 0x63, 0x6f, 0x64, 0x65, 0x12, 0x10, 0x0a, 0x03,
	0x6d, 0x73, 0x67, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x03, 0x6d, 0x73, 0x67, 0x22, 0x55,
	0x0a, 0x0e, 0x53, 0x68, 0x61, 0x72, 0x65, 0x53, 0x63, 0x72, 0x69, 0x70, 0x74, 0x52, 0x65, 0x71,
	0x12, 0x27, 0x0a, 0x09, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20,
	0x01, 0x28, 0x03, 0x42, 0x0a, 0xe0, 0x41, 0x02, 0xfa, 0x42, 0x04, 0x22, 0x02, 0x20, 0x00, 0x52,
	0x08, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x49, 0x64, 0x12, 0x1a, 0x0a, 0x08, 0x70, 0x6c, 0x61,
	0x74, 0x66, 0x6f, 0x72, 0x6d, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x70, 0x6c, 0x61,
	0x74, 0x66, 0x6f, 0x72, 0x6d, 0x22, 0x37, 0x0a, 0x0f, 0x53, 0x68, 0x61, 0x72, 0x65, 0x53, 0x63,
	0x72, 0x69, 0x70, 0x74, 0x52, 0x65, 0x73, 0x70, 0x12, 0x12, 0x0a, 0x04, 0x63, 0x6f, 0x64, 0x65,
	0x18, 0x01, 0x20, 0x01, 0x28, 0x05, 0x52, 0x04, 0x63, 0x6f, 0x64, 0x65, 0x12, 0x10, 0x0a, 0x03,
	0x6d, 0x73, 0x67, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x03, 0x6d, 0x73, 0x67, 0x22, 0x95,
	0x01, 0x0a, 0x14, 0x47, 0x65, 0x74, 0x4c, 0x69, 0x6e, 0x65, 0x44, 0x75, 0x62, 0x62, 0x69, 0x6e,
	0x67, 0x41, 0x6c, 0x6c, 0x52, 0x65, 0x71, 0x12, 0x27, 0x0a, 0x09, 0x73, 0x63, 0x72, 0x69, 0x70,
	0x74, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x42, 0x0a, 0xe0, 0x41, 0x02, 0xfa,
	0x42, 0x04, 0x22, 0x02, 0x20, 0x00, 0x52, 0x08, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x49, 0x64,
	0x12, 0x23, 0x0a, 0x07, 0x6c, 0x69, 0x6e, 0x65, 0x5f, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28,
	0x03, 0x42, 0x0a, 0xe0, 0x41, 0x02, 0xfa, 0x42, 0x04, 0x22, 0x02, 0x20, 0x00, 0x52, 0x06, 0x6c,
	0x69, 0x6e, 0x65, 0x49, 0x64, 0x12, 0x12, 0x0a, 0x04, 0x70, 0x61, 0x67, 0x65, 0x18, 0x03, 0x20,
	0x01, 0x28, 0x05, 0x52, 0x04, 0x70, 0x61, 0x67, 0x65, 0x12, 0x1b, 0x0a, 0x09, 0x70, 0x61, 0x67,
	0x65, 0x5f, 0x73, 0x69, 0x7a, 0x65, 0x18, 0x04, 0x20, 0x01, 0x28, 0x05, 0x52, 0x08, 0x70, 0x61,
	0x67, 0x65, 0x53, 0x69, 0x7a, 0x65, 0x22, 0x78, 0x0a, 0x15, 0x47, 0x65, 0x74, 0x4c, 0x69, 0x6e,
	0x65, 0x44, 0x75, 0x62, 0x62, 0x69, 0x6e, 0x67, 0x41, 0x6c, 0x6c, 0x52, 0x65, 0x73, 0x70, 0x12,
	0x12, 0x0a, 0x04, 0x63, 0x6f, 0x64, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x05, 0x52, 0x04, 0x63,
	0x6f, 0x64, 0x65, 0x12, 0x10, 0x0a, 0x03, 0x6d, 0x73, 0x67, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x03, 0x6d, 0x73, 0x67, 0x12, 0x39, 0x0a, 0x04, 0x64, 0x61, 0x74, 0x61, 0x18, 0x03, 0x20,
	0x01, 0x28, 0x0b, 0x32, 0x25, 0x2e, 0x76, 0x63, 0x2e, 0x73, 0x76, 0x63, 0x73, 0x63, 0x72, 0x69,
	0x70, 0x74, 0x2e, 0x47, 0x65, 0x74, 0x4c, 0x69, 0x6e, 0x65, 0x44, 0x75, 0x62, 0x62, 0x69, 0x6e,
	0x67, 0x73, 0x52, 0x65, 0x73, 0x70, 0x44, 0x61, 0x74, 0x61, 0x52, 0x04, 0x64, 0x61, 0x74, 0x61,
	0x22, 0x6e, 0x0a, 0x17, 0x47, 0x65, 0x74, 0x4c, 0x69, 0x6e, 0x65, 0x44, 0x75, 0x62, 0x62, 0x69,
	0x6e, 0x67, 0x53, 0x69, 0x6d, 0x70, 0x6c, 0x65, 0x52, 0x65, 0x71, 0x12, 0x27, 0x0a, 0x09, 0x73,
	0x63, 0x72, 0x69, 0x70, 0x74, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x42, 0x0a,
	0xe0, 0x41, 0x02, 0xfa, 0x42, 0x04, 0x22, 0x02, 0x20, 0x00, 0x52, 0x08, 0x73, 0x63, 0x72, 0x69,
	0x70, 0x74, 0x49, 0x64, 0x12, 0x2a, 0x0a, 0x11, 0x64, 0x75, 0x62, 0x62, 0x69, 0x6e, 0x67, 0x5f,
	0x72, 0x65, 0x63, 0x6f, 0x72, 0x64, 0x5f, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x03, 0x52,
	0x0f, 0x64, 0x75, 0x62, 0x62, 0x69, 0x6e, 0x67, 0x52, 0x65, 0x63, 0x6f, 0x72, 0x64, 0x49, 0x64,
	0x22, 0x81, 0x01, 0x0a, 0x18, 0x47, 0x65, 0x74, 0x4c, 0x69, 0x6e, 0x65, 0x44, 0x75, 0x62, 0x62,
	0x69, 0x6e, 0x67, 0x53, 0x69, 0x6d, 0x70, 0x6c, 0x65, 0x52, 0x65, 0x73, 0x70, 0x12, 0x12, 0x0a,
	0x04, 0x63, 0x6f, 0x64, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x05, 0x52, 0x04, 0x63, 0x6f, 0x64,
	0x65, 0x12, 0x10, 0x0a, 0x03, 0x6d, 0x73, 0x67, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x03,
	0x6d, 0x73, 0x67, 0x12, 0x3f, 0x0a, 0x04, 0x64, 0x61, 0x74, 0x61, 0x18, 0x03, 0x20, 0x01, 0x28,
	0x0b, 0x32, 0x2b, 0x2e, 0x76, 0x63, 0x2e, 0x73, 0x76, 0x63, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74,
	0x2e, 0x47, 0x65, 0x74, 0x4c, 0x69, 0x6e, 0x65, 0x44, 0x75, 0x62, 0x62, 0x69, 0x6e, 0x67, 0x73,
	0x53, 0x69, 0x6d, 0x70, 0x6c, 0x65, 0x52, 0x65, 0x73, 0x70, 0x44, 0x61, 0x74, 0x61, 0x52, 0x04,
	0x64, 0x61, 0x74, 0x61, 0x22, 0x43, 0x0a, 0x18, 0x47, 0x65, 0x74, 0x53, 0x63, 0x72, 0x69, 0x70,
	0x74, 0x46, 0x69, 0x72, 0x73, 0x74, 0x44, 0x75, 0x62, 0x62, 0x69, 0x6e, 0x67, 0x52, 0x65, 0x71,
	0x12, 0x27, 0x0a, 0x09, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20,
	0x01, 0x28, 0x03, 0x42, 0x0a, 0xe0, 0x41, 0x02, 0xfa, 0x42, 0x04, 0x22, 0x02, 0x20, 0x00, 0x52,
	0x08, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x49, 0x64, 0x22, 0x7e, 0x0a, 0x19, 0x47, 0x65, 0x74,
	0x53, 0x63, 0x72, 0x69, 0x70, 0x74, 0x46, 0x69, 0x72, 0x73, 0x74, 0x44, 0x75, 0x62, 0x62, 0x69,
	0x6e, 0x67, 0x52, 0x65, 0x73, 0x70, 0x12, 0x12, 0x0a, 0x04, 0x63, 0x6f, 0x64, 0x65, 0x18, 0x01,
	0x20, 0x01, 0x28, 0x05, 0x52, 0x04, 0x63, 0x6f, 0x64, 0x65, 0x12, 0x10, 0x0a, 0x03, 0x6d, 0x73,
	0x67, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x03, 0x6d, 0x73, 0x67, 0x12, 0x3b, 0x0a, 0x04,
	0x64, 0x61, 0x74, 0x61, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x27, 0x2e, 0x76, 0x63, 0x2e,
	0x73, 0x76, 0x63, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x2e, 0x47, 0x65, 0x74, 0x53, 0x63, 0x72,
	0x69, 0x70, 0x74, 0x46, 0x69, 0x72, 0x73, 0x74, 0x44, 0x75, 0x62, 0x62, 0x69, 0x6e, 0x67, 0x44,
	0x61, 0x74, 0x61, 0x52, 0x04, 0x64, 0x61, 0x74, 0x61, 0x22, 0x4d, 0x0a, 0x1d, 0x42, 0x61, 0x74,
	0x63, 0x68, 0x47, 0x65, 0x74, 0x53, 0x63, 0x72, 0x69, 0x70, 0x74, 0x46, 0x69, 0x72, 0x73, 0x74,
	0x44, 0x75, 0x62, 0x62, 0x69, 0x6e, 0x67, 0x52, 0x65, 0x71, 0x12, 0x2c, 0x0a, 0x0a, 0x73, 0x63,
	0x72, 0x69, 0x70, 0x74, 0x5f, 0x69, 0x64, 0x73, 0x18, 0x01, 0x20, 0x03, 0x28, 0x03, 0x42, 0x0d,
	0xe0, 0x41, 0x02, 0xfa, 0x42, 0x07, 0x92, 0x01, 0x04, 0x08, 0x01, 0x10, 0x32, 0x52, 0x09, 0x73,
	0x63, 0x72, 0x69, 0x70, 0x74, 0x49, 0x64, 0x73, 0x22, 0x88, 0x01, 0x0a, 0x1e, 0x42, 0x61, 0x74,
	0x63, 0x68, 0x47, 0x65, 0x74, 0x53, 0x63, 0x72, 0x69, 0x70, 0x74, 0x46, 0x69, 0x72, 0x73, 0x74,
	0x44, 0x75, 0x62, 0x62, 0x69, 0x6e, 0x67, 0x52, 0x65, 0x73, 0x70, 0x12, 0x12, 0x0a, 0x04, 0x63,
	0x6f, 0x64, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x05, 0x52, 0x04, 0x63, 0x6f, 0x64, 0x65, 0x12,
	0x10, 0x0a, 0x03, 0x6d, 0x73, 0x67, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x03, 0x6d, 0x73,
	0x67, 0x12, 0x40, 0x0a, 0x04, 0x64, 0x61, 0x74, 0x61, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0b, 0x32,
	0x2c, 0x2e, 0x76, 0x63, 0x2e, 0x73, 0x76, 0x63, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x2e, 0x42,
	0x61, 0x74, 0x63, 0x68, 0x47, 0x65, 0x74, 0x53, 0x63, 0x72, 0x69, 0x70, 0x74, 0x46, 0x69, 0x72,
	0x73, 0x74, 0x44, 0x75, 0x62, 0x62, 0x69, 0x6e, 0x67, 0x44, 0x61, 0x74, 0x61, 0x52, 0x04, 0x64,
	0x61, 0x74, 0x61, 0x22, 0xef, 0x01, 0x0a, 0x10, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x44, 0x75,
	0x62, 0x62, 0x69, 0x6e, 0x67, 0x52, 0x65, 0x71, 0x12, 0x27, 0x0a, 0x09, 0x73, 0x63, 0x72, 0x69,
	0x70, 0x74, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x42, 0x0a, 0xe0, 0x41, 0x02,
	0xfa, 0x42, 0x04, 0x22, 0x02, 0x20, 0x00, 0x52, 0x08, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x49,
	0x64, 0x12, 0x55, 0x0a, 0x0d, 0x6c, 0x69, 0x6e, 0x65, 0x5f, 0x64, 0x75, 0x62, 0x62, 0x69, 0x6e,
	0x67, 0x73, 0x18, 0x02, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x30, 0x2e, 0x76, 0x63, 0x2e, 0x62, 0x69,
	0x7a, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x2e, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x44, 0x75,
	0x62, 0x62, 0x69, 0x6e, 0x67, 0x52, 0x65, 0x71, 0x2e, 0x4c, 0x69, 0x6e, 0x65, 0x44, 0x75, 0x62,
	0x62, 0x69, 0x6e, 0x67, 0x73, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x52, 0x0c, 0x6c, 0x69, 0x6e, 0x65,
	0x44, 0x75, 0x62, 0x62, 0x69, 0x6e, 0x67, 0x73, 0x1a, 0x5b, 0x0a, 0x11, 0x4c, 0x69, 0x6e, 0x65,
	0x44, 0x75, 0x62, 0x62, 0x69, 0x6e, 0x67, 0x73, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x12, 0x10, 0x0a,
	0x03, 0x6b, 0x65, 0x79, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x52, 0x03, 0x6b, 0x65, 0x79, 0x12,
	0x30, 0x0a, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1a,
	0x2e, 0x76, 0x63, 0x2e, 0x73, 0x76, 0x63, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x2e, 0x4c, 0x69,
	0x6e, 0x65, 0x44, 0x75, 0x62, 0x62, 0x69, 0x6e, 0x67, 0x73, 0x52, 0x05, 0x76, 0x61, 0x6c, 0x75,
	0x65, 0x3a, 0x02, 0x38, 0x01, 0x22, 0x72, 0x0a, 0x11, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x44,
	0x75, 0x62, 0x62, 0x69, 0x6e, 0x67, 0x52, 0x65, 0x73, 0x70, 0x12, 0x12, 0x0a, 0x04, 0x63, 0x6f,
	0x64, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x05, 0x52, 0x04, 0x63, 0x6f, 0x64, 0x65, 0x12, 0x10,
	0x0a, 0x03, 0x6d, 0x73, 0x67, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x03, 0x6d, 0x73, 0x67,
	0x12, 0x37, 0x0a, 0x04, 0x64, 0x61, 0x74, 0x61, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x23,
	0x2e, 0x76, 0x63, 0x2e, 0x73, 0x76, 0x63, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x2e, 0x43, 0x72,
	0x65, 0x61, 0x74, 0x65, 0x44, 0x75, 0x62, 0x62, 0x69, 0x6e, 0x67, 0x52, 0x65, 0x73, 0x70, 0x44,
	0x61, 0x74, 0x61, 0x52, 0x04, 0x64, 0x61, 0x74, 0x61, 0x22, 0x50, 0x0a, 0x16, 0x44, 0x65, 0x6c,
	0x65, 0x74, 0x65, 0x44, 0x75, 0x62, 0x62, 0x69, 0x6e, 0x67, 0x52, 0x65, 0x63, 0x6f, 0x72, 0x64,
	0x52, 0x65, 0x71, 0x12, 0x36, 0x0a, 0x11, 0x64, 0x75, 0x62, 0x62, 0x69, 0x6e, 0x67, 0x5f, 0x72,
	0x65, 0x63, 0x6f, 0x72, 0x64, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x42, 0x0a,
	0xe0, 0x41, 0x02, 0xfa, 0x42, 0x04, 0x22, 0x02, 0x20, 0x00, 0x52, 0x0f, 0x64, 0x75, 0x62, 0x62,
	0x69, 0x6e, 0x67, 0x52, 0x65, 0x63, 0x6f, 0x72, 0x64, 0x49, 0x64, 0x22, 0x3f, 0x0a, 0x17, 0x44,
	0x65, 0x6c, 0x65, 0x74, 0x65, 0x44, 0x75, 0x62, 0x62, 0x69, 0x6e, 0x67, 0x52, 0x65, 0x63, 0x6f,
	0x72, 0x64, 0x52, 0x65, 0x73, 0x70, 0x12, 0x12, 0x0a, 0x04, 0x63, 0x6f, 0x64, 0x65, 0x18, 0x01,
	0x20, 0x01, 0x28, 0x05, 0x52, 0x04, 0x63, 0x6f, 0x64, 0x65, 0x12, 0x10, 0x0a, 0x03, 0x6d, 0x73,
	0x67, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x03, 0x6d, 0x73, 0x67, 0x22, 0xe5, 0x01, 0x0a,
	0x11, 0x47, 0x65, 0x74, 0x43, 0x6f, 0x6d, 0x6d, 0x65, 0x6e, 0x74, 0x4c, 0x69, 0x73, 0x74, 0x52,
	0x65, 0x71, 0x12, 0x41, 0x0a, 0x0c, 0x63, 0x6f, 0x6d, 0x6d, 0x65, 0x6e, 0x74, 0x5f, 0x74, 0x79,
	0x70, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x19, 0x2e, 0x76, 0x63, 0x2e, 0x73, 0x76,
	0x63, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x2e, 0x43, 0x6f, 0x6d, 0x6d, 0x65, 0x6e, 0x74, 0x54,
	0x79, 0x70, 0x65, 0x42, 0x03, 0xe0, 0x41, 0x02, 0x52, 0x0b, 0x63, 0x6f, 0x6d, 0x6d, 0x65, 0x6e,
	0x74, 0x54, 0x79, 0x70, 0x65, 0x12, 0x20, 0x0a, 0x09, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x5f,
	0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x03, 0x42, 0x03, 0xe0, 0x41, 0x02, 0x52, 0x08, 0x73,
	0x63, 0x72, 0x69, 0x70, 0x74, 0x49, 0x64, 0x12, 0x1d, 0x0a, 0x0a, 0x64, 0x75, 0x62, 0x62, 0x69,
	0x6e, 0x67, 0x5f, 0x69, 0x64, 0x18, 0x03, 0x20, 0x01, 0x28, 0x03, 0x52, 0x09, 0x64, 0x75, 0x62,
	0x62, 0x69, 0x6e, 0x67, 0x49, 0x64, 0x12, 0x1b, 0x0a, 0x09, 0x70, 0x61, 0x72, 0x65, 0x6e, 0x74,
	0x5f, 0x69, 0x64, 0x18, 0x04, 0x20, 0x01, 0x28, 0x03, 0x52, 0x08, 0x70, 0x61, 0x72, 0x65, 0x6e,
	0x74, 0x49, 0x64, 0x12, 0x12, 0x0a, 0x04, 0x70, 0x61, 0x67, 0x65, 0x18, 0x05, 0x20, 0x01, 0x28,
	0x05, 0x52, 0x04, 0x70, 0x61, 0x67, 0x65, 0x12, 0x1b, 0x0a, 0x09, 0x70, 0x61, 0x67, 0x65, 0x5f,
	0x73, 0x69, 0x7a, 0x65, 0x18, 0x06, 0x20, 0x01, 0x28, 0x05, 0x52, 0x08, 0x70, 0x61, 0x67, 0x65,
	0x53, 0x69, 0x7a, 0x65, 0x22, 0x74, 0x0a, 0x12, 0x47, 0x65, 0x74, 0x43, 0x6f, 0x6d, 0x6d, 0x65,
	0x6e, 0x74, 0x4c, 0x69, 0x73, 0x74, 0x52, 0x65, 0x73, 0x70, 0x12, 0x12, 0x0a, 0x04, 0x63, 0x6f,
	0x64, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x05, 0x52, 0x04, 0x63, 0x6f, 0x64, 0x65, 0x12, 0x10,
	0x0a, 0x03, 0x6d, 0x73, 0x67, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x03, 0x6d, 0x73, 0x67,
	0x12, 0x38, 0x0a, 0x04, 0x64, 0x61, 0x74, 0x61, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x24,
	0x2e, 0x76, 0x63, 0x2e, 0x73, 0x76, 0x63, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x2e, 0x47, 0x65,
	0x74, 0x43, 0x6f, 0x6d, 0x6d, 0x65, 0x6e, 0x74, 0x4c, 0x69, 0x73, 0x74, 0x52, 0x65, 0x73, 0x70,
	0x44, 0x61, 0x74, 0x61, 0x52, 0x04, 0x64, 0x61, 0x74, 0x61, 0x22, 0x70, 0x0a, 0x14, 0x47, 0x65,
	0x74, 0x43, 0x6f, 0x6d, 0x6d, 0x65, 0x6e, 0x74, 0x52, 0x65, 0x70, 0x6c, 0x69, 0x65, 0x73, 0x52,
	0x65, 0x71, 0x12, 0x27, 0x0a, 0x09, 0x70, 0x61, 0x72, 0x65, 0x6e, 0x74, 0x5f, 0x69, 0x64, 0x18,
	0x01, 0x20, 0x01, 0x28, 0x03, 0x42, 0x0a, 0xe0, 0x41, 0x02, 0xfa, 0x42, 0x04, 0x22, 0x02, 0x20,
	0x00, 0x52, 0x08, 0x70, 0x61, 0x72, 0x65, 0x6e, 0x74, 0x49, 0x64, 0x12, 0x12, 0x0a, 0x04, 0x70,
	0x61, 0x67, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x05, 0x52, 0x04, 0x70, 0x61, 0x67, 0x65, 0x12,
	0x1b, 0x0a, 0x09, 0x70, 0x61, 0x67, 0x65, 0x5f, 0x73, 0x69, 0x7a, 0x65, 0x18, 0x03, 0x20, 0x01,
	0x28, 0x05, 0x52, 0x08, 0x70, 0x61, 0x67, 0x65, 0x53, 0x69, 0x7a, 0x65, 0x22, 0x77, 0x0a, 0x15,
	0x47, 0x65, 0x74, 0x43, 0x6f, 0x6d, 0x6d, 0x65, 0x6e, 0x74, 0x52, 0x65, 0x70, 0x6c, 0x69, 0x65,
	0x73, 0x52, 0x65, 0x73, 0x70, 0x12, 0x12, 0x0a, 0x04, 0x63, 0x6f, 0x64, 0x65, 0x18, 0x01, 0x20,
	0x01, 0x28, 0x05, 0x52, 0x04, 0x63, 0x6f, 0x64, 0x65, 0x12, 0x10, 0x0a, 0x03, 0x6d, 0x73, 0x67,
	0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x03, 0x6d, 0x73, 0x67, 0x12, 0x38, 0x0a, 0x04, 0x64,
	0x61, 0x74, 0x61, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x24, 0x2e, 0x76, 0x63, 0x2e, 0x73,
	0x76, 0x63, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x2e, 0x47, 0x65, 0x74, 0x43, 0x6f, 0x6d, 0x6d,
	0x65, 0x6e, 0x74, 0x4c, 0x69, 0x73, 0x74, 0x52, 0x65, 0x73, 0x70, 0x44, 0x61, 0x74, 0x61, 0x52,
	0x04, 0x64, 0x61, 0x74, 0x61, 0x22, 0xe0, 0x04, 0x0a, 0x10, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65,
	0x43, 0x6f, 0x6d, 0x6d, 0x65, 0x6e, 0x74, 0x52, 0x65, 0x71, 0x12, 0x49, 0x0a, 0x0c, 0x63, 0x6f,
	0x6d, 0x6d, 0x65, 0x6e, 0x74, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0e,
	0x32, 0x19, 0x2e, 0x76, 0x63, 0x2e, 0x73, 0x76, 0x63, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x2e,
	0x43, 0x6f, 0x6d, 0x6d, 0x65, 0x6e, 0x74, 0x54, 0x79, 0x70, 0x65, 0x42, 0x0b, 0xe0, 0x41, 0x02,
	0xfa, 0x42, 0x05, 0x82, 0x01, 0x02, 0x10, 0x01, 0x52, 0x0b, 0x63, 0x6f, 0x6d, 0x6d, 0x65, 0x6e,
	0x74, 0x54, 0x79, 0x70, 0x65, 0x12, 0x1b, 0x0a, 0x09, 0x70, 0x61, 0x72, 0x65, 0x6e, 0x74, 0x5f,
	0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x03, 0x52, 0x08, 0x70, 0x61, 0x72, 0x65, 0x6e, 0x74,
	0x49, 0x64, 0x12, 0x1b, 0x0a, 0x09, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x5f, 0x69, 0x64, 0x18,
	0x03, 0x20, 0x01, 0x28, 0x03, 0x52, 0x08, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x49, 0x64, 0x12,
	0x1d, 0x0a, 0x0a, 0x64, 0x75, 0x62, 0x62, 0x69, 0x6e, 0x67, 0x5f, 0x69, 0x64, 0x18, 0x04, 0x20,
	0x01, 0x28, 0x03, 0x52, 0x09, 0x64, 0x75, 0x62, 0x62, 0x69, 0x6e, 0x67, 0x49, 0x64, 0x12, 0x21,
	0x0a, 0x0c, 0x63, 0x68, 0x61, 0x72, 0x61, 0x63, 0x74, 0x65, 0x72, 0x5f, 0x69, 0x64, 0x18, 0x05,
	0x20, 0x01, 0x28, 0x03, 0x52, 0x0b, 0x63, 0x68, 0x61, 0x72, 0x61, 0x63, 0x74, 0x65, 0x72, 0x49,
	0x64, 0x12, 0x2c, 0x0a, 0x12, 0x63, 0x68, 0x61, 0x72, 0x61, 0x63, 0x74, 0x65, 0x72, 0x5f, 0x61,
	0x73, 0x73, 0x65, 0x74, 0x5f, 0x69, 0x64, 0x18, 0x06, 0x20, 0x01, 0x28, 0x03, 0x52, 0x10, 0x63,
	0x68, 0x61, 0x72, 0x61, 0x63, 0x74, 0x65, 0x72, 0x41, 0x73, 0x73, 0x65, 0x74, 0x49, 0x64, 0x12,
	0x41, 0x0a, 0x0c, 0x63, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x74, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x18,
	0x07, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x19, 0x2e, 0x76, 0x63, 0x2e, 0x73, 0x76, 0x63, 0x73, 0x63,
	0x72, 0x69, 0x70, 0x74, 0x2e, 0x43, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x74, 0x54, 0x79, 0x70, 0x65,
	0x42, 0x03, 0xe0, 0x41, 0x02, 0x52, 0x0b, 0x63, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x74, 0x54, 0x79,
	0x70, 0x65, 0x12, 0x18, 0x0a, 0x07, 0x63, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x74, 0x18, 0x08, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x07, 0x63, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x74, 0x12, 0x1b, 0x0a, 0x09,
	0x76, 0x6f, 0x69, 0x63, 0x65, 0x5f, 0x75, 0x72, 0x6c, 0x18, 0x09, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x08, 0x76, 0x6f, 0x69, 0x63, 0x65, 0x55, 0x72, 0x6c, 0x12, 0x25, 0x0a, 0x0e, 0x76, 0x6f, 0x69,
	0x63, 0x65, 0x5f, 0x64, 0x75, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x18, 0x0a, 0x20, 0x01, 0x28,
	0x05, 0x52, 0x0d, 0x76, 0x6f, 0x69, 0x63, 0x65, 0x44, 0x75, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e,
	0x12, 0x2c, 0x0a, 0x12, 0x6f, 0x72, 0x69, 0x67, 0x69, 0x6e, 0x61, 0x6c, 0x5f, 0x76, 0x6f, 0x69,
	0x63, 0x65, 0x5f, 0x75, 0x72, 0x6c, 0x18, 0x0c, 0x20, 0x01, 0x28, 0x09, 0x52, 0x10, 0x6f, 0x72,
	0x69, 0x67, 0x69, 0x6e, 0x61, 0x6c, 0x56, 0x6f, 0x69, 0x63, 0x65, 0x55, 0x72, 0x6c, 0x12, 0x36,
	0x0a, 0x17, 0x6f, 0x72, 0x69, 0x67, 0x69, 0x6e, 0x61, 0x6c, 0x5f, 0x76, 0x6f, 0x69, 0x63, 0x65,
	0x5f, 0x64, 0x75, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x18, 0x0d, 0x20, 0x01, 0x28, 0x05, 0x52,
	0x15, 0x6f, 0x72, 0x69, 0x67, 0x69, 0x6e, 0x61, 0x6c, 0x56, 0x6f, 0x69, 0x63, 0x65, 0x44, 0x75,
	0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x22, 0x0a, 0x0d, 0x73, 0x76, 0x63, 0x5f, 0x76, 0x6f,
	0x69, 0x63, 0x65, 0x5f, 0x75, 0x72, 0x6c, 0x18, 0x0e, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0b, 0x73,
	0x76, 0x63, 0x56, 0x6f, 0x69, 0x63, 0x65, 0x55, 0x72, 0x6c, 0x12, 0x2c, 0x0a, 0x12, 0x73, 0x76,
	0x63, 0x5f, 0x76, 0x6f, 0x69, 0x63, 0x65, 0x5f, 0x64, 0x75, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e,
	0x18, 0x0f, 0x20, 0x01, 0x28, 0x05, 0x52, 0x10, 0x73, 0x76, 0x63, 0x56, 0x6f, 0x69, 0x63, 0x65,
	0x44, 0x75, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x22, 0x72, 0x0a, 0x11, 0x43, 0x72, 0x65, 0x61,
	0x74, 0x65, 0x43, 0x6f, 0x6d, 0x6d, 0x65, 0x6e, 0x74, 0x52, 0x65, 0x73, 0x70, 0x12, 0x12, 0x0a,
	0x04, 0x63, 0x6f, 0x64, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x05, 0x52, 0x04, 0x63, 0x6f, 0x64,
	0x65, 0x12, 0x10, 0x0a, 0x03, 0x6d, 0x73, 0x67, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x03,
	0x6d, 0x73, 0x67, 0x12, 0x37, 0x0a, 0x04, 0x64, 0x61, 0x74, 0x61, 0x18, 0x03, 0x20, 0x01, 0x28,
	0x0b, 0x32, 0x23, 0x2e, 0x76, 0x63, 0x2e, 0x73, 0x76, 0x63, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74,
	0x2e, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x43, 0x6f, 0x6d, 0x6d, 0x65, 0x6e, 0x74, 0x52, 0x65,
	0x73, 0x70, 0x44, 0x61, 0x74, 0x61, 0x52, 0x04, 0x64, 0x61, 0x74, 0x61, 0x22, 0x59, 0x0a, 0x10,
	0x53, 0x65, 0x74, 0x43, 0x6f, 0x6d, 0x6d, 0x65, 0x6e, 0x74, 0x54, 0x6f, 0x70, 0x52, 0x65, 0x71,
	0x12, 0x29, 0x0a, 0x0a, 0x63, 0x6f, 0x6d, 0x6d, 0x65, 0x6e, 0x74, 0x5f, 0x69, 0x64, 0x18, 0x01,
	0x20, 0x01, 0x28, 0x03, 0x42, 0x0a, 0xe0, 0x41, 0x02, 0xfa, 0x42, 0x04, 0x22, 0x02, 0x20, 0x00,
	0x52, 0x09, 0x63, 0x6f, 0x6d, 0x6d, 0x65, 0x6e, 0x74, 0x49, 0x64, 0x12, 0x1a, 0x0a, 0x06, 0x69,
	0x73, 0x5f, 0x74, 0x6f, 0x70, 0x18, 0x02, 0x20, 0x01, 0x28, 0x08, 0x42, 0x03, 0xe0, 0x41, 0x02,
	0x52, 0x05, 0x69, 0x73, 0x54, 0x6f, 0x70, 0x22, 0x39, 0x0a, 0x11, 0x53, 0x65, 0x74, 0x43, 0x6f,
	0x6d, 0x6d, 0x65, 0x6e, 0x74, 0x54, 0x6f, 0x70, 0x52, 0x65, 0x73, 0x70, 0x12, 0x12, 0x0a, 0x04,
	0x63, 0x6f, 0x64, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x05, 0x52, 0x04, 0x63, 0x6f, 0x64, 0x65,
	0x12, 0x10, 0x0a, 0x03, 0x6d, 0x73, 0x67, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x03, 0x6d,
	0x73, 0x67, 0x22, 0x3d, 0x0a, 0x10, 0x44, 0x65, 0x6c, 0x65, 0x74, 0x65, 0x43, 0x6f, 0x6d, 0x6d,
	0x65, 0x6e, 0x74, 0x52, 0x65, 0x71, 0x12, 0x29, 0x0a, 0x0a, 0x63, 0x6f, 0x6d, 0x6d, 0x65, 0x6e,
	0x74, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x42, 0x0a, 0xe0, 0x41, 0x02, 0xfa,
	0x42, 0x04, 0x22, 0x02, 0x20, 0x00, 0x52, 0x09, 0x63, 0x6f, 0x6d, 0x6d, 0x65, 0x6e, 0x74, 0x49,
	0x64, 0x22, 0x39, 0x0a, 0x11, 0x44, 0x65, 0x6c, 0x65, 0x74, 0x65, 0x43, 0x6f, 0x6d, 0x6d, 0x65,
	0x6e, 0x74, 0x52, 0x65, 0x73, 0x70, 0x12, 0x12, 0x0a, 0x04, 0x63, 0x6f, 0x64, 0x65, 0x18, 0x01,
	0x20, 0x01, 0x28, 0x05, 0x52, 0x04, 0x63, 0x6f, 0x64, 0x65, 0x12, 0x10, 0x0a, 0x03, 0x6d, 0x73,
	0x67, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x03, 0x6d, 0x73, 0x67, 0x22, 0x3d, 0x0a, 0x10,
	0x47, 0x65, 0x74, 0x43, 0x6f, 0x6d, 0x6d, 0x65, 0x6e, 0x74, 0x41, 0x53, 0x52, 0x52, 0x65, 0x71,
	0x12, 0x29, 0x0a, 0x0a, 0x63, 0x6f, 0x6d, 0x6d, 0x65, 0x6e, 0x74, 0x5f, 0x69, 0x64, 0x18, 0x01,
	0x20, 0x01, 0x28, 0x03, 0x42, 0x0a, 0xe0, 0x41, 0x02, 0xfa, 0x42, 0x04, 0x22, 0x02, 0x20, 0x00,
	0x52, 0x09, 0x63, 0x6f, 0x6d, 0x6d, 0x65, 0x6e, 0x74, 0x49, 0x64, 0x22, 0x72, 0x0a, 0x11, 0x47,
	0x65, 0x74, 0x43, 0x6f, 0x6d, 0x6d, 0x65, 0x6e, 0x74, 0x41, 0x53, 0x52, 0x52, 0x65, 0x73, 0x70,
	0x12, 0x12, 0x0a, 0x04, 0x63, 0x6f, 0x64, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x05, 0x52, 0x04,
	0x63, 0x6f, 0x64, 0x65, 0x12, 0x10, 0x0a, 0x03, 0x6d, 0x73, 0x67, 0x18, 0x02, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x03, 0x6d, 0x73, 0x67, 0x12, 0x37, 0x0a, 0x04, 0x64, 0x61, 0x74, 0x61, 0x18, 0x03,
	0x20, 0x01, 0x28, 0x0b, 0x32, 0x23, 0x2e, 0x76, 0x63, 0x2e, 0x73, 0x76, 0x63, 0x73, 0x63, 0x72,
	0x69, 0x70, 0x74, 0x2e, 0x47, 0x65, 0x74, 0x43, 0x6f, 0x6d, 0x6d, 0x65, 0x6e, 0x74, 0x41, 0x53,
	0x52, 0x52, 0x65, 0x73, 0x70, 0x44, 0x61, 0x74, 0x61, 0x52, 0x04, 0x64, 0x61, 0x74, 0x61, 0x22,
	0x71, 0x0a, 0x07, 0x4c, 0x69, 0x6b, 0x65, 0x52, 0x65, 0x71, 0x12, 0x40, 0x0a, 0x09, 0x6c, 0x69,
	0x6b, 0x65, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x16, 0x2e,
	0x76, 0x63, 0x2e, 0x73, 0x76, 0x63, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x2e, 0x4c, 0x69, 0x6b,
	0x65, 0x54, 0x79, 0x70, 0x65, 0x42, 0x0b, 0xe0, 0x41, 0x02, 0xfa, 0x42, 0x05, 0x82, 0x01, 0x02,
	0x10, 0x01, 0x52, 0x08, 0x6c, 0x69, 0x6b, 0x65, 0x54, 0x79, 0x70, 0x65, 0x12, 0x24, 0x0a, 0x09,
	0x74, 0x61, 0x72, 0x67, 0x65, 0x74, 0x5f, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x03, 0x42,
	0x07, 0xfa, 0x42, 0x04, 0x22, 0x02, 0x20, 0x00, 0x52, 0x08, 0x74, 0x61, 0x72, 0x67, 0x65, 0x74,
	0x49, 0x64, 0x22, 0x30, 0x0a, 0x08, 0x4c, 0x69, 0x6b, 0x65, 0x52, 0x65, 0x73, 0x70, 0x12, 0x12,
	0x0a, 0x04, 0x63, 0x6f, 0x64, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x05, 0x52, 0x04, 0x63, 0x6f,
	0x64, 0x65, 0x12, 0x10, 0x0a, 0x03, 0x6d, 0x73, 0x67, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x03, 0x6d, 0x73, 0x67, 0x22, 0x73, 0x0a, 0x09, 0x55, 0x6e, 0x6c, 0x69, 0x6b, 0x65, 0x52, 0x65,
	0x71, 0x12, 0x40, 0x0a, 0x09, 0x6c, 0x69, 0x6b, 0x65, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x18, 0x01,
	0x20, 0x01, 0x28, 0x0e, 0x32, 0x16, 0x2e, 0x76, 0x63, 0x2e, 0x73, 0x76, 0x63, 0x73, 0x63, 0x72,
	0x69, 0x70, 0x74, 0x2e, 0x4c, 0x69, 0x6b, 0x65, 0x54, 0x79, 0x70, 0x65, 0x42, 0x0b, 0xe0, 0x41,
	0x02, 0xfa, 0x42, 0x05, 0x82, 0x01, 0x02, 0x10, 0x01, 0x52, 0x08, 0x6c, 0x69, 0x6b, 0x65, 0x54,
	0x79, 0x70, 0x65, 0x12, 0x24, 0x0a, 0x09, 0x74, 0x61, 0x72, 0x67, 0x65, 0x74, 0x5f, 0x69, 0x64,
	0x18, 0x02, 0x20, 0x01, 0x28, 0x03, 0x42, 0x07, 0xfa, 0x42, 0x04, 0x22, 0x02, 0x20, 0x00, 0x52,
	0x08, 0x74, 0x61, 0x72, 0x67, 0x65, 0x74, 0x49, 0x64, 0x22, 0x32, 0x0a, 0x0a, 0x55, 0x6e, 0x6c,
	0x69, 0x6b, 0x65, 0x52, 0x65, 0x73, 0x70, 0x12, 0x12, 0x0a, 0x04, 0x63, 0x6f, 0x64, 0x65, 0x18,
	0x01, 0x20, 0x01, 0x28, 0x05, 0x52, 0x04, 0x63, 0x6f, 0x64, 0x65, 0x12, 0x10, 0x0a, 0x03, 0x6d,
	0x73, 0x67, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x03, 0x6d, 0x73, 0x67, 0x22, 0x80, 0x01,
	0x0a, 0x10, 0x42, 0x61, 0x74, 0x63, 0x68, 0x47, 0x65, 0x74, 0x4c, 0x69, 0x6b, 0x65, 0x73, 0x52,
	0x65, 0x71, 0x12, 0x40, 0x0a, 0x09, 0x6c, 0x69, 0x6b, 0x65, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x18,
	0x01, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x16, 0x2e, 0x76, 0x63, 0x2e, 0x73, 0x76, 0x63, 0x73, 0x63,
	0x72, 0x69, 0x70, 0x74, 0x2e, 0x4c, 0x69, 0x6b, 0x65, 0x54, 0x79, 0x70, 0x65, 0x42, 0x0b, 0xe0,
	0x41, 0x02, 0xfa, 0x42, 0x05, 0x82, 0x01, 0x02, 0x10, 0x01, 0x52, 0x08, 0x6c, 0x69, 0x6b, 0x65,
	0x54, 0x79, 0x70, 0x65, 0x12, 0x2a, 0x0a, 0x0a, 0x74, 0x61, 0x72, 0x67, 0x65, 0x74, 0x5f, 0x69,
	0x64, 0x73, 0x18, 0x02, 0x20, 0x03, 0x28, 0x03, 0x42, 0x0b, 0xe0, 0x41, 0x02, 0xfa, 0x42, 0x05,
	0x92, 0x01, 0x02, 0x08, 0x01, 0x52, 0x09, 0x74, 0x61, 0x72, 0x67, 0x65, 0x74, 0x49, 0x64, 0x73,
	0x22, 0xb1, 0x01, 0x0a, 0x11, 0x42, 0x61, 0x74, 0x63, 0x68, 0x47, 0x65, 0x74, 0x4c, 0x69, 0x6b,
	0x65, 0x73, 0x52, 0x65, 0x73, 0x70, 0x12, 0x12, 0x0a, 0x04, 0x63, 0x6f, 0x64, 0x65, 0x18, 0x01,
	0x20, 0x01, 0x28, 0x05, 0x52, 0x04, 0x63, 0x6f, 0x64, 0x65, 0x12, 0x10, 0x0a, 0x03, 0x6d, 0x73,
	0x67, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x03, 0x6d, 0x73, 0x67, 0x12, 0x3d, 0x0a, 0x04,
	0x64, 0x61, 0x74, 0x61, 0x18, 0x03, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x29, 0x2e, 0x76, 0x63, 0x2e,
	0x62, 0x69, 0x7a, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x2e, 0x42, 0x61, 0x74, 0x63, 0x68, 0x47,
	0x65, 0x74, 0x4c, 0x69, 0x6b, 0x65, 0x73, 0x52, 0x65, 0x73, 0x70, 0x2e, 0x44, 0x61, 0x74, 0x61,
	0x45, 0x6e, 0x74, 0x72, 0x79, 0x52, 0x04, 0x64, 0x61, 0x74, 0x61, 0x1a, 0x37, 0x0a, 0x09, 0x44,
	0x61, 0x74, 0x61, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x12, 0x10, 0x0a, 0x03, 0x6b, 0x65, 0x79, 0x18,
	0x01, 0x20, 0x01, 0x28, 0x03, 0x52, 0x03, 0x6b, 0x65, 0x79, 0x12, 0x14, 0x0a, 0x05, 0x76, 0x61,
	0x6c, 0x75, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x05, 0x52, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65,
	0x3a, 0x02, 0x38, 0x01, 0x22, 0x5d, 0x0a, 0x13, 0x47, 0x65, 0x74, 0x52, 0x65, 0x70, 0x6f, 0x72,
	0x74, 0x52, 0x65, 0x61, 0x73, 0x6f, 0x6e, 0x73, 0x52, 0x65, 0x71, 0x12, 0x46, 0x0a, 0x0b, 0x72,
	0x65, 0x70, 0x6f, 0x72, 0x74, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0e,
	0x32, 0x18, 0x2e, 0x76, 0x63, 0x2e, 0x73, 0x76, 0x63, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x2e,
	0x52, 0x65, 0x70, 0x6f, 0x72, 0x74, 0x54, 0x79, 0x70, 0x65, 0x42, 0x0b, 0xe0, 0x41, 0x02, 0xfa,
	0x42, 0x05, 0x82, 0x01, 0x02, 0x10, 0x01, 0x52, 0x0a, 0x72, 0x65, 0x70, 0x6f, 0x72, 0x74, 0x54,
	0x79, 0x70, 0x65, 0x22, 0x78, 0x0a, 0x14, 0x47, 0x65, 0x74, 0x52, 0x65, 0x70, 0x6f, 0x72, 0x74,
	0x52, 0x65, 0x61, 0x73, 0x6f, 0x6e, 0x73, 0x52, 0x65, 0x73, 0x70, 0x12, 0x12, 0x0a, 0x04, 0x63,
	0x6f, 0x64, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x05, 0x52, 0x04, 0x63, 0x6f, 0x64, 0x65, 0x12,
	0x10, 0x0a, 0x03, 0x6d, 0x73, 0x67, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x03, 0x6d, 0x73,
	0x67, 0x12, 0x3a, 0x0a, 0x04, 0x64, 0x61, 0x74, 0x61, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0b, 0x32,
	0x26, 0x2e, 0x76, 0x63, 0x2e, 0x73, 0x76, 0x63, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x2e, 0x47,
	0x65, 0x74, 0x52, 0x65, 0x70, 0x6f, 0x72, 0x74, 0x52, 0x65, 0x61, 0x73, 0x6f, 0x6e, 0x73, 0x52,
	0x65, 0x73, 0x70, 0x44, 0x61, 0x74, 0x61, 0x52, 0x04, 0x64, 0x61, 0x74, 0x61, 0x22, 0xcb, 0x01,
	0x0a, 0x09, 0x52, 0x65, 0x70, 0x6f, 0x72, 0x74, 0x52, 0x65, 0x71, 0x12, 0x46, 0x0a, 0x0b, 0x72,
	0x65, 0x70, 0x6f, 0x72, 0x74, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0e,
	0x32, 0x18, 0x2e, 0x76, 0x63, 0x2e, 0x73, 0x76, 0x63, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x2e,
	0x52, 0x65, 0x70, 0x6f, 0x72, 0x74, 0x54, 0x79, 0x70, 0x65, 0x42, 0x0b, 0xe0, 0x41, 0x02, 0xfa,
	0x42, 0x05, 0x82, 0x01, 0x02, 0x10, 0x01, 0x52, 0x0a, 0x72, 0x65, 0x70, 0x6f, 0x72, 0x74, 0x54,
	0x79, 0x70, 0x65, 0x12, 0x27, 0x0a, 0x09, 0x74, 0x61, 0x72, 0x67, 0x65, 0x74, 0x5f, 0x69, 0x64,
	0x18, 0x02, 0x20, 0x01, 0x28, 0x03, 0x42, 0x0a, 0xe0, 0x41, 0x02, 0xfa, 0x42, 0x04, 0x22, 0x02,
	0x20, 0x00, 0x52, 0x08, 0x74, 0x61, 0x72, 0x67, 0x65, 0x74, 0x49, 0x64, 0x12, 0x1b, 0x0a, 0x09,
	0x72, 0x65, 0x61, 0x73, 0x6f, 0x6e, 0x5f, 0x69, 0x64, 0x18, 0x03, 0x20, 0x01, 0x28, 0x05, 0x52,
	0x08, 0x72, 0x65, 0x61, 0x73, 0x6f, 0x6e, 0x49, 0x64, 0x12, 0x16, 0x0a, 0x06, 0x72, 0x65, 0x61,
	0x73, 0x6f, 0x6e, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x72, 0x65, 0x61, 0x73, 0x6f,
	0x6e, 0x12, 0x18, 0x0a, 0x07, 0x63, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x74, 0x18, 0x05, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x07, 0x63, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x74, 0x22, 0x32, 0x0a, 0x0a, 0x52,
	0x65, 0x70, 0x6f, 0x72, 0x74, 0x52, 0x65, 0x73, 0x70, 0x12, 0x12, 0x0a, 0x04, 0x63, 0x6f, 0x64,
	0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x05, 0x52, 0x04, 0x63, 0x6f, 0x64, 0x65, 0x12, 0x10, 0x0a,
	0x03, 0x6d, 0x73, 0x67, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x03, 0x6d, 0x73, 0x67, 0x22,
	0x3f, 0x0a, 0x0c, 0x47, 0x65, 0x74, 0x49, 0x50, 0x4c, 0x69, 0x73, 0x74, 0x52, 0x65, 0x71, 0x12,
	0x12, 0x0a, 0x04, 0x70, 0x61, 0x67, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x05, 0x52, 0x04, 0x70,
	0x61, 0x67, 0x65, 0x12, 0x1b, 0x0a, 0x09, 0x70, 0x61, 0x67, 0x65, 0x5f, 0x73, 0x69, 0x7a, 0x65,
	0x18, 0x02, 0x20, 0x01, 0x28, 0x05, 0x52, 0x08, 0x70, 0x61, 0x67, 0x65, 0x53, 0x69, 0x7a, 0x65,
	0x22, 0x6a, 0x0a, 0x0d, 0x47, 0x65, 0x74, 0x49, 0x50, 0x4c, 0x69, 0x73, 0x74, 0x52, 0x65, 0x73,
	0x70, 0x12, 0x12, 0x0a, 0x04, 0x63, 0x6f, 0x64, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x05, 0x52,
	0x04, 0x63, 0x6f, 0x64, 0x65, 0x12, 0x10, 0x0a, 0x03, 0x6d, 0x73, 0x67, 0x18, 0x02, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x03, 0x6d, 0x73, 0x67, 0x12, 0x33, 0x0a, 0x04, 0x64, 0x61, 0x74, 0x61, 0x18,
	0x03, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1f, 0x2e, 0x76, 0x63, 0x2e, 0x73, 0x76, 0x63, 0x73, 0x63,
	0x72, 0x69, 0x70, 0x74, 0x2e, 0x47, 0x65, 0x74, 0x49, 0x50, 0x4c, 0x69, 0x73, 0x74, 0x52, 0x65,
	0x73, 0x70, 0x44, 0x61, 0x74, 0x61, 0x52, 0x04, 0x64, 0x61, 0x74, 0x61, 0x22, 0x2a, 0x0a, 0x0f,
	0x47, 0x65, 0x74, 0x55, 0x73, 0x65, 0x72, 0x53, 0x74, 0x61, 0x74, 0x73, 0x52, 0x65, 0x71, 0x12,
	0x17, 0x0a, 0x07, 0x75, 0x73, 0x65, 0x72, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03,
	0x52, 0x06, 0x75, 0x73, 0x65, 0x72, 0x49, 0x64, 0x22, 0x70, 0x0a, 0x10, 0x47, 0x65, 0x74, 0x55,
	0x73, 0x65, 0x72, 0x53, 0x74, 0x61, 0x74, 0x73, 0x52, 0x65, 0x73, 0x70, 0x12, 0x12, 0x0a, 0x04,
	0x63, 0x6f, 0x64, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x05, 0x52, 0x04, 0x63, 0x6f, 0x64, 0x65,
	0x12, 0x10, 0x0a, 0x03, 0x6d, 0x73, 0x67, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x03, 0x6d,
	0x73, 0x67, 0x12, 0x36, 0x0a, 0x04, 0x64, 0x61, 0x74, 0x61, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0b,
	0x32, 0x22, 0x2e, 0x76, 0x63, 0x2e, 0x73, 0x76, 0x63, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x2e,
	0x47, 0x65, 0x74, 0x55, 0x73, 0x65, 0x72, 0x53, 0x74, 0x61, 0x74, 0x73, 0x52, 0x65, 0x73, 0x70,
	0x44, 0x61, 0x74, 0x61, 0x52, 0x04, 0x64, 0x61, 0x74, 0x61, 0x22, 0xb1, 0x01, 0x0a, 0x15, 0x47,
	0x65, 0x74, 0x55, 0x73, 0x65, 0x72, 0x53, 0x63, 0x72, 0x69, 0x70, 0x74, 0x4c, 0x69, 0x73, 0x74,
	0x73, 0x52, 0x65, 0x71, 0x12, 0x4e, 0x0a, 0x09, 0x6c, 0x69, 0x73, 0x74, 0x5f, 0x74, 0x79, 0x70,
	0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x2c, 0x2e, 0x76, 0x63, 0x2e, 0x73, 0x76, 0x63,
	0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x2e, 0x47, 0x65, 0x74, 0x55, 0x73, 0x65, 0x72, 0x53, 0x63,
	0x72, 0x69, 0x70, 0x74, 0x4c, 0x69, 0x73, 0x74, 0x73, 0x52, 0x65, 0x71, 0x2e, 0x4c, 0x69, 0x73,
	0x74, 0x54, 0x79, 0x70, 0x65, 0x42, 0x03, 0xe0, 0x41, 0x02, 0x52, 0x08, 0x6c, 0x69, 0x73, 0x74,
	0x54, 0x79, 0x70, 0x65, 0x12, 0x17, 0x0a, 0x07, 0x75, 0x73, 0x65, 0x72, 0x5f, 0x69, 0x64, 0x18,
	0x02, 0x20, 0x01, 0x28, 0x03, 0x52, 0x06, 0x75, 0x73, 0x65, 0x72, 0x49, 0x64, 0x12, 0x12, 0x0a,
	0x04, 0x70, 0x61, 0x67, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x05, 0x52, 0x04, 0x70, 0x61, 0x67,
	0x65, 0x12, 0x1b, 0x0a, 0x09, 0x70, 0x61, 0x67, 0x65, 0x5f, 0x73, 0x69, 0x7a, 0x65, 0x18, 0x04,
	0x20, 0x01, 0x28, 0x05, 0x52, 0x08, 0x70, 0x61, 0x67, 0x65, 0x53, 0x69, 0x7a, 0x65, 0x22, 0x7c,
	0x0a, 0x16, 0x47, 0x65, 0x74, 0x55, 0x73, 0x65, 0x72, 0x53, 0x63, 0x72, 0x69, 0x70, 0x74, 0x4c,
	0x69, 0x73, 0x74, 0x73, 0x52, 0x65, 0x73, 0x70, 0x12, 0x12, 0x0a, 0x04, 0x63, 0x6f, 0x64, 0x65,
	0x18, 0x01, 0x20, 0x01, 0x28, 0x05, 0x52, 0x04, 0x63, 0x6f, 0x64, 0x65, 0x12, 0x10, 0x0a, 0x03,
	0x6d, 0x73, 0x67, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x03, 0x6d, 0x73, 0x67, 0x12, 0x3c,
	0x0a, 0x04, 0x64, 0x61, 0x74, 0x61, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x28, 0x2e, 0x76,
	0x63, 0x2e, 0x73, 0x76, 0x63, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x2e, 0x47, 0x65, 0x74, 0x55,
	0x73, 0x65, 0x72, 0x53, 0x63, 0x72, 0x69, 0x70, 0x74, 0x4c, 0x69, 0x73, 0x74, 0x73, 0x52, 0x65,
	0x73, 0x70, 0x44, 0x61, 0x74, 0x61, 0x52, 0x04, 0x64, 0x61, 0x74, 0x61, 0x22, 0x6f, 0x0a, 0x0f,
	0x47, 0x65, 0x74, 0x43, 0x6f, 0x76, 0x65, 0x72, 0x4c, 0x69, 0x73, 0x74, 0x52, 0x65, 0x71, 0x12,
	0x12, 0x0a, 0x04, 0x70, 0x61, 0x67, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x05, 0x52, 0x04, 0x70,
	0x61, 0x67, 0x65, 0x12, 0x1b, 0x0a, 0x09, 0x70, 0x61, 0x67, 0x65, 0x5f, 0x73, 0x69, 0x7a, 0x65,
	0x18, 0x02, 0x20, 0x01, 0x28, 0x05, 0x52, 0x08, 0x70, 0x61, 0x67, 0x65, 0x53, 0x69, 0x7a, 0x65,
	0x12, 0x2b, 0x0a, 0x04, 0x74, 0x79, 0x70, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x17,
	0x2e, 0x76, 0x63, 0x2e, 0x73, 0x76, 0x63, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x2e, 0x43, 0x6f,
	0x76, 0x65, 0x72, 0x54, 0x79, 0x70, 0x65, 0x52, 0x04, 0x74, 0x79, 0x70, 0x65, 0x22, 0x70, 0x0a,
	0x10, 0x47, 0x65, 0x74, 0x43, 0x6f, 0x76, 0x65, 0x72, 0x4c, 0x69, 0x73, 0x74, 0x52, 0x65, 0x73,
	0x70, 0x12, 0x12, 0x0a, 0x04, 0x63, 0x6f, 0x64, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x05, 0x52,
	0x04, 0x63, 0x6f, 0x64, 0x65, 0x12, 0x10, 0x0a, 0x03, 0x6d, 0x73, 0x67, 0x18, 0x02, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x03, 0x6d, 0x73, 0x67, 0x12, 0x36, 0x0a, 0x04, 0x64, 0x61, 0x74, 0x61, 0x18,
	0x03, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x22, 0x2e, 0x76, 0x63, 0x2e, 0x73, 0x76, 0x63, 0x73, 0x63,
	0x72, 0x69, 0x70, 0x74, 0x2e, 0x47, 0x65, 0x74, 0x43, 0x6f, 0x76, 0x65, 0x72, 0x4c, 0x69, 0x73,
	0x74, 0x52, 0x65, 0x73, 0x70, 0x44, 0x61, 0x74, 0x61, 0x52, 0x04, 0x64, 0x61, 0x74, 0x61, 0x22,
	0x4a, 0x0a, 0x16, 0x47, 0x65, 0x6e, 0x65, 0x72, 0x61, 0x74, 0x65, 0x53, 0x63, 0x72, 0x69, 0x70,
	0x74, 0x4c, 0x69, 0x6e, 0x65, 0x73, 0x52, 0x65, 0x71, 0x12, 0x30, 0x0a, 0x0d, 0x63, 0x68, 0x61,
	0x72, 0x61, 0x63, 0x74, 0x65, 0x72, 0x5f, 0x69, 0x64, 0x73, 0x18, 0x01, 0x20, 0x03, 0x28, 0x03,
	0x42, 0x0b, 0xe0, 0x41, 0x02, 0xfa, 0x42, 0x05, 0x92, 0x01, 0x02, 0x08, 0x01, 0x52, 0x0c, 0x63,
	0x68, 0x61, 0x72, 0x61, 0x63, 0x74, 0x65, 0x72, 0x49, 0x64, 0x73, 0x22, 0x7e, 0x0a, 0x17, 0x47,
	0x65, 0x6e, 0x65, 0x72, 0x61, 0x74, 0x65, 0x53, 0x63, 0x72, 0x69, 0x70, 0x74, 0x4c, 0x69, 0x6e,
	0x65, 0x73, 0x52, 0x65, 0x73, 0x70, 0x12, 0x12, 0x0a, 0x04, 0x63, 0x6f, 0x64, 0x65, 0x18, 0x01,
	0x20, 0x01, 0x28, 0x05, 0x52, 0x04, 0x63, 0x6f, 0x64, 0x65, 0x12, 0x10, 0x0a, 0x03, 0x6d, 0x73,
	0x67, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x03, 0x6d, 0x73, 0x67, 0x12, 0x3d, 0x0a, 0x04,
	0x64, 0x61, 0x74, 0x61, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x29, 0x2e, 0x76, 0x63, 0x2e,
	0x62, 0x69, 0x7a, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x2e, 0x47, 0x65, 0x6e, 0x65, 0x72, 0x61,
	0x74, 0x65, 0x53, 0x63, 0x72, 0x69, 0x70, 0x74, 0x4c, 0x69, 0x6e, 0x65, 0x73, 0x52, 0x65, 0x73,
	0x70, 0x44, 0x61, 0x74, 0x61, 0x52, 0x04, 0x64, 0x61, 0x74, 0x61, 0x22, 0xfa, 0x01, 0x0a, 0x1b,
	0x47, 0x65, 0x6e, 0x65, 0x72, 0x61, 0x74, 0x65, 0x53, 0x63, 0x72, 0x69, 0x70, 0x74, 0x4c, 0x69,
	0x6e, 0x65, 0x73, 0x52, 0x65, 0x73, 0x70, 0x44, 0x61, 0x74, 0x61, 0x12, 0x28, 0x0a, 0x05, 0x6c,
	0x69, 0x6e, 0x65, 0x73, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x12, 0x2e, 0x76, 0x63, 0x2e,
	0x73, 0x76, 0x63, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x2e, 0x4c, 0x69, 0x6e, 0x65, 0x52, 0x05,
	0x6c, 0x69, 0x6e, 0x65, 0x73, 0x12, 0x59, 0x0a, 0x0a, 0x63, 0x68, 0x61, 0x72, 0x61, 0x63, 0x74,
	0x65, 0x72, 0x73, 0x18, 0x02, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x39, 0x2e, 0x76, 0x63, 0x2e, 0x62,
	0x69, 0x7a, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x2e, 0x47, 0x65, 0x6e, 0x65, 0x72, 0x61, 0x74,
	0x65, 0x53, 0x63, 0x72, 0x69, 0x70, 0x74, 0x4c, 0x69, 0x6e, 0x65, 0x73, 0x52, 0x65, 0x73, 0x70,
	0x44, 0x61, 0x74, 0x61, 0x2e, 0x43, 0x68, 0x61, 0x72, 0x61, 0x63, 0x74, 0x65, 0x72, 0x73, 0x45,
	0x6e, 0x74, 0x72, 0x79, 0x52, 0x0a, 0x63, 0x68, 0x61, 0x72, 0x61, 0x63, 0x74, 0x65, 0x72, 0x73,
	0x1a, 0x56, 0x0a, 0x0f, 0x43, 0x68, 0x61, 0x72, 0x61, 0x63, 0x74, 0x65, 0x72, 0x73, 0x45, 0x6e,
	0x74, 0x72, 0x79, 0x12, 0x10, 0x0a, 0x03, 0x6b, 0x65, 0x79, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03,
	0x52, 0x03, 0x6b, 0x65, 0x79, 0x12, 0x2d, 0x0a, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x18, 0x02,
	0x20, 0x01, 0x28, 0x0b, 0x32, 0x17, 0x2e, 0x76, 0x63, 0x2e, 0x73, 0x76, 0x63, 0x73, 0x63, 0x72,
	0x69, 0x70, 0x74, 0x2e, 0x43, 0x68, 0x61, 0x72, 0x61, 0x63, 0x74, 0x65, 0x72, 0x52, 0x05, 0x76,
	0x61, 0x6c, 0x75, 0x65, 0x3a, 0x02, 0x38, 0x01, 0x22, 0x3d, 0x0a, 0x10, 0x44, 0x65, 0x6c, 0x65,
	0x74, 0x65, 0x44, 0x75, 0x62, 0x62, 0x69, 0x6e, 0x67, 0x52, 0x65, 0x71, 0x12, 0x29, 0x0a, 0x0a,
	0x64, 0x75, 0x62, 0x62, 0x69, 0x6e, 0x67, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03,
	0x42, 0x0a, 0xe0, 0x41, 0x02, 0xfa, 0x42, 0x04, 0x22, 0x02, 0x20, 0x00, 0x52, 0x09, 0x64, 0x75,
	0x62, 0x62, 0x69, 0x6e, 0x67, 0x49, 0x64, 0x22, 0x39, 0x0a, 0x11, 0x44, 0x65, 0x6c, 0x65, 0x74,
	0x65, 0x44, 0x75, 0x62, 0x62, 0x69, 0x6e, 0x67, 0x52, 0x65, 0x73, 0x70, 0x12, 0x12, 0x0a, 0x04,
	0x63, 0x6f, 0x64, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x05, 0x52, 0x04, 0x63, 0x6f, 0x64, 0x65,
	0x12, 0x10, 0x0a, 0x03, 0x6d, 0x73, 0x67, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x03, 0x6d,
	0x73, 0x67, 0x22, 0x54, 0x0a, 0x10, 0x53, 0x65, 0x74, 0x44, 0x75, 0x62, 0x62, 0x69, 0x6e, 0x67,
	0x54, 0x6f, 0x70, 0x52, 0x65, 0x71, 0x12, 0x29, 0x0a, 0x0a, 0x64, 0x75, 0x62, 0x62, 0x69, 0x6e,
	0x67, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x42, 0x0a, 0xe0, 0x41, 0x02, 0xfa,
	0x42, 0x04, 0x22, 0x02, 0x20, 0x00, 0x52, 0x09, 0x64, 0x75, 0x62, 0x62, 0x69, 0x6e, 0x67, 0x49,
	0x64, 0x12, 0x15, 0x0a, 0x06, 0x69, 0x73, 0x5f, 0x74, 0x6f, 0x70, 0x18, 0x02, 0x20, 0x01, 0x28,
	0x08, 0x52, 0x05, 0x69, 0x73, 0x54, 0x6f, 0x70, 0x22, 0x39, 0x0a, 0x11, 0x53, 0x65, 0x74, 0x44,
	0x75, 0x62, 0x62, 0x69, 0x6e, 0x67, 0x54, 0x6f, 0x70, 0x52, 0x65, 0x73, 0x70, 0x12, 0x12, 0x0a,
	0x04, 0x63, 0x6f, 0x64, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x05, 0x52, 0x04, 0x63, 0x6f, 0x64,
	0x65, 0x12, 0x10, 0x0a, 0x03, 0x6d, 0x73, 0x67, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x03,
	0x6d, 0x73, 0x67, 0x32, 0x97, 0x25, 0x0a, 0x01, 0x73, 0x12, 0x87, 0x01, 0x0a, 0x10, 0x47, 0x65,
	0x74, 0x43, 0x68, 0x61, 0x72, 0x61, 0x63, 0x74, 0x65, 0x72, 0x4c, 0x69, 0x73, 0x74, 0x12, 0x21,
	0x2e, 0x76, 0x63, 0x2e, 0x62, 0x69, 0x7a, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x2e, 0x47, 0x65,
	0x74, 0x43, 0x68, 0x61, 0x72, 0x61, 0x63, 0x74, 0x65, 0x72, 0x4c, 0x69, 0x73, 0x74, 0x52, 0x65,
	0x71, 0x1a, 0x22, 0x2e, 0x76, 0x63, 0x2e, 0x62, 0x69, 0x7a, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74,
	0x2e, 0x47, 0x65, 0x74, 0x43, 0x68, 0x61, 0x72, 0x61, 0x63, 0x74, 0x65, 0x72, 0x4c, 0x69, 0x73,
	0x74, 0x52, 0x65, 0x73, 0x70, 0x22, 0x2c, 0x82, 0xd3, 0xe4, 0x93, 0x02, 0x26, 0x3a, 0x01, 0x2a,
	0x22, 0x21, 0x2f, 0x76, 0x63, 0x2e, 0x62, 0x69, 0x7a, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x2e,
	0x73, 0x2f, 0x76, 0x31, 0x2f, 0x63, 0x68, 0x61, 0x72, 0x61, 0x63, 0x74, 0x65, 0x72, 0x2f, 0x6c,
	0x69, 0x73, 0x74, 0x12, 0x89, 0x01, 0x0a, 0x10, 0x53, 0x65, 0x61, 0x72, 0x63, 0x68, 0x43, 0x68,
	0x61, 0x72, 0x61, 0x63, 0x74, 0x65, 0x72, 0x73, 0x12, 0x21, 0x2e, 0x76, 0x63, 0x2e, 0x62, 0x69,
	0x7a, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x2e, 0x53, 0x65, 0x61, 0x72, 0x63, 0x68, 0x43, 0x68,
	0x61, 0x72, 0x61, 0x63, 0x74, 0x65, 0x72, 0x73, 0x52, 0x65, 0x71, 0x1a, 0x22, 0x2e, 0x76, 0x63,
	0x2e, 0x62, 0x69, 0x7a, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x2e, 0x53, 0x65, 0x61, 0x72, 0x63,
	0x68, 0x43, 0x68, 0x61, 0x72, 0x61, 0x63, 0x74, 0x65, 0x72, 0x73, 0x52, 0x65, 0x73, 0x70, 0x22,
	0x2e, 0x82, 0xd3, 0xe4, 0x93, 0x02, 0x28, 0x3a, 0x01, 0x2a, 0x22, 0x23, 0x2f, 0x76, 0x63, 0x2e,
	0x62, 0x69, 0x7a, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x2e, 0x73, 0x2f, 0x76, 0x31, 0x2f, 0x63,
	0x68, 0x61, 0x72, 0x61, 0x63, 0x74, 0x65, 0x72, 0x2f, 0x73, 0x65, 0x61, 0x72, 0x63, 0x68, 0x12,
	0x86, 0x01, 0x0a, 0x10, 0x53, 0x65, 0x74, 0x55, 0x73, 0x65, 0x72, 0x43, 0x68, 0x61, 0x72, 0x61,
	0x63, 0x74, 0x65, 0x72, 0x12, 0x21, 0x2e, 0x76, 0x63, 0x2e, 0x62, 0x69, 0x7a, 0x73, 0x63, 0x72,
	0x69, 0x70, 0x74, 0x2e, 0x53, 0x65, 0x74, 0x55, 0x73, 0x65, 0x72, 0x43, 0x68, 0x61, 0x72, 0x61,
	0x63, 0x74, 0x65, 0x72, 0x52, 0x65, 0x71, 0x1a, 0x22, 0x2e, 0x76, 0x63, 0x2e, 0x62, 0x69, 0x7a,
	0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x2e, 0x53, 0x65, 0x74, 0x55, 0x73, 0x65, 0x72, 0x43, 0x68,
	0x61, 0x72, 0x61, 0x63, 0x74, 0x65, 0x72, 0x52, 0x65, 0x73, 0x70, 0x22, 0x2b, 0x82, 0xd3, 0xe4,
	0x93, 0x02, 0x25, 0x3a, 0x01, 0x2a, 0x22, 0x20, 0x2f, 0x76, 0x63, 0x2e, 0x62, 0x69, 0x7a, 0x73,
	0x63, 0x72, 0x69, 0x70, 0x74, 0x2e, 0x73, 0x2f, 0x76, 0x31, 0x2f, 0x63, 0x68, 0x61, 0x72, 0x61,
	0x63, 0x74, 0x65, 0x72, 0x2f, 0x73, 0x65, 0x74, 0x12, 0x8a, 0x01, 0x0a, 0x11, 0x47, 0x65, 0x74,
	0x55, 0x73, 0x65, 0x72, 0x43, 0x68, 0x61, 0x72, 0x61, 0x63, 0x74, 0x65, 0x72, 0x73, 0x12, 0x22,
	0x2e, 0x76, 0x63, 0x2e, 0x62, 0x69, 0x7a, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x2e, 0x47, 0x65,
	0x74, 0x55, 0x73, 0x65, 0x72, 0x43, 0x68, 0x61, 0x72, 0x61, 0x63, 0x74, 0x65, 0x72, 0x73, 0x52,
	0x65, 0x71, 0x1a, 0x23, 0x2e, 0x76, 0x63, 0x2e, 0x62, 0x69, 0x7a, 0x73, 0x63, 0x72, 0x69, 0x70,
	0x74, 0x2e, 0x47, 0x65, 0x74, 0x55, 0x73, 0x65, 0x72, 0x43, 0x68, 0x61, 0x72, 0x61, 0x63, 0x74,
	0x65, 0x72, 0x73, 0x52, 0x65, 0x73, 0x70, 0x22, 0x2c, 0x82, 0xd3, 0xe4, 0x93, 0x02, 0x26, 0x3a,
	0x01, 0x2a, 0x22, 0x21, 0x2f, 0x76, 0x63, 0x2e, 0x62, 0x69, 0x7a, 0x73, 0x63, 0x72, 0x69, 0x70,
	0x74, 0x2e, 0x73, 0x2f, 0x76, 0x31, 0x2f, 0x63, 0x68, 0x61, 0x72, 0x61, 0x63, 0x74, 0x65, 0x72,
	0x2f, 0x75, 0x73, 0x65, 0x72, 0x12, 0xc8, 0x01, 0x0a, 0x1f, 0x42, 0x61, 0x74, 0x63, 0x68, 0x43,
	0x68, 0x65, 0x63, 0x6b, 0x43, 0x68, 0x61, 0x72, 0x61, 0x63, 0x74, 0x65, 0x72, 0x41, 0x76, 0x61,
	0x69, 0x6c, 0x61, 0x62, 0x69, 0x6c, 0x69, 0x74, 0x79, 0x12, 0x30, 0x2e, 0x76, 0x63, 0x2e, 0x62,
	0x69, 0x7a, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x2e, 0x42, 0x61, 0x74, 0x63, 0x68, 0x43, 0x68,
	0x65, 0x63, 0x6b, 0x43, 0x68, 0x61, 0x72, 0x61, 0x63, 0x74, 0x65, 0x72, 0x41, 0x76, 0x61, 0x69,
	0x6c, 0x61, 0x62, 0x69, 0x6c, 0x69, 0x74, 0x79, 0x52, 0x65, 0x71, 0x1a, 0x31, 0x2e, 0x76, 0x63,
	0x2e, 0x62, 0x69, 0x7a, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x2e, 0x42, 0x61, 0x74, 0x63, 0x68,
	0x43, 0x68, 0x65, 0x63, 0x6b, 0x43, 0x68, 0x61, 0x72, 0x61, 0x63, 0x74, 0x65, 0x72, 0x41, 0x76,
	0x61, 0x69, 0x6c, 0x61, 0x62, 0x69, 0x6c, 0x69, 0x74, 0x79, 0x52, 0x65, 0x73, 0x70, 0x22, 0x40,
	0x82, 0xd3, 0xe4, 0x93, 0x02, 0x3a, 0x3a, 0x01, 0x2a, 0x22, 0x35, 0x2f, 0x76, 0x63, 0x2e, 0x62,
	0x69, 0x7a, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x2e, 0x73, 0x2f, 0x76, 0x31, 0x2f, 0x63, 0x68,
	0x61, 0x72, 0x61, 0x63, 0x74, 0x65, 0x72, 0x2f, 0x62, 0x61, 0x74, 0x63, 0x68, 0x5f, 0x63, 0x68,
	0x65, 0x63, 0x6b, 0x5f, 0x61, 0x76, 0x61, 0x69, 0x6c, 0x61, 0x62, 0x69, 0x6c, 0x69, 0x74, 0x79,
	0x12, 0x77, 0x0a, 0x0c, 0x47, 0x65, 0x74, 0x54, 0x6f, 0x70, 0x69, 0x63, 0x4c, 0x69, 0x73, 0x74,
	0x12, 0x1d, 0x2e, 0x76, 0x63, 0x2e, 0x62, 0x69, 0x7a, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x2e,
	0x47, 0x65, 0x74, 0x54, 0x6f, 0x70, 0x69, 0x63, 0x4c, 0x69, 0x73, 0x74, 0x52, 0x65, 0x71, 0x1a,
	0x1e, 0x2e, 0x76, 0x63, 0x2e, 0x62, 0x69, 0x7a, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x2e, 0x47,
	0x65, 0x74, 0x54, 0x6f, 0x70, 0x69, 0x63, 0x4c, 0x69, 0x73, 0x74, 0x52, 0x65, 0x73, 0x70, 0x22,
	0x28, 0x82, 0xd3, 0xe4, 0x93, 0x02, 0x22, 0x3a, 0x01, 0x2a, 0x22, 0x1d, 0x2f, 0x76, 0x63, 0x2e,
	0x62, 0x69, 0x7a, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x2e, 0x73, 0x2f, 0x76, 0x31, 0x2f, 0x74,
	0x6f, 0x70, 0x69, 0x63, 0x2f, 0x6c, 0x69, 0x73, 0x74, 0x12, 0x7a, 0x0a, 0x0c, 0x43, 0x72, 0x65,
	0x61, 0x74, 0x65, 0x53, 0x63, 0x72, 0x69, 0x70, 0x74, 0x12, 0x1d, 0x2e, 0x76, 0x63, 0x2e, 0x62,
	0x69, 0x7a, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x2e, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x53,
	0x63, 0x72, 0x69, 0x70, 0x74, 0x52, 0x65, 0x71, 0x1a, 0x1e, 0x2e, 0x76, 0x63, 0x2e, 0x62, 0x69,
	0x7a, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x2e, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x53, 0x63,
	0x72, 0x69, 0x70, 0x74, 0x52, 0x65, 0x73, 0x70, 0x22, 0x2b, 0x82, 0xd3, 0xe4, 0x93, 0x02, 0x25,
	0x3a, 0x01, 0x2a, 0x22, 0x20, 0x2f, 0x76, 0x63, 0x2e, 0x62, 0x69, 0x7a, 0x73, 0x63, 0x72, 0x69,
	0x70, 0x74, 0x2e, 0x73, 0x2f, 0x76, 0x31, 0x2f, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x2f, 0x63,
	0x72, 0x65, 0x61, 0x74, 0x65, 0x12, 0x7a, 0x0a, 0x0c, 0x44, 0x65, 0x6c, 0x65, 0x74, 0x65, 0x53,
	0x63, 0x72, 0x69, 0x70, 0x74, 0x12, 0x1d, 0x2e, 0x76, 0x63, 0x2e, 0x62, 0x69, 0x7a, 0x73, 0x63,
	0x72, 0x69, 0x70, 0x74, 0x2e, 0x44, 0x65, 0x6c, 0x65, 0x74, 0x65, 0x53, 0x63, 0x72, 0x69, 0x70,
	0x74, 0x52, 0x65, 0x71, 0x1a, 0x1e, 0x2e, 0x76, 0x63, 0x2e, 0x62, 0x69, 0x7a, 0x73, 0x63, 0x72,
	0x69, 0x70, 0x74, 0x2e, 0x44, 0x65, 0x6c, 0x65, 0x74, 0x65, 0x53, 0x63, 0x72, 0x69, 0x70, 0x74,
	0x52, 0x65, 0x73, 0x70, 0x22, 0x2b, 0x82, 0xd3, 0xe4, 0x93, 0x02, 0x25, 0x3a, 0x01, 0x2a, 0x22,
	0x20, 0x2f, 0x76, 0x63, 0x2e, 0x62, 0x69, 0x7a, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x2e, 0x73,
	0x2f, 0x76, 0x31, 0x2f, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x2f, 0x64, 0x65, 0x6c, 0x65, 0x74,
	0x65, 0x12, 0x7b, 0x0a, 0x0d, 0x47, 0x65, 0x74, 0x53, 0x63, 0x72, 0x69, 0x70, 0x74, 0x4c, 0x69,
	0x73, 0x74, 0x12, 0x1e, 0x2e, 0x76, 0x63, 0x2e, 0x62, 0x69, 0x7a, 0x73, 0x63, 0x72, 0x69, 0x70,
	0x74, 0x2e, 0x47, 0x65, 0x74, 0x53, 0x63, 0x72, 0x69, 0x70, 0x74, 0x4c, 0x69, 0x73, 0x74, 0x52,
	0x65, 0x71, 0x1a, 0x1f, 0x2e, 0x76, 0x63, 0x2e, 0x62, 0x69, 0x7a, 0x73, 0x63, 0x72, 0x69, 0x70,
	0x74, 0x2e, 0x47, 0x65, 0x74, 0x53, 0x63, 0x72, 0x69, 0x70, 0x74, 0x4c, 0x69, 0x73, 0x74, 0x52,
	0x65, 0x73, 0x70, 0x22, 0x29, 0x82, 0xd3, 0xe4, 0x93, 0x02, 0x23, 0x3a, 0x01, 0x2a, 0x22, 0x1e,
	0x2f, 0x76, 0x63, 0x2e, 0x62, 0x69, 0x7a, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x2e, 0x73, 0x2f,
	0x76, 0x31, 0x2f, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x2f, 0x6c, 0x69, 0x73, 0x74, 0x12, 0x83,
	0x01, 0x0a, 0x0f, 0x47, 0x65, 0x74, 0x53, 0x63, 0x72, 0x69, 0x70, 0x74, 0x44, 0x65, 0x74, 0x61,
	0x69, 0x6c, 0x12, 0x20, 0x2e, 0x76, 0x63, 0x2e, 0x62, 0x69, 0x7a, 0x73, 0x63, 0x72, 0x69, 0x70,
	0x74, 0x2e, 0x47, 0x65, 0x74, 0x53, 0x63, 0x72, 0x69, 0x70, 0x74, 0x44, 0x65, 0x74, 0x61, 0x69,
	0x6c, 0x52, 0x65, 0x71, 0x1a, 0x21, 0x2e, 0x76, 0x63, 0x2e, 0x62, 0x69, 0x7a, 0x73, 0x63, 0x72,
	0x69, 0x70, 0x74, 0x2e, 0x47, 0x65, 0x74, 0x53, 0x63, 0x72, 0x69, 0x70, 0x74, 0x44, 0x65, 0x74,
	0x61, 0x69, 0x6c, 0x52, 0x65, 0x73, 0x70, 0x22, 0x2b, 0x82, 0xd3, 0xe4, 0x93, 0x02, 0x25, 0x3a,
	0x01, 0x2a, 0x22, 0x20, 0x2f, 0x76, 0x63, 0x2e, 0x62, 0x69, 0x7a, 0x73, 0x63, 0x72, 0x69, 0x70,
	0x74, 0x2e, 0x73, 0x2f, 0x76, 0x31, 0x2f, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x2f, 0x64, 0x65,
	0x74, 0x61, 0x69, 0x6c, 0x12, 0x68, 0x0a, 0x06, 0x53, 0x65, 0x61, 0x72, 0x63, 0x68, 0x12, 0x17,
	0x2e, 0x76, 0x63, 0x2e, 0x62, 0x69, 0x7a, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x2e, 0x53, 0x65,
	0x61, 0x72, 0x63, 0x68, 0x52, 0x65, 0x71, 0x1a, 0x18, 0x2e, 0x76, 0x63, 0x2e, 0x62, 0x69, 0x7a,
	0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x2e, 0x53, 0x65, 0x61, 0x72, 0x63, 0x68, 0x52, 0x65, 0x73,
	0x70, 0x22, 0x2b, 0x82, 0xd3, 0xe4, 0x93, 0x02, 0x25, 0x3a, 0x01, 0x2a, 0x22, 0x20, 0x2f, 0x76,
	0x63, 0x2e, 0x62, 0x69, 0x7a, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x2e, 0x73, 0x2f, 0x76, 0x31,
	0x2f, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x2f, 0x73, 0x65, 0x61, 0x72, 0x63, 0x68, 0x12, 0x76,
	0x0a, 0x0b, 0x53, 0x68, 0x61, 0x72, 0x65, 0x53, 0x63, 0x72, 0x69, 0x70, 0x74, 0x12, 0x1c, 0x2e,
	0x76, 0x63, 0x2e, 0x62, 0x69, 0x7a, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x2e, 0x53, 0x68, 0x61,
	0x72, 0x65, 0x53, 0x63, 0x72, 0x69, 0x70, 0x74, 0x52, 0x65, 0x71, 0x1a, 0x1d, 0x2e, 0x76, 0x63,
	0x2e, 0x62, 0x69, 0x7a, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x2e, 0x53, 0x68, 0x61, 0x72, 0x65,
	0x53, 0x63, 0x72, 0x69, 0x70, 0x74, 0x52, 0x65, 0x73, 0x70, 0x22, 0x2a, 0x82, 0xd3, 0xe4, 0x93,
	0x02, 0x24, 0x3a, 0x01, 0x2a, 0x22, 0x1f, 0x2f, 0x76, 0x63, 0x2e, 0x62, 0x69, 0x7a, 0x73, 0x63,
	0x72, 0x69, 0x70, 0x74, 0x2e, 0x73, 0x2f, 0x76, 0x31, 0x2f, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74,
	0x2f, 0x73, 0x68, 0x61, 0x72, 0x65, 0x12, 0x87, 0x01, 0x0a, 0x11, 0x47, 0x65, 0x74, 0x4c, 0x69,
	0x6e, 0x65, 0x44, 0x75, 0x62, 0x62, 0x69, 0x6e, 0x67, 0x41, 0x6c, 0x6c, 0x12, 0x22, 0x2e, 0x76,
	0x63, 0x2e, 0x62, 0x69, 0x7a, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x2e, 0x47, 0x65, 0x74, 0x4c,
	0x69, 0x6e, 0x65, 0x44, 0x75, 0x62, 0x62, 0x69, 0x6e, 0x67, 0x41, 0x6c, 0x6c, 0x52, 0x65, 0x71,
	0x1a, 0x23, 0x2e, 0x76, 0x63, 0x2e, 0x62, 0x69, 0x7a, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x2e,
	0x47, 0x65, 0x74, 0x4c, 0x69, 0x6e, 0x65, 0x44, 0x75, 0x62, 0x62, 0x69, 0x6e, 0x67, 0x41, 0x6c,
	0x6c, 0x52, 0x65, 0x73, 0x70, 0x22, 0x29, 0x82, 0xd3, 0xe4, 0x93, 0x02, 0x23, 0x3a, 0x01, 0x2a,
	0x22, 0x1e, 0x2f, 0x76, 0x63, 0x2e, 0x62, 0x69, 0x7a, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x2e,
	0x73, 0x2f, 0x76, 0x31, 0x2f, 0x64, 0x75, 0x62, 0x62, 0x69, 0x6e, 0x67, 0x2f, 0x61, 0x6c, 0x6c,
	0x12, 0x93, 0x01, 0x0a, 0x14, 0x47, 0x65, 0x74, 0x4c, 0x69, 0x6e, 0x65, 0x44, 0x75, 0x62, 0x62,
	0x69, 0x6e, 0x67, 0x53, 0x69, 0x6d, 0x70, 0x6c, 0x65, 0x12, 0x25, 0x2e, 0x76, 0x63, 0x2e, 0x62,
	0x69, 0x7a, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x2e, 0x47, 0x65, 0x74, 0x4c, 0x69, 0x6e, 0x65,
	0x44, 0x75, 0x62, 0x62, 0x69, 0x6e, 0x67, 0x53, 0x69, 0x6d, 0x70, 0x6c, 0x65, 0x52, 0x65, 0x71,
	0x1a, 0x26, 0x2e, 0x76, 0x63, 0x2e, 0x62, 0x69, 0x7a, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x2e,
	0x47, 0x65, 0x74, 0x4c, 0x69, 0x6e, 0x65, 0x44, 0x75, 0x62, 0x62, 0x69, 0x6e, 0x67, 0x53, 0x69,
	0x6d, 0x70, 0x6c, 0x65, 0x52, 0x65, 0x73, 0x70, 0x22, 0x2c, 0x82, 0xd3, 0xe4, 0x93, 0x02, 0x26,
	0x3a, 0x01, 0x2a, 0x22, 0x21, 0x2f, 0x76, 0x63, 0x2e, 0x62, 0x69, 0x7a, 0x73, 0x63, 0x72, 0x69,
	0x70, 0x74, 0x2e, 0x73, 0x2f, 0x76, 0x31, 0x2f, 0x64, 0x75, 0x62, 0x62, 0x69, 0x6e, 0x67, 0x2f,
	0x73, 0x69, 0x6d, 0x70, 0x6c, 0x65, 0x12, 0x99, 0x01, 0x0a, 0x15, 0x47, 0x65, 0x74, 0x53, 0x63,
	0x72, 0x69, 0x70, 0x74, 0x46, 0x69, 0x72, 0x73, 0x74, 0x44, 0x75, 0x62, 0x62, 0x69, 0x6e, 0x67,
	0x12, 0x26, 0x2e, 0x76, 0x63, 0x2e, 0x62, 0x69, 0x7a, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x2e,
	0x47, 0x65, 0x74, 0x53, 0x63, 0x72, 0x69, 0x70, 0x74, 0x46, 0x69, 0x72, 0x73, 0x74, 0x44, 0x75,
	0x62, 0x62, 0x69, 0x6e, 0x67, 0x52, 0x65, 0x71, 0x1a, 0x27, 0x2e, 0x76, 0x63, 0x2e, 0x62, 0x69,
	0x7a, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x2e, 0x47, 0x65, 0x74, 0x53, 0x63, 0x72, 0x69, 0x70,
	0x74, 0x46, 0x69, 0x72, 0x73, 0x74, 0x44, 0x75, 0x62, 0x62, 0x69, 0x6e, 0x67, 0x52, 0x65, 0x73,
	0x70, 0x22, 0x2f, 0x82, 0xd3, 0xe4, 0x93, 0x02, 0x29, 0x3a, 0x01, 0x2a, 0x22, 0x24, 0x2f, 0x76,
	0x63, 0x2e, 0x62, 0x69, 0x7a, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x2e, 0x73, 0x2f, 0x76, 0x31,
	0x2f, 0x64, 0x75, 0x62, 0x62, 0x69, 0x6e, 0x67, 0x2f, 0x61, 0x6c, 0x6c, 0x5f, 0x66, 0x69, 0x72,
	0x73, 0x74, 0x12, 0xae, 0x01, 0x0a, 0x1a, 0x42, 0x61, 0x74, 0x63, 0x68, 0x47, 0x65, 0x74, 0x53,
	0x63, 0x72, 0x69, 0x70, 0x74, 0x46, 0x69, 0x72, 0x73, 0x74, 0x44, 0x75, 0x62, 0x62, 0x69, 0x6e,
	0x67, 0x12, 0x2b, 0x2e, 0x76, 0x63, 0x2e, 0x62, 0x69, 0x7a, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74,
	0x2e, 0x42, 0x61, 0x74, 0x63, 0x68, 0x47, 0x65, 0x74, 0x53, 0x63, 0x72, 0x69, 0x70, 0x74, 0x46,
	0x69, 0x72, 0x73, 0x74, 0x44, 0x75, 0x62, 0x62, 0x69, 0x6e, 0x67, 0x52, 0x65, 0x71, 0x1a, 0x2c,
	0x2e, 0x76, 0x63, 0x2e, 0x62, 0x69, 0x7a, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x2e, 0x42, 0x61,
	0x74, 0x63, 0x68, 0x47, 0x65, 0x74, 0x53, 0x63, 0x72, 0x69, 0x70, 0x74, 0x46, 0x69, 0x72, 0x73,
	0x74, 0x44, 0x75, 0x62, 0x62, 0x69, 0x6e, 0x67, 0x52, 0x65, 0x73, 0x70, 0x22, 0x35, 0x82, 0xd3,
	0xe4, 0x93, 0x02, 0x2f, 0x3a, 0x01, 0x2a, 0x22, 0x2a, 0x2f, 0x76, 0x63, 0x2e, 0x62, 0x69, 0x7a,
	0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x2e, 0x73, 0x2f, 0x76, 0x31, 0x2f, 0x64, 0x75, 0x62, 0x62,
	0x69, 0x6e, 0x67, 0x2f, 0x62, 0x61, 0x74, 0x63, 0x68, 0x5f, 0x61, 0x6c, 0x6c, 0x5f, 0x66, 0x69,
	0x72, 0x73, 0x74, 0x12, 0x7e, 0x0a, 0x0d, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x44, 0x75, 0x62,
	0x62, 0x69, 0x6e, 0x67, 0x12, 0x1e, 0x2e, 0x76, 0x63, 0x2e, 0x62, 0x69, 0x7a, 0x73, 0x63, 0x72,
	0x69, 0x70, 0x74, 0x2e, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x44, 0x75, 0x62, 0x62, 0x69, 0x6e,
	0x67, 0x52, 0x65, 0x71, 0x1a, 0x1f, 0x2e, 0x76, 0x63, 0x2e, 0x62, 0x69, 0x7a, 0x73, 0x63, 0x72,
	0x69, 0x70, 0x74, 0x2e, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x44, 0x75, 0x62, 0x62, 0x69, 0x6e,
	0x67, 0x52, 0x65, 0x73, 0x70, 0x22, 0x2c, 0x82, 0xd3, 0xe4, 0x93, 0x02, 0x26, 0x3a, 0x01, 0x2a,
	0x22, 0x21, 0x2f, 0x76, 0x63, 0x2e, 0x62, 0x69, 0x7a, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x2e,
	0x73, 0x2f, 0x76, 0x31, 0x2f, 0x64, 0x75, 0x62, 0x62, 0x69, 0x6e, 0x67, 0x2f, 0x63, 0x72, 0x65,
	0x61, 0x74, 0x65, 0x12, 0x97, 0x01, 0x0a, 0x13, 0x44, 0x65, 0x6c, 0x65, 0x74, 0x65, 0x44, 0x75,
	0x62, 0x62, 0x69, 0x6e, 0x67, 0x52, 0x65, 0x63, 0x6f, 0x72, 0x64, 0x12, 0x24, 0x2e, 0x76, 0x63,
	0x2e, 0x62, 0x69, 0x7a, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x2e, 0x44, 0x65, 0x6c, 0x65, 0x74,
	0x65, 0x44, 0x75, 0x62, 0x62, 0x69, 0x6e, 0x67, 0x52, 0x65, 0x63, 0x6f, 0x72, 0x64, 0x52, 0x65,
	0x71, 0x1a, 0x25, 0x2e, 0x76, 0x63, 0x2e, 0x62, 0x69, 0x7a, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74,
	0x2e, 0x44, 0x65, 0x6c, 0x65, 0x74, 0x65, 0x44, 0x75, 0x62, 0x62, 0x69, 0x6e, 0x67, 0x52, 0x65,
	0x63, 0x6f, 0x72, 0x64, 0x52, 0x65, 0x73, 0x70, 0x22, 0x33, 0x82, 0xd3, 0xe4, 0x93, 0x02, 0x2d,
	0x3a, 0x01, 0x2a, 0x22, 0x28, 0x2f, 0x76, 0x63, 0x2e, 0x62, 0x69, 0x7a, 0x73, 0x63, 0x72, 0x69,
	0x70, 0x74, 0x2e, 0x73, 0x2f, 0x76, 0x31, 0x2f, 0x64, 0x75, 0x62, 0x62, 0x69, 0x6e, 0x67, 0x2f,
	0x64, 0x65, 0x6c, 0x65, 0x74, 0x65, 0x5f, 0x72, 0x65, 0x63, 0x6f, 0x72, 0x64, 0x12, 0x7e, 0x0a,
	0x0d, 0x44, 0x65, 0x6c, 0x65, 0x74, 0x65, 0x44, 0x75, 0x62, 0x62, 0x69, 0x6e, 0x67, 0x12, 0x1e,
	0x2e, 0x76, 0x63, 0x2e, 0x62, 0x69, 0x7a, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x2e, 0x44, 0x65,
	0x6c, 0x65, 0x74, 0x65, 0x44, 0x75, 0x62, 0x62, 0x69, 0x6e, 0x67, 0x52, 0x65, 0x71, 0x1a, 0x1f,
	0x2e, 0x76, 0x63, 0x2e, 0x62, 0x69, 0x7a, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x2e, 0x44, 0x65,
	0x6c, 0x65, 0x74, 0x65, 0x44, 0x75, 0x62, 0x62, 0x69, 0x6e, 0x67, 0x52, 0x65, 0x73, 0x70, 0x22,
	0x2c, 0x82, 0xd3, 0xe4, 0x93, 0x02, 0x26, 0x3a, 0x01, 0x2a, 0x22, 0x21, 0x2f, 0x76, 0x63, 0x2e,
	0x62, 0x69, 0x7a, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x2e, 0x73, 0x2f, 0x76, 0x31, 0x2f, 0x64,
	0x75, 0x62, 0x62, 0x69, 0x6e, 0x67, 0x2f, 0x64, 0x65, 0x6c, 0x65, 0x74, 0x65, 0x12, 0x7b, 0x0a,
	0x0d, 0x53, 0x65, 0x74, 0x44, 0x75, 0x62, 0x62, 0x69, 0x6e, 0x67, 0x54, 0x6f, 0x70, 0x12, 0x1e,
	0x2e, 0x76, 0x63, 0x2e, 0x62, 0x69, 0x7a, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x2e, 0x53, 0x65,
	0x74, 0x44, 0x75, 0x62, 0x62, 0x69, 0x6e, 0x67, 0x54, 0x6f, 0x70, 0x52, 0x65, 0x71, 0x1a, 0x1f,
	0x2e, 0x76, 0x63, 0x2e, 0x62, 0x69, 0x7a, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x2e, 0x53, 0x65,
	0x74, 0x44, 0x75, 0x62, 0x62, 0x69, 0x6e, 0x67, 0x54, 0x6f, 0x70, 0x52, 0x65, 0x73, 0x70, 0x22,
	0x29, 0x82, 0xd3, 0xe4, 0x93, 0x02, 0x23, 0x3a, 0x01, 0x2a, 0x22, 0x1e, 0x2f, 0x76, 0x63, 0x2e,
	0x62, 0x69, 0x7a, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x2e, 0x73, 0x2f, 0x76, 0x31, 0x2f, 0x64,
	0x75, 0x62, 0x62, 0x69, 0x6e, 0x67, 0x2f, 0x74, 0x6f, 0x70, 0x12, 0x7f, 0x0a, 0x0e, 0x47, 0x65,
	0x74, 0x43, 0x6f, 0x6d, 0x6d, 0x65, 0x6e, 0x74, 0x4c, 0x69, 0x73, 0x74, 0x12, 0x1f, 0x2e, 0x76,
	0x63, 0x2e, 0x62, 0x69, 0x7a, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x2e, 0x47, 0x65, 0x74, 0x43,
	0x6f, 0x6d, 0x6d, 0x65, 0x6e, 0x74, 0x4c, 0x69, 0x73, 0x74, 0x52, 0x65, 0x71, 0x1a, 0x20, 0x2e,
	0x76, 0x63, 0x2e, 0x62, 0x69, 0x7a, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x2e, 0x47, 0x65, 0x74,
	0x43, 0x6f, 0x6d, 0x6d, 0x65, 0x6e, 0x74, 0x4c, 0x69, 0x73, 0x74, 0x52, 0x65, 0x73, 0x70, 0x22,
	0x2a, 0x82, 0xd3, 0xe4, 0x93, 0x02, 0x24, 0x3a, 0x01, 0x2a, 0x22, 0x1f, 0x2f, 0x76, 0x63, 0x2e,
	0x62, 0x69, 0x7a, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x2e, 0x73, 0x2f, 0x76, 0x31, 0x2f, 0x63,
	0x6f, 0x6d, 0x6d, 0x65, 0x6e, 0x74, 0x2f, 0x6c, 0x69, 0x73, 0x74, 0x12, 0x8b, 0x01, 0x0a, 0x11,
	0x47, 0x65, 0x74, 0x43, 0x6f, 0x6d, 0x6d, 0x65, 0x6e, 0x74, 0x52, 0x65, 0x70, 0x6c, 0x69, 0x65,
	0x73, 0x12, 0x22, 0x2e, 0x76, 0x63, 0x2e, 0x62, 0x69, 0x7a, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74,
	0x2e, 0x47, 0x65, 0x74, 0x43, 0x6f, 0x6d, 0x6d, 0x65, 0x6e, 0x74, 0x52, 0x65, 0x70, 0x6c, 0x69,
	0x65, 0x73, 0x52, 0x65, 0x71, 0x1a, 0x23, 0x2e, 0x76, 0x63, 0x2e, 0x62, 0x69, 0x7a, 0x73, 0x63,
	0x72, 0x69, 0x70, 0x74, 0x2e, 0x47, 0x65, 0x74, 0x43, 0x6f, 0x6d, 0x6d, 0x65, 0x6e, 0x74, 0x52,
	0x65, 0x70, 0x6c, 0x69, 0x65, 0x73, 0x52, 0x65, 0x73, 0x70, 0x22, 0x2d, 0x82, 0xd3, 0xe4, 0x93,
	0x02, 0x27, 0x3a, 0x01, 0x2a, 0x22, 0x22, 0x2f, 0x76, 0x63, 0x2e, 0x62, 0x69, 0x7a, 0x73, 0x63,
	0x72, 0x69, 0x70, 0x74, 0x2e, 0x73, 0x2f, 0x76, 0x31, 0x2f, 0x63, 0x6f, 0x6d, 0x6d, 0x65, 0x6e,
	0x74, 0x2f, 0x72, 0x65, 0x70, 0x6c, 0x69, 0x65, 0x73, 0x12, 0x7e, 0x0a, 0x0d, 0x43, 0x72, 0x65,
	0x61, 0x74, 0x65, 0x43, 0x6f, 0x6d, 0x6d, 0x65, 0x6e, 0x74, 0x12, 0x1e, 0x2e, 0x76, 0x63, 0x2e,
	0x62, 0x69, 0x7a, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x2e, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65,
	0x43, 0x6f, 0x6d, 0x6d, 0x65, 0x6e, 0x74, 0x52, 0x65, 0x71, 0x1a, 0x1f, 0x2e, 0x76, 0x63, 0x2e,
	0x62, 0x69, 0x7a, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x2e, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65,
	0x43, 0x6f, 0x6d, 0x6d, 0x65, 0x6e, 0x74, 0x52, 0x65, 0x73, 0x70, 0x22, 0x2c, 0x82, 0xd3, 0xe4,
	0x93, 0x02, 0x26, 0x3a, 0x01, 0x2a, 0x22, 0x21, 0x2f, 0x76, 0x63, 0x2e, 0x62, 0x69, 0x7a, 0x73,
	0x63, 0x72, 0x69, 0x70, 0x74, 0x2e, 0x73, 0x2f, 0x76, 0x31, 0x2f, 0x63, 0x6f, 0x6d, 0x6d, 0x65,
	0x6e, 0x74, 0x2f, 0x63, 0x72, 0x65, 0x61, 0x74, 0x65, 0x12, 0x7b, 0x0a, 0x0d, 0x53, 0x65, 0x74,
	0x43, 0x6f, 0x6d, 0x6d, 0x65, 0x6e, 0x74, 0x54, 0x6f, 0x70, 0x12, 0x1e, 0x2e, 0x76, 0x63, 0x2e,
	0x62, 0x69, 0x7a, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x2e, 0x53, 0x65, 0x74, 0x43, 0x6f, 0x6d,
	0x6d, 0x65, 0x6e, 0x74, 0x54, 0x6f, 0x70, 0x52, 0x65, 0x71, 0x1a, 0x1f, 0x2e, 0x76, 0x63, 0x2e,
	0x62, 0x69, 0x7a, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x2e, 0x53, 0x65, 0x74, 0x43, 0x6f, 0x6d,
	0x6d, 0x65, 0x6e, 0x74, 0x54, 0x6f, 0x70, 0x52, 0x65, 0x73, 0x70, 0x22, 0x29, 0x82, 0xd3, 0xe4,
	0x93, 0x02, 0x23, 0x3a, 0x01, 0x2a, 0x22, 0x1e, 0x2f, 0x76, 0x63, 0x2e, 0x62, 0x69, 0x7a, 0x73,
	0x63, 0x72, 0x69, 0x70, 0x74, 0x2e, 0x73, 0x2f, 0x76, 0x31, 0x2f, 0x63, 0x6f, 0x6d, 0x6d, 0x65,
	0x6e, 0x74, 0x2f, 0x74, 0x6f, 0x70, 0x12, 0x7e, 0x0a, 0x0d, 0x44, 0x65, 0x6c, 0x65, 0x74, 0x65,
	0x43, 0x6f, 0x6d, 0x6d, 0x65, 0x6e, 0x74, 0x12, 0x1e, 0x2e, 0x76, 0x63, 0x2e, 0x62, 0x69, 0x7a,
	0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x2e, 0x44, 0x65, 0x6c, 0x65, 0x74, 0x65, 0x43, 0x6f, 0x6d,
	0x6d, 0x65, 0x6e, 0x74, 0x52, 0x65, 0x71, 0x1a, 0x1f, 0x2e, 0x76, 0x63, 0x2e, 0x62, 0x69, 0x7a,
	0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x2e, 0x44, 0x65, 0x6c, 0x65, 0x74, 0x65, 0x43, 0x6f, 0x6d,
	0x6d, 0x65, 0x6e, 0x74, 0x52, 0x65, 0x73, 0x70, 0x22, 0x2c, 0x82, 0xd3, 0xe4, 0x93, 0x02, 0x26,
	0x3a, 0x01, 0x2a, 0x22, 0x21, 0x2f, 0x76, 0x63, 0x2e, 0x62, 0x69, 0x7a, 0x73, 0x63, 0x72, 0x69,
	0x70, 0x74, 0x2e, 0x73, 0x2f, 0x76, 0x31, 0x2f, 0x63, 0x6f, 0x6d, 0x6d, 0x65, 0x6e, 0x74, 0x2f,
	0x64, 0x65, 0x6c, 0x65, 0x74, 0x65, 0x12, 0x7f, 0x0a, 0x0d, 0x47, 0x65, 0x74, 0x43, 0x6f, 0x6d,
	0x6d, 0x65, 0x6e, 0x74, 0x41, 0x73, 0x72, 0x12, 0x1e, 0x2e, 0x76, 0x63, 0x2e, 0x62, 0x69, 0x7a,
	0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x2e, 0x47, 0x65, 0x74, 0x43, 0x6f, 0x6d, 0x6d, 0x65, 0x6e,
	0x74, 0x41, 0x53, 0x52, 0x52, 0x65, 0x71, 0x1a, 0x1f, 0x2e, 0x76, 0x63, 0x2e, 0x62, 0x69, 0x7a,
	0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x2e, 0x47, 0x65, 0x74, 0x43, 0x6f, 0x6d, 0x6d, 0x65, 0x6e,
	0x74, 0x41, 0x53, 0x52, 0x52, 0x65, 0x73, 0x70, 0x22, 0x2d, 0x82, 0xd3, 0xe4, 0x93, 0x02, 0x27,
	0x3a, 0x01, 0x2a, 0x22, 0x22, 0x2f, 0x76, 0x63, 0x2e, 0x62, 0x69, 0x7a, 0x73, 0x63, 0x72, 0x69,
	0x70, 0x74, 0x2e, 0x73, 0x2f, 0x76, 0x31, 0x2f, 0x63, 0x6f, 0x6d, 0x6d, 0x65, 0x6e, 0x74, 0x2f,
	0x67, 0x65, 0x74, 0x5f, 0x61, 0x73, 0x72, 0x12, 0x59, 0x0a, 0x04, 0x4c, 0x69, 0x6b, 0x65, 0x12,
	0x15, 0x2e, 0x76, 0x63, 0x2e, 0x62, 0x69, 0x7a, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x2e, 0x4c,
	0x69, 0x6b, 0x65, 0x52, 0x65, 0x71, 0x1a, 0x16, 0x2e, 0x76, 0x63, 0x2e, 0x62, 0x69, 0x7a, 0x73,
	0x63, 0x72, 0x69, 0x70, 0x74, 0x2e, 0x4c, 0x69, 0x6b, 0x65, 0x52, 0x65, 0x73, 0x70, 0x22, 0x22,
	0x82, 0xd3, 0xe4, 0x93, 0x02, 0x1c, 0x3a, 0x01, 0x2a, 0x22, 0x17, 0x2f, 0x76, 0x63, 0x2e, 0x62,
	0x69, 0x7a, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x2e, 0x73, 0x2f, 0x76, 0x31, 0x2f, 0x6c, 0x69,
	0x6b, 0x65, 0x12, 0x61, 0x0a, 0x06, 0x55, 0x6e, 0x6c, 0x69, 0x6b, 0x65, 0x12, 0x17, 0x2e, 0x76,
	0x63, 0x2e, 0x62, 0x69, 0x7a, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x2e, 0x55, 0x6e, 0x6c, 0x69,
	0x6b, 0x65, 0x52, 0x65, 0x71, 0x1a, 0x18, 0x2e, 0x76, 0x63, 0x2e, 0x62, 0x69, 0x7a, 0x73, 0x63,
	0x72, 0x69, 0x70, 0x74, 0x2e, 0x55, 0x6e, 0x6c, 0x69, 0x6b, 0x65, 0x52, 0x65, 0x73, 0x70, 0x22,
	0x24, 0x82, 0xd3, 0xe4, 0x93, 0x02, 0x1e, 0x3a, 0x01, 0x2a, 0x22, 0x19, 0x2f, 0x76, 0x63, 0x2e,
	0x62, 0x69, 0x7a, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x2e, 0x73, 0x2f, 0x76, 0x31, 0x2f, 0x75,
	0x6e, 0x6c, 0x69, 0x6b, 0x65, 0x12, 0x84, 0x01, 0x0a, 0x0d, 0x42, 0x61, 0x74, 0x63, 0x68, 0x47,
	0x65, 0x74, 0x4c, 0x69, 0x6b, 0x65, 0x73, 0x12, 0x1e, 0x2e, 0x76, 0x63, 0x2e, 0x62, 0x69, 0x7a,
	0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x2e, 0x42, 0x61, 0x74, 0x63, 0x68, 0x47, 0x65, 0x74, 0x4c,
	0x69, 0x6b, 0x65, 0x73, 0x52, 0x65, 0x71, 0x1a, 0x1f, 0x2e, 0x76, 0x63, 0x2e, 0x62, 0x69, 0x7a,
	0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x2e, 0x42, 0x61, 0x74, 0x63, 0x68, 0x47, 0x65, 0x74, 0x4c,
	0x69, 0x6b, 0x65, 0x73, 0x52, 0x65, 0x73, 0x70, 0x22, 0x32, 0x82, 0xd3, 0xe4, 0x93, 0x02, 0x2c,
	0x3a, 0x01, 0x2a, 0x22, 0x27, 0x2f, 0x76, 0x63, 0x2e, 0x62, 0x69, 0x7a, 0x73, 0x63, 0x72, 0x69,
	0x70, 0x74, 0x2e, 0x73, 0x2f, 0x76, 0x31, 0x2f, 0x6c, 0x69, 0x6b, 0x65, 0x2f, 0x62, 0x61, 0x74,
	0x63, 0x68, 0x5f, 0x67, 0x65, 0x74, 0x5f, 0x6c, 0x69, 0x6b, 0x65, 0x73, 0x12, 0x87, 0x01, 0x0a,
	0x10, 0x47, 0x65, 0x74, 0x52, 0x65, 0x70, 0x6f, 0x72, 0x74, 0x52, 0x65, 0x61, 0x73, 0x6f, 0x6e,
	0x73, 0x12, 0x21, 0x2e, 0x76, 0x63, 0x2e, 0x62, 0x69, 0x7a, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74,
	0x2e, 0x47, 0x65, 0x74, 0x52, 0x65, 0x70, 0x6f, 0x72, 0x74, 0x52, 0x65, 0x61, 0x73, 0x6f, 0x6e,
	0x73, 0x52, 0x65, 0x71, 0x1a, 0x22, 0x2e, 0x76, 0x63, 0x2e, 0x62, 0x69, 0x7a, 0x73, 0x63, 0x72,
	0x69, 0x70, 0x74, 0x2e, 0x47, 0x65, 0x74, 0x52, 0x65, 0x70, 0x6f, 0x72, 0x74, 0x52, 0x65, 0x61,
	0x73, 0x6f, 0x6e, 0x73, 0x52, 0x65, 0x73, 0x70, 0x22, 0x2c, 0x82, 0xd3, 0xe4, 0x93, 0x02, 0x26,
	0x3a, 0x01, 0x2a, 0x22, 0x21, 0x2f, 0x76, 0x63, 0x2e, 0x62, 0x69, 0x7a, 0x73, 0x63, 0x72, 0x69,
	0x70, 0x74, 0x2e, 0x73, 0x2f, 0x76, 0x31, 0x2f, 0x72, 0x65, 0x70, 0x6f, 0x72, 0x74, 0x2f, 0x72,
	0x65, 0x61, 0x73, 0x6f, 0x6e, 0x73, 0x12, 0x61, 0x0a, 0x06, 0x52, 0x65, 0x70, 0x6f, 0x72, 0x74,
	0x12, 0x17, 0x2e, 0x76, 0x63, 0x2e, 0x62, 0x69, 0x7a, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x2e,
	0x52, 0x65, 0x70, 0x6f, 0x72, 0x74, 0x52, 0x65, 0x71, 0x1a, 0x18, 0x2e, 0x76, 0x63, 0x2e, 0x62,
	0x69, 0x7a, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x2e, 0x52, 0x65, 0x70, 0x6f, 0x72, 0x74, 0x52,
	0x65, 0x73, 0x70, 0x22, 0x24, 0x82, 0xd3, 0xe4, 0x93, 0x02, 0x1e, 0x3a, 0x01, 0x2a, 0x22, 0x19,
	0x2f, 0x76, 0x63, 0x2e, 0x62, 0x69, 0x7a, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x2e, 0x73, 0x2f,
	0x76, 0x31, 0x2f, 0x72, 0x65, 0x70, 0x6f, 0x72, 0x74, 0x12, 0x6b, 0x0a, 0x09, 0x47, 0x65, 0x74,
	0x49, 0x50, 0x4c, 0x69, 0x73, 0x74, 0x12, 0x1a, 0x2e, 0x76, 0x63, 0x2e, 0x62, 0x69, 0x7a, 0x73,
	0x63, 0x72, 0x69, 0x70, 0x74, 0x2e, 0x47, 0x65, 0x74, 0x49, 0x50, 0x4c, 0x69, 0x73, 0x74, 0x52,
	0x65, 0x71, 0x1a, 0x1b, 0x2e, 0x76, 0x63, 0x2e, 0x62, 0x69, 0x7a, 0x73, 0x63, 0x72, 0x69, 0x70,
	0x74, 0x2e, 0x47, 0x65, 0x74, 0x49, 0x50, 0x4c, 0x69, 0x73, 0x74, 0x52, 0x65, 0x73, 0x70, 0x22,
	0x25, 0x82, 0xd3, 0xe4, 0x93, 0x02, 0x1f, 0x3a, 0x01, 0x2a, 0x22, 0x1a, 0x2f, 0x76, 0x63, 0x2e,
	0x62, 0x69, 0x7a, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x2e, 0x73, 0x2f, 0x76, 0x31, 0x2f, 0x69,
	0x70, 0x2f, 0x6c, 0x69, 0x73, 0x74, 0x12, 0x77, 0x0a, 0x0c, 0x47, 0x65, 0x74, 0x55, 0x73, 0x65,
	0x72, 0x53, 0x74, 0x61, 0x74, 0x73, 0x12, 0x1d, 0x2e, 0x76, 0x63, 0x2e, 0x62, 0x69, 0x7a, 0x73,
	0x63, 0x72, 0x69, 0x70, 0x74, 0x2e, 0x47, 0x65, 0x74, 0x55, 0x73, 0x65, 0x72, 0x53, 0x74, 0x61,
	0x74, 0x73, 0x52, 0x65, 0x71, 0x1a, 0x1e, 0x2e, 0x76, 0x63, 0x2e, 0x62, 0x69, 0x7a, 0x73, 0x63,
	0x72, 0x69, 0x70, 0x74, 0x2e, 0x47, 0x65, 0x74, 0x55, 0x73, 0x65, 0x72, 0x53, 0x74, 0x61, 0x74,
	0x73, 0x52, 0x65, 0x73, 0x70, 0x22, 0x28, 0x82, 0xd3, 0xe4, 0x93, 0x02, 0x22, 0x3a, 0x01, 0x2a,
	0x22, 0x1d, 0x2f, 0x76, 0x63, 0x2e, 0x62, 0x69, 0x7a, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x2e,
	0x73, 0x2f, 0x76, 0x31, 0x2f, 0x75, 0x73, 0x65, 0x72, 0x2f, 0x73, 0x74, 0x61, 0x74, 0x73, 0x12,
	0x90, 0x01, 0x0a, 0x12, 0x47, 0x65, 0x74, 0x55, 0x73, 0x65, 0x72, 0x53, 0x63, 0x72, 0x69, 0x70,
	0x74, 0x4c, 0x69, 0x73, 0x74, 0x73, 0x12, 0x23, 0x2e, 0x76, 0x63, 0x2e, 0x62, 0x69, 0x7a, 0x73,
	0x63, 0x72, 0x69, 0x70, 0x74, 0x2e, 0x47, 0x65, 0x74, 0x55, 0x73, 0x65, 0x72, 0x53, 0x63, 0x72,
	0x69, 0x70, 0x74, 0x4c, 0x69, 0x73, 0x74, 0x73, 0x52, 0x65, 0x71, 0x1a, 0x24, 0x2e, 0x76, 0x63,
	0x2e, 0x62, 0x69, 0x7a, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x2e, 0x47, 0x65, 0x74, 0x55, 0x73,
	0x65, 0x72, 0x53, 0x63, 0x72, 0x69, 0x70, 0x74, 0x4c, 0x69, 0x73, 0x74, 0x73, 0x52, 0x65, 0x73,
	0x70, 0x22, 0x2f, 0x82, 0xd3, 0xe4, 0x93, 0x02, 0x29, 0x3a, 0x01, 0x2a, 0x22, 0x24, 0x2f, 0x76,
	0x63, 0x2e, 0x62, 0x69, 0x7a, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x2e, 0x73, 0x2f, 0x76, 0x31,
	0x2f, 0x75, 0x73, 0x65, 0x72, 0x2f, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x5f, 0x6c, 0x69, 0x73,
	0x74, 0x73, 0x12, 0x77, 0x0a, 0x0c, 0x47, 0x65, 0x74, 0x43, 0x6f, 0x76, 0x65, 0x72, 0x4c, 0x69,
	0x73, 0x74, 0x12, 0x1d, 0x2e, 0x76, 0x63, 0x2e, 0x62, 0x69, 0x7a, 0x73, 0x63, 0x72, 0x69, 0x70,
	0x74, 0x2e, 0x47, 0x65, 0x74, 0x43, 0x6f, 0x76, 0x65, 0x72, 0x4c, 0x69, 0x73, 0x74, 0x52, 0x65,
	0x71, 0x1a, 0x1e, 0x2e, 0x76, 0x63, 0x2e, 0x62, 0x69, 0x7a, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74,
	0x2e, 0x47, 0x65, 0x74, 0x43, 0x6f, 0x76, 0x65, 0x72, 0x4c, 0x69, 0x73, 0x74, 0x52, 0x65, 0x73,
	0x70, 0x22, 0x28, 0x82, 0xd3, 0xe4, 0x93, 0x02, 0x22, 0x3a, 0x01, 0x2a, 0x22, 0x1d, 0x2f, 0x76,
	0x63, 0x2e, 0x62, 0x69, 0x7a, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x2e, 0x73, 0x2f, 0x76, 0x31,
	0x2f, 0x63, 0x6f, 0x76, 0x65, 0x72, 0x2f, 0x6c, 0x69, 0x73, 0x74, 0x12, 0x8f, 0x01, 0x0a, 0x13,
	0x47, 0x65, 0x6e, 0x65, 0x72, 0x61, 0x74, 0x65, 0x53, 0x63, 0x72, 0x69, 0x70, 0x74, 0x4c, 0x69,
	0x6e, 0x65, 0x73, 0x12, 0x24, 0x2e, 0x76, 0x63, 0x2e, 0x62, 0x69, 0x7a, 0x73, 0x63, 0x72, 0x69,
	0x70, 0x74, 0x2e, 0x47, 0x65, 0x6e, 0x65, 0x72, 0x61, 0x74, 0x65, 0x53, 0x63, 0x72, 0x69, 0x70,
	0x74, 0x4c, 0x69, 0x6e, 0x65, 0x73, 0x52, 0x65, 0x71, 0x1a, 0x25, 0x2e, 0x76, 0x63, 0x2e, 0x62,
	0x69, 0x7a, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x2e, 0x47, 0x65, 0x6e, 0x65, 0x72, 0x61, 0x74,
	0x65, 0x53, 0x63, 0x72, 0x69, 0x70, 0x74, 0x4c, 0x69, 0x6e, 0x65, 0x73, 0x52, 0x65, 0x73, 0x70,
	0x22, 0x2b, 0x82, 0xd3, 0xe4, 0x93, 0x02, 0x25, 0x3a, 0x01, 0x2a, 0x22, 0x20, 0x2f, 0x76, 0x63,
	0x2e, 0x62, 0x69, 0x7a, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x2e, 0x73, 0x2f, 0x76, 0x31, 0x2f,
	0x6c, 0x69, 0x6e, 0x65, 0x2f, 0x67, 0x65, 0x6e, 0x65, 0x72, 0x61, 0x74, 0x65, 0x42, 0x3d, 0x5a,
	0x3b, 0x6e, 0x65, 0x77, 0x2d, 0x67, 0x69, 0x74, 0x6c, 0x61, 0x62, 0x2e, 0x78, 0x75, 0x6e, 0x6c,
	0x65, 0x69, 0x2e, 0x63, 0x6e, 0x2f, 0x76, 0x63, 0x70, 0x72, 0x6f, 0x6a, 0x65, 0x63, 0x74, 0x2f,
	0x62, 0x61, 0x63, 0x6b, 0x65, 0x6e, 0x64, 0x73, 0x2f, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2f, 0x61,
	0x70, 0x69, 0x2f, 0x62, 0x69, 0x7a, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x62, 0x06, 0x70, 0x72,
	0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_bizscript_proto_rawDescOnce sync.Once
	file_bizscript_proto_rawDescData = file_bizscript_proto_rawDesc
)

func file_bizscript_proto_rawDescGZIP() []byte {
	file_bizscript_proto_rawDescOnce.Do(func() {
		file_bizscript_proto_rawDescData = protoimpl.X.CompressGZIP(file_bizscript_proto_rawDescData)
	})
	return file_bizscript_proto_rawDescData
}

var file_bizscript_proto_msgTypes = make([]protoimpl.MessageInfo, 80)
var file_bizscript_proto_goTypes = []interface{}{
	(*GetCharacterListReq)(nil),                               // 0: vc.bizscript.GetCharacterListReq
	(*GetCharacterListResp)(nil),                              // 1: vc.bizscript.GetCharacterListResp
	(*SearchCharactersReq)(nil),                               // 2: vc.bizscript.SearchCharactersReq
	(*SearchCharactersResp)(nil),                              // 3: vc.bizscript.SearchCharactersResp
	(*SetUserCharacterReq)(nil),                               // 4: vc.bizscript.SetUserCharacterReq
	(*SetUserCharacterResp)(nil),                              // 5: vc.bizscript.SetUserCharacterResp
	(*GetUserCharactersReq)(nil),                              // 6: vc.bizscript.GetUserCharactersReq
	(*GetUserCharactersResp)(nil),                             // 7: vc.bizscript.GetUserCharactersResp
	(*BatchCheckCharacterAvailabilityReq)(nil),                // 8: vc.bizscript.BatchCheckCharacterAvailabilityReq
	(*BatchCheckCharacterAvailabilityResp)(nil),               // 9: vc.bizscript.BatchCheckCharacterAvailabilityResp
	(*GetTopicListReq)(nil),                                   // 10: vc.bizscript.GetTopicListReq
	(*GetTopicListResp)(nil),                                  // 11: vc.bizscript.GetTopicListResp
	(*CreateScriptReq)(nil),                                   // 12: vc.bizscript.CreateScriptReq
	(*CreateScriptResp)(nil),                                  // 13: vc.bizscript.CreateScriptResp
	(*DeleteScriptReq)(nil),                                   // 14: vc.bizscript.DeleteScriptReq
	(*DeleteScriptResp)(nil),                                  // 15: vc.bizscript.DeleteScriptResp
	(*GetScriptListReq)(nil),                                  // 16: vc.bizscript.GetScriptListReq
	(*GetScriptListResp)(nil),                                 // 17: vc.bizscript.GetScriptListResp
	(*GetScriptDetailReq)(nil),                                // 18: vc.bizscript.GetScriptDetailReq
	(*GetScriptDetailResp)(nil),                               // 19: vc.bizscript.GetScriptDetailResp
	(*SearchReq)(nil),                                         // 20: vc.bizscript.SearchReq
	(*SearchResp)(nil),                                        // 21: vc.bizscript.SearchResp
	(*FollowScriptReq)(nil),                                   // 22: vc.bizscript.FollowScriptReq
	(*FollowScriptResp)(nil),                                  // 23: vc.bizscript.FollowScriptResp
	(*UnfollowScriptReq)(nil),                                 // 24: vc.bizscript.UnfollowScriptReq
	(*UnfollowScriptResp)(nil),                                // 25: vc.bizscript.UnfollowScriptResp
	(*ShareScriptReq)(nil),                                    // 26: vc.bizscript.ShareScriptReq
	(*ShareScriptResp)(nil),                                   // 27: vc.bizscript.ShareScriptResp
	(*GetLineDubbingAllReq)(nil),                              // 28: vc.bizscript.GetLineDubbingAllReq
	(*GetLineDubbingAllResp)(nil),                             // 29: vc.bizscript.GetLineDubbingAllResp
	(*GetLineDubbingSimpleReq)(nil),                           // 30: vc.bizscript.GetLineDubbingSimpleReq
	(*GetLineDubbingSimpleResp)(nil),                          // 31: vc.bizscript.GetLineDubbingSimpleResp
	(*GetScriptFirstDubbingReq)(nil),                          // 32: vc.bizscript.GetScriptFirstDubbingReq
	(*GetScriptFirstDubbingResp)(nil),                         // 33: vc.bizscript.GetScriptFirstDubbingResp
	(*BatchGetScriptFirstDubbingReq)(nil),                     // 34: vc.bizscript.BatchGetScriptFirstDubbingReq
	(*BatchGetScriptFirstDubbingResp)(nil),                    // 35: vc.bizscript.BatchGetScriptFirstDubbingResp
	(*CreateDubbingReq)(nil),                                  // 36: vc.bizscript.CreateDubbingReq
	(*CreateDubbingResp)(nil),                                 // 37: vc.bizscript.CreateDubbingResp
	(*DeleteDubbingRecordReq)(nil),                            // 38: vc.bizscript.DeleteDubbingRecordReq
	(*DeleteDubbingRecordResp)(nil),                           // 39: vc.bizscript.DeleteDubbingRecordResp
	(*GetCommentListReq)(nil),                                 // 40: vc.bizscript.GetCommentListReq
	(*GetCommentListResp)(nil),                                // 41: vc.bizscript.GetCommentListResp
	(*GetCommentRepliesReq)(nil),                              // 42: vc.bizscript.GetCommentRepliesReq
	(*GetCommentRepliesResp)(nil),                             // 43: vc.bizscript.GetCommentRepliesResp
	(*CreateCommentReq)(nil),                                  // 44: vc.bizscript.CreateCommentReq
	(*CreateCommentResp)(nil),                                 // 45: vc.bizscript.CreateCommentResp
	(*SetCommentTopReq)(nil),                                  // 46: vc.bizscript.SetCommentTopReq
	(*SetCommentTopResp)(nil),                                 // 47: vc.bizscript.SetCommentTopResp
	(*DeleteCommentReq)(nil),                                  // 48: vc.bizscript.DeleteCommentReq
	(*DeleteCommentResp)(nil),                                 // 49: vc.bizscript.DeleteCommentResp
	(*GetCommentASRReq)(nil),                                  // 50: vc.bizscript.GetCommentASRReq
	(*GetCommentASRResp)(nil),                                 // 51: vc.bizscript.GetCommentASRResp
	(*LikeReq)(nil),                                           // 52: vc.bizscript.LikeReq
	(*LikeResp)(nil),                                          // 53: vc.bizscript.LikeResp
	(*UnlikeReq)(nil),                                         // 54: vc.bizscript.UnlikeReq
	(*UnlikeResp)(nil),                                        // 55: vc.bizscript.UnlikeResp
	(*BatchGetLikesReq)(nil),                                  // 56: vc.bizscript.BatchGetLikesReq
	(*BatchGetLikesResp)(nil),                                 // 57: vc.bizscript.BatchGetLikesResp
	(*GetReportReasonsReq)(nil),                               // 58: vc.bizscript.GetReportReasonsReq
	(*GetReportReasonsResp)(nil),                              // 59: vc.bizscript.GetReportReasonsResp
	(*ReportReq)(nil),                                         // 60: vc.bizscript.ReportReq
	(*ReportResp)(nil),                                        // 61: vc.bizscript.ReportResp
	(*GetIPListReq)(nil),                                      // 62: vc.bizscript.GetIPListReq
	(*GetIPListResp)(nil),                                     // 63: vc.bizscript.GetIPListResp
	(*GetUserStatsReq)(nil),                                   // 64: vc.bizscript.GetUserStatsReq
	(*GetUserStatsResp)(nil),                                  // 65: vc.bizscript.GetUserStatsResp
	(*GetUserScriptListsReq)(nil),                             // 66: vc.bizscript.GetUserScriptListsReq
	(*GetUserScriptListsResp)(nil),                            // 67: vc.bizscript.GetUserScriptListsResp
	(*GetCoverListReq)(nil),                                   // 68: vc.bizscript.GetCoverListReq
	(*GetCoverListResp)(nil),                                  // 69: vc.bizscript.GetCoverListResp
	(*GenerateScriptLinesReq)(nil),                            // 70: vc.bizscript.GenerateScriptLinesReq
	(*GenerateScriptLinesResp)(nil),                           // 71: vc.bizscript.GenerateScriptLinesResp
	(*GenerateScriptLinesRespData)(nil),                       // 72: vc.bizscript.GenerateScriptLinesRespData
	(*DeleteDubbingReq)(nil),                                  // 73: vc.bizscript.DeleteDubbingReq
	(*DeleteDubbingResp)(nil),                                 // 74: vc.bizscript.DeleteDubbingResp
	(*SetDubbingTopReq)(nil),                                  // 75: vc.bizscript.SetDubbingTopReq
	(*SetDubbingTopResp)(nil),                                 // 76: vc.bizscript.SetDubbingTopResp
	nil,                                                       // 77: vc.bizscript.CreateDubbingReq.LineDubbingsEntry
	nil,                                                       // 78: vc.bizscript.BatchGetLikesResp.DataEntry
	nil,                                                       // 79: vc.bizscript.GenerateScriptLinesRespData.CharactersEntry
	(*svcscript.GetCharacterListRespData)(nil),                // 80: vc.svcscript.GetCharacterListRespData
	(*svcscript.GetUserCharactersRespData)(nil),               // 81: vc.svcscript.GetUserCharactersRespData
	(*svcscript.BatchCheckCharacterAvailabilityRespData)(nil), // 82: vc.svcscript.BatchCheckCharacterAvailabilityRespData
	(svcscript.TOPIC_SCENE)(0),                                // 83: vc.svcscript.TOPIC_SCENE
	(*svcscript.GetTopicListRespData)(nil),                    // 84: vc.svcscript.GetTopicListRespData
	(*svcscript.ScriptCharacterAsset)(nil),                    // 85: vc.svcscript.ScriptCharacterAsset
	(*svcscript.CreateLineReq)(nil),                           // 86: vc.svcscript.CreateLineReq
	(*svcscript.CreateScriptRespData)(nil),                    // 87: vc.svcscript.CreateScriptRespData
	(*svcscript.GetScriptListRespData)(nil),                   // 88: vc.svcscript.GetScriptListRespData
	(*svcscript.Script)(nil),                                  // 89: vc.svcscript.Script
	(svcscript.SearchType)(0),                                 // 90: vc.svcscript.SearchType
	(*svcscript.SearchRespData)(nil),                          // 91: vc.svcscript.SearchRespData
	(*svcscript.GetLineDubbingsRespData)(nil),                 // 92: vc.svcscript.GetLineDubbingsRespData
	(*svcscript.GetLineDubbingsSimpleRespData)(nil),           // 93: vc.svcscript.GetLineDubbingsSimpleRespData
	(*svcscript.GetScriptFirstDubbingData)(nil),               // 94: vc.svcscript.GetScriptFirstDubbingData
	(*svcscript.BatchGetScriptFirstDubbingData)(nil),          // 95: vc.svcscript.BatchGetScriptFirstDubbingData
	(*svcscript.CreateDubbingRespData)(nil),                   // 96: vc.svcscript.CreateDubbingRespData
	(svcscript.CommentType)(0),                                // 97: vc.svcscript.CommentType
	(*svcscript.GetCommentListRespData)(nil),                  // 98: vc.svcscript.GetCommentListRespData
	(svcscript.ContentType)(0),                                // 99: vc.svcscript.ContentType
	(*svcscript.CreateCommentRespData)(nil),                   // 100: vc.svcscript.CreateCommentRespData
	(*svcscript.GetCommentASRRespData)(nil),                   // 101: vc.svcscript.GetCommentASRRespData
	(svcscript.LikeType)(0),                                   // 102: vc.svcscript.LikeType
	(svcscript.ReportType)(0),                                 // 103: vc.svcscript.ReportType
	(*svcscript.GetReportReasonsRespData)(nil),                // 104: vc.svcscript.GetReportReasonsRespData
	(*svcscript.GetIPListRespData)(nil),                       // 105: vc.svcscript.GetIPListRespData
	(*svcscript.GetUserStatsRespData)(nil),                    // 106: vc.svcscript.GetUserStatsRespData
	(svcscript.GetUserScriptListsReq_ListType)(0),             // 107: vc.svcscript.GetUserScriptListsReq.ListType
	(*svcscript.GetUserScriptListsRespData)(nil),              // 108: vc.svcscript.GetUserScriptListsRespData
	(svcscript.CoverType)(0),                                  // 109: vc.svcscript.CoverType
	(*svcscript.GetCoverListRespData)(nil),                    // 110: vc.svcscript.GetCoverListRespData
	(*svcscript.Line)(nil),                                    // 111: vc.svcscript.Line
	(*svcscript.LineDubbings)(nil),                            // 112: vc.svcscript.LineDubbings
	(*svcscript.Character)(nil),                               // 113: vc.svcscript.Character
}
var file_bizscript_proto_depIdxs = []int32{
	80,  // 0: vc.bizscript.GetCharacterListResp.data:type_name -> vc.svcscript.GetCharacterListRespData
	80,  // 1: vc.bizscript.SearchCharactersResp.data:type_name -> vc.svcscript.GetCharacterListRespData
	81,  // 2: vc.bizscript.GetUserCharactersResp.data:type_name -> vc.svcscript.GetUserCharactersRespData
	82,  // 3: vc.bizscript.BatchCheckCharacterAvailabilityResp.data:type_name -> vc.svcscript.BatchCheckCharacterAvailabilityRespData
	83,  // 4: vc.bizscript.GetTopicListReq.scene:type_name -> vc.svcscript.TOPIC_SCENE
	84,  // 5: vc.bizscript.GetTopicListResp.data:type_name -> vc.svcscript.GetTopicListRespData
	85,  // 6: vc.bizscript.CreateScriptReq.character_asset_ids:type_name -> vc.svcscript.ScriptCharacterAsset
	86,  // 7: vc.bizscript.CreateScriptReq.lines:type_name -> vc.svcscript.CreateLineReq
	87,  // 8: vc.bizscript.CreateScriptResp.data:type_name -> vc.svcscript.CreateScriptRespData
	88,  // 9: vc.bizscript.GetScriptListResp.data:type_name -> vc.svcscript.GetScriptListRespData
	89,  // 10: vc.bizscript.GetScriptDetailResp.data:type_name -> vc.svcscript.Script
	90,  // 11: vc.bizscript.SearchReq.search_type:type_name -> vc.svcscript.SearchType
	91,  // 12: vc.bizscript.SearchResp.data:type_name -> vc.svcscript.SearchRespData
	92,  // 13: vc.bizscript.GetLineDubbingAllResp.data:type_name -> vc.svcscript.GetLineDubbingsRespData
	93,  // 14: vc.bizscript.GetLineDubbingSimpleResp.data:type_name -> vc.svcscript.GetLineDubbingsSimpleRespData
	94,  // 15: vc.bizscript.GetScriptFirstDubbingResp.data:type_name -> vc.svcscript.GetScriptFirstDubbingData
	95,  // 16: vc.bizscript.BatchGetScriptFirstDubbingResp.data:type_name -> vc.svcscript.BatchGetScriptFirstDubbingData
	77,  // 17: vc.bizscript.CreateDubbingReq.line_dubbings:type_name -> vc.bizscript.CreateDubbingReq.LineDubbingsEntry
	96,  // 18: vc.bizscript.CreateDubbingResp.data:type_name -> vc.svcscript.CreateDubbingRespData
	97,  // 19: vc.bizscript.GetCommentListReq.comment_type:type_name -> vc.svcscript.CommentType
	98,  // 20: vc.bizscript.GetCommentListResp.data:type_name -> vc.svcscript.GetCommentListRespData
	98,  // 21: vc.bizscript.GetCommentRepliesResp.data:type_name -> vc.svcscript.GetCommentListRespData
	97,  // 22: vc.bizscript.CreateCommentReq.comment_type:type_name -> vc.svcscript.CommentType
	99,  // 23: vc.bizscript.CreateCommentReq.content_type:type_name -> vc.svcscript.ContentType
	100, // 24: vc.bizscript.CreateCommentResp.data:type_name -> vc.svcscript.CreateCommentRespData
	101, // 25: vc.bizscript.GetCommentASRResp.data:type_name -> vc.svcscript.GetCommentASRRespData
	102, // 26: vc.bizscript.LikeReq.like_type:type_name -> vc.svcscript.LikeType
	102, // 27: vc.bizscript.UnlikeReq.like_type:type_name -> vc.svcscript.LikeType
	102, // 28: vc.bizscript.BatchGetLikesReq.like_type:type_name -> vc.svcscript.LikeType
	78,  // 29: vc.bizscript.BatchGetLikesResp.data:type_name -> vc.bizscript.BatchGetLikesResp.DataEntry
	103, // 30: vc.bizscript.GetReportReasonsReq.report_type:type_name -> vc.svcscript.ReportType
	104, // 31: vc.bizscript.GetReportReasonsResp.data:type_name -> vc.svcscript.GetReportReasonsRespData
	103, // 32: vc.bizscript.ReportReq.report_type:type_name -> vc.svcscript.ReportType
	105, // 33: vc.bizscript.GetIPListResp.data:type_name -> vc.svcscript.GetIPListRespData
	106, // 34: vc.bizscript.GetUserStatsResp.data:type_name -> vc.svcscript.GetUserStatsRespData
	107, // 35: vc.bizscript.GetUserScriptListsReq.list_type:type_name -> vc.svcscript.GetUserScriptListsReq.ListType
	108, // 36: vc.bizscript.GetUserScriptListsResp.data:type_name -> vc.svcscript.GetUserScriptListsRespData
	109, // 37: vc.bizscript.GetCoverListReq.type:type_name -> vc.svcscript.CoverType
	110, // 38: vc.bizscript.GetCoverListResp.data:type_name -> vc.svcscript.GetCoverListRespData
	72,  // 39: vc.bizscript.GenerateScriptLinesResp.data:type_name -> vc.bizscript.GenerateScriptLinesRespData
	111, // 40: vc.bizscript.GenerateScriptLinesRespData.lines:type_name -> vc.svcscript.Line
	79,  // 41: vc.bizscript.GenerateScriptLinesRespData.characters:type_name -> vc.bizscript.GenerateScriptLinesRespData.CharactersEntry
	112, // 42: vc.bizscript.CreateDubbingReq.LineDubbingsEntry.value:type_name -> vc.svcscript.LineDubbings
	113, // 43: vc.bizscript.GenerateScriptLinesRespData.CharactersEntry.value:type_name -> vc.svcscript.Character
	0,   // 44: vc.bizscript.s.GetCharacterList:input_type -> vc.bizscript.GetCharacterListReq
	2,   // 45: vc.bizscript.s.SearchCharacters:input_type -> vc.bizscript.SearchCharactersReq
	4,   // 46: vc.bizscript.s.SetUserCharacter:input_type -> vc.bizscript.SetUserCharacterReq
	6,   // 47: vc.bizscript.s.GetUserCharacters:input_type -> vc.bizscript.GetUserCharactersReq
	8,   // 48: vc.bizscript.s.BatchCheckCharacterAvailability:input_type -> vc.bizscript.BatchCheckCharacterAvailabilityReq
	10,  // 49: vc.bizscript.s.GetTopicList:input_type -> vc.bizscript.GetTopicListReq
	12,  // 50: vc.bizscript.s.CreateScript:input_type -> vc.bizscript.CreateScriptReq
	14,  // 51: vc.bizscript.s.DeleteScript:input_type -> vc.bizscript.DeleteScriptReq
	16,  // 52: vc.bizscript.s.GetScriptList:input_type -> vc.bizscript.GetScriptListReq
	18,  // 53: vc.bizscript.s.GetScriptDetail:input_type -> vc.bizscript.GetScriptDetailReq
	20,  // 54: vc.bizscript.s.Search:input_type -> vc.bizscript.SearchReq
	26,  // 55: vc.bizscript.s.ShareScript:input_type -> vc.bizscript.ShareScriptReq
	28,  // 56: vc.bizscript.s.GetLineDubbingAll:input_type -> vc.bizscript.GetLineDubbingAllReq
	30,  // 57: vc.bizscript.s.GetLineDubbingSimple:input_type -> vc.bizscript.GetLineDubbingSimpleReq
	32,  // 58: vc.bizscript.s.GetScriptFirstDubbing:input_type -> vc.bizscript.GetScriptFirstDubbingReq
	34,  // 59: vc.bizscript.s.BatchGetScriptFirstDubbing:input_type -> vc.bizscript.BatchGetScriptFirstDubbingReq
	36,  // 60: vc.bizscript.s.CreateDubbing:input_type -> vc.bizscript.CreateDubbingReq
	38,  // 61: vc.bizscript.s.DeleteDubbingRecord:input_type -> vc.bizscript.DeleteDubbingRecordReq
	73,  // 62: vc.bizscript.s.DeleteDubbing:input_type -> vc.bizscript.DeleteDubbingReq
	75,  // 63: vc.bizscript.s.SetDubbingTop:input_type -> vc.bizscript.SetDubbingTopReq
	40,  // 64: vc.bizscript.s.GetCommentList:input_type -> vc.bizscript.GetCommentListReq
	42,  // 65: vc.bizscript.s.GetCommentReplies:input_type -> vc.bizscript.GetCommentRepliesReq
	44,  // 66: vc.bizscript.s.CreateComment:input_type -> vc.bizscript.CreateCommentReq
	46,  // 67: vc.bizscript.s.SetCommentTop:input_type -> vc.bizscript.SetCommentTopReq
	48,  // 68: vc.bizscript.s.DeleteComment:input_type -> vc.bizscript.DeleteCommentReq
	50,  // 69: vc.bizscript.s.GetCommentAsr:input_type -> vc.bizscript.GetCommentASRReq
	52,  // 70: vc.bizscript.s.Like:input_type -> vc.bizscript.LikeReq
	54,  // 71: vc.bizscript.s.Unlike:input_type -> vc.bizscript.UnlikeReq
	56,  // 72: vc.bizscript.s.BatchGetLikes:input_type -> vc.bizscript.BatchGetLikesReq
	58,  // 73: vc.bizscript.s.GetReportReasons:input_type -> vc.bizscript.GetReportReasonsReq
	60,  // 74: vc.bizscript.s.Report:input_type -> vc.bizscript.ReportReq
	62,  // 75: vc.bizscript.s.GetIPList:input_type -> vc.bizscript.GetIPListReq
	64,  // 76: vc.bizscript.s.GetUserStats:input_type -> vc.bizscript.GetUserStatsReq
	66,  // 77: vc.bizscript.s.GetUserScriptLists:input_type -> vc.bizscript.GetUserScriptListsReq
	68,  // 78: vc.bizscript.s.GetCoverList:input_type -> vc.bizscript.GetCoverListReq
	70,  // 79: vc.bizscript.s.GenerateScriptLines:input_type -> vc.bizscript.GenerateScriptLinesReq
	1,   // 80: vc.bizscript.s.GetCharacterList:output_type -> vc.bizscript.GetCharacterListResp
	3,   // 81: vc.bizscript.s.SearchCharacters:output_type -> vc.bizscript.SearchCharactersResp
	5,   // 82: vc.bizscript.s.SetUserCharacter:output_type -> vc.bizscript.SetUserCharacterResp
	7,   // 83: vc.bizscript.s.GetUserCharacters:output_type -> vc.bizscript.GetUserCharactersResp
	9,   // 84: vc.bizscript.s.BatchCheckCharacterAvailability:output_type -> vc.bizscript.BatchCheckCharacterAvailabilityResp
	11,  // 85: vc.bizscript.s.GetTopicList:output_type -> vc.bizscript.GetTopicListResp
	13,  // 86: vc.bizscript.s.CreateScript:output_type -> vc.bizscript.CreateScriptResp
	15,  // 87: vc.bizscript.s.DeleteScript:output_type -> vc.bizscript.DeleteScriptResp
	17,  // 88: vc.bizscript.s.GetScriptList:output_type -> vc.bizscript.GetScriptListResp
	19,  // 89: vc.bizscript.s.GetScriptDetail:output_type -> vc.bizscript.GetScriptDetailResp
	21,  // 90: vc.bizscript.s.Search:output_type -> vc.bizscript.SearchResp
	27,  // 91: vc.bizscript.s.ShareScript:output_type -> vc.bizscript.ShareScriptResp
	29,  // 92: vc.bizscript.s.GetLineDubbingAll:output_type -> vc.bizscript.GetLineDubbingAllResp
	31,  // 93: vc.bizscript.s.GetLineDubbingSimple:output_type -> vc.bizscript.GetLineDubbingSimpleResp
	33,  // 94: vc.bizscript.s.GetScriptFirstDubbing:output_type -> vc.bizscript.GetScriptFirstDubbingResp
	35,  // 95: vc.bizscript.s.BatchGetScriptFirstDubbing:output_type -> vc.bizscript.BatchGetScriptFirstDubbingResp
	37,  // 96: vc.bizscript.s.CreateDubbing:output_type -> vc.bizscript.CreateDubbingResp
	39,  // 97: vc.bizscript.s.DeleteDubbingRecord:output_type -> vc.bizscript.DeleteDubbingRecordResp
	74,  // 98: vc.bizscript.s.DeleteDubbing:output_type -> vc.bizscript.DeleteDubbingResp
	76,  // 99: vc.bizscript.s.SetDubbingTop:output_type -> vc.bizscript.SetDubbingTopResp
	41,  // 100: vc.bizscript.s.GetCommentList:output_type -> vc.bizscript.GetCommentListResp
	43,  // 101: vc.bizscript.s.GetCommentReplies:output_type -> vc.bizscript.GetCommentRepliesResp
	45,  // 102: vc.bizscript.s.CreateComment:output_type -> vc.bizscript.CreateCommentResp
	47,  // 103: vc.bizscript.s.SetCommentTop:output_type -> vc.bizscript.SetCommentTopResp
	49,  // 104: vc.bizscript.s.DeleteComment:output_type -> vc.bizscript.DeleteCommentResp
	51,  // 105: vc.bizscript.s.GetCommentAsr:output_type -> vc.bizscript.GetCommentASRResp
	53,  // 106: vc.bizscript.s.Like:output_type -> vc.bizscript.LikeResp
	55,  // 107: vc.bizscript.s.Unlike:output_type -> vc.bizscript.UnlikeResp
	57,  // 108: vc.bizscript.s.BatchGetLikes:output_type -> vc.bizscript.BatchGetLikesResp
	59,  // 109: vc.bizscript.s.GetReportReasons:output_type -> vc.bizscript.GetReportReasonsResp
	61,  // 110: vc.bizscript.s.Report:output_type -> vc.bizscript.ReportResp
	63,  // 111: vc.bizscript.s.GetIPList:output_type -> vc.bizscript.GetIPListResp
	65,  // 112: vc.bizscript.s.GetUserStats:output_type -> vc.bizscript.GetUserStatsResp
	67,  // 113: vc.bizscript.s.GetUserScriptLists:output_type -> vc.bizscript.GetUserScriptListsResp
	69,  // 114: vc.bizscript.s.GetCoverList:output_type -> vc.bizscript.GetCoverListResp
	71,  // 115: vc.bizscript.s.GenerateScriptLines:output_type -> vc.bizscript.GenerateScriptLinesResp
	80,  // [80:116] is the sub-list for method output_type
	44,  // [44:80] is the sub-list for method input_type
	44,  // [44:44] is the sub-list for extension type_name
	44,  // [44:44] is the sub-list for extension extendee
	0,   // [0:44] is the sub-list for field type_name
}

func init() { file_bizscript_proto_init() }
func file_bizscript_proto_init() {
	if File_bizscript_proto != nil {
		return
	}
	if !protoimpl.UnsafeEnabled {
		file_bizscript_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetCharacterListReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_bizscript_proto_msgTypes[1].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetCharacterListResp); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_bizscript_proto_msgTypes[2].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*SearchCharactersReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_bizscript_proto_msgTypes[3].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*SearchCharactersResp); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_bizscript_proto_msgTypes[4].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*SetUserCharacterReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_bizscript_proto_msgTypes[5].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*SetUserCharacterResp); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_bizscript_proto_msgTypes[6].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetUserCharactersReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_bizscript_proto_msgTypes[7].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetUserCharactersResp); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_bizscript_proto_msgTypes[8].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*BatchCheckCharacterAvailabilityReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_bizscript_proto_msgTypes[9].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*BatchCheckCharacterAvailabilityResp); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_bizscript_proto_msgTypes[10].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetTopicListReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_bizscript_proto_msgTypes[11].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetTopicListResp); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_bizscript_proto_msgTypes[12].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CreateScriptReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_bizscript_proto_msgTypes[13].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CreateScriptResp); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_bizscript_proto_msgTypes[14].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*DeleteScriptReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_bizscript_proto_msgTypes[15].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*DeleteScriptResp); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_bizscript_proto_msgTypes[16].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetScriptListReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_bizscript_proto_msgTypes[17].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetScriptListResp); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_bizscript_proto_msgTypes[18].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetScriptDetailReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_bizscript_proto_msgTypes[19].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetScriptDetailResp); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_bizscript_proto_msgTypes[20].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*SearchReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_bizscript_proto_msgTypes[21].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*SearchResp); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_bizscript_proto_msgTypes[22].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*FollowScriptReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_bizscript_proto_msgTypes[23].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*FollowScriptResp); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_bizscript_proto_msgTypes[24].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*UnfollowScriptReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_bizscript_proto_msgTypes[25].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*UnfollowScriptResp); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_bizscript_proto_msgTypes[26].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ShareScriptReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_bizscript_proto_msgTypes[27].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ShareScriptResp); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_bizscript_proto_msgTypes[28].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetLineDubbingAllReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_bizscript_proto_msgTypes[29].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetLineDubbingAllResp); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_bizscript_proto_msgTypes[30].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetLineDubbingSimpleReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_bizscript_proto_msgTypes[31].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetLineDubbingSimpleResp); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_bizscript_proto_msgTypes[32].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetScriptFirstDubbingReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_bizscript_proto_msgTypes[33].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetScriptFirstDubbingResp); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_bizscript_proto_msgTypes[34].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*BatchGetScriptFirstDubbingReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_bizscript_proto_msgTypes[35].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*BatchGetScriptFirstDubbingResp); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_bizscript_proto_msgTypes[36].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CreateDubbingReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_bizscript_proto_msgTypes[37].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CreateDubbingResp); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_bizscript_proto_msgTypes[38].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*DeleteDubbingRecordReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_bizscript_proto_msgTypes[39].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*DeleteDubbingRecordResp); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_bizscript_proto_msgTypes[40].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetCommentListReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_bizscript_proto_msgTypes[41].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetCommentListResp); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_bizscript_proto_msgTypes[42].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetCommentRepliesReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_bizscript_proto_msgTypes[43].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetCommentRepliesResp); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_bizscript_proto_msgTypes[44].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CreateCommentReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_bizscript_proto_msgTypes[45].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CreateCommentResp); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_bizscript_proto_msgTypes[46].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*SetCommentTopReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_bizscript_proto_msgTypes[47].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*SetCommentTopResp); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_bizscript_proto_msgTypes[48].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*DeleteCommentReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_bizscript_proto_msgTypes[49].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*DeleteCommentResp); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_bizscript_proto_msgTypes[50].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetCommentASRReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_bizscript_proto_msgTypes[51].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetCommentASRResp); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_bizscript_proto_msgTypes[52].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*LikeReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_bizscript_proto_msgTypes[53].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*LikeResp); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_bizscript_proto_msgTypes[54].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*UnlikeReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_bizscript_proto_msgTypes[55].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*UnlikeResp); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_bizscript_proto_msgTypes[56].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*BatchGetLikesReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_bizscript_proto_msgTypes[57].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*BatchGetLikesResp); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_bizscript_proto_msgTypes[58].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetReportReasonsReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_bizscript_proto_msgTypes[59].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetReportReasonsResp); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_bizscript_proto_msgTypes[60].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ReportReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_bizscript_proto_msgTypes[61].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ReportResp); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_bizscript_proto_msgTypes[62].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetIPListReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_bizscript_proto_msgTypes[63].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetIPListResp); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_bizscript_proto_msgTypes[64].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetUserStatsReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_bizscript_proto_msgTypes[65].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetUserStatsResp); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_bizscript_proto_msgTypes[66].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetUserScriptListsReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_bizscript_proto_msgTypes[67].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetUserScriptListsResp); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_bizscript_proto_msgTypes[68].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetCoverListReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_bizscript_proto_msgTypes[69].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetCoverListResp); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_bizscript_proto_msgTypes[70].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GenerateScriptLinesReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_bizscript_proto_msgTypes[71].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GenerateScriptLinesResp); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_bizscript_proto_msgTypes[72].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GenerateScriptLinesRespData); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_bizscript_proto_msgTypes[73].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*DeleteDubbingReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_bizscript_proto_msgTypes[74].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*DeleteDubbingResp); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_bizscript_proto_msgTypes[75].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*SetDubbingTopReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_bizscript_proto_msgTypes[76].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*SetDubbingTopResp); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_bizscript_proto_rawDesc,
			NumEnums:      0,
			NumMessages:   80,
			NumExtensions: 0,
			NumServices:   1,
		},
		GoTypes:           file_bizscript_proto_goTypes,
		DependencyIndexes: file_bizscript_proto_depIdxs,
		MessageInfos:      file_bizscript_proto_msgTypes,
	}.Build()
	File_bizscript_proto = out.File
	file_bizscript_proto_rawDesc = nil
	file_bizscript_proto_goTypes = nil
	file_bizscript_proto_depIdxs = nil
}
