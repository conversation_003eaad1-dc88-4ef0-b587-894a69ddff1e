// Code generated by protoc-gen-validate. DO NOT EDIT.
// source: basemsgtopic.proto

package basemsgtopic

import (
	"bytes"
	"errors"
	"fmt"
	"net"
	"net/mail"
	"net/url"
	"regexp"
	"sort"
	"strings"
	"time"
	"unicode/utf8"

	"google.golang.org/protobuf/types/known/anypb"
)

// ensure the imports are used
var (
	_ = bytes.MinRead
	_ = errors.New("")
	_ = fmt.Print
	_ = utf8.UTFMax
	_ = (*regexp.Regexp)(nil)
	_ = (*strings.Reader)(nil)
	_ = net.IPv4len
	_ = time.Duration(0)
	_ = (*url.URL)(nil)
	_ = (*mail.Address)(nil)
	_ = anypb.Any{}
	_ = sort.Sort
)

// Validate checks the field values on TestReq with the rules defined in the
// proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *TestReq) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on TestReq with the rules defined in the
// proto definition for this message. If any rules are violated, the result is
// a list of violation errors wrapped in TestReqMultiError, or nil if none found.
func (m *TestReq) ValidateAll() error {
	return m.validate(true)
}

func (m *TestReq) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if len(errors) > 0 {
		return TestReqMultiError(errors)
	}

	return nil
}

// TestReqMultiError is an error wrapping multiple validation errors returned
// by TestReq.ValidateAll() if the designated constraints aren't met.
type TestReqMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m TestReqMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m TestReqMultiError) AllErrors() []error { return m }

// TestReqValidationError is the validation error returned by TestReq.Validate
// if the designated constraints aren't met.
type TestReqValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e TestReqValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e TestReqValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e TestReqValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e TestReqValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e TestReqValidationError) ErrorName() string { return "TestReqValidationError" }

// Error satisfies the builtin error interface
func (e TestReqValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sTestReq.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = TestReqValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = TestReqValidationError{}

// Validate checks the field values on TestRsp with the rules defined in the
// proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *TestRsp) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on TestRsp with the rules defined in the
// proto definition for this message. If any rules are violated, the result is
// a list of violation errors wrapped in TestRspMultiError, or nil if none found.
func (m *TestRsp) ValidateAll() error {
	return m.validate(true)
}

func (m *TestRsp) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Result

	// no validation rules for Message

	if len(errors) > 0 {
		return TestRspMultiError(errors)
	}

	return nil
}

// TestRspMultiError is an error wrapping multiple validation errors returned
// by TestRsp.ValidateAll() if the designated constraints aren't met.
type TestRspMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m TestRspMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m TestRspMultiError) AllErrors() []error { return m }

// TestRspValidationError is the validation error returned by TestRsp.Validate
// if the designated constraints aren't met.
type TestRspValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e TestRspValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e TestRspValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e TestRspValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e TestRspValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e TestRspValidationError) ErrorName() string { return "TestRspValidationError" }

// Error satisfies the builtin error interface
func (e TestRspValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sTestRsp.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = TestRspValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = TestRspValidationError{}

// Validate checks the field values on ConnStatusReportReq with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *ConnStatusReportReq) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on ConnStatusReportReq with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// ConnStatusReportReqMultiError, or nil if none found.
func (m *ConnStatusReportReq) ValidateAll() error {
	return m.validate(true)
}

func (m *ConnStatusReportReq) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Type

	// no validation rules for UserToken

	// no validation rules for ConnId

	// no validation rules for BrokerIP

	// no validation rules for OpTime

	if len(errors) > 0 {
		return ConnStatusReportReqMultiError(errors)
	}

	return nil
}

// ConnStatusReportReqMultiError is an error wrapping multiple validation
// errors returned by ConnStatusReportReq.ValidateAll() if the designated
// constraints aren't met.
type ConnStatusReportReqMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m ConnStatusReportReqMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m ConnStatusReportReqMultiError) AllErrors() []error { return m }

// ConnStatusReportReqValidationError is the validation error returned by
// ConnStatusReportReq.Validate if the designated constraints aren't met.
type ConnStatusReportReqValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e ConnStatusReportReqValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e ConnStatusReportReqValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e ConnStatusReportReqValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e ConnStatusReportReqValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e ConnStatusReportReqValidationError) ErrorName() string {
	return "ConnStatusReportReqValidationError"
}

// Error satisfies the builtin error interface
func (e ConnStatusReportReqValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sConnStatusReportReq.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = ConnStatusReportReqValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = ConnStatusReportReqValidationError{}

// Validate checks the field values on ConnStatusReportRsp with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *ConnStatusReportRsp) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on ConnStatusReportRsp with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// ConnStatusReportRspMultiError, or nil if none found.
func (m *ConnStatusReportRsp) ValidateAll() error {
	return m.validate(true)
}

func (m *ConnStatusReportRsp) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Result

	// no validation rules for Message

	if len(errors) > 0 {
		return ConnStatusReportRspMultiError(errors)
	}

	return nil
}

// ConnStatusReportRspMultiError is an error wrapping multiple validation
// errors returned by ConnStatusReportRsp.ValidateAll() if the designated
// constraints aren't met.
type ConnStatusReportRspMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m ConnStatusReportRspMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m ConnStatusReportRspMultiError) AllErrors() []error { return m }

// ConnStatusReportRspValidationError is the validation error returned by
// ConnStatusReportRsp.Validate if the designated constraints aren't met.
type ConnStatusReportRspValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e ConnStatusReportRspValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e ConnStatusReportRspValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e ConnStatusReportRspValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e ConnStatusReportRspValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e ConnStatusReportRspValidationError) ErrorName() string {
	return "ConnStatusReportRspValidationError"
}

// Error satisfies the builtin error interface
func (e ConnStatusReportRspValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sConnStatusReportRsp.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = ConnStatusReportRspValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = ConnStatusReportRspValidationError{}

// Validate checks the field values on SubscribeReq with the rules defined in
// the proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *SubscribeReq) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on SubscribeReq with the rules defined
// in the proto definition for this message. If any rules are violated, the
// result is a list of violation errors wrapped in SubscribeReqMultiError, or
// nil if none found.
func (m *SubscribeReq) ValidateAll() error {
	return m.validate(true)
}

func (m *SubscribeReq) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for PlatformId

	// no validation rules for UserId

	// no validation rules for DeviceId

	if len(errors) > 0 {
		return SubscribeReqMultiError(errors)
	}

	return nil
}

// SubscribeReqMultiError is an error wrapping multiple validation errors
// returned by SubscribeReq.ValidateAll() if the designated constraints aren't met.
type SubscribeReqMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m SubscribeReqMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m SubscribeReqMultiError) AllErrors() []error { return m }

// SubscribeReqValidationError is the validation error returned by
// SubscribeReq.Validate if the designated constraints aren't met.
type SubscribeReqValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e SubscribeReqValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e SubscribeReqValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e SubscribeReqValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e SubscribeReqValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e SubscribeReqValidationError) ErrorName() string { return "SubscribeReqValidationError" }

// Error satisfies the builtin error interface
func (e SubscribeReqValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sSubscribeReq.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = SubscribeReqValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = SubscribeReqValidationError{}

// Validate checks the field values on SubscribeRsp with the rules defined in
// the proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *SubscribeRsp) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on SubscribeRsp with the rules defined
// in the proto definition for this message. If any rules are violated, the
// result is a list of violation errors wrapped in SubscribeRspMultiError, or
// nil if none found.
func (m *SubscribeRsp) ValidateAll() error {
	return m.validate(true)
}

func (m *SubscribeRsp) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Result

	// no validation rules for Message

	if len(errors) > 0 {
		return SubscribeRspMultiError(errors)
	}

	return nil
}

// SubscribeRspMultiError is an error wrapping multiple validation errors
// returned by SubscribeRsp.ValidateAll() if the designated constraints aren't met.
type SubscribeRspMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m SubscribeRspMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m SubscribeRspMultiError) AllErrors() []error { return m }

// SubscribeRspValidationError is the validation error returned by
// SubscribeRsp.Validate if the designated constraints aren't met.
type SubscribeRspValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e SubscribeRspValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e SubscribeRspValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e SubscribeRspValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e SubscribeRspValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e SubscribeRspValidationError) ErrorName() string { return "SubscribeRspValidationError" }

// Error satisfies the builtin error interface
func (e SubscribeRspValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sSubscribeRsp.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = SubscribeRspValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = SubscribeRspValidationError{}

// Validate checks the field values on UnSubscribeReq with the rules defined in
// the proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *UnSubscribeReq) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on UnSubscribeReq with the rules defined
// in the proto definition for this message. If any rules are violated, the
// result is a list of violation errors wrapped in UnSubscribeReqMultiError,
// or nil if none found.
func (m *UnSubscribeReq) ValidateAll() error {
	return m.validate(true)
}

func (m *UnSubscribeReq) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for PlatformId

	// no validation rules for UserId

	// no validation rules for DeviceId

	if len(errors) > 0 {
		return UnSubscribeReqMultiError(errors)
	}

	return nil
}

// UnSubscribeReqMultiError is an error wrapping multiple validation errors
// returned by UnSubscribeReq.ValidateAll() if the designated constraints
// aren't met.
type UnSubscribeReqMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m UnSubscribeReqMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m UnSubscribeReqMultiError) AllErrors() []error { return m }

// UnSubscribeReqValidationError is the validation error returned by
// UnSubscribeReq.Validate if the designated constraints aren't met.
type UnSubscribeReqValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e UnSubscribeReqValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e UnSubscribeReqValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e UnSubscribeReqValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e UnSubscribeReqValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e UnSubscribeReqValidationError) ErrorName() string { return "UnSubscribeReqValidationError" }

// Error satisfies the builtin error interface
func (e UnSubscribeReqValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sUnSubscribeReq.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = UnSubscribeReqValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = UnSubscribeReqValidationError{}

// Validate checks the field values on UnSubscribeRsp with the rules defined in
// the proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *UnSubscribeRsp) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on UnSubscribeRsp with the rules defined
// in the proto definition for this message. If any rules are violated, the
// result is a list of violation errors wrapped in UnSubscribeRspMultiError,
// or nil if none found.
func (m *UnSubscribeRsp) ValidateAll() error {
	return m.validate(true)
}

func (m *UnSubscribeRsp) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Result

	// no validation rules for Message

	if len(errors) > 0 {
		return UnSubscribeRspMultiError(errors)
	}

	return nil
}

// UnSubscribeRspMultiError is an error wrapping multiple validation errors
// returned by UnSubscribeRsp.ValidateAll() if the designated constraints
// aren't met.
type UnSubscribeRspMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m UnSubscribeRspMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m UnSubscribeRspMultiError) AllErrors() []error { return m }

// UnSubscribeRspValidationError is the validation error returned by
// UnSubscribeRsp.Validate if the designated constraints aren't met.
type UnSubscribeRspValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e UnSubscribeRspValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e UnSubscribeRspValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e UnSubscribeRspValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e UnSubscribeRspValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e UnSubscribeRspValidationError) ErrorName() string { return "UnSubscribeRspValidationError" }

// Error satisfies the builtin error interface
func (e UnSubscribeRspValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sUnSubscribeRsp.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = UnSubscribeRspValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = UnSubscribeRspValidationError{}

// Validate checks the field values on TopicUsersReq with the rules defined in
// the proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *TopicUsersReq) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on TopicUsersReq with the rules defined
// in the proto definition for this message. If any rules are violated, the
// result is a list of violation errors wrapped in TopicUsersReqMultiError, or
// nil if none found.
func (m *TopicUsersReq) ValidateAll() error {
	return m.validate(true)
}

func (m *TopicUsersReq) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for PlatformId

	// no validation rules for Topic

	if len(errors) > 0 {
		return TopicUsersReqMultiError(errors)
	}

	return nil
}

// TopicUsersReqMultiError is an error wrapping multiple validation errors
// returned by TopicUsersReq.ValidateAll() if the designated constraints
// aren't met.
type TopicUsersReqMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m TopicUsersReqMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m TopicUsersReqMultiError) AllErrors() []error { return m }

// TopicUsersReqValidationError is the validation error returned by
// TopicUsersReq.Validate if the designated constraints aren't met.
type TopicUsersReqValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e TopicUsersReqValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e TopicUsersReqValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e TopicUsersReqValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e TopicUsersReqValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e TopicUsersReqValidationError) ErrorName() string { return "TopicUsersReqValidationError" }

// Error satisfies the builtin error interface
func (e TopicUsersReqValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sTopicUsersReq.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = TopicUsersReqValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = TopicUsersReqValidationError{}

// Validate checks the field values on TopicUsersRsp with the rules defined in
// the proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *TopicUsersRsp) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on TopicUsersRsp with the rules defined
// in the proto definition for this message. If any rules are violated, the
// result is a list of violation errors wrapped in TopicUsersRspMultiError, or
// nil if none found.
func (m *TopicUsersRsp) ValidateAll() error {
	return m.validate(true)
}

func (m *TopicUsersRsp) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Result

	// no validation rules for Message

	if all {
		switch v := interface{}(m.GetData()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, TopicUsersRspValidationError{
					field:  "Data",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, TopicUsersRspValidationError{
					field:  "Data",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetData()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return TopicUsersRspValidationError{
				field:  "Data",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return TopicUsersRspMultiError(errors)
	}

	return nil
}

// TopicUsersRspMultiError is an error wrapping multiple validation errors
// returned by TopicUsersRsp.ValidateAll() if the designated constraints
// aren't met.
type TopicUsersRspMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m TopicUsersRspMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m TopicUsersRspMultiError) AllErrors() []error { return m }

// TopicUsersRspValidationError is the validation error returned by
// TopicUsersRsp.Validate if the designated constraints aren't met.
type TopicUsersRspValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e TopicUsersRspValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e TopicUsersRspValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e TopicUsersRspValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e TopicUsersRspValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e TopicUsersRspValidationError) ErrorName() string { return "TopicUsersRspValidationError" }

// Error satisfies the builtin error interface
func (e TopicUsersRspValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sTopicUsersRsp.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = TopicUsersRspValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = TopicUsersRspValidationError{}

// Validate checks the field values on TopicUsersData with the rules defined in
// the proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *TopicUsersData) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on TopicUsersData with the rules defined
// in the proto definition for this message. If any rules are violated, the
// result is a list of violation errors wrapped in TopicUsersDataMultiError,
// or nil if none found.
func (m *TopicUsersData) ValidateAll() error {
	return m.validate(true)
}

func (m *TopicUsersData) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if len(errors) > 0 {
		return TopicUsersDataMultiError(errors)
	}

	return nil
}

// TopicUsersDataMultiError is an error wrapping multiple validation errors
// returned by TopicUsersData.ValidateAll() if the designated constraints
// aren't met.
type TopicUsersDataMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m TopicUsersDataMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m TopicUsersDataMultiError) AllErrors() []error { return m }

// TopicUsersDataValidationError is the validation error returned by
// TopicUsersData.Validate if the designated constraints aren't met.
type TopicUsersDataValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e TopicUsersDataValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e TopicUsersDataValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e TopicUsersDataValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e TopicUsersDataValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e TopicUsersDataValidationError) ErrorName() string { return "TopicUsersDataValidationError" }

// Error satisfies the builtin error interface
func (e TopicUsersDataValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sTopicUsersData.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = TopicUsersDataValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = TopicUsersDataValidationError{}

// Validate checks the field values on TopicUsersWithConnInfoRsp with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *TopicUsersWithConnInfoRsp) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on TopicUsersWithConnInfoRsp with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// TopicUsersWithConnInfoRspMultiError, or nil if none found.
func (m *TopicUsersWithConnInfoRsp) ValidateAll() error {
	return m.validate(true)
}

func (m *TopicUsersWithConnInfoRsp) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Result

	// no validation rules for Message

	if all {
		switch v := interface{}(m.GetData()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, TopicUsersWithConnInfoRspValidationError{
					field:  "Data",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, TopicUsersWithConnInfoRspValidationError{
					field:  "Data",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetData()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return TopicUsersWithConnInfoRspValidationError{
				field:  "Data",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return TopicUsersWithConnInfoRspMultiError(errors)
	}

	return nil
}

// TopicUsersWithConnInfoRspMultiError is an error wrapping multiple validation
// errors returned by TopicUsersWithConnInfoRsp.ValidateAll() if the
// designated constraints aren't met.
type TopicUsersWithConnInfoRspMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m TopicUsersWithConnInfoRspMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m TopicUsersWithConnInfoRspMultiError) AllErrors() []error { return m }

// TopicUsersWithConnInfoRspValidationError is the validation error returned by
// TopicUsersWithConnInfoRsp.Validate if the designated constraints aren't met.
type TopicUsersWithConnInfoRspValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e TopicUsersWithConnInfoRspValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e TopicUsersWithConnInfoRspValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e TopicUsersWithConnInfoRspValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e TopicUsersWithConnInfoRspValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e TopicUsersWithConnInfoRspValidationError) ErrorName() string {
	return "TopicUsersWithConnInfoRspValidationError"
}

// Error satisfies the builtin error interface
func (e TopicUsersWithConnInfoRspValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sTopicUsersWithConnInfoRsp.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = TopicUsersWithConnInfoRspValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = TopicUsersWithConnInfoRspValidationError{}

// Validate checks the field values on TopicUsersWithConnInfoData with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *TopicUsersWithConnInfoData) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on TopicUsersWithConnInfoData with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// TopicUsersWithConnInfoDataMultiError, or nil if none found.
func (m *TopicUsersWithConnInfoData) ValidateAll() error {
	return m.validate(true)
}

func (m *TopicUsersWithConnInfoData) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	for idx, item := range m.GetConns() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, TopicUsersWithConnInfoDataValidationError{
						field:  fmt.Sprintf("Conns[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, TopicUsersWithConnInfoDataValidationError{
						field:  fmt.Sprintf("Conns[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return TopicUsersWithConnInfoDataValidationError{
					field:  fmt.Sprintf("Conns[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	if len(errors) > 0 {
		return TopicUsersWithConnInfoDataMultiError(errors)
	}

	return nil
}

// TopicUsersWithConnInfoDataMultiError is an error wrapping multiple
// validation errors returned by TopicUsersWithConnInfoData.ValidateAll() if
// the designated constraints aren't met.
type TopicUsersWithConnInfoDataMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m TopicUsersWithConnInfoDataMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m TopicUsersWithConnInfoDataMultiError) AllErrors() []error { return m }

// TopicUsersWithConnInfoDataValidationError is the validation error returned
// by TopicUsersWithConnInfoData.Validate if the designated constraints aren't met.
type TopicUsersWithConnInfoDataValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e TopicUsersWithConnInfoDataValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e TopicUsersWithConnInfoDataValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e TopicUsersWithConnInfoDataValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e TopicUsersWithConnInfoDataValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e TopicUsersWithConnInfoDataValidationError) ErrorName() string {
	return "TopicUsersWithConnInfoDataValidationError"
}

// Error satisfies the builtin error interface
func (e TopicUsersWithConnInfoDataValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sTopicUsersWithConnInfoData.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = TopicUsersWithConnInfoDataValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = TopicUsersWithConnInfoDataValidationError{}

// Validate checks the field values on GetPlatformUserConnReq with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *GetPlatformUserConnReq) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GetPlatformUserConnReq with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// GetPlatformUserConnReqMultiError, or nil if none found.
func (m *GetPlatformUserConnReq) ValidateAll() error {
	return m.validate(true)
}

func (m *GetPlatformUserConnReq) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for PlatformId

	// no validation rules for UserId

	// no validation rules for DeviceId

	if len(errors) > 0 {
		return GetPlatformUserConnReqMultiError(errors)
	}

	return nil
}

// GetPlatformUserConnReqMultiError is an error wrapping multiple validation
// errors returned by GetPlatformUserConnReq.ValidateAll() if the designated
// constraints aren't met.
type GetPlatformUserConnReqMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetPlatformUserConnReqMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetPlatformUserConnReqMultiError) AllErrors() []error { return m }

// GetPlatformUserConnReqValidationError is the validation error returned by
// GetPlatformUserConnReq.Validate if the designated constraints aren't met.
type GetPlatformUserConnReqValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetPlatformUserConnReqValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetPlatformUserConnReqValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetPlatformUserConnReqValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetPlatformUserConnReqValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetPlatformUserConnReqValidationError) ErrorName() string {
	return "GetPlatformUserConnReqValidationError"
}

// Error satisfies the builtin error interface
func (e GetPlatformUserConnReqValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetPlatformUserConnReq.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetPlatformUserConnReqValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetPlatformUserConnReqValidationError{}

// Validate checks the field values on GetPlatformUserConnRsp with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *GetPlatformUserConnRsp) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GetPlatformUserConnRsp with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// GetPlatformUserConnRspMultiError, or nil if none found.
func (m *GetPlatformUserConnRsp) ValidateAll() error {
	return m.validate(true)
}

func (m *GetPlatformUserConnRsp) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Result

	// no validation rules for Message

	if all {
		switch v := interface{}(m.GetData()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetPlatformUserConnRspValidationError{
					field:  "Data",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetPlatformUserConnRspValidationError{
					field:  "Data",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetData()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetPlatformUserConnRspValidationError{
				field:  "Data",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return GetPlatformUserConnRspMultiError(errors)
	}

	return nil
}

// GetPlatformUserConnRspMultiError is an error wrapping multiple validation
// errors returned by GetPlatformUserConnRsp.ValidateAll() if the designated
// constraints aren't met.
type GetPlatformUserConnRspMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetPlatformUserConnRspMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetPlatformUserConnRspMultiError) AllErrors() []error { return m }

// GetPlatformUserConnRspValidationError is the validation error returned by
// GetPlatformUserConnRsp.Validate if the designated constraints aren't met.
type GetPlatformUserConnRspValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetPlatformUserConnRspValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetPlatformUserConnRspValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetPlatformUserConnRspValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetPlatformUserConnRspValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetPlatformUserConnRspValidationError) ErrorName() string {
	return "GetPlatformUserConnRspValidationError"
}

// Error satisfies the builtin error interface
func (e GetPlatformUserConnRspValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetPlatformUserConnRsp.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetPlatformUserConnRspValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetPlatformUserConnRspValidationError{}

// Validate checks the field values on PlatformUserConnData with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *PlatformUserConnData) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on PlatformUserConnData with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// PlatformUserConnDataMultiError, or nil if none found.
func (m *PlatformUserConnData) ValidateAll() error {
	return m.validate(true)
}

func (m *PlatformUserConnData) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	for idx, item := range m.GetConns() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, PlatformUserConnDataValidationError{
						field:  fmt.Sprintf("Conns[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, PlatformUserConnDataValidationError{
						field:  fmt.Sprintf("Conns[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return PlatformUserConnDataValidationError{
					field:  fmt.Sprintf("Conns[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	if len(errors) > 0 {
		return PlatformUserConnDataMultiError(errors)
	}

	return nil
}

// PlatformUserConnDataMultiError is an error wrapping multiple validation
// errors returned by PlatformUserConnData.ValidateAll() if the designated
// constraints aren't met.
type PlatformUserConnDataMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m PlatformUserConnDataMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m PlatformUserConnDataMultiError) AllErrors() []error { return m }

// PlatformUserConnDataValidationError is the validation error returned by
// PlatformUserConnData.Validate if the designated constraints aren't met.
type PlatformUserConnDataValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e PlatformUserConnDataValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e PlatformUserConnDataValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e PlatformUserConnDataValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e PlatformUserConnDataValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e PlatformUserConnDataValidationError) ErrorName() string {
	return "PlatformUserConnDataValidationError"
}

// Error satisfies the builtin error interface
func (e PlatformUserConnDataValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sPlatformUserConnData.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = PlatformUserConnDataValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = PlatformUserConnDataValidationError{}

// Validate checks the field values on PlatformUserConn with the rules defined
// in the proto definition for this message. If any rules are violated, the
// first error encountered is returned, or nil if there are no violations.
func (m *PlatformUserConn) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on PlatformUserConn with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// PlatformUserConnMultiError, or nil if none found.
func (m *PlatformUserConn) ValidateAll() error {
	return m.validate(true)
}

func (m *PlatformUserConn) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for PlatformId

	// no validation rules for UserId

	// no validation rules for DeviceId

	// no validation rules for ClientId

	// no validation rules for ClientVersion

	// no validation rules for BrokerIp

	// no validation rules for ConnId

	// no validation rules for ConnTime

	if len(errors) > 0 {
		return PlatformUserConnMultiError(errors)
	}

	return nil
}

// PlatformUserConnMultiError is an error wrapping multiple validation errors
// returned by PlatformUserConn.ValidateAll() if the designated constraints
// aren't met.
type PlatformUserConnMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m PlatformUserConnMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m PlatformUserConnMultiError) AllErrors() []error { return m }

// PlatformUserConnValidationError is the validation error returned by
// PlatformUserConn.Validate if the designated constraints aren't met.
type PlatformUserConnValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e PlatformUserConnValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e PlatformUserConnValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e PlatformUserConnValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e PlatformUserConnValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e PlatformUserConnValidationError) ErrorName() string { return "PlatformUserConnValidationError" }

// Error satisfies the builtin error interface
func (e PlatformUserConnValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sPlatformUserConn.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = PlatformUserConnValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = PlatformUserConnValidationError{}

// Validate checks the field values on UserTopicsReq with the rules defined in
// the proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *UserTopicsReq) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on UserTopicsReq with the rules defined
// in the proto definition for this message. If any rules are violated, the
// result is a list of violation errors wrapped in UserTopicsReqMultiError, or
// nil if none found.
func (m *UserTopicsReq) ValidateAll() error {
	return m.validate(true)
}

func (m *UserTopicsReq) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for PlatformId

	// no validation rules for UserId

	// no validation rules for DeviceId

	if len(errors) > 0 {
		return UserTopicsReqMultiError(errors)
	}

	return nil
}

// UserTopicsReqMultiError is an error wrapping multiple validation errors
// returned by UserTopicsReq.ValidateAll() if the designated constraints
// aren't met.
type UserTopicsReqMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m UserTopicsReqMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m UserTopicsReqMultiError) AllErrors() []error { return m }

// UserTopicsReqValidationError is the validation error returned by
// UserTopicsReq.Validate if the designated constraints aren't met.
type UserTopicsReqValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e UserTopicsReqValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e UserTopicsReqValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e UserTopicsReqValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e UserTopicsReqValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e UserTopicsReqValidationError) ErrorName() string { return "UserTopicsReqValidationError" }

// Error satisfies the builtin error interface
func (e UserTopicsReqValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sUserTopicsReq.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = UserTopicsReqValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = UserTopicsReqValidationError{}

// Validate checks the field values on UserTopicsRsp with the rules defined in
// the proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *UserTopicsRsp) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on UserTopicsRsp with the rules defined
// in the proto definition for this message. If any rules are violated, the
// result is a list of violation errors wrapped in UserTopicsRspMultiError, or
// nil if none found.
func (m *UserTopicsRsp) ValidateAll() error {
	return m.validate(true)
}

func (m *UserTopicsRsp) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Result

	// no validation rules for Message

	if all {
		switch v := interface{}(m.GetData()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, UserTopicsRspValidationError{
					field:  "Data",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, UserTopicsRspValidationError{
					field:  "Data",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetData()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return UserTopicsRspValidationError{
				field:  "Data",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return UserTopicsRspMultiError(errors)
	}

	return nil
}

// UserTopicsRspMultiError is an error wrapping multiple validation errors
// returned by UserTopicsRsp.ValidateAll() if the designated constraints
// aren't met.
type UserTopicsRspMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m UserTopicsRspMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m UserTopicsRspMultiError) AllErrors() []error { return m }

// UserTopicsRspValidationError is the validation error returned by
// UserTopicsRsp.Validate if the designated constraints aren't met.
type UserTopicsRspValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e UserTopicsRspValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e UserTopicsRspValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e UserTopicsRspValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e UserTopicsRspValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e UserTopicsRspValidationError) ErrorName() string { return "UserTopicsRspValidationError" }

// Error satisfies the builtin error interface
func (e UserTopicsRspValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sUserTopicsRsp.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = UserTopicsRspValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = UserTopicsRspValidationError{}

// Validate checks the field values on UserTopicsData with the rules defined in
// the proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *UserTopicsData) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on UserTopicsData with the rules defined
// in the proto definition for this message. If any rules are violated, the
// result is a list of violation errors wrapped in UserTopicsDataMultiError,
// or nil if none found.
func (m *UserTopicsData) ValidateAll() error {
	return m.validate(true)
}

func (m *UserTopicsData) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	for idx, item := range m.GetList() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, UserTopicsDataValidationError{
						field:  fmt.Sprintf("List[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, UserTopicsDataValidationError{
						field:  fmt.Sprintf("List[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return UserTopicsDataValidationError{
					field:  fmt.Sprintf("List[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	if len(errors) > 0 {
		return UserTopicsDataMultiError(errors)
	}

	return nil
}

// UserTopicsDataMultiError is an error wrapping multiple validation errors
// returned by UserTopicsData.ValidateAll() if the designated constraints
// aren't met.
type UserTopicsDataMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m UserTopicsDataMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m UserTopicsDataMultiError) AllErrors() []error { return m }

// UserTopicsDataValidationError is the validation error returned by
// UserTopicsData.Validate if the designated constraints aren't met.
type UserTopicsDataValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e UserTopicsDataValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e UserTopicsDataValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e UserTopicsDataValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e UserTopicsDataValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e UserTopicsDataValidationError) ErrorName() string { return "UserTopicsDataValidationError" }

// Error satisfies the builtin error interface
func (e UserTopicsDataValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sUserTopicsData.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = UserTopicsDataValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = UserTopicsDataValidationError{}

// Validate checks the field values on UserDeviceTopicsData with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *UserDeviceTopicsData) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on UserDeviceTopicsData with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// UserDeviceTopicsDataMultiError, or nil if none found.
func (m *UserDeviceTopicsData) ValidateAll() error {
	return m.validate(true)
}

func (m *UserDeviceTopicsData) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for PlatformId

	// no validation rules for UserId

	// no validation rules for DeviceId

	if len(errors) > 0 {
		return UserDeviceTopicsDataMultiError(errors)
	}

	return nil
}

// UserDeviceTopicsDataMultiError is an error wrapping multiple validation
// errors returned by UserDeviceTopicsData.ValidateAll() if the designated
// constraints aren't met.
type UserDeviceTopicsDataMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m UserDeviceTopicsDataMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m UserDeviceTopicsDataMultiError) AllErrors() []error { return m }

// UserDeviceTopicsDataValidationError is the validation error returned by
// UserDeviceTopicsData.Validate if the designated constraints aren't met.
type UserDeviceTopicsDataValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e UserDeviceTopicsDataValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e UserDeviceTopicsDataValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e UserDeviceTopicsDataValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e UserDeviceTopicsDataValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e UserDeviceTopicsDataValidationError) ErrorName() string {
	return "UserDeviceTopicsDataValidationError"
}

// Error satisfies the builtin error interface
func (e UserDeviceTopicsDataValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sUserDeviceTopicsData.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = UserDeviceTopicsDataValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = UserDeviceTopicsDataValidationError{}
