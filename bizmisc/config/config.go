package config

import (
	"new-gitlab.xunlei.cn/vcproject/backends/baselib/consul"
	"new-gitlab.xunlei.cn/vcproject/backends/baselib/logger"
)

// ReviewSwitchConfig 审核开关配置
type MiscConfig struct {
	// 剧本审核开关（标题）
	ForceNoRvc     bool   `yaml:"force_no_rvc" json:"force_no_rvc"`         // 变声是否强制不用rvc
	OfficialApkUrl string `yaml:"official_apk_url" json:"official_apk_url"` //官方apk下载地址
}

var miscConfig MiscConfig

func InitConfig() {
	consul.WatchYaml("config.yaml", &miscConfig, func() {
		logger.Infof("config=%+v", miscConfig)
	})
}

func IsForceNoRvc() bool {
	return miscConfig.ForceNoRvc
}

func GetOfficialApkUrl() string {
	return miscConfig.OfficialApkUrl
}
