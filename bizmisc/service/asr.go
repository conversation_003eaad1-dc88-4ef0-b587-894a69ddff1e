package service

import (
	"context"
	"crypto/hmac"
	"crypto/sha1"
	"encoding/base64"
	"fmt"
	"net/url"
	"time"

	"new-gitlab.xunlei.cn/vcproject/backends/baselib/config"
	"new-gitlab.xunlei.cn/vcproject/backends/baselib/errcode"
	"new-gitlab.xunlei.cn/vcproject/backends/baselib/logger"
	"new-gitlab.xunlei.cn/vcproject/backends/proto/api/bizmisc"
)

const (
	AsrAddr = "wss://dashscope.aliyuncs.com/api-ws/v1/inference"
)

func GetAsrAddr(ctx context.Context, req *bizmisc.GetAsrAddrReq) (resp *bizmisc.GetAsrAddrResp, err error) {
	resp = &bizmisc.GetAsrAddrResp{}
	errcode.ErrOK.SetTo(resp)
	asrConfig := config.GetAsrConfig()
	if asrConfig == nil {
		logger.Errorf("asr config not found")
		errcode.ErrorInternal.SetTo(resp)
		return
	}
	timestamp := time.Now().Unix()
	signature, tmpErr := generateSignature(timestamp, asrConfig)
	if tmpErr != nil {
		logger.Errorf("generateSignature failed: %v", tmpErr)
		errcode.ErrorInternal.SetTo(resp)
		return
	}
	wsURL := fmt.Sprintf("%s?appid=%s&timestamp=%d&signature=%s", AsrAddr, asrConfig.AsrAppId, timestamp, signature)
	resp.Data = &bizmisc.GetAsrAddrRespData{
		Addr: wsURL,
	}
	return
}

func generateSignature(timestamp int64, asrConfig *config.AsrConfig) (signature string, err error) {
	stringToSign := fmt.Sprintf("%s&%d", asrConfig.AsrAppId, timestamp)
	h := hmac.New(sha1.New, []byte(asrConfig.AsrAppId))
	h.Write([]byte(stringToSign))
	signature = base64.StdEncoding.EncodeToString(h.Sum(nil))
	return url.QueryEscape(signature), nil
}
