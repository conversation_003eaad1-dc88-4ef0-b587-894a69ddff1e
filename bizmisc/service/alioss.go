package service

import (
	"context"
	"crypto/hmac"
	"crypto/rand"
	"crypto/sha1"
	"encoding/base64"
	"fmt"
	"strings"
	"time"

	"new-gitlab.xunlei.cn/vcproject/backends/baselib/alioss"
	"new-gitlab.xunlei.cn/vcproject/backends/baselib/config"
	"new-gitlab.xunlei.cn/vcproject/backends/baselib/errcode"
	"new-gitlab.xunlei.cn/vcproject/backends/baselib/logger"
	"new-gitlab.xunlei.cn/vcproject/backends/baselib/server/env"
	"new-gitlab.xunlei.cn/vcproject/backends/proto/api/bizmisc"
	"new-gitlab.xunlei.cn/vcproject/backends/proto/consts"
)

const (
	BucketTypeImage = "image"
	BucketTypeAudio = "audio"
	BucketTypeVideo = "video"
	BucketTypeData  = "data"

	BucketBizTypeAvatar         = "avatar"          // 头像
	BucketBizTypeBackground     = "background"      // 背景图
	BucketBizTypeSignature      = "signature"       // 语音签名
	BucketBizTypeScriptCover    = "scenario_cover"  // 剧本封面
	BucketBizTypeScriptDubbing  = "script_dubbuing" // 剧本配音
	BucketBizTypeCommentDubbing = "comment_dubbing" // 评论配音
)

func GetAliObjectId(ctx context.Context, req *bizmisc.AliObjectIdReq) (resp *bizmisc.AliObjectIdResp, err error) {
	resp = &bizmisc.AliObjectIdResp{
		Code: errcode.ErrOK.Code,
		Msg:  errcode.ErrOK.Msg,
	}
	ossConfig := alioss.GetAliOssConfig(consts.MediaType(req.Type))
	if ossConfig == nil {
		logger.Warnf("oss config not found, type: %s", req.Type)
		resp.Code = errcode.ErrorParam.Code
		resp.Msg = errcode.ErrorParam.Msg
		return
	}
	objectKey, objectId, tmpErr := getObjectKey(req.BizType)
	if tmpErr != nil {
		logger.Errorf("get object key failed: %v", tmpErr)
		resp.Code = errcode.ErrorParam.Code
		resp.Msg = errcode.ErrorParam.Msg
		return
	}
	endPoint := ossConfig.Endpoint
	if env.IsProd() {
		endPoint = "https://oss-cn-shanghai.aliyuncs.com"
	}
	resp.Data = &bizmisc.AliObjectIdRespData{
		Endpoint:  endPoint,
		Bucket:    ossConfig.Bucket,
		ObjectKey: objectKey,
		ObjectId:  objectId,
	}
	return
}

// https://help.aliyun.com/zh/oss/use-cases/obtain-signature-information-from-the-server-and-upload-data-to-oss
func GenerateAliOssSignature(ctx context.Context, req *bizmisc.AliOssSigatureReq) (resp *bizmisc.AliOssSigatureResp, err error) {
	resp = &bizmisc.AliOssSigatureResp{
		Code: errcode.ErrOK.Code,
		Msg:  errcode.ErrOK.Msg,
	}
	ossConfig := alioss.GetAliOssConfig(consts.MediaType(req.Type))
	if ossConfig == nil {
		logger.Warnf("oss config not found, biz_type: %s", req.Type)
		resp.Code = errcode.ErrorParam.Code
		resp.Msg = errcode.ErrorParam.Msg
		return
	}
	if !strings.HasPrefix(req.Content, "PUT") && !strings.HasPrefix(req.Content, "GET") {
		logger.Warnf("invalid content, content: %s", req.Content)
		resp.Code = errcode.ErrorParam.Code
		resp.Msg = errcode.ErrorParam.Msg
		return
	}
	signature := MakeSignature(req.Content, ossConfig)
	resp.Data = &bizmisc.AliOssSigatureRespData{
		Signature: signature,
	}
	return
}

func MakeSignature(content string, ossConfig *config.AliOssConfig) string {
	mac := hmac.New(sha1.New, []byte(ossConfig.AccessKeySecret))
	mac.Write([]byte(content))
	return fmt.Sprintf("OSS %s:%s", ossConfig.AccessKeyId, base64.StdEncoding.EncodeToString(mac.Sum(nil)))
}

func getObjectKey(bizType string) (objectKey, objectId string, err error) {
	randomStr := make([]byte, 4)
	if _, err = rand.Read(randomStr); err != nil {
		logger.Errorf("generate random string failed: %v", err)
		return
	}
	switch bizType {
	case BucketBizTypeAvatar, BucketBizTypeScriptCover, BucketBizTypeBackground:
		objectId = fmt.Sprintf("%d_%x", time.Now().UnixNano()/1e6, randomStr)
		if env.IsProd() {
			objectKey = fmt.Sprintf("%s/%d/%s", bizType, time.Now().Year(), objectId)
		} else {
			objectKey = fmt.Sprintf("images/%s/%s", bizType, objectId)
		}
	case BucketBizTypeScriptDubbing, BucketBizTypeCommentDubbing, BucketBizTypeSignature:
		objectId = fmt.Sprintf("%d_%x", time.Now().UnixNano()/1e6, randomStr)
		if env.IsProd() {
			objectKey = fmt.Sprintf("%s/%d/%s", bizType, time.Now().Year(), objectId)
		} else {
			objectKey = fmt.Sprintf("audio/%s/%s", bizType, objectId)
		}
	default:
		err = fmt.Errorf("invalid biz_type: %s", bizType)
	}
	return
}
