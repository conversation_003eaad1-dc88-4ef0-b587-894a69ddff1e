package controller

import (
	"context"
	"errors"
	"fmt"
	"strings"

	"new-gitlab.xunlei.cn/vcproject/backends/baselib/errcode"
	"new-gitlab.xunlei.cn/vcproject/backends/baselib/logger"
	"new-gitlab.xunlei.cn/vcproject/backends/baselib/msgchan"
	"new-gitlab.xunlei.cn/vcproject/backends/baselib/server/env"
	"new-gitlab.xunlei.cn/vcproject/backends/baselib/server/interceptor/bizcontext"
	"new-gitlab.xunlei.cn/vcproject/backends/baselib/util"
	"new-gitlab.xunlei.cn/vcproject/backends/baselib/xlaccount"
	consulConf "new-gitlab.xunlei.cn/vcproject/backends/bizmisc/config"
	"new-gitlab.xunlei.cn/vcproject/backends/bizmisc/service"
	"new-gitlab.xunlei.cn/vcproject/backends/proto/api/basemsgtransfer"
	"new-gitlab.xunlei.cn/vcproject/backends/proto/api/bizmisc"
	"new-gitlab.xunlei.cn/vcproject/backends/proto/api/svcchat"
	"new-gitlab.xunlei.cn/vcproject/backends/proto/api/svcconfig"
	"new-gitlab.xunlei.cn/vcproject/backends/proto/consts"
	"new-gitlab.xunlei.cn/vcproject/backends/proto/svcmgr"
)

var (
	_ bizmisc.SServer = new(Controller)
)

type Controller struct{}

func NewController() *Controller {
	svcmgr.InitServices()
	return &Controller{}
}

func (c *Controller) GetAliObjectId(ctx context.Context, req *bizmisc.AliObjectIdReq) (*bizmisc.AliObjectIdResp, error) {
	if err := req.Validate(); err != nil {
		return &bizmisc.AliObjectIdResp{
			Code: errcode.ErrorParam.Code,
			Msg:  errcode.ErrorParam.Msg,
		}, nil
	}
	return service.GetAliObjectId(ctx, req)
}

func (c *Controller) GenerateAliOssSignature(ctx context.Context, req *bizmisc.AliOssSigatureReq) (*bizmisc.AliOssSigatureResp, error) {
	if err := req.Validate(); err != nil {
		return &bizmisc.AliOssSigatureResp{
			Code: errcode.ErrorParam.Code,
			Msg:  errcode.ErrorParam.Msg,
		}, nil
	}
	return service.GenerateAliOssSignature(ctx, req)
}

func (c *Controller) GetChanToken(ctx context.Context, req *bizmisc.GetChanTokenReq) (resp *bizmisc.GetChanTokenResp, err error) {
	resp = &bizmisc.GetChanTokenResp{Code: errcode.ErrOK.Code}
	authInfo := xlaccount.GetAuthInfo(ctx)
	if authInfo == nil || authInfo.UserId == 0 {
		return &bizmisc.GetChanTokenResp{
			Code: errcode.ErrUnauthorized.Code,
			Msg:  errcode.ErrUnauthorized.Msg,
		}, nil
	}
	baseParam := bizcontext.GetBaseContext(ctx)
	if baseParam == nil {
		return &bizmisc.GetChanTokenResp{
			Code: errcode.ErrorParam.Code,
			Msg:  errcode.ErrorParam.Msg,
		}, nil
	}
	tokenData := &msgchan.TokenData{
		Userid: authInfo.UserId,
		Devid:  baseParam.GetDid(),
		Data: util.JsonStr(msgchan.ExtendData{
			Scheme:    "ws",
			BaseParam: baseParam,
		}),
	}
	logger.Debugf("tokenData=%+v", util.JsonStr(tokenData))
	data, err := msgchan.GetTokenData(tokenData)
	if err != nil {
		logger.Errorf("GetChanToken error %v req=%+v", err, util.JsonStr(req))
		return &bizmisc.GetChanTokenResp{
			Code: errcode.ErrorInternal.Code,
			Msg:  err.Error(),
		}, nil
	}
	logger.Debugf("data=%+v", util.JsonStr(data))
	wsUrl := fmt.Sprintf("ws://%s:%d?ticker=%s", data.GetMasterAddr().GetHost(), data.GetMasterAddr().Port, data.GetToken())
	if env.IsProd() {
		wsUrl = fmt.Sprintf("wss://%s:%d?ticker=%s", data.GetMasterAddr().GetHost(), data.GetMasterAddr().Port, data.GetToken())
	}
	respData := &bizmisc.GetChanTokenRespData{
		WsUrl:        wsUrl,
		GeneralTopic: data.GeneralTopic,
		Gzip:         false,
	}
	resp.Data = respData
	return
}

func (c *Controller) GetAsrAddr(ctx context.Context, req *bizmisc.GetAsrAddrReq) (resp *bizmisc.GetAsrAddrResp, err error) {
	resp = &bizmisc.GetAsrAddrResp{Code: errcode.ErrOK.Code, Msg: errcode.ErrOK.Msg}
	authInfo := xlaccount.GetAuthInfo(ctx)
	if authInfo == nil || authInfo.UserId == 0 {
		errcode.ErrUnauthorized.SetTo(resp)
		return
	}
	return service.GetAsrAddr(ctx, req)
}

func (c *Controller) GetVcPushAddr(ctx context.Context, req *bizmisc.GetVcPushAddrReq) (resp *bizmisc.GetVcPushAddrResp, err error) {
	resp = &bizmisc.GetVcPushAddrResp{}
	if err = req.ValidateAll(); err != nil {
		resp.Code = errcode.ErrorParam.Code
		resp.Msg = errcode.ErrorParam.Msg
		return resp, nil
	}
	authInfo := xlaccount.GetAuthInfo(ctx)
	if authInfo == nil || authInfo.UserId == 0 {
		errcode.ErrUnauthorized.SetTo(resp)
		return
	}
	baseParam := bizcontext.GetBaseContext(ctx)
	if baseParam == nil {
		errcode.ErrorParam.SetTo(resp)
		return
	}
	tokenData := &msgchan.TokenData{
		Userid: authInfo.UserId,
		Devid:  baseParam.GetDid(),
		Data: util.JsonStr(msgchan.ExtendData{
			Scheme:    "ws",
			BaseParam: baseParam,
		}),
	}
	logger.Debugf("tokenData=%+v", util.JsonStr(tokenData))
	data, err := msgchan.GetTokenData(tokenData)
	if err != nil {
		logger.Errorf("GetChanToken error %v req=%+v", err, util.JsonStr(req))
		errcode.ErrorInternal.SetRespWithMsg(resp, err.Error())
		return
	}
	logger.Debugf("data=%+v", util.JsonStr(data))
	wsAddr := fmt.Sprintf("ws://rvc-test.character.xunlei.com/v1/cascaded_vc/ws_infer?ticker=%s", data.Token)
	if env.IsProd() {
		wsAddr = fmt.Sprintf("wss://rvc.character.xunlei.com/v1/cascaded_vc/ws_infer?ticker=%s", data.Token)
	} else {
		if util.InInt64Slice([]int64{500020}, authInfo.UserId) {
			wsAddr = fmt.Sprintf("wss://rvc-test.character.xunlei.com/v1/cascaded_vc/ws_infer?ticker=%s", data.Token)
		}
	}
	// 获取rvc信息
	presetName := fmt.Sprintf("%d_%d", req.CharacterId, req.CharacterAssetsId)
	if env.IsTest() {
		presetName = fmt.Sprintf("test_%d_%d", req.CharacterId, req.CharacterAssetsId)
	}
	rvcSvc := service.GetRVCService()
	presetResp, tmpErr := rvcSvc.GetPresetByName(ctx, presetName)
	if tmpErr != nil {
		logger.Errorf("tmpErr=%s presetResp=%+v", tmpErr, util.JsonStr(presetResp))
		if errors.Is(tmpErr, context.Canceled) {
			resp.Code = errcode.ErrorCanceled.Code
			return
		}
		resp.Code = errcode.ErrorInternal.Code
		return
	}
	presetConfig := presetResp.Data
	if presetConfig == nil || len(presetConfig.RvcName) == 0 || len(presetConfig.ReferenceAudioUrl) == 0 {
		logger.Warnf("presetConfig invalid: %+s presetName=%s", util.JsonStr(presetConfig), presetName)
		resp.Code = errcode.ErrorInternal.Code
		return
	}
	mid := authInfo.UserId
	forceNoRvc := consulConf.IsForceNoRvc()
	useRvc := false
	if !forceNoRvc {
		useRvc = presetConfig.CascadedUseRvc
	}
	logger.Infof("mid=%d forceNoRvc=%+v useRvc=%+v", mid, forceNoRvc, useRvc)
	if useRvc {
		loadModelReq := service.LoadModelReq{
			RvcName:       presetConfig.RvcName,
			SeedName:      presetConfig.SeedName,
			ReferAudioUrl: presetConfig.ReferenceAudioUrl,
			RequestId:     fmt.Sprintf("%s-%d", util.UUID(), mid),
			Mid:           fmt.Sprintf("%d", mid),
		}
		loadModelResp, tmpErr := rvcSvc.LoadModel(ctx, &loadModelReq)
		if tmpErr != nil || loadModelResp == nil || loadModelResp.Ret != service.RvcOK {
			logger.Errorf("tmpErr=%s presetResp=%+v loadModelReq=%+v", tmpErr, util.JsonStr(presetResp), loadModelReq)
			if errors.Is(tmpErr, context.Canceled) {
				resp.Code = errcode.ErrorCanceled.Code
				return
			}
			resp.Code = errcode.ErrorInternal.Code
			return
		}
	}
	rvcInfo := bizmisc.RvcInfo{
		RvcName:           presetConfig.RvcName,
		ReferenceAudioUrl: presetConfig.ReferenceAudioUrl,
		SeedName:          presetConfig.SeedName,
		RequestId:         fmt.Sprintf("%s-%d", util.UUID(), mid),
		Mid:               fmt.Sprintf("%d", mid),
		CascadedUseRvc:    useRvc,
		F0Method:          "rmvpe",
		RmsMixRate:        0.33,
		Protect:           0.33,
		DiffusionSteps:    50.0,
		LengthAdjust:      1.0,
		InferenceCfgRate:  0.7,
		BitDepth:          32,
		Channels:          1,
		OutputSampleRate:  16000,
		OutputBitDepth:    16,
		OutputChannels:    1,
		ChunkMs:           1000,
	}
	if useRvc {
		rvcInfo.OutputSampleRate = 48000
	}
	if util.InStringSlice([]string{"comment", "signature"}, req.Scene) {
		rvcInfo.AudioMilliDuration = 60 * 1000
	} else if req.Scene == "dubbing" {
		rvcInfo.AudioMilliDuration = 180 * 1000
	} else {
		rvcInfo.AudioMilliDuration = 60 * 1000
	}
	// 先强制不使用rvc
	// if env.IsTest() {
	// 	rvcInfo.CascadedUseRvc = false
	// }
	if strings.ToLower(rvcInfo.SeedName) == "whisper_small" || strings.ToLower(rvcInfo.SeedName) == "tat_xlsr" {
		rvcInfo.SampleRate = 22050
	} else if strings.ToLower(rvcInfo.SeedName) == "whisper_base_f0" {
		rvcInfo.SampleRate = 44100
	} else {
		logger.Errorf("seed name %s not supported")
		resp.Code = errcode.ErrorInternal.Code
		return
	}
	respData := &bizmisc.GetVcPushAddrRespData{
		WsAddr: wsAddr,
		Rvc:    &rvcInfo,
	}
	resp.Data = respData
	return
}

func (c *Controller) TestSendSecretaryMsg(ctx context.Context, req *bizmisc.SendSecretaryMsgReq) (resp *bizmisc.SendSecretaryMsgResp, err error) {
	resp = &bizmisc.SendSecretaryMsgResp{}
	if err = req.ValidateAll(); err != nil {
		errcode.ErrorParam.SetTo(resp)
		return resp, nil
	}
	msgContent := map[string]interface{}{
		"content": &svcchat.TemplateMsg{
			Tpl:  req.GetTpl(),
			Data: req.GetTplData(),
		},
		"time": util.NowTimeMillis(),
	}
	if req.GetMember() != nil {
		msgContent["member"] = req.GetMember()
	}
	if req.GetScript() != nil {
		msgContent["script"] = req.GetScript()
	}
	if req.GetComment() != nil {
		msgContent["comment"] = req.GetComment()
	}
	if req.GetCommentReply() != nil {
		msgContent["comment_reply"] = req.GetCommentReply()
	}
	if req.GetDubbing() != nil {
		msgContent["dubbing"] = req.GetDubbing()
	}
	msg := &basemsgtransfer.SendMsgReq{
		Msgs: []*basemsgtransfer.MsgData{
			{
				From:        consts.OfficialSecretaryUserid,
				To:          req.ToUid,
				SessionType: int32(basemsgtransfer.SessionType_SessionTypeSystem),
				ContentType: int32(req.ContentType),
				Content:     util.JsonStr(msgContent),
				MsgFrom:     uint32(basemsgtransfer.MsgFromEnum_MsgFromIm),
				CreateTime:  util.NowTimeMillis(),
			},
		},
	}
	logger.Infof("msg=%+v", util.JsonStr(msg))
	vResp, tmpErr := svcmgr.BaseMsgTransferClient().SendMsg(ctx, msg)
	if tmpErr != nil || errcode.NotOk(vResp) {
		logger.Errorf("err=%+v vResp=%+v", tmpErr.Error(), util.JsonStr(vResp))
		errcode.ErrorInternal.SetTo(resp)
		return
	}
	return
}

func (c *Controller) TestSendSystemMsg(ctx context.Context, req *bizmisc.TestSendSystemMsgReq) (resp *bizmisc.TestSendSystemMsgResp, err error) {
	resp = &bizmisc.TestSendSystemMsgResp{}
	if err = req.ValidateAll(); err != nil {
		errcode.ErrorParam.SetTo(resp)
		return resp, nil
	}

	content := ""
	switch req.ContentType {
	case int32(basemsgtransfer.ContentType_RichTextNormal):
		content = util.JsonStr(req.SystemMsg)
	case int32(basemsgtransfer.ContentType_Follow):
		content = util.JsonStr(req.FollowMsg)
	case int32(basemsgtransfer.ContentType_Comment),
		int32(basemsgtransfer.ContentType_CommentScript),
		int32(basemsgtransfer.ContentType_CommentReply):
		content = util.JsonStr(req.CommentMsg)
	case int32(basemsgtransfer.ContentType_Like),
		int32(basemsgtransfer.ContentType_LikeScript),
		int32(basemsgtransfer.ContentType_LikeComment),
		int32(basemsgtransfer.ContentType_LikeDubbing):
		content = util.JsonStr(req.LikeMsg)
	case int32(basemsgtransfer.ContentType_Dubbing):
		content = util.JsonStr(req.DubbingMsg)
	case int32(basemsgtransfer.ContentType_Review),
		int32(basemsgtransfer.ContentType_ReviewScript):
		content = util.JsonStr(req.ReviewMsg)
	default:
		errcode.ErrorParam.SetTo(resp)
		return
	}

	if content == "" {
		errcode.ErrorParam.SetTo(resp)
		return
	}

	msg := &basemsgtransfer.SendMsgReq{
		Msgs: []*basemsgtransfer.MsgData{
			{
				MsgId:       util.UUID(),
				From:        consts.OfficialSecretaryUserid,
				To:          req.ToUid,
				SessionType: int32(basemsgtransfer.SessionType_SessionTypeSystem),
				ContentType: req.ContentType,
				Content:     content,
				MsgFrom:     uint32(basemsgtransfer.MsgFromEnum_MsgFromIm),
				CreateTime:  util.NowTimeMillis(),
			},
		},
	}
	logger.Infof("msg=%+v", util.JsonStr(msg))
	vResp, tmpErr := svcmgr.BaseMsgTransferClient().SendMsg(ctx, msg)
	if tmpErr != nil || errcode.NotOk(vResp) {
		logger.Errorf("err=%+v vResp=%+v", tmpErr.Error(), util.JsonStr(vResp))
		errcode.ErrorInternal.SetTo(resp)
		return
	}

	return
}

// CheckVersion 检查版本更新
func (c *Controller) CheckVersion(ctx context.Context, req *bizmisc.CheckVersionReq) (resp *bizmisc.CheckVersionResp, err error) {
	resp = &bizmisc.CheckVersionResp{}

	svcReq := &svcconfig.CheckVersionReq{}
	svcResp, err := svcmgr.ConfigClient().CheckVersion(ctx, svcReq)
	if err != nil {
		logger.Errorf("CheckVersion: call svcconfig failed, err=%v", err)
		errcode.FromError(err).SetTo(resp)
		return resp, nil
	}

	errcode.ErrOK.SetTo(resp, svcResp.Data)
	return resp, nil
}

func (c *Controller) GetOfficialApkUrl(ctx context.Context, req *bizmisc.GetApkUrlReq) (resp *bizmisc.GetApkUrlResp, err error) {
	apkDownloadUrl := consulConf.GetOfficialApkUrl()
	resp = &bizmisc.GetApkUrlResp{
		Code: errcode.ErrOK.Code,
		Msg:  errcode.ErrOK.Msg,
		Data: &bizmisc.GetApkUrlRespData{
			Url: apkDownloadUrl,
		},
	}
	return resp, nil
}
