package main

import (
	"context"
	"fmt"
	"os"
	"os/signal"
	"syscall"

	"new-gitlab.xunlei.cn/vcproject/backends/baselib/config"
	"new-gitlab.xunlei.cn/vcproject/backends/baselib/consul"
	"new-gitlab.xunlei.cn/vcproject/backends/baselib/logger"
	"new-gitlab.xunlei.cn/vcproject/backends/baselib/msgchan"
	"new-gitlab.xunlei.cn/vcproject/backends/baselib/server"
	"new-gitlab.xunlei.cn/vcproject/backends/baselib/server/env"
	"new-gitlab.xunlei.cn/vcproject/backends/baselib/tracer"
	consulConf "new-gitlab.xunlei.cn/vcproject/backends/bizmisc/config"
	"new-gitlab.xunlei.cn/vcproject/backends/bizmisc/controller"
	"new-gitlab.xunlei.cn/vcproject/backends/proto/api/bizmisc"

	"github.com/grpc-ecosystem/grpc-gateway/v2/runtime"
	"google.golang.org/grpc"
)

func main() {
	config.Init()
	conf := config.GetServerConfig()
	logger.InitLoggerWitchLevel(conf.Logger, conf.LogLevel)

	// consul
	prefix := fmt.Sprintf("%s/%s", env.GetEnvironment(), conf.Name)
	consul.Init(conf.ConsulAddr, prefix)
	consulConf.InitConfig()

	//接入tracing
	ctx, cancel := context.WithCancel(context.Background())
	if conf.Jaeger != nil && conf.Jaeger.Enable {
		prv, _ := tracer.InitTracer(ctx, conf.Jaeger)
		defer prv.Close(ctx)
	}
	msgchan.Init()
	svr := server.NewBizServer(conf)
	svr.RegisterServices(func(s *grpc.Server) {
		bizmisc.RegisterSServer(s, controller.NewController())
	})
	//aliyun.InitOss()
	//aliyun.InitCertifyConf()
	//sconfig.InitConfig()

	if env.IsLocal() {
		go server.StartHttp(func(conn *grpc.ClientConn, mux *runtime.ServeMux) {
			bizmisc.RegisterSHandler(context.Background(), mux, conn)
		})
	}
	go svr.Start()
	sigChan := make(chan os.Signal, 1)
	signal.Notify(sigChan, syscall.SIGINT, syscall.SIGTERM)
	sig := <-sigChan
	logger.Infof("received signal: %v starting graceful shutdown", sig)
	// 优雅退出流程
	// 1. 取消上下文，通知所有使用该上下文的 goroutine
	cancel()
	// 2. 停止接收新的请求并等待现有请求处理完成
	svr.GracefulStop()
	logger.Infof("server shutdown completed")
}
