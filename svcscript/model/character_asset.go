package model

import (
	"context"
	"errors"

	"new-gitlab.xunlei.cn/vcproject/backends/proto/api/svcscript"

	"gorm.io/gorm"
	"new-gitlab.xunlei.cn/vcproject/backends/baselib/alioss"
	"new-gitlab.xunlei.cn/vcproject/backends/baselib/logger"
)

// CharacterAsset 角色资源包
type CharacterAsset struct {
	ID                   int64            `gorm:"primaryKey;column:id" json:"id"`                                // 资源包ID
	CharacterID          int64            `gorm:"column:character_id;not null" json:"character_id"`              // 角色ID
	Name                 string           `gorm:"column:name;not null" json:"name"`                              // 资源包名称
	AvatarURL            string           `gorm:"column:avatar_url" json:"avatar_url"`                           // 角色头像URL
	BackgroundURL        string           `gorm:"column:background_url" json:"background_url"`                   // 角色背景图URL
	BgThemeColor         string           `gorm:"column:bg_theme_color" json:"bg_theme_color"`                   // 背景图主题色
	AnimatedURL          string           `gorm:"column:animated_url" json:"animated_url"`                       // 角色动态图URL
	AnimatedThemeColor   string           `gorm:"column:animated_theme_color" json:"animated_theme_color"`       // 动态图主题色
	SampleAudio          string           `gorm:"column:sample_audio" json:"sample_audio"`                       // 试听音频URL
	SampleAudioText      string           `gorm:"column:sample_audio_text" json:"sample_audio_text"`             // 试听音频文本
	Type                 string           `gorm:"column:type;default:''" json:"type"`                            // 资源包类型
	Sort                 int32            `gorm:"column:sort;default:0" json:"sort"`                             // 排序值
	IsDefault            bool             `gorm:"column:is_default;default:false" json:"is_default"`             // 是否默认资源包
	Status               svcscript.Status `gorm:"column:status;default:1" json:"status"`                         // 状态（1:正常，2:删除）
	PresetStatus         int32            `gorm:"column:preset_status;" json:"preset_status" `                   // 1: 已预设 否则未预设
	ReferenceAudioUseRvc int              `gorm:"column:reference_audio_use_rvc" json:"reference_audio_use_rvc"` // 参考音频是否使用rvc 1:使用 2:不使用
	UserDubbingUseRvc    int              `gorm:"column:user_dubbing_use_rvc" json:"user_dubbing_use_rvc"`       // 用户配音是否使用rvc 1:使用 2:不使用
	TtsEngine            string           `gorm:"column:tts_engine" json:"tts_engine"`                           // tts使用引擎，默认llasa, [llasa, fish_speech]
	CreatedAt            int64            `gorm:"column:created_at" json:"created_at"`                           // 创建时间
	UpdatedAt            int64            `gorm:"column:updated_at" json:"updated_at"`                           // 更新时间
}

// TableName 表名
func (CharacterAsset) TableName() string {
	return "character_assets"
}

// GetFullAvatarURL 获取完整头像URL
func (a CharacterAsset) GetFullAvatarURL() string {
	if a.AvatarURL == "" {
		return ""
	}
	return alioss.FillImageUrl(a.AvatarURL)
}

// GetFullBackgroundURL 获取完整背景图URL
func (a CharacterAsset) GetFullBackgroundURL() string {
	if a.BackgroundURL == "" {
		return ""
	}
	return alioss.FillImageUrl(a.BackgroundURL)
}

// GetFullAnimatedURL 获取完整动态图URL
func (a CharacterAsset) GetFullAnimatedURL() string {
	if a.AnimatedURL == "" {
		return ""
	}
	return alioss.FillImageUrl(a.AnimatedURL)
}

// GetFullSampleAudioURL 获取完整试听音频URL
func (a CharacterAsset) GetFullSampleAudioURL() string {
	if a.SampleAudio == "" {
		return ""
	}
	return alioss.FillAudioUrl(a.SampleAudio)
}

// CharacterAssetModelInterface 角色资源包模型接口
type CharacterAssetModelInterface interface {
	// 查询方法
	GetAssetsByCharacterID(ctx context.Context, characterID int64, mustPreset bool) ([]*CharacterAsset, error)
	GetAssetByID(ctx context.Context, id int64) (*CharacterAsset, error)
	GetAssetsByIDs(ctx context.Context, ids []int64) ([]*CharacterAsset, error)
	// map[assetId]*CharacterAsset
	GetAssetsMapByIDs(ctx context.Context, ids []int64) (map[int64]*CharacterAsset, error)
	// map[characterId]*CharacterAsset
	GetAssetsCharIdMapByIDs(ctx context.Context, ids []int64) (map[int64]*CharacterAsset, error)
	GetAssetsByCharacterIDs(ctx context.Context, characterIDs []int64, mustPreset bool) (map[int64][]*CharacterAsset, error)
	GetRandomAssetByCharacterID(ctx context.Context, characterID int64) (*CharacterAsset, error)
	GetDefaultAssetByCharacterID(ctx context.Context, characterID int64) (*CharacterAsset, error)
}

// CharacterAssetModel 角色资源包模型实现
type CharacterAssetModel struct {
	BaseModelInterface
}

// NewCharacterAssetModel 创建角色资源包模型实例
func NewCharacterAssetModel(baseModel BaseModelInterface) CharacterAssetModelInterface {
	return &CharacterAssetModel{
		baseModel,
	}
}

// GetAssetsByCharacterID 根据角色ID获取资源包列表
func (m *CharacterAssetModel) GetAssetsByCharacterID(ctx context.Context, characterID int64, mustPreset bool) ([]*CharacterAsset, error) {
	var assets []*CharacterAsset

	db := m.GetDB().WithContext(ctx).
		Model(&CharacterAsset{}).
		Where("character_id = ? AND status = ?", characterID, 1)

	if mustPreset {
		db = db.Where("preset_status = 1")
	}

	if err := db.
		Order("sort DESC, id DESC").
		Find(&assets).Error; err != nil {
		logger.Errorf("GetAssetsByCharacterID error: %v", err)
		return nil, err
	}

	return assets, nil
}

// GetAssetByID 根据ID获取资源包
func (m *CharacterAssetModel) GetAssetByID(ctx context.Context, id int64) (*CharacterAsset, error) {
	var asset CharacterAsset

	if err := m.GetDB().WithContext(ctx).
		Model(&CharacterAsset{}).
		Where("id = ? AND status = ?  AND preset_status = ?", id, 1, 1).
		First(&asset).Error; err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, nil
		}
		logger.Errorf("GetAssetByID error: %v", err)
		return nil, err
	}

	return &asset, nil
}

// GetAssetsByCharacterIDs 批量获取多个角色的资源包
func (m *CharacterAssetModel) GetAssetsByCharacterIDs(ctx context.Context, characterIDs []int64, mustPreset bool) (map[int64][]*CharacterAsset, error) {
	if len(characterIDs) == 0 {
		return make(map[int64][]*CharacterAsset), nil
	}

	db := m.GetDB().WithContext(ctx).
		Model(&CharacterAsset{}).
		Where("character_id IN (?) AND status = ?", characterIDs, 1)

	if mustPreset {
		db = db.Where("preset_status = 1")
	}

	var assets []*CharacterAsset
	if err := db.Order("sort DESC, id DESC").
		Find(&assets).Error; err != nil {
		logger.Errorf("GetAssetsByCharacterIDs error: %v", err)
		return nil, err
	}

	// 按角色ID分组
	result := make(map[int64][]*CharacterAsset)
	for _, asset := range assets {
		result[asset.CharacterID] = append(result[asset.CharacterID], asset)
	}

	return result, nil
}

// GetAssetsByIDs 根据ID列表获取资源包
func (m *CharacterAssetModel) GetAssetsByIDs(ctx context.Context, ids []int64) ([]*CharacterAsset, error) {
	if len(ids) == 0 {
		return []*CharacterAsset{}, nil
	}

	var assets []*CharacterAsset
	if err := m.GetDB().WithContext(ctx).
		Model(&CharacterAsset{}).
		Where("id IN (?) AND status = ?", ids, 1).
		Order("id ASC").
		Find(&assets).Error; err != nil {
		logger.Errorf("GetAssetsByIDs error: %v", err)
		return nil, err
	}

	return assets, nil
}

// GetAssetsMapByIDs 根据ID列表获取资源包Map
func (m *CharacterAssetModel) GetAssetsMapByIDs(ctx context.Context, ids []int64) (map[int64]*CharacterAsset, error) {
	if len(ids) == 0 {
		return make(map[int64]*CharacterAsset), nil
	}

	assets, err := m.GetAssetsByIDs(ctx, ids)
	if err != nil {
		return nil, err
	}

	assetsMap := make(map[int64]*CharacterAsset)
	for _, asset := range assets {
		assetsMap[asset.ID] = asset
	}

	return assetsMap, nil
}

func (m *CharacterAssetModel) GetAssetsCharIdMapByIDs(ctx context.Context, ids []int64) (map[int64]*CharacterAsset, error) {
	if len(ids) == 0 {
		return make(map[int64]*CharacterAsset), nil
	}

	assets, err := m.GetAssetsByIDs(ctx, ids)
	if err != nil {
		return nil, err
	}

	assetsMap := make(map[int64]*CharacterAsset)
	for _, asset := range assets {
		assetsMap[asset.CharacterID] = asset
	}

	return assetsMap, nil
}

// GetRandomAssetByCharacterID 随机获取角色的一个资源包
func (m *CharacterAssetModel) GetRandomAssetByCharacterID(ctx context.Context, characterID int64) (*CharacterAsset, error) {
	var asset CharacterAsset

	if err := m.GetDB().WithContext(ctx).
		Model(&CharacterAsset{}).
		Where("character_id = ? AND status = ? AND preset_status = ?", characterID, 1, 1).
		Order("RAND()").
		First(&asset).Error; err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, nil
		}
		logger.Errorf("GetRandomAssetByCharacterID error: %v", err)
		return nil, err
	}

	return &asset, nil
}

// GetDefaultAssetByCharacterID 获取角色的默认资源包
func (m *CharacterAssetModel) GetDefaultAssetByCharacterID(ctx context.Context, characterID int64) (*CharacterAsset, error) {
	var asset CharacterAsset

	if err := m.GetDB().WithContext(ctx).
		Model(&CharacterAsset{}).
		Where("character_id = ? AND status = ? AND preset_status = ? AND is_default = ?", characterID, 1, 1, true).
		First(&asset).Error; err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, nil
		}
		logger.Errorf("GetDefaultAssetByCharacterID error: %v", err)
		return nil, err
	}

	return &asset, nil
}
