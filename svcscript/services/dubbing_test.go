package services

import (
	"testing"
	"time"

	"new-gitlab.xunlei.cn/vcproject/backends/svcscript/model"
)

// TestApplySortingLogicUnified_DuplicateTopUserDubbing 测试置顶且属于当前用户的配音记录去重
func TestApplySortingLogicUnified_DuplicateTopUserDubbing(t *testing.T) {
	service := &DubbingService{}
	
	// 模拟问题场景：一条配音记录既是置顶的，又属于当前用户
	userID := int64(500093)
	dubbingID := int64(1773)
	
	// 创建测试数据
	dubbings := []*model.Dubbing{
		{
			ID:        dubbingID,
			UserID:    userID,
			IsTop:     true,  // 置顶
			Likes:     10,
			CreatedAt: time.Now().UnixMilli(),
		},
		{
			ID:        2,
			UserID:    123456,
			IsTop:     false,
			Likes:     5,
			CreatedAt: time.Now().UnixMilli() - 1000,
		},
		{
			ID:        3,
			UserID:    789012,
			IsTop:     true,  // 另一个置顶配音
			Likes:     8,
			CreatedAt: time.Now().UnixMilli() - 2000,
		},
	}
	
	// 测试完整列表排序逻辑（不去重）
	config := DubbingSortConfig{
		EnableBatchGroup:   false, // 完整列表不受批次影响
		EnableUserDedup:    false, // 完整列表不去重
		Limit:              0,     // 不限制数量
		BatchAllowMultiple: false,
	}
	
	result := service.applySortingLogicUnified(dubbings, userID, []int64{}, config)
	
	// 验证结果：每条配音只应该出现一次
	dubbingIDCount := make(map[int64]int)
	for _, dubbing := range result {
		dubbingIDCount[dubbing.ID]++
	}
	
	// 检查是否有重复
	for id, count := range dubbingIDCount {
		if count > 1 {
			t.Errorf("配音ID %d 出现了 %d 次，应该只出现1次", id, count)
		}
	}
	
	// 验证排序逻辑：当前用户的配音应该在前面
	if len(result) > 0 && result[0].ID != dubbingID {
		t.Errorf("期望第一条配音是用户 %d 的配音（ID: %d），实际是 ID: %d", userID, dubbingID, result[0].ID)
	}
	
	// 验证总数量正确
	if len(result) != len(dubbings) {
		t.Errorf("期望返回 %d 条配音，实际返回 %d 条", len(dubbings), len(result))
	}
}

// TestApplySortingLogicUnified_SimpleList 测试简略列表的去重逻辑
func TestApplySortingLogicUnified_SimpleList(t *testing.T) {
	service := &DubbingService{}
	
	userID := int64(500093)
	dubbingID := int64(1773)
	
	// 创建测试数据：同一用户有多条配音
	dubbings := []*model.Dubbing{
		{
			ID:        dubbingID,
			UserID:    userID,
			IsTop:     true,  // 置顶
			Likes:     10,
			CreatedAt: time.Now().UnixMilli(),
		},
		{
			ID:        2,
			UserID:    userID,  // 同一用户的另一条配音
			IsTop:     false,
			Likes:     5,
			CreatedAt: time.Now().UnixMilli() - 1000,
		},
		{
			ID:        3,
			UserID:    789012,
			IsTop:     false,
			Likes:     8,
			CreatedAt: time.Now().UnixMilli() - 2000,
		},
	}
	
	// 测试简略列表排序逻辑（用户去重）
	config := DubbingSortConfig{
		EnableBatchGroup:   true,
		EnableUserDedup:    true,  // 简略列表启用用户去重
		Limit:              10,
		BatchAllowMultiple: true,
	}
	
	result := service.applySortingLogicUnified(dubbings, userID, []int64{}, config)
	
	// 验证用户去重：每个用户最多只应该有一条配音
	userIDCount := make(map[int64]int)
	for _, dubbing := range result {
		userIDCount[dubbing.UserID]++
	}
	
	for uid, count := range userIDCount {
		if count > 1 {
			t.Errorf("用户ID %d 的配音出现了 %d 次，简略列表中每个用户应该最多只有1条配音", uid, count)
		}
	}
	
	// 验证当前用户的配音被优先选择（应该是置顶的那条）
	foundUserDubbing := false
	for _, dubbing := range result {
		if dubbing.UserID == userID {
			if dubbing.ID != dubbingID {
				t.Errorf("期望选择用户 %d 的置顶配音（ID: %d），实际选择了 ID: %d", userID, dubbingID, dubbing.ID)
			}
			foundUserDubbing = true
			break
		}
	}
	
	if !foundUserDubbing {
		t.Errorf("结果中没有找到用户 %d 的配音", userID)
	}
}

// TestApplySortingLogicUnified_BatchPriority 测试批次置顶逻辑
func TestApplySortingLogicUnified_BatchPriority(t *testing.T) {
	service := &DubbingService{}
	
	userID := int64(500093)
	batchDubbingIDs := []int64{2, 3}  // 批次配音ID
	
	dubbings := []*model.Dubbing{
		{
			ID:        1,
			UserID:    userID,
			IsTop:     true,
			Likes:     10,
			CreatedAt: time.Now().UnixMilli(),
		},
		{
			ID:        2,  // 批次配音
			UserID:    123456,
			IsTop:     false,
			Likes:     5,
			CreatedAt: time.Now().UnixMilli() - 1000,
		},
		{
			ID:        3,  // 批次配音
			UserID:    789012,
			IsTop:     false,
			Likes:     8,
			CreatedAt: time.Now().UnixMilli() - 2000,
		},
	}
	
	// 测试简略列表的批次置顶逻辑
	config := DubbingSortConfig{
		EnableBatchGroup:   true,
		EnableUserDedup:    true,
		Limit:              10,
		BatchAllowMultiple: true,
	}
	
	result := service.applySortingLogicUnified(dubbings, userID, batchDubbingIDs, config)
	
	// 验证批次配音在前面
	if len(result) < 2 {
		t.Fatalf("期望至少返回2条配音，实际返回 %d 条", len(result))
	}
	
	// 前两条应该是批次配音
	expectedBatchIDs := map[int64]bool{2: true, 3: true}
	for i := 0; i < 2; i++ {
		if !expectedBatchIDs[result[i].ID] {
			t.Errorf("期望前两条是批次配音（ID: 2, 3），但第 %d 条是 ID: %d", i+1, result[i].ID)
		}
	}
}
