package controller

import (
	"context"

	"new-gitlab.xunlei.cn/vcproject/backends/baselib/errcode"
	"new-gitlab.xunlei.cn/vcproject/backends/baselib/logger"
	"new-gitlab.xunlei.cn/vcproject/backends/baselib/util"
	"new-gitlab.xunlei.cn/vcproject/backends/proto/api/vcxxjob"
	"new-gitlab.xunlei.cn/vcproject/backends/proto/svcmgr"
	"new-gitlab.xunlei.cn/vcproject/backends/vcxxjob/service"
)

var (
	_ vcxxjob.SServer = new(Controller)
)

type Controller struct {
}

func NewController() *Controller {
	svcmgr.InitServices()
	return &Controller{}
}

func (c *Controller) AsyncTTS(ctx context.Context, req *vcxxjob.AsyncTTSReq) (resp *vcxxjob.AsyncTTSResp, err error) {
	resp = &vcxxjob.AsyncTTSResp{Base: errcode.ErrOK.ToSvcBaseResp()}
	if err = req.ValidateAll(); err != nil {
		resp.Base = errcode.ErrorParam.ToSvcBaseResp()
		return resp, nil
	}
	logger.Infof("AsyncTTS req=%+v", util.JsonStr(req))
	svc := service.GetDialogueVCService()
	return svc.CreateVcTask(ctx, req)
}

func (c *Controller) AsyncBatchTTS(ctx context.Context, req *vcxxjob.AsyncBatchTTSReq) (resp *vcxxjob.AsyncBatchTTSResp, err error) {
	resp = &vcxxjob.AsyncBatchTTSResp{Base: errcode.ErrOK.ToSvcBaseResp(), Data: &vcxxjob.AsyncBatchTTSRespData{}}
	if err = req.ValidateAll(); err != nil {
		resp.Base = errcode.ErrorParam.ToSvcBaseResp()
		return resp, nil
	}
	logger.Infof("AsyncBatchTTS req=%+v", util.JsonStr(req))
	svc := service.GetDialogueVCService()
	for _, req := range req.List {
		createResp, tmpErr := svc.CreateVcTask(ctx, req)
		if tmpErr != nil {
			logger.Errorf("CreateVcTask error: %v, req=%+v", tmpErr, util.JsonStr(req))
		} else {
			resp.Data.List = append(resp.Data.List, &vcxxjob.AsyncBatchTTSRespDataItem{
				TaskId: createResp.Data.TaskId,
				OutId:  req.OutId,
			})
		}
	}
	return resp, nil
}

func (c *Controller) UpdateVcTask(ctx context.Context, req *vcxxjob.UpdateVcTaskReq) (resp *vcxxjob.UpdateVcTaskResp, err error) {
	resp = &vcxxjob.UpdateVcTaskResp{Base: errcode.ErrOK.ToSvcBaseResp()}
	if err = req.ValidateAll(); err != nil {
		resp.Base = errcode.ErrorParam.ToSvcBaseResp()
		return resp, nil
	}
	logger.Infof("UpdateVcTask req=%+v", util.JsonStr(req))
	svc := service.GetDialogueVCService()
	resp, err = svc.UpdateVcTask(ctx, req)
	return
}
