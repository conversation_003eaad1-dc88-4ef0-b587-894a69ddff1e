package model

import (
	"context"

	"gorm.io/gorm"
	"new-gitlab.xunlei.cn/vcproject/backends/baselib/logger"
	"new-gitlab.xunlei.cn/vcproject/backends/baselib/util"
	"new-gitlab.xunlei.cn/vcproject/backends/proto/api/vcxxjob"
)

type VcTask struct {
	ID                 int64                `gorm:"primaryKey;column:id" json:"id"`                          // ID
	TaskId             string               `gorm:"column:task_id;not null" json:"task_id"`                  // 任务id
	OutId              string               `gorm:"column:out_id" json:"out_id"`                             // out_id
	Source             int                  `gorm:"column:source" json:"source"`                             // 来源
	ReferAudioUrl      string               `gorm:"column:refer_audio_url;" json:"refer_audio_url"`          // 引导音频url
	ReferAudioText     string               `gorm:"column:refer_audio_text" json:"refer_audio_text"`         // 引导音频文本
	Content            string               `gorm:"column:content" json:"content"`                           // 待合成内容
	TTSEngine          string               `gorm:"column:tts_engine" json:"tts_engine"`                     // tts引擎
	Ext                string               `gorm:"column:ext" json:"ext"`                                   // 扩展信息
	Status             vcxxjob.VcTaskStatus `gorm:"column:status;default:0" json:"status"`                   //  '状态 0:未开始 1:进行中 2:完成 3:失败'
	UseVc              int32                `gorm:"column:use_vc;default:1" json:"use_vc"`                   // 是否使用vc变声
	UseRvc             int32                `gorm:"column:use_rvc;default:1" json:"use_rvc"`                 // 是否使用rvc变声
	TtsAudioUrl        string               `gorm:"column:tts_audio_url" json:"tts_audio_url"`               // tts音频url
	TtsAudioDuration   int32                `gorm:"column:tts_audio_duration" json:"tts_audio_duration"`     // tts音频时长毫秒
	FinalAudioUrl      string               `gorm:"column:final_audio_url" json:"final_audio_url"`           // 最终音频url
	FinalAudioDuration int32                `gorm:"column:final_audio_duration" json:"final_audio_duration"` // 最终音频时长毫秒
	CreatedAt          int64                `gorm:"column:created_at" json:"created_at"`                     // 创建时间
	UpdatedAt          int64                `gorm:"column:updated_at" json:"updated_at"`                     // 更新时间
}

type VcTaskModelInterface interface {
	CreateVcTask(ctx context.Context, vcTask *VcTask) error
	GetVcTaskByID(ctx context.Context, id int64) (*VcTask, error)
	GetVcTaskByTaskID(ctx context.Context, taskID string) (*VcTask, error)
	GetVcTaskByOutIDAndSource(ctx context.Context, outID string, source int) (*VcTask, error)
	UpdateVcTaskByTaskId(ctx context.Context, taskID string, updates map[string]interface{}) error
}

// TableName 表名
func (VcTask) TableName() string {
	return "vc_tasks"
}

// ScriptModel 剧本模型
type VcTaskModel struct {
	BaseModelInterface
}

func NewVcTaskModel(baseModel BaseModelInterface) VcTaskModelInterface {
	return &VcTaskModel{
		baseModel,
	}
}

func (m *VcTaskModel) CreateVcTask(ctx context.Context, vcTask *VcTask) (err error) {
	logger.Infof("CreateVcTask: %+v", util.JsonStr(vcTask))
	return m.GetDB().WithContext(ctx).Create(vcTask).Error
}

func (m *VcTaskModel) GetVcTaskByID(ctx context.Context, id int64) (vcTask *VcTask, err error) {
	err = m.GetDB().WithContext(ctx).Where("id = ?", id).First(&vcTask).Error
	if err != nil && err == gorm.ErrRecordNotFound {
		return nil, nil
	}
	return
}

func (m *VcTaskModel) GetVcTaskByTaskID(ctx context.Context, taskID string) (vcTask *VcTask, err error) {
	err = m.GetDB().WithContext(ctx).Where("task_id = ?", taskID).First(&vcTask).Error
	if err != nil && err == gorm.ErrRecordNotFound {
		return nil, nil
	}
	return
}

func (m *VcTaskModel) GetVcTaskByOutIDAndSource(ctx context.Context, outID string, source int) (vcTask *VcTask, err error) {
	err = m.GetDB().WithContext(ctx).Where("out_id = ? AND source = ?", outID, source).First(&vcTask).Error
	if err != nil && err == gorm.ErrRecordNotFound {
		return nil, err
	}
	return
}

func (m *VcTaskModel) UpdateVcTaskByTaskId(ctx context.Context, taskID string, updates map[string]interface{}) (err error) {
	if len(updates) == 0 {
		return
	}
	err = m.GetDB().WithContext(ctx).Model(&VcTask{}).Where("task_id = ?", taskID).Updates(updates).Error
	return
}
