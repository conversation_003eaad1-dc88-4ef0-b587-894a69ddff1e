package model

import (
	"context"
	"errors"

	"gorm.io/gorm"
)

type BaseModelInterface interface {
	GetDB() *gorm.DB
	Transaction(ctx context.Context, txFunc func(tx *gorm.DB) error) error
	CreateWithTransaction(ctx context.Context, txFunc func(tx *gorm.DB) (int64, error)) (int64, error)
}

type BaseModel struct {
	db *gorm.DB
}

// NewBaseModel 创建基础模型实例
func NewBaseModel(db *gorm.DB) BaseModelInterface {
	return &BaseModel{
		db: db,
	}
}

func (m *BaseModel) GetDB() *gorm.DB {
	return m.db
}

// 仅事务
func (m *BaseModel) Transaction(ctx context.Context, txFunc func(tx *gorm.DB) error) error {
	if m.db == nil {
		return errors.New("数据库连接未初始化")
	}

	err := m.db.WithContext(ctx).Transaction(func(tx *gorm.DB) error {
		var err error
		err = txFunc(tx)
		return err
	})

	if err != nil {
		return err
	}

	return nil
}

// 创建型事务，返回主键ID
func (m *BaseModel) CreateWithTransaction(ctx context.Context, txFunc func(tx *gorm.DB) (int64, error)) (int64, error) {
	if m.db == nil {
		return 0, errors.New("数据库连接未初始化")
	}

	var id int64
	err := m.db.WithContext(ctx).Transaction(func(tx *gorm.DB) error {
		var err error
		id, err = txFunc(tx)
		return err
	})

	if err != nil {
		return 0, err
	}

	return id, nil
}
