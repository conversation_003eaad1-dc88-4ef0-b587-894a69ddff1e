package controller

import (
	"context"
	"regexp"

	"new-gitlab.xunlei.cn/vcproject/backends/baselib/xlaccount"

	"new-gitlab.xunlei.cn/vcproject/backends/baselib/errcode"
	"new-gitlab.xunlei.cn/vcproject/backends/baselib/logger"
	"new-gitlab.xunlei.cn/vcproject/backends/proto/svcmgr"

	"new-gitlab.xunlei.cn/vcproject/backends/proto/api/bizaccount"
	"new-gitlab.xunlei.cn/vcproject/backends/proto/api/svcaccount"
	"new-gitlab.xunlei.cn/vcproject/backends/proto/api/svcmoment"
)

var (
	_ bizaccount.SServer = new(Controller)
)

type Controller struct{}

func NewController() *Controller {
	svcmgr.InitServices()
	return &Controller{}
}

func (c *Controller) GetVerificationCode(ctx context.Context, req *svcaccount.GetVerificationCodeReq) (*bizaccount.GetVerificationCodeResp, error) {
	resp := &bizaccount.GetVerificationCodeResp{}

	if err := req.Validate(); err != nil {
		errcode.ErrInvalidVerifyCode.SetTo(resp)
		return resp, nil
	}

	if !regexp.MustCompile(`^1\d{10}$`).MatchString(req.GetPhoneNumber()) {
		errcode.ErrInvalidVerifyCode.SetTo(resp)
		return resp, nil
	}

	svcResp, err := svcmgr.AccountClient().GetVerificationCode(ctx, req)
	if err != nil {
		errcode.FromError(err).SetTo(resp)
		return resp, nil
	}

	errcode.ErrOK.SetTo(resp, svcResp.GetData())
	return resp, nil
}

func (c *Controller) AccountSignUp(ctx context.Context, req *svcaccount.AccountSignUpReq) (*bizaccount.AccountSignUpResp, error) {
	resp := &bizaccount.AccountSignUpResp{}

	if err := req.Validate(); err != nil {
		errcode.ErrInvalidVerifyCode.SetTo(resp)
		return resp, nil
	}

	svcResp, err := svcmgr.AccountClient().AccountSignUp(ctx, req)
	if err != nil {
		errcode.FromError(err).SetTo(resp)
		return resp, nil
	}

	errcode.ErrOK.SetTo(resp, svcResp.GetData())
	return resp, nil
}

func (c *Controller) AccountSignIn(ctx context.Context, req *svcaccount.AccountSignInReq) (*bizaccount.AccountSignInResp, error) {
	resp := &bizaccount.AccountSignInResp{}

	if err := req.Validate(); err != nil {
		errcode.ErrInvalidVerifyCode.SetTo(resp)
		return resp, nil
	}

	svcResp, err := svcmgr.AccountClient().AccountSignIn(ctx, req)
	if err != nil {
		errcode.FromError(err).SetTo(resp)
		return resp, nil
	}

	errcode.ErrOK.SetTo(resp, svcResp.GetData())
	return resp, nil
}

func (c *Controller) AccountSignOut(ctx context.Context, req *svcaccount.AccountSignOutReq) (*bizaccount.AccountSignOutResp, error) {
	resp := &bizaccount.AccountSignOutResp{}
	_, err := svcmgr.AccountClient().AccountSignOut(ctx, req)
	if err != nil {
		errcode.FromError(err).SetTo(resp)
		return resp, nil
	}

	errcode.ErrOK.SetTo(resp)
	return resp, nil
}

func (c *Controller) RefreshToken(ctx context.Context, req *svcaccount.RefreshTokenReq) (*bizaccount.RefreshTokenResp, error) {
	resp := &bizaccount.RefreshTokenResp{}
	svcResp, err := svcmgr.AccountClient().RefreshToken(ctx, req)
	if err != nil {
		errcode.FromError(err).SetTo(resp)
		return resp, nil
	}

	errcode.ErrOK.SetTo(resp, svcResp.GetData())
	return resp, nil
}

func (c *Controller) AccountDelete(ctx context.Context, req *svcaccount.AccountDeleteReq) (*bizaccount.AccountDeleteResp, error) {
	resp := &bizaccount.AccountDeleteResp{}

	// 获取当前用户ID
	authInfo := xlaccount.GetAuthInfo(ctx)
	if authInfo.UserId <= 0 {
		errcode.ErrUnauthorized.SetTo(resp)
		return resp, nil
	}

	// 删除账户数据
	_, err := svcmgr.AccountClient().AccountDelete(ctx, req)
	if err != nil {
		errcode.FromError(err).SetTo(resp)
		return resp, nil
	}

	// 删除朋友圈数据
	_, err = svcmgr.MomentClient().DeleteUserData(ctx, &svcmoment.DeleteUserDataReq{
		UserId: authInfo.UserId,
	})
	if err != nil {
		logger.Errorf("DeleteUserLike error: %v", err)
		// 朋友圈数据删除失败不影响账户注销
	}

	errcode.ErrOK.SetTo(resp)
	return resp, nil
}

func (c *Controller) GetUserInfo(ctx context.Context, req *svcaccount.GetUserInfoReq) (*bizaccount.GetUserInfoResp, error) {
	resp := &bizaccount.GetUserInfoResp{}

	// 获取当前用户ID
	authInfo := xlaccount.GetAuthInfo(ctx)
	if authInfo.UserId <= 0 && req.UserId <= 0 {
		errcode.ErrUnauthorized.SetTo(resp)
		return resp, nil
	}

	if req.UserId <= 0 {
		req.UserId = authInfo.UserId
	}

	svcResp, err := svcmgr.AccountClient().GetUserInfo(ctx, req)
	if err != nil {
		errcode.FromError(err).SetTo(resp)
		return resp, nil
	}

	errcode.ErrOK.SetTo(resp, svcResp.GetData())
	return resp, nil
}

// 更新用户信息
func (c *Controller) UpdateUserInfo(ctx context.Context, req *svcaccount.UpdateUserInfoReq) (*bizaccount.UpdateUserInfoResp, error) {
	resp := &bizaccount.UpdateUserInfoResp{}

	authInfo := xlaccount.GetAuthInfo(ctx)
	if authInfo.UserId <= 0 {
		errcode.ErrUnauthorized.SetTo(resp)
		return resp, nil
	}
	req.UserId = authInfo.UserId

	if err := req.Validate(); err != nil {
		errcode.ErrorParam.SetTo(resp)
		return resp, nil
	}

	_, err := svcmgr.AccountClient().UpdateUserInfo(ctx, req)
	if err != nil {
		errcode.FromError(err).SetTo(resp)
		return resp, nil
	}

	errcode.ErrOK.SetTo(resp)
	return resp, nil
}

// FollowUser 关注用户
func (c *Controller) FollowUser(ctx context.Context, req *bizaccount.FollowUserReq) (*bizaccount.FollowUserResp, error) {
	resp := &bizaccount.FollowUserResp{}

	if err := req.Validate(); err != nil {
		errcode.ErrorParam.SetTo(resp)
		return resp, nil
	}

	// 获取当前用户ID
	authInfo := xlaccount.GetAuthInfo(ctx)
	if authInfo.UserId <= 0 {
		errcode.ErrUnauthorized.SetTo(resp)
		return resp, nil
	}

	// 不能关注自己
	if authInfo.UserId == req.FollowingId {
		errcode.ErrorParam.SetTo(resp)
		return resp, nil
	}

	svcReq := &svcaccount.FollowUserReq{
		FollowerId:  authInfo.UserId,
		FollowingId: req.FollowingId,
	}
	_, err := svcmgr.AccountClient().FollowUser(ctx, svcReq)
	if err != nil {
		errcode.FromError(err).SetTo(resp)
		return resp, nil
	}

	errcode.ErrOK.SetTo(resp)
	return resp, nil
}

// UnfollowUser 取消关注用户
func (c *Controller) UnfollowUser(ctx context.Context, req *bizaccount.UnfollowUserReq) (*bizaccount.UnfollowUserResp, error) {
	resp := &bizaccount.UnfollowUserResp{}

	if err := req.Validate(); err != nil {
		errcode.ErrorParam.SetTo(resp)
		return resp, nil
	}

	// 获取当前用户ID
	authInfo := xlaccount.GetAuthInfo(ctx)
	if authInfo.UserId <= 0 {
		errcode.ErrUnauthorized.SetTo(resp)
		return resp, nil
	}

	svcReq := &svcaccount.UnfollowUserReq{
		FollowerId:  authInfo.UserId,
		FollowingId: req.FollowingId,
	}
	_, err := svcmgr.AccountClient().UnfollowUser(ctx, svcReq)
	if err != nil {
		errcode.FromError(err).SetTo(resp)
		return resp, nil
	}

	errcode.ErrOK.SetTo(resp)
	return resp, nil
}

// CheckFollowing 检查是否关注
func (c *Controller) CheckFollowing(ctx context.Context, req *bizaccount.CheckFollowingReq) (*bizaccount.CheckFollowingResp, error) {
	resp := &bizaccount.CheckFollowingResp{}

	if err := req.Validate(); err != nil {
		errcode.ErrorParam.SetTo(resp)
		return resp, nil
	}

	// 获取当前用户ID
	authInfo := xlaccount.GetAuthInfo(ctx)
	if authInfo.UserId <= 0 {
		errcode.ErrUnauthorized.SetTo(resp)
		return resp, nil
	}

	svcReq := &svcaccount.CheckFollowingReq{
		FollowerId:  authInfo.UserId,
		FollowingId: req.FollowingId,
	}
	svcResp, err := svcmgr.AccountClient().CheckFollowing(ctx, svcReq)
	if err != nil {
		errcode.FromError(err).SetTo(resp)
		return resp, nil
	}

	// 设置响应
	resp.IsFollowing = svcResp.GetIsFollowing()
	errcode.ErrOK.SetTo(resp)
	return resp, nil
}

// GetFollowingUsers 获取关注的用户列表
func (c *Controller) GetFollowingUsers(ctx context.Context, req *bizaccount.GetFollowingUsersReq) (*bizaccount.GetFollowingUsersResp, error) {
	resp := &bizaccount.GetFollowingUsersResp{}

	if err := req.Validate(); err != nil {
		errcode.ErrorParam.SetTo(resp)
		return resp, nil
	}

	// 获取当前用户ID
	authInfo := xlaccount.GetAuthInfo(ctx)
	if authInfo.UserId <= 0 {
		errcode.ErrUnauthorized.SetTo(resp)
		return resp, nil
	}

	svcReq := &svcaccount.GetFollowingUsersReq{
		UserId:   authInfo.UserId,
		Page:     req.Page,
		PageSize: req.PageSize,
	}
	svcResp, err := svcmgr.AccountClient().GetFollowingUsers(ctx, svcReq)
	if err != nil {
		errcode.FromError(err).SetTo(resp)
		return resp, nil
	}

	// 转换数据
	data := &bizaccount.GetFollowingUsersRespData{
		Users: svcResp.GetData().GetUsers(),
		Total: svcResp.GetData().GetTotal(),
	}

	errcode.ErrOK.SetTo(resp, data)
	return resp, nil
}

// GetFollowers 获取粉丝列表
func (c *Controller) GetFollowers(ctx context.Context, req *bizaccount.GetFollowersReq) (*bizaccount.GetFollowersResp, error) {
	resp := &bizaccount.GetFollowersResp{}

	if err := req.Validate(); err != nil {
		errcode.ErrorParam.SetTo(resp)
		return resp, nil
	}

	// 获取当前用户ID
	authInfo := xlaccount.GetAuthInfo(ctx)
	if authInfo.UserId <= 0 {
		errcode.ErrUnauthorized.SetTo(resp)
		return resp, nil
	}

	svcReq := &svcaccount.GetFollowersReq{
		UserId:   authInfo.UserId,
		Page:     req.Page,
		PageSize: req.PageSize,
	}
	svcResp, err := svcmgr.AccountClient().GetFollowers(ctx, svcReq)
	if err != nil {
		errcode.FromError(err).SetTo(resp)
		return resp, nil
	}

	// 转换数据
	data := &bizaccount.GetFollowersRespData{
		Users: svcResp.GetData().GetUsers(),
		Total: svcResp.GetData().GetTotal(),
	}

	errcode.ErrOK.SetTo(resp, data)
	return resp, nil
}

// BatchCheckFollowing 批量检查是否关注
func (c *Controller) BatchCheckFollowing(ctx context.Context, req *bizaccount.BatchCheckFollowingReq) (*bizaccount.BatchCheckFollowingResp, error) {
	resp := &bizaccount.BatchCheckFollowingResp{}

	if err := req.Validate(); err != nil {
		errcode.ErrorParam.SetTo(resp)
		return resp, nil
	}

	if len(req.FollowingIds) == 0 {
		errcode.ErrorParam.SetTo(resp)
		return resp, nil
	}

	// 获取当前用户ID
	authInfo := xlaccount.GetAuthInfo(ctx)
	if authInfo.UserId <= 0 {
		errcode.ErrUnauthorized.SetTo(resp)
		return resp, nil
	}

	svcReq := &svcaccount.BatchCheckFollowingReq{
		FollowerId:   authInfo.UserId,
		FollowingIds: req.FollowingIds,
	}
	svcResp, err := svcmgr.AccountClient().BatchCheckFollowing(ctx, svcReq)
	if err != nil {
		errcode.FromError(err).SetTo(resp)
		return resp, nil
	}
	resp.Data = svcResp.Data
	errcode.ErrOK.SetTo(resp)
	return resp, nil
}
