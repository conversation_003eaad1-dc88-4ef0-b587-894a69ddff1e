package controller

import (
	"context"
	"fmt"
	"new-gitlab.xunlei.cn/vcproject/backends/baselib/errcode"
	"new-gitlab.xunlei.cn/vcproject/backends/baselib/logger"
	"new-gitlab.xunlei.cn/vcproject/backends/baselib/review"
	"new-gitlab.xunlei.cn/vcproject/backends/baselib/review/shumei"
	"new-gitlab.xunlei.cn/vcproject/backends/baselib/util"
	"new-gitlab.xunlei.cn/vcproject/backends/proto/api/common"
	"new-gitlab.xunlei.cn/vcproject/backends/proto/api/svcreview"
	"new-gitlab.xunlei.cn/vcproject/backends/svcreview/services"
)

func (c Controller) ReviewMoment(ctx context.Context, req *svcreview.ReviewMomentReq) (*common.SvcCommonResp, error) {
	logger.Infof("ReviewMonent %#v", req)
	if req.Moid <= 0 {
		return ErrParamBad, nil
	}
	if req.CallbackData == nil {
		return ErrParamBad, nil
	}
	if len(req.Text) == 0 || len(req.Images) == 0 { //目前只支持图文
		return ErrParamBad, nil
	}
	//把内容存到数据库日志
	txt := util.JsonStr(req.Text)
	images := util.JsonStr(req.Images)
	// 动态审核是异步的，先记录初始状态，回调时更新完整结果
	_, err := services.RecordReviewLogWithAuditResult(req.Userid, review.BizTypeMoment, req.Moid, fmt.Sprintf("%s,%s", txt, images), 0, nil)
	if err != nil {
		logger.Errorf("error record review log %v", err)
		return nil, err
	}
	// 上报数美API调用指标
	c.metrics.ReportShumeiAPICall(ctx, svcreview.ReviewBizType_Moment,
		services.ContentTypeMix, services.APITypeAsync, true)

	//开始调用
	_, err = shumei.MixScan(req.Userid, []string{req.Text}, req.Images, fmt.Sprintf("%d", req.Moid), review.BizTypeMoment,
		req.Country, req.Lang, &shumei.MixCallbackData{Moid: req.Moid})
	logger.Infof("ReviewMoment req: %v,err:%v", req, err)

	// 上报API调用结果（异步审核，这里只上报API调用）
	c.metrics.ReportShumeiAPICall(ctx, svcreview.ReviewBizType_Moment,
		services.ContentTypeMix, services.APITypeAsync, err == nil)

	if err != nil {
		return &common.SvcCommonResp{
			Base: errcode.ErrorParam.ToSvcBaseResp(),
		}, nil
	}

	return &common.SvcCommonResp{
		Base: errcode.ErrOK.ToSvcBaseResp(),
	}, nil
}
