package controller

import (
	"context"
	"fmt"
	"new-gitlab.xunlei.cn/vcproject/backends/baselib/errcode"
	"new-gitlab.xunlei.cn/vcproject/backends/baselib/logger"
	"new-gitlab.xunlei.cn/vcproject/backends/baselib/review"
	"new-gitlab.xunlei.cn/vcproject/backends/baselib/review/shumei"
	"new-gitlab.xunlei.cn/vcproject/backends/baselib/util"
	"new-gitlab.xunlei.cn/vcproject/backends/proto/api/common"
	"new-gitlab.xunlei.cn/vcproject/backends/proto/api/svcreview"
	"new-gitlab.xunlei.cn/vcproject/backends/svcreview/services"
)

func (c Controller) ReviewScript(ctx context.Context, req *svcreview.ReviewScriptReq) (*svcreview.ReviewCommonResp, error) {
	result := &svcreview.ReviewCommonResp{
		Base: &common.SvcBaseResp{},
	}

	var auditResult svcreview.AuditResult
	var err error
	switch req.BizType {
	case svcreview.ReviewBizType_ScriptTitle,
		svcreview.ReviewBizType_ScriptLine,
		svcreview.ReviewBizType_ScriptTopic,
		svcreview.ReviewBizType_ScriptDubbingText,
		svcreview.ReviewBizType_ScriptCommentText:
		auditResult, err = c.shumeiReviewScriptTextInfo(ctx, req)

	case svcreview.ReviewBizType_ScriptCover:
		auditResult, err = c.shumeiReviewScriptImageInfo(ctx, req)

	case svcreview.ReviewBizType_ScriptDubbingVoice,
		svcreview.ReviewBizType_ScriptCommentVoice:
		auditResult, err = c.shumeiReviewScriptAudioInfo(ctx, req)
	}

	result.Result = auditResult

	if err != nil {
		result.Base = errcode.ErrNotOK.ToSvcBaseResp()
	} else {
		result.Base = errcode.ErrOK.ToSvcBaseResp()
	}

	return result, err
}

func (c Controller) shumeiReviewScriptTextInfo(ctx context.Context, req *svcreview.ReviewScriptReq) (auditResult svcreview.AuditResult, err error) {
	bizType := ""
	switch req.BizType {
	case svcreview.ReviewBizType_ScriptTitle:
		bizType = review.BizTypeScriptTitle
	case svcreview.ReviewBizType_ScriptLine:
		bizType = review.BizTypeScriptLine
	case svcreview.ReviewBizType_ScriptTopic:
		bizType = review.BizTypeScriptTopic
	case svcreview.ReviewBizType_ScriptDubbingText:
		bizType = review.BizTypeScriptDubbingText
	case svcreview.ReviewBizType_ScriptCommentText:
		bizType = review.BizTypeScriptCommentText
	default:
		return
	}

	peerId := req.UserId
	if req.CallbackData != nil && req.CallbackData.Id != 0 {
		peerId = req.CallbackData.Id
	}

	// 上报数美API调用指标
	contentType := c.metrics.GetContentTypeFromBizType(req.BizType)
	apiType := c.metrics.GetAPITypeFromBizType(req.BizType)

	shumeiRes, err := shumei.TextScan(req.UserId, peerId, 0, 0, req.Text[0], bizType, "", nil, false)

	// 上报API调用结果
	c.metrics.ReportShumeiAPICall(ctx, req.BizType, contentType, apiType, err == nil)

	if err != nil {
		logger.Errorf("error audit text result %v", err)
		return
	}

	auditResult = c.GetShumeiAuditResult(shumeiRes)

	// 记录增强的审核日志（包含风险原因）
	content := util.JsonStr(req)
	_, logErr := services.RecordReviewLogWithAuditResult(req.UserId, bizType, req.Id, content, int(auditResult), &shumeiRes)
	if logErr != nil {
		logger.Errorf("error record enhanced review log %v", logErr)
	}

	// 上报同步审核结果
	resultStatus := c.metrics.GetResultStatusFromShumeiResult(auditResult)
	c.metrics.ReportSyncReviewResult(ctx, req.BizType, contentType, resultStatus, true)

	return
}

func (c Controller) shumeiReviewScriptImageInfo(ctx context.Context, req *svcreview.ReviewScriptReq) (auditResult svcreview.AuditResult, err error) {
	bizType := ""
	if len(req.Images) == 0 {
		return
	}

	switch req.BizType {
	case svcreview.ReviewBizType_ScriptCover:
		bizType = review.BizTypeScriptCover
	default:
		return
	}

	// 上报数美API调用指标
	contentType := c.metrics.GetContentTypeFromBizType(req.BizType)
	apiType := c.metrics.GetAPITypeFromBizType(req.BizType)

	url := req.Images[0]
	result, err := shumei.ImageScan(req.UserId, 0, 0, 0, url, bizType, "", nil)

	// 上报API调用结果
	c.metrics.ReportShumeiAPICall(ctx, req.BizType, contentType, apiType, err == nil)

	if err != nil {
		logger.Errorf("error audit img result %v", err)
		return
	}

	auditResult = c.GetShumeiAuditResult(result)

	// 记录增强的审核日志（包含风险原因）
	content := util.JsonStr(req)
	_, logErr := services.RecordReviewLogWithAuditResult(req.UserId, bizType, req.Id, content, int(auditResult), &result)
	if logErr != nil {
		logger.Errorf("error record enhanced review log %v", logErr)
	}

	// 上报同步审核结果
	resultStatus := c.metrics.GetResultStatusFromShumeiResult(auditResult)
	c.metrics.ReportSyncReviewResult(ctx, req.BizType, contentType, resultStatus, true)

	return auditResult, nil
}

func (c Controller) shumeiReviewScriptAudioInfo(ctx context.Context, req *svcreview.ReviewScriptReq) (auditResult svcreview.AuditResult, err error) {
	if req.Voice == nil || len(req.Voice.Id) == 0 || len(req.Voice.Url) == 0 {
		return
	}
	content := util.JsonStr(req)

	bizType := ""
	var bizId int64
	switch req.BizType {
	case svcreview.ReviewBizType_ScriptDubbingVoice:
		bizType = review.BizTypeScriptDubbingAudio
		bizId = req.Id // 配音ID
	case svcreview.ReviewBizType_ScriptCommentVoice:
		bizType = review.BizTypeScriptCommentAudio
		bizId = req.Id // 评论ID
	}

	// 音频审核是异步的，先记录初始状态，回调时更新完整结果
	seqId, err := services.RecordReviewLogWithAuditResult(req.UserId, bizType, bizId, content, 0, nil)
	if err != nil {
		logger.Errorf("error record review log %v", err)
		return
	}

	// 上报数美API调用指标
	contentType := c.metrics.GetContentTypeFromBizType(req.BizType)
	apiType := c.metrics.GetAPITypeFromBizType(req.BizType)

	var result shumei.AuditResultShumei
	result, err = shumei.AudioScan(req.UserId, req.UserId, 0, 0, fmt.Sprintf("%d-%s", req.UserId, req.Voice.Id),
		req.Voice.Url, bizType, "", &shumei.Origin{Content: content, SeqId: seqId, BizType: bizType})

	// 上报API调用结果（音频审核是异步的，这里只上报API调用）
	c.metrics.ReportShumeiAPICall(ctx, req.BizType, contentType, apiType, err == nil)

	if err != nil {
		logger.Errorf("error audit audio result %v", err)
		return
	}
	return c.GetShumeiAuditResult(result), nil
}
