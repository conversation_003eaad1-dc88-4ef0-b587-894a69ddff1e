package controller

import (
	"context"
	"errors"
	"new-gitlab.xunlei.cn/vcproject/backends/baselib/errcode"
	"new-gitlab.xunlei.cn/vcproject/backends/baselib/logger"
	"new-gitlab.xunlei.cn/vcproject/backends/baselib/review"
	"new-gitlab.xunlei.cn/vcproject/backends/baselib/review/shumei"
	"new-gitlab.xunlei.cn/vcproject/backends/baselib/util"
	"new-gitlab.xunlei.cn/vcproject/backends/proto/api/common"
	"new-gitlab.xunlei.cn/vcproject/backends/proto/api/svcreview"
	"new-gitlab.xunlei.cn/vcproject/backends/svcreview/services"
)

var (
	ErrReviewCallbackOriginEmpty = errors.New("review callback origin is empty")
	ErrReviewCallbackOriginBad   = errors.New("review callback origin is bad")
)

// 数美语音回调
func (c Controller) ShumeiAudioCallback(ctx context.Context, req *svcreview.ShumeiAudioCallbackReq) (resp *common.SvcCommonResp, err error) {
	logger.Infof("ShumeiAudioCallback %#v", req)
	resp = &common.SvcCommonResp{
		Base: &common.SvcBaseResp{},
	}
	if req.CallbackParam == nil {
		logger.Errorf("ShumeiAudioCallback callbackParam:%#v, is nil", req)
		return resp, nil
	}

	var result shumei.AuditResultShumei
	reason := ""
	if req.Code == int32(shumei.CodeSuccess) {
		result.RiskLevel = shumei.RiskLevel(req.RiskLevel)
		result.RequestId = req.RequestId
		result.BtId = req.BtId

		// 从detail中提取风险信息
		if len(req.Detail) > 0 {
			detail := req.Detail[0]
			result.RiskDescription = detail.Description
			reason = detail.Description
		}

		// 从labels中解析风险标签（如果有的话）
		if req.Labels != "" {
			// labels通常是JSON格式，这里简化处理
			result.RiskDescription = req.Labels
			if reason == "" {
				reason = req.Labels
			}
		}
	} else {
		result.RiskLevel = shumei.RiskLevelReview
		result.RequestId = req.RequestId
		result.BtId = req.BtId
		result.RiskDescription = req.Message
		reason = req.Message
	}
	auditResult := c.GetShumeiAuditResult(result)
	userid := int64(0)
	r := &svcreview.ReviewResultDetail{
		Result: auditResult,
	}
	r.Voice = &svcreview.ReviewVoiceResult{
		Result: auditResult,
		Reason: reason,
	}

	switch req.CallbackParam.BizType {
	case review.BizTypeSignVoice:
		reqInfo := &svcreview.ReviewMemberInfoReq{}
		err = util.Unmarshal([]byte(req.CallbackParam.Content), reqInfo)
		if err != nil {
			logger.Errorf("ShumeiAudioCallback req:%#v,content:%v, err:%v", req, req.CallbackParam.Content, err)
			return resp, nil
		}
		logger.Infof("ShumeiAudioCallback reqInfo:%v", reqInfo)
		if reqInfo.CallbackData != nil {
			err = c.r.CallbackMember(&svcreview.ReviewMemberInfoCallbackData{
				Userid: reqInfo.Userid,
				Id:     reqInfo.CallbackData.Id,
				Type:   reqInfo.CallbackData.Type,
			}, &svcreview.ReviewResultDetail{
				Result: auditResult,
				Voice: &svcreview.ReviewVoiceResult{
					Voice:  reqInfo.Voice,
					Result: auditResult,
					Reason: reason,
				},
			})

			// 上报异步回调结果指标
			resultStatus := c.metrics.GetResultStatusFromShumeiResult(auditResult)
			c.metrics.ReportAsyncCallbackResult(ctx, svcreview.ReviewBizType_UserSignVoice,
				services.ContentTypeAudio, resultStatus, err == nil)
		}
		userid = reqInfo.Userid

	case review.BizTypeScriptDubbingAudio,
		review.BizTypeScriptCommentAudio:
		reqInfo := &svcreview.ReviewScriptReq{}
		err = util.Unmarshal([]byte(req.CallbackParam.Content), reqInfo)
		if err != nil {
			logger.Errorf("ShumeiAudioCallback req:%#v,content:%v, err:%v", req, req.CallbackParam.Content, err)
			return resp, nil
		}

		if reqInfo.CallbackData != nil {
			err = c.r.CallbackScript(&svcreview.ReviewScriptCallbackData{
				Userid: reqInfo.UserId,
				Id:     reqInfo.CallbackData.Id,
				Type:   reqInfo.CallbackData.Type,
			}, &svcreview.ReviewResultDetail{
				Result: auditResult,
				Voice: &svcreview.ReviewVoiceResult{
					Voice:  reqInfo.Voice,
					Result: auditResult,
				},
			})

			// 上报异步回调结果指标
			resultStatus := c.metrics.GetResultStatusFromShumeiResult(auditResult)
			var bizType svcreview.ReviewBizType
			if req.CallbackParam.BizType == review.BizTypeScriptDubbingAudio {
				bizType = svcreview.ReviewBizType_ScriptDubbingVoice
			} else {
				bizType = svcreview.ReviewBizType_ScriptCommentVoice
			}
			c.metrics.ReportAsyncCallbackResult(ctx, bizType,
				services.ContentTypeAudio, resultStatus, err == nil)
		}
		userid = reqInfo.UserId
	}

	if err != nil {
		logger.Errorf("ShumeiAudioCallback CallbackChat ,r:%#v, err:%v", r, err.Error())
	}

	if req.CallbackParam.Seqid != 0 {
		// 使用增强的更新方法，保存完整的审核结果详情
		err = services.UpdateReviewLogWithAuditResult(userid, req.CallbackParam.Seqid, int(auditResult), &result)
		if err != nil {
			logger.Errorf("ShumeiAudioCallback UpdateReviewLogWithAuditResult, err:%v", err.Error())
		}
	}

	return
}

func (c Controller) ReviewCallback(ctx context.Context, req *svcreview.ReviewCallbackReq) (resp *common.SvcCommonResp, err error) {
	logger.Infof("ReviewCallback %#v", req)
	switch req.BizType {
	case review.BizTypeAvatar,
		review.BizTypeNickname,
		review.BizTypeLoveWords,
		review.BizTypeAlbum,
		review.BizTypeSignVoice:
		resp, err = c.reviewCallbackMember(req)
	case review.BizTypeChat,
		review.BizTypeChatImage,
		review.BizTypeChatAudio,
		review.BizTypeChatVideo,
		review.BizTypeCommonMsg:
		resp, err = c.reviewCallbackChat(req)
	case review.BizTypeComment,
		review.BizTypeMoment:
		resp, err = c.reviewCallbackMonent(req)
	// case review.BizTypeRoomBg,
	// 	review.BizTypeRoomCover,
	// 	review.BizTypeRoomNotice,
	// 	review.BizTypeRoomTitle:
	// 	resp, err = c.reviewRoomProfileCallback(req)
	// case review.BizTypeFamilyName,
	// 	review.BizTypeFamilyAnnouncement,
	// 	review.BizTypeFamilyCover:
	// 	resp, err = c.reviewFamilyProfileCallback(req)
	// case review.BizUserCommonMsg:
	// 	err = c.reviewCallbackUserCommonMsg(req)
	// 	if err == nil {
	// 		resp = errcode.ErrorOK.ToSvcResp()
	// 	}
	default:
		return &common.SvcCommonResp{
			Base: &common.SvcBaseResp{
				Code: errcode.ErrorParam.Code,
				Msg:  errcode.ErrorParam.Msg,
			},
		}, nil
	}
	if err != nil || resp.Base.Code != errcode.ErrOK.Code {
		logger.Errorf("review callback fail,err:%v,req:%#v,resp:%#v", err, req, resp)
	}
	return
}

func (c Controller) reviewCallbackMember(req *svcreview.ReviewCallbackReq) (*common.SvcCommonResp, error) {
	if req.Origin == nil || len(req.Origin.Content) == 0 { // 注册用户 的头像审核时没有生成userid ,不处理
		if !review.Suggestion(req.Suggestion).IsPass() {
			logger.Warnf("review member callback %v", util.JsonStr(req))
		}
		return &common.SvcCommonResp{
			Base: &common.SvcBaseResp{
				Code: errcode.ErrOK.Code,
			},
		}, nil
	}

	reqInfo := &svcreview.ReviewMemberInfoReq{}
	err := util.Unmarshal([]byte(req.Origin.Content), reqInfo)
	if err != nil {
		return nil, err
	}

	if reqInfo.Userid == 0 {
		return nil, ErrReviewCallbackOriginBad
	}
	result := review.ReviewResult{Suggestion: review.Suggestion(req.Suggestion), Label: req.Label}
	details := make(map[string]review.ReviewResult)

	if (reqInfo.BizType == svcreview.ReviewBizType_UserNickname ||
		reqInfo.BizType == svcreview.ReviewBizType_UserTextSign) && len(reqInfo.Text) != 0 {
		details[reqInfo.Text[0]] = result
	} else if reqInfo.BizType == svcreview.ReviewBizType_UserAlbum || //图片和相册都是单张送审
		reqInfo.BizType == svcreview.ReviewBizType_UserAvatar {
		if len(reqInfo.Images) != 0 {
			for _, i := range reqInfo.Images {
				details[i.Id] = result
			}
		}
	} else if reqInfo.BizType == svcreview.ReviewBizType_UserSignVoice {
		if reqInfo.Voice != nil {
			details[reqInfo.Voice.Id] = result
		}
	}

	err = c.reviewMemberCallFunc(req.Origin.Seqid, reqInfo, result, details, false)
	if err != nil {
		return nil, err
	}
	return &common.SvcCommonResp{
		Base: &common.SvcBaseResp{
			Code: errcode.ErrOK.Code,
		},
	}, nil
}

func (c Controller) reviewCallbackChat(req *svcreview.ReviewCallbackReq) (*common.SvcCommonResp, error) {
	if req.Origin == nil {
		return nil, ErrReviewCallbackOriginEmpty
	}
	reqInfo := &svcreview.ReviewChatReq{}
	err := util.Unmarshal([]byte(req.Origin.Content), reqInfo)
	if err != nil {
		return nil, err
	}

	//result := review.ReviewResult{Suggestion: review.Suggestion(req.Suggestion), Label: req.Label}
	//err = c.reviewChatCallFunc(req.Origin.Seqid, reqInfo, result)
	//if err != nil {
	//	return nil, err
	//}

	return &common.SvcCommonResp{
		Base: &common.SvcBaseResp{
			Code: errcode.ErrOK.Code,
		},
	}, nil
}

func (c Controller) reviewCallbackUserCommonMsg(req *svcreview.ReviewCallbackReq, reqInfo *svcreview.ReviewChatReq) (err error) {
	if reqInfo.Userid == 0 {
		return ErrReviewCallbackOriginBad
	}
	if reqInfo.CallbackData != nil {
		callbackData := &svcreview.ReviewUserCommonMsgCallbackData{
			// Userid: reqInfo.CallbackData.Sid,
			// Id:     reqInfo.CallbackData.Msgid,
		}
		result := review.ReviewResult{Suggestion: review.Suggestion(req.Suggestion), Label: req.Label}
		err = c.r.CallbackChatUserCommonMsg(callbackData, &svcreview.ReviewResultDetail{
			Result: c.GetAuditResult(result),
			Text:   nil,
			Image:  nil,
			Voice:  nil,
			Video:  nil,
		})
		if err != nil {
			logger.Errorf("error callback user common msg %v", err)
			return err
		}
		return
	} else {
		return ErrReviewCallbackOriginBad
	}
}

func (c Controller) reviewCallbackMonent(req *svcreview.ReviewCallbackReq) (*common.SvcCommonResp, error) {
	// if req.Origin == nil {
	// 	return nil, ErrReviewCallbackOriginEmpty
	// }

	// reqInfo := &svcreview.ReviewMonentReq{}
	// err := util.Unmarshal([]byte(req.Origin.Content), reqInfo)
	// if err != nil {
	// 	return nil, err
	// }
	// result := review.ReviewResult{Suggestion: review.Suggestion(req.Suggestion), Label: req.Label}
	// details := make(map[string]map[string]review.ReviewResult)
	// err = c.reviewMonentCallFunc(req.Origin.Seqid, reqInfo, result, details, false)
	// if err != nil {
	// 	return nil, err
	// }
	return &common.SvcCommonResp{
		Base: &common.SvcBaseResp{
			Code: errcode.ErrOK.Code,
		},
	}, nil
}

func (c Controller) ShumeiMixCallback(ctx context.Context, req *svcreview.ShumeiMixCallbackReq) (resp *common.SvcCommonResp, err error) {
	logger.Infof("ShumeiMixCallback %#v", req)
	resp = &common.SvcCommonResp{
		Base: &common.SvcBaseResp{},
	}
	if req.PassThrough == nil {
		logger.Errorf("ShumeiMixCallback callbackParam:%#v, is nil", req)
		return resp, nil
	}
	//目前只支持图文
	var result shumei.AuditResultShumei
	result.RiskLevel = shumei.RiskLevel(req.RiskLevel)
	auditResult := c.GetShumeiAuditResult(result)
	r := &svcreview.ReviewResultDetail{
		Result: auditResult,
	}

	err = c.r.CallbackMoment(&svcreview.ReviewMultiImageCallbackData{
		Id: req.PassThrough.Id,
	}, r)

	// 上报异步回调结果指标
	resultStatus := c.metrics.GetResultStatusFromShumeiResult(auditResult)
	c.metrics.ReportAsyncCallbackResult(ctx, svcreview.ReviewBizType_Moment,
		services.ContentTypeMix, resultStatus, err == nil)

	return
}
