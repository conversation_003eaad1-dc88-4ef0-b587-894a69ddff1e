package model

import (
	"errors"
	"fmt"
	"gorm.io/gorm"
	"new-gitlab.xunlei.cn/vcproject/backends/baselib/database"
	"new-gitlab.xunlei.cn/vcproject/backends/baselib/logger"
	"new-gitlab.xunlei.cn/vcproject/backends/baselib/server/env"
	"new-gitlab.xunlei.cn/vcproject/backends/baselib/util"
)

type ReviewLog struct {
	Id          int64  `json:"id" xorm:"id" gorm:"primaryKey;column:id"`
	Userid      int64  `json:"userid" xorm:"userid" gorm:"userid"`
	BizType     string `json:"biz_type" xorm:"biz_type" gorm:"biz_type"`
	BizId       int64  `json:"biz_id" xorm:"biz_id" gorm:"biz_id"`                         // 业务ID
	Data        string `json:"data" xorm:"data" gorm:"data"`
	Status      int    `json:"status" xorm:"status" gorm:"status"`
	RequestId   string `json:"request_id" xorm:"request_id" gorm:"request_id"`
	RiskReason  string `json:"risk_reason" xorm:"risk_reason" gorm:"risk_reason"`          // 风险原因
	RiskLabels  string `json:"risk_labels" xorm:"risk_labels" gorm:"risk_labels"`          // 风险标签JSON
	RiskLevel   string `json:"risk_level" xorm:"risk_level" gorm:"risk_level"`             // 风险等级
	AuditDetail string `json:"audit_detail" xorm:"audit_detail" gorm:"audit_detail"`      // 审核详情JSON
	Ct          int64  `json:"ct" xorm:"ct" gorm:"ct"`
	Ut          int64  `json:"ut" xorm:"ut" gorm:"ut"`
}

func ReviewLogTableName(userid int64) string {
	if env.IsProd() {
		return fmt.Sprintf("review_log_%d", userid%10+1)
	}
	return "review_log"
}

func (t ReviewLog) TableCount() int {
	if env.IsProd() {
		return 10
	}
	return 1
}

type ReviewLogModel struct {
	DB *gorm.DB
}

func NewReviewLogModel() *ReviewLogModel {
	return &ReviewLogModel{
		DB: database.GetMysqlDB("vc_review"),
	}
}

func (c *ReviewLogModel) Insert(rl *ReviewLog) (seqId int64, err error) {
	err = c.DB.Table(ReviewLogTableName(rl.Userid)).Create(rl).Error
	if err != nil {
		logger.Errorf("error insert review log fail,err:%v,ra:%#v", err, rl)
		return
	}
	seqId = rl.Id
	return
}

func (c *ReviewLogModel) UpdateStatus(userid, seqId int64, status int) (err error) {
	updates := map[string]interface{}{
		"status": status,
		"ut":     util.NowTimeMillis()}
	err = c.DB.Table(ReviewLogTableName(userid)).Where("id=?", seqId).Updates(updates).Error
	if err != nil {
		logger.Errorf("error update review log fail,err:%v,seqId:%v,updates:%v", err, seqId, updates)
		return
	}
	return
}

func (c *ReviewLogModel) GetReviewLogStatus(userid, seqId int64) (status int32) {
	var result ReviewLog
	err := c.DB.Table(ReviewLogTableName(userid)).Where("id=?", seqId).First(&result).Error
	if errors.Is(err, gorm.ErrRecordNotFound) {
		return
	} else if err != nil {
		logger.Errorf("GetReviewLogStatus userid %v seqid %v err %v", userid, seqId, err)
		return
	}
	return int32(result.Status)
}

// GetReviewLogByBizId 根据业务类型和业务ID查询审核记录
func (c *ReviewLogModel) GetReviewLogByBizId(userid int64, bizType string, bizId int64) (result *ReviewLog, err error) {
	result = &ReviewLog{}
	err = c.DB.Table(ReviewLogTableName(userid)).
		Where("biz_type=? AND biz_id=?", bizType, bizId).
		Order("ct DESC").
		First(result).Error
	if errors.Is(err, gorm.ErrRecordNotFound) {
		return nil, nil
	} else if err != nil {
		logger.Errorf("GetReviewLogByBizId userid %v bizType %v bizId %v err %v", userid, bizType, bizId, err)
		return nil, err
	}
	return result, nil
}

// GetReviewLogsByBizType 根据业务类型查询审核记录列表
func (c *ReviewLogModel) GetReviewLogsByBizType(userid int64, bizType string, limit, offset int) (results []ReviewLog, total int64, err error) {
	query := c.DB.Table(ReviewLogTableName(userid)).Where("biz_type=?", bizType)

	// 获取总数
	err = query.Count(&total).Error
	if err != nil {
		logger.Errorf("GetReviewLogsByBizType count userid %v bizType %v err %v", userid, bizType, err)
		return nil, 0, err
	}

	// 获取列表
	err = query.Order("ct DESC").Limit(limit).Offset(offset).Find(&results).Error
	if err != nil {
		logger.Errorf("GetReviewLogsByBizType find userid %v bizType %v err %v", userid, bizType, err)
		return nil, 0, err
	}

	return results, total, nil
}

// GetReviewLogsByRiskLevel 根据风险等级查询审核记录
func (c *ReviewLogModel) GetReviewLogsByRiskLevel(userid int64, riskLevel string, limit, offset int) (results []ReviewLog, total int64, err error) {
	query := c.DB.Table(ReviewLogTableName(userid)).Where("risk_level=?", riskLevel)

	// 获取总数
	err = query.Count(&total).Error
	if err != nil {
		logger.Errorf("GetReviewLogsByRiskLevel count userid %v riskLevel %v err %v", userid, riskLevel, err)
		return nil, 0, err
	}

	// 获取列表
	err = query.Order("ct DESC").Limit(limit).Offset(offset).Find(&results).Error
	if err != nil {
		logger.Errorf("GetReviewLogsByRiskLevel find userid %v riskLevel %v err %v", userid, riskLevel, err)
		return nil, 0, err
	}

	return results, total, nil
}
