package services

import (
	"encoding/json"
	"new-gitlab.xunlei.cn/vcproject/backends/baselib/review/shumei"
	"new-gitlab.xunlei.cn/vcproject/backends/baselib/util"
	"new-gitlab.xunlei.cn/vcproject/backends/svcreview/model"
)

func RecordReviewLog(userid int64, bizType, data string, status int) (seqId int64, err error) {
	ts := util.NowTimeMillis()
	seqId, err = model.NewReviewLogModel().Insert(&model.ReviewLog{
		Userid:  userid,
		Data:    data,
		BizType: bizType,
		Status:  status,
		Ct:      ts,
		Ut:      ts,
	})
	return
}

func RecordReviewLogShumei(userid int64, bizType, data string, status int, requestId string) (seqId int64, err error) {
	ts := util.NowTimeMillis()
	seqId, err = model.NewReviewLogModel().Insert(&model.ReviewLog{
		Userid:    userid,
		Data:      data,
		BizType:   bizType,
		Status:    status,
		RequestId: requestId,
		Ct:        ts,
		Ut:        ts,
	})
	return
}

func UpdateReviewLogStatus(userid, seqId int64, status int) (err error) {
	return model.NewReviewLogModel().UpdateStatus(userid, seqId, status)
}

// UpdateReviewLogWithAuditResult 更新审核日志状态和审核结果详情
func UpdateReviewLogWithAuditResult(userid, seqId int64, status int, auditResult *shumei.AuditResultShumei) (err error) {
	ts := util.NowTimeMillis()
	updates := map[string]interface{}{
		"status": status,
		"ut":     ts,
	}

	// 如果有审核结果，更新相关字段
	if auditResult != nil {
		updates["risk_reason"] = auditResult.RiskDescription
		updates["risk_level"] = string(auditResult.RiskLevel)

		// 构建风险标签JSON
		if len(auditResult.AllLabels) > 0 {
			riskLabelsData := map[string]interface{}{
				"riskLabel1":      auditResult.RiskLabel1,
				"riskLabel2":      auditResult.RiskLabel2,
				"riskLabel3":      auditResult.RiskLabel3,
				"riskDescription": auditResult.RiskDescription,
				"allLabels":       auditResult.AllLabels,
			}
			if riskLabelsBytes, jsonErr := json.Marshal(riskLabelsData); jsonErr == nil {
				updates["risk_labels"] = string(riskLabelsBytes)
			}
		}

		// 构建完整审核详情JSON
		auditDetailData := map[string]interface{}{
			"riskLevel":       auditResult.RiskLevel,
			"requestId":       auditResult.RequestId,
			"btId":            auditResult.BtId,
			"riskLabel1":      auditResult.RiskLabel1,
			"riskLabel2":      auditResult.RiskLabel2,
			"riskLabel3":      auditResult.RiskLabel3,
			"riskDescription": auditResult.RiskDescription,
			"allLabels":       auditResult.AllLabels,
			"code":            auditResult.Code,
		}
		if auditDetailBytes, jsonErr := json.Marshal(auditDetailData); jsonErr == nil {
			updates["audit_detail"] = string(auditDetailBytes)
		}
	}

	return model.NewReviewLogModel().DB.Table(model.ReviewLogTableName(userid)).
		Where("id=?", seqId).Updates(updates).Error
}

func GetReviewLogStatus(userid, seqId int64) (status int32) {
	return model.NewReviewLogModel().GetReviewLogStatus(userid, seqId)
}

// RecordReviewLogWithAuditResult 记录审核日志（包含完整的审核结果）
func RecordReviewLogWithAuditResult(userid int64, bizType string, bizId int64, data string, status int, auditResult *shumei.AuditResultShumei) (seqId int64, err error) {
	ts := util.NowTimeMillis()

	reviewLog := &model.ReviewLog{
		Userid:    userid,
		BizType:   bizType,
		BizId:     bizId,
		Data:      data,
		Status:    status,
		Ct:        ts,
		Ut:        ts,
	}

	// 如果有审核结果，填充相关字段
	if auditResult != nil {
		reviewLog.RequestId = auditResult.RequestId
		reviewLog.RiskReason = auditResult.RiskDescription
		reviewLog.RiskLevel = string(auditResult.RiskLevel)

		// 构建风险标签JSON
		if len(auditResult.AllLabels) > 0 {
			riskLabelsData := map[string]interface{}{
				"riskLabel1":      auditResult.RiskLabel1,
				"riskLabel2":      auditResult.RiskLabel2,
				"riskLabel3":      auditResult.RiskLabel3,
				"riskDescription": auditResult.RiskDescription,
				"allLabels":       auditResult.AllLabels,
			}
			if riskLabelsBytes, jsonErr := json.Marshal(riskLabelsData); jsonErr == nil {
				reviewLog.RiskLabels = string(riskLabelsBytes)
			}
		}

		// 构建完整审核详情JSON
		auditDetailData := map[string]interface{}{
			"riskLevel":       auditResult.RiskLevel,
			"requestId":       auditResult.RequestId,
			"btId":            auditResult.BtId,
			"riskLabel1":      auditResult.RiskLabel1,
			"riskLabel2":      auditResult.RiskLabel2,
			"riskLabel3":      auditResult.RiskLabel3,
			"riskDescription": auditResult.RiskDescription,
			"allLabels":       auditResult.AllLabels,
			"code":            auditResult.Code,
		}
		if auditDetailBytes, jsonErr := json.Marshal(auditDetailData); jsonErr == nil {
			reviewLog.AuditDetail = string(auditDetailBytes)
		}
	}

	seqId, err = model.NewReviewLogModel().Insert(reviewLog)
	return
}

// GetReviewLogByBizId 根据业务ID查询审核记录
func GetReviewLogByBizId(userid int64, bizType string, bizId int64) (*model.ReviewLog, error) {
	return model.NewReviewLogModel().GetReviewLogByBizId(userid, bizType, bizId)
}

// GetReviewLogsByBizType 根据业务类型查询审核记录列表
func GetReviewLogsByBizType(userid int64, bizType string, limit, offset int) ([]model.ReviewLog, int64, error) {
	return model.NewReviewLogModel().GetReviewLogsByBizType(userid, bizType, limit, offset)
}

// GetReviewLogsByRiskLevel 根据风险等级查询审核记录
func GetReviewLogsByRiskLevel(userid int64, riskLevel string, limit, offset int) ([]model.ReviewLog, int64, error) {
	return model.NewReviewLogModel().GetReviewLogsByRiskLevel(userid, riskLevel, limit, offset)
}
