package controller

import (
	"context"

	"new-gitlab.xunlei.cn/vcproject/backends/baselib/errcode"
	"new-gitlab.xunlei.cn/vcproject/backends/baselib/logger"
	"new-gitlab.xunlei.cn/vcproject/backends/baselib/xlaccount"
	"new-gitlab.xunlei.cn/vcproject/backends/proto/api/bizactivity"
	"new-gitlab.xunlei.cn/vcproject/backends/proto/api/svcactivity"
	"new-gitlab.xunlei.cn/vcproject/backends/proto/svcmgr"
)

var (
	_ bizactivity.SServer = new(Controller)
)

type Controller struct{}

func NewController() *Controller {
	svcmgr.InitServices()
	return &Controller{}
}

func (c *Controller) DailyCheckIn(ctx context.Context, req *bizactivity.DailyCheckInReq) (resp *bizactivity.DailyCheckInResp, err error) {
	resp = &bizactivity.DailyCheckInResp{}
	if tmpErr := req.ValidateAll(); tmpErr != nil {
		errcode.ErrorParam.SetTo(resp)
		return
	}
	authorInfo := xlaccount.GetAuthInfo(ctx)
	if authorInfo == nil || authorInfo.UserId == 0 {
		errcode.ErrUnauthorized.SetTo(resp)
		return
	}
	vResp, tmpErr := svcmgr.ActivityClient().DailyCheckIn(ctx, &svcactivity.DailyCheckInReq{
		UserId: authorInfo.UserId,
	})
	if tmpErr != nil || errcode.NotOk(vResp) {
		logger.Errorf("DailyCheckIn err=%+v vResp=%+v", tmpErr, vResp)
		errcode.ErrDailyCheckInFailed.SetTo(resp)
		return
	}
	errcode.ErrOK.SetTo(resp, vResp.Data)
	return
}
