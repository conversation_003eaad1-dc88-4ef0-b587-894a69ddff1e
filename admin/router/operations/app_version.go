package operations

import (
	"github.com/flipped-aurora/gin-vue-admin/server/middleware"
	"github.com/gin-gonic/gin"
)

type AppVersionRouter struct{}

// InitAppVersionRouter 初始化APP版本管理路由信息
func (s *AppVersionRouter) InitAppVersionRouter(Router *gin.RouterGroup) {
	appVersionRouter := Router.Group("appVersion").Use(middleware.OperationRecord())
	appVersionRouterWithoutRecord := Router.Group("appVersion")
	{
		appVersionRouter.POST("createAppVersion", appVersionApi.CreateAppVersion)       // 新建APP版本
		appVersionRouter.DELETE("deleteAppVersion", appVersionApi.DeleteAppVersion)     // 删除APP版本
		appVersionRouter.DELETE("deleteAppVersionByIds", appVersionApi.DeleteAppVersionByIds) // 批量删除APP版本
		appVersionRouter.PUT("updateAppVersion", appVersionApi.UpdateAppVersion)        // 更新APP版本
		appVersionRouter.POST("publishAppVersion", appVersionApi.PublishAppVersion)     // 发布APP版本
		appVersionRouter.POST("offlineAppVersion", appVersionApi.OfflineAppVersion)     // 下线APP版本
	}
	{
		appVersionRouterWithoutRecord.GET("findAppVersion", appVersionApi.FindAppVersion)           // 根据ID获取APP版本
		appVersionRouterWithoutRecord.GET("getAppVersionList", appVersionApi.GetAppVersionList)     // 获取APP版本列表
		appVersionRouterWithoutRecord.GET("getPlatformOptions", appVersionApi.GetPlatformOptions)   // 获取平台选项
		appVersionRouterWithoutRecord.GET("getStatusOptions", appVersionApi.GetStatusOptions)       // 获取状态选项
	}
}
