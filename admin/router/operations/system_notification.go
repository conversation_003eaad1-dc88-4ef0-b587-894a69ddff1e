package operations

import (
	"github.com/flipped-aurora/gin-vue-admin/server/middleware"
	"github.com/gin-gonic/gin"
)

type SystemNotificationRouter struct{}

// InitSystemNotificationRouter 初始化系统通知路由信息
func (s *SystemNotificationRouter) InitSystemNotificationRouter(Router *gin.RouterGroup) {
	systemNotificationRouter := Router.Group("systemNotification").Use(middleware.OperationRecord())
	systemNotificationRouterWithoutRecord := Router.Group("systemNotification")
	{
		systemNotificationRouter.POST("sendSystemNotification", systemNotificationApi.SendSystemNotification) // 发送系统通知
	}
	{
		systemNotificationRouterWithoutRecord.GET("getTemplateJumpTypes", systemNotificationApi.GetTemplateJumpTypes) // 获取跳转类型枚举
		systemNotificationRouterWithoutRecord.GET("getSystemNotificationRecords", systemNotificationApi.GetSystemNotificationRecords) // 获取系统通知记录列表
		systemNotificationRouterWithoutRecord.GET("getJumpUrlOptions", systemNotificationApi.GetJumpUrlOptions) // 获取跳转地址选项列表
	}
}
