package users

import (
	"github.com/flipped-aurora/gin-vue-admin/server/middleware"
	"github.com/gin-gonic/gin"
)

type UserRouter struct{}

// InitUserRouter 初始化 users表 路由信息
func (s *UserRouter) InitUserRouter(Router *gin.RouterGroup, PublicRouter *gin.RouterGroup) {
	userRouter := Router.Group("user").Use(middleware.OperationRecord())
	userRouterWithoutRecord := Router.Group("user")
	{
		userRouter.POST("createUser", userApi.CreateUser)                   // 创建用户
		userRouter.PUT("updateUser", userApi.UpdateUser)                    // 更新users表
		userRouter.POST("rewardUserScore", userApi.RewardScore)             // 发放用户奖励
		userRouter.POST("resetUserInfo", userApi.ResetUserInfo)             // 重置用户信息
		userRouter.POST("banUser", userApi.BanUser)                         // 封禁用户
		userRouter.POST("unbanUser", userApi.UnbanUser)                     // 解封用户
		userRouter.POST("setPremiumCreator", userApi.SetPremiumCreator)     // 设置优质创作者
	}
	{
		userRouterWithoutRecord.GET("findUser", userApi.FindUser)       // 根据ID获取users表
		userRouterWithoutRecord.GET("getUserList", userApi.GetUserList) // 获取users表列表
		userRouterWithoutRecord.GET("getUserInfoByCreationUserId", userApi.GetUserInfoByCreationUserId) // 根据admin用户ID获取关联的业务用户信息
	}
	{
		userRouterWithoutRecord.GET("getUserScore", userScoreApi.FindUserScore) // 获取用户积分
	}
}
