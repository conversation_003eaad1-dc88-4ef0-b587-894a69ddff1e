package users

import (
	"github.com/gin-gonic/gin"
)

type UserScoreLogRouter struct{}

// InitUserScoreLogRouter 初始化 userScoreLog表 路由信息
func (s *UserScoreLogRouter) InitUserScoreLogRouter(Router *gin.RouterGroup, PublicRouter *gin.RouterGroup) {
	userScoreLogRouterWithoutRecord := Router.Group("userScoreLog")
	{
		userScoreLogRouterWithoutRecord.GET("findUserScoreLog", userScoreLogApi.FindUserScoreLog)        // 根据ID获取userScoreLog表
		userScoreLogRouterWithoutRecord.POST("getUserScoreLogList", userScoreLogApi.GetUserScoreLogList) // 获取userScoreLog表列表
	}
}
