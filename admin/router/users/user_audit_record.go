package users

import (
	"github.com/flipped-aurora/gin-vue-admin/server/api/v1"
	"github.com/flipped-aurora/gin-vue-admin/server/middleware"
	"github.com/gin-gonic/gin"
)

type UserAuditRecordRouter struct{}

// InitUserAuditRecordRouter 初始化用户审核记录路由信息
func (s *UserAuditRecordRouter) InitUserAuditRecordRouter(Router *gin.RouterGroup, PublicRouter *gin.RouterGroup) {
	userAuditRecordRouter := Router.Group("userAuditRecord").Use(middleware.OperationRecord())
	userAuditRecordRouterWithoutRecord := Router.Group("userAuditRecord")

	var userAuditRecordApi = v1.ApiGroupApp.UsersApiGroup.UserAuditRecordApi
	{
		userAuditRecordRouter.POST("approveUserAuditRecord", userAuditRecordApi.ApproveUserAuditRecord)             // 审核通过用户审核记录
		userAuditRecordRouter.POST("rejectUserAuditRecord", userAuditRecordApi.RejectUserAuditRecord)               // 审核拒绝用户审核记录
		userAuditRecordRouter.POST("batchApproveUserAuditRecords", userAuditRecordApi.BatchApproveUserAuditRecords) // 批量审核通过用户审核记录
		userAuditRecordRouter.POST("batchRejectUserAuditRecords", userAuditRecordApi.BatchRejectUserAuditRecords)   // 批量审核拒绝用户审核记录
	}
	{
		userAuditRecordRouterWithoutRecord.GET("getUserAuditRecordList", userAuditRecordApi.GetUserAuditRecordList) // 获取用户审核记录列表
		userAuditRecordRouterWithoutRecord.GET("findUserAuditRecord", userAuditRecordApi.FindUserAuditRecord)       // 根据ID获取用户审核记录
	}
}
