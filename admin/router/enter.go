package router

import (
	"github.com/flipped-aurora/gin-vue-admin/server/router/characters"
	"github.com/flipped-aurora/gin-vue-admin/server/router/example"
	"github.com/flipped-aurora/gin-vue-admin/server/router/ips"
	"github.com/flipped-aurora/gin-vue-admin/server/router/mymoments"
	"github.com/flipped-aurora/gin-vue-admin/server/router/operations"
	"github.com/flipped-aurora/gin-vue-admin/server/router/scripts"
	"github.com/flipped-aurora/gin-vue-admin/server/router/system"
	"github.com/flipped-aurora/gin-vue-admin/server/router/users"
)

var RouterGroupApp = new(RouterGroup)

type RouterGroup struct {
	System     system.RouterGroup
	Example    example.RouterGroup
	Mymoments  mymoments.RouterGroup
	Characters characters.RouterGroup
	Ips        ips.RouterGroup
	Scripts    scripts.RouterGroup
	Users      users.RouterGroup
	Operations operations.RouterGroup
}
