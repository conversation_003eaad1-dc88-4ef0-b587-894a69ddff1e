package characters

import (
	"github.com/flipped-aurora/gin-vue-admin/server/middleware"
	"github.com/gin-gonic/gin"
)

type CharacterRouter struct{}

// InitCharacterRouter 初始化 characters表 路由信息
func (s *CharacterRouter) InitCharacterRouter(Router *gin.RouterGroup, PublicRouter *gin.RouterGroup) {
	characterRouter := Router.Group("character").Use(middleware.OperationRecord())
	characterRouterWithoutRecord := Router.Group("character")
	{
		characterRouter.POST("createCharacter", characterApi.CreateCharacter)                         // 新建characters表
		characterRouter.DELETE("deleteCharacter", characterApi.DeleteCharacter)                       // 删除characters表
		characterRouter.DELETE("deleteCharacterByIds", characterApi.DeleteCharacterByIds)             // 批量删除characters表
		characterRouter.PUT("updateCharacter", characterApi.UpdateCharacter)                          // 更新characters表
		characterRouter.PUT("setCharacterRecommended", characterApi.SetCharacterRecommended)          // 设置角色推荐状态
		characterRouter.PUT("batchSetCharacterRecommended", characterApi.BatchSetCharacterRecommended) // 批量设置角色推荐状态
	}
	{
		characterRouterWithoutRecord.GET("findCharacter", characterApi.FindCharacter)                       // 根据ID获取characters表
		characterRouterWithoutRecord.GET("getCharacterList", characterApi.GetCharacterList)                 // 获取characters表列表
		characterRouterWithoutRecord.GET("getRecommendedCharacterList", characterApi.GetRecommendedCharacterList) // 获取推荐角色列表
	}
}
