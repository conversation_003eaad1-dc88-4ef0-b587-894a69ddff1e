package characters

import (
	"github.com/flipped-aurora/gin-vue-admin/server/middleware"
	"github.com/gin-gonic/gin"
)

type CharacterAssetsRouter struct{}

// InitCharacterAssetsRouter 初始化 characterAssets表 路由信息
func (s *CharacterAssetsRouter) InitCharacterAssetsRouter(Router *gin.RouterGroup, PublicRouter *gin.RouterGroup) {
	characterAssetsRouter := Router.Group("characterAssets").Use(middleware.OperationRecord())
	characterAssetsRouterWithoutRecord := Router.Group("characterAssets")
	{
		characterAssetsRouter.POST("createCharacterAssets", characterAssetsApi.CreateCharacterAssets)             // 新建characterAssets表
		characterAssetsRouter.DELETE("deleteCharacterAssets", characterAssetsApi.DeleteCharacterAssets)           // 删除characterAssets表
		characterAssetsRouter.DELETE("deleteCharacterAssetsByIds", characterAssetsApi.DeleteCharacterAssetsByIds) // 批量删除characterAssets表
		characterAssetsRouter.PUT("updateCharacterAssets", characterAssetsApi.UpdateCharacterAssets)              // 更新characterAssets表
		characterAssetsRouter.POST("updatePresetStatus", characterAssetsApi.UpdatePresetStatus)                   // 更新预设状态
	}
	{
		characterAssetsRouterWithoutRecord.GET("findCharacterAssets", characterAssetsApi.FindCharacterAssets)       // 根据ID获取characterAssets表
		characterAssetsRouterWithoutRecord.GET("getCharacterAssetsList", characterAssetsApi.GetCharacterAssetsList) // 获取characterAssets表列表
	}
}
