package ips

import (
	"github.com/flipped-aurora/gin-vue-admin/server/middleware"
	"github.com/gin-gonic/gin"
)

type IpsRouter struct{}

// InitIpsRouter 初始化 ips表 路由信息
func (s *IpsRouter) InitIpsRouter(Router *gin.RouterGroup, PublicRouter *gin.RouterGroup) {
	IpRouter := Router.Group("Ip").Use(middleware.OperationRecord())
	IpRouterWithoutRecord := Router.Group("Ip")
	{
		IpRouter.POST("createIps", IpApi.CreateIps)             // 新建ips表
		IpRouter.DELETE("deleteIp", IpApi.DeleteIp)             // 删除ips表
		IpRouter.DELETE("deleteIpsByIds", IpApi.DeleteIpsByIds) // 批量删除ips表
		IpRouter.PUT("updateIps", IpApi.UpdateIps)              // 更新ips表
	}
	{
		IpRouterWithoutRecord.GET("findIps", IpApi.FindIps)       // 根据ID获取ips表
		IpRouterWithoutRecord.GET("getIpsList", IpApi.GetIpsList) // 获取ips表列表
		IpRouterWithoutRecord.GET("getAllIps", IpApi.GetAllIps)   // 获取全部的ips
	}
}
