package mymoments

import (
	"github.com/flipped-aurora/gin-vue-admin/server/middleware"
	"github.com/gin-gonic/gin"
)

type MomentsRouter struct{}

// InitMomentsRouter 初始化 moments表 路由信息
func (s *MomentsRouter) InitMomentsRouter(Router *gin.RouterGroup, PublicRouter *gin.RouterGroup) {
	momentsRouter := Router.Group("moments").Use(middleware.OperationRecord())
	momentsRouterWithoutRecord := Router.Group("moments")
	momentsRouterWithoutAuth := PublicRouter.Group("moments")
	{
		momentsRouter.POST("createMoments", momentsApi.CreateMoments)             // 新建moments表
		momentsRouter.DELETE("deleteMoments", momentsApi.DeleteMoments)           // 删除moments表
		momentsRouter.DELETE("deleteMomentsByIds", momentsApi.DeleteMomentsByIds) // 批量删除moments表
		momentsRouter.PUT("updateMoments", momentsApi.UpdateMoments)              // 更新moments表
	}
	{
		momentsRouterWithoutRecord.GET("findMoments", momentsApi.FindMoments)       // 根据ID获取moments表
		momentsRouterWithoutRecord.GET("getMomentsList", momentsApi.GetMomentsList) // 获取moments表列表
	}
	{
		momentsRouterWithoutAuth.GET("getMomentsPublic", momentsApi.GetMomentsPublic) // moments表开放接口
	}
}
