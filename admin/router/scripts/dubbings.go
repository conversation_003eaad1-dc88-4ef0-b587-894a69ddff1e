package scripts

import (
	"github.com/flipped-aurora/gin-vue-admin/server/middleware"
	"github.com/gin-gonic/gin"
)

type DubbingRouter struct {}

// InitDubbingRouter 初始化 dubbings表 路由信息
func (s *DubbingRouter) InitDubbingRouter(Router *gin.RouterGroup,PublicRouter *gin.RouterGroup) {
	dubbingRouter := Router.Group("dubbing").Use(middleware.OperationRecord())
	dubbingRouterWithoutRecord := Router.Group("dubbing")
	dubbingRouterWithoutAuth := PublicRouter.Group("dubbing")
	{
		dubbingRouter.POST("createDubbing", dubbingApi.CreateDubbing)   // 新建dubbings表
		dubbingRouter.DELETE("deleteDubbing", dubbingApi.DeleteDubbing) // 删除dubbings表
		dubbingRouter.DELETE("deleteDubbingByIds", dubbingApi.DeleteDubbingByIds) // 批量删除dubbings表
		dubbingRouter.PUT("updateDubbing", dubbingApi.UpdateDubbing)    // 更新dubbings表
		dubbingRouter.PUT("auditDubbing", dubbingApi.AuditDubbing)      // 审核dubbings表
		dubbingRouter.PUT("setDubbingTop", dubbingApi.SetDubbingTop)    // 设置配音置顶状态
		dubbingRouter.POST("manualUploadDubbing", dubbingApi.ManualUploadDubbing) // 手动上传配音
	}
	{
		dubbingRouterWithoutRecord.GET("findDubbing", dubbingApi.FindDubbing)        // 根据ID获取dubbings表
		dubbingRouterWithoutRecord.GET("getDubbingList", dubbingApi.GetDubbingList)  // 获取dubbings表列表
	}
	{
	    dubbingRouterWithoutAuth.GET("getDubbingPublic", dubbingApi.GetDubbingPublic)  // dubbings表开放接口
	}
}
