package scripts

import (
	"github.com/flipped-aurora/gin-vue-admin/server/middleware"
	"github.com/gin-gonic/gin"
)

type ReportRouter struct {}

// InitReportRouter 初始化 reports表 路由信息
func (s *ReportRouter) InitReportRouter(Router *gin.RouterGroup,PublicRouter *gin.RouterGroup) {
	reportRouter := Router.Group("report").Use(middleware.OperationRecord())
	reportRouterWithoutRecord := Router.Group("report")
	reportRouterWithoutAuth := PublicRouter.Group("report")
	{
		reportRouter.POST("createReport", reportApi.CreateReport)   // 新建reports表
		reportRouter.DELETE("deleteReport", reportApi.DeleteReport) // 删除reports表
		reportRouter.DELETE("deleteReportByIds", reportApi.DeleteReportByIds) // 批量删除reports表
		reportRouter.PUT("updateReport", reportApi.UpdateReport)    // 更新reports表
		reportRouter.PUT("auditReport", reportApi.AuditReport)      // 审核举报
	}
	{
		reportRouterWithoutRecord.GET("findReport", reportApi.FindReport)        // 根据ID获取reports表
		reportRouterWithoutRecord.GET("getReportList", reportApi.GetReportList)  // 获取reports表列表
		reportRouterWithoutRecord.GET("getReportContentDetail", reportApi.GetReportContentDetail) // 获取举报内容详情
	}
	{
	    reportRouterWithoutAuth.GET("getReportPublic", reportApi.GetReportPublic)  // reports表开放接口
	}
}
