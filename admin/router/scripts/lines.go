package scripts

import (
	"github.com/flipped-aurora/gin-vue-admin/server/middleware"
	"github.com/gin-gonic/gin"
)

type LineRouter struct{}

// InitLineRouter 初始化 lines表 路由信息
func (s *LineRouter) InitLineRouter(Router *gin.RouterGroup, PublicRouter *gin.RouterGroup) {
	lineRouter := Router.Group("line").Use(middleware.OperationRecord())
	lineRouterWithoutRecord := Router.Group("line")
	lineRouterWithoutAuth := PublicRouter.Group("line")
	{
		lineRouter.POST("createLine", lineApi.CreateLine)             // 新建lines表
		lineRouter.DELETE("deleteLine", lineApi.DeleteLine)           // 删除lines表
		lineRouter.DELETE("deleteLineByIds", lineApi.DeleteLineByIds) // 批量删除lines表
		lineRouter.PUT("updateLine", lineApi.UpdateLine)              // 更新lines表
		lineRouter.POST("reDubbing", lineApi.ReDubbing)               // 台词重新配音
	}
	{
		lineRouterWithoutRecord.GET("findLine", lineApi.FindLine)       // 根据ID获取lines表
		lineRouterWithoutRecord.GET("getLineList", lineApi.GetLineList) // 获取lines表列表
	}
	{
		lineRouterWithoutAuth.GET("getLinePublic", lineApi.GetLinePublic) // lines表开放接口
	}
}
