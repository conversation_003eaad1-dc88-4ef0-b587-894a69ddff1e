package scripts

import (
	"github.com/flipped-aurora/gin-vue-admin/server/middleware"
	"github.com/gin-gonic/gin"
)

type ScriptRouter struct{}

// InitScriptRouter 初始化 scripts表 路由信息
func (s *ScriptRouter) InitScriptRouter(Router *gin.RouterGroup, PublicRouter *gin.RouterGroup) {
	scriptRouter := Router.Group("script").Use(middleware.OperationRecord())
	scriptRouterWithoutRecord := Router.Group("script")
	scriptRouterWithoutAuth := PublicRouter.Group("script")
	{
		scriptRouter.POST("createScript", scriptApi.CreateScript)             // 新建scripts表
		scriptRouter.POST("batchCreateScript", scriptApi.CreateBatchScript)   // 批量创建scripts表
		scriptRouter.DELETE("deleteScript", scriptApi.DeleteScript)           // 删除scripts表
		scriptRouter.DELETE("deleteScriptByIds", scriptApi.DeleteScriptByIds) // 批量删除scripts表
		scriptRouter.PUT("updateScript", scriptApi.UpdateScript)              // 更新scripts表
		scriptRouter.PUT("auditScript", scriptApi.AuditScript)                // 审核scripts表
	}
	{
		scriptRouterWithoutRecord.GET("findScript", scriptApi.FindScript)       // 根据ID获取scripts表
		scriptRouterWithoutRecord.GET("getScriptList", scriptApi.GetScriptList) // 获取scripts表列表
	}
	{
		scriptRouterWithoutAuth.GET("getScriptPublic", scriptApi.GetScriptPublic) // scripts表开放接口
	}
}
