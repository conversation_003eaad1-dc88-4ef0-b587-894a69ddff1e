package scripts

import (
	"github.com/flipped-aurora/gin-vue-admin/server/middleware"
	"github.com/gin-gonic/gin"
)

type CoverRouter struct {}

// InitCoverRouter 初始化 covers表 路由信息
func (s *CoverRouter) InitCoverRouter(Router *gin.RouterGroup,PublicRouter *gin.RouterGroup) {
	coverRouter := Router.Group("cover").Use(middleware.OperationRecord())
	coverRouterWithoutRecord := Router.Group("cover")
	coverRouterWithoutAuth := PublicRouter.Group("cover")
	{
		coverRouter.POST("createCover", coverApi.CreateCover)   // 新建covers表
		coverRouter.DELETE("deleteCover", coverApi.DeleteCover) // 删除covers表
		coverRouter.DELETE("deleteCoverByIds", coverApi.DeleteCoverByIds) // 批量删除covers表
		coverRouter.PUT("updateCover", coverApi.UpdateCover)    // 更新covers表
	}
	{
		coverRouterWithoutRecord.GET("findCover", coverApi.FindCover)        // 根据ID获取covers表
		coverRouterWithoutRecord.GET("getCoverList", coverApi.GetCoverList)  // 获取covers表列表
	}
	{
	    coverRouterWithoutAuth.GET("getCoverPublic", coverApi.GetCoverPublic)  // covers表开放接口
	}
}
