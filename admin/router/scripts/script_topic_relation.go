package scripts

import (
	"github.com/flipped-aurora/gin-vue-admin/server/middleware"
	"github.com/gin-gonic/gin"
)

type ScriptTopicRelationRouter struct {}

// InitScriptTopicRelationRouter 初始化 scriptTopicRelation表 路由信息
func (s *ScriptTopicRelationRouter) InitScriptTopicRelationRouter(Router *gin.RouterGroup,PublicRouter *gin.RouterGroup) {
	scriptTopicRelationRouter := Router.Group("scriptTopicRelation").Use(middleware.OperationRecord())
	scriptTopicRelationRouterWithoutRecord := Router.Group("scriptTopicRelation")
	scriptTopicRelationRouterWithoutAuth := PublicRouter.Group("scriptTopicRelation")
	{
		scriptTopicRelationRouter.POST("createScriptTopicRelation", scriptTopicRelationApi.CreateScriptTopicRelation)   // 新建scriptTopicRelation表
		scriptTopicRelationRouter.DELETE("deleteScriptTopicRelation", scriptTopicRelationApi.DeleteScriptTopicRelation) // 删除scriptTopicRelation表
		scriptTopicRelationRouter.DELETE("deleteScriptTopicRelationByIds", scriptTopicRelationApi.DeleteScriptTopicRelationByIds) // 批量删除scriptTopicRelation表
		scriptTopicRelationRouter.PUT("updateScriptTopicRelation", scriptTopicRelationApi.UpdateScriptTopicRelation)    // 更新scriptTopicRelation表
	}
	{
		scriptTopicRelationRouterWithoutRecord.GET("findScriptTopicRelation", scriptTopicRelationApi.FindScriptTopicRelation)        // 根据ID获取scriptTopicRelation表
		scriptTopicRelationRouterWithoutRecord.GET("getScriptTopicRelationList", scriptTopicRelationApi.GetScriptTopicRelationList)  // 获取scriptTopicRelation表列表
	}
	{
	    scriptTopicRelationRouterWithoutAuth.GET("getScriptTopicRelationPublic", scriptTopicRelationApi.GetScriptTopicRelationPublic)  // scriptTopicRelation表开放接口
	}
}
