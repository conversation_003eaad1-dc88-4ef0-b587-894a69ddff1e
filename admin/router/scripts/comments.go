package scripts

import (
	"github.com/flipped-aurora/gin-vue-admin/server/middleware"
	"github.com/gin-gonic/gin"
)

type CommentRouter struct{}

// InitCommentRouter 初始化 comments表 路由信息
func (s *CommentRouter) InitCommentRouter(Router *gin.RouterGroup, PublicRouter *gin.RouterGroup) {
	commentRouter := Router.Group("comment").Use(middleware.OperationRecord())
	commentRouterWithoutRecord := Router.Group("comment")
	commentRouterWithoutAuth := PublicRouter.Group("comment")
	{
		commentRouter.POST("createComment", commentApi.CreateComment)             // 新建comments表
		commentRouter.DELETE("deleteComment", commentApi.DeleteComment)           // 删除comments表
		commentRouter.DELETE("deleteCommentByIds", commentApi.DeleteCommentByIds) // 批量删除comments表
		commentRouter.PUT("updateComment", commentApi.UpdateComment)              // 更新comments表
		commentRouter.PUT("auditComment", commentApi.AuditComment)                // 审核comments表
		commentRouter.PUT("setCommentTop", commentApi.SetCommentTop)              // 设置评论置顶状态
		commentRouter.POST("speechToText", commentApi.SpeechToText)               // 语音转文本接口
	}
	{
		commentRouterWithoutRecord.GET("findComment", commentApi.FindComment)       // 根据ID获取comments表
		commentRouterWithoutRecord.GET("getCommentList", commentApi.GetCommentList) // 获取comments表列表
	}
	{
		commentRouterWithoutAuth.GET("getCommentPublic", commentApi.GetCommentPublic) // comments表开放接口
	}
}
