package scripts

import api "github.com/flipped-aurora/gin-vue-admin/server/api/v1"

type RouterGroup struct {
	ScriptRouter
	TopicRouter
	CoverRouter
	LineRouter
	DubbingRouter
	CommentRouter
	ScriptTopicRelationRouter
	ScriptCharacterRelationRouter
	ReportRouter
	RecordingConfigRouter
}

var (
	scriptApi                  = api.ApiGroupApp.ScriptsApiGroup.ScriptApi
	topicApi                   = api.ApiGroupApp.ScriptsApiGroup.TopicApi
	coverApi                   = api.ApiGroupApp.ScriptsApiGroup.CoverApi
	lineApi                    = api.ApiGroupApp.ScriptsApiGroup.LineApi
	dubbingApi                 = api.ApiGroupApp.ScriptsApiGroup.DubbingApi
	commentApi                 = api.ApiGroupApp.ScriptsApiGroup.CommentApi
	scriptTopicRelationApi     = api.ApiGroupApp.ScriptsApiGroup.ScriptTopicRelationApi
	scriptCharacterRelationApi = api.ApiGroupApp.ScriptsApiGroup.ScriptCharacterRelationApi
	reportApi                  = api.ApiGroupApp.ScriptsApiGroup.ReportApi
	recordingConfigApi         = api.ApiGroupApp.ScriptsApiGroup.RecordingConfigApi
)
