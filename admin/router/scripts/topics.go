package scripts

import (
	"github.com/flipped-aurora/gin-vue-admin/server/middleware"
	"github.com/gin-gonic/gin"
)

type TopicRouter struct{}

// InitTopicRouter 初始化 topics表 路由信息
func (s *TopicRouter) InitTopicRouter(Router *gin.RouterGroup, PublicRouter *gin.RouterGroup) {
	topicRouter := Router.Group("topic").Use(middleware.OperationRecord())
	topicRouterWithoutRecord := Router.Group("topic")
	topicRouterWithoutAuth := PublicRouter.Group("topic")
	{
		topicRouter.POST("createTopic", topicApi.CreateTopic)             // 新建topics表
		topicRouter.DELETE("deleteTopic", topicApi.DeleteTopic)           // 删除topics表
		topicRouter.DELETE("deleteTopicByIds", topicApi.DeleteTopicByIds) // 批量删除topics表
		topicRouter.PUT("updateTopic", topicApi.UpdateTopic)              // 更新topics表
	}
	{
		topicRouterWithoutRecord.GET("findTopic", topicApi.FindTopic)                 // 根据ID获取topics表
		topicRouterWithoutRecord.GET("getTopicList", topicApi.GetTopicList)           // 获取topics表列表
		topicRouterWithoutRecord.GET("getTopicLabelList", topicApi.GetTopicLabelList) // 获取topics label表列表
	}
	{
		topicRouterWithoutAuth.GET("getTopicPublic", topicApi.GetTopicPublic) // topics表开放接口
	}
}
