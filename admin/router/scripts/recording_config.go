package scripts

import (
	"github.com/flipped-aurora/gin-vue-admin/server/middleware"
	"github.com/gin-gonic/gin"
)

type RecordingConfigRouter struct{}

// InitRecordingConfigRouter 初始化录音配置路由
func (r *RecordingConfigRouter) InitRecordingConfigRouter(Router *gin.RouterGroup, PublicRouter *gin.RouterGroup) {
	recordingConfigRouter := Router.Group("recordingConfig").Use(middleware.OperationRecord())
	recordingConfigRouterWithoutRecord := Router.Group("recordingConfig")
	{
		recordingConfigRouter.POST("getRecordingConfig", recordingConfigApi.GetRecordingConfig) // 获取录音配置
	}
	{
		recordingConfigRouterWithoutRecord.GET("getRecordingConfigByLineId", recordingConfigApi.GetRecordingConfigByLineId) // 根据台词ID获取录音配置
	}
}
