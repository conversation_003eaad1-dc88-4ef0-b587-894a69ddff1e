package scripts

import (
	"github.com/flipped-aurora/gin-vue-admin/server/middleware"
	"github.com/gin-gonic/gin"
)

type ScriptCharacterRelationRouter struct {}

// InitScriptCharacterRelationRouter 初始化 scriptCharacterRelation表 路由信息
func (s *ScriptCharacterRelationRouter) InitScriptCharacterRelationRouter(Router *gin.RouterGroup,PublicRouter *gin.RouterGroup) {
	scriptCharacterRelationRouter := Router.Group("scriptCharacterRelation").Use(middleware.OperationRecord())
	scriptCharacterRelationRouterWithoutRecord := Router.Group("scriptCharacterRelation")
	scriptCharacterRelationRouterWithoutAuth := PublicRouter.Group("scriptCharacterRelation")
	{
		scriptCharacterRelationRouter.POST("createScriptCharacterRelation", scriptCharacterRelationApi.CreateScriptCharacterRelation)   // 新建scriptCharacterRelation表
		scriptCharacterRelationRouter.DELETE("deleteScriptCharacterRelation", scriptCharacterRelationApi.DeleteScriptCharacterRelation) // 删除scriptCharacterRelation表
		scriptCharacterRelationRouter.DELETE("deleteScriptCharacterRelationByIds", scriptCharacterRelationApi.DeleteScriptCharacterRelationByIds) // 批量删除scriptCharacterRelation表
		scriptCharacterRelationRouter.PUT("updateScriptCharacterRelation", scriptCharacterRelationApi.UpdateScriptCharacterRelation)    // 更新scriptCharacterRelation表
	}
	{
		scriptCharacterRelationRouterWithoutRecord.GET("findScriptCharacterRelation", scriptCharacterRelationApi.FindScriptCharacterRelation)        // 根据ID获取scriptCharacterRelation表
		scriptCharacterRelationRouterWithoutRecord.GET("getScriptCharacterRelationList", scriptCharacterRelationApi.GetScriptCharacterRelationList)  // 获取scriptCharacterRelation表列表
	}
	{
	    scriptCharacterRelationRouterWithoutAuth.GET("getScriptCharacterRelationPublic", scriptCharacterRelationApi.GetScriptCharacterRelationPublic)  // scriptCharacterRelation表开放接口
	}
}
