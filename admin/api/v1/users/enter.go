package users

import "github.com/flipped-aurora/gin-vue-admin/server/service"

type ApiGroup struct {
	UserApi
	UserScoreApi
	UserScoreLogApi
	UserAuditRecordApi
}

var (
	userService         = service.ServiceGroupApp.UsersServiceGroup.UserService
	userScoreService    = service.ServiceGroupApp.UsersServiceGroup.UserScoreService
	userScoreLogService = service.ServiceGroupApp.UsersServiceGroup.UserScoreLogService
)
