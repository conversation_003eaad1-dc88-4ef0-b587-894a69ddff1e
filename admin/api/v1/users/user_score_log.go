package users

import (
	"github.com/flipped-aurora/gin-vue-admin/server/global"
	"github.com/flipped-aurora/gin-vue-admin/server/model/common/response"
	usersReq "github.com/flipped-aurora/gin-vue-admin/server/model/users/request"
	"github.com/gin-gonic/gin"
	"go.uber.org/zap"
)

type UserScoreLogApi struct{}

// FindUserScoreLog 用id查询userScoreLog表
// @Tags UserScoreLog
// @Summary 用id查询userScoreLog表
// @Security ApiKeyAuth
// @Accept application/json
// @Produce application/json
// @Param id query int true "用id查询userScoreLog表"
// @Success 200 {object} response.Response{data=users.UserScoreLog,msg=string} "查询成功"
// @Router /userScoreLog/findUserScoreLog [get]
func (userScoreLogApi *UserScoreLogApi) FindUserScoreLog(c *gin.Context) {
	// 创建业务用Context
	ctx := c.Request.Context()

	id := c.Query("id")
	reuserScoreLog, err := userScoreLogService.GetUserScoreLog(ctx, id)
	if err != nil {
		global.GVA_LOG.Error("查询失败!", zap.Error(err))
		response.FailWithMessage("查询失败:"+err.Error(), c)
		return
	}
	response.OkWithData(reuserScoreLog, c)
}

// GetUserScoreLogList 分页获取userScoreLog表列表
// @Tags UserScoreLog
// @Summary 分页获取userScoreLog表列表
// @Security ApiKeyAuth
// @Accept application/json
// @Produce application/json
// @Param data query usersReq.UserScoreLogSearch true "分页获取userScoreLog表列表"
// @Success 200 {object} response.Response{data=response.PageResult,msg=string} "获取成功"
// @Router /userScoreLog/getUserScoreLogList [post]
func (userScoreLogApi *UserScoreLogApi) GetUserScoreLogList(c *gin.Context) {
	// 创建业务用Context
	ctx := c.Request.Context()

	var pageInfo usersReq.UserScoreLogSearch
	err := c.ShouldBindJSON(&pageInfo)
	if err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}
	list, total, err := userScoreLogService.GetUserScoreLogInfoList(ctx, pageInfo)
	if err != nil {
		global.GVA_LOG.Error("获取失败!", zap.Error(err))
		response.FailWithMessage("获取失败:"+err.Error(), c)
		return
	}
	response.OkWithDetailed(response.PageResult{
		List:     list,
		Total:    total,
		Page:     pageInfo.Page,
		PageSize: pageInfo.PageSize,
	}, "获取成功", c)
}
