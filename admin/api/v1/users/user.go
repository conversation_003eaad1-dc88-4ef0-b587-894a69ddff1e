package users

import (
	"fmt"
	"github.com/flipped-aurora/gin-vue-admin/server/global"
	"github.com/flipped-aurora/gin-vue-admin/server/model/common/response"
	"github.com/flipped-aurora/gin-vue-admin/server/model/users"
	usersReq "github.com/flipped-aurora/gin-vue-admin/server/model/users/request"
	"github.com/flipped-aurora/gin-vue-admin/server/utils"
	"github.com/gin-gonic/gin"
	"go.uber.org/zap"
	"new-gitlab.xunlei.cn/vcproject/backends/baselib/errcode"
	"new-gitlab.xunlei.cn/vcproject/backends/baselib/server/env"
	"new-gitlab.xunlei.cn/vcproject/backends/baselib/util"
	"new-gitlab.xunlei.cn/vcproject/backends/proto/api/svcaccount"
	"new-gitlab.xunlei.cn/vcproject/backends/proto/consts"
	"new-gitlab.xunlei.cn/vcproject/backends/proto/svcmgr"
	"regexp"
	"strings"
)

type UserApi struct{}

// UpdateUser 更新users表
// @Tags User
// @Summary 更新users表
// @Security ApiKeyAuth
// @Accept application/json
// @Produce application/json
// @Param data body users.User true "更新users表"
// @Success 200 {object} response.Response{msg=string} "更新成功"
// @Router /user/updateUser [put]
func (userApi *UserApi) UpdateUser(c *gin.Context) {
	// 从ctx获取标准context进行业务行为
	ctx := c.Request.Context()

	var user users.User
	err := c.ShouldBindJSON(&user)
	if err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}
	nowTicks := util.NowTimeMillis()
	user.UpdatedAt = &nowTicks
	err = userService.UpdateUser(ctx, user)
	if err != nil {
		global.GVA_LOG.Error("更新失败!", zap.Error(err))
		response.FailWithMessage("更新失败:"+err.Error(), c)
		return
	}
	response.OkWithMessage("更新成功", c)
}

// CreateUser 创建用户
// @Tags User
// @Summary 创建用户
// @Security ApiKeyAuth
// @Accept application/json
// @Produce application/json
// @Param data body usersReq.CreateUserReq true "创建用户"
// @Success 200 {object} response.Response{data=map[string]interface{},msg=string} "创建成功"
// @Router /user/createUser [post]
func (userApi *UserApi) CreateUser(c *gin.Context) {
	// 从ctx获取标准context进行业务行为
	ctx := c.Request.Context()

	var req usersReq.CreateUserReq
	err := c.ShouldBindJSON(&req)
	if err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}

	// 验证手机号格式
	phoneRegex := regexp.MustCompile(`^1[3-9]\d{9}$`)
	if !phoneRegex.MatchString(req.Phone) {
		response.FailWithMessage("手机号格式不正确", c)
		return
	}

	// 处理creation_user_id参数
	var creationUserId uint32
	if req.CreationUserId != nil {
		creationUserId = uint32(*req.CreationUserId)
		global.GVA_LOG.Info(fmt.Sprintf("创建用户时绑定创作者: 手机号=%s, 创作者admin用户ID=%d", req.Phone, creationUserId))
	}

	// 调用svcaccount服务创建用户
	vResp, err := svcmgr.AccountClient().AdminCreateUser(ctx, &svcaccount.AdminCreateUserReq{
		Phone:             req.Phone,
		Nickname:          req.Nickname,
		Avatar:            req.Avatar,
		Gender:            req.Gender,
		Status:            req.Status,
		BackgroundUrl:     req.BackgroundUrl,
		VoiceSignatureUrl: req.VoiceSignatureUrl,
		VoiceDuration:     req.VoiceDuration,
		IsPremiumCreator:  req.IsPremiumCreator,
		CreationUserId:    creationUserId,
	})
	if err != nil || errcode.NotOk(vResp) {
		global.GVA_LOG.Error("创建用户失败!", zap.Error(err))
		response.FailWithMessage("创建失败:"+err.Error(), c)
		return
	}

	response.OkWithData(map[string]interface{}{
		"user_id": vResp.UserId,
	}, c)
}

// FindUser 用id查询users表
// @Tags User
// @Summary 用id查询users表
// @Security ApiKeyAuth
// @Accept application/json
// @Produce application/json
// @Param user_id query int true "用id查询users表"
// @Success 200 {object} response.Response{data=users.User,msg=string} "查询成功"
// @Router /user/findUser [get]
func (userApi *UserApi) FindUser(c *gin.Context) {
	// 创建业务用Context
	ctx := c.Request.Context()

	user_id := c.Query("user_id")
	reuser, err := userService.GetUser(ctx, user_id)
	if err != nil {
		global.GVA_LOG.Error("查询失败!", zap.Error(err))
		response.FailWithMessage("查询失败:"+err.Error(), c)
		return
	}
	avatar := *reuser.Avatar
	avatar = fillImageUrl(avatar)
	reuser.Avatar = &avatar

	backgroundUrl := *reuser.BackgroundUrl
	backgroundUrl = fillImageUrl(backgroundUrl)
	reuser.BackgroundUrl = &backgroundUrl

	voiceSignUrl := *reuser.VoiceSignatureUrl
	voiceSignUrl = fillAudioUrl(voiceSignUrl)
	reuser.VoiceSignatureUrl = &voiceSignUrl

	// 对手机号进行脱敏处理
	if reuser.Phone != nil && len(*reuser.Phone) > 0 {
		maskedPhone := util.MaskPhoneNumber(*reuser.Phone)
		reuser.Phone = &maskedPhone
	}

	response.OkWithData(reuser, c)
}

// GetUserList 分页获取users表列表
// @Tags User
// @Summary 分页获取users表列表
// @Security ApiKeyAuth
// @Accept application/json
// @Produce application/json
// @Param data query usersReq.UserSearch true "分页获取users表列表"
// @Success 200 {object} response.Response{data=response.PageResult,msg=string} "获取成功"
// @Router /user/getUserList [get]
func (userApi *UserApi) GetUserList(c *gin.Context) {
	// 创建业务用Context
	ctx := c.Request.Context()

	var pageInfo usersReq.UserSearch
	err := c.ShouldBindQuery(&pageInfo)
	if err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}
	list, total, err := userService.GetUserInfoList(ctx, pageInfo)
	if err != nil {
		global.GVA_LOG.Error("获取失败!", zap.Error(err))
		response.FailWithMessage("获取失败:"+err.Error(), c)
		return
	}
	for _, userItem := range list {
		avatar := *userItem.Avatar
		avatar = fillImageUrl(avatar)
		userItem.Avatar = &avatar

		backgroundUrl := *userItem.BackgroundUrl
		backgroundUrl = fillImageUrl(backgroundUrl)
		userItem.BackgroundUrl = &backgroundUrl

		voiceSignUrl := *userItem.VoiceSignatureUrl
		voiceSignUrl = fillAudioUrl(voiceSignUrl)
		userItem.VoiceSignatureUrl = &voiceSignUrl

		// 对手机号进行脱敏处理
		if userItem.Phone != nil && len(*userItem.Phone) > 0 {
			maskedPhone := util.MaskPhoneNumber(*userItem.Phone)
			userItem.Phone = &maskedPhone
		}
	}
	response.OkWithDetailed(response.PageResult{
		List:     list,
		Total:    total,
		Page:     pageInfo.Page,
		PageSize: pageInfo.PageSize,
	}, "获取成功", c)
}

func fillAudioUrl(audioUrl string) string {
	if len(strings.TrimSpace(audioUrl)) == 0 {
		return ""
	}
	if !strings.HasPrefix(audioUrl, "http") {
		if env.IsProd() {
			if strings.HasPrefix(audioUrl, "/") {
				audioUrl = "https://audio.voicelives.com" + audioUrl
			} else {
				audioUrl = "https://audio.voicelives.com/" + audioUrl
			}
		} else {
			if strings.HasPrefix(audioUrl, "/") {
				audioUrl = "https://data-test.voicelives.com" + audioUrl
			} else {
				audioUrl = "https://data-test.voicelives.com/" + audioUrl
			}
		}
	}
	return audioUrl
}

func fillImageUrl(imageUrl string) string {
	if len(strings.TrimSpace(imageUrl)) == 0 {
		return ""
	}
	if !strings.HasPrefix(imageUrl, "http") {
		if env.IsProd() {
			if strings.HasPrefix(imageUrl, "/") {
				imageUrl = "https://image.voicelives.com" + imageUrl
			} else {
				imageUrl = "https://image.voicelives.com/" + imageUrl
			}
		} else {
			if strings.HasPrefix(imageUrl, "/") {
				imageUrl = "https://data-test.voicelives.com" + imageUrl
			} else {
				imageUrl = "https://data-test.voicelives.com/" + imageUrl
			}
		}
	}
	return imageUrl
}

// RewardScore
// @Tags User
// @Summary 发放用户奖励
// @Security ApiKeyAuth
// @Accept application/json
// @Produce application/json
// @Param data body users.User true "更新users表"
// @Success 200 {object} response.Response{msg=string} "更新成功"
// @Router /user/updateUser [put]
func (userApi *UserApi) RewardScore(c *gin.Context) {
	// 从ctx获取标准context进行业务行为
	ctx := c.Request.Context()
	var req usersReq.RewardUserScoreReq
	err := c.ShouldBindJSON(&req)
	if err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}
	if req.UserId <= 0 || req.Coins <= 0 || req.RewardType <= 0 {
		global.GVA_LOG.Error("请求参数", zap.Any("req", req))
		response.FailWithMessage("参数非法", c)
		return
	}
	claims, _ := utils.GetClaims(c) // 从 JWT token 获取 claims
	username := claims.Username     // 获取用户名
	userId := claims.BaseClaims.ID  // 获取用户 ID
	orderId := util.NewOrderID(util.TOrderType(consts.BizTypeDailyCheckIn)).String()
	vResp, err := svcmgr.AccountClient().AddUserScore(ctx, &svcaccount.AddUserScoreReq{
		UserId:        req.UserId,
		ValuableScore: 0,
		FeeScore:      req.Coins * consts.ScaleScore,
		BizType:       req.RewardType,
		BizName:       req.RewardName,
		OrderId:       orderId,
		Remark:        util.JsonStr(map[string]interface{}{"uid": userId, "nick": username}),
	})
	global.GVA_LOG.Info("AddUserScore", zap.String("err", util.JsonStr(err)), zap.String("vResp", util.JsonStr(vResp)))
	if err != nil || errcode.NotOk(vResp) {
		global.GVA_LOG.Error("发放失败!", zap.Error(err))
		response.FailWithMessage(err.Error(), c)
		return
	}
	response.OkWithMessage("发放成功", c)
}

// ResetUserInfo 重置用户信息
// @Tags User
// @Summary 重置用户信息
// @Security ApiKeyAuth
// @Accept application/json
// @Produce application/json
// @Param data body map[string]interface{} true "用户ID"
// @Success 200 {object} response.Response{msg=string} "重置成功"
// @Router /user/resetUserInfo [post]
func (userApi *UserApi) ResetUserInfo(c *gin.Context) {
	ctx := c.Request.Context()

	var req map[string]interface{}
	err := c.ShouldBindJSON(&req)
	if err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}

	userIdFloat, ok := req["user_id"].(float64)
	if !ok {
		response.FailWithMessage("用户ID参数错误", c)
		return
	}
	userId := int64(userIdFloat)

	if userId <= 0 {
		response.FailWithMessage("用户ID不能为空", c)
		return
	}

	vResp, err := svcmgr.AccountClient().ResetUserInfo(ctx, &svcaccount.ResetUserInfoReq{
		UserId: userId,
	})
	if err != nil || errcode.NotOk(vResp) {
		global.GVA_LOG.Error("重置用户信息失败!", zap.Error(err))
		response.FailWithMessage("重置失败:"+err.Error(), c)
		return
	}
	response.OkWithMessage("重置成功", c)
}

// BanUser 封禁用户
// @Tags User
// @Summary 封禁用户
// @Security ApiKeyAuth
// @Accept application/json
// @Produce application/json
// @Param data body map[string]interface{} true "用户ID"
// @Success 200 {object} response.Response{msg=string} "封禁成功"
// @Router /user/banUser [post]
func (userApi *UserApi) BanUser(c *gin.Context) {
	ctx := c.Request.Context()

	var req map[string]interface{}
	err := c.ShouldBindJSON(&req)
	if err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}

	userIdFloat, ok := req["user_id"].(float64)
	if !ok {
		response.FailWithMessage("用户ID参数错误", c)
		return
	}
	userId := int64(userIdFloat)

	if userId <= 0 {
		response.FailWithMessage("用户ID不能为空", c)
		return
	}

	vResp, err := svcmgr.AccountClient().BanUser(ctx, &svcaccount.BanUserReq{
		UserId: userId,
	})
	if err != nil || errcode.NotOk(vResp) {
		global.GVA_LOG.Error("封禁用户失败!", zap.Error(err))
		response.FailWithMessage("封禁失败:"+err.Error(), c)
		return
	}
	response.OkWithMessage("封禁成功", c)
}

// UnbanUser 解封用户
// @Tags User
// @Summary 解封用户
// @Security ApiKeyAuth
// @Accept application/json
// @Produce application/json
// @Param data body map[string]interface{} true "用户ID"
// @Success 200 {object} response.Response{msg=string} "解封成功"
// @Router /user/unbanUser [post]
func (userApi *UserApi) UnbanUser(c *gin.Context) {
	ctx := c.Request.Context()

	var req map[string]interface{}
	err := c.ShouldBindJSON(&req)
	if err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}

	userIdFloat, ok := req["user_id"].(float64)
	if !ok {
		response.FailWithMessage("用户ID参数错误", c)
		return
	}
	userId := int64(userIdFloat)

	if userId <= 0 {
		response.FailWithMessage("用户ID不能为空", c)
		return
	}

	vResp, err := svcmgr.AccountClient().UnbanUser(ctx, &svcaccount.UnbanUserReq{
		UserId: userId,
	})
	if err != nil || errcode.NotOk(vResp) {
		global.GVA_LOG.Error("解封用户失败!", zap.Error(err))
		response.FailWithMessage("解封失败:"+err.Error(), c)
		return
	}
	response.OkWithMessage("解封成功", c)
}

// SetPremiumCreator 设置优质创作者
// @Tags User
// @Summary 设置优质创作者
// @Security ApiKeyAuth
// @Accept application/json
// @Produce application/json
// @Param data body map[string]interface{} true "用户ID和优质创作者状态"
// @Success 200 {object} response.Response{msg=string} "设置成功"
// @Router /user/setPremiumCreator [post]
func (userApi *UserApi) SetPremiumCreator(c *gin.Context) {
	ctx := c.Request.Context()

	var req map[string]interface{}
	err := c.ShouldBindJSON(&req)
	if err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}

	userIdFloat, ok := req["user_id"].(float64)
	if !ok {
		response.FailWithMessage("用户ID参数错误", c)
		return
	}
	userId := int64(userIdFloat)

	if userId <= 0 {
		response.FailWithMessage("用户ID不能为空", c)
		return
	}

	isPremiumCreator, ok := req["is_premium_creator"].(bool)
	if !ok {
		response.FailWithMessage("优质创作者状态参数错误", c)
		return
	}

	// 调用svcaccount服务设置优质创作者
	vResp, err := svcmgr.AccountClient().SetPremiumCreator(ctx, &svcaccount.SetPremiumCreatorReq{
		UserId:           userId,
		IsPremiumCreator: isPremiumCreator,
	})
	if err != nil || errcode.NotOk(vResp) {
		global.GVA_LOG.Error("设置优质创作者失败!", zap.Error(err))
		response.FailWithMessage("设置失败:"+err.Error(), c)
		return
	}

	action := "取消"
	if isPremiumCreator {
		action = "设置为"
	}
	response.OkWithMessage(fmt.Sprintf("%s优质创作者成功", action), c)
}

// GetUserInfoByCreationUserId 根据admin用户ID获取关联的业务用户信息
// @Tags User
// @Summary 根据admin用户ID获取关联的业务用户信息
// @Security ApiKeyAuth
// @Accept application/json
// @Produce application/json
// @Success 200 {object} response.Response{data=map[string]interface{},msg=string} "获取成功"
// @Router /user/getUserInfoByCreationUserId [get]
func (userApi *UserApi) GetUserInfoByCreationUserId(c *gin.Context) {
	// 从ctx获取标准context进行业务行为
	ctx := c.Request.Context()

	// 获取当前admin用户ID
	claims, _ := utils.GetClaims(c)
	adminUserId := claims.BaseClaims.ID

	global.GVA_LOG.Info(fmt.Sprintf("根据admin用户ID获取关联的业务用户信息: adminUserId=%d", adminUserId))

	// 调用svcaccount服务获取关联的业务用户信息
	vResp, err := svcmgr.AccountClient().GetUserInfoByCreationUserId(ctx, &svcaccount.GetUserInfoByCreationUserIdReq{
		CreationUserId: uint32(adminUserId),
		WithFollow:     false,
		WithScore:      false,
	})

	if err != nil {
		global.GVA_LOG.Error("获取用户信息失败!", zap.Error(err))
		response.FailWithMessage("获取用户信息失败:"+err.Error(), c)
		return
	}

	if errcode.NotOk(vResp) {
		global.GVA_LOG.Error("获取用户信息失败!", zap.String("msg", vResp.Base.Msg))
		response.FailWithMessage("获取用户信息失败:"+vResp.Base.Msg, c)
		return
	}

	if vResp.Data == nil {
		response.FailWithMessage("未找到关联的用户信息", c)
		return
	}

	// 对手机号进行脱敏处理
	maskedPhone := vResp.Data.Phone
	if len(vResp.Data.Phone) > 0 {
		maskedPhone = util.MaskPhoneNumber(vResp.Data.Phone)
	}

	// 构建返回数据
	result := map[string]interface{}{
		"user_id":     vResp.Data.UserId,
		"nickname":    vResp.Data.Nickname,
		"phone":       maskedPhone,
		"avatar":      vResp.Data.Avatar,
		"gender":      vResp.Data.Gender,
		"status":      vResp.Data.Status,
		"created_at":  vResp.Data.CreatedAt,
		"is_deleted":  vResp.Data.IsDeleted,
	}

	global.GVA_LOG.Info(fmt.Sprintf("获取用户信息成功: adminUserId=%d, userId=%d, nickname=%s",
		adminUserId, vResp.Data.UserId, vResp.Data.Nickname))

	response.OkWithDetailed(result, "获取成功", c)
}
