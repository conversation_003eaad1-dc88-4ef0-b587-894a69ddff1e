package users

import (
	"github.com/flipped-aurora/gin-vue-admin/server/global"
	"github.com/flipped-aurora/gin-vue-admin/server/model/common/response"
	"github.com/flipped-aurora/gin-vue-admin/server/model/users"
	usersReq "github.com/flipped-aurora/gin-vue-admin/server/model/users/request"
	usersService "github.com/flipped-aurora/gin-vue-admin/server/service/users"
	"github.com/flipped-aurora/gin-vue-admin/server/utils"
	"github.com/gin-gonic/gin"
	"go.uber.org/zap"
	"strconv"
)

type UserAuditRecordApi struct{}

var userAuditRecordService = new(usersService.UserAuditRecordService)

// GetUserAuditRecordList 分页获取用户审核记录列表
// @Tags UserAuditRecord
// @Summary 分页获取用户审核记录列表
// @Security ApiKeyAuth
// @Accept application/json
// @Produce application/json
// @Param data query usersReq.UserAuditRecordSearch true "分页获取用户审核记录列表"
// @Success 200 {object} response.Response{data=response.PageResult,msg=string} "获取成功"
// @Router /userAuditRecord/getUserAuditRecordList [get]
func (api *UserAuditRecordApi) GetUserAuditRecordList(c *gin.Context) {
	ctx := c.Request.Context()
	
	var pageInfo usersReq.UserAuditRecordSearch
	err := c.ShouldBindQuery(&pageInfo)
	if err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}
	
	list, total, err := userAuditRecordService.GetUserAuditRecordList(ctx, pageInfo)
	if err != nil {
		global.GVA_LOG.Error("获取失败!", zap.Error(err))
		response.FailWithMessage("获取失败:"+err.Error(), c)
		return
	}
	
	// 填充完整的URL
	for _, record := range list {
		content, err := record.GetContentStruct()
		if err != nil {
			global.GVA_LOG.Error("解析审核内容失败", zap.Error(err))
			continue
		}
		
		// 根据审核类型填充对应的完整URL
		switch record.AuditType {
		case users.UserAuditTypeAvatar:
			content.Avatar = fillImageUrl(content.Avatar)
		case users.UserAuditTypeBackground:
			content.BackgroundUrl = fillImageUrl(content.BackgroundUrl)
		case users.UserAuditTypeVoice:
			content.VoiceSignatureUrl = fillAudioUrl(content.VoiceSignatureUrl)
			content.ReviewUrl = fillAudioUrl(content.ReviewUrl)
		}
		
		// 重新设置内容
		record.SetContentStruct(content)
	}
	
	response.OkWithDetailed(response.PageResult{
		List:     list,
		Total:    total,
		Page:     pageInfo.Page,
		PageSize: pageInfo.PageSize,
	}, "获取成功", c)
}

// FindUserAuditRecord 用id查询用户审核记录
// @Tags UserAuditRecord
// @Summary 用id查询用户审核记录
// @Security ApiKeyAuth
// @Accept application/json
// @Produce application/json
// @Param id query int true "用id查询用户审核记录"
// @Success 200 {object} response.Response{data=users.UserAuditRecord,msg=string} "查询成功"
// @Router /userAuditRecord/findUserAuditRecord [get]
func (api *UserAuditRecordApi) FindUserAuditRecord(c *gin.Context) {
	ctx := c.Request.Context()
	
	idStr := c.Query("id")
	id, err := strconv.ParseInt(idStr, 10, 64)
	if err != nil {
		response.FailWithMessage("ID参数错误", c)
		return
	}
	
	record, err := userAuditRecordService.GetUserAuditRecord(ctx, id)
	if err != nil {
		global.GVA_LOG.Error("查询失败!", zap.Error(err))
		response.FailWithMessage("查询失败:"+err.Error(), c)
		return
	}
	
	// 填充完整的URL
	content, err := record.GetContentStruct()
	if err != nil {
		global.GVA_LOG.Error("解析审核内容失败", zap.Error(err))
	} else {
		// 根据审核类型填充对应的完整URL
		switch record.AuditType {
		case users.UserAuditTypeAvatar:
			content.Avatar = fillImageUrl(content.Avatar)
		case users.UserAuditTypeBackground:
			content.BackgroundUrl = fillImageUrl(content.BackgroundUrl)
		case users.UserAuditTypeVoice:
			content.VoiceSignatureUrl = fillAudioUrl(content.VoiceSignatureUrl)
			content.ReviewUrl = fillAudioUrl(content.ReviewUrl)
		}
		
		// 重新设置内容
		record.SetContentStruct(content)
	}
	
	response.OkWithData(record, c)
}

// ApproveUserAuditRecord 审核通过用户审核记录
// @Tags UserAuditRecord
// @Summary 审核通过用户审核记录
// @Security ApiKeyAuth
// @Accept application/json
// @Produce application/json
// @Param data body usersReq.UserAuditRecordApprove true "审核通过用户审核记录"
// @Success 200 {object} response.Response{msg=string} "审核通过成功"
// @Router /userAuditRecord/approveUserAuditRecord [post]
func (api *UserAuditRecordApi) ApproveUserAuditRecord(c *gin.Context) {
	ctx := c.Request.Context()
	
	var req usersReq.UserAuditRecordApprove
	err := c.ShouldBindJSON(&req)
	if err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}
	
	err = userAuditRecordService.ApproveUserAuditRecord(ctx, req.ID)
	if err != nil {
		global.GVA_LOG.Error("审核通过失败!", zap.Error(err))
		response.FailWithMessage("审核通过失败:"+err.Error(), c)
		return
	}
	
	// 记录操作日志
	claims, _ := utils.GetClaims(c)
	global.GVA_LOG.Info("用户审核通过操作", 
		zap.Int64("recordId", req.ID),
		zap.String("operator", claims.Username),
		zap.Uint("operatorId", claims.BaseClaims.ID))
	
	response.OkWithMessage("审核通过成功", c)
}

// RejectUserAuditRecord 审核拒绝用户审核记录
// @Tags UserAuditRecord
// @Summary 审核拒绝用户审核记录
// @Security ApiKeyAuth
// @Accept application/json
// @Produce application/json
// @Param data body usersReq.UserAuditRecordReject true "审核拒绝用户审核记录"
// @Success 200 {object} response.Response{msg=string} "审核拒绝成功"
// @Router /userAuditRecord/rejectUserAuditRecord [post]
func (api *UserAuditRecordApi) RejectUserAuditRecord(c *gin.Context) {
	ctx := c.Request.Context()
	
	var req usersReq.UserAuditRecordReject
	err := c.ShouldBindJSON(&req)
	if err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}
	
	err = userAuditRecordService.RejectUserAuditRecord(ctx, req.ID, req.Reason)
	if err != nil {
		global.GVA_LOG.Error("审核拒绝失败!", zap.Error(err))
		response.FailWithMessage("审核拒绝失败:"+err.Error(), c)
		return
	}
	
	// 记录操作日志
	claims, _ := utils.GetClaims(c)
	global.GVA_LOG.Info("用户审核拒绝操作", 
		zap.Int64("recordId", req.ID),
		zap.String("reason", req.Reason),
		zap.String("operator", claims.Username),
		zap.Uint("operatorId", claims.BaseClaims.ID))
	
	response.OkWithMessage("审核拒绝成功", c)
}

// BatchApproveUserAuditRecords 批量审核通过用户审核记录
// @Tags UserAuditRecord
// @Summary 批量审核通过用户审核记录
// @Security ApiKeyAuth
// @Accept application/json
// @Produce application/json
// @Param data body usersReq.UserAuditRecordBatchApprove true "批量审核通过用户审核记录"
// @Success 200 {object} response.Response{msg=string} "批量审核通过成功"
// @Router /userAuditRecord/batchApproveUserAuditRecords [post]
func (api *UserAuditRecordApi) BatchApproveUserAuditRecords(c *gin.Context) {
	ctx := c.Request.Context()
	
	var req usersReq.UserAuditRecordBatchApprove
	err := c.ShouldBindJSON(&req)
	if err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}
	
	err = userAuditRecordService.BatchApproveUserAuditRecords(ctx, req.IDs)
	if err != nil {
		global.GVA_LOG.Error("批量审核通过失败!", zap.Error(err))
		response.FailWithMessage("批量审核通过失败:"+err.Error(), c)
		return
	}
	
	// 记录操作日志
	claims, _ := utils.GetClaims(c)
	global.GVA_LOG.Info("批量用户审核通过操作", 
		zap.Any("recordIds", req.IDs),
		zap.String("operator", claims.Username),
		zap.Uint("operatorId", claims.BaseClaims.ID))
	
	response.OkWithMessage("批量审核通过成功", c)
}

// BatchRejectUserAuditRecords 批量审核拒绝用户审核记录
// @Tags UserAuditRecord
// @Summary 批量审核拒绝用户审核记录
// @Security ApiKeyAuth
// @Accept application/json
// @Produce application/json
// @Param data body usersReq.UserAuditRecordBatchReject true "批量审核拒绝用户审核记录"
// @Success 200 {object} response.Response{msg=string} "批量审核拒绝成功"
// @Router /userAuditRecord/batchRejectUserAuditRecords [post]
func (api *UserAuditRecordApi) BatchRejectUserAuditRecords(c *gin.Context) {
	ctx := c.Request.Context()
	
	var req usersReq.UserAuditRecordBatchReject
	err := c.ShouldBindJSON(&req)
	if err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}
	
	err = userAuditRecordService.BatchRejectUserAuditRecords(ctx, req.IDs, req.Reason)
	if err != nil {
		global.GVA_LOG.Error("批量审核拒绝失败!", zap.Error(err))
		response.FailWithMessage("批量审核拒绝失败:"+err.Error(), c)
		return
	}
	
	// 记录操作日志
	claims, _ := utils.GetClaims(c)
	global.GVA_LOG.Info("批量用户审核拒绝操作", 
		zap.Any("recordIds", req.IDs),
		zap.String("reason", req.Reason),
		zap.String("operator", claims.Username),
		zap.Uint("operatorId", claims.BaseClaims.ID))
	
	response.OkWithMessage("批量审核拒绝成功", c)
}


