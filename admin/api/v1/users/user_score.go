package users

import (
	"github.com/flipped-aurora/gin-vue-admin/server/global"
	"github.com/flipped-aurora/gin-vue-admin/server/model/common/response"
	"github.com/gin-gonic/gin"
	"go.uber.org/zap"
)

type User<PERSON>coreApi struct{}

// FindUserScore 用id查询user_score表
// @Tags User
// @Summary 用user_id查询user_score表
// @Security ApiKeyAuth
// @Accept application/json
// @Produce application/json
// @Param user_id query int true "用user_id查询user_score表"
// @Success 200 {object} response.Response{data=users.User,msg=string} "查询成功"
// @Router /user/findUser [get]
func (userScoreApi *UserScoreApi) FindUserScore(c *gin.Context) {
	// 创建业务用Context
	ctx := c.Request.Context()

	user_id := c.Query("user_id")
	userScore, err := userScoreService.GetUserScore(ctx, user_id)
	if err != nil {
		global.GVA_LOG.Error("查询失败!", zap.Error(err))
		response.FailWithMessage("查询失败:"+err.Error(), c)
		return
	}
	response.OkWithData(userScore, c)
}
