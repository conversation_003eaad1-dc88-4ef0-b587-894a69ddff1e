package scripts

import (
	"github.com/flipped-aurora/gin-vue-admin/server/global"
	"github.com/flipped-aurora/gin-vue-admin/server/model/common/response"
	"github.com/flipped-aurora/gin-vue-admin/server/model/scripts"
	scriptsReq "github.com/flipped-aurora/gin-vue-admin/server/model/scripts/request"
	"github.com/flipped-aurora/gin-vue-admin/server/utils"
	"github.com/gin-gonic/gin"
	"go.uber.org/zap"
	"new-gitlab.xunlei.cn/vcproject/backends/baselib/logger"
	"new-gitlab.xunlei.cn/vcproject/backends/baselib/util"
	"new-gitlab.xunlei.cn/vcproject/backends/proto/api/svcscript"
	"new-gitlab.xunlei.cn/vcproject/backends/proto/svcmgr"
)

type ScriptApi struct{}

// CreateScript 创建scripts表
// @Tags Script
// @Summary 创建scripts表
// @Security ApiKeyAuth
// @Accept application/json
// @Produce application/json
// @Param data body scripts.Script true "创建scripts表"
// @Success 200 {object} response.Response{msg=string} "创建成功"
// @Router /script/createScript [post]
func (scriptApi *ScriptApi) CreateScript(c *gin.Context) {
	// 创建业务用Context
	ctx := c.Request.Context()

	var script scripts.Script
	err := c.ShouldBindJSON(&script)
	if err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}
	script.CreatedAt = util.NowTimeMillis()
	script.UpdatedAt = util.NowTimeMillis()
	err = scriptService.CreateScript(ctx, &script)
	if err != nil {
		global.GVA_LOG.Error("创建失败!", zap.Error(err))
		response.FailWithMessage("创建失败:"+err.Error(), c)
		return
	}
	response.OkWithMessage("创建成功", c)
}

// CreateBatchScript 批量创建scripts表
// @Tags Script
// @Summary 批量创建scripts表
// @Security ApiKeyAuth
// @Accept application/json
// @Produce application/json
// @Param data body scriptsReq.BatchCreateScriptReq true "批量创建scripts表"
// @Success 200 {object} response.Response{msg=string} "批量创建成功"
// @Router /script/batchCreateScript [post]
func (scriptApi *ScriptApi) CreateBatchScript(c *gin.Context) {
	ctx := c.Request.Context()

	var req scriptsReq.BatchCreateScriptReq
	err := c.ShouldBindJSON(&req)
	if err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}

	// 获取当前登录用户ID
	adminUserId := utils.GetUserID(c)

	err = scriptService.CreateBatchScript(ctx, req.Scripts, adminUserId)
	if err != nil {
		global.GVA_LOG.Error("批量创建失败!", zap.Error(err))
		response.FailWithMessage("批量创建失败:"+err.Error(), c)
		return
	}

	response.OkWithMessage("批量创建成功", c)
}

// DeleteScript 删除scripts表
// @Tags Script
// @Summary 删除scripts表
// @Security ApiKeyAuth
// @Accept application/json
// @Produce application/json
// @Param data body scripts.Script true "删除scripts表"
// @Success 200 {object} response.Response{msg=string} "删除成功"
// @Router /script/deleteScript [delete]
func (scriptApi *ScriptApi) DeleteScript(c *gin.Context) {
	// 创建业务用Context
	ctx := c.Request.Context()

	id := c.Query("id")
	err := scriptService.DeleteScript(ctx, id)
	if err != nil {
		global.GVA_LOG.Error("删除失败!", zap.Error(err))
		response.FailWithMessage("删除失败:"+err.Error(), c)
		return
	}
	response.OkWithMessage("删除成功", c)
}

// DeleteScriptByIds 批量删除scripts表
// @Tags Script
// @Summary 批量删除scripts表
// @Security ApiKeyAuth
// @Accept application/json
// @Produce application/json
// @Success 200 {object} response.Response{msg=string} "批量删除成功"
// @Router /script/deleteScriptByIds [delete]
func (scriptApi *ScriptApi) DeleteScriptByIds(c *gin.Context) {
	// 创建业务用Context
	ctx := c.Request.Context()

	ids := c.QueryArray("ids[]")
	err := scriptService.DeleteScriptByIds(ctx, ids)
	if err != nil {
		global.GVA_LOG.Error("批量删除失败!", zap.Error(err))
		response.FailWithMessage("批量删除失败:"+err.Error(), c)
		return
	}
	response.OkWithMessage("批量删除成功", c)
}

// UpdateScript 更新scripts表
// @Tags Script
// @Summary 更新scripts表
// @Security ApiKeyAuth
// @Accept application/json
// @Produce application/json
// @Param data body scripts.Script true "更新scripts表"
// @Success 200 {object} response.Response{msg=string} "更新成功"
// @Router /script/updateScript [put]
func (scriptApi *ScriptApi) UpdateScript(c *gin.Context) {
	// 从ctx获取标准context进行业务行为
	ctx := c.Request.Context()

	var script scripts.Script
	err := c.ShouldBindJSON(&script)
	if err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}
	// 设置更新时间戳（毫秒）
	nowTicks := util.NowTimeMillis()
	script.UpdatedAt = nowTicks
	err = scriptService.UpdateScript(ctx, script)
	if err != nil {
		global.GVA_LOG.Error("更新失败!", zap.Error(err))
		response.FailWithMessage("更新失败:"+err.Error(), c)
		return
	}
	response.OkWithMessage("更新成功", c)
}

// FindScript 用id查询scripts表
// @Tags Script
// @Summary 用id查询scripts表
// @Security ApiKeyAuth
// @Accept application/json
// @Produce application/json
// @Param id query int true "用id查询scripts表"
// @Success 200 {object} response.Response{data=scripts.Script,msg=string} "查询成功"
// @Router /script/findScript [get]
func (scriptApi *ScriptApi) FindScript(c *gin.Context) {
	// 创建业务用Context
	ctx := c.Request.Context()

	id := c.Query("id")
	script, err := scriptService.GetScript(ctx, id)
	if err != nil {
		global.GVA_LOG.Error("查询失败!", zap.Error(err))
		response.FailWithMessage("查询失败:"+err.Error(), c)
		return
	}

	bgmUrl := utils.FillImageUrl(*script.BgmUrl)
	script.BgmUrl = &bgmUrl
	coverUrl := utils.FillImageUrl(*script.Cover)
	script.Cover = &coverUrl
	audioUrl := utils.FillAudioUrl(*script.BgmUrl)
	script.BgmUrl = &audioUrl

	response.OkWithData(script, c)
}

// GetScriptList 分页获取scripts表列表
// @Tags Script
// @Summary 分页获取scripts表列表
// @Security ApiKeyAuth
// @Accept application/json
// @Produce application/json
// @Param data query scriptsReq.ScriptSearch true "分页获取scripts表列表"
// @Success 200 {object} response.Response{data=response.PageResult,msg=string} "获取成功"
// @Router /script/getScriptList [get]
func (scriptApi *ScriptApi) GetScriptList(c *gin.Context) {
	// 创建业务用Context
	ctx := c.Request.Context()

	var pageInfo scriptsReq.ScriptSearch
	err := c.ShouldBindQuery(&pageInfo)
	if err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}
	// 获取当前登录用户信息
	currentUserID := utils.GetUserID(c)
	currentUserAuthorityID := utils.GetUserAuthorityId(c)

	list, total, err := scriptService.GetScriptInfoList(ctx, pageInfo, currentUserID, currentUserAuthorityID)
	if err != nil {
		global.GVA_LOG.Error("获取失败!", zap.Error(err))
		response.FailWithMessage("获取失败:"+err.Error(), c)
		return
	}

	for _, script := range list {
		bgmUrl := utils.FillImageUrl(*script.BgmUrl)
		script.BgmUrl = &bgmUrl
		coverUrl := utils.FillImageUrl(*script.Cover)
		script.Cover = &coverUrl
		audioUrl := utils.FillAudioUrl(*script.BgmUrl)
		script.BgmUrl = &audioUrl
	}

	response.OkWithDetailed(response.PageResult{
		List:     list,
		Total:    total,
		Page:     pageInfo.Page,
		PageSize: pageInfo.PageSize,
	}, "获取成功", c)
}

// GetScriptPublic 不需要鉴权的scripts表接口
// @Tags Script
// @Summary 不需要鉴权的scripts表接口
// @Accept application/json
// @Produce application/json
// @Success 200 {object} response.Response{data=object,msg=string} "获取成功"
// @Router /script/getScriptPublic [get]
func (scriptApi *ScriptApi) GetScriptPublic(c *gin.Context) {
	// 创建业务用Context
	ctx := c.Request.Context()

	// 此接口不需要鉴权
	// 示例为返回了一个固定的消息接口，一般本接口用于C端服务，需要自己实现业务逻辑
	scriptService.GetScriptPublic(ctx)
	response.OkWithDetailed(gin.H{
		"info": "不需要鉴权的scripts表接口信息",
	}, "获取成功", c)
}

// AuditScript 审核剧本
// @Tags Script
// @Summary 审核剧本
// @Security ApiKeyAuth
// @Accept application/json
// @Produce application/json
// @Param data body map[string]interface{} true "审核剧本"
// @Success 200 {object} response.Response{msg=string} "审核成功"
// @Router /script/auditScript [put]
func (scriptApi *ScriptApi) AuditScript(c *gin.Context) {
	// 创建业务用Context
	ctx := c.Request.Context()

	var req struct {
		ID           int64  `json:"id" binding:"required"`
		ReviewStatus int    `json:"review_status" binding:"required"`
		RejectReason string `json:"reject_reason"`
	}

	err := c.ShouldBindJSON(&req)
	if err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}

	// 调用svcscript服务进行审核
	resp, err := svcmgr.ScriptClient().AuditScript(ctx, &svcscript.AuditScriptReq{
		ScriptId:     req.ID,
		ReviewStatus: svcscript.ReviewStatus(req.ReviewStatus),
		RejectReason: req.RejectReason,
	})
	if err != nil {
		logger.Errorf("AuditScript failed, err: %v, scriptId:%v, resp:%v", err, req.ID, resp)
		response.FailWithMessage("审核失败:"+err.Error(), c)
		return
	}

	response.OkWithMessage("审核成功", c)
}
