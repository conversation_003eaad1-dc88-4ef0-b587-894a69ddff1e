package scripts

import (
	"github.com/flipped-aurora/gin-vue-admin/server/global"
	"github.com/flipped-aurora/gin-vue-admin/server/model/common/response"
	"github.com/flipped-aurora/gin-vue-admin/server/model/scripts"
	scriptsReq "github.com/flipped-aurora/gin-vue-admin/server/model/scripts/request"
	"github.com/flipped-aurora/gin-vue-admin/server/utils"
	"github.com/gin-gonic/gin"
	"go.uber.org/zap"
	"new-gitlab.xunlei.cn/vcproject/backends/baselib/util"
)

type TopicApi struct{}

// CreateTopic 创建topics表
// @Tags Topic
// @Summary 创建topics表
// @Security ApiKeyAuth
// @Accept application/json
// @Produce application/json
// @Param data body scripts.Topic true "创建topics表"
// @Success 200 {object} response.Response{msg=string} "创建成功"
// @Router /topic/createTopic [post]
func (topicApi *TopicApi) CreateTopic(c *gin.Context) {
	// 创建业务用Context
	ctx := c.Request.Context()

	var topic scripts.Topic
	err := c.ShouldBindJSON(&topic)
	if err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}
	nowTicks := util.NowTimeMillis()
	topic.CreatedAt = &nowTicks
	topic.UpdatedAt = &nowTicks
	status := 1
	topic.Status = &status
	err = topicService.CreateTopic(ctx, &topic)
	if err != nil {
		global.GVA_LOG.Error("创建失败!", zap.Error(err))
		response.FailWithMessage("创建失败:"+err.Error(), c)
		return
	}
	response.OkWithMessage("创建成功", c)
}

// DeleteTopic 删除topics表
// @Tags Topic
// @Summary 删除topics表
// @Security ApiKeyAuth
// @Accept application/json
// @Produce application/json
// @Param data body scripts.Topic true "删除topics表"
// @Success 200 {object} response.Response{msg=string} "删除成功"
// @Router /topic/deleteTopic [delete]
func (topicApi *TopicApi) DeleteTopic(c *gin.Context) {
	// 创建业务用Context
	ctx := c.Request.Context()

	id := c.Query("id")
	err := topicService.DeleteTopic(ctx, id)
	if err != nil {
		global.GVA_LOG.Error("删除失败!", zap.Error(err))
		response.FailWithMessage("删除失败:"+err.Error(), c)
		return
	}
	response.OkWithMessage("删除成功", c)
}

// DeleteTopicByIds 批量删除topics表
// @Tags Topic
// @Summary 批量删除topics表
// @Security ApiKeyAuth
// @Accept application/json
// @Produce application/json
// @Success 200 {object} response.Response{msg=string} "批量删除成功"
// @Router /topic/deleteTopicByIds [delete]
func (topicApi *TopicApi) DeleteTopicByIds(c *gin.Context) {
	// 创建业务用Context
	ctx := c.Request.Context()

	ids := c.QueryArray("ids[]")
	err := topicService.DeleteTopicByIds(ctx, ids)
	if err != nil {
		global.GVA_LOG.Error("批量删除失败!", zap.Error(err))
		response.FailWithMessage("批量删除失败:"+err.Error(), c)
		return
	}
	response.OkWithMessage("批量删除成功", c)
}

// UpdateTopic 更新topics表
// @Tags Topic
// @Summary 更新topics表
// @Security ApiKeyAuth
// @Accept application/json
// @Produce application/json
// @Param data body scripts.Topic true "更新topics表"
// @Success 200 {object} response.Response{msg=string} "更新成功"
// @Router /topic/updateTopic [put]
func (topicApi *TopicApi) UpdateTopic(c *gin.Context) {
	// 从ctx获取标准context进行业务行为
	ctx := c.Request.Context()

	var topic scripts.Topic
	err := c.ShouldBindJSON(&topic)
	if err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}

	nowTicks := util.NowTimeMillis()
	topic.UpdatedAt = &nowTicks
	err = topicService.UpdateTopic(ctx, topic)
	if err != nil {
		global.GVA_LOG.Error("更新失败!", zap.Error(err))
		response.FailWithMessage("更新失败:"+err.Error(), c)
		return
	}
	response.OkWithMessage("更新成功", c)
}

// FindTopic 用id查询topics表
// @Tags Topic
// @Summary 用id查询topics表
// @Security ApiKeyAuth
// @Accept application/json
// @Produce application/json
// @Param id query int true "用id查询topics表"
// @Success 200 {object} response.Response{data=scripts.Topic,msg=string} "查询成功"
// @Router /topic/findTopic [get]
func (topicApi *TopicApi) FindTopic(c *gin.Context) {
	// 创建业务用Context
	ctx := c.Request.Context()

	id := c.Query("id")
	retopic, err := topicService.GetTopic(ctx, id)
	if err != nil {
		global.GVA_LOG.Error("查询失败!", zap.Error(err))
		response.FailWithMessage("查询失败:"+err.Error(), c)
		return
	}
	response.OkWithData(retopic, c)
}

// GetTopicList 分页获取topics表列表
// @Tags Topic
// @Summary 分页获取topics表列表
// @Security ApiKeyAuth
// @Accept application/json
// @Produce application/json
// @Param data query scriptsReq.TopicSearch true "分页获取topics表列表"
// @Success 200 {object} response.Response{data=response.PageResult,msg=string} "获取成功"
// @Router /topic/getTopicList [get]
func (topicApi *TopicApi) GetTopicList(c *gin.Context) {
	// 创建业务用Context
	ctx := c.Request.Context()

	var pageInfo scriptsReq.TopicSearch
	err := c.ShouldBindQuery(&pageInfo)
	if err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}
	list, total, err := topicService.GetTopicInfoList(ctx, pageInfo)
	if err != nil {
		global.GVA_LOG.Error("获取失败!", zap.Error(err))
		response.FailWithMessage("获取失败:"+err.Error(), c)
		return
	}
	response.OkWithDetailed(response.PageResult{
		List:     list,
		Total:    total,
		Page:     pageInfo.Page,
		PageSize: pageInfo.PageSize,
	}, "获取成功", c)
}

// GetTopicPublic 不需要鉴权的topics表接口
// @Tags Topic
// @Summary 不需要鉴权的topics表接口
// @Accept application/json
// @Produce application/json
// @Success 200 {object} response.Response{data=object,msg=string} "获取成功"
// @Router /topic/getTopicPublic [get]
func (topicApi *TopicApi) GetTopicPublic(c *gin.Context) {
	// 创建业务用Context
	ctx := c.Request.Context()

	// 此接口不需要鉴权
	// 示例为返回了一个固定的消息接口，一般本接口用于C端服务，需要自己实现业务逻辑
	topicService.GetTopicPublic(ctx)
	response.OkWithDetailed(gin.H{
		"info": "不需要鉴权的topics表接口信息",
	}, "获取成功", c)
}

// GetTopicList 分页获取topics表列表
// @Tags Topic
// @Summary 分页获取topics表列表
// @Security ApiKeyAuth
// @Accept application/json
// @Produce application/json
// @Param data query scriptsReq.TopicSearch true "分页获取topics表列表"
// @Success 200 {object} response.Response{data=response.PageResult,msg=string} "获取成功"
// @Router /topic/getTopicLabelList [get]
func (topicApi *TopicApi) GetTopicLabelList(c *gin.Context) {
	// 创建业务用Context
	ctx := c.Request.Context()

	list, total, err := topicService.GetTopicLabels(ctx)
	if err != nil {
		global.GVA_LOG.Error("获取失败!", zap.Error(err))
		response.FailWithMessage("获取失败:"+err.Error(), c)
		return
	}

	for _, item := range list {
		bgmUrl := utils.FillImageUrl(*item.BgUrl)
		item.BgUrl = &bgmUrl
	}

	response.OkWithDetailed(response.PageResult{
		List:  list,
		Total: total,
	}, "获取成功", c)
}
