package scripts

import (
	"fmt"
	"github.com/flipped-aurora/gin-vue-admin/server/global"
	"github.com/flipped-aurora/gin-vue-admin/server/model/common/response"
	"github.com/flipped-aurora/gin-vue-admin/server/model/scripts"
	scriptsReq "github.com/flipped-aurora/gin-vue-admin/server/model/scripts/request"
	"github.com/gin-gonic/gin"
	"go.uber.org/zap"
	"new-gitlab.xunlei.cn/vcproject/backends/baselib/util"
)

type ScriptCharacterRelationApi struct{}

// CreateScriptCharacterRelation 创建scriptCharacterRelation表
// @Tags ScriptCharacterRelation
// @Summary 创建scriptCharacterRelation表
// @Security ApiKeyAuth
// @Accept application/json
// @Produce application/json
// @Param data body scripts.ScriptCharacterRelation true "创建scriptCharacterRelation表"
// @Success 200 {object} response.Response{msg=string} "创建成功"
// @Router /scriptCharacterRelation/createScriptCharacterRelation [post]
func (scriptCharacterRelationApi *ScriptCharacterRelationApi) CreateScriptCharacterRelation(c *gin.Context) {
	// 创建业务用Context
	ctx := c.Request.Context()

	var scriptCharacterRelation scripts.ScriptCharacterRelation
	err := c.ShouldBindJSON(&scriptCharacterRelation)
	if err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}
	err = scriptCharacterRelationService.CreateScriptCharacterRelation(ctx, &scriptCharacterRelation)
	if err != nil {
		global.GVA_LOG.Error("创建失败!", zap.Error(err))
		response.FailWithMessage("创建失败:"+err.Error(), c)
		return
	}
	response.OkWithMessage("创建成功", c)
}

// DeleteScriptCharacterRelation 删除scriptCharacterRelation表
// @Tags ScriptCharacterRelation
// @Summary 删除scriptCharacterRelation表
// @Security ApiKeyAuth
// @Accept application/json
// @Produce application/json
// @Param data body scripts.ScriptCharacterRelation true "删除scriptCharacterRelation表"
// @Success 200 {object} response.Response{msg=string} "删除成功"
// @Router /scriptCharacterRelation/deleteScriptCharacterRelation [delete]
func (scriptCharacterRelationApi *ScriptCharacterRelationApi) DeleteScriptCharacterRelation(c *gin.Context) {
	// 创建业务用Context
	ctx := c.Request.Context()

	id := c.Query("id")
	err := scriptCharacterRelationService.DeleteScriptCharacterRelation(ctx, id)
	if err != nil {
		global.GVA_LOG.Error("删除失败!", zap.Error(err))
		response.FailWithMessage("删除失败:"+err.Error(), c)
		return
	}
	response.OkWithMessage("删除成功", c)
}

// DeleteScriptCharacterRelationByIds 批量删除scriptCharacterRelation表
// @Tags ScriptCharacterRelation
// @Summary 批量删除scriptCharacterRelation表
// @Security ApiKeyAuth
// @Accept application/json
// @Produce application/json
// @Success 200 {object} response.Response{msg=string} "批量删除成功"
// @Router /scriptCharacterRelation/deleteScriptCharacterRelationByIds [delete]
func (scriptCharacterRelationApi *ScriptCharacterRelationApi) DeleteScriptCharacterRelationByIds(c *gin.Context) {
	// 创建业务用Context
	ctx := c.Request.Context()

	ids := c.QueryArray("ids[]")
	err := scriptCharacterRelationService.DeleteScriptCharacterRelationByIds(ctx, ids)
	if err != nil {
		global.GVA_LOG.Error("批量删除失败!", zap.Error(err))
		response.FailWithMessage("批量删除失败:"+err.Error(), c)
		return
	}
	response.OkWithMessage("批量删除成功", c)
}

// UpdateScriptCharacterRelation 更新scriptCharacterRelation表
// @Tags ScriptCharacterRelation
// @Summary 更新scriptCharacterRelation表
// @Security ApiKeyAuth
// @Accept application/json
// @Produce application/json
// @Param data body scripts.ScriptCharacterRelation true "更新scriptCharacterRelation表"
// @Success 200 {object} response.Response{msg=string} "更新成功"
// @Router /scriptCharacterRelation/updateScriptCharacterRelation [put]
func (scriptCharacterRelationApi *ScriptCharacterRelationApi) UpdateScriptCharacterRelation(c *gin.Context) {
	// 从ctx获取标准context进行业务行为
	ctx := c.Request.Context()

	var scriptCharacterRelation scripts.ScriptCharacterRelation
	err := c.ShouldBindJSON(&scriptCharacterRelation)
	if err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}

	nowTicks := util.NowTimeMillis()
	scriptCharacterRelation.UpdatedAt = &nowTicks
	err = scriptCharacterRelationService.UpdateScriptCharacterRelation(ctx, scriptCharacterRelation)
	if err != nil {
		global.GVA_LOG.Error("更新失败!", zap.Error(err))
		response.FailWithMessage("更新失败:"+err.Error(), c)
		return
	}
	response.OkWithMessage("更新成功", c)
}

// FindScriptCharacterRelation 用id查询scriptCharacterRelation表
// @Tags ScriptCharacterRelation
// @Summary 用id查询scriptCharacterRelation表
// @Security ApiKeyAuth
// @Accept application/json
// @Produce application/json
// @Param id query int true "用id查询scriptCharacterRelation表"
// @Success 200 {object} response.Response{data=scripts.ScriptCharacterRelation,msg=string} "查询成功"
// @Router /scriptCharacterRelation/findScriptCharacterRelation [get]
func (scriptCharacterRelationApi *ScriptCharacterRelationApi) FindScriptCharacterRelation(c *gin.Context) {
	// 创建业务用Context
	ctx := c.Request.Context()

	id := c.Query("id")
	rescriptCharacterRelation, err := scriptCharacterRelationService.GetScriptCharacterRelation(ctx, id)
	if err != nil {
		global.GVA_LOG.Error("查询失败!", zap.Error(err))
		response.FailWithMessage("查询失败:"+err.Error(), c)
		return
	}
	response.OkWithData(rescriptCharacterRelation, c)
}

// GetScriptCharacterRelationList 分页获取scriptCharacterRelation表列表
// @Tags ScriptCharacterRelation
// @Summary 分页获取scriptCharacterRelation表列表
// @Security ApiKeyAuth
// @Accept application/json
// @Produce application/json
// @Param data query scriptsReq.ScriptCharacterRelationSearch true "分页获取scriptCharacterRelation表列表"
// @Success 200 {object} response.Response{data=response.PageResult,msg=string} "获取成功"
// @Router /scriptCharacterRelation/getScriptCharacterRelationList [get]
func (scriptCharacterRelationApi *ScriptCharacterRelationApi) GetScriptCharacterRelationList(c *gin.Context) {
	// 创建业务用Context
	ctx := c.Request.Context()

	var pageInfo scriptsReq.ScriptCharacterRelationSearch
	err := c.ShouldBindQuery(&pageInfo)
	if err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}
	list, total, err := scriptCharacterRelationService.GetScriptCharacterRelationInfoList(ctx, pageInfo)
	if err != nil {
		global.GVA_LOG.Error("获取失败!", zap.Error(err))
		response.FailWithMessage("获取失败:"+err.Error(), c)
		return
	}

	for _, item := range list {
		id := fmt.Sprintf("%d", *item.CharacterId)
		char, err := characterService.GetCharacter(ctx, id)
		if err != nil {
			global.GVA_LOG.Error("查询失败!", zap.Error(err))
		}
		assetId := fmt.Sprintf("%d", *item.CharacterAssetId)
		asset, err := characterAssetsService.GetCharacterAssets(ctx, assetId)
		if err != nil {
			global.GVA_LOG.Error("查询asset失败!", zap.Error(err))
		}
		item.CharacterName = *char.Name
		item.AssetUrl = *asset.BackgroundUrl
	}

	response.OkWithDetailed(response.PageResult{
		List:     list,
		Total:    total,
		Page:     pageInfo.Page,
		PageSize: pageInfo.PageSize,
	}, "获取成功", c)
}

// GetScriptCharacterRelationPublic 不需要鉴权的scriptCharacterRelation表接口
// @Tags ScriptCharacterRelation
// @Summary 不需要鉴权的scriptCharacterRelation表接口
// @Accept application/json
// @Produce application/json
// @Success 200 {object} response.Response{data=object,msg=string} "获取成功"
// @Router /scriptCharacterRelation/getScriptCharacterRelationPublic [get]
func (scriptCharacterRelationApi *ScriptCharacterRelationApi) GetScriptCharacterRelationPublic(c *gin.Context) {
	// 创建业务用Context
	ctx := c.Request.Context()

	// 此接口不需要鉴权
	// 示例为返回了一个固定的消息接口，一般本接口用于C端服务，需要自己实现业务逻辑
	scriptCharacterRelationService.GetScriptCharacterRelationPublic(ctx)
	response.OkWithDetailed(gin.H{
		"info": "不需要鉴权的scriptCharacterRelation表接口信息",
	}, "获取成功", c)
}
