package scripts

import (
	"github.com/flipped-aurora/gin-vue-admin/server/global"
	"github.com/flipped-aurora/gin-vue-admin/server/model/common/response"
	"github.com/flipped-aurora/gin-vue-admin/server/model/scripts"
	scriptsReq "github.com/flipped-aurora/gin-vue-admin/server/model/scripts/request"
	"github.com/flipped-aurora/gin-vue-admin/server/utils"
	"github.com/gin-gonic/gin"
	"go.uber.org/zap"
	"new-gitlab.xunlei.cn/vcproject/backends/baselib/util"
)

type CoverApi struct{}

// CreateCover 创建covers表
// @Tags Cover
// @Summary 创建covers表
// @Security ApiKeyAuth
// @Accept application/json
// @Produce application/json
// @Param data body scripts.Cover true "创建covers表"
// @Success 200 {object} response.Response{msg=string} "创建成功"
// @Router /cover/createCover [post]
func (coverApi *CoverApi) CreateCover(c *gin.Context) {
	// 创建业务用Context
	ctx := c.Request.Context()

	var cover scripts.Cover
	err := c.ShouldBindJSON(&cover)
	if err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}
	cover.CreatedAt = util.NowTimeMillis()
	cover.UpdatedAt = util.NowTimeMillis()
	err = coverService.CreateCover(ctx, &cover)
	if err != nil {
		global.GVA_LOG.Error("创建失败!", zap.Error(err))
		response.FailWithMessage("创建失败:"+err.Error(), c)
		return
	}
	response.OkWithMessage("创建成功", c)
}

// DeleteCover 删除covers表
// @Tags Cover
// @Summary 删除covers表
// @Security ApiKeyAuth
// @Accept application/json
// @Produce application/json
// @Param data body scripts.Cover true "删除covers表"
// @Success 200 {object} response.Response{msg=string} "删除成功"
// @Router /cover/deleteCover [delete]
func (coverApi *CoverApi) DeleteCover(c *gin.Context) {
	// 创建业务用Context
	ctx := c.Request.Context()

	id := c.Query("id")
	err := coverService.DeleteCover(ctx, id)
	if err != nil {
		global.GVA_LOG.Error("删除失败!", zap.Error(err))
		response.FailWithMessage("删除失败:"+err.Error(), c)
		return
	}
	response.OkWithMessage("删除成功", c)
}

// DeleteCoverByIds 批量删除covers表
// @Tags Cover
// @Summary 批量删除covers表
// @Security ApiKeyAuth
// @Accept application/json
// @Produce application/json
// @Success 200 {object} response.Response{msg=string} "批量删除成功"
// @Router /cover/deleteCoverByIds [delete]
func (coverApi *CoverApi) DeleteCoverByIds(c *gin.Context) {
	// 创建业务用Context
	ctx := c.Request.Context()

	ids := c.QueryArray("ids[]")
	err := coverService.DeleteCoverByIds(ctx, ids)
	if err != nil {
		global.GVA_LOG.Error("批量删除失败!", zap.Error(err))
		response.FailWithMessage("批量删除失败:"+err.Error(), c)
		return
	}
	response.OkWithMessage("批量删除成功", c)
}

// UpdateCover 更新covers表
// @Tags Cover
// @Summary 更新covers表
// @Security ApiKeyAuth
// @Accept application/json
// @Produce application/json
// @Param data body scripts.Cover true "更新covers表"
// @Success 200 {object} response.Response{msg=string} "更新成功"
// @Router /cover/updateCover [put]
func (coverApi *CoverApi) UpdateCover(c *gin.Context) {
	// 从ctx获取标准context进行业务行为
	ctx := c.Request.Context()

	var cover scripts.Cover
	err := c.ShouldBindJSON(&cover)
	if err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}
	err = coverService.UpdateCover(ctx, cover)
	if err != nil {
		global.GVA_LOG.Error("更新失败!", zap.Error(err))
		response.FailWithMessage("更新失败:"+err.Error(), c)
		return
	}
	response.OkWithMessage("更新成功", c)
}

// FindCover 用id查询covers表
// @Tags Cover
// @Summary 用id查询covers表
// @Security ApiKeyAuth
// @Accept application/json
// @Produce application/json
// @Param id query int true "用id查询covers表"
// @Success 200 {object} response.Response{data=scripts.Cover,msg=string} "查询成功"
// @Router /cover/findCover [get]
func (coverApi *CoverApi) FindCover(c *gin.Context) {
	// 创建业务用Context
	ctx := c.Request.Context()

	id := c.Query("id")
	recover, err := coverService.GetCover(ctx, id)
	if err != nil {
		global.GVA_LOG.Error("查询失败!", zap.Error(err))
		response.FailWithMessage("查询失败:"+err.Error(), c)
		return
	}
	if len(*recover.Url) > 0 {
		bgmUrl := utils.FillImageUrl(*recover.Url)
		recover.Url = &bgmUrl
	}
	response.OkWithData(recover, c)
}

// GetCoverList 分页获取covers表列表
// @Tags Cover
// @Summary 分页获取covers表列表
// @Security ApiKeyAuth
// @Accept application/json
// @Produce application/json
// @Param data query scriptsReq.CoverSearch true "分页获取covers表列表"
// @Success 200 {object} response.Response{data=response.PageResult,msg=string} "获取成功"
// @Router /cover/getCoverList [get]
func (coverApi *CoverApi) GetCoverList(c *gin.Context) {
	// 创建业务用Context
	ctx := c.Request.Context()

	var pageInfo scriptsReq.CoverSearch
	err := c.ShouldBindQuery(&pageInfo)
	if err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}
	list, total, err := coverService.GetCoverInfoList(ctx, pageInfo)
	if err != nil {
		global.GVA_LOG.Error("获取失败!", zap.Error(err))
		response.FailWithMessage("获取失败:"+err.Error(), c)
		return
	}

	for _, item := range list {
		bgmUrl := utils.FillImageUrl(*item.Url)
		item.Url = &bgmUrl
	}

	response.OkWithDetailed(response.PageResult{
		List:     list,
		Total:    total,
		Page:     pageInfo.Page,
		PageSize: pageInfo.PageSize,
	}, "获取成功", c)
}

// GetCoverPublic 不需要鉴权的covers表接口
// @Tags Cover
// @Summary 不需要鉴权的covers表接口
// @Accept application/json
// @Produce application/json
// @Success 200 {object} response.Response{data=object,msg=string} "获取成功"
// @Router /cover/getCoverPublic [get]
func (coverApi *CoverApi) GetCoverPublic(c *gin.Context) {
	// 创建业务用Context
	ctx := c.Request.Context()

	// 此接口不需要鉴权
	// 示例为返回了一个固定的消息接口，一般本接口用于C端服务，需要自己实现业务逻辑
	coverService.GetCoverPublic(ctx)
	response.OkWithDetailed(gin.H{
		"info": "不需要鉴权的covers表接口信息",
	}, "获取成功", c)
}
