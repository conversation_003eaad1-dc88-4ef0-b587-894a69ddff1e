package scripts

import (
	"github.com/flipped-aurora/gin-vue-admin/server/global"
	"github.com/flipped-aurora/gin-vue-admin/server/model/common/response"
	"github.com/flipped-aurora/gin-vue-admin/server/model/scripts"
	scriptsReq "github.com/flipped-aurora/gin-vue-admin/server/model/scripts/request"
	"github.com/gin-gonic/gin"
	"go.uber.org/zap"
	"new-gitlab.xunlei.cn/vcproject/backends/baselib/util"
)

type ReportApi struct{}

// CreateReport 创建reports表
// @Tags Report
// @Summary 创建reports表
// @Security ApiKeyAuth
// @Accept application/json
// @Produce application/json
// @Param data body scripts.Report true "创建reports表"
// @Success 200 {object} response.Response{msg=string} "创建成功"
// @Router /report/createReport [post]
func (reportApi *ReportApi) CreateReport(c *gin.Context) {
	// 创建业务用Context
	ctx := c.Request.Context()

	var report scripts.Report
	err := c.ShouldBindJSON(&report)
	if err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}
	err = reportService.CreateReport(ctx, &report)
	if err != nil {
		global.GVA_LOG.Error("创建失败!", zap.Error(err))
		response.FailWithMessage("创建失败:"+err.Error(), c)
		return
	}
	response.OkWithMessage("创建成功", c)
}

// DeleteReport 删除reports表
// @Tags Report
// @Summary 删除reports表
// @Security ApiKeyAuth
// @Accept application/json
// @Produce application/json
// @Param data body scripts.Report true "删除reports表"
// @Success 200 {object} response.Response{msg=string} "删除成功"
// @Router /report/deleteReport [delete]
func (reportApi *ReportApi) DeleteReport(c *gin.Context) {
	// 创建业务用Context
	ctx := c.Request.Context()

	id := c.Query("id")
	err := reportService.DeleteReport(ctx, id)
	if err != nil {
		global.GVA_LOG.Error("删除失败!", zap.Error(err))
		response.FailWithMessage("删除失败:"+err.Error(), c)
		return
	}
	response.OkWithMessage("删除成功", c)
}

// DeleteReportByIds 批量删除reports表
// @Tags Report
// @Summary 批量删除reports表
// @Security ApiKeyAuth
// @Accept application/json
// @Produce application/json
// @Success 200 {object} response.Response{msg=string} "批量删除成功"
// @Router /report/deleteReportByIds [delete]
func (reportApi *ReportApi) DeleteReportByIds(c *gin.Context) {
	// 创建业务用Context
	ctx := c.Request.Context()

	ids := c.QueryArray("ids[]")
	err := reportService.DeleteReportByIds(ctx, ids)
	if err != nil {
		global.GVA_LOG.Error("批量删除失败!", zap.Error(err))
		response.FailWithMessage("批量删除失败:"+err.Error(), c)
		return
	}
	response.OkWithMessage("批量删除成功", c)
}

// UpdateReport 更新reports表
// @Tags Report
// @Summary 更新reports表
// @Security ApiKeyAuth
// @Accept application/json
// @Produce application/json
// @Param data body scripts.Report true "更新reports表"
// @Success 200 {object} response.Response{msg=string} "更新成功"
// @Router /report/updateReport [put]
func (reportApi *ReportApi) UpdateReport(c *gin.Context) {
	// 从ctx获取标准context进行业务行为
	ctx := c.Request.Context()

	var report scripts.Report
	err := c.ShouldBindJSON(&report)
	if err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}

	nowTicks := util.NowTimeMillis()
	report.UpdatedAt = &nowTicks
	err = reportService.UpdateReport(ctx, report)
	if err != nil {
		global.GVA_LOG.Error("更新失败!", zap.Error(err))
		response.FailWithMessage("更新失败:"+err.Error(), c)
		return
	}
	response.OkWithMessage("更新成功", c)
}

// FindReport 用id查询reports表
// @Tags Report
// @Summary 用id查询reports表
// @Security ApiKeyAuth
// @Accept application/json
// @Produce application/json
// @Param id query int true "用id查询reports表"
// @Success 200 {object} response.Response{data=scripts.Report,msg=string} "查询成功"
// @Router /report/findReport [get]
func (reportApi *ReportApi) FindReport(c *gin.Context) {
	// 创建业务用Context
	ctx := c.Request.Context()

	id := c.Query("id")
	rereport, err := reportService.GetReport(ctx, id)
	if err != nil {
		global.GVA_LOG.Error("查询失败!", zap.Error(err))
		response.FailWithMessage("查询失败:"+err.Error(), c)
		return
	}
	response.OkWithData(rereport, c)
}

// GetReportList 分页获取reports表列表
// @Tags Report
// @Summary 分页获取reports表列表
// @Security ApiKeyAuth
// @Accept application/json
// @Produce application/json
// @Param data query scriptsReq.ReportSearch true "分页获取reports表列表"
// @Success 200 {object} response.Response{data=response.PageResult,msg=string} "获取成功"
// @Router /report/getReportList [get]
func (reportApi *ReportApi) GetReportList(c *gin.Context) {
	// 创建业务用Context
	ctx := c.Request.Context()

	var pageInfo scriptsReq.ReportSearch
	err := c.ShouldBindQuery(&pageInfo)
	if err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}
	list, total, err := reportService.GetReportInfoList(ctx, pageInfo)
	if err != nil {
		global.GVA_LOG.Error("获取失败!", zap.Error(err))
		response.FailWithMessage("获取失败:"+err.Error(), c)
		return
	}
	response.OkWithDetailed(response.PageResult{
		List:     list,
		Total:    total,
		Page:     pageInfo.Page,
		PageSize: pageInfo.PageSize,
	}, "获取成功", c)
}

// GetReportPublic 不需要鉴权的reports表接口
// @Tags Report
// @Summary 不需要鉴权的reports表接口
// @Accept application/json
// @Produce application/json
// @Success 200 {object} response.Response{data=object,msg=string} "获取成功"
// @Router /report/getReportPublic [get]
func (reportApi *ReportApi) GetReportPublic(c *gin.Context) {
	// 创建业务用Context
	ctx := c.Request.Context()

	// 此接口不需要鉴权
	// 示例为返回了一个固定的消息接口，一般本接口用于C端服务，需要自己实现业务逻辑
	reportService.GetReportPublic(ctx)
	response.OkWithDetailed(gin.H{
		"info": "不需要鉴权的reports表接口信息",
	}, "获取成功", c)
}

// AuditReport 审核举报
// @Tags Report
// @Summary 审核举报
// @Security ApiKeyAuth
// @Accept application/json
// @Produce application/json
// @Param data body scriptsReq.ReportAuditReq true "审核举报"
// @Success 200 {object} response.Response{msg=string} "审核成功"
// @Router /report/auditReport [put]
func (reportApi *ReportApi) AuditReport(c *gin.Context) {
	// 创建业务用Context
	ctx := c.Request.Context()

	var req scriptsReq.ReportAuditReq
	err := c.ShouldBindJSON(&req)
	if err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}

	// 验证审核状态
	if req.Status != 1 && req.Status != 2 {
		response.FailWithMessage("审核状态只能是1(不通过)或2(通过)", c)
		return
	}

	// 如果是不通过状态，必须选择违规标签
	if req.Status == 1 && len(req.Tags) == 0 {
		response.FailWithMessage("审核不通过时必须选择至少一个违规标签", c)
		return
	}

	err = reportService.AuditReport(ctx, req)
	if err != nil {
		global.GVA_LOG.Error("审核失败!", zap.Error(err))
		response.FailWithMessage("审核失败:"+err.Error(), c)
		return
	}
	response.OkWithMessage("审核成功", c)
}

// GetReportContentDetail 获取举报内容详情
// @Tags Report
// @Summary 获取举报内容详情
// @Security ApiKeyAuth
// @Accept application/json
// @Produce application/json
// @Param data query scriptsReq.ReportContentDetailReq true "获取举报内容详情"
// @Success 200 {object} response.Response{data=object,msg=string} "获取成功"
// @Router /report/getReportContentDetail [get]
func (reportApi *ReportApi) GetReportContentDetail(c *gin.Context) {
	// 创建业务用Context
	ctx := c.Request.Context()

	// 调试输出原始参数
	global.GVA_LOG.Info("GetReportContentDetail 原始查询参数", zap.Any("query", c.Request.URL.Query()))

	var req scriptsReq.ReportContentDetailReq
	err := c.ShouldBindQuery(&req)
	if err != nil {
		global.GVA_LOG.Error("参数绑定失败", zap.Error(err), zap.Any("query", c.Request.URL.Query()))
		response.FailWithMessage("参数验证失败: "+err.Error(), c)
		return
	}

	global.GVA_LOG.Info("绑定后的参数", zap.Any("req", req))

	detail, err := reportService.GetReportContentDetail(ctx, req)
	if err != nil {
		global.GVA_LOG.Error("获取举报内容详情失败!", zap.Error(err))
		response.FailWithMessage("获取失败:"+err.Error(), c)
		return
	}
	response.OkWithData(detail, c)
}
