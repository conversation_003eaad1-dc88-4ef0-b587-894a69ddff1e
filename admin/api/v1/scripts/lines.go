package scripts

import (
	"github.com/flipped-aurora/gin-vue-admin/server/global"
	"github.com/flipped-aurora/gin-vue-admin/server/model/common/response"
	"github.com/flipped-aurora/gin-vue-admin/server/model/scripts"
	scriptsReq "github.com/flipped-aurora/gin-vue-admin/server/model/scripts/request"
	"github.com/gin-gonic/gin"
	"github.com/jinzhu/copier"
	"go.uber.org/zap"
	"new-gitlab.xunlei.cn/vcproject/backends/baselib/util"
)

type LineApi struct{}

// CreateLine 创建lines表
// @Tags Line
// @Summary 创建lines表
// @Security ApiKeyAuth
// @Accept application/json
// @Produce application/json
// @Param data body scripts.Line true "创建lines表"
// @Success 200 {object} response.Response{msg=string} "创建成功"
// @Router /line/createLine [post]
func (lineApi *LineApi) CreateLine(c *gin.Context) {
	// 创建业务用Context
	ctx := c.Request.Context()

	var line scripts.Line
	err := c.ShouldBindJSON(&line)
	if err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}
	err = lineService.CreateLine(ctx, &line)
	if err != nil {
		global.GVA_LOG.Error("创建失败!", zap.Error(err))
		response.FailWithMessage("创建失败:"+err.Error(), c)
		return
	}
	response.OkWithMessage("创建成功", c)
}

// DeleteLine 删除lines表
// @Tags Line
// @Summary 删除lines表
// @Security ApiKeyAuth
// @Accept application/json
// @Produce application/json
// @Param data body scripts.Line true "删除lines表"
// @Success 200 {object} response.Response{msg=string} "删除成功"
// @Router /line/deleteLine [delete]
func (lineApi *LineApi) DeleteLine(c *gin.Context) {
	// 创建业务用Context
	ctx := c.Request.Context()

	id := c.Query("id")
	err := lineService.DeleteLine(ctx, id)
	if err != nil {
		global.GVA_LOG.Error("删除失败!", zap.Error(err))
		response.FailWithMessage("删除失败:"+err.Error(), c)
		return
	}
	response.OkWithMessage("删除成功", c)
}

// DeleteLineByIds 批量删除lines表
// @Tags Line
// @Summary 批量删除lines表
// @Security ApiKeyAuth
// @Accept application/json
// @Produce application/json
// @Success 200 {object} response.Response{msg=string} "批量删除成功"
// @Router /line/deleteLineByIds [delete]
func (lineApi *LineApi) DeleteLineByIds(c *gin.Context) {
	// 创建业务用Context
	ctx := c.Request.Context()

	ids := c.QueryArray("ids[]")
	err := lineService.DeleteLineByIds(ctx, ids)
	if err != nil {
		global.GVA_LOG.Error("批量删除失败!", zap.Error(err))
		response.FailWithMessage("批量删除失败:"+err.Error(), c)
		return
	}
	response.OkWithMessage("批量删除成功", c)
}

// UpdateLine 更新lines表
// @Tags Line
// @Summary 更新lines表
// @Security ApiKeyAuth
// @Accept application/json
// @Produce application/json
// @Param data body scripts.Line true "更新lines表"
// @Success 200 {object} response.Response{msg=string} "更新成功"
// @Router /line/updateLine [put]
func (lineApi *LineApi) UpdateLine(c *gin.Context) {
	// 从ctx获取标准context进行业务行为
	ctx := c.Request.Context()

	var line scripts.Line
	err := c.ShouldBindJSON(&line)
	if err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}

	nowTicks := util.NowTimeMillis()
	line.UpdatedAt = &nowTicks
	global.GVA_LOG.Debug("updateLine", zap.Any("line", line))
	err = lineService.UpdateLine(ctx, line)
	if err != nil {
		global.GVA_LOG.Error("更新失败!", zap.Error(err))
		response.FailWithMessage("更新失败:"+err.Error(), c)
		return
	}
	response.OkWithMessage("更新成功", c)
}

// FindLine 用id查询lines表
// @Tags Line
// @Summary 用id查询lines表
// @Security ApiKeyAuth
// @Accept application/json
// @Produce application/json
// @Param id query int true "用id查询lines表"
// @Success 200 {object} response.Response{data=scripts.Line,msg=string} "查询成功"
// @Router /line/findLine [get]
func (lineApi *LineApi) FindLine(c *gin.Context) {
	// 创建业务用Context
	ctx := c.Request.Context()

	id := c.Query("id")
	reline, err := lineService.GetLine(ctx, id)
	if err != nil {
		global.GVA_LOG.Error("查询失败!", zap.Error(err))
		response.FailWithMessage("查询失败:"+err.Error(), c)
		return
	}
	response.OkWithData(reline, c)
}

// GetLineList 分页获取lines表列表
// @Tags Line
// @Summary 分页获取lines表列表
// @Security ApiKeyAuth
// @Accept application/json
// @Produce application/json
// @Param data query scriptsReq.LineSearch true "分页获取lines表列表"
// @Success 200 {object} response.Response{data=response.PageResult,msg=string} "获取成功"
// @Router /line/getLineList [get]
func (lineApi *LineApi) GetLineList(c *gin.Context) {
	// 创建业务用Context
	ctx := c.Request.Context()

	var pageInfo scriptsReq.LineSearch
	err := c.ShouldBindQuery(&pageInfo)
	if err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}
	list, total, err := lineService.GetLineInfoList(ctx, pageInfo)
	if err != nil {
		global.GVA_LOG.Error("获取失败!", zap.Error(err))
		response.FailWithMessage("获取失败:"+err.Error(), c)
		return
	}
	lineList := make([]*scripts.ScriptLine, 0, len(list))
	if len(list) > 0 {
		lineIds := make([]int64, 0, len(list))
		for _, lineInfo := range list {
			lineIds = append(lineIds, *lineInfo.Id)
		}
		dubbings, err := dubbingService.GetDubbingInfoByLineIds(ctx, lineIds)
		if err != nil {
			global.GVA_LOG.Error("获取失败!", zap.Error(err))
			response.FailWithMessage("获取失败:"+err.Error(), c)
			return
		}
		dubbingMap := make(map[int64]*scripts.Dubbing)
		for _, dubbingItem := range dubbings {
			dubbingMap[*dubbingItem.LineId] = dubbingItem
		}
		for _, lineInfoItem := range list {
			scriptLine := &scripts.ScriptLine{}
			copier.Copy(scriptLine, lineInfoItem)
			if dubbingItem, ok := dubbingMap[*lineInfoItem.Id]; ok {
				scriptLine.DubbedUrl = *dubbingItem.DubbedUrl
				scriptLine.DubbedDuration = int32(*dubbingItem.DubbedDuration)
			}
			lineList = append(lineList, scriptLine)
		}
	}
	response.OkWithDetailed(response.PageResult{
		List:     lineList,
		Total:    total,
		Page:     pageInfo.Page,
		PageSize: pageInfo.PageSize,
	}, "获取成功", c)
}

// GetLinePublic 不需要鉴权的lines表接口
// @Tags Line
// @Summary 不需要鉴权的lines表接口
// @Accept application/json
// @Produce application/json
// @Success 200 {object} response.Response{data=object,msg=string} "获取成功"
// @Router /line/getLinePublic [get]
func (lineApi *LineApi) GetLinePublic(c *gin.Context) {
	// 创建业务用Context
	ctx := c.Request.Context()

	// 此接口不需要鉴权
	// 示例为返回了一个固定的消息接口，一般本接口用于C端服务，需要自己实现业务逻辑
	lineService.GetLinePublic(ctx)
	response.OkWithDetailed(gin.H{
		"info": "不需要鉴权的lines表接口信息",
	}, "获取成功", c)
}

// ReDubbing 台词重新配音
// @Tags Line
// @Summary 台词重新配音
// @Security ApiKeyAuth
// @Accept application/json
// @Produce application/json
// @Param data body scripts.Line true "台词重新配音"
// @Success 200 {object} response.Response{msg=string} "提交成功"
// @Router /line/reDubbing [post]
func (lineApi *LineApi) ReDubbing(c *gin.Context) {
	// 创建业务用Context
	ctx := c.Request.Context()

	id := c.Query("id")
	err := lineService.ReDubbing(ctx, id)
	if err != nil {
		global.GVA_LOG.Error("删除失败!", zap.Error(err))
		response.FailWithMessage("删除失败:"+err.Error(), c)
		return
	}
	response.OkWithMessage("删除成功", c)
}
