package scripts

import (
	"github.com/flipped-aurora/gin-vue-admin/server/global"
	"github.com/flipped-aurora/gin-vue-admin/server/model/common/response"
	"github.com/flipped-aurora/gin-vue-admin/server/model/scripts"
	scriptsReq "github.com/flipped-aurora/gin-vue-admin/server/model/scripts/request"
	"github.com/flipped-aurora/gin-vue-admin/server/utils"
	"github.com/gin-gonic/gin"
	"go.uber.org/zap"
	"new-gitlab.xunlei.cn/vcproject/backends/baselib/logger"
	"new-gitlab.xunlei.cn/vcproject/backends/baselib/util"
	"new-gitlab.xunlei.cn/vcproject/backends/proto/api/svcscript"
	"new-gitlab.xunlei.cn/vcproject/backends/proto/svcmgr"
)

type DubbingApi struct{}

// CreateDubbing 创建dubbings表
// @Tags Dubbing
// @Summary 创建dubbings表
// @Security ApiKeyAuth
// @Accept application/json
// @Produce application/json
// @Param data body scripts.Dubbing true "创建dubbings表"
// @Success 200 {object} response.Response{msg=string} "创建成功"
// @Router /dubbing/createDubbing [post]
func (dubbingApi *DubbingApi) CreateDubbing(c *gin.Context) {
	// 创建业务用Context
	ctx := c.Request.Context()

	var dubbing scripts.Dubbing
	err := c.ShouldBindJSON(&dubbing)
	if err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}
	err = dubbingService.CreateDubbing(ctx, &dubbing)
	if err != nil {
		global.GVA_LOG.Error("创建失败!", zap.Error(err))
		response.FailWithMessage("创建失败:"+err.Error(), c)
		return
	}
	response.OkWithMessage("创建成功", c)
}

// DeleteDubbing 删除dubbings表
// @Tags Dubbing
// @Summary 删除dubbings表
// @Security ApiKeyAuth
// @Accept application/json
// @Produce application/json
// @Param data body scripts.Dubbing true "删除dubbings表"
// @Success 200 {object} response.Response{msg=string} "删除成功"
// @Router /dubbing/deleteDubbing [delete]
func (dubbingApi *DubbingApi) DeleteDubbing(c *gin.Context) {
	// 创建业务用Context
	ctx := c.Request.Context()

	id := c.Query("id")
	err := dubbingService.DeleteDubbing(ctx, id)
	if err != nil {
		global.GVA_LOG.Error("删除失败!", zap.Error(err))
		response.FailWithMessage("删除失败:"+err.Error(), c)
		return
	}
	response.OkWithMessage("删除成功", c)
}

// DeleteDubbingByIds 批量删除dubbings表
// @Tags Dubbing
// @Summary 批量删除dubbings表
// @Security ApiKeyAuth
// @Accept application/json
// @Produce application/json
// @Success 200 {object} response.Response{msg=string} "批量删除成功"
// @Router /dubbing/deleteDubbingByIds [delete]
func (dubbingApi *DubbingApi) DeleteDubbingByIds(c *gin.Context) {
	// 创建业务用Context
	ctx := c.Request.Context()

	ids := c.QueryArray("ids[]")
	err := dubbingService.DeleteDubbingByIds(ctx, ids)
	if err != nil {
		global.GVA_LOG.Error("批量删除失败!", zap.Error(err))
		response.FailWithMessage("批量删除失败:"+err.Error(), c)
		return
	}
	response.OkWithMessage("批量删除成功", c)
}

// UpdateDubbing 更新dubbings表
// @Tags Dubbing
// @Summary 更新dubbings表
// @Security ApiKeyAuth
// @Accept application/json
// @Produce application/json
// @Param data body scripts.Dubbing true "更新dubbings表"
// @Success 200 {object} response.Response{msg=string} "更新成功"
// @Router /dubbing/updateDubbing [put]
func (dubbingApi *DubbingApi) UpdateDubbing(c *gin.Context) {
	// 从ctx获取标准context进行业务行为
	ctx := c.Request.Context()

	var dubbing scripts.Dubbing
	err := c.ShouldBindJSON(&dubbing)
	if err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}

	nowTicks := util.NowTimeMillis()
	dubbing.UpdatedAt = &nowTicks
	err = dubbingService.UpdateDubbing(ctx, dubbing)
	if err != nil {
		global.GVA_LOG.Error("更新失败!", zap.Error(err))
		response.FailWithMessage("更新失败:"+err.Error(), c)
		return
	}
	response.OkWithMessage("更新成功", c)
}

// FindDubbing 用id查询dubbings表
// @Tags Dubbing
// @Summary 用id查询dubbings表
// @Security ApiKeyAuth
// @Accept application/json
// @Produce application/json
// @Param id query int true "用id查询dubbings表"
// @Success 200 {object} response.Response{data=scripts.Dubbing,msg=string} "查询成功"
// @Router /dubbing/findDubbing [get]
func (dubbingApi *DubbingApi) FindDubbing(c *gin.Context) {
	// 创建业务用Context
	ctx := c.Request.Context()

	id := c.Query("id")
	redubbing, err := dubbingService.GetDubbing(ctx, id)
	if err != nil {
		global.GVA_LOG.Error("查询失败!", zap.Error(err))
		response.FailWithMessage("查询失败:"+err.Error(), c)
		return
	}

	// 处理音频URL
	if redubbing.DubbedUrl != nil && *redubbing.DubbedUrl != "" {
		fullUrl := utils.FillAudioUrl(*redubbing.DubbedUrl)
		redubbing.DubbedUrl = &fullUrl
	}
	if redubbing.OriginalUrl != nil && *redubbing.OriginalUrl != "" {
		fullUrl := utils.FillAudioUrl(*redubbing.OriginalUrl)
		redubbing.OriginalUrl = &fullUrl
	}

	response.OkWithDetailed(gin.H{
		"redubbing": redubbing,
	}, "查询成功", c)
}

// GetDubbingList 分页获取dubbings表列表
// @Tags Dubbing
// @Summary 分页获取dubbings表列表
// @Security ApiKeyAuth
// @Accept application/json
// @Produce application/json
// @Param data query scriptsReq.DubbingSearch true "分页获取dubbings表列表"
// @Success 200 {object} response.Response{data=response.PageResult,msg=string} "获取成功"
// @Router /dubbing/getDubbingList [get]
func (dubbingApi *DubbingApi) GetDubbingList(c *gin.Context) {
	// 创建业务用Context
	ctx := c.Request.Context()

	var pageInfo scriptsReq.DubbingSearch
	err := c.ShouldBindQuery(&pageInfo)
	if err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}
	list, total, err := dubbingService.GetDubbingInfoList(ctx, pageInfo)
	if err != nil {
		global.GVA_LOG.Error("获取失败!", zap.Error(err))
		response.FailWithMessage("获取失败:"+err.Error(), c)
		return
	}

	// 处理音频URL
	for _, dubbing := range list {
		if dubbing.DubbedUrl != nil && *dubbing.DubbedUrl != "" {
			fullUrl := utils.FillAudioUrl(*dubbing.DubbedUrl)
			dubbing.DubbedUrl = &fullUrl
		}
		if dubbing.OriginalUrl != nil && *dubbing.OriginalUrl != "" {
			fullUrl := utils.FillAudioUrl(*dubbing.OriginalUrl)
			dubbing.OriginalUrl = &fullUrl
		}
	}

	response.OkWithDetailed(response.PageResult{
		List:     list,
		Total:    total,
		Page:     pageInfo.Page,
		PageSize: pageInfo.PageSize,
	}, "获取成功", c)
}

// GetDubbingPublic 不需要鉴权的dubbings表接口
// @Tags Dubbing
// @Summary 不需要鉴权的dubbings表接口
// @Accept application/json
// @Produce application/json
// @Success 200 {object} response.Response{data=object,msg=string} "获取成功"
// @Router /dubbing/getDubbingPublic [get]
func (dubbingApi *DubbingApi) GetDubbingPublic(c *gin.Context) {
	// 创建业务用Context
	ctx := c.Request.Context()

	// 此接口不需要鉴权
	// 示例为返回了一个固定的消息接口，一般本接口用于C端服务，需要自己实现业务逻辑
	dubbingService.GetDubbingPublic(ctx)
	response.OkWithDetailed(gin.H{
		"info": "不需要鉴权的dubbings表接口信息",
	}, "获取成功", c)
}

// AuditDubbing 审核配音
// @Tags Dubbing
// @Summary 审核配音
// @Security ApiKeyAuth
// @Accept application/json
// @Produce application/json
// @Param data body map[string]interface{} true "审核配音"
// @Success 200 {object} response.Response{msg=string} "审核成功"
// @Router /dubbing/auditDubbing [put]
func (dubbingApi *DubbingApi) AuditDubbing(c *gin.Context) {
	// 创建业务用Context
	ctx := c.Request.Context()

	var req struct {
		ID           int64  `json:"id" binding:"required"`
		ReviewStatus int    `json:"review_status" binding:"required"`
		RejectReason string `json:"reject_reason"`
	}

	err := c.ShouldBindJSON(&req)
	if err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}

	// 调用svcscript服务进行审核
	resp, err := svcmgr.ScriptClient().AdminAuditDubbing(ctx, &svcscript.AdminAuditDubbingReq{
		DubbingId:    req.ID,
		ReviewStatus: svcscript.ReviewStatus(req.ReviewStatus),
		RejectReason: req.RejectReason,
	})
	if err != nil {
		logger.Errorf("AdminAuditDubbing failed, err: %v, dubbingId:%v, resp:%v", err, req.ID, resp)
		response.FailWithMessage("审核失败:"+err.Error(), c)
		return
	}

	response.OkWithMessage("审核成功", c)
}

// SetDubbingTop 设置配音置顶状态
// @Tags Dubbing
// @Summary 设置配音置顶状态
// @Security ApiKeyAuth
// @Accept application/json
// @Produce application/json
// @Param data body map[string]interface{} true "设置配音置顶状态"
// @Success 200 {object} response.Response{msg=string} "设置成功"
// @Router /dubbing/setDubbingTop [put]
func (dubbingApi *DubbingApi) SetDubbingTop(c *gin.Context) {
	// 创建业务用Context
	ctx := c.Request.Context()

	var req struct {
		ID    int64 `json:"id" binding:"required"`
		IsTop bool  `json:"is_top"`
	}

	err := c.ShouldBindJSON(&req)
	if err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}

	err = dubbingService.SetDubbingTop(ctx, req.ID, req.IsTop)
	if err != nil {
		global.GVA_LOG.Error("设置置顶状态失败!", zap.Error(err))
		response.FailWithMessage("设置置顶状态失败:"+err.Error(), c)
		return
	}

	response.OkWithMessage("设置置顶状态成功", c)
}

// ManualUploadDubbing 手动上传配音
// @Tags Dubbing
// @Summary 手动上传配音
// @Security ApiKeyAuth
// @Accept application/json
// @Produce application/json
// @Param data body scriptsReq.ManualUploadDubbingReq true "手动上传配音"
// @Success 200 {object} response.Response{msg=string} "上传成功"
// @Router /dubbing/manualUploadDubbing [post]
func (dubbingApi *DubbingApi) ManualUploadDubbing(c *gin.Context) {
	// 创建业务用Context
	ctx := c.Request.Context()

	var req scriptsReq.ManualUploadDubbingReq
	err := c.ShouldBindJSON(&req)
	if err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}

	err = dubbingService.ManualUploadDubbing(ctx, req)
	if err != nil {
		global.GVA_LOG.Error("手动上传配音失败!", zap.Error(err))
		response.FailWithMessage("手动上传配音失败:"+err.Error(), c)
		return
	}

	response.OkWithMessage("手动上传配音成功", c)
}
