package scripts

import (
	"github.com/flipped-aurora/gin-vue-admin/server/global"
	"github.com/flipped-aurora/gin-vue-admin/server/model/common/response"
	"github.com/flipped-aurora/gin-vue-admin/server/model/scripts"
	scriptsService "github.com/flipped-aurora/gin-vue-admin/server/service/scripts"
	"github.com/gin-gonic/gin"
	"go.uber.org/zap"
)

type RecordingConfigApi struct{}

var recordingConfigService = scriptsService.RecordingConfigServiceApp

// GetRecordingConfig 获取录音配置
// @Tags      RecordingConfig
// @Summary   获取录音配置
// @Security  ApiKeyAuth
// @accept    application/json
// @Produce   application/json
// @Param     data  body      scripts.GetRecordingConfigReq                                     true  "角色ID, 角色资源包ID, 场景"
// @Success   200   {object}  response.Response{data=scripts.RecordingConfigData,msg=string}  "获取录音配置成功"
// @Router    /recordingConfig/getRecordingConfig [post]
func (r *RecordingConfigApi) GetRecordingConfig(c *gin.Context) {
	var req scripts.GetRecordingConfigReq
	err := c.ShouldBindJSON(&req)
	if err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}

	// 验证请求参数
	err = recordingConfigService.ValidateRecordingConfigRequest(&req)
	if err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}

	// 获取录音配置
	config, err := recordingConfigService.GetRecordingConfig(c.Request.Context(), &req)
	if err != nil {
		global.GVA_LOG.Error("获取录音配置失败!", zap.Error(err))
		response.FailWithMessage("获取录音配置失败: "+err.Error(), c)
		return
	}

	// 转换为前端使用的数据格式
	configData := config.ToRecordingConfigData()
	
	response.OkWithDetailed(configData, "获取录音配置成功", c)
}

// GetRecordingConfigByLineId 根据台词ID获取录音配置
// @Tags      RecordingConfig
// @Summary   根据台词ID获取录音配置
// @Security  ApiKeyAuth
// @accept    application/json
// @Produce   application/json
// @Param     line_id   query     int64                                                        true  "台词ID"
// @Param     scene     query     string                                                       false "场景(默认为dubbing)"
// @Success   200       {object}  response.Response{data=scripts.RecordingConfigData,msg=string}  "获取录音配置成功"
// @Router    /recordingConfig/getRecordingConfigByLineId [get]
func (r *RecordingConfigApi) GetRecordingConfigByLineId(c *gin.Context) {
	lineIdStr := c.Query("line_id")
	scene := c.DefaultQuery("scene", "dubbing")
	
	if lineIdStr == "" {
		response.FailWithMessage("台词ID不能为空", c)
		return
	}

	// 根据台词ID获取台词信息
	line, err := lineService.GetLine(c.Request.Context(), lineIdStr)
	if err != nil {
		global.GVA_LOG.Error("获取台词信息失败!", zap.Error(err))
		response.FailWithMessage("获取台词信息失败: "+err.Error(), c)
		return
	}

	// 检查台词是否绑定了角色和资源包
	if line.CharacterId == nil || *line.CharacterId <= 0 {
		response.FailWithMessage("该台词未绑定角色", c)
		return
	}
	if line.CharacterAssetId == nil || *line.CharacterAssetId <= 0 {
		response.FailWithMessage("该台词未绑定角色资源包", c)
		return
	}

	// 构建录音配置请求
	req := scripts.GetRecordingConfigReq{
		CharacterID:      int64(*line.CharacterId),
		CharacterAssetID: int64(*line.CharacterAssetId),
		Scene:            scene,
	}

	// 获取录音配置
	config, err := recordingConfigService.GetRecordingConfig(c.Request.Context(), &req)
	if err != nil {
		global.GVA_LOG.Error("获取录音配置失败!", zap.Error(err))
		response.FailWithMessage("获取录音配置失败: "+err.Error(), c)
		return
	}

	// 转换为前端使用的数据格式
	configData := config.ToRecordingConfigData()
	
	response.OkWithDetailed(configData, "获取录音配置成功", c)
}
