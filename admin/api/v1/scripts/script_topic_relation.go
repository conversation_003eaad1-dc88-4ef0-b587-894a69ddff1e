package scripts

import (
	"fmt"
	"github.com/flipped-aurora/gin-vue-admin/server/global"
	"github.com/flipped-aurora/gin-vue-admin/server/model/common/response"
	"github.com/flipped-aurora/gin-vue-admin/server/model/scripts"
	scriptsReq "github.com/flipped-aurora/gin-vue-admin/server/model/scripts/request"
	"github.com/gin-gonic/gin"
	"go.uber.org/zap"
)

type ScriptTopicRelationApi struct{}

// CreateScriptTopicRelation 创建scriptTopicRelation表
// @Tags ScriptTopicRelation
// @Summary 创建scriptTopicRelation表
// @Security ApiKeyAuth
// @Accept application/json
// @Produce application/json
// @Param data body scripts.ScriptTopicRelation true "创建scriptTopicRelation表"
// @Success 200 {object} response.Response{msg=string} "创建成功"
// @Router /scriptTopicRelation/createScriptTopicRelation [post]
func (scriptTopicRelationApi *ScriptTopicRelationApi) CreateScriptTopicRelation(c *gin.Context) {
	// 创建业务用Context
	ctx := c.Request.Context()

	var scriptTopicRelation scripts.ScriptTopicRelation
	err := c.ShouldBindJSON(&scriptTopicRelation)
	if err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}
	err = scriptTopicRelationService.CreateScriptTopicRelation(ctx, &scriptTopicRelation)
	if err != nil {
		global.GVA_LOG.Error("创建失败!", zap.Error(err))
		response.FailWithMessage("创建失败:"+err.Error(), c)
		return
	}
	response.OkWithMessage("创建成功", c)
}

// DeleteScriptTopicRelation 删除scriptTopicRelation表
// @Tags ScriptTopicRelation
// @Summary 删除scriptTopicRelation表
// @Security ApiKeyAuth
// @Accept application/json
// @Produce application/json
// @Param data body scripts.ScriptTopicRelation true "删除scriptTopicRelation表"
// @Success 200 {object} response.Response{msg=string} "删除成功"
// @Router /scriptTopicRelation/deleteScriptTopicRelation [delete]
func (scriptTopicRelationApi *ScriptTopicRelationApi) DeleteScriptTopicRelation(c *gin.Context) {
	// 创建业务用Context
	ctx := c.Request.Context()

	id := c.Query("id")
	err := scriptTopicRelationService.DeleteScriptTopicRelation(ctx, id)
	if err != nil {
		global.GVA_LOG.Error("删除失败!", zap.Error(err))
		response.FailWithMessage("删除失败:"+err.Error(), c)
		return
	}
	response.OkWithMessage("删除成功", c)
}

// DeleteScriptTopicRelationByIds 批量删除scriptTopicRelation表
// @Tags ScriptTopicRelation
// @Summary 批量删除scriptTopicRelation表
// @Security ApiKeyAuth
// @Accept application/json
// @Produce application/json
// @Success 200 {object} response.Response{msg=string} "批量删除成功"
// @Router /scriptTopicRelation/deleteScriptTopicRelationByIds [delete]
func (scriptTopicRelationApi *ScriptTopicRelationApi) DeleteScriptTopicRelationByIds(c *gin.Context) {
	// 创建业务用Context
	ctx := c.Request.Context()

	ids := c.QueryArray("ids[]")
	err := scriptTopicRelationService.DeleteScriptTopicRelationByIds(ctx, ids)
	if err != nil {
		global.GVA_LOG.Error("批量删除失败!", zap.Error(err))
		response.FailWithMessage("批量删除失败:"+err.Error(), c)
		return
	}
	response.OkWithMessage("批量删除成功", c)
}

// UpdateScriptTopicRelation 更新scriptTopicRelation表
// @Tags ScriptTopicRelation
// @Summary 更新scriptTopicRelation表
// @Security ApiKeyAuth
// @Accept application/json
// @Produce application/json
// @Param data body scripts.ScriptTopicRelation true "更新scriptTopicRelation表"
// @Success 200 {object} response.Response{msg=string} "更新成功"
// @Router /scriptTopicRelation/updateScriptTopicRelation [put]
func (scriptTopicRelationApi *ScriptTopicRelationApi) UpdateScriptTopicRelation(c *gin.Context) {
	// 从ctx获取标准context进行业务行为
	ctx := c.Request.Context()

	var scriptTopicRelation scripts.ScriptTopicRelation
	err := c.ShouldBindJSON(&scriptTopicRelation)
	if err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}
	// 注意：ScriptTopicRelation模型没有UpdatedAt字段，无需设置时间戳
	err = scriptTopicRelationService.UpdateScriptTopicRelation(ctx, scriptTopicRelation)
	if err != nil {
		global.GVA_LOG.Error("更新失败!", zap.Error(err))
		response.FailWithMessage("更新失败:"+err.Error(), c)
		return
	}
	response.OkWithMessage("更新成功", c)
}

// FindScriptTopicRelation 用id查询scriptTopicRelation表
// @Tags ScriptTopicRelation
// @Summary 用id查询scriptTopicRelation表
// @Security ApiKeyAuth
// @Accept application/json
// @Produce application/json
// @Param id query int true "用id查询scriptTopicRelation表"
// @Success 200 {object} response.Response{data=scripts.ScriptTopicRelation,msg=string} "查询成功"
// @Router /scriptTopicRelation/findScriptTopicRelation [get]
func (scriptTopicRelationApi *ScriptTopicRelationApi) FindScriptTopicRelation(c *gin.Context) {
	// 创建业务用Context
	ctx := c.Request.Context()

	id := c.Query("id")
	rescriptTopicRelation, err := scriptTopicRelationService.GetScriptTopicRelation(ctx, id)
	if err != nil {
		global.GVA_LOG.Error("查询失败!", zap.Error(err))
		response.FailWithMessage("查询失败:"+err.Error(), c)
		return
	}
	response.OkWithData(rescriptTopicRelation, c)
}

// GetScriptTopicRelationList 分页获取scriptTopicRelation表列表
// @Tags ScriptTopicRelation
// @Summary 分页获取scriptTopicRelation表列表
// @Security ApiKeyAuth
// @Accept application/json
// @Produce application/json
// @Param data query scriptsReq.ScriptTopicRelationSearch true "分页获取scriptTopicRelation表列表"
// @Success 200 {object} response.Response{data=response.PageResult,msg=string} "获取成功"
// @Router /scriptTopicRelation/getScriptTopicRelationList [get]
func (scriptTopicRelationApi *ScriptTopicRelationApi) GetScriptTopicRelationList(c *gin.Context) {
	// 创建业务用Context
	ctx := c.Request.Context()

	var pageInfo scriptsReq.ScriptTopicRelationSearch
	err := c.ShouldBindQuery(&pageInfo)
	if err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}
	list, total, err := scriptTopicRelationService.GetScriptTopicRelationInfoList(ctx, pageInfo)
	if err != nil {
		global.GVA_LOG.Error("获取失败!", zap.Error(err))
		response.FailWithMessage("获取失败:"+err.Error(), c)
		return
	}

	for _, item := range list {
		topicId := fmt.Sprintf("%d", *item.TopicId)
		topic, err := topicService.GetTopic(ctx, topicId)
		if err != nil {
			global.GVA_LOG.Error("topic获取失败!", zap.Error(err))
		}
		if topic.Name != nil {
			item.TopicName = *topic.Name
		}
	}
	response.OkWithDetailed(response.PageResult{
		List:     list,
		Total:    total,
		Page:     pageInfo.Page,
		PageSize: pageInfo.PageSize,
	}, "获取成功", c)
}

// GetScriptTopicRelationPublic 不需要鉴权的scriptTopicRelation表接口
// @Tags ScriptTopicRelation
// @Summary 不需要鉴权的scriptTopicRelation表接口
// @Accept application/json
// @Produce application/json
// @Success 200 {object} response.Response{data=object,msg=string} "获取成功"
// @Router /scriptTopicRelation/getScriptTopicRelationPublic [get]
func (scriptTopicRelationApi *ScriptTopicRelationApi) GetScriptTopicRelationPublic(c *gin.Context) {
	// 创建业务用Context
	ctx := c.Request.Context()

	// 此接口不需要鉴权
	// 示例为返回了一个固定的消息接口，一般本接口用于C端服务，需要自己实现业务逻辑
	scriptTopicRelationService.GetScriptTopicRelationPublic(ctx)
	response.OkWithDetailed(gin.H{
		"info": "不需要鉴权的scriptTopicRelation表接口信息",
	}, "获取成功", c)
}
