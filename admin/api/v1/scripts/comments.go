package scripts

import (
	"strconv"

	"github.com/flipped-aurora/gin-vue-admin/server/global"
	"github.com/flipped-aurora/gin-vue-admin/server/model/common/response"
	"github.com/flipped-aurora/gin-vue-admin/server/model/scripts"
	scriptsReq "github.com/flipped-aurora/gin-vue-admin/server/model/scripts/request"
	"github.com/flipped-aurora/gin-vue-admin/server/utils"
	"github.com/gin-gonic/gin"
	"go.uber.org/zap"
	"new-gitlab.xunlei.cn/vcproject/backends/baselib/errcode"
	"new-gitlab.xunlei.cn/vcproject/backends/baselib/logger"
	"new-gitlab.xunlei.cn/vcproject/backends/baselib/util"
	"new-gitlab.xunlei.cn/vcproject/backends/proto/api/svcscript"
	"new-gitlab.xunlei.cn/vcproject/backends/proto/svcmgr"
)

type CommentApi struct{}

// CreateComment 创建comments表
// @Tags Comment
// @Summary 创建comments表
// @Security ApiKeyAuth
// @Accept application/json
// @Produce application/json
// @Param data body scripts.Comment true "创建comments表"
// @Success 200 {object} response.Response{msg=string} "创建成功"
// @Router /comment/createComment [post]
func (commentApi *CommentApi) CreateComment(c *gin.Context) {
	// 创建业务用Context
	ctx := c.Request.Context()

	var comment scripts.Comment
	err := c.ShouldBindJSON(&comment)
	if err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}
	err = commentService.CreateComment(ctx, &comment)
	if err != nil {
		global.GVA_LOG.Error("创建失败!", zap.Error(err))
		response.FailWithMessage("创建失败:"+err.Error(), c)
		return
	}
	response.OkWithMessage("创建成功", c)
}

// DeleteComment 删除comments表
// @Tags Comment
// @Summary 删除comments表
// @Security ApiKeyAuth
// @Accept application/json
// @Produce application/json
// @Param data body scripts.Comment true "删除comments表"
// @Success 200 {object} response.Response{msg=string} "删除成功"
// @Router /comment/deleteComment [delete]
func (commentApi *CommentApi) DeleteComment(c *gin.Context) {
	// 创建业务用Context
	ctx := c.Request.Context()

	id := c.Query("id")
	err := commentService.DeleteComment(ctx, id)
	if err != nil {
		global.GVA_LOG.Error("删除失败!", zap.Error(err))
		response.FailWithMessage("删除失败:"+err.Error(), c)
		return
	}
	response.OkWithMessage("删除成功", c)
}

// DeleteCommentByIds 批量删除comments表
// @Tags Comment
// @Summary 批量删除comments表
// @Security ApiKeyAuth
// @Accept application/json
// @Produce application/json
// @Success 200 {object} response.Response{msg=string} "批量删除成功"
// @Router /comment/deleteCommentByIds [delete]
func (commentApi *CommentApi) DeleteCommentByIds(c *gin.Context) {
	// 创建业务用Context
	ctx := c.Request.Context()

	ids := c.QueryArray("ids[]")
	err := commentService.DeleteCommentByIds(ctx, ids)
	if err != nil {
		global.GVA_LOG.Error("批量删除失败!", zap.Error(err))
		response.FailWithMessage("批量删除失败:"+err.Error(), c)
		return
	}
	response.OkWithMessage("批量删除成功", c)
}

// UpdateComment 更新comments表
// @Tags Comment
// @Summary 更新comments表
// @Security ApiKeyAuth
// @Accept application/json
// @Produce application/json
// @Param data body scripts.Comment true "更新comments表"
// @Success 200 {object} response.Response{msg=string} "更新成功"
// @Router /comment/updateComment [put]
func (commentApi *CommentApi) UpdateComment(c *gin.Context) {
	// 从ctx获取标准context进行业务行为
	ctx := c.Request.Context()

	var comment scripts.Comment
	err := c.ShouldBindJSON(&comment)
	if err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}

	nowTicks := util.NowTimeMillis()
	comment.UpdatedAt = &nowTicks
	err = commentService.UpdateComment(ctx, comment)
	if err != nil {
		global.GVA_LOG.Error("更新失败!", zap.Error(err))
		response.FailWithMessage("更新失败:"+err.Error(), c)
		return
	}
	response.OkWithMessage("更新成功", c)
}

// FindComment 用id查询comments表
// @Tags Comment
// @Summary 用id查询comments表
// @Security ApiKeyAuth
// @Accept application/json
// @Produce application/json
// @Param id query int true "用id查询comments表"
// @Success 200 {object} response.Response{data=scripts.Comment,msg=string} "查询成功"
// @Router /comment/findComment [get]
func (commentApi *CommentApi) FindComment(c *gin.Context) {
	// 创建业务用Context
	ctx := c.Request.Context()

	id := c.Query("id")
	recomment, err := commentService.GetComment(ctx, id)
	if err != nil {
		global.GVA_LOG.Error("查询失败!", zap.Error(err))
		response.FailWithMessage("查询失败:"+err.Error(), c)
		return
	}
	if len(*recomment.VoiceUrl) > 0 {
		voiceUrl := utils.FillAudioUrl(*recomment.VoiceUrl)
		recomment.VoiceUrl = &voiceUrl
	}
	response.OkWithDetailed(gin.H{
		"recomment": recomment,
	}, "查询成功", c)
}

// GetCommentList 分页获取comments表列表
// @Tags Comment
// @Summary 分页获取comments表列表
// @Security ApiKeyAuth
// @Accept application/json
// @Produce application/json
// @Param data query scriptsReq.CommentSearch true "分页获取comments表列表"
// @Success 200 {object} response.Response{data=response.PageResult,msg=string} "获取成功"
// @Router /comment/getCommentList [get]
func (commentApi *CommentApi) GetCommentList(c *gin.Context) {
	// 创建业务用Context
	ctx := c.Request.Context()

	var pageInfo scriptsReq.CommentSearch
	err := c.ShouldBindQuery(&pageInfo)
	if err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}
	list, total, err := commentService.GetCommentInfoList(ctx, pageInfo)
	if err != nil {
		global.GVA_LOG.Error("获取失败!", zap.Error(err))
		response.FailWithMessage("获取失败:"+err.Error(), c)
		return
	}
	for _, item := range list {
		voiceUrl := utils.FillAudioUrl(*item.VoiceUrl)
		item.VoiceUrl = &voiceUrl
	}
	response.OkWithDetailed(response.PageResult{
		List:     list,
		Total:    total,
		Page:     pageInfo.Page,
		PageSize: pageInfo.PageSize,
	}, "获取成功", c)
}

// GetCommentPublic 不需要鉴权的comments表接口
// @Tags Comment
// @Summary 不需要鉴权的comments表接口
// @Accept application/json
// @Produce application/json
// @Success 200 {object} response.Response{data=object,msg=string} "获取成功"
// @Router /comment/getCommentPublic [get]
func (commentApi *CommentApi) GetCommentPublic(c *gin.Context) {
	// 创建业务用Context
	ctx := c.Request.Context()

	// 此接口不需要鉴权
	// 示例为返回了一个固定的消息接口，一般本接口用于C端服务，需要自己实现业务逻辑
	commentService.GetCommentPublic(ctx)
	response.OkWithDetailed(gin.H{
		"info": "不需要鉴权的comments表接口信息",
	}, "获取成功", c)
}

// AuditComment 审核评论
// @Tags Comment
// @Summary 审核评论
// @Security ApiKeyAuth
// @Accept application/json
// @Produce application/json
// @Param data body map[string]interface{} true "审核评论"
// @Success 200 {object} response.Response{msg=string} "审核成功"
// @Router /comment/auditComment [put]
func (commentApi *CommentApi) AuditComment(c *gin.Context) {
	// 创建业务用Context
	ctx := c.Request.Context()

	var req struct {
		ID           int64  `json:"id" binding:"required"`
		ReviewStatus int    `json:"review_status" binding:"required"`
		RejectReason string `json:"reject_reason"`
	}

	err := c.ShouldBindJSON(&req)
	if err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}

	// 调用svcscript服务进行审核
	resp, err := svcmgr.ScriptClient().AdminAuditComment(ctx, &svcscript.AdminAuditCommentReq{
		CommentId:    req.ID,
		ReviewStatus: svcscript.ReviewStatus(req.ReviewStatus),
		RejectReason: req.RejectReason,
	})
	if err != nil {
		logger.Errorf("AdminAuditComment failed, err: %v, commentId:%v, resp:%v", err, req.ID, resp)
		response.FailWithMessage("审核失败:"+err.Error(), c)
		return
	}

	response.OkWithMessage("审核成功", c)
}

// SetCommentTop 设置评论置顶状态
// @Tags Comment
// @Summary 设置评论置顶状态
// @Security ApiKeyAuth
// @Accept application/json
// @Produce application/json
// @Param data body map[string]interface{} true "设置评论置顶状态"
// @Success 200 {object} response.Response{msg=string} "设置成功"
// @Router /comment/setCommentTop [put]
func (commentApi *CommentApi) SetCommentTop(c *gin.Context) {
	// 创建业务用Context
	ctx := c.Request.Context()

	var req struct {
		ID    int64 `json:"id" binding:"required"`
		IsTop bool  `json:"is_top"`
	}

	err := c.ShouldBindJSON(&req)
	if err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}

	err = commentService.SetCommentTop(ctx, req.ID, req.IsTop)
	if err != nil {
		global.GVA_LOG.Error("设置置顶状态失败!", zap.Error(err))
		response.FailWithMessage("设置置顶状态失败:"+err.Error(), c)
		return
	}

	response.OkWithMessage("设置置顶状态成功", c)
}

// SetCommentTop 语音转文本
// @Tags Comment
// @Summary 语音转文本
// @Security ApiKeyAuth
// @Accept application/json
// @Produce application/json
// @Param data body map[string]interface{} true "语音转文本"
// @Success 200 {object} response.Response{msg=string} "设置成功"
// @Router /comment/SpeechToText [post]
func (commentApi *CommentApi) SpeechToText(c *gin.Context) {
	// 创建业务用Context
	ctx := c.Request.Context()

	var req struct {
		ID int64 `json:"id" binding:"required"`
	}

	err := c.ShouldBindJSON(&req)
	if err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}
	strID := strconv.FormatInt(req.ID, 10)
	comment, err := commentService.GetComment(ctx, strID)
	if err != nil {
		global.GVA_LOG.Error("SpeechToText failed!", zap.Error(err))
		response.FailWithMessage("SpeechToText failed:"+err.Error(), c)
		return
	}
	vResp, tmpErr := svcmgr.ScriptClient().GetCommentASR(ctx, &svcscript.GetCommentASRReq{UserId: *comment.UserId, CommentId: req.ID})
	if tmpErr != nil || errcode.NotOk(vResp) {
		if vResp.GetBase().GetCode() == errcode.ErrASRProcessing.Code {
			response.OkWithMessage("ok", c)
			return
		}
		global.GVA_LOG.Error("SpeechToText failed: ", zap.Error(tmpErr), zap.Any("vResp", util.JsonStr(vResp)))
		response.FailWithMessage("call Rpc failed", c)
		return
	}
	response.OkWithMessage("ok", c)
}
