package characters

import (
	"github.com/flipped-aurora/gin-vue-admin/server/global"
	"github.com/flipped-aurora/gin-vue-admin/server/model/characters"
	charactersReq "github.com/flipped-aurora/gin-vue-admin/server/model/characters/request"
	"github.com/flipped-aurora/gin-vue-admin/server/model/common/response"
	"github.com/gin-gonic/gin"
	"go.uber.org/zap"
	"new-gitlab.xunlei.cn/vcproject/backends/baselib/server/env"
	"new-gitlab.xunlei.cn/vcproject/backends/baselib/util"
	"strings"
)

type CharacterAssetsApi struct{}

// CreateCharacterAssets 创建characterAssets表
// @Tags CharacterAssets
// @Summary 创建characterAssets表
// @Security ApiKeyAuth
// @Accept application/json
// @Produce application/json
// @Param data body characters.CharacterAssets true "创建characterAssets表"
// @Success 200 {object} response.Response{msg=string} "创建成功"
// @Router /characterAssets/createCharacterAssets [post]
func (characterAssetsApi *CharacterAssetsApi) CreateCharacterAssets(c *gin.Context) {
	// 创建业务用Context
	ctx := c.Request.Context()

	var characterAssets characters.CharacterAssets
	err := c.ShouldBindJSON(&characterAssets)
	if err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}
	global.GVA_LOG.Info("CreateCharacterAssets = %+v", zap.Any("characterAssets", characterAssets))
	nowTicks := util.NowTimeMillis()
	if characterAssets.CreatedAt == nil {
		characterAssets.CreatedAt = &nowTicks
	}
	if characterAssets.UpdatedAt == nil {
		characterAssets.UpdatedAt = &nowTicks
	}
	err = characterAssetsService.CreateCharacterAssets(ctx, &characterAssets)
	if err != nil {
		global.GVA_LOG.Error("创建失败!", zap.Error(err))
		response.FailWithMessage("创建失败:"+err.Error(), c)
		return
	}
	response.OkWithMessage("创建成功", c)
}

// DeleteCharacterAssets 删除characterAssets表
// @Tags CharacterAssets
// @Summary 删除characterAssets表
// @Security ApiKeyAuth
// @Accept application/json
// @Produce application/json
// @Param data body characters.CharacterAssets true "删除characterAssets表"
// @Success 200 {object} response.Response{msg=string} "删除成功"
// @Router /characterAssets/deleteCharacterAssets [delete]
func (characterAssetsApi *CharacterAssetsApi) DeleteCharacterAssets(c *gin.Context) {
	// 创建业务用Context
	ctx := c.Request.Context()

	id := c.Query("id")
	err := characterAssetsService.DeleteCharacterAssets(ctx, id)
	if err != nil {
		global.GVA_LOG.Error("删除失败!", zap.Error(err))
		response.FailWithMessage("删除失败:"+err.Error(), c)
		return
	}
	response.OkWithMessage("删除成功", c)
}

// DeleteCharacterAssetsByIds 批量删除characterAssets表
// @Tags CharacterAssets
// @Summary 批量删除characterAssets表
// @Security ApiKeyAuth
// @Accept application/json
// @Produce application/json
// @Success 200 {object} response.Response{msg=string} "批量删除成功"
// @Router /characterAssets/deleteCharacterAssetsByIds [delete]
func (characterAssetsApi *CharacterAssetsApi) DeleteCharacterAssetsByIds(c *gin.Context) {
	// 创建业务用Context
	ctx := c.Request.Context()

	ids := c.QueryArray("ids[]")
	err := characterAssetsService.DeleteCharacterAssetsByIds(ctx, ids)
	if err != nil {
		global.GVA_LOG.Error("批量删除失败!", zap.Error(err))
		response.FailWithMessage("批量删除失败:"+err.Error(), c)
		return
	}
	response.OkWithMessage("批量删除成功", c)
}

// UpdateCharacterAssets 更新characterAssets表
// @Tags CharacterAssets
// @Summary 更新characterAssets表
// @Security ApiKeyAuth
// @Accept application/json
// @Produce application/json
// @Param data body characters.CharacterAssets true "更新characterAssets表"
// @Success 200 {object} response.Response{msg=string} "更新成功"
// @Router /characterAssets/updateCharacterAssets [put]
func (characterAssetsApi *CharacterAssetsApi) UpdateCharacterAssets(c *gin.Context) {
	// 从ctx获取标准context进行业务行为
	ctx := c.Request.Context()

	var characterAssets characters.CharacterAssets
	err := c.ShouldBindJSON(&characterAssets)
	if err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}
	// 设置更新时间戳（毫秒）
	nowTicks := util.NowTimeMillis()
	characterAssets.UpdatedAt = &nowTicks
	err = characterAssetsService.UpdateCharacterAssets(ctx, characterAssets)
	if err != nil {
		global.GVA_LOG.Error("更新失败!", zap.Error(err))
		response.FailWithMessage("更新失败:"+err.Error(), c)
		return
	}
	response.OkWithMessage("更新成功", c)
}

// FindCharacterAssets 用id查询characterAssets表
// @Tags CharacterAssets
// @Summary 用id查询characterAssets表
// @Security ApiKeyAuth
// @Accept application/json
// @Produce application/json
// @Param id query int true "用id查询characterAssets表"
// @Success 200 {object} response.Response{data=characters.CharacterAssets,msg=string} "查询成功"
// @Router /characterAssets/findCharacterAssets [get]
func (characterAssetsApi *CharacterAssetsApi) FindCharacterAssets(c *gin.Context) {
	// 创建业务用Context
	ctx := c.Request.Context()

	id := c.Query("id")
	recharacterAssets, err := characterAssetsService.GetCharacterAssets(ctx, id)
	if err != nil {
		global.GVA_LOG.Error("查询失败!", zap.Error(err))
		response.FailWithMessage("查询失败:"+err.Error(), c)
		return
	}
	response.OkWithData(recharacterAssets, c)
}

// GetCharacterAssetsList 分页获取characterAssets表列表
// @Tags CharacterAssets
// @Summary 分页获取characterAssets表列表
// @Security ApiKeyAuth
// @Accept application/json
// @Produce application/json
// @Param data query charactersReq.CharacterAssetsSearch true "分页获取characterAssets表列表"
// @Success 200 {object} response.Response{data=response.PageResult,msg=string} "获取成功"
// @Router /characterAssets/getCharacterAssetsList [get]
func (characterAssetsApi *CharacterAssetsApi) GetCharacterAssetsList(c *gin.Context) {
	// 创建业务用Context
	ctx := c.Request.Context()

	var pageInfo charactersReq.CharacterAssetsSearch
	err := c.ShouldBindQuery(&pageInfo)
	if err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}
	list, total, err := characterAssetsService.GetCharacterAssetsInfoList(ctx, pageInfo)
	if err != nil {
		global.GVA_LOG.Error("获取失败!", zap.Error(err))
		response.FailWithMessage("获取失败:"+err.Error(), c)
		return
	}
	for _, characterItem := range list {
		newSampleAudio := *characterItem.SampleAudio
		newSampleAudio = fillAudioUrl(newSampleAudio)
		characterItem.SampleAudio = &newSampleAudio

		avatarUrl := *characterItem.AvatarUrl
		avatarUrl = fillImageUrl(avatarUrl)
		characterItem.AvatarUrl = &avatarUrl

		backgroundUrl := *characterItem.BackgroundUrl
		backgroundUrl = fillImageUrl(backgroundUrl)
		characterItem.BackgroundUrl = &backgroundUrl

		animatedUrl := *characterItem.AnimatedUrl
		animatedUrl = fillImageUrl(animatedUrl)
		characterItem.AnimatedUrl = &animatedUrl

	}

	response.OkWithDetailed(response.PageResult{
		List:     list,
		Total:    total,
		Page:     pageInfo.Page,
		PageSize: pageInfo.PageSize,
	}, "获取成功", c)
}

// UpdatePresetStatus 更新预设状态
// @Tags CharacterAssets
// @Summary 更新预设状态
// @Security ApiKeyAuth
// @Accept application/json
// @Produce application/json
// @Param data body characters.CharacterAssets true "更新characterAssets表"
// @Success 200 {object} response.Response{msg=string} "更新成功"
// @Router /characterAssets/UpdatePresetStatus [put]
func (characterAssetsApi *CharacterAssetsApi) UpdatePresetStatus(c *gin.Context) {
	// 从ctx获取标准context进行业务行为
	ctx := c.Request.Context()

	var updatePresetStatus charactersReq.UpdatePresetStatus
	err := c.ShouldBindJSON(&updatePresetStatus)
	if err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}
	err = characterAssetsService.UpdatePresetStatus(ctx, updatePresetStatus)
	if err != nil {
		global.GVA_LOG.Error("更新失败!", zap.Error(err))
		response.FailWithMessage("更新失败:"+err.Error(), c)
		return
	}
	response.OkWithMessage("更新成功", c)
}

func fillAudioUrl(audioUrl string) string {
	if len(strings.TrimSpace(audioUrl)) == 0 {
		return ""
	}
	if !strings.HasPrefix(audioUrl, "http") {
		if env.IsProd() {
			if strings.HasPrefix(audioUrl, "/") {
				audioUrl = "https://audio.voicelives.com" + audioUrl
			} else {
				audioUrl = "https://audio.voicelives.com/" + audioUrl
			}
		} else {
			if strings.HasPrefix(audioUrl, "/") {
				audioUrl = "https://data-test.voicelives.com" + audioUrl
			} else {
				audioUrl = "https://data-test.voicelives.com/" + audioUrl
			}
		}
	}
	return audioUrl
}

func fillImageUrl(imageUrl string) string {
	if len(strings.TrimSpace(imageUrl)) == 0 {
		return ""
	}
	if !strings.HasPrefix(imageUrl, "http") {
		if env.IsProd() {
			if strings.HasPrefix(imageUrl, "/") {
				imageUrl = "https://image.voicelives.com" + imageUrl
			} else {
				imageUrl = "https://image.voicelives.com/" + imageUrl
			}
		} else {
			if strings.HasPrefix(imageUrl, "/") {
				imageUrl = "https://data-test.voicelives.com" + imageUrl
			} else {
				imageUrl = "https://data-test.voicelives.com/" + imageUrl
			}
		}
	}
	return imageUrl
}
