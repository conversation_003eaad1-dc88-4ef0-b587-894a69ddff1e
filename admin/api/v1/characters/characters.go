package characters

import (
	"github.com/flipped-aurora/gin-vue-admin/server/global"
	"github.com/flipped-aurora/gin-vue-admin/server/model/characters"
	charactersReq "github.com/flipped-aurora/gin-vue-admin/server/model/characters/request"
	"github.com/flipped-aurora/gin-vue-admin/server/model/common/response"
	"github.com/gin-gonic/gin"
	"go.uber.org/zap"
	"new-gitlab.xunlei.cn/vcproject/backends/baselib/util"
)

type CharacterApi struct{}

// CreateCharacter 创建characters表
// @Tags Character
// @Summary 创建characters表
// @Security ApiKeyAuth
// @Accept application/json
// @Produce application/json
// @Param data body characters.Character true "创建characters表"
// @Success 200 {object} response.Response{msg=string} "创建成功"
// @Router /character/createCharacter [post]
func (characterApi *CharacterApi) CreateCharacter(c *gin.Context) {
	// 创建业务用Context
	ctx := c.Request.Context()

	var character characters.Character
	err := c.ShouldBindJSON(&character)
	if err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}
	nowTicks := util.NowTimeMillis()
	character.CreatedAt = &nowTicks
	character.UpdatedAt = &nowTicks

	// 如果试听音频为空，设置默认值为空字符串
	if character.PreviewAudio == nil {
		emptyString := ""
		character.PreviewAudio = &emptyString
	}

	err = characterService.CreateCharacter(ctx, &character)
	if err != nil {
		global.GVA_LOG.Error("创建失败!", zap.Error(err))
		response.FailWithMessage("创建失败:"+err.Error(), c)
		return
	}
	response.OkWithMessage("创建成功", c)
}

// DeleteCharacter 删除characters表
// @Tags Character
// @Summary 删除characters表
// @Security ApiKeyAuth
// @Accept application/json
// @Produce application/json
// @Param data body characters.Character true "删除characters表"
// @Success 200 {object} response.Response{msg=string} "删除成功"
// @Router /character/deleteCharacter [delete]
func (characterApi *CharacterApi) DeleteCharacter(c *gin.Context) {
	// 创建业务用Context
	ctx := c.Request.Context()

	id := c.Query("id")
	err := characterService.DeleteCharacter(ctx, id)
	if err != nil {
		global.GVA_LOG.Error("删除失败!", zap.Error(err))
		response.FailWithMessage("删除失败:"+err.Error(), c)
		return
	}
	response.OkWithMessage("删除成功", c)
}

// DeleteCharacterByIds 批量删除characters表
// @Tags Character
// @Summary 批量删除characters表
// @Security ApiKeyAuth
// @Accept application/json
// @Produce application/json
// @Success 200 {object} response.Response{msg=string} "批量删除成功"
// @Router /character/deleteCharacterByIds [delete]
func (characterApi *CharacterApi) DeleteCharacterByIds(c *gin.Context) {
	// 创建业务用Context
	ctx := c.Request.Context()

	ids := c.QueryArray("ids[]")
	err := characterService.DeleteCharacterByIds(ctx, ids)
	if err != nil {
		global.GVA_LOG.Error("批量删除失败!", zap.Error(err))
		response.FailWithMessage("批量删除失败:"+err.Error(), c)
		return
	}
	response.OkWithMessage("批量删除成功", c)
}

// UpdateCharacter 更新characters表
// @Tags Character
// @Summary 更新characters表
// @Security ApiKeyAuth
// @Accept application/json
// @Produce application/json
// @Param data body characters.Character true "更新characters表"
// @Success 200 {object} response.Response{msg=string} "更新成功"
// @Router /character/updateCharacter [put]
func (characterApi *CharacterApi) UpdateCharacter(c *gin.Context) {
	// 从ctx获取标准context进行业务行为
	ctx := c.Request.Context()

	var character characters.Character
	err := c.ShouldBindJSON(&character)
	if err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}
	nowTicks := util.NowTimeMillis()
	character.UpdatedAt = &nowTicks

	// 如果试听音频为空，设置默认值为空字符串
	if character.PreviewAudio == nil {
		emptyString := ""
		character.PreviewAudio = &emptyString
	}

	err = characterService.UpdateCharacter(ctx, character)
	if err != nil {
		global.GVA_LOG.Error("更新失败!", zap.Error(err))
		response.FailWithMessage("更新失败:"+err.Error(), c)
		return
	}
	response.OkWithMessage("更新成功", c)
}

// FindCharacter 用id查询characters表
// @Tags Character
// @Summary 用id查询characters表
// @Security ApiKeyAuth
// @Accept application/json
// @Produce application/json
// @Param id query int true "用id查询characters表"
// @Success 200 {object} response.Response{data=characters.Character,msg=string} "查询成功"
// @Router /character/findCharacter [get]
func (characterApi *CharacterApi) FindCharacter(c *gin.Context) {
	// 创建业务用Context
	ctx := c.Request.Context()

	id := c.Query("id")
	recharacter, err := characterService.GetCharacter(ctx, id)
	if err != nil {
		global.GVA_LOG.Error("查询失败!", zap.Error(err))
		response.FailWithMessage("查询失败:"+err.Error(), c)
		return
	}
	response.OkWithData(recharacter, c)
}

// GetCharacterList 分页获取characters表列表
// @Tags Character
// @Summary 分页获取characters表列表
// @Security ApiKeyAuth
// @Accept application/json
// @Produce application/json
// @Param data query charactersReq.CharacterSearch true "分页获取characters表列表"
// @Success 200 {object} response.Response{data=response.PageResult,msg=string} "获取成功"
// @Router /character/getCharacterList [get]
func (characterApi *CharacterApi) GetCharacterList(c *gin.Context) {
	// 创建业务用Context
	ctx := c.Request.Context()

	var pageInfo charactersReq.CharacterSearch
	err := c.ShouldBindQuery(&pageInfo)
	if err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}
	list, total, err := characterService.GetCharacterInfoList(ctx, pageInfo)
	if err != nil {
		global.GVA_LOG.Error("获取失败!", zap.Error(err))
		response.FailWithMessage("获取失败:"+err.Error(), c)
		return
	}
	response.OkWithDetailed(response.PageResult{
		List:     list,
		Total:    total,
		Page:     pageInfo.Page,
		PageSize: pageInfo.PageSize,
	}, "获取成功", c)
}

// GetRecommendedCharacterList 获取推荐角色列表
// @Tags Character
// @Summary 获取推荐角色列表
// @Security ApiKeyAuth
// @Accept application/json
// @Produce application/json
// @Param data query charactersReq.CharacterSearch true "获取推荐角色列表"
// @Success 200 {object} response.Response{data=response.PageResult,msg=string} "获取成功"
// @Router /character/getRecommendedCharacterList [get]
func (characterApi *CharacterApi) GetRecommendedCharacterList(c *gin.Context) {
	// 创建业务用Context
	ctx := c.Request.Context()

	var pageInfo charactersReq.CharacterSearch
	err := c.ShouldBindQuery(&pageInfo)
	if err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}
	list, total, err := characterService.GetRecommendedCharacters(ctx, pageInfo)
	if err != nil {
		global.GVA_LOG.Error("获取推荐角色失败!", zap.Error(err))
		response.FailWithMessage("获取推荐角色失败:"+err.Error(), c)
		return
	}
	response.OkWithDetailed(response.PageResult{
		List:     list,
		Total:    total,
		Page:     pageInfo.Page,
		PageSize: pageInfo.PageSize,
	}, "获取成功", c)
}

// SetCharacterRecommended 设置角色推荐状态
// @Tags Character
// @Summary 设置角色推荐状态
// @Security ApiKeyAuth
// @Accept application/json
// @Produce application/json
// @Param id query string true "角色ID"
// @Param is_recommended query bool true "是否推荐"
// @Success 200 {object} response.Response{msg=string} "设置成功"
// @Router /character/setCharacterRecommended [put]
func (characterApi *CharacterApi) SetCharacterRecommended(c *gin.Context) {
	// 创建业务用Context
	ctx := c.Request.Context()

	id := c.Query("id")
	isRecommendedStr := c.Query("is_recommended")
	isRecommended := isRecommendedStr == "true"

	err := characterService.SetCharacterRecommended(ctx, id, isRecommended)
	if err != nil {
		global.GVA_LOG.Error("设置推荐状态失败!", zap.Error(err))
		response.FailWithMessage("设置推荐状态失败:"+err.Error(), c)
		return
	}
	response.OkWithMessage("设置成功", c)
}

// BatchSetCharacterRecommended 批量设置角色推荐状态
// @Tags Character
// @Summary 批量设置角色推荐状态
// @Security ApiKeyAuth
// @Accept application/json
// @Produce application/json
// @Param ids query []string true "角色ID列表"
// @Param is_recommended query bool true "是否推荐"
// @Success 200 {object} response.Response{msg=string} "批量设置成功"
// @Router /character/batchSetCharacterRecommended [put]
func (characterApi *CharacterApi) BatchSetCharacterRecommended(c *gin.Context) {
	// 创建业务用Context
	ctx := c.Request.Context()

	ids := c.QueryArray("ids[]")
	isRecommendedStr := c.Query("is_recommended")
	isRecommended := isRecommendedStr == "true"

	err := characterService.BatchSetCharacterRecommended(ctx, ids, isRecommended)
	if err != nil {
		global.GVA_LOG.Error("批量设置推荐状态失败!", zap.Error(err))
		response.FailWithMessage("批量设置推荐状态失败:"+err.Error(), c)
		return
	}
	response.OkWithMessage("批量设置成功", c)
}
