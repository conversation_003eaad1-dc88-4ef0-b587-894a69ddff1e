package ips

import (
	"github.com/flipped-aurora/gin-vue-admin/server/utils"
	"new-gitlab.xunlei.cn/vcproject/backends/baselib/util"
	"time"

	"github.com/flipped-aurora/gin-vue-admin/server/global"
	"github.com/flipped-aurora/gin-vue-admin/server/model/common/response"
	"github.com/flipped-aurora/gin-vue-admin/server/model/ips"
	ipsReq "github.com/flipped-aurora/gin-vue-admin/server/model/ips/request"
	"github.com/gin-gonic/gin"
	"go.uber.org/zap"
)

type IpsApi struct{}

// CreateIps 创建ips表
// @Tags Ips
// @Summary 创建ips表
// @Security ApiKeyAuth
// @Accept application/json
// @Produce application/json
// @Param data body ips.Ips true "创建ips表"
// @Success 200 {object} response.Response{msg=string} "创建成功"
// @Router /Ip/createIps [post]
func (IpApi *IpsApi) CreateIps(c *gin.Context) {
	// 创建业务用Context
	ctx := c.Request.Context()

	var Ip ips.Ips
	err := c.ShouldBindJSON(&Ip)
	if err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}
	nowTicks := time.Now().Unix()
	Ip.CreatedAt = &nowTicks
	Ip.UpdatedAt = &nowTicks
	err = IpService.CreateIps(ctx, &Ip)
	if err != nil {
		global.GVA_LOG.Error("创建失败!", zap.Error(err))
		response.FailWithMessage("创建失败:"+err.Error(), c)
		return
	}
	response.OkWithMessage("创建成功", c)
}

// DeleteIps 删除ips表
// @Tags Ips
// @Summary 删除ips表
// @Security ApiKeyAuth
// @Accept application/json
// @Produce application/json
// @Param data body ips.Ips true "删除ips表"
// @Success 200 {object} response.Response{msg=string} "删除成功"
// @Router /Ip/deleteIp [delete]
func (IpApi *IpsApi) DeleteIp(c *gin.Context) {
	// 创建业务用Context
	ctx := c.Request.Context()

	id := c.Query("id")
	err := IpService.DeleteIp(ctx, id)
	if err != nil {
		global.GVA_LOG.Error("删除失败!", zap.Error(err))
		response.FailWithMessage("删除失败:"+err.Error(), c)
		return
	}
	response.OkWithMessage("删除成功", c)
}

// DeleteIpsByIds 批量删除ips表
// @Tags Ips
// @Summary 批量删除ips表
// @Security ApiKeyAuth
// @Accept application/json
// @Produce application/json
// @Success 200 {object} response.Response{msg=string} "批量删除成功"
// @Router /Ip/deleteIpsByIds [delete]
func (IpApi *IpsApi) DeleteIpsByIds(c *gin.Context) {
	// 创建业务用Context
	ctx := c.Request.Context()

	ids := c.QueryArray("ids[]")
	err := IpService.DeleteIpsByIds(ctx, ids)
	if err != nil {
		global.GVA_LOG.Error("批量删除失败!", zap.Error(err))
		response.FailWithMessage("批量删除失败:"+err.Error(), c)
		return
	}
	response.OkWithMessage("批量删除成功", c)
}

// UpdateIps 更新ips表
// @Tags Ips
// @Summary 更新ips表
// @Security ApiKeyAuth
// @Accept application/json
// @Produce application/json
// @Param data body ips.Ips true "更新ips表"
// @Success 200 {object} response.Response{msg=string} "更新成功"
// @Router /Ip/updateIps [put]
func (IpApi *IpsApi) UpdateIps(c *gin.Context) {
	// 从ctx获取标准context进行业务行为
	ctx := c.Request.Context()

	var Ip ips.Ips
	err := c.ShouldBindJSON(&Ip)
	if err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}
	nowTicks := util.NowTimeMillis()
	Ip.UpdatedAt = &nowTicks
	err = IpService.UpdateIps(ctx, Ip)
	if err != nil {
		global.GVA_LOG.Error("更新失败!", zap.Error(err))
		response.FailWithMessage("更新失败:"+err.Error(), c)
		return
	}
	response.OkWithMessage("更新成功", c)
}

// FindIps 用id查询ips表
// @Tags Ips
// @Summary 用id查询ips表
// @Security ApiKeyAuth
// @Accept application/json
// @Produce application/json
// @Param id query int true "用id查询ips表"
// @Success 200 {object} response.Response{data=ips.Ips,msg=string} "查询成功"
// @Router /Ip/findIps [get]
func (IpApi *IpsApi) FindIps(c *gin.Context) {
	// 创建业务用Context
	ctx := c.Request.Context()

	id := c.Query("id")
	reIp, err := IpService.GetIps(ctx, id)
	if err != nil {
		global.GVA_LOG.Error("查询失败!", zap.Error(err))
		response.FailWithMessage("查询失败:"+err.Error(), c)
		return
	}
	response.OkWithData(reIp, c)
}

// GetIpsList 分页获取ips表列表
// @Tags Ips
// @Summary 分页获取ips表列表
// @Security ApiKeyAuth
// @Accept application/json
// @Produce application/json
// @Param data query ipsReq.IpsSearch true "分页获取ips表列表"
// @Success 200 {object} response.Response{data=response.PageResult,msg=string} "获取成功"
// @Router /Ip/getIpsList [get]
func (IpApi *IpsApi) GetIpsList(c *gin.Context) {
	// 创建业务用Context
	ctx := c.Request.Context()

	var pageInfo ipsReq.IpsSearch
	err := c.ShouldBindQuery(&pageInfo)
	if err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}
	list, total, err := IpService.GetIpsInfoList(ctx, pageInfo)
	if err != nil {
		global.GVA_LOG.Error("获取失败!", zap.Error(err))
		response.FailWithMessage("获取失败:"+err.Error(), c)
		return
	}
	for _, ipItem := range list {
		logo := *ipItem.Logo
		logo = utils.FillImageUrl(logo)
		ipItem.Logo = &logo
	}
	response.OkWithDetailed(response.PageResult{
		List:     list,
		Total:    total,
		Page:     pageInfo.Page,
		PageSize: pageInfo.PageSize,
	}, "获取成功", c)
}

// GetAllIps 获取所有ips列表
// @Tags Ips
// @Summary 获取所有ips列表
// @Security ApiKeyAuth
// @Accept application/json
// @Produce application/json
// @Param data query ipsReq.IpsSearch true "分页获取ips表列表"
// @Success 200 {object} response.Response{data=response.PageResult,msg=string} "获取成功"
// @Router /Ip/getAllIps [get]
func (IpApi *IpsApi) GetAllIps(c *gin.Context) {
	// 创建业务用Context
	ctx := c.Request.Context()

	var pageInfo ipsReq.IpsSearch
	err := c.ShouldBindQuery(&pageInfo)
	if err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}
	list, err := IpService.GetAllIps(ctx, pageInfo)
	if err != nil {
		global.GVA_LOG.Error("获取失败!", zap.Error(err))
		response.FailWithMessage("获取失败:"+err.Error(), c)
		return
	}
	for _, ipItem := range list {
		logo := *ipItem.Logo
		logo = utils.FillImageUrl(logo)
		ipItem.Logo = &logo
	}
	total := len(list)
	response.OkWithDetailed(response.PageResult{
		List:     list,
		Total:    int64(total),
		Page:     pageInfo.Page,
		PageSize: pageInfo.PageSize,
	}, "获取成功", c)
}
