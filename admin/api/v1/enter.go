package v1

import (
	"github.com/flipped-aurora/gin-vue-admin/server/api/v1/characters"
	"github.com/flipped-aurora/gin-vue-admin/server/api/v1/example"
	"github.com/flipped-aurora/gin-vue-admin/server/api/v1/ips"
	"github.com/flipped-aurora/gin-vue-admin/server/api/v1/mymoments"
	"github.com/flipped-aurora/gin-vue-admin/server/api/v1/operations"
	"github.com/flipped-aurora/gin-vue-admin/server/api/v1/scripts"
	"github.com/flipped-aurora/gin-vue-admin/server/api/v1/system"
	"github.com/flipped-aurora/gin-vue-admin/server/api/v1/users"
)

var ApiGroupApp = new(ApiGroup)

type ApiGroup struct {
	SystemApiGroup     system.ApiGroup
	ExampleApiGroup    example.ApiGroup
	MymomentsApiGroup  mymoments.ApiGroup
	CharactersApiGroup characters.ApiGroup
	IpsApiGroup        ips.ApiGroup
	ScriptsApiGroup    scripts.ApiGroup
	UsersApiGroup      users.ApiGroup
	OperationsApiGroup operations.ApiGroup
}
