package example

import (
	"github.com/gin-gonic/gin"
)

type CallRpcApi struct {
}

// TestCallRpc
// @Tags      TestRpc
// @Summary   测试rpc调用
// @Security  ApiKeyAuth
// @accept    application/json
// @Produce   application/json
// @Param     data  query     example.TestCallRpcReq    true  "请求参数"
// @Success   200   {object}  response.Response{data=example.TestCallRpcResp,msg=string}  测试接口
// @Router    /test/test_call_rpc [post]
func (e *CallRpcApi) TestCallRpc(c *gin.Context) {
	//var req example.TestCallRpcReq
	//err := c.ShouldBindJSON(&req)
	//if err != nil {
	//	response.FailWithMessage(err.Error(), c)
	//	return
	//}
	//vResp, err := svcmgr.ActivityClient().GetDailyCheckIn(c.Request.Context(), &svcactivity.GetDailyCheckInReq{
	//	UserId: req.UserId,
	//})
	//global.GVA_LOG.Info("TestHello", zap.String("err", util.JsonStr(err)), zap.String("vResp", util.JsonStr(vResp)))
	//if err != nil || errcode.NotOk(vResp) {
	//	response.FailWithMessage(err.Error(), c)
	//	return
	//}
	//response.OkWithDetailed(example.TestCallRpcResp{Checked: vResp.Data.Checked}, errcode.ErrOK.Msg, c)
}
