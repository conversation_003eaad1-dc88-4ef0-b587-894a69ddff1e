package operations

import (
	"context"
	"strconv"
	"time"

	"github.com/flipped-aurora/gin-vue-admin/server/global"
	"github.com/flipped-aurora/gin-vue-admin/server/model/common/response"
	"github.com/flipped-aurora/gin-vue-admin/server/model/operations"
	operationsReq "github.com/flipped-aurora/gin-vue-admin/server/model/operations/request"
	"github.com/flipped-aurora/gin-vue-admin/server/utils"
	"github.com/gin-gonic/gin"
	"go.uber.org/zap"
)

type AppVersionApi struct{}

// CreateAppVersion 创建APP版本
// @Tags AppVersion
// @Summary 创建APP版本
// @Security ApiKeyAuth
// @Accept application/json
// @Produce application/json
// @Param data body operationsReq.AppVersionCreate true "创建APP版本"
// @Success 200 {object} response.Response{msg=string} "创建成功"
// @Router /appVersion/createAppVersion [post]
func (appVersionApi *AppVersionApi) CreateAppVersion(c *gin.Context) {
	ctx := context.Background()
	var req operationsReq.AppVersionCreate
	err := c.ShouldBindJSON(&req)
	if err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}

	// 获取当前用户信息
	claims, _ := utils.GetClaims(c)
	createdBy := claims.Username

	// 构建AppVersion对象
	now := time.Now().UnixMilli()
	appVersion := operations.AppVersion{
		Title:       &req.Title,
		PackageName: &req.PackageName,
		Version:     &req.Version,
		UpdateInfo:  req.UpdateInfo,
		DownloadURL: &req.DownloadURL,
		FileSize:    req.FileSize,
		FileMD5:     &req.FileMD5,
		MinVersion:  &req.MinVersion,
		ForceUpdate: &req.ForceUpdate,
		Platform:    &req.Platform,
		Channel:     &req.Channel,
		Remark:      &req.Remark,
		CreatedBy:   &createdBy,
		CreatedAt:   &now,
		UpdatedAt:   &now,
	}

	err = appVersionService.CreateAppVersion(ctx, &appVersion)
	if err != nil {
		global.GVA_LOG.Error("创建失败!", zap.Error(err))
		response.FailWithMessage("创建失败:"+err.Error(), c)
		return
	}
	response.OkWithMessage("创建成功", c)
}

// DeleteAppVersion 删除APP版本
// @Tags AppVersion
// @Summary 删除APP版本
// @Security ApiKeyAuth
// @Accept application/json
// @Produce application/json
// @Param data body operationsReq.AppVersionById true "删除APP版本"
// @Success 200 {object} response.Response{msg=string} "删除成功"
// @Router /appVersion/deleteAppVersion [delete]
func (appVersionApi *AppVersionApi) DeleteAppVersion(c *gin.Context) {
	ctx := context.Background()
	var req operationsReq.AppVersionById
	err := c.ShouldBindJSON(&req)
	if err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}

	err = appVersionService.DeleteAppVersion(ctx, req.ID)
	if err != nil {
		global.GVA_LOG.Error("删除失败!", zap.Error(err))
		response.FailWithMessage("删除失败:"+err.Error(), c)
		return
	}
	response.OkWithMessage("删除成功", c)
}

// DeleteAppVersionByIds 批量删除APP版本
// @Tags AppVersion
// @Summary 批量删除APP版本
// @Security ApiKeyAuth
// @Accept application/json
// @Produce application/json
// @Param data body operationsReq.AppVersionByIds true "批量删除APP版本"
// @Success 200 {object} response.Response{msg=string} "批量删除成功"
// @Router /appVersion/deleteAppVersionByIds [delete]
func (appVersionApi *AppVersionApi) DeleteAppVersionByIds(c *gin.Context) {
	ctx := context.Background()
	var req operationsReq.AppVersionByIds
	err := c.ShouldBindJSON(&req)
	if err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}

	err = appVersionService.DeleteAppVersionByIds(ctx, req.IDs)
	if err != nil {
		global.GVA_LOG.Error("批量删除失败!", zap.Error(err))
		response.FailWithMessage("批量删除失败:"+err.Error(), c)
		return
	}
	response.OkWithMessage("批量删除成功", c)
}

// UpdateAppVersion 更新APP版本
// @Tags AppVersion
// @Summary 更新APP版本
// @Security ApiKeyAuth
// @Accept application/json
// @Produce application/json
// @Param data body operationsReq.AppVersionUpdate true "更新APP版本"
// @Success 200 {object} response.Response{msg=string} "更新成功"
// @Router /appVersion/updateAppVersion [put]
func (appVersionApi *AppVersionApi) UpdateAppVersion(c *gin.Context) {
	ctx := context.Background()
	var req operationsReq.AppVersionUpdate
	err := c.ShouldBindJSON(&req)
	if err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}

	// 获取当前用户信息
	claims, _ := utils.GetClaims(c)
	updatedBy := claims.Username

	// 构建AppVersion对象
	now := time.Now().UnixMilli()
	appVersion := operations.AppVersion{
		ID:          &req.ID,
		Title:       &req.Title,
		PackageName: &req.PackageName,
		Version:     &req.Version,
		UpdateInfo:  req.UpdateInfo,
		DownloadURL: &req.DownloadURL,
		FileSize:    req.FileSize,
		FileMD5:     &req.FileMD5,
		MinVersion:  &req.MinVersion,
		ForceUpdate: &req.ForceUpdate,
		Platform:    &req.Platform,
		Channel:     &req.Channel,
		Remark:      &req.Remark,
		UpdatedBy:   &updatedBy,
		UpdatedAt:   &now,
	}

	err = appVersionService.UpdateAppVersion(ctx, appVersion)
	if err != nil {
		global.GVA_LOG.Error("更新失败!", zap.Error(err))
		response.FailWithMessage("更新失败:"+err.Error(), c)
		return
	}
	response.OkWithMessage("更新成功", c)
}

// FindAppVersion 根据ID获取APP版本
// @Tags AppVersion
// @Summary 根据ID获取APP版本
// @Security ApiKeyAuth
// @Accept application/json
// @Produce application/json
// @Param id query uint64 true "主键ID"
// @Success 200 {object} response.Response{data=operations.AppVersion,msg=string} "查询成功"
// @Router /appVersion/findAppVersion [get]
func (appVersionApi *AppVersionApi) FindAppVersion(c *gin.Context) {
	ctx := context.Background()
	idStr := c.Query("id")
	id, err := strconv.ParseUint(idStr, 10, 64)
	if err != nil {
		response.FailWithMessage("ID格式错误", c)
		return
	}

	appVersion, err := appVersionService.GetAppVersion(ctx, id)
	if err != nil {
		global.GVA_LOG.Error("查询失败!", zap.Error(err))
		response.FailWithMessage("查询失败:"+err.Error(), c)
		return
	}
	response.OkWithDetailed(gin.H{"reAppVersion": appVersion}, "查询成功", c)
}

// GetAppVersionList 分页获取APP版本列表
// @Tags AppVersion
// @Summary 分页获取APP版本列表
// @Security ApiKeyAuth
// @Accept application/json
// @Produce application/json
// @Param data query operationsReq.AppVersionSearch true "分页获取APP版本列表"
// @Success 200 {object} response.Response{data=response.PageResult,msg=string} "获取成功"
// @Router /appVersion/getAppVersionList [get]
func (appVersionApi *AppVersionApi) GetAppVersionList(c *gin.Context) {
	ctx := context.Background()
	var pageInfo operationsReq.AppVersionSearch
	err := c.ShouldBindQuery(&pageInfo)
	if err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}

	list, total, err := appVersionService.GetAppVersionInfoList(ctx, pageInfo)
	if err != nil {
		global.GVA_LOG.Error("获取失败!", zap.Error(err))
		response.FailWithMessage("获取失败:"+err.Error(), c)
		return
	}
	response.OkWithDetailed(response.PageResult{
		List:     list,
		Total:    total,
		Page:     pageInfo.Page,
		PageSize: pageInfo.PageSize,
	}, "获取成功", c)
}

// PublishAppVersion 发布APP版本
// @Tags AppVersion
// @Summary 发布APP版本
// @Security ApiKeyAuth
// @Accept application/json
// @Produce application/json
// @Param data body operationsReq.AppVersionPublish true "发布APP版本"
// @Success 200 {object} response.Response{msg=string} "发布成功"
// @Router /appVersion/publishAppVersion [post]
func (appVersionApi *AppVersionApi) PublishAppVersion(c *gin.Context) {
	ctx := context.Background()
	var req operationsReq.AppVersionPublish
	err := c.ShouldBindJSON(&req)
	if err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}

	err = appVersionService.PublishAppVersion(ctx, req.ID)
	if err != nil {
		global.GVA_LOG.Error("发布失败!", zap.Error(err))
		response.FailWithMessage("发布失败:"+err.Error(), c)
		return
	}
	response.OkWithMessage("发布成功", c)
}

// OfflineAppVersion 下线APP版本
// @Tags AppVersion
// @Summary 下线APP版本
// @Security ApiKeyAuth
// @Accept application/json
// @Produce application/json
// @Param data body operationsReq.AppVersionOffline true "下线APP版本"
// @Success 200 {object} response.Response{msg=string} "下线成功"
// @Router /appVersion/offlineAppVersion [post]
func (appVersionApi *AppVersionApi) OfflineAppVersion(c *gin.Context) {
	ctx := context.Background()
	var req operationsReq.AppVersionOffline
	err := c.ShouldBindJSON(&req)
	if err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}

	err = appVersionService.OfflineAppVersion(ctx, req.ID)
	if err != nil {
		global.GVA_LOG.Error("下线失败!", zap.Error(err))
		response.FailWithMessage("下线失败:"+err.Error(), c)
		return
	}
	response.OkWithMessage("下线成功", c)
}

// GetPlatformOptions 获取平台选项
// @Tags AppVersion
// @Summary 获取平台选项
// @Security ApiKeyAuth
// @Accept application/json
// @Produce application/json
// @Success 200 {object} response.Response{data=map[string]string,msg=string} "获取成功"
// @Router /appVersion/getPlatformOptions [get]
func (appVersionApi *AppVersionApi) GetPlatformOptions(c *gin.Context) {
	options := appVersionService.GetPlatformOptions()
	response.OkWithDetailed(gin.H{"options": options}, "获取成功", c)
}

// GetStatusOptions 获取状态选项
// @Tags AppVersion
// @Summary 获取状态选项
// @Security ApiKeyAuth
// @Accept application/json
// @Produce application/json
// @Success 200 {object} response.Response{data=map[string]string,msg=string} "获取成功"
// @Router /appVersion/getStatusOptions [get]
func (appVersionApi *AppVersionApi) GetStatusOptions(c *gin.Context) {
	options := appVersionService.GetStatusOptions()
	response.OkWithDetailed(gin.H{"options": options}, "获取成功", c)
}
