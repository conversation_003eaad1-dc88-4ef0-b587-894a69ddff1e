package mymoments

import (
	"github.com/flipped-aurora/gin-vue-admin/server/global"
	"github.com/flipped-aurora/gin-vue-admin/server/model/common/response"
	"github.com/flipped-aurora/gin-vue-admin/server/model/mymoments"
	mymomentsReq "github.com/flipped-aurora/gin-vue-admin/server/model/mymoments/request"
	"github.com/gin-gonic/gin"
	"go.uber.org/zap"
	"new-gitlab.xunlei.cn/vcproject/backends/baselib/util"
	"time"
)

type MomentsApi struct{}

// CreateMoments 创建moments表
// @Tags Moments
// @Summary 创建moments表
// @Security ApiKeyAuth
// @Accept application/json
// @Produce application/json
// @Param data body mymoments.Moments true "创建moments表"
// @Success 200 {object} response.Response{msg=string} "创建成功"
// @Router /moments/createMoments [post]
func (momentsApi *MomentsApi) CreateMoments(c *gin.Context) {
	// 创建业务用Context
	ctx := c.Request.Context()

	var moments mymoments.Moments
	err := c.ShouldBindJSON(&moments)
	if err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}
	nowTicks := time.Now().Unix()
	moments.CreatedAt = &nowTicks
	moments.UpdatedAt = &nowTicks
	err = momentsService.CreateMoments(ctx, &moments)
	if err != nil {
		global.GVA_LOG.Error("创建失败!", zap.Error(err))
		response.FailWithMessage("创建失败:"+err.Error(), c)
		return
	}
	response.OkWithMessage("创建成功", c)
}

// DeleteMoments 删除moments表
// @Tags Moments
// @Summary 删除moments表
// @Security ApiKeyAuth
// @Accept application/json
// @Produce application/json
// @Param data body mymoments.Moments true "删除moments表"
// @Success 200 {object} response.Response{msg=string} "删除成功"
// @Router /moments/deleteMoments [delete]
func (momentsApi *MomentsApi) DeleteMoments(c *gin.Context) {
	// 创建业务用Context
	response.FailWithMessage("暂不支持删除", c)
	// ctx := c.Request.Context()

	// id := c.Query("id")
	// err := momentsService.DeleteMoments(ctx, id)
	// if err != nil {
	// 	global.GVA_LOG.Error("删除失败!", zap.Error(err))
	// 	response.FailWithMessage("删除失败:"+err.Error(), c)
	// 	return
	// }
	// response.OkWithMessage("删除成功", c)
}

// DeleteMomentsByIds 批量删除moments表
// @Tags Moments
// @Summary 批量删除moments表
// @Security ApiKeyAuth
// @Accept application/json
// @Produce application/json
// @Success 200 {object} response.Response{msg=string} "批量删除成功"
// @Router /moments/deleteMomentsByIds [delete]
func (momentsApi *MomentsApi) DeleteMomentsByIds(c *gin.Context) {
	// 创建业务用Context
	response.FailWithMessage("暂不支持删除", c)
	//ctx := c.Request.Context()

	// ids := c.QueryArray("ids[]")
	// err := momentsService.DeleteMomentsByIds(ctx, ids)
	// if err != nil {
	// 	global.GVA_LOG.Error("批量删除失败!", zap.Error(err))
	// 	response.FailWithMessage("批量删除失败:"+err.Error(), c)
	// 	return
	// }
	// response.OkWithMessage("批量删除成功", c)
}

// UpdateMoments 更新moments表
// @Tags Moments
// @Summary 更新moments表
// @Security ApiKeyAuth
// @Accept application/json
// @Produce application/json
// @Param data body mymoments.Moments true "更新moments表"
// @Success 200 {object} response.Response{msg=string} "更新成功"
// @Router /moments/updateMoments [put]
func (momentsApi *MomentsApi) UpdateMoments(c *gin.Context) {
	// 从ctx获取标准context进行业务行为
	ctx := c.Request.Context()

	var moments mymoments.Moments
	err := c.ShouldBindJSON(&moments)
	if err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}
	nowTicks := util.NowTimeMillis()
	moments.UpdatedAt = &nowTicks
	err = momentsService.UpdateMoments(ctx, moments)
	if err != nil {
		global.GVA_LOG.Error("更新失败!", zap.Error(err))
		response.FailWithMessage("更新失败:"+err.Error(), c)
		return
	}
	response.OkWithMessage("更新成功", c)
}

// FindMoments 用id查询moments表
// @Tags Moments
// @Summary 用id查询moments表
// @Security ApiKeyAuth
// @Accept application/json
// @Produce application/json
// @Param id query int true "用id查询moments表"
// @Success 200 {object} response.Response{data=mymoments.Moments,msg=string} "查询成功"
// @Router /moments/findMoments [get]
func (momentsApi *MomentsApi) FindMoments(c *gin.Context) {
	// 创建业务用Context
	ctx := c.Request.Context()

	id := c.Query("id")
	remoments, err := momentsService.GetMoments(ctx, id)
	if err != nil {
		global.GVA_LOG.Error("查询失败!", zap.Error(err))
		response.FailWithMessage("查询失败:"+err.Error(), c)
		return
	}
	response.OkWithData(remoments, c)
}

// GetMomentsList 分页获取moments表列表
// @Tags Moments
// @Summary 分页获取moments表列表
// @Security ApiKeyAuth
// @Accept application/json
// @Produce application/json
// @Param data query mymomentsReq.MomentsSearch true "分页获取moments表列表"
// @Success 200 {object} response.Response{data=response.PageResult,msg=string} "获取成功"
// @Router /moments/getMomentsList [get]
func (momentsApi *MomentsApi) GetMomentsList(c *gin.Context) {
	// 创建业务用Context
	ctx := c.Request.Context()

	var pageInfo mymomentsReq.MomentsSearch
	err := c.ShouldBindQuery(&pageInfo)
	if err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}
	list, total, err := momentsService.GetMomentsInfoList(ctx, pageInfo)
	if err != nil {
		global.GVA_LOG.Error("获取失败!", zap.Error(err))
		response.FailWithMessage("获取失败:"+err.Error(), c)
		return
	}
	response.OkWithDetailed(response.PageResult{
		List:     list,
		Total:    total,
		Page:     pageInfo.Page,
		PageSize: pageInfo.PageSize,
	}, "获取成功", c)
}

// GetMomentsPublic 不需要鉴权的moments表接口
// @Tags Moments
// @Summary 不需要鉴权的moments表接口
// @Accept application/json
// @Produce application/json
// @Success 200 {object} response.Response{data=object,msg=string} "获取成功"
// @Router /moments/getMomentsPublic [get]
func (momentsApi *MomentsApi) GetMomentsPublic(c *gin.Context) {
	// 创建业务用Context
	ctx := c.Request.Context()

	// 此接口不需要鉴权
	// 示例为返回了一个固定的消息接口，一般本接口用于C端服务，需要自己实现业务逻辑
	momentsService.GetMomentsPublic(ctx)
	response.OkWithDetailed(gin.H{
		"info": "不需要鉴权的moments表接口信息",
	}, "获取成功", c)
}
