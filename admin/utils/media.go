package utils

import (
	"strings"

	"new-gitlab.xunlei.cn/vcproject/backends/baselib/server/env"
)

func FillImageUrl(imageUrl string) string {
	if len(strings.TrimSpace(imageUrl)) == 0 {
		return ""
	}
	if !strings.HasPrefix(imageUrl, "http") {
		if env.IsProd() {
			if strings.HasPrefix(imageUrl, "/") {
				imageUrl = "https://image.voicelives.com" + imageUrl
			} else {
				imageUrl = "https://image.voicelives.com/" + imageUrl
			}
		} else {
			if strings.HasPrefix(imageUrl, "/") {
				imageUrl = "https://data-test.voicelives.com" + imageUrl
			} else {
				imageUrl = "https://data-test.voicelives.com/" + imageUrl
			}
		}
	}
	return imageUrl
}

func FillAudioUrl(imageUrl string) string {
	if len(strings.TrimSpace(imageUrl)) == 0 {
		return ""
	}
	if !strings.HasPrefix(imageUrl, "http") {
		if env.IsProd() {
			if strings.HasPrefix(imageUrl, "/") {
				imageUrl = "https://audio.voicelives.com" + imageUrl
			} else {
				imageUrl = "https://audio.voicelives.com/" + imageUrl
			}
		} else {
			if strings.HasPrefix(imageUrl, "/") {
				imageUrl = "https://data-test.voicelives.com" + imageUrl
			} else {
				imageUrl = "https://data-test.voicelives.com/" + imageUrl
			}
		}
	}
	return imageUrl
}
