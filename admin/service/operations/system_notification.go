package operations

import (
	"context"
	"encoding/json"
	"errors"
	"fmt"
	"sync"
	"time"

	"github.com/flipped-aurora/gin-vue-admin/server/global"
	"github.com/flipped-aurora/gin-vue-admin/server/model/operations"
	"github.com/flipped-aurora/gin-vue-admin/server/model/operations/request"
	"github.com/flipped-aurora/gin-vue-admin/server/service/users"
	"github.com/flipped-aurora/gin-vue-admin/server/utils"
	"github.com/gin-gonic/gin"
	"go.uber.org/zap"
	"new-gitlab.xunlei.cn/vcproject/backends/baselib/util"
	"new-gitlab.xunlei.cn/vcproject/backends/proto/api/basemsgtransfer"
	"new-gitlab.xunlei.cn/vcproject/backends/proto/api/bizmisc"
	"new-gitlab.xunlei.cn/vcproject/backends/proto/api/svcchat"
	"new-gitlab.xunlei.cn/vcproject/backends/proto/svcmgr"
)

type SystemNotificationService struct {
	userService *users.UserService
}

const (
	OfficialSecretaryUserid = 1    // 小秘书用户ID
	BatchSize               = 1000 // 批量发送的批次大小
	MaxConcurrency          = 10   // 最大并发数
)

// SystemNotificationRecordResponse 系统通知记录响应
type SystemNotificationRecordResponse struct {
	ID              uint       `json:"ID"`
	CreatedAt       time.Time  `json:"CreatedAt"`
	UpdatedAt       time.Time  `json:"UpdatedAt"`
	Title           string     `json:"title"`
	Content         string     `json:"content"`
	ReceiverType    int32      `json:"receiver_type"`
	ReceiverTypeStr string     `json:"receiver_type_str"`
	ReceiverCount   int32      `json:"receiver_count"`
	SendStatus      int32      `json:"send_status"`
	SendStatusStr   string     `json:"send_status_str"`
	SuccessCount    int32      `json:"success_count"`
	FailureCount    int32      `json:"failure_count"`
	ErrorMessage    string     `json:"error_message"`
	SystemMsgData   string     `json:"system_msg_data"`
	SendStartTime   time.Time  `json:"send_start_time"`
	SendEndTime     *time.Time `json:"send_end_time"`
	SendDuration    int64      `json:"send_duration"`
	SendDurationStr string     `json:"send_duration_str"`
	OperatorId      uint       `json:"operator_id"`
	OperatorName    string     `json:"operator_name"`
}

// GetSystemNotificationRecordsResponse 获取系统通知记录列表响应
type GetSystemNotificationRecordsResponse struct {
	List     []SystemNotificationRecordResponse `json:"list"`
	Total    int64                              `json:"total"`
	Page     int                                `json:"page"`
	PageSize int                                `json:"pageSize"`
}

// JumpUrlOption 跳转地址选项
type JumpUrlOption struct {
	Label       string `json:"label"`       // 显示名称
	Value       string `json:"value"`       // schema地址值
	Description string `json:"description"` // 描述信息
	Params      string `json:"params"`      // 参数说明
}

// convertToResponse 转换为响应格式
func convertToResponse(record operations.SystemNotificationRecord) SystemNotificationRecordResponse {
	resp := SystemNotificationRecordResponse{
		ID:            record.ID,
		CreatedAt:     record.CreatedAt,
		UpdatedAt:     record.UpdatedAt,
		Title:         record.Title,
		Content:       record.Content,
		ReceiverType:  record.ReceiverType,
		ReceiverCount: record.ReceiverCount,
		SendStatus:    record.SendStatus,
		SuccessCount:  record.SuccessCount,
		FailureCount:  record.FailureCount,
		ErrorMessage:  record.ErrorMessage,
		SystemMsgData: record.SystemMsgData,
		SendStartTime: record.SendStartTime,
		SendEndTime:   record.SendEndTime,
		SendDuration:  record.SendDuration,
		OperatorId:    record.OperatorId,
		OperatorName:  record.OperatorName,
	}

	// 转换接收者类型
	switch record.ReceiverType {
	case operations.ReceiverTypeAll:
		resp.ReceiverTypeStr = "全部用户"
	case operations.ReceiverTypeSpecific:
		resp.ReceiverTypeStr = "指定用户"
	default:
		resp.ReceiverTypeStr = "未知"
	}

	// 转换发送状态
	switch record.SendStatus {
	case operations.SendStatusSending:
		resp.SendStatusStr = "发送中"
	case operations.SendStatusSuccess:
		resp.SendStatusStr = "发送成功"
	case operations.SendStatusFailed:
		resp.SendStatusStr = "发送失败"
	case operations.SendStatusPartialFailed:
		resp.SendStatusStr = "部分失败"
	default:
		resp.SendStatusStr = "未知"
	}

	// 转换发送耗时
	if record.SendDuration > 0 {
		if record.SendDuration < 1000 {
			resp.SendDurationStr = "< 1秒"
		} else {
			seconds := record.SendDuration / 1000
			resp.SendDurationStr = formatDuration(seconds)
		}
	} else {
		resp.SendDurationStr = "-"
	}

	return resp
}

// formatDuration 格式化耗时显示
func formatDuration(seconds int64) string {
	if seconds < 60 {
		return fmt.Sprintf("%d秒", seconds)
	} else if seconds < 3600 {
		minutes := seconds / 60
		remainSeconds := seconds % 60
		if remainSeconds == 0 {
			return fmt.Sprintf("%d分钟", minutes)
		}
		return fmt.Sprintf("%d分%d秒", minutes, remainSeconds)
	} else {
		hours := seconds / 3600
		minutes := (seconds % 3600) / 60
		if minutes == 0 {
			return fmt.Sprintf("%d小时", hours)
		}
		return fmt.Sprintf("%d小时%d分钟", hours, minutes)
	}
}

// NewSystemNotificationService 创建系统通知服务实例
func NewSystemNotificationService() *SystemNotificationService {
	return &SystemNotificationService{
		userService: &users.UserService{},
	}
}

// SendSystemNotification 发送系统通知
func (s *SystemNotificationService) SendSystemNotification(ctx context.Context, req request.SendSystemNotificationRequest) error {
	// 创建发送记录
	record, err := s.createNotificationRecord(ctx, req)
	if err != nil {
		global.GVA_LOG.Error("创建通知记录失败", zap.Error(err))
		return err
	}

	// 构建系统消息内容
	systemMsg := &bizmisc.SystemMsg{
		Title: &svcchat.TemplateMsg{
			Tpl:      req.SystemMsg.Title.Tpl,
			Data:     convertTemplateData(req.SystemMsg.Title.Data),
			ImageUrl: req.SystemMsg.Title.ImageUrl,
			Userid:   req.SystemMsg.Title.UserId,
			TplColor: req.SystemMsg.Title.TplColor,
			Type:     req.SystemMsg.Title.Type,
			Url:      req.SystemMsg.Title.Url,
		},
		BgmUrl:  req.SystemMsg.BgmUrl,
		JumpUrl: req.SystemMsg.JumpUrl,
		Content: &svcchat.TemplateMsg{
			Tpl:      req.SystemMsg.Content.Tpl,
			Data:     convertTemplateData(req.SystemMsg.Content.Data),
			ImageUrl: req.SystemMsg.Content.ImageUrl,
			Userid:   req.SystemMsg.Content.UserId,
			TplColor: req.SystemMsg.Content.TplColor,
			Type:     req.SystemMsg.Content.Type,
			Url:      req.SystemMsg.Content.Url,
		},
		Time: req.SystemMsg.Time,
	}

	// 如果时间为0，使用当前时间
	if systemMsg.Time == 0 {
		systemMsg.Time = time.Now().UnixMilli()
	}

	// 构建消息数据
	content := util.JsonStr(systemMsg)

	// 确定接收者列表
	var targetUids []int64
	if req.ToUid == -1 {
		// 全部用户：查询所有有效用户ID
		allUserIDs, err := s.userService.GetAllActiveUserIDs(ctx)
		if err != nil {
			global.GVA_LOG.Error("获取所有有效用户ID失败", zap.Error(err))
			return err
		}
		targetUids = allUserIDs
		global.GVA_LOG.Info("获取到有效用户", zap.Int("userCount", len(targetUids)))
	} else {
		// 指定用户：使用传入的用户ID列表
		targetUids = req.ToUids
	}

	if len(targetUids) == 0 {
		global.GVA_LOG.Warn("没有找到目标用户，跳过发送")
		return nil
	}

	// 使用并发批量发送
	err = s.sendNotificationsConcurrently(ctx, targetUids, content, req.ContentType, record.ID)

	// 更新发送记录
	s.updateNotificationRecord(ctx, record.ID, len(targetUids), err)

	return err
}

// sendNotificationsConcurrently 并发发送系统通知
func (s *SystemNotificationService) sendNotificationsConcurrently(ctx context.Context, targetUids []int64, content string, contentType int32, recordID uint) error {
	totalUsers := len(targetUids)
	global.GVA_LOG.Info("开始并发发送系统通知",
		zap.Int("totalUsers", totalUsers),
		zap.Int("batchSize", BatchSize),
		zap.Int("maxConcurrency", MaxConcurrency))

	// 分批处理
	batches := s.splitIntoBatches(targetUids, BatchSize)

	// 使用带缓冲的channel控制并发数
	semaphore := make(chan struct{}, MaxConcurrency)
	var wg sync.WaitGroup
	var mu sync.Mutex
	var totalSent int
	var totalErrors int

	for i, batch := range batches {
		wg.Add(1)
		go func(batchIndex int, userBatch []int64) {
			defer wg.Done()

			// 获取信号量
			semaphore <- struct{}{}
			defer func() { <-semaphore }()

			// 发送当前批次
			sent, err := s.sendBatch(ctx, userBatch, content, contentType, batchIndex)

			// 更新统计
			mu.Lock()
			totalSent += sent
			if err != nil {
				totalErrors++
				global.GVA_LOG.Error("批次发送失败",
					zap.Int("batchIndex", batchIndex),
					zap.Int("batchSize", len(userBatch)),
					zap.Error(err))
			}
			mu.Unlock()
		}(i, batch)
	}

	// 等待所有批次完成
	wg.Wait()

	global.GVA_LOG.Info("系统通知发送完成",
		zap.Int("totalUsers", totalUsers),
		zap.Int("totalSent", totalSent),
		zap.Int("totalErrors", totalErrors),
		zap.Int("totalBatches", len(batches)))

	if totalErrors > 0 {
		return errors.New("部分批次发送失败")
	}

	return nil
}

// splitIntoBatches 将用户ID列表分割成批次
func (s *SystemNotificationService) splitIntoBatches(userIDs []int64, batchSize int) [][]int64 {
	var batches [][]int64
	for i := 0; i < len(userIDs); i += batchSize {
		end := i + batchSize
		if end > len(userIDs) {
			end = len(userIDs)
		}
		batches = append(batches, userIDs[i:end])
	}
	return batches
}

// sendBatch 发送单个批次的消息
func (s *SystemNotificationService) sendBatch(ctx context.Context, userIDs []int64, content string, contentType int32, batchIndex int) (int, error) {
	if len(userIDs) == 0 {
		return 0, nil
	}

	// 构建消息列表
	msgs := make([]*basemsgtransfer.MsgData, 0, len(userIDs))
	for _, uid := range userIDs {
		msgs = append(msgs, &basemsgtransfer.MsgData{
			MsgId:       util.UUID(),
			From:        OfficialSecretaryUserid,
			To:          uid,
			SessionType: int32(basemsgtransfer.SessionType_SessionTypeSystem),
			ContentType: contentType,
			Content:     content,
			MsgFrom:     uint32(basemsgtransfer.MsgFromEnum_MsgFromIm),
			CreateTime:  time.Now().UnixMilli(),
		})
	}

	msg := &basemsgtransfer.SendMsgReq{
		Msgs: msgs,
	}

	global.GVA_LOG.Debug("发送批次消息",
		zap.Int("batchIndex", batchIndex),
		zap.Int("batchSize", len(userIDs)))

	// 调用消息发送服务
	resp, err := svcmgr.BaseMsgTransferClient().SendMsg(ctx, msg)
	if err != nil {
		global.GVA_LOG.Error("批次消息发送失败",
			zap.Int("batchIndex", batchIndex),
			zap.Error(err))
		return 0, err
	}

	if resp.Base != nil && resp.Base.Code != 0 {
		global.GVA_LOG.Error("批次消息发送服务返回错误",
			zap.Int("batchIndex", batchIndex),
			zap.String("msg", resp.Base.Msg))
		return 0, errors.New(resp.Base.Msg)
	}

	global.GVA_LOG.Debug("批次消息发送成功",
		zap.Int("batchIndex", batchIndex),
		zap.Int("sentCount", len(msgs)))

	return len(msgs), nil
}

// convertTemplateData 转换模版数据格式
func convertTemplateData(data map[string]request.TemplateItemRequest) map[string]*svcchat.TemplateItem {
	result := make(map[string]*svcchat.TemplateItem)
	for key, item := range data {
		result[key] = &svcchat.TemplateItem{
			Text:      item.Text,
			Color:     item.Color,
			Url:       item.Url,
			Type:      item.Type,
			Name:      item.Name,
			Ext:       item.Ext,
			Underline: item.Underline,
			Size:      item.Size,
		}
	}
	return result
}

// createNotificationRecord 创建通知发送记录
func (s *SystemNotificationService) createNotificationRecord(ctx context.Context, req request.SendSystemNotificationRequest) (*operations.SystemNotificationRecord, error) {
	// 提取标题和内容
	title := s.extractTextFromTemplate(req.SystemMsg.Title.Tpl)
	content := s.extractTextFromTemplate(req.SystemMsg.Content.Tpl)

	// 确定接收者类型
	receiverType := operations.ReceiverTypeSpecific
	var receiverUserIds string
	if req.ToUid == -1 {
		receiverType = operations.ReceiverTypeAll
	} else if len(req.ToUids) > 0 {
		userIdsBytes, _ := json.Marshal(req.ToUids)
		receiverUserIds = string(userIdsBytes)
	}

	// 序列化系统消息数据
	systemMsgData, _ := json.Marshal(req.SystemMsg)

	// 从context中获取操作员信息
	operatorId, operatorName := s.getOperatorInfo(ctx)

	record := &operations.SystemNotificationRecord{
		Title:           title,
		Content:         content,
		ReceiverType:    int32(receiverType),
		ReceiverCount:   0, // 稍后更新
		ReceiverUserIds: receiverUserIds,
		SendStatus:      operations.SendStatusSending,
		SuccessCount:    0,
		FailureCount:    0,
		SystemMsgData:   string(systemMsgData),
		SendStartTime:   time.Now(),
		OperatorId:      operatorId,
		OperatorName:    operatorName,
	}

	err := global.GVA_DB.WithContext(ctx).Create(record).Error
	return record, err
}

// getOperatorInfo 从context中获取操作员信息
func (s *SystemNotificationService) getOperatorInfo(ctx context.Context) (uint, string) {
	// 尝试从gin.Context中获取用户信息
	if ginCtx, ok := ctx.(*gin.Context); ok {
		operatorId := utils.GetUserID(ginCtx)
		operatorName := utils.GetUserName(ginCtx)

		// 如果获取到有效的用户信息，返回
		if operatorId > 0 && operatorName != "" {
			return operatorId, operatorName
		}
	}

	// 如果无法获取用户信息，返回默认值
	return 1, "系统管理员"
}

// updateNotificationRecord 更新通知发送记录
func (s *SystemNotificationService) updateNotificationRecord(ctx context.Context, recordID uint, totalCount int, sendErr error) {
	endTime := time.Now()
	updates := map[string]interface{}{
		"receiver_count": totalCount,
		"send_end_time":  &endTime,
	}

	// 计算发送耗时
	var record operations.SystemNotificationRecord
	if err := global.GVA_DB.WithContext(ctx).First(&record, recordID).Error; err == nil {
		duration := endTime.Sub(record.SendStartTime).Milliseconds()
		updates["send_duration"] = duration
	}

	if sendErr != nil {
		updates["send_status"] = operations.SendStatusFailed
		updates["error_message"] = sendErr.Error()
		updates["failure_count"] = totalCount
	} else {
		updates["send_status"] = operations.SendStatusSuccess
		updates["success_count"] = totalCount
	}

	global.GVA_DB.WithContext(ctx).Model(&operations.SystemNotificationRecord{}).
		Where("id = ?", recordID).Updates(updates)
}

// extractTextFromTemplate 从模板中提取纯文本
func (s *SystemNotificationService) extractTextFromTemplate(template string) string {
	// 简单的模板变量替换，去掉{{}}标记
	result := template
	// 可以根据需要进一步处理模板变量
	return result
}

// GetSystemNotificationRecords 获取系统通知记录列表
func (s *SystemNotificationService) GetSystemNotificationRecords(req request.GetSystemNotificationRecordsRequest) (GetSystemNotificationRecordsResponse, error) {
	var records []operations.SystemNotificationRecord
	var total int64

	db := global.GVA_DB.Model(&operations.SystemNotificationRecord{})

	// 搜索条件
	if req.Title != "" {
		db = db.Where("title LIKE ?", "%"+req.Title+"%")
	}
	if req.ReceiverType != nil {
		db = db.Where("receiver_type = ?", *req.ReceiverType)
	}
	if req.SendStatus != nil {
		db = db.Where("send_status = ?", *req.SendStatus)
	}
	if req.OperatorName != "" {
		db = db.Where("operator_name LIKE ?", "%"+req.OperatorName+"%")
	}
	if req.StartTime != "" {
		db = db.Where("created_at >= ?", req.StartTime)
	}
	if req.EndTime != "" {
		db = db.Where("created_at <= ?", req.EndTime)
	}

	// 获取总数
	err := db.Count(&total).Error
	if err != nil {
		return GetSystemNotificationRecordsResponse{}, err
	}

	// 排序
	orderBy := "created_at DESC" // 默认按创建时间降序
	if req.OrderKey != "" {
		direction := "ASC"
		if req.Desc {
			direction = "DESC"
		}

		// 验证排序字段
		validOrderKeys := map[string]bool{
			"id": true, "created_at": true, "title": true, "receiver_type": true,
			"receiver_count": true, "send_status": true, "success_count": true,
			"failure_count": true, "send_duration": true, "operator_name": true,
		}

		if validOrderKeys[req.OrderKey] {
			orderBy = fmt.Sprintf("%s %s", req.OrderKey, direction)
		}
	}

	// 分页查询
	offset := (req.Page - 1) * req.PageSize
	err = db.Order(orderBy).Offset(offset).Limit(req.PageSize).Find(&records).Error
	if err != nil {
		return GetSystemNotificationRecordsResponse{}, err
	}

	// 转换为响应格式
	var list []SystemNotificationRecordResponse
	for _, record := range records {
		list = append(list, convertToResponse(record))
	}

	return GetSystemNotificationRecordsResponse{
		List:     list,
		Total:    total,
		Page:     req.Page,
		PageSize: req.PageSize,
	}, nil
}

// GetJumpUrlOptions 获取跳转地址选项列表
func (s *SystemNotificationService) GetJumpUrlOptions() []JumpUrlOption {
	return []JumpUrlOption{
		{
			Label:       "个人资料编辑页",
			Value:       "vc://voicelives/account/edit",
			Description: "跳转到个人资料编辑页面",
			Params:      "",
		},
		{
			Label:       "首页",
			Value:       "vc://voicelives/main",
			Description: "跳转到应用首页",
			Params:      "tab=0(首页)、1(我的)",
		},
		{
			Label:       "搜索",
			Value:       "vc://voicelives/search",
			Description: "跳转到搜索页面",
			Params:      "",
		},
		{
			Label:       "话题聚合页",
			Value:       "vc://voicelives/topic",
			Description: "跳转到话题聚合页面",
			Params:      "topic={id:xxxx}",
		},
		{
			Label:       "剧本详情",
			Value:       "vc://voicelives/scenario",
			Description: "跳转到剧本详情页面",
			Params:      "scenario={id:xxx}tab=0(角色演绎)、1(剧本)dubbing=true(直接打开配音角色选择)",
		},
		{
			Label:       "剧本配音",
			Value:       "vc://voicelives/scenario/dubbing",
			Description: "跳转到剧本配音页面",
			Params:      "scenario={id:xxx}character_list=[{id:xxx}]",
		},
		{
			Label:       "剧本创建",
			Value:       "vc://voicelives/scenario/create",
			Description: "跳转到剧本创建页面",
			Params:      "",
		},
		{
			Label:       "通知",
			Value:       "vc://voicelives/scenario/notice",
			Description: "跳转到通知页面",
			Params:      "",
		},
		{
			Label:       "粉丝通知",
			Value:       "vc://voicelives/scenario/notice/fans",
			Description: "跳转到粉丝通知页面",
			Params:      "",
		},
		{
			Label:       "评论回复通知",
			Value:       "vc://voicelives/scenario/notice/comment/reply",
			Description: "跳转到评论回复通知页面",
			Params:      "",
		},
		{
			Label:       "收到的赞通知",
			Value:       "vc://voicelives/scenario/notice/like",
			Description: "跳转到收到的赞通知页面",
			Params:      "",
		},
		{
			Label:       "收到配音通知",
			Value:       "vc://voicelives/scenario/notice/dubbing",
			Description: "跳转到收到配音通知页面",
			Params:      "",
		},
		{
			Label:       "个人资料页",
			Value:       "vc://voicelives/scenario/user",
			Description: "跳转到个人资料页面",
			Params:      "user_id=xxxtab=0(创建)、1(配过)、2(赞过)",
		},
		{
			Label:       "我的关注",
			Value:       "vc://voicelives/scenario/mine/follow",
			Description: "跳转到我的关注页面",
			Params:      "",
		},
		{
			Label:       "我的粉丝",
			Value:       "vc://voicelives/scenario/mine/fans",
			Description: "跳转到我的粉丝页面",
			Params:      "",
		},
	}
}
