package operations

import (
	"context"
	"errors"
	"time"

	"github.com/flipped-aurora/gin-vue-admin/server/global"
	"github.com/flipped-aurora/gin-vue-admin/server/model/operations"
	operationsReq "github.com/flipped-aurora/gin-vue-admin/server/model/operations/request"
)

type AppVersionService struct{}

// CreateAppVersion 创建APP版本记录
func (appVersionService *AppVersionService) CreateAppVersion(ctx context.Context, appVersion *operations.AppVersion) (err error) {
	// 检查同一包名、版本、平台是否已存在
	var count int64
	err = global.GVA_DB.Model(&operations.AppVersion{}).Where(
		"package_name = ? AND version = ? AND platform = ? AND status != ?",
		*appVersion.PackageName, *appVersion.Version, *appVersion.Platform, 4,
	).Count(&count).Error
	if err != nil {
		return err
	}
	if count > 0 {
		return errors.New("该包名、版本号、平台的版本已存在")
	}

	// 设置创建时间
	now := time.Now().UnixMilli()
	appVersion.CreatedAt = &now
	appVersion.UpdatedAt = &now

	// 设置默认值
	if appVersion.Status == nil {
		status := int8(1) // 待发布
		appVersion.Status = &status
	}
	if appVersion.Channel == nil {
		channel := "official"
		appVersion.Channel = &channel
	}
	if appVersion.ForceUpdate == nil {
		forceUpdate := false
		appVersion.ForceUpdate = &forceUpdate
	}

	err = global.GVA_DB.Create(appVersion).Error
	return err
}

// DeleteAppVersion 删除APP版本记录
func (appVersionService *AppVersionService) DeleteAppVersion(ctx context.Context, id uint64) (err error) {
	// 软删除：更新状态为已删除
	now := time.Now().UnixMilli()
	status := int8(4) // 已删除
	err = global.GVA_DB.Model(&operations.AppVersion{}).Where("id = ?", id).Updates(map[string]interface{}{
		"status":     status,
		"updated_at": now,
	}).Error
	return err
}

// DeleteAppVersionByIds 批量删除APP版本记录
func (appVersionService *AppVersionService) DeleteAppVersionByIds(ctx context.Context, ids []uint64) (err error) {
	// 软删除：更新状态为已删除
	now := time.Now().UnixMilli()
	status := int8(4) // 已删除
	err = global.GVA_DB.Model(&operations.AppVersion{}).Where("id IN ?", ids).Updates(map[string]interface{}{
		"status":     status,
		"updated_at": now,
	}).Error
	return err
}

// UpdateAppVersion 更新APP版本记录
func (appVersionService *AppVersionService) UpdateAppVersion(ctx context.Context, appVersion operations.AppVersion) (err error) {
	// 检查同一包名、版本、平台是否已存在（排除当前记录）
	var count int64
	err = global.GVA_DB.Model(&operations.AppVersion{}).Where(
		"package_name = ? AND version = ? AND platform = ? AND status != ? AND id != ?",
		*appVersion.PackageName, *appVersion.Version, *appVersion.Platform, 4, *appVersion.ID,
	).Count(&count).Error
	if err != nil {
		return err
	}
	if count > 0 {
		return errors.New("该包名、版本号、平台的版本已存在")
	}

	// 设置更新时间
	now := time.Now().UnixMilli()
	appVersion.UpdatedAt = &now

	err = global.GVA_DB.Model(&operations.AppVersion{}).Where("id = ?", *appVersion.ID).Updates(&appVersion).Error
	return err
}

// GetAppVersion 根据ID获取APP版本记录
func (appVersionService *AppVersionService) GetAppVersion(ctx context.Context, id uint64) (appVersion operations.AppVersion, err error) {
	err = global.GVA_DB.Where("id = ? AND status != ?", id, 4).First(&appVersion).Error
	return
}

// GetAppVersionInfoList 分页获取APP版本记录
func (appVersionService *AppVersionService) GetAppVersionInfoList(ctx context.Context, info operationsReq.AppVersionSearch) (list []operations.AppVersion, total int64, err error) {
	limit := info.PageSize
	offset := info.PageSize * (info.Page - 1)

	// 构建查询条件
	db := global.GVA_DB.Model(&operations.AppVersion{}).Where("status != ?", 4)

	// 添加搜索条件
	if info.Title != nil && *info.Title != "" {
		db = db.Where("title LIKE ?", "%"+*info.Title+"%")
	}
	if info.PackageName != nil && *info.PackageName != "" {
		db = db.Where("package_name LIKE ?", "%"+*info.PackageName+"%")
	}
	if info.Version != nil && *info.Version != "" {
		db = db.Where("version LIKE ?", "%"+*info.Version+"%")
	}
	if info.Platform != nil && *info.Platform != "" {
		db = db.Where("platform = ?", *info.Platform)
	}
	if info.Status != nil {
		db = db.Where("status = ?", *info.Status)
	}
	if info.StartCreatedAt != nil && info.EndCreatedAt != nil {
		db = db.Where("created_at BETWEEN ? AND ?", *info.StartCreatedAt, *info.EndCreatedAt)
	}

	// 排序
	orderStr := "created_at DESC" // 默认按创建时间倒序
	if info.Sort != "" && info.Order != "" {
		orderStr = info.Sort + " " + info.Order
	}

	err = db.Count(&total).Error
	if err != nil {
		return
	}

	err = db.Order(orderStr).Limit(limit).Offset(offset).Find(&list).Error
	return list, total, err
}

// PublishAppVersion 发布APP版本
func (appVersionService *AppVersionService) PublishAppVersion(ctx context.Context, id uint64) (err error) {
	now := time.Now().UnixMilli()
	status := int8(2) // 已发布
	err = global.GVA_DB.Model(&operations.AppVersion{}).Where("id = ? AND status = ?", id, 1).Updates(map[string]interface{}{
		"status":       status,
		"publish_time": now,
		"updated_at":   now,
	}).Error
	if err != nil {
		return err
	}

	// 检查是否更新成功
	var count int64
	err = global.GVA_DB.Model(&operations.AppVersion{}).Where("id = ? AND status = ?", id, 2).Count(&count).Error
	if err != nil {
		return err
	}
	if count == 0 {
		return errors.New("版本状态不正确，只能发布待发布状态的版本")
	}

	return nil
}

// OfflineAppVersion 下线APP版本
func (appVersionService *AppVersionService) OfflineAppVersion(ctx context.Context, id uint64) (err error) {
	now := time.Now().UnixMilli()
	status := int8(3) // 已下线
	err = global.GVA_DB.Model(&operations.AppVersion{}).Where("id = ? AND status = ?", id, 2).Updates(map[string]interface{}{
		"status":       status,
		"offline_time": now,
		"updated_at":   now,
	}).Error
	if err != nil {
		return err
	}

	// 检查是否更新成功
	var count int64
	err = global.GVA_DB.Model(&operations.AppVersion{}).Where("id = ? AND status = ?", id, 3).Count(&count).Error
	if err != nil {
		return err
	}
	if count == 0 {
		return errors.New("版本状态不正确，只能下线已发布状态的版本")
	}

	return nil
}

// GetPlatformOptions 获取平台选项
func (appVersionService *AppVersionService) GetPlatformOptions() map[string]string {
	return map[string]string{
		"ios":     "iOS",
		"android": "Android",
		"all":     "通用",
	}
}

// GetStatusOptions 获取状态选项
func (appVersionService *AppVersionService) GetStatusOptions() map[string]string {
	return map[string]string{
		"1": "待发布",
		"2": "已发布",
		"3": "已下线",
		"4": "已删除",
	}
}

// GetLatestVersionByPackageAndPlatform 根据包名和平台获取最新版本
func (appVersionService *AppVersionService) GetLatestVersionByPackageAndPlatform(ctx context.Context, packageName, platform string) (appVersion operations.AppVersion, err error) {
	// 查询已发布状态的最新版本，按创建时间倒序
	err = global.GVA_DB.Where("package_name = ? AND (platform = ? OR platform = 'all') AND status = ?",
		packageName, platform, 2).
		Order("created_at DESC").
		First(&appVersion).Error
	return
}
