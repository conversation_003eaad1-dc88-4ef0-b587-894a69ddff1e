package characters

import (
	"context"
	"errors"

	"github.com/flipped-aurora/gin-vue-admin/server/global"
	"github.com/flipped-aurora/gin-vue-admin/server/model/characters"
	charactersReq "github.com/flipped-aurora/gin-vue-admin/server/model/characters/request"
)

type CharacterService struct{}

// CreateCharacter 创建characters表记录
// Author [yourname](https://github.com/yourname)
func (characterService *CharacterService) CreateCharacter(ctx context.Context, character *characters.Character) (err error) {
	// 如果创建时状态为上架（status=1），需要验证是否有默认资源包
	// 注意：新创建的角色通常不会有资源包，所以这里主要是防止意外情况
	if character.Status != nil && *character.Status == 1 && character.Id != nil {
		// 检查是否有默认资源包
		characterAssetsService := &CharacterAssetsService{}
		hasDefault, err := characterAssetsService.HasDefaultAsset(ctx, *character.Id)
		if err != nil {
			return err
		}
		if !hasDefault {
			return errors.New("角色创建失败：该角色没有配置默认资源包，请先设置默认资源包")
		}
	}

	err = global.GVA_DB.Create(character).Error
	return err
}

// DeleteCharacter 删除characters表记录
// Author [yourname](https://github.com/yourname)
func (characterService *CharacterService) DeleteCharacter(ctx context.Context, id string) (err error) {
	err = global.GVA_DB.Model(&characters.Character{}).Where("id = ?", id).Update("status", 2).Error
	return err
}

// DeleteCharacterByIds 批量删除characters表记录
// Author [yourname](https://github.com/yourname)
func (characterService *CharacterService) DeleteCharacterByIds(ctx context.Context, ids []string) (err error) {
	err = global.GVA_DB.Model(&characters.Character{}).Where("id in ?", ids).Update("status", 2).Error
	return err
}

// UpdateCharacter 更新characters表记录
// Author [yourname](https://github.com/yourname)
func (characterService *CharacterService) UpdateCharacter(ctx context.Context, character characters.Character) (err error) {
	// 如果要上架角色（status=1），需要验证是否有默认资源包
	if character.Status != nil && *character.Status == 1 {
		// 获取当前角色信息
		var currentCharacter characters.Character
		err = global.GVA_DB.Where("id = ?", character.Id).First(&currentCharacter).Error
		if err != nil {
			return err
		}

		// 如果当前状态不是上架，说明是要进行上架操作，需要验证默认资源包
		if currentCharacter.Status == nil || *currentCharacter.Status != 1 {
			// 检查是否有默认资源包
			characterAssetsService := &CharacterAssetsService{}
			hasDefault, err := characterAssetsService.HasDefaultAsset(ctx, *character.Id)
			if err != nil {
				return err
			}
			if !hasDefault {
				return errors.New("角色上架失败：该角色没有配置默认资源包，请先设置默认资源包")
			}
		}
	}

	err = global.GVA_DB.Model(&characters.Character{}).Where("id = ?", character.Id).Updates(&character).Error
	return err
}

// GetCharacter 根据id获取characters表记录
// Author [yourname](https://github.com/yourname)
func (characterService *CharacterService) GetCharacter(ctx context.Context, id string) (character characters.Character, err error) {
	err = global.GVA_DB.Where("id = ?", id).First(&character).Error
	return
}

// GetCharacterInfoList 分页获取characters表记录
// Author [yourname](https://github.com/yourname)
func (characterService *CharacterService) GetCharacterInfoList(ctx context.Context, info charactersReq.CharacterSearch) (list []characters.Character, total int64, err error) {
	limit := info.PageSize
	offset := info.PageSize * (info.Page - 1)
	// 创建db
	db := global.GVA_DB.Model(&characters.Character{})
	var characters []characters.Character
	// 如果有条件搜索 下方会自动创建搜索语句
	if len(info.Name) > 0 {
		db = db.Where("name LIKE ?", "%"+info.Name+"%")
	}
	// 推荐状态过滤
	if info.IsRecommended != nil {
		db = db.Where("is_recommended = ?", *info.IsRecommended)
	}
	// IP ID过滤
	if info.IpId != nil && *info.IpId > 0 {
		db = db.Where("ip_id = ?", *info.IpId)
	}

	err = db.Count(&total).Error
	if err != nil {
		return
	}

	if limit != 0 {
		db = db.Limit(limit).Offset(offset)
	}

	err = db.Find(&characters).Error
	return characters, total, err
}

// GetRecommendedCharacters 获取推荐角色列表
// Author [yourname](https://github.com/yourname)
func (characterService *CharacterService) GetRecommendedCharacters(ctx context.Context, info charactersReq.CharacterSearch) (list []characters.Character, total int64, err error) {
	limit := info.PageSize
	offset := info.PageSize * (info.Page - 1)
	// 创建db，只查询推荐角色
	db := global.GVA_DB.Model(&characters.Character{}).Where("is_recommended = ? AND status = ?", true, 1)
	var characters []characters.Character

	// 如果有条件搜索
	if len(info.Name) > 0 {
		db = db.Where("name LIKE ?", "%"+info.Name+"%")
	}
	// IP ID过滤
	if info.IpId != nil && *info.IpId > 0 {
		db = db.Where("ip_id = ?", *info.IpId)
	}

	err = db.Count(&total).Error
	if err != nil {
		return
	}

	if limit != 0 {
		db = db.Limit(limit).Offset(offset)
	}

	err = db.Find(&characters).Error
	return characters, total, err
}

// SetCharacterRecommended 设置角色推荐状态
// Author [yourname](https://github.com/yourname)
func (characterService *CharacterService) SetCharacterRecommended(ctx context.Context, id string, isRecommended bool) (err error) {
	err = global.GVA_DB.Model(&characters.Character{}).Where("id = ?", id).Update("is_recommended", isRecommended).Error
	return err
}

// BatchSetCharacterRecommended 批量设置角色推荐状态
// Author [yourname](https://github.com/yourname)
func (characterService *CharacterService) BatchSetCharacterRecommended(ctx context.Context, ids []string, isRecommended bool) (err error) {
	err = global.GVA_DB.Model(&characters.Character{}).Where("id in ?", ids).Update("is_recommended", isRecommended).Error
	return err
}
