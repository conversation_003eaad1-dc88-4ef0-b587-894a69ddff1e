package users

import (
	"context"

	"github.com/flipped-aurora/gin-vue-admin/server/global"
	"github.com/flipped-aurora/gin-vue-admin/server/model/users"
	usersReq "github.com/flipped-aurora/gin-vue-admin/server/model/users/request"
)

type UserService struct{}

// UpdateUser 更新users表记录
// Author [yourname](https://github.com/yourname)
func (userService *UserService) UpdateUser(ctx context.Context, user users.User) (err error) {
	err = global.GVA_DB.Model(&users.User{}).Where("user_id = ?", user.UserId).Omit("phone", "user_id").Updates(&user).Error
	return err
}

// GetUser 根据user_id获取users表记录
// Author [yourname](https://github.com/yourname)
func (userService *UserService) GetUser(ctx context.Context, user_id string) (user *users.User, err error) {
	err = global.GVA_DB.Where("user_id = ?", user_id).First(&user).Error
	return
}

// GetUserInfoList 分页获取users表记录
// Author [yourname](https://github.com/yourname)
func (userService *UserService) GetUserInfoList(ctx context.Context, info usersReq.UserSearch) (list []*users.User, total int64, err error) {
	limit := info.PageSize
	offset := info.PageSize * (info.Page - 1)
	// 创建db
	db := global.GVA_DB.Model(&users.User{})
	var users []*users.User
	// admin后台可以查看所有用户（包括已删除的），通过deleted_at字段区分状态
	// 如果有条件搜索 下方会自动创建搜索语句
	if info.UserId > 0 {
		db = db.Where("user_id = ?", info.UserId)
	}
	if len(info.NickName) > 0 {
		db = db.Where("nickname like ?", "%"+info.NickName+"%")
	}
	if len(info.Phone) > 0 {
		db = db.Where("phone = ?", info.Phone)
	}
	err = db.Count(&total).Error
	if err != nil {
		return
	}

	// 添加排序逻辑
	orderBy := "created_at DESC" // 默认按创建时间倒序
	if len(info.Sort) > 0 && len(info.Order) > 0 {
		// 验证排序字段，防止SQL注入
		allowedSortFields := map[string]string{
			"user_id":                    "user_id",
			"created_at":                 "created_at",
			"status":                     "status",
			"is_premium_creator":         "is_premium_creator",
			"review_voice_signature_url": "review_voice_signature_url",
		}
		if dbField, ok := allowedSortFields[info.Sort]; ok {
			if info.Order == "asc" || info.Order == "desc" {
				orderBy = dbField + " " + info.Order
			}
		}
	}
	db = db.Order(orderBy)

	if limit != 0 {
		db = db.Limit(limit).Offset(offset)
	}

	err = db.Find(&users).Error
	return users, total, err
}

// GetAllActiveUserIDs 获取所有有效用户ID列表（用于系统通知等批量操作）
func (userService *UserService) GetAllActiveUserIDs(ctx context.Context) (userIDs []int64, err error) {
	var userList []users.User
	// 查询所有有效用户（状态为正常且未删除）
	err = global.GVA_DB.WithContext(ctx).
		Model(&users.User{}).
		Select("user_id").
		Where("status = ? AND deleted_at = 0", 1). // 状态为正常且未删除
		Find(&userList).Error

	if err != nil {
		return nil, err
	}

	// 提取用户ID
	userIDs = make([]int64, 0, len(userList))
	for _, user := range userList {
		if user.UserId != nil {
			userIDs = append(userIDs, int64(*user.UserId))
		}
	}

	return userIDs, nil
}
