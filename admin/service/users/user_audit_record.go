package users

import (
	"context"
	"fmt"
	"github.com/flipped-aurora/gin-vue-admin/server/global"
	"github.com/flipped-aurora/gin-vue-admin/server/model/users"
	usersReq "github.com/flipped-aurora/gin-vue-admin/server/model/users/request"
	"go.uber.org/zap"
	"new-gitlab.xunlei.cn/vcproject/backends/baselib/errcode"
	"new-gitlab.xunlei.cn/vcproject/backends/proto/api/svcaccount"
	"new-gitlab.xunlei.cn/vcproject/backends/proto/api/svcscript"
	"new-gitlab.xunlei.cn/vcproject/backends/proto/svcmgr"
)

type UserAuditRecordService struct {
	userAuditRecordService users.UserAuditRecordService
}

// GetUserAuditRecordList 获取用户审核记录列表
func (s *UserAuditRecordService) GetUserAuditRecordList(ctx context.Context, info usersReq.UserAuditRecordSearch) (list []*users.UserAuditRecord, total int64, err error) {
	limit := info.PageSize
	offset := info.PageSize * (info.Page - 1)

	// 调用模型层方法获取数据
	list, total, err = s.userAuditRecordService.GetUserAuditRecordsList(
		ctx,
		info.UserId,
		info.AuditType,
		info.AuditStatus,
		info.StartTime,
		info.EndTime,
		limit,
		offset,
	)

	if err != nil {
		global.GVA_LOG.Error("获取用户审核记录列表失败", zap.Error(err))
		return nil, 0, err
	}

	return list, total, nil
}

// GetUserAuditRecord 根据ID获取用户审核记录
func (s *UserAuditRecordService) GetUserAuditRecord(ctx context.Context, id int64) (*users.UserAuditRecord, error) {
	record, err := s.userAuditRecordService.GetUserAuditRecordByID(ctx, id)
	if err != nil {
		global.GVA_LOG.Error("获取用户审核记录失败", zap.Error(err))
		return nil, err
	}
	return record, nil
}

// ApproveUserAuditRecord 审核通过用户审核记录
func (s *UserAuditRecordService) ApproveUserAuditRecord(ctx context.Context, id int64) error {
	// 获取审核记录
	record, err := s.userAuditRecordService.GetUserAuditRecordByID(ctx, id)
	if err != nil {
		global.GVA_LOG.Error("获取用户审核记录失败", zap.Error(err))
		return err
	}

	// 检查审核状态，只有待审核或机审通过的记录才能进行人审
	if record.AuditStatus != svcscript.ReviewStatus_REVIEW_STATUS_PENDING &&
		record.AuditStatus != svcscript.ReviewStatus_REVIEW_STATUS_AUTO_APPROVED {
		return errcode.ErrUserAuditInvalidStatus.WithMessage("只有待审核或机审通过的记录才能进行人审")
	}

	// 更新审核记录状态为人审通过
	err = s.userAuditRecordService.UpdateUserAuditRecord(ctx, id, map[string]any{
		"audit_status": svcscript.ReviewStatus_REVIEW_STATUS_MANUAL_APPROVED,
	})
	if err != nil {
		global.GVA_LOG.Error("更新审核记录状态失败", zap.Error(err))
		return err
	}

	// 根据审核类型更新用户信息
	err = s.updateUserInfoAfterApproval(ctx, record)
	if err != nil {
		global.GVA_LOG.Error("更新用户信息失败", zap.Error(err))
		// 回滚审核记录状态
		err := s.userAuditRecordService.UpdateUserAuditRecord(ctx, id, map[string]any{
			"audit_status": record.AuditStatus,
		})
		if err != nil {
			global.GVA_LOG.Error("回滚审核记录状态失败", zap.Error(err))
			return err
		}
		return err
	}

	global.GVA_LOG.Info("用户审核通过成功", zap.Int64("recordId", id), zap.Int64("userId", record.UserId))
	return nil
}

// RejectUserAuditRecord 审核拒绝用户审核记录
func (s *UserAuditRecordService) RejectUserAuditRecord(ctx context.Context, id int64, reason string) error {
	// 获取审核记录
	record, err := s.userAuditRecordService.GetUserAuditRecordByID(ctx, id)
	if err != nil {
		global.GVA_LOG.Error("获取用户审核记录失败", zap.Error(err))
		return err
	}

	// 检查审核状态，只有待审核或机审通过的记录才能进行人审
	if record.AuditStatus != svcscript.ReviewStatus_REVIEW_STATUS_PENDING &&
		record.AuditStatus != svcscript.ReviewStatus_REVIEW_STATUS_AUTO_APPROVED {
		return errcode.ErrUserAuditInvalidStatus.WithMessage("只有待审核或机审通过的记录才能进行人审")
	}

	// 更新审核记录状态为人审拒绝
	err = s.userAuditRecordService.UpdateUserAuditRecord(ctx, id, map[string]any{
		"audit_status": svcscript.ReviewStatus_REVIEW_STATUS_MANUAL_REJECTED,
	})
	if err != nil {
		global.GVA_LOG.Error("更新审核记录状态失败", zap.Error(err))
		return err
	}

	// 审核拒绝时不需要额外的用户信息更新操作
	_, err = svcmgr.AccountClient().ReviewNotify(ctx, &svcaccount.ReviewNotifyReq{
		UserId:           record.UserId,
		ReviewNotifyType: svcaccount.ReviewNotifyType_SIGNATURE_REJECT,
	})
	if err != nil {
		global.GVA_LOG.Error("通知用户失败", zap.Error(err))
	}

	global.GVA_LOG.Info("用户审核拒绝成功", zap.Int64("recordId", id), zap.Int64("userId", record.UserId), zap.String("reason", reason))
	return nil
}

// updateUserInfoAfterApproval 审核通过后更新用户信息
func (s *UserAuditRecordService) updateUserInfoAfterApproval(ctx context.Context, record *users.UserAuditRecord) error {
	content, err := record.GetContentStruct()
	if err != nil {
		return fmt.Errorf("解析审核内容失败: %v", err)
	}

	switch record.AuditType {
	case users.UserAuditTypeVoice:
		// 语音签名审核通过
		_, err = svcmgr.AccountClient().AdminUpdateUserInfo(ctx, &svcaccount.AdminUpdateUserInfoReq{
			UserId:            record.UserId,
			VoiceSignatureUrl: content.VoiceSignatureUrl,
			VoiceDuration:     content.VoiceDuration,
		})
		if err != nil {
			return fmt.Errorf("更新用户语音签名失败: %v", err)
		}

	case users.UserAuditTypeAvatar:
		// 头像审核通过
		_, err = svcmgr.AccountClient().AdminUpdateUserInfo(ctx, &svcaccount.AdminUpdateUserInfoReq{
			UserId: record.UserId,
			Avatar: content.Avatar,
		})
		if err != nil {
			return fmt.Errorf("更新用户头像失败: %v", err)
		}

	case users.UserAuditTypeBackground:
		// 背景图审核通过
		_, err = svcmgr.AccountClient().AdminUpdateUserInfo(ctx, &svcaccount.AdminUpdateUserInfoReq{
			UserId:        record.UserId,
			BackgroundUrl: content.BackgroundUrl,
		})
		if err != nil {
			return fmt.Errorf("更新用户背景图失败: %v", err)
		}

	case users.UserAuditTypeNickname:
		// 昵称审核通过
		_, err = svcmgr.AccountClient().AdminUpdateUserInfo(ctx, &svcaccount.AdminUpdateUserInfoReq{
			UserId:   record.UserId,
			Nickname: content.Nickname,
		})
		if err != nil {
			return fmt.Errorf("更新用户昵称失败: %v", err)
		}
	}

	return nil
}

// BatchApproveUserAuditRecords 批量审核通过
func (s *UserAuditRecordService) BatchApproveUserAuditRecords(ctx context.Context, ids []int64) error {
	for _, id := range ids {
		err := s.ApproveUserAuditRecord(ctx, id)
		if err != nil {
			global.GVA_LOG.Error("批量审核通过失败", zap.Int64("recordId", id), zap.Error(err))
			return fmt.Errorf("审核记录ID %d 处理失败: %v", id, err)
		}
	}
	return nil
}

// BatchRejectUserAuditRecords 批量审核拒绝
func (s *UserAuditRecordService) BatchRejectUserAuditRecords(ctx context.Context, ids []int64, reason string) error {
	for _, id := range ids {
		err := s.RejectUserAuditRecord(ctx, id, reason)
		if err != nil {
			global.GVA_LOG.Error("批量审核拒绝失败", zap.Int64("recordId", id), zap.Error(err))
			return fmt.Errorf("审核记录ID %d 处理失败: %v", id, err)
		}
	}
	return nil
}
