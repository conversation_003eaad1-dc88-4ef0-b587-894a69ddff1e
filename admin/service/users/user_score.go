package users

import (
	"context"
	"strconv"

	"github.com/flipped-aurora/gin-vue-admin/server/global"
	"github.com/flipped-aurora/gin-vue-admin/server/model/users"
	"gorm.io/gorm"
)

type UserScoreService struct{}

// GetUserScore 根据user_id获取user_socre表记录
// Author [yourname](https://github.com/yourname)
func (userScoreService *UserScoreService) GetUserScore(ctx context.Context, user_id string) (userScore *users.UserScore, err error) {
	err = global.GVA_DB.Where("user_id = ?", user_id).First(&userScore).Error
	if err == gorm.ErrRecordNotFound {
		uid, _ := strconv.ParseInt(user_id, 10, 64)
		userScore = &users.UserScore{
			UserId: uid,
		}
		return userScore, nil
	}
	return
}
