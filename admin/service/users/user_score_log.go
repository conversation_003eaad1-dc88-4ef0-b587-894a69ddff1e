package users

import (
	"context"

	"github.com/flipped-aurora/gin-vue-admin/server/global"
	"github.com/flipped-aurora/gin-vue-admin/server/model/users"
	usersReq "github.com/flipped-aurora/gin-vue-admin/server/model/users/request"
)

type UserScoreLogService struct{}

// GetUserScoreLog 根据id获取userScoreLog表记录
// Author [yourname](https://github.com/yourname)
func (userScoreLogService *UserScoreLogService) GetUserScoreLog(ctx context.Context, id string) (userScoreLog *users.UserScoreLog, err error) {
	err = global.GVA_DB.Where("id = ?", id).First(&userScoreLog).Error
	return
}

// GetUserScoreLogInfoList 分页获取userScoreLog表记录
// Author [yourname](https://github.com/yourname)
func (userScoreLogService *UserScoreLogService) GetUserScoreLogInfoList(ctx context.Context, info usersReq.UserScoreLogSearch) (list []*users.UserScoreLog, total int64, err error) {
	if info.UserId <= 0 {
		return
	}
	limit := info.PageSize
	offset := info.PageSize * (info.Page - 1)
	// 创建db
	db := global.GVA_DB.Table(users.UserScoreLog{}.TableName(info.UserId))
	var userScoreLogs []*users.UserScoreLog
	// 如果有条件搜索 下方会自动创建搜索语句
	if info.UserId > 0 {
		db = db.Where("user_id = ?", info.UserId)
	}

	err = db.Count(&total).Error
	if err != nil {
		return
	}

	if limit != 0 {
		db = db.Limit(limit).Offset(offset)
	}

	err = db.Find(&userScoreLogs).Error
	return userScoreLogs, total, err
}
