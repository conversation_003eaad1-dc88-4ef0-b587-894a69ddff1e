package mymoments

import (
	"context"

	"github.com/flipped-aurora/gin-vue-admin/server/global"
	"github.com/flipped-aurora/gin-vue-admin/server/model/mymoments"
	mymomentsReq "github.com/flipped-aurora/gin-vue-admin/server/model/mymoments/request"
)

type MomentsService struct{}

// CreateMoments 创建moments表记录
// Author [yourname](https://github.com/yourname)
func (momentsService *MomentsService) CreateMoments(ctx context.Context, moments *mymoments.Moments) (err error) {
	err = global.GVA_DB.Create(moments).Error
	return err
}

// DeleteMoments 删除moments表记录
// Author [yourname](https://github.com/yourname)
func (momentsService *MomentsService) DeleteMoments(ctx context.Context, id string) (err error) {
	err = global.GVA_DB.Delete(&mymoments.Moments{}, "id = ?", id).Error
	return err
}

// DeleteMomentsByIds 批量删除moments表记录
// Author [yourname](https://github.com/yourname)
func (momentsService *MomentsService) DeleteMomentsByIds(ctx context.Context, ids []string) (err error) {
	err = global.GVA_DB.Delete(&[]mymoments.Moments{}, "id in ?", ids).Error
	return err
}

// UpdateMoments 更新moments表记录
// Author [yourname](https://github.com/yourname)
func (momentsService *MomentsService) UpdateMoments(ctx context.Context, moments mymoments.Moments) (err error) {
	err = global.GVA_DB.Model(&mymoments.Moments{}).Where("id = ?", moments.Id).Updates(&moments).Error
	return err
}

// GetMoments 根据id获取moments表记录
// Author [yourname](https://github.com/yourname)
func (momentsService *MomentsService) GetMoments(ctx context.Context, id string) (moments mymoments.Moments, err error) {
	err = global.GVA_DB.Where("id = ?", id).First(&moments).Error
	return
}

// GetMomentsInfoList 分页获取moments表记录
// Author [yourname](https://github.com/yourname)
func (momentsService *MomentsService) GetMomentsInfoList(ctx context.Context, info mymomentsReq.MomentsSearch) (list []mymoments.Moments, total int64, err error) {
	limit := info.PageSize
	offset := info.PageSize * (info.Page - 1)
	// 创建db
	db := global.GVA_DB.Model(&mymoments.Moments{})
	var momentss []mymoments.Moments
	// 如果有条件搜索 下方会自动创建搜索语句

	err = db.Count(&total).Error
	if err != nil {
		return
	}

	if limit != 0 {
		db = db.Limit(limit).Offset(offset)
	}

	err = db.Find(&momentss).Error
	return momentss, total, err
}
func (momentsService *MomentsService) GetMomentsPublic(ctx context.Context) {
	// 此方法为获取数据源定义的数据
	// 请自行实现
}
