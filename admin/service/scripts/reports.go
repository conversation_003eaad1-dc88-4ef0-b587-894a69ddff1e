package scripts

import (
	"context"
	"errors"
	"fmt"
	"github.com/flipped-aurora/gin-vue-admin/server/global"
	"github.com/flipped-aurora/gin-vue-admin/server/model/characters"
	"github.com/flipped-aurora/gin-vue-admin/server/model/scripts"
	scriptsReq "github.com/flipped-aurora/gin-vue-admin/server/model/scripts/request"
	"github.com/flipped-aurora/gin-vue-admin/server/utils"
	"gorm.io/gorm"
	"new-gitlab.xunlei.cn/vcproject/backends/baselib/logger"
	"new-gitlab.xunlei.cn/vcproject/backends/proto/api/svcaccount"
	"new-gitlab.xunlei.cn/vcproject/backends/proto/api/svcscript"
	"new-gitlab.xunlei.cn/vcproject/backends/proto/svcmgr"
	"strings"
)

type ReportService struct{}

// CreateReport 创建reports表记录
// Author [yourname](https://github.com/yourname)
func (reportService *ReportService) CreateReport(ctx context.Context, report *scripts.Report) (err error) {
	err = global.GVA_DB.Create(report).Error
	return err
}

// DeleteReport 删除reports表记录
// Author [yourname](https://github.com/yourname)
func (reportService *ReportService) DeleteReport(ctx context.Context, id string) (err error) {
	err = global.GVA_DB.Delete(&scripts.Report{}, "id = ?", id).Error
	return err
}

// DeleteReportByIds 批量删除reports表记录
// Author [yourname](https://github.com/yourname)
func (reportService *ReportService) DeleteReportByIds(ctx context.Context, ids []string) (err error) {
	err = global.GVA_DB.Delete(&[]scripts.Report{}, "id in ?", ids).Error
	return err
}

// UpdateReport 更新reports表记录
// Author [yourname](https://github.com/yourname)
func (reportService *ReportService) UpdateReport(ctx context.Context, report scripts.Report) (err error) {
	err = global.GVA_DB.Model(&scripts.Report{}).Where("id = ?", report.Id).Updates(&report).Error
	return err
}

// GetReport 根据id获取reports表记录
// Author [yourname](https://github.com/yourname)
func (reportService *ReportService) GetReport(ctx context.Context, id string) (report scripts.Report, err error) {
	err = global.GVA_DB.Where("id = ?", id).First(&report).Error
	return
}

// GetReportInfoList 分页获取reports表记录
// Author [yourname](https://github.com/yourname)
func (reportService *ReportService) GetReportInfoList(ctx context.Context, info scriptsReq.ReportSearch) (list []scripts.Report, total int64, err error) {
	limit := info.PageSize
	offset := info.PageSize * (info.Page - 1)
	// 创建db
	db := global.GVA_DB.Model(&scripts.Report{})
	var reports []scripts.Report
	// 如果有条件搜索 下方会自动创建搜索语句

	if info.Id != nil {
		db = db.Where("id = ?", *info.Id)
	}
	if info.ReportType != nil {
		db = db.Where("report_type = ?", *info.ReportType)
	}
	if info.TargetId != nil {
		db = db.Where("target_id = ?", *info.TargetId)
	}
	if info.UserId != nil {
		db = db.Where("user_id = ?", *info.UserId)
	}
	if info.ReasonId != nil {
		db = db.Where("reason_id = ?", *info.ReasonId)
	}
	if info.ReasonText != nil && *info.ReasonText != "" {
		db = db.Where("reason_text LIKE ?", "%"+*info.ReasonText+"%")
	}
	if info.Content != nil && *info.Content != "" {
		db = db.Where("content LIKE ?", "%"+*info.Content+"%")
	}
	if info.Status != nil {
		db = db.Where("status = ?", *info.Status)
	}
	if info.Tag != nil && *info.Tag != "" {
		db = db.Where("tag LIKE ?", "%"+*info.Tag+"%")
	}
	if info.Reply != nil && *info.Reply != "" {
		db = db.Where("reply LIKE ?", "%"+*info.Reply+"%")
	}
	if info.StartCreatedAt != nil && info.EndCreatedAt != nil {
		db = db.Where("created_at BETWEEN ? AND ? ", info.StartCreatedAt, info.EndCreatedAt)
	}
	err = db.Count(&total).Error
	if err != nil {
		return
	}
	var OrderStr string
	orderMap := make(map[string]bool)
	orderMap["id"] = true
	orderMap["report_type"] = true
	orderMap["reason_id"] = true
	orderMap["created_at"] = true
	if orderMap[info.Sort] {
		OrderStr = info.Sort
		if info.Order == "descending" {
			OrderStr = OrderStr + " desc"
		}
		db = db.Order(OrderStr)
	} else {
		db = db.Order("created_at desc")
	}

	if limit != 0 {
		db = db.Limit(limit).Offset(offset)
	}

	err = db.Find(&reports).Error
	return reports, total, err
}
func (reportService *ReportService) GetReportPublic(ctx context.Context) {
	// 此方法为获取数据源定义的数据
	// 请自行实现
}

// AuditReport 审核举报
func (reportService *ReportService) AuditReport(ctx context.Context, req scriptsReq.ReportAuditReq) (err error) {
	// 构建更新数据
	updateData := map[string]interface{}{
		"status": req.Status,
		"reply":  req.Reply,
	}

	// 如果是不通过状态且有标签，则更新标签
	if req.Status == 1 && len(req.Tags) > 0 {
		tagStr := strings.Join(req.Tags, ",")
		updateData["tag"] = tagStr
	}

	// 更新举报记录
	err = global.GVA_DB.Model(&scripts.Report{}).Where("id = ?", req.Id).Updates(updateData).Error
	if err != nil {
		return err
	}

	// 如果审核不通过，需要调用svcscript服务处理被举报内容
	if req.Status == 1 {
		// 先获取举报详情
		var report scripts.Report
		err = global.GVA_DB.Where("id = ?", req.Id).First(&report).Error
		if err != nil {
			return err
		}

		// 根据举报类型处理被举报内容
		err = reportService.handleReportedContent(ctx, report)
		if err != nil {
			return err
		}
	}

	return nil
}

// handleReportedContent 处理被举报内容
func (reportService *ReportService) handleReportedContent(ctx context.Context, report scripts.Report) error {
	// 这里需要调用svcscript服务将被举报内容状态修改为"人审拒绝"
	// 根据举报类型调用不同的审核接口

	switch *report.ReportType {
	case 1: // 剧本举报
		// 调用剧本审核接口，将状态设为人审拒绝
		return reportService.auditScript(ctx, *report.TargetId, false)
	case 2: // 配音举报
		// 调用配音审核接口，将状态设为人审拒绝
		return reportService.auditDubbing(ctx, *report.TargetId, false)
	case 3: // 评论举报
		// 调用评论审核接口，将状态设为人审拒绝
		return reportService.auditComment(ctx, *report.TargetId, false)
	case 4: // 用户举报
		// 用户举报暂时只更新举报状态，不处理用户
		return nil
	default:
		return fmt.Errorf("unknown report type: %d", *report.ReportType)
	}
}

// auditScript 审核剧本
func (reportService *ReportService) auditScript(ctx context.Context, scriptID int64, approved bool) error {
	// 调用svcscript服务的剧本审核接口
	reviewStatus := svcscript.ReviewStatus_REVIEW_STATUS_MANUAL_REJECTED
	if approved {
		reviewStatus = svcscript.ReviewStatus_REVIEW_STATUS_MANUAL_APPROVED
	}

	resp, err := svcmgr.ScriptClient().AuditScript(ctx, &svcscript.AuditScriptReq{
		ScriptId:     scriptID,
		ReviewStatus: reviewStatus,
		RejectReason: "举报审核通过，内容违规",
	})
	if err != nil {
		logger.Errorf("auditScript failed, err: %v, scriptId:%v, resp:%v", err, scriptID, resp)
		return fmt.Errorf("审核剧本失败: %v", err)
	}

	return nil
}

// auditDubbing 审核配音
func (reportService *ReportService) auditDubbing(ctx context.Context, dubbingID int64, approved bool) error {
	// 调用svcscript服务的配音审核接口
	reviewStatus := svcscript.ReviewStatus_REVIEW_STATUS_MANUAL_REJECTED
	if approved {
		reviewStatus = svcscript.ReviewStatus_REVIEW_STATUS_MANUAL_APPROVED
	}

	resp, err := svcmgr.ScriptClient().AdminAuditDubbing(ctx, &svcscript.AdminAuditDubbingReq{
		DubbingId:    dubbingID,
		ReviewStatus: reviewStatus,
		RejectReason: "举报审核通过，内容违规",
	})
	if err != nil {
		logger.Errorf("auditDubbing failed, err: %v, dubbingId:%v, resp:%v", err, dubbingID, resp)
		return fmt.Errorf("审核配音失败: %v", err)
	}

	return nil
}

// auditComment 审核评论
func (reportService *ReportService) auditComment(ctx context.Context, commentID int64, approved bool) error {
	// 调用svcscript服务的评论审核接口
	reviewStatus := svcscript.ReviewStatus_REVIEW_STATUS_MANUAL_REJECTED
	if approved {
		reviewStatus = svcscript.ReviewStatus_REVIEW_STATUS_MANUAL_APPROVED
	}

	resp, err := svcmgr.ScriptClient().AdminAuditComment(ctx, &svcscript.AdminAuditCommentReq{
		CommentId:    commentID,
		ReviewStatus: reviewStatus,
		RejectReason: "举报审核通过，内容违规",
	})
	if err != nil {
		logger.Errorf("auditComment failed, err: %v, commentId:%v, resp:%v", err, commentID, resp)
		return fmt.Errorf("审核评论失败: %v", err)
	}

	return nil
}

// GetReportContentDetail 获取举报内容详情
func (reportService *ReportService) GetReportContentDetail(ctx context.Context, req scriptsReq.ReportContentDetailReq) (interface{}, error) {
	// 参数验证
	if req.ReportType == nil || req.TargetId == nil {
		return nil, fmt.Errorf("举报类型和目标ID不能为空")
	}

	// 根据举报类型和目标ID获取被举报内容的详情
	switch *req.ReportType {
	case 1: // 剧本举报
		return reportService.getScriptDetail(ctx, *req.TargetId)
	case 2: // 配音举报
		return reportService.getDubbingDetail(ctx, *req.TargetId)
	case 3: // 评论举报
		return reportService.getCommentDetail(ctx, *req.TargetId)
	case 4: // 用户举报
		return reportService.getUserDetail(ctx, *req.TargetId)
	default:
		return nil, fmt.Errorf("unknown report type: %d", *req.ReportType)
	}
}

// getScriptDetail 获取剧本详情
func (reportService *ReportService) getScriptDetail(ctx context.Context, scriptID int64) (interface{}, error) {
	// 调用svcscript服务获取剧本详情
	resp, err := svcmgr.ScriptClient().GetScriptDetail(ctx, &svcscript.GetScriptDetailReq{
		ScriptId: scriptID,
		UserId:   0, // 管理员查看，不需要用户ID
	})
	if err != nil {
		logger.Errorf("getScriptDetail failed, err: %v, scriptId:%v", err, scriptID)
		// 优化错误处理，提供更友好的错误信息
		if strings.Contains(err.Error(), "not found") || strings.Contains(err.Error(), "NotFound") {
			return nil, fmt.Errorf("剧本信息不存在或已被删除")
		}
		return nil, fmt.Errorf("获取剧本详情失败，请稍后重试")
	}

	script := resp.GetData()

	// 处理封面URL，确保返回完整的URL用于前端展示
	coverUrl := script.GetCover()
	if coverUrl != "" {
		// 使用admin服务的工具函数处理图片URL
		coverUrl = reportService.fillImageUrl(coverUrl)
	}

	// 处理台词列表
	lines := make([]map[string]interface{}, 0)
	for _, line := range script.GetLines() {
		// 获取角色信息
		characterName := "未知角色"
		for _, character := range script.GetCharacters() {
			if character.GetId() == line.GetCharacterId() {
				characterName = character.GetName()
				break
			}
		}

		lines = append(lines, map[string]interface{}{
			"id":             line.GetId(),
			"character_id":   line.GetCharacterId(),
			"character_name": characterName,
			"content":        line.GetContent(),
			"order":          line.GetSort(), // 使用GetSort()而不是GetOrder()
		})
	}

	return map[string]interface{}{
		"type":       "script",
		"id":         scriptID,
		"title":      script.GetTitle(),
		"cover_url":  coverUrl,
		"status":     script.GetReviewStatus(),
		"created_at": script.GetCreatedAt(),
		"lines":      lines, // 添加台词列表
	}, nil
}

// getDubbingDetail 获取配音详情
func (reportService *ReportService) getDubbingDetail(ctx context.Context, dubbingID int64) (interface{}, error) {
	// 由于svcscript服务没有直接获取单个配音详情的接口，直接查询数据库
	var dubbing scripts.Dubbing
	err := global.GVA_DB.Table(dubbing.TableName()).Where("id = ?", dubbingID).First(&dubbing).Error
	if err != nil {
		logger.Errorf("getDubbingDetail query failed, err: %v, dubbingId:%v", err, dubbingID)
		// 优化错误处理
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, fmt.Errorf("配音信息不存在或已被删除")
		}
		return nil, fmt.Errorf("获取配音详情失败，请稍后重试")
	}

	// 查询关联的剧本信息获取剧本标题
	var script scripts.Script
	scriptTitle := ""
	if dubbing.ScriptId != nil {
		err = global.GVA_DB.Table(script.TableName()).Where("id = ?", *dubbing.ScriptId).Select("title").First(&script).Error
		if err == nil && script.Title != nil {
			scriptTitle = *script.Title
		}
	}

	// 查询对应的台词信息
	var line scripts.Line
	lineInfo := map[string]interface{}{
		"character_name": "未知角色",
		"content":        "台词内容不存在",
	}

	if dubbing.LineId != nil {
		err = global.GVA_DB.Table(line.TableName()).Where("id = ?", *dubbing.LineId).First(&line).Error
		if err == nil {
			// 查询角色信息
			var character characters.Character
			characterName := "未知角色"
			if line.CharacterId != nil {
				err = global.GVA_DB.Table(character.TableName()).Where("id = ?", *line.CharacterId).Select("name").First(&character).Error
				if err == nil && character.Name != nil {
					characterName = *character.Name
				}
			}

			// 安全地获取台词内容
			content := ""
			if line.Content != nil {
				content = *line.Content
			}

			// 安全地获取排序值
			order := 0
			if line.Sort != nil {
				order = *line.Sort
			}

			lineInfo = map[string]interface{}{
				"id":             line.Id,
				"character_id":   line.CharacterId,
				"character_name": characterName,
				"content":        content,
				"order":          order,
			}
		}
	}

	// 处理音频URL，确保返回完整的URL
	audioUrl := ""
	if dubbing.DubbedUrl != nil && *dubbing.DubbedUrl != "" {
		audioUrl = reportService.fillAudioUrl(*dubbing.DubbedUrl)
	}

	// 获取配音时长
	duration := int32(0)
	if dubbing.DubbedDuration != nil {
		duration = int32(*dubbing.DubbedDuration)
	}

	return map[string]interface{}{
		"type":          "dubbing",
		"id":            dubbingID,
		"script_title":  scriptTitle,
		"audio_url":     audioUrl,
		"duration":      duration, // 配音时长（毫秒）
		"status":        dubbing.Status,
		"review_status": dubbing.ReviewStatus,
		"created_at":    dubbing.CreatedAt,
		"line":          lineInfo, // 添加对应的台词信息
	}, nil
}

// getCommentDetail 获取评论详情
func (reportService *ReportService) getCommentDetail(ctx context.Context, commentID int64) (interface{}, error) {
	// 由于svcscript服务没有直接获取单个评论详情的接口，直接查询数据库
	var comment scripts.Comment
	err := global.GVA_DB.Table(comment.TableName()).Where("id = ?", commentID).First(&comment).Error
	if err != nil {
		logger.Errorf("getCommentDetail query failed, err: %v, commentId:%v", err, commentID)
		// 优化错误处理
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, fmt.Errorf("评论信息不存在或已被删除")
		}
		return nil, fmt.Errorf("获取评论详情失败，请稍后重试")
	}

	// 查询关联的剧本信息获取剧本标题
	var script scripts.Script
	scriptTitle := ""
	if comment.ScriptId != nil {
		err = global.GVA_DB.Table(script.TableName()).Where("id = ?", *comment.ScriptId).Select("title").First(&script).Error
		if err == nil && script.Title != nil {
			scriptTitle = *script.Title
		}
	}

	// 如果评论关联了配音，查询配音对应的台词信息
	var lineInfo map[string]interface{}
	if comment.DubbingId != nil && *comment.DubbingId > 0 {
		// 通过配音ID查询台词信息
		var dubbing scripts.Dubbing
		err = global.GVA_DB.Table(dubbing.TableName()).Where("id = ?", *comment.DubbingId).First(&dubbing).Error
		if err == nil && dubbing.LineId != nil {
			var line scripts.Line
			err = global.GVA_DB.Table(line.TableName()).Where("id = ?", *dubbing.LineId).First(&line).Error
			if err == nil {
				// 查询角色信息
				var character characters.Character
				characterName := "未知角色"
				if line.CharacterId != nil {
					err = global.GVA_DB.Table(character.TableName()).Where("id = ?", *line.CharacterId).Select("name").First(&character).Error
					if err == nil && character.Name != nil {
						characterName = *character.Name
					}
				}

				// 安全地获取台词内容
				content := ""
				if line.Content != nil {
					content = *line.Content
				}

				// 安全地获取排序值
				order := 0
				if line.Sort != nil {
					order = *line.Sort
				}

				lineInfo = map[string]interface{}{
					"id":             line.Id,
					"character_id":   line.CharacterId,
					"character_name": characterName,
					"content":        content,
					"order":          order,
				}
			}
		}
	}

	// 处理评论内容
	content := ""
	if comment.Content != nil {
		content = *comment.Content
	}

	// 处理内容类型
	contentType := 1 // 默认文本类型
	if comment.ContentType != nil {
		contentType = *comment.ContentType
	}

	// 处理语音URL（如果是语音评论）
	audioUrl := ""
	if comment.VoiceUrl != nil && *comment.VoiceUrl != "" {
		audioUrl = reportService.fillAudioUrl(*comment.VoiceUrl)
	}

	// 获取语音时长
	voiceDuration := int32(0)
	if comment.VoiceDuration != nil {
		voiceDuration = int32(*comment.VoiceDuration)
	}

	result := map[string]interface{}{
		"type":           "comment",
		"id":             commentID,
		"script_title":   scriptTitle,
		"content":        content,
		"content_type":   contentType,   // 1-文本，2-语音
		"audio_url":      audioUrl,      // 语音评论的音频地址
		"voice_duration": voiceDuration, // 语音时长（毫秒）
		"status":         comment.Status,
		"review_status":  comment.ReviewStatus,
		"created_at":     comment.CreatedAt,
	}

	// 如果有关联的台词信息，添加到结果中
	if lineInfo != nil {
		result["line"] = lineInfo
	}

	return result, nil
}

// getUserDetail 获取用户详情
func (reportService *ReportService) getUserDetail(ctx context.Context, userID int64) (interface{}, error) {
	// 调用svcaccount服务获取用户详情
	resp, err := svcmgr.AccountClient().GetUserInfo(ctx, &svcaccount.GetUserInfoReq{
		UserId: userID,
	})
	if err != nil {
		logger.Errorf("getUserDetail failed, err: %v, userId:%v", err, userID)
		// 优化错误处理
		if strings.Contains(err.Error(), "not found") || strings.Contains(err.Error(), "NotFound") {
			return nil, fmt.Errorf("用户信息不存在或已被删除")
		}
		return nil, fmt.Errorf("获取用户详情失败，请稍后重试")
	}

	user := resp.GetData()
	return map[string]interface{}{
		"type":     "user",
		"id":       userID,
		"nickname": user.GetNickname(),
		"avatar":   user.GetAvatar(),
	}, nil
}

// fillImageUrl 处理图片URL，确保返回完整的URL
func (reportService *ReportService) fillImageUrl(imageUrl string) string {
	return utils.FillImageUrl(imageUrl)
}

// fillAudioUrl 处理音频URL，确保返回完整的URL
func (reportService *ReportService) fillAudioUrl(audioUrl string) string {
	return utils.FillAudioUrl(audioUrl)
}
