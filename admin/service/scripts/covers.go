package scripts

import (
	"context"

	"github.com/flipped-aurora/gin-vue-admin/server/global"
	"github.com/flipped-aurora/gin-vue-admin/server/model/scripts"
	scriptsReq "github.com/flipped-aurora/gin-vue-admin/server/model/scripts/request"
)

type CoverService struct{}

// CreateCover 创建covers表记录
// Author [yourname](https://github.com/yourname)
func (coverService *CoverService) CreateCover(ctx context.Context, cover *scripts.Cover) (err error) {
	err = global.GVA_DB.Create(cover).Error
	return err
}

// DeleteCover 软删除Cover表记录
// Author [yourname](https://github.com/yourname)
func (coverService *CoverService) DeleteCover(ctx context.Context, id string) (err error) {
	// 软删除：更新status字段为2（删除状态）
	err = global.GVA_DB.Model(&scripts.Cover{}).Where("id = ?", id).Update("status", 2).Error
	return err
}

// DeleteCoverByIds 批量软删除Cover表记录
// Author [yourname](https://github.com/yourname)
func (coverService *CoverService) DeleteCoverByIds(ctx context.Context, ids []string) (err error) {
	// 软删除：更新status字段为2（删除状态）
	err = global.GVA_DB.Model(&scripts.Cover{}).Where("id in ?", ids).Update("status", 2).Error
	return err
}

// UpdateCover 更新covers表记录
// Author [yourname](https://github.com/yourname)
func (coverService *CoverService) UpdateCover(ctx context.Context, cover scripts.Cover) (err error) {
	err = global.GVA_DB.Model(&scripts.Cover{}).Where("id = ?", cover.Id).Updates(&cover).Error
	return err
}

// GetCover 根据id获取Cover表记录
// Author [yourname](https://github.com/yourname)
func (coverService *CoverService) GetCover(ctx context.Context, id string) (cover scripts.Cover, err error) {
	// 添加status=1条件，只获取未删除的记录
	err = global.GVA_DB.Where("id = ? AND status = 1", id).First(&cover).Error
	return
}

// GetCoverInfoList 分页获取Cover表记录
// Author [yourname](https://github.com/yourname)
func (coverService *CoverService) GetCoverInfoList(ctx context.Context, info scriptsReq.CoverSearch) (list []*scripts.Cover, total int64, err error) {
	limit := info.PageSize
	offset := info.PageSize * (info.Page - 1)
	// 创建db
	db := global.GVA_DB.Model(&scripts.Cover{}).Where("status = 1")

	var covers []*scripts.Cover
	// 如果有条件搜索 下方会自动创建搜索语句

	if info.Description != nil && *info.Description != "" {
		db = db.Where("description LIKE ?", "%"+*info.Description+"%")
	}
	if info.Type != nil {
		db = db.Where("type = ?", *info.Type)
	}
	if info.StartCreatedAt != nil && info.EndCreatedAt != nil {
		db = db.Where("created_at BETWEEN ? AND ? ", info.StartCreatedAt, info.EndCreatedAt)
	}
	err = db.Count(&total).Error
	if err != nil {
		return
	}
	var OrderStr string
	orderMap := make(map[string]bool)
	orderMap["id"] = true
	orderMap["status"] = true
	orderMap["type"] = true
	orderMap["sort"] = true
	orderMap["created_at"] = true
	if orderMap[info.Sort] {
		OrderStr = info.Sort
		if info.Order == "descending" {
			OrderStr = OrderStr + " desc"
		}
		db = db.Order(OrderStr)
	} else {
		db = db.Order("sort desc, created_at desc")
	}

	if limit != 0 {
		db = db.Limit(limit).Offset(offset)
	}

	err = db.Find(&covers).Error
	return covers, total, err
}
func (coverService *CoverService) GetCoverPublic(ctx context.Context) {
	// 此方法为获取数据源定义的数据
	// 请自行实现
}
