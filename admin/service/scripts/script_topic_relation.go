package scripts

import (
	"context"
	"github.com/flipped-aurora/gin-vue-admin/server/global"
	"github.com/flipped-aurora/gin-vue-admin/server/model/scripts"
	scriptsReq "github.com/flipped-aurora/gin-vue-admin/server/model/scripts/request"
)

type ScriptTopicRelationService struct{}

// CreateScriptTopicRelation 创建scriptTopicRelation表记录
// Author [yourname](https://github.com/yourname)
func (scriptTopicRelationService *ScriptTopicRelationService) CreateScriptTopicRelation(ctx context.Context, scriptTopicRelation *scripts.ScriptTopicRelation) (err error) {
	err = global.GVA_DB.Create(scriptTopicRelation).Error
	return err
}

// DeleteScriptTopicRelation 删除scriptTopicRelation表记录
// Author [yourname](https://github.com/yourname)
func (scriptTopicRelationService *ScriptTopicRelationService) DeleteScriptTopicRelation(ctx context.Context, id string) (err error) {
	err = global.GVA_DB.Model(&scripts.ScriptTopicRelation{}).Where("id = ?", id).Update("status", 2).Error
	return err
}

// DeleteScriptTopicRelationByIds 批量删除scriptTopicRelation表记录
// Author [yourname](https://github.com/yourname)
func (scriptTopicRelationService *ScriptTopicRelationService) DeleteScriptTopicRelationByIds(ctx context.Context, ids []string) (err error) {
	err = global.GVA_DB.Model(&scripts.ScriptTopicRelation{}).Where("id in ?", ids).Update("status", 2).Error
	return err
}

// UpdateScriptTopicRelation 更新scriptTopicRelation表记录
// Author [yourname](https://github.com/yourname)
func (scriptTopicRelationService *ScriptTopicRelationService) UpdateScriptTopicRelation(ctx context.Context, scriptTopicRelation scripts.ScriptTopicRelation) (err error) {
	err = global.GVA_DB.Model(&scripts.ScriptTopicRelation{}).Where("id = ?", scriptTopicRelation.Id).Updates(&scriptTopicRelation).Error
	return err
}

// GetScriptTopicRelation 根据id获取scriptTopicRelation表记录
// Author [yourname](https://github.com/yourname)
func (scriptTopicRelationService *ScriptTopicRelationService) GetScriptTopicRelation(ctx context.Context, id string) (scriptTopicRelation scripts.ScriptTopicRelation, err error) {
	err = global.GVA_DB.Where("id = ? AND status = 1", id).First(&scriptTopicRelation).Error
	return
}

// GetScriptTopicRelationInfoList 分页获取scriptTopicRelation表记录
// Author [yourname](https://github.com/yourname)
func (scriptTopicRelationService *ScriptTopicRelationService) GetScriptTopicRelationInfoList(ctx context.Context, info scriptsReq.ScriptTopicRelationSearch) (list []*scripts.ScriptTopicRelation, total int64, err error) {
	limit := info.PageSize
	offset := info.PageSize * (info.Page - 1)
	// 创建db
	db := global.GVA_DB.Model(&scripts.ScriptTopicRelation{}).Where("status = 1")
	var scriptTopicRelations []*scripts.ScriptTopicRelation
	// 如果有条件搜索 下方会自动创建搜索语句

	if info.Id != nil {
		db = db.Where("id = ?", *info.Id)
	}
	if info.ScriptId != nil {
		db = db.Where("script_id = ?", *info.ScriptId)
	}
	if info.TopicId != nil {
		db = db.Where("topic_id = ?", *info.TopicId)
	}
	if info.StartCreatedAt != nil && info.EndCreatedAt != nil {
		db = db.Where("created_at BETWEEN ? AND ? ", info.StartCreatedAt, info.EndCreatedAt)
	}
	err = db.Count(&total).Error
	if err != nil {
		return
	}

	if limit != 0 {
		db = db.Limit(limit).Offset(offset)
	}

	err = db.Find(&scriptTopicRelations).Error
	return scriptTopicRelations, total, err
}
func (scriptTopicRelationService *ScriptTopicRelationService) GetScriptTopicRelationPublic(ctx context.Context) {
	// 此方法为获取数据源定义的数据
	// 请自行实现
}
