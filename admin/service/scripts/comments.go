package scripts

import (
	"context"
	"errors"
	"github.com/flipped-aurora/gin-vue-admin/server/global"
	"github.com/flipped-aurora/gin-vue-admin/server/model/scripts"
	scriptsReq "github.com/flipped-aurora/gin-vue-admin/server/model/scripts/request"
	"gorm.io/gorm"
	"new-gitlab.xunlei.cn/vcproject/backends/baselib/logger"
	"new-gitlab.xunlei.cn/vcproject/backends/proto/api/svcscript"
	"new-gitlab.xunlei.cn/vcproject/backends/proto/svcmgr"
	"strconv"
	"sync"
)

type CommentService struct{}

// CreateComment 创建comments表记录
// Author [yourname](https://github.com/yourname)
func (commentService *CommentService) CreateComment(ctx context.Context, comment *scripts.Comment) (err error) {
	err = global.GVA_DB.Create(comment).Error
	return err
}

// DeleteComment 软删除Comment表记录
// Author [yourname](https://github.com/yourname)
func (commentService *CommentService) DeleteComment(ctx context.Context, id string) (err error) {
	commentId, err := strconv.ParseInt(id, 10, 64)
	if err != nil {
		return err
	}
	resp, err := svcmgr.ScriptClient().AdminDeleteComment(ctx, &svcscript.AdminDeleteCommentReq{CommentId: commentId})
	if err != nil {
		logger.Errorf("AdminDeleteComment failed, err: %v, commentId:%v, resp:%v", err, commentId, resp)
		return err
	}
	return err
}

// DeleteCommentByIds 批量软删除Comment表记录
// Author [yourname](https://github.com/yourname)
func (commentService *CommentService) DeleteCommentByIds(ctx context.Context, ids []string) (err error) {
	var wg sync.WaitGroup
	errChan := make(chan error, len(ids))

	semaphore := make(chan struct{}, 10)

	for _, id := range ids {
		wg.Add(1)

		idCopy := id

		go func() {
			defer wg.Done()

			semaphore <- struct{}{}
			defer func() { <-semaphore }()

			err := commentService.DeleteComment(ctx, idCopy)
			if err != nil {
				logger.Errorf("DeleteComment failed, err: %v, commentID: %v", err, idCopy)
				errChan <- err
			}
		}()
	}

	wg.Wait()
	close(errChan)

	// 检查是否有错误
	for e := range errChan {
		if e != nil {
			return e // 返回第一个遇到的错误
		}
	}

	return nil
}

// UpdateComment 更新comments表记录
// Author [yourname](https://github.com/yourname)
func (commentService *CommentService) UpdateComment(ctx context.Context, comment scripts.Comment) (err error) {
	err = global.GVA_DB.Model(&scripts.Comment{}).Where("id = ?", comment.Id).Updates(&comment).Error
	return err
}

// GetComment 根据id获取Comment表记录
// Author [yourname](https://github.com/yourname)
func (commentService *CommentService) GetComment(ctx context.Context, id string) (comment scripts.Comment, err error) {
	// 添加status=1条件，只获取未删除的记录
	err = global.GVA_DB.Where("id = ? AND status = 1", id).First(&comment).Error
	return
}

// GetCommentInfoList 分页获取Comment表记录
// Author [yourname](https://github.com/yourname)
func (commentService *CommentService) GetCommentInfoList(ctx context.Context, info scriptsReq.CommentSearch) (list []*scripts.Comment, total int64, err error) {
	limit := info.PageSize
	offset := info.PageSize * (info.Page - 1)

	// 创建db，如果需要关联剧本表查询剧本标题，则使用JOIN
	var db *gorm.DB
	if info.ScriptTitle != nil && *info.ScriptTitle != "" {
		// 关联剧本表查询
		db = global.GVA_DB.Table("vc_script.`comments` AS c").
			Joins("LEFT JOIN vc_script.`scripts` AS s ON c.script_id = s.id").
			Where("s.title LIKE ?", "%"+*info.ScriptTitle+"%")
	} else {
		db = global.GVA_DB.Model(&scripts.Comment{})
	}

	// 默认只查询未删除的记录（status=1）
	if info.Status == nil {
		if info.ScriptTitle != nil && *info.ScriptTitle != "" {
			db = db.Where("c.status = 1")
		} else {
			db = db.Where("status = 1")
		}
	}

	var comments []*scripts.Comment
	// 如果有条件搜索 下方会自动创建搜索语句

	// 根据是否关联查询调整字段前缀
	fieldPrefix := ""
	if info.ScriptTitle != nil && *info.ScriptTitle != "" {
		fieldPrefix = "c."
	}

	// ID查询 - 需要特别处理，因为前端传的是int64，但数据库字段可能是int
	if info.ID != nil {
		db = db.Where(fieldPrefix+"id = ?", *info.ID)
	}
	if info.CommentType != nil {
		db = db.Where(fieldPrefix+"comment_type = ?", *info.CommentType)
	}
	if info.ParentId != nil {
		db = db.Where(fieldPrefix+"parent_id = ?", *info.ParentId)
	}
	if info.ScriptId != nil {
		db = db.Where(fieldPrefix+"script_id = ?", *info.ScriptId)
	}
	if info.DubbingId != nil {
		db = db.Where(fieldPrefix+"dubbing_id = ?", *info.DubbingId)
	}
	if info.UserId != nil {
		db = db.Where(fieldPrefix+"user_id = ?", *info.UserId)
	}
	if info.CharacterId != nil {
		db = db.Where(fieldPrefix+"character_id = ?", *info.CharacterId)
	}
	if info.ContentType != nil {
		db = db.Where(fieldPrefix+"content_type = ?", *info.ContentType)
	}
	if info.AsrStatus != nil {
		db = db.Where(fieldPrefix+"asr_status = ?", *info.AsrStatus)
	}
	if info.Status != nil {
		db = db.Where(fieldPrefix+"status = ?", *info.Status)
	}
	if info.ReviewStatus != nil {
		db = db.Where(fieldPrefix+"review_status = ?", *info.ReviewStatus)
	}
	if info.Content != nil && *info.Content != "" {
		db = db.Where(fieldPrefix+"content LIKE ?", "%"+*info.Content+"%")
	}
	if info.StartCreatedAt != nil && info.EndCreatedAt != nil {
		db = db.Where(fieldPrefix+"created_at BETWEEN ? AND ? ", info.StartCreatedAt, info.EndCreatedAt)
	}

	err = db.Count(&total).Error
	if err != nil {
		return
	}

	if limit != 0 {
		db = db.Limit(limit).Offset(offset)
	}

	// 排序处理
	orderBy := fieldPrefix + "created_at DESC"
	if info.Sort != "" {
		direction := "ASC"
		if info.Order == "descending" {
			direction = "DESC"
		}
		orderBy = fieldPrefix + info.Sort + " " + direction
	}
	db = db.Order(orderBy)

	// 如果是关联查询，需要明确选择字段
	if info.ScriptTitle != nil && *info.ScriptTitle != "" {
		// 关联查询时，只选择评论表的字段
		db = db.Select("c.*")
	}

	err = db.Find(&comments).Error

	// 批量查询剧本标题（无论是否关联查询都需要填充）
	commentService.fillScriptTitles(ctx, comments)

	return comments, total, err
}
func (commentService *CommentService) GetCommentPublic(ctx context.Context) {
	// 此方法为获取数据源定义的数据
	// 请自行实现
}

// SetCommentTop 设置评论置顶状态
// Author [yourname](https://github.com/yourname)
func (commentService *CommentService) SetCommentTop(ctx context.Context, id int64, isTop bool) (err error) {
	// 检查是否为一级评论（只有一级评论才能置顶）
	var comment scripts.Comment
	err = global.GVA_DB.Where("id = ?", id).First(&comment).Error
	if err != nil {
		return err
	}

	if comment.ParentId != nil && *comment.ParentId != 0 {
		return errors.New("只有一级评论才能置顶")
	}

	// 检查评论状态（已删除或被拒绝的评论不可置顶）
	if isTop {
		// 检查评论是否已删除
		if comment.Status != nil && *comment.Status == 2 { // 2表示删除状态
			return errors.New("已删除的评论不可置顶")
		}

		// 检查评论是否被机审拒绝或人审拒绝
		if comment.ReviewStatus != nil && (*comment.ReviewStatus == 3 || *comment.ReviewStatus == 5) { // 3:机审拒绝, 5:人审拒绝
			return errors.New("被拒绝的评论不可置顶")
		}
	}

	// 使用事务确保同一剧本下只有一个置顶评论
	return global.GVA_DB.Transaction(func(tx *gorm.DB) error {
		if isTop {
			// 如果要置顶，先取消该剧本下所有其他评论的置顶状态
			err := tx.Model(&scripts.Comment{}).
				Where("script_id = ? AND id != ? AND is_top = ? AND (parent_id IS NULL OR parent_id = 0)", comment.ScriptId, id, true).
				Update("is_top", false).Error
			if err != nil {
				return err
			}
		}

		// 设置当前评论的置顶状态
		err := tx.Model(&scripts.Comment{}).Where("id = ?", id).Update("is_top", isTop).Error
		return err
	})
}

// fillScriptTitles 批量填充剧本标题
func (commentService *CommentService) fillScriptTitles(ctx context.Context, comments []*scripts.Comment) {
	if len(comments) == 0 {
		return
	}

	// 收集所有剧本ID
	scriptIDs := make([]int, 0)
	scriptIDMap := make(map[int]bool)
	for _, comment := range comments {
		if comment.ScriptId != nil && *comment.ScriptId > 0 {
			if !scriptIDMap[*comment.ScriptId] {
				scriptIDs = append(scriptIDs, *comment.ScriptId)
				scriptIDMap[*comment.ScriptId] = true
			}
		}
	}

	if len(scriptIDs) == 0 {
		return
	}

	// 批量查询剧本标题
	type ScriptTitle struct {
		ID    int    `gorm:"column:id"`
		Title string `gorm:"column:title"`
	}

	var scriptTitles []ScriptTitle
	err := global.GVA_DB.Table("vc_script.`scripts`").
		Select("id, title").
		Where("id IN (?)", scriptIDs).
		Find(&scriptTitles).Error

	if err != nil {
		logger.Errorf("fillScriptTitles query scripts failed: %v", err)
		return
	}

	// 创建ID到标题的映射
	titleMap := make(map[int]string)
	for _, st := range scriptTitles {
		titleMap[st.ID] = st.Title
	}

	// 填充评论的剧本标题
	for _, comment := range comments {
		if comment.ScriptId != nil && *comment.ScriptId > 0 {
			if title, exists := titleMap[*comment.ScriptId]; exists {
				comment.ScriptTitle = title
			}
		}
	}
}
