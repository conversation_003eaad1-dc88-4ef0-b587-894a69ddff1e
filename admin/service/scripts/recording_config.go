package scripts

import (
	"context"
	"fmt"
	"strings"

	"github.com/flipped-aurora/gin-vue-admin/server/global"
	"github.com/flipped-aurora/gin-vue-admin/server/model/scripts"
	"go.uber.org/zap"
	"new-gitlab.xunlei.cn/vcproject/backends/baselib/logger"
	"new-gitlab.xunlei.cn/vcproject/backends/baselib/server/env"
	"new-gitlab.xunlei.cn/vcproject/backends/baselib/util"
	"new-gitlab.xunlei.cn/vcproject/backends/bizmisc/service"
)

type RecordingConfigService struct{}

var RecordingConfigServiceApp = new(RecordingConfigService)

// GetRecordingConfig 获取录音配置
// Author [yourname](https://github.com/yourname)
func (s *RecordingConfigService) GetRecordingConfig(ctx context.Context, req *scripts.GetRecordingConfigReq) (*scripts.GetRecordingConfigResp, error) {
	// 1. 构建预设名称
	presetName := fmt.Sprintf("%d_%d", req.CharacterID, req.CharacterAssetID)
	if env.IsTest() {
		presetName = fmt.Sprintf("test_%d_%d", req.CharacterID, req.CharacterAssetID)
	}

	global.GVA_LOG.Info("GetRecordingConfig",
		zap.Int64("character_id", req.CharacterID),
		zap.Int64("character_asset_id", req.CharacterAssetID),
		zap.String("scene", req.Scene),
		zap.String("preset_name", presetName))

	// 2. 获取RVC预设配置
	rvcSvc := service.GetRVCService()
	presetResp, err := rvcSvc.GetPresetByName(ctx, presetName)
	if err != nil {
		logger.Errorf("GetPresetByName error: %v, presetName: %s", err, presetName)
		global.GVA_LOG.Error("GetPresetByName failed", zap.Error(err), zap.String("preset_name", presetName))
		return nil, fmt.Errorf("获取角色预设配置失败: %v", err)
	}

	presetConfig := presetResp.Data
	if presetConfig == nil || len(presetConfig.RvcName) == 0 || len(presetConfig.ReferenceAudioUrl) == 0 {
		logger.Warnf("presetConfig invalid: %+s presetName=%s", util.JsonStr(presetConfig), presetName)
		global.GVA_LOG.Warn("Invalid preset config",
			zap.String("preset_config", util.JsonStr(presetConfig)),
			zap.String("preset_name", presetName))
		return nil, fmt.Errorf("角色预设配置无效")
	}

	// 3. 确定是否使用RVC变声
	useRvc := presetConfig.CascadedUseRvc

	global.GVA_LOG.Info("RVC config",
		zap.Bool("use_rvc", useRvc),
		zap.Bool("cascaded_use_rvc", presetConfig.CascadedUseRvc))

	// 4. 构建录音配置
	config := &scripts.GetRecordingConfigResp{
		// 基础参数（固定值）
		BitDepth:          32,
		Channels:          1,
		OutputBitDepth:    16,
		OutputChannels:    1,
		ChunkMs:           1000,
		UseRvc:            useRvc,
		SeedName:          presetConfig.SeedName,
		RvcName:           presetConfig.RvcName,
		ReferenceAudioUrl: presetConfig.ReferenceAudioUrl,
	}

	// 5. 设置输出采样率（根据是否使用RVC）
	config.OutputSampleRate = 16000 // 默认16kHz
	if useRvc {
		config.OutputSampleRate = 48000 // 使用RVC时为48kHz
	}

	// 6. 设置录音采样率（根据SeedName）
	seedNameLower := strings.ToLower(presetConfig.SeedName)
	switch seedNameLower {
	case "whisper_small", "tat_xlsr":
		config.SampleRate = 22050 // 22.05kHz
	case "whisper_base_f0":
		config.SampleRate = 44100 // 44.1kHz
	default:
		logger.Errorf("Unsupported seed name: %s", presetConfig.SeedName)
		global.GVA_LOG.Error("Unsupported seed name", zap.String("seed_name", presetConfig.SeedName))
		return nil, fmt.Errorf("不支持的种子名称: %s", presetConfig.SeedName)
	}

	// 7. 设置录音时长限制（根据场景）
	switch req.Scene {
	case "comment", "signature":
		config.AudioMilliDuration = 60 * 1000 // 评论/签名：60秒
	case "dubbing":
		config.AudioMilliDuration = 180 * 1000 // 配音：180秒
	default:
		config.AudioMilliDuration = 60 * 1000 // 默认：60秒
	}

	global.GVA_LOG.Info("Recording config generated",
		zap.Int64("sample_rate", config.SampleRate),
		zap.Int32("channels", config.Channels),
		zap.Int64("output_sample_rate", config.OutputSampleRate),
		zap.Int32("audio_duration_ms", config.AudioMilliDuration),
		zap.Bool("use_rvc", config.UseRvc))

	return config, nil
}

// ValidateRecordingConfigRequest 验证录音配置请求参数
func (s *RecordingConfigService) ValidateRecordingConfigRequest(req *scripts.GetRecordingConfigReq) error {
	if req.CharacterID <= 0 {
		return fmt.Errorf("角色ID必须大于0")
	}
	if req.CharacterAssetID <= 0 {
		return fmt.Errorf("角色资源包ID必须大于0")
	}

	validScenes := []string{"comment", "dubbing", "signature"}
	if !util.InStringSlice(validScenes, req.Scene) {
		return fmt.Errorf("场景必须是以下之一: %v", validScenes)
	}

	return nil
}
