package scripts

import (
	"context"
	"github.com/flipped-aurora/gin-vue-admin/server/global"
	"github.com/flipped-aurora/gin-vue-admin/server/model/scripts"
	scriptsReq "github.com/flipped-aurora/gin-vue-admin/server/model/scripts/request"
)

type TopicService struct{}

// CreateTopic 创建topics表记录
// Author [yourname](https://github.com/yourname)
func (topicService *TopicService) CreateTopic(ctx context.Context, topic *scripts.Topic) (err error) {
	err = global.GVA_DB.Create(topic).Error
	return err
}

// DeleteTopic 删除topics表记录
// Author [yourname](https://github.com/yourname)
func (topicService *TopicService) DeleteTopic(ctx context.Context, id string) (err error) {
	err = global.GVA_DB.Model(&scripts.Topic{}).Where("id = ?", id).Update("status", 2).Error
	return err
}

// DeleteTopicByIds 批量删除topics表记录
// Author [yourname](https://github.com/yourname)
func (topicService *TopicService) DeleteTopicByIds(ctx context.Context, ids []string) (err error) {
	err = global.GVA_DB.Model(&scripts.Topic{}).Where("id in ?", ids).Update("status", 2).Error
	return err
}

// UpdateTopic 更新topics表记录
// Author [yourname](https://github.com/yourname)
func (topicService *TopicService) UpdateTopic(ctx context.Context, topic scripts.Topic) (err error) {
	err = global.GVA_DB.Model(&scripts.Topic{}).Where("id = ?", topic.Id).Updates(&topic).Error
	return err
}

// GetTopic 根据id获取topics表记录
// Author [yourname](https://github.com/yourname)
func (topicService *TopicService) GetTopic(ctx context.Context, id string) (topic scripts.Topic, err error) {
	err = global.GVA_DB.Where("id = ? AND status = 1", id).First(&topic).Error
	return
}

// GetTopicInfoList 分页获取topics表记录
// Author [yourname](https://github.com/yourname)
func (topicService *TopicService) GetTopicInfoList(ctx context.Context, info scriptsReq.TopicSearch) (list []scripts.Topic, total int64, err error) {
	limit := info.PageSize
	offset := info.PageSize * (info.Page - 1)
	// 创建db
	db := global.GVA_DB.Model(&scripts.Topic{}).Where("status = 1")
	var topics []scripts.Topic
	// 如果有条件搜索 下方会自动创建搜索语句

	if info.Id != nil {
		db = db.Where("id = ?", *info.Id)
	}
	if info.Name != nil && *info.Name != "" {
		db = db.Where("name LIKE ?", "%"+*info.Name+"%")
	}
	if info.LabelId != nil {
		db = db.Where("label_id = ?", *info.LabelId)
	}
	if info.IsRecommend != nil {
		db = db.Where("is_recommend = ?", *info.IsRecommend)
	}
	if info.IsHot != nil {
		db = db.Where("is_hot = ?", *info.IsHot)
	}
	if info.StartCreatedAt != nil && info.EndCreatedAt != nil {
		db = db.Where("created_at BETWEEN ? AND ? ", info.StartCreatedAt, info.EndCreatedAt)
	}
	err = db.Count(&total).Error
	if err != nil {
		return
	}
	var OrderStr string
	orderMap := make(map[string]bool)
	orderMap["id"] = true
	orderMap["label_id"] = true
	orderMap["sort"] = true
	orderMap["recommend_sort"] = true
	orderMap["created_at"] = true
	orderMap["hot_sort"] = true
	if orderMap[info.Sort] {
		OrderStr = info.Sort
		if info.Order == "descending" {
			OrderStr = OrderStr + " desc"
		}
		db = db.Order(OrderStr)
	} else {
		db = db.Order("sort desc, created_at desc")
	}

	if limit != 0 {
		db = db.Limit(limit).Offset(offset)
	}

	err = db.Find(&topics).Error
	return topics, total, err
}
func (topicService *TopicService) GetTopicPublic(ctx context.Context) {
	// 此方法为获取数据源定义的数据
	// 请自行实现
}

// GetTopicLabels 分页获取topics labels表记录
func (topicService *TopicService) GetTopicLabels(ctx context.Context) (list []*scripts.TopicLabel, total int64, err error) {
	db := global.GVA_DB.Model(&scripts.TopicLabel{})
	var labels []*scripts.TopicLabel
	err = db.Find(&labels).Error
	return labels, total, err
}
