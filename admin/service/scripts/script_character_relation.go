package scripts

import (
	"context"
	"github.com/flipped-aurora/gin-vue-admin/server/global"
	"github.com/flipped-aurora/gin-vue-admin/server/model/scripts"
	scriptsReq "github.com/flipped-aurora/gin-vue-admin/server/model/scripts/request"
)

type ScriptCharacterRelationService struct{}

// CreateScriptCharacterRelation 创建scriptCharacterRelation表记录
// Author [yourname](https://github.com/yourname)
func (scriptCharacterRelationService *ScriptCharacterRelationService) CreateScriptCharacterRelation(ctx context.Context, scriptCharacterRelation *scripts.ScriptCharacterRelation) (err error) {
	err = global.GVA_DB.Create(scriptCharacterRelation).Error
	return err
}

// DeleteScriptCharacterRelation 删除scriptCharacterRelation表记录
// Author [yourname](https://github.com/yourname)
func (scriptCharacterRelationService *ScriptCharacterRelationService) DeleteScriptCharacterRelation(ctx context.Context, id string) (err error) {
	// 软删除：更新status字段为2（删除状态）
	err = global.GVA_DB.Model(&scripts.ScriptCharacterRelation{}).Where("id = ?", id).Update("status", 2).Error
	return err
}

// DeleteScriptCharacterRelationByIds 批量删除scriptCharacterRelation表记录
// Author [yourname](https://github.com/yourname)
func (scriptCharacterRelationService *ScriptCharacterRelationService) DeleteScriptCharacterRelationByIds(ctx context.Context, ids []string) (err error) {
	err = global.GVA_DB.Model(&scripts.ScriptCharacterRelation{}).Where("id in ?", ids).Update("status", 2).Error
	return err
}

// UpdateScriptCharacterRelation 更新scriptCharacterRelation表记录
// Author [yourname](https://github.com/yourname)
func (scriptCharacterRelationService *ScriptCharacterRelationService) UpdateScriptCharacterRelation(ctx context.Context, scriptCharacterRelation scripts.ScriptCharacterRelation) (err error) {
	err = global.GVA_DB.Model(&scripts.ScriptCharacterRelation{}).Where("id = ?", scriptCharacterRelation.Id).Updates(&scriptCharacterRelation).Error
	return err
}

// GetScriptCharacterRelation 根据id获取scriptCharacterRelation表记录
// Author [yourname](https://github.com/yourname)
func (scriptCharacterRelationService *ScriptCharacterRelationService) GetScriptCharacterRelation(ctx context.Context, id string) (scriptCharacterRelation scripts.ScriptCharacterRelation, err error) {
	err = global.GVA_DB.Where("id = ? AND status = 1", id).First(&scriptCharacterRelation).Error
	return
}

// GetScriptCharacterRelationInfoList 分页获取scriptCharacterRelation表记录
// Author [yourname](https://github.com/yourname)
func (scriptCharacterRelationService *ScriptCharacterRelationService) GetScriptCharacterRelationInfoList(ctx context.Context, info scriptsReq.ScriptCharacterRelationSearch) (list []*scripts.ScriptCharacterRelation, total int64, err error) {
	limit := info.PageSize
	offset := info.PageSize * (info.Page - 1)
	// 创建db
	db := global.GVA_DB.Model(&scripts.ScriptCharacterRelation{}).Where("status = 1")
	var scriptCharacterRelations []*scripts.ScriptCharacterRelation
	// 如果有条件搜索 下方会自动创建搜索语句

	if info.Id != nil {
		db = db.Where("id = ?", *info.Id)
	}
	if info.ScriptId != nil {
		db = db.Where("script_id = ?", *info.ScriptId)
	}
	if info.CharacterId != nil {
		db = db.Where("character_id = ?", *info.CharacterId)
	}
	if info.StartCreatedAt != nil && info.EndCreatedAt != nil {
		db = db.Where("created_at BETWEEN ? AND ? ", info.StartCreatedAt, info.EndCreatedAt)
	}
	err = db.Count(&total).Error
	if err != nil {
		return
	}
	var OrderStr string
	orderMap := make(map[string]bool)
	orderMap["line_count"] = true
	orderMap["sort"] = true
	if orderMap[info.Sort] {
		OrderStr = info.Sort
		if info.Order == "descending" {
			OrderStr = OrderStr + " desc"
		}
		db = db.Order(OrderStr)
	} else {
		db = db.Order("created_at desc")
	}

	if limit != 0 {
		db = db.Limit(limit).Offset(offset)
	}

	err = db.Find(&scriptCharacterRelations).Error
	return scriptCharacterRelations, total, err
}
func (scriptCharacterRelationService *ScriptCharacterRelationService) GetScriptCharacterRelationPublic(ctx context.Context) {
	// 此方法为获取数据源定义的数据
	// 请自行实现
}
