package scripts

import (
	"context"
	"errors"
	"strconv"
	"sync"

	"github.com/flipped-aurora/gin-vue-admin/server/global"
	"github.com/flipped-aurora/gin-vue-admin/server/model/scripts"
	scriptsReq "github.com/flipped-aurora/gin-vue-admin/server/model/scripts/request"
	"gorm.io/gorm"
	"new-gitlab.xunlei.cn/vcproject/backends/baselib/logger"
	"new-gitlab.xunlei.cn/vcproject/backends/proto/api/svcscript"
	"new-gitlab.xunlei.cn/vcproject/backends/proto/consts"
	"new-gitlab.xunlei.cn/vcproject/backends/proto/svcmgr"
)

type DubbingService struct{}

// CreateDubbing 创建dubbings表记录
// Author [yourname](https://github.com/yourname)
func (dubbingService *DubbingService) CreateDubbing(ctx context.Context, dubbing *scripts.Dubbing) (err error) {
	err = global.GVA_DB.Create(dubbing).Error
	return err
}

// DeleteDubbing 软删除Dubbing表记录
// Author [yourname](https://github.com/yourname)
func (dubbingService *DubbingService) DeleteDubbing(ctx context.Context, id string) (err error) {
	dubbingId, err := strconv.Atoi(id)
	if err != nil {
		return err
	}
	resp, err := svcmgr.ScriptClient().AdminDeleteDubbing(ctx, &svcscript.AdminDeleteDubbingReq{DubbingId: int64(dubbingId)})
	if err != nil {
		logger.Errorf("AdminDeleteDubbing failed, err: %v, dubbingId:%v, resp:%v", err, dubbingId, resp)
		return err
	}
	return err
}

// DeleteDubbingByIds 批量软删除Dubbing表记录
// Author [yourname](https://github.com/yourname)
func (dubbingService *DubbingService) DeleteDubbingByIds(ctx context.Context, ids []string) (err error) {
	var wg sync.WaitGroup
	errChan := make(chan error, len(ids))

	semaphore := make(chan struct{}, 10)

	for _, id := range ids {
		wg.Add(1)

		idCopy := id

		go func() {
			defer wg.Done()

			semaphore <- struct{}{}
			defer func() { <-semaphore }()

			err := dubbingService.DeleteDubbing(ctx, idCopy)
			if err != nil {
				logger.Errorf("DeleteDubbing failed, err: %v, dubbingId: %v", err, idCopy)
				errChan <- err
			}
		}()
	}

	wg.Wait()
	close(errChan)

	// 检查是否有错误
	for e := range errChan {
		if e != nil {
			return e // 返回第一个遇到的错误
		}
	}

	return nil
}

// UpdateDubbing 更新dubbings表记录
// Author [yourname](https://github.com/yourname)
func (dubbingService *DubbingService) UpdateDubbing(ctx context.Context, dubbing scripts.Dubbing) (err error) {
	err = global.GVA_DB.Model(&scripts.Dubbing{}).Where("id = ?", dubbing.Id).Updates(&dubbing).Error
	return err
}

// GetDubbing 根据id获取Dubbing表记录
// Author [yourname](https://github.com/yourname)
func (dubbingService *DubbingService) GetDubbing(ctx context.Context, id string) (dubbing scripts.Dubbing, err error) {
	// 添加status=1条件，只获取未删除的记录
	err = global.GVA_DB.Where("id = ? AND status = 1", id).First(&dubbing).Error
	return
}

// GetDubbingInfoList 分页获取Dubbing表记录
// Author [yourname](https://github.com/yourname)
func (dubbingService *DubbingService) GetDubbingInfoList(ctx context.Context, info scriptsReq.DubbingSearch) (list []*scripts.Dubbing, total int64, err error) {
	limit := info.PageSize
	offset := info.PageSize * (info.Page - 1)

	// 创建db，如果需要关联剧本表查询剧本标题，则使用JOIN
	var db *gorm.DB
	if info.ScriptTitle != nil && *info.ScriptTitle != "" {
		// 关联剧本表查询
		db = global.GVA_DB.Table("vc_script.`dubbings` AS d").
			Joins("LEFT JOIN vc_script.`scripts` AS s ON d.script_id = s.id").
			Where("s.title LIKE ?", "%"+*info.ScriptTitle+"%")
	} else {
		db = global.GVA_DB.Model(&scripts.Dubbing{})
	}

	// 默认只查询未删除的记录（status=1）
	if info.Status == nil {
		if info.ScriptTitle != nil && *info.ScriptTitle != "" {
			db = db.Where("d.status = 1")
		} else {
			db = db.Where("status = 1")
		}
	}

	var dubbings []*scripts.Dubbing
	// 如果有条件搜索 下方会自动创建搜索语句

	// 根据是否关联查询调整字段前缀
	fieldPrefix := ""
	if info.ScriptTitle != nil && *info.ScriptTitle != "" {
		fieldPrefix = "d."
	}

	// ID查询
	if info.ID != nil {
		db = db.Where(fieldPrefix+"id = ?", *info.ID)
	}
	if info.ScriptId != nil {
		db = db.Where(fieldPrefix+"script_id = ?", *info.ScriptId)
	}
	if info.LineId != nil {
		db = db.Where(fieldPrefix+"line_id = ?", *info.LineId)
	}
	if info.TopicId != nil {
		db = db.Where(fieldPrefix+"topic_id = ?", *info.TopicId)
	}
	if info.UserId != nil {
		db = db.Where(fieldPrefix+"user_id = ?", *info.UserId)
	}
	if info.CharacterId != nil {
		db = db.Where(fieldPrefix+"character_id = ?", *info.CharacterId)
	}
	if info.ReviewStatus != nil {
		db = db.Where(fieldPrefix+"review_status = ?", *info.ReviewStatus)
	}
	if info.Status != nil {
		db = db.Where(fieldPrefix+"status = ?", *info.Status)
	}
	if info.StartCreatedAt != nil && info.EndCreatedAt != nil {
		db = db.Where(fieldPrefix+"created_at BETWEEN ? AND ? ", info.StartCreatedAt, info.EndCreatedAt)
	}

	err = db.Count(&total).Error
	if err != nil {
		return
	}

	if limit != 0 {
		db = db.Limit(limit).Offset(offset)
	}

	// 排序处理
	orderBy := fieldPrefix + "created_at DESC"
	if info.Sort != "" {
		direction := "ASC"
		if info.Order == "descending" {
			direction = "DESC"
		}
		orderBy = fieldPrefix + info.Sort + " " + direction
	}
	db = db.Order(orderBy)

	// 如果是关联查询，需要明确选择字段
	if info.ScriptTitle != nil && *info.ScriptTitle != "" {
		// 关联查询时，只选择配音表的字段
		db = db.Select("d.*")
	}

	err = db.Find(&dubbings).Error

	// 批量查询剧本标题（无论是否关联查询都需要填充）
	dubbingService.fillScriptTitles(ctx, dubbings)

	return dubbings, total, err
}

func (dubbingService *DubbingService) GetDubbingPublic(ctx context.Context) {
	// 此方法为获取数据源定义的数据
	// 请自行实现
}

// SetDubbingTop 设置配音置顶状态
// Author [yourname](https://github.com/yourname)
func (dubbingService *DubbingService) SetDubbingTop(ctx context.Context, id int64, isTop bool) (err error) {
	// 获取配音信息
	var dubbing scripts.Dubbing
	err = global.GVA_DB.Where("id = ?", id).First(&dubbing).Error
	if err != nil {
		return err
	}

	// 检查配音状态（已删除或被拒绝的配音不可置顶）
	if isTop {
		// 检查配音是否已删除
		if dubbing.Status != nil && *dubbing.Status == 2 { // 2表示删除状态
			return errors.New("已删除的配音不可置顶")
		}

		// 检查配音是否被机审拒绝或人审拒绝
		if dubbing.ReviewStatus != nil && (*dubbing.ReviewStatus == 3 || *dubbing.ReviewStatus == 5) { // 3:机审拒绝, 5:人审拒绝
			return errors.New("被拒绝的配音不可置顶")
		}
	}

	// 使用事务确保同一台词下只有一个置顶配音
	return global.GVA_DB.Transaction(func(tx *gorm.DB) error {
		if isTop {
			// 如果要置顶，先取消该台词下所有其他配音的置顶状态
			err := tx.Model(&scripts.Dubbing{}).
				Where("line_id = ? AND id != ? AND is_top = ?", dubbing.LineId, id, true).
				Update("is_top", false).Error
			if err != nil {
				return err
			}
		}

		// 设置当前配音的置顶状态
		err := tx.Model(&scripts.Dubbing{}).Where("id = ?", id).Update("is_top", isTop).Error
		return err
	})
}

func (dubbingService *DubbingService) GetDubbingInfoByLineIds(ctx context.Context, lineIds []int64) (list []*scripts.Dubbing, err error) {
	if len(lineIds) == 0 {
		return
	}
	err = global.GVA_DB.Model(&scripts.Dubbing{}).Where("line_id in ?", lineIds).Find(&list).Error
	return
}

// fillScriptTitles 批量填充剧本标题
func (dubbingService *DubbingService) fillScriptTitles(ctx context.Context, dubbings []*scripts.Dubbing) {
	if len(dubbings) == 0 {
		return
	}

	// 收集所有剧本ID
	scriptIDs := make([]int64, 0)
	scriptIDMap := make(map[int64]bool)
	for _, dubbing := range dubbings {
		if dubbing.ScriptId != nil && *dubbing.ScriptId > 0 {
			if !scriptIDMap[*dubbing.ScriptId] {
				scriptIDs = append(scriptIDs, *dubbing.ScriptId)
				scriptIDMap[*dubbing.ScriptId] = true
			}
		}
	}

	if len(scriptIDs) == 0 {
		return
	}

	// 批量查询剧本标题
	type ScriptTitle struct {
		ID    int64  `gorm:"column:id"`
		Title string `gorm:"column:title"`
	}

	var scriptTitles []ScriptTitle
	err := global.GVA_DB.Table("vc_script.`scripts`").
		Select("id, title").
		Where("id IN (?)", scriptIDs).
		Find(&scriptTitles).Error

	if err != nil {
		logger.Errorf("fillScriptTitles query scripts failed: %v", err)
		return
	}

	// 创建ID到标题的映射
	titleMap := make(map[int64]string)
	for _, st := range scriptTitles {
		titleMap[st.ID] = st.Title
	}

	// 填充配音的剧本标题
	for _, dubbing := range dubbings {
		if dubbing.ScriptId != nil && *dubbing.ScriptId > 0 {
			if title, exists := titleMap[*dubbing.ScriptId]; exists {
				dubbing.ScriptTitle = title
			}
		}
	}
}

// ManualUploadDubbing 手动上传配音
// Author [yourname](https://github.com/yourname)
func (dubbingService *DubbingService) ManualUploadDubbing(ctx context.Context, req scriptsReq.ManualUploadDubbingReq) (err error) {
	// 调用svcscript服务的手动上传配音接口
	resp, err := svcmgr.ScriptClient().ManualUploadDubbing(ctx, &svcscript.ManualUploadDubbingReq{
		UserId:        consts.AIUserID, // 使用固定的AI用户ID
		LineId:        req.LineId,
		CharacterId:   req.CharacterId,
		DubbedUrl:     req.DubbedUrl,
		DubbedDuration: req.DubbedDuration,
	})
	if err != nil {
		logger.Errorf("ManualUploadDubbing failed, err: %v, req:%+v, resp:%+v", err, req, resp)
		return err
	}
	return nil
}
