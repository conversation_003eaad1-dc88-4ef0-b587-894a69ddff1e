package scripts

import (
	"context"
	"new-gitlab.xunlei.cn/vcproject/backends/baselib/logger"
	"new-gitlab.xunlei.cn/vcproject/backends/proto/api/svcscript"
	"new-gitlab.xunlei.cn/vcproject/backends/proto/svcmgr"
	"strconv"

	"github.com/flipped-aurora/gin-vue-admin/server/global"
	"github.com/flipped-aurora/gin-vue-admin/server/model/scripts"
	scriptsReq "github.com/flipped-aurora/gin-vue-admin/server/model/scripts/request"
)

type LineService struct{}

// CreateLine 创建lines表记录
// Author [yourname](https://github.com/yourname)
func (lineService *LineService) CreateLine(ctx context.Context, line *scripts.Line) (err error) {
	err = global.GVA_DB.Create(line).Error
	return err
}

// DeleteLine 软删除Line表记录
// Author [yourname](https://github.com/yourname)
func (lineService *LineService) DeleteLine(ctx context.Context, id string) (err error) {
	// 软删除：更新status字段为2（删除状态）
	err = global.GVA_DB.Model(&scripts.Line{}).Where("id = ?", id).Update("status", 2).Error
	return err
}

// DeleteLineByIds 批量软删除Line表记录
// Author [yourname](https://github.com/yourname)
func (lineService *LineService) DeleteLineByIds(ctx context.Context, ids []string) (err error) {
	// 软删除：更新status字段为2（删除状态）
	err = global.GVA_DB.Model(&scripts.Line{}).Where("id in ?", ids).Update("status", 2).Error
	return err
}

// UpdateLine 更新lines表记录
// Author [yourname](https://github.com/yourname)
func (lineService *LineService) UpdateLine(ctx context.Context, line scripts.Line) (err error) {
	err = global.GVA_DB.Model(&scripts.Line{}).Where("id = ?", line.Id).Updates(&line).Error
	return err
}

// GetLine 根据id获取Line表记录
// Author [yourname](https://github.com/yourname)
func (lineService *LineService) GetLine(ctx context.Context, id string) (line scripts.Line, err error) {
	// 添加status=1条件，只获取未删除的记录
	err = global.GVA_DB.Where("id = ? AND status = 1", id).First(&line).Error
	return
}

// GetLineInfoList 分页获取Line表记录
// Author [yourname](https://github.com/yourname)
func (lineService *LineService) GetLineInfoList(ctx context.Context, info scriptsReq.LineSearch) (list []*scripts.Line, total int64, err error) {
	limit := info.PageSize
	offset := info.PageSize * (info.Page - 1)
	// 创建db
	db := global.GVA_DB.Model(&scripts.Line{})
	// 默认只查询未删除的记录（status=1）
	db = db.Where("status = 1")
	var lines []*scripts.Line

	if info.ScriptId != nil {
		db = db.Where("script_id = ?", info.ScriptId)
	}

	if info.Keyword != "" {
		db = db.Where("keyword LIKE ?", "%"+info.Keyword+"%")
	}

	err = db.Count(&total).Error
	if err != nil {
		return
	}

	if limit != 0 {
		db = db.Limit(limit).Offset(offset)
	}

	err = db.Find(&lines).Error
	return lines, total, err
}
func (lineService *LineService) GetLinePublic(ctx context.Context) {
	// 此方法为获取数据源定义的数据
	// 请自行实现
}

// ReDubbing 台词重新配音
// Author [yourname](https://github.com/yourname)
func (lineService *LineService) ReDubbing(ctx context.Context, id string) (err error) {
	lineId, err := strconv.Atoi(id)
	if err != nil {
		return err
	}
	_, err = svcmgr.ScriptClient().ReDubbingByAI(ctx, &svcscript.ReDubbingByAIReq{
		LineId: int64(lineId),
	})
	if err != nil {
		logger.Errorf("ReDubbingByAI error:%v", err)
		return err
	}
	return err
}
