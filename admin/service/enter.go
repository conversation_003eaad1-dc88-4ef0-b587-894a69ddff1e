package service

import (
	"github.com/flipped-aurora/gin-vue-admin/server/service/characters"
	"github.com/flipped-aurora/gin-vue-admin/server/service/example"
	"github.com/flipped-aurora/gin-vue-admin/server/service/ips"
	"github.com/flipped-aurora/gin-vue-admin/server/service/mymoments"
	"github.com/flipped-aurora/gin-vue-admin/server/service/operations"
	"github.com/flipped-aurora/gin-vue-admin/server/service/scripts"
	"github.com/flipped-aurora/gin-vue-admin/server/service/system"
	"github.com/flipped-aurora/gin-vue-admin/server/service/users"
)

var ServiceGroupApp = &ServiceGroup{
	SystemServiceGroup:     system.ServiceGroup{},
	ExampleServiceGroup:    example.ServiceGroup{},
	MymomentsServiceGroup:  mymoments.ServiceGroup{},
	CharactersServiceGroup: characters.ServiceGroup{},
	IpsServiceGroup:        ips.ServiceGroup{},
	ScriptsServiceGroup:    scripts.ServiceGroup{},
	UsersServiceGroup:      users.ServiceGroup{},
	OperationsServiceGroup: *operations.NewServiceGroup(),
}

type ServiceGroup struct {
	SystemServiceGroup     system.ServiceGroup
	ExampleServiceGroup    example.ServiceGroup
	MymomentsServiceGroup  mymoments.ServiceGroup
	CharactersServiceGroup characters.ServiceGroup
	IpsServiceGroup        ips.ServiceGroup
	ScriptsServiceGroup    scripts.ServiceGroup
	UsersServiceGroup      users.ServiceGroup
	OperationsServiceGroup operations.ServiceGroup
}
