package ips

import (
	"context"

	"github.com/flipped-aurora/gin-vue-admin/server/global"
	"github.com/flipped-aurora/gin-vue-admin/server/model/ips"
	ipsReq "github.com/flipped-aurora/gin-vue-admin/server/model/ips/request"
)

type IpsService struct{}

// CreateIps 创建ips表记录
// Author [yourname](https://github.com/yourname)
func (IpService *IpsService) CreateIps(ctx context.Context, Ip *ips.Ips) (err error) {
	err = global.GVA_DB.Create(Ip).Error
	return err
}

// DeleteIps 删除ips表记录
// Author [yourname](https://github.com/yourname)
func (IpService *IpsService) DeleteIp(ctx context.Context, id string) (err error) {
	err = global.GVA_DB.Delete(&ips.Ips{}, "id = ?", id).Error
	return err
}

// DeleteIpsByIds 批量删除ips表记录
// Author [yourname](https://github.com/yourname)
func (IpService *IpsService) DeleteIpsByIds(ctx context.Context, ids []string) (err error) {
	err = global.GVA_DB.Delete(&[]ips.Ips{}, "id in ?", ids).Error
	return err
}

// UpdateIps 更新ips表记录
// Author [yourname](https://github.com/yourname)
func (IpService *IpsService) UpdateIps(ctx context.Context, Ip ips.Ips) (err error) {
	err = global.GVA_DB.Model(&ips.Ips{}).Where("id = ?", Ip.Id).Updates(&Ip).Error
	return err
}

// GetIps 根据id获取ips表记录
// Author [yourname](https://github.com/yourname)
func (IpService *IpsService) GetIps(ctx context.Context, id string) (Ip ips.Ips, err error) {
	err = global.GVA_DB.Where("id = ?", id).First(&Ip).Error
	return
}

// GetIpsInfoList 分页获取ips表记录
// Author [yourname](https://github.com/yourname)
func (IpService *IpsService) GetIpsInfoList(ctx context.Context, info ipsReq.IpsSearch) (list []*ips.Ips, total int64, err error) {
	limit := info.PageSize
	offset := info.PageSize * (info.Page - 1)
	// 创建db
	db := global.GVA_DB.Model(&ips.Ips{})
	var Ips []*ips.Ips
	if len(info.Name) > 0 {
		db = db.Where("name like ?", "%"+info.Name+"%")
	}
	err = db.Count(&total).Error
	if err != nil {
		return
	}

	if limit != 0 {
		db = db.Limit(limit).Offset(offset)
	}

	err = db.Find(&Ips).Error
	return Ips, total, err
}

// GetAllIps 获取ips表记录
func (IpService *IpsService) GetAllIps(ctx context.Context, info ipsReq.IpsSearch) (list []*ips.Ips, err error) {
	// 创建db
	db := global.GVA_DB.Model(&ips.Ips{})
	var Ips []*ips.Ips
	err = db.Find(&Ips).Error
	return Ips, err
}
