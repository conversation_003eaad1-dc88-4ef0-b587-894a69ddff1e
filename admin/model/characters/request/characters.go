package request

type CharacterSearch struct {
	Page          int    `json:"page" form:"page"`                   // 页码
	PageSize      int    `json:"pageSize" form:"pageSize"`           // 每页大小
	Name          string `json:"name" form:"name"`                   // 角色名
	IsRecommended *bool  `json:"is_recommended" form:"is_recommended"` // 是否推荐（可选过滤条件）
	IpId          *int   `json:"ip_id" form:"ip_id"`                 // IP ID（可选过滤条件）
}
