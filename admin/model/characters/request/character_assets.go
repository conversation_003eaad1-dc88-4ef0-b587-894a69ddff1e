package request

import (
	"github.com/flipped-aurora/gin-vue-admin/server/model/common/request"
)

type CharacterAssetsSearch struct {
	request.PageInfo
	CharacterId   int64 `json:"character_id" form:"character_id"`
	PresetStatus  *int  `json:"preset_status" form:"preset_status"` // 预设状态过滤（1:已预设，2:未预设）
}

type UpdatePresetStatus struct {
	Id           int64 `json:"id" form:"id"`
	PresetStatus int   `json:"preset_status" form:"preset_status"`
}
