// 自动生成模板Character
package characters

// characters表 结构体  Character
type Character struct {
	Id            *int    `json:"id" form:"id" gorm:"primarykey;comment:角色ID;column:id;"size:19;`                         //角色ID
	Name          *string `json:"name" form:"name" gorm:"comment:角色名称;column:name;"size:50;`                              //角色名称
	ModelPth      *string `json:"model_pth" form:"model_pth" gorm:"comment:角色模型pth;column:model_pth;"size:19;`            //角色模型pth
	ModelIdx      *string `json:"model_idx" form:"model_idx" gorm:"comment:角色模型index;column:model_idx;"size:19;`          //角色模型index
	Sort          *int    `json:"sort" form:"sort" gorm:"comment:角色排序;column:sort;"size:10;`                              //角色排序
	IpId          *int    `json:"ip_id" form:"ip_id" gorm:"comment:角色所属IP;column:ip_id;"size:19;`                         //角色所属IP
	IpName        *string `json:"ip_name" form:"ip_name" gorm:"comment:IP名称;column:ip_name;"size:50;`                     //IP名称
	DubbingCount  *int    `json:"dubbing_count" form:"dubbing_count" gorm:"comment:角色配音次数;column:dubbing_count;"size:19;` //角色配音次数
	PreviewAudio  *string `json:"preview_audio" form:"preview_audio" gorm:"comment:试听音频;column:preview_audio;"size:19;`   //试听音频
	IsRecommended *bool   `json:"is_recommended" form:"is_recommended" gorm:"comment:是否推荐;column:is_recommended;"`        //是否推荐
	Status        *int    `json:"status" form:"status" gorm:"comment:状态（1:正常，2:删除）;column:status;"size:10;`               //状态（1:正常，2:删除）
	CreatedAt     *int64  `json:"created_at" form:"created_at" gorm:"comment:创建时间;column:created_at;"size:19;`            //创建时间
	UpdatedAt     *int64  `json:"updated_at" form:"updated_at" gorm:"comment:更新时间;column:updated_at;"size:19;`            //更新时间
}

// TableName characters表 Character自定义表名 characters
func (Character) TableName() string {
	return "vc_script.`characters`"
}
