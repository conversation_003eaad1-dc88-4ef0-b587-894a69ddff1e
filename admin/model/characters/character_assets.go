// 自动生成模板CharacterAssets
package characters

// characterAssets表 结构体  CharacterAssets
type CharacterAssets struct {
	Id                     *int    `json:"id" form:"id" gorm:"primarykey;comment:资源包ID;column:id;"size:19;`                                                //资源包ID
	CharacterId            *int    `json:"character_id" form:"character_id" gorm:"comment:角色ID;column:character_id;"size:19;`                              //角色ID
	Type                   *string `json:"type" form:"type" gorm:"comment:资源包类型（经典/休闲等）;column:type;"size:30;`                                             //资源包类型（经典/休闲等）
	Name                   *string `json:"name" form:"name" gorm:"comment:资源包名称;column:name;"size:100;`                                                    //资源包名称
	AvatarUrl              *string `json:"avatar_url" form:"avatar_url" gorm:"comment:角色头像URL;column:avatar_url;"size:255;`                                //角色头像URL
	BackgroundUrl          *string `json:"background_url" form:"background_url" gorm:"comment:角色背景图URL;column:background_url;"size:255;`                   //角色背景图URL
	BgThemeColor           *string `json:"bg_theme_color" form:"bg_theme_color" gorm:"comment:角色背景主题色;column:bg_theme_color;"size:255;`                    //角色背景主题色
	AnimatedUrl            *string `json:"animated_url" form:"animated_url" gorm:"comment:角色动态图URL;column:animated_url;"size:255;`                         //角色动态图URL
	AnimatedThemeColor     *string `json:"animated_theme_color" form:"animated_theme_color" gorm:"comment:角色动态图主题色;column:animated_theme_color;"size:255;` //角色动态图主题色
	SampleAudio            *string `json:"sample_audio" form:"sample_audio" gorm:"comment:试听音频URL;column:sample_audio;"size:255;`                          //试听音频URL
	SampleAudioText        *string `json:"sample_audio_text" form:"sample_audio_text" gorm:"comment:试听文本;column:sample_audio_text;"size:255;`              //试听文本
	IsDefault              *bool   `json:"is_default" form:"is_default" gorm:"comment:是否默认;column:is_default;"`                                            //是否默认
	Sort                   *int    `json:"sort" form:"sort" gorm:"comment:排序;column:sort;"size:10;`                                                        //排序
	Status                 *int    `json:"status" form:"status" gorm:"comment:状态（1:正常，2:删除）;column:status;"`                                               //状态（1:正常，2:删除）
	PresetStatus           *int    `json:"preset_status" form:"preset_status" gorm:"comment:状态（1:已预设，2:未预设）;column:preset_status;"`
	ReferenceAudioUseRvc   *int    `json:"reference_audio_use_rvc" form:"reference_audio_use_rvc" gorm:"comment:参考音频是否使用rvc（1:使用，2:不使用）;column:reference_audio_use_rvc;"`
	UserDubbingAudioUseRvc *int    `json:"user_dubbing_use_rvc" form:"user_dubbing_use_rvc" gorm:"comment:用户配音是否使用rvc（1:使用，2:不使用）;column:user_dubbing_use_rvc;"`
	TtsEngine              *string `json:"tts_engine" form:"tts_engine" gorm:"comment:tts引擎，默认llasa;column:tts_engine;"`
	CreatedAt              *int64  `json:"created_at" form:"created_at" gorm:"comment:创建时间;column:created_at;"size:19;` //创建时间
	UpdatedAt              *int64  `json:"updated_at" form:"updated_at" gorm:"comment:更新时间;column:updated_at;"size:19;` //更新时间
}

// TableName characterAssets表 CharacterAssets自定义表名 character_assets
func (CharacterAssets) TableName() string {
	return "`vc_script`.`character_assets`"
}
