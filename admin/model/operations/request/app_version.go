package request

import (
	"github.com/flipped-aurora/gin-vue-admin/server/model/common/request"
	"github.com/flipped-aurora/gin-vue-admin/server/model/operations"
)

type AppVersionSearch struct {
	operations.AppVersion
	StartCreatedAt *int64 `json:"startCreatedAt" form:"startCreatedAt"`
	EndCreatedAt   *int64 `json:"endCreatedAt" form:"endCreatedAt"`
	request.PageInfo
	Sort  string `json:"sort" form:"sort"`
	Order string `json:"order" form:"order"`
}

type AppVersionCreate struct {
	Title       string                `json:"title" binding:"required" comment:"版本标题"`
	PackageName string                `json:"package_name" binding:"required" comment:"应用包名"`
	Version     string                `json:"version" binding:"required" comment:"版本号"`
	UpdateInfo  operations.UpdateInfo `json:"update_info" comment:"更新内容"`
	DownloadURL string                `json:"download_url" comment:"下载链接"`
	FileSize    *uint64               `json:"file_size" comment:"文件大小"`
	FileMD5     string                `json:"file_md5" comment:"文件MD5"`
	MinVersion  string                `json:"min_version" comment:"最低支持版本"`
	ForceUpdate bool                  `json:"force_update" comment:"是否强制更新"`
	Platform    string                `json:"platform" binding:"required,oneof=ios android all" comment:"平台类型"`
	Channel     string                `json:"channel" comment:"渠道标识"`
	Remark      string                `json:"remark" comment:"备注信息"`
}

type AppVersionUpdate struct {
	ID          uint64                `json:"id" binding:"required" comment:"主键ID"`
	Title       string                `json:"title" binding:"required" comment:"版本标题"`
	PackageName string                `json:"package_name" binding:"required" comment:"应用包名"`
	Version     string                `json:"version" binding:"required" comment:"版本号"`
	UpdateInfo  operations.UpdateInfo `json:"update_info" comment:"更新内容"`
	DownloadURL string                `json:"download_url" comment:"下载链接"`
	FileSize    *uint64               `json:"file_size" comment:"文件大小"`
	FileMD5     string                `json:"file_md5" comment:"文件MD5"`
	MinVersion  string                `json:"min_version" comment:"最低支持版本"`
	ForceUpdate bool                  `json:"force_update" comment:"是否强制更新"`
	Platform    string                `json:"platform" binding:"required,oneof=ios android all" comment:"平台类型"`
	Channel     string                `json:"channel" comment:"渠道标识"`
	Remark      string                `json:"remark" comment:"备注信息"`
}

type AppVersionById struct {
	ID uint64 `json:"id" form:"id" binding:"required" comment:"主键ID"`
}

type AppVersionByIds struct {
	IDs []uint64 `json:"ids" form:"ids" binding:"required" comment:"主键ID列表"`
}

type AppVersionPublish struct {
	ID uint64 `json:"id" binding:"required" comment:"主键ID"`
}

type AppVersionOffline struct {
	ID uint64 `json:"id" binding:"required" comment:"主键ID"`
}
