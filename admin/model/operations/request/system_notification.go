package request

import (
	"github.com/flipped-aurora/gin-vue-admin/server/model/common/request"
	"new-gitlab.xunlei.cn/vcproject/backends/proto/api/svcchat"
)

// TemplateItemRequest 模版数据项请求结构
type TemplateItemRequest struct {
	Text      string                     `json:"text" binding:"required"`      // 文本内容
	Color     string                     `json:"color"`                         // 字体颜色
	Url       string                     `json:"url"`                           // 跳转地址
	Type      svcchat.TemplateJumpType   `json:"type"`                          // 点击行为类型
	Name      string                     `json:"name"`                          // 名称
	Ext       string                     `json:"ext"`                           // 扩展字段
	Underline bool                       `json:"underline"`                     // 是否下划线
	Size      int32                      `json:"size"`                          // 字体大小
}

// TemplateMsgRequest 富文本消息请求结构
type TemplateMsgRequest struct {
	Tpl      string                              `json:"tpl" binding:"required"`    // 模版字符串
	Data     map[string]TemplateItemRequest      `json:"data"`                      // 模版数据
	ImageUrl string                              `json:"image_url"`                 // 图片地址
	UserId   int64                               `json:"user_id"`                   // 用户ID
	TplColor string                              `json:"tpl_color"`                 // 文案颜色
	Type     svcchat.TemplateJumpType            `json:"type"`                      // 外部跳转类型
	Url      string                              `json:"url"`                       // 外部跳转地址
}

// SystemMsgRequest 系统消息请求结构
type SystemMsgRequest struct {
	Title   TemplateMsgRequest `json:"title" binding:"required"`   // 标题
	BgmUrl  string             `json:"bgm_url"`                     // 背景图片URL
	JumpUrl string             `json:"jump_url"`                    // 整体跳转URL
	Content TemplateMsgRequest `json:"content" binding:"required"`  // 内容
	Time    int64              `json:"time"`                        // 时间戳
}

// SendSystemNotificationRequest 发送系统通知请求
type SendSystemNotificationRequest struct {
	ToUid       int64            `json:"to_uid"`                          // 接收者用户ID，-1表示全部用户（兼容旧版本）
	ToUids      []int64          `json:"to_uids"`                         // 接收者用户ID列表，支持多用户
	ContentType int32            `json:"content_type" binding:"required"`  // 内容类型，150为系统消息
	SystemMsg   SystemMsgRequest `json:"system_msg" binding:"required"`   // 系统消息内容
}

// GetSystemNotificationRecordsRequest 获取系统通知记录列表请求
type GetSystemNotificationRecordsRequest struct {
	request.PageInfo
	Title        string `json:"title" form:"title"`               // 标题搜索
	ReceiverType *int32 `json:"receiver_type" form:"receiver_type"` // 接收者类型
	SendStatus   *int32 `json:"send_status" form:"send_status"`   // 发送状态
	OperatorName string `json:"operator_name" form:"operator_name"` // 操作员姓名
	StartTime    string `json:"start_time" form:"start_time"`     // 开始时间
	EndTime      string `json:"end_time" form:"end_time"`         // 结束时间
	OrderKey     string `json:"order_key" form:"order_key"`       // 排序字段
	Desc         bool   `json:"desc" form:"desc"`                 // 是否降序
}
