// 自动生成模板AppVersion
package operations

import (
	"database/sql/driver"
	"encoding/json"
	"fmt"
)

// AppVersion APP版本管理表 结构体
type AppVersion struct {
	ID          *uint64    `json:"id" form:"id" gorm:"primarykey;comment:主键ID;column:id;"`
	Title       *string    `json:"title" form:"title" gorm:"comment:版本标题;column:title;size:100;"`
	PackageName *string    `json:"package_name" form:"package_name" gorm:"comment:应用包名;column:package_name;size:100;"`
	Version     *string    `json:"version" form:"version" gorm:"comment:版本号(格式：1.1.0);column:version;size:20;"`
	Status      *int8      `json:"status" form:"status" gorm:"comment:状态：1-待发布 2-已发布 3-已下线 4-已删除;column:status;default:1;"`
	UpdateInfo  UpdateInfo `json:"update_info" form:"update_info" gorm:"comment:更新内容(支持多语言);column:update_info;type:json;"`
	DownloadURL *string    `json:"download_url" form:"download_url" gorm:"comment:下载链接;column:download_url;size:500;"`
	FileSize    *uint64    `json:"file_size" form:"file_size" gorm:"comment:文件大小(字节);column:file_size;"`
	FileMD5     *string    `json:"file_md5" form:"file_md5" gorm:"comment:文件MD5校验值;column:file_md5;size:32;"`
	MinVersion  *string    `json:"min_version" form:"min_version" gorm:"comment:最低支持版本;column:min_version;size:20;"`
	ForceUpdate *bool      `json:"force_update" form:"force_update" gorm:"comment:是否强制更新：0-否 1-是;column:force_update;default:false;"`
	Platform    *string    `json:"platform" form:"platform" gorm:"comment:平台类型;column:platform;type:enum('ios','android','all');default:'all';"`
	Channel     *string    `json:"channel" form:"channel" gorm:"comment:渠道标识;column:channel;size:50;default:'official';"`
	PublishTime *int64     `json:"publish_time" form:"publish_time" gorm:"comment:发布时间戳(毫秒);column:publish_time;"`
	OfflineTime *int64     `json:"offline_time" form:"offline_time" gorm:"comment:下线时间戳(毫秒);column:offline_time;"`
	Remark      *string    `json:"remark" form:"remark" gorm:"comment:备注信息;column:remark;size:500;"`
	CreatedBy   *string    `json:"created_by" form:"created_by" gorm:"comment:创建人;column:created_by;size:50;"`
	UpdatedBy   *string    `json:"updated_by" form:"updated_by" gorm:"comment:更新人;column:updated_by;size:50;"`
	CreatedAt   *int64     `json:"created_at" form:"created_at" gorm:"comment:创建时间戳(毫秒);column:created_at;"`
	UpdatedAt   *int64     `json:"updated_at" form:"updated_at" gorm:"comment:更新时间戳(毫秒);column:updated_at;"`
}

// UpdateInfo 更新内容结构体，支持多语言
type UpdateInfo map[string]interface{}

// Value 实现 driver.Valuer 接口，用于数据库存储
func (u UpdateInfo) Value() (driver.Value, error) {
	if u == nil {
		return nil, nil
	}
	return json.Marshal(u)
}

// Scan 实现 sql.Scanner 接口，用于数据库读取
func (u *UpdateInfo) Scan(value interface{}) error {
	if value == nil {
		*u = nil
		return nil
	}

	var bytes []byte
	switch v := value.(type) {
	case []byte:
		bytes = v
	case string:
		bytes = []byte(v)
	default:
		return fmt.Errorf("cannot scan %T into UpdateInfo", value)
	}

	return json.Unmarshal(bytes, u)
}

// TableName 设置表名
func (AppVersion) TableName() string {
	return "`vc_config`.`app_versions`"
}
