package operations

import (
	"github.com/flipped-aurora/gin-vue-admin/server/global"
	"time"
)

// SystemNotificationRecord 系统通知发送记录
type SystemNotificationRecord struct {
	global.GVA_MODEL
	Title           string     `json:"title" gorm:"column:title;type:varchar(255);not null;comment:通知标题"`
	Content         string     `json:"content" gorm:"column:content;type:text;comment:通知内容"`
	ReceiverType    int32      `json:"receiver_type" gorm:"column:receiver_type;type:tinyint;not null;default:1;comment:接收者类型 1:全部用户 2:指定用户"`
	ReceiverCount   int32      `json:"receiver_count" gorm:"column:receiver_count;type:int;not null;default:0;comment:接收者数量"`
	ReceiverUserIds string     `json:"receiver_user_ids" gorm:"column:receiver_user_ids;type:text;comment:指定用户ID列表(JSON格式)"`
	SendStatus      int32      `json:"send_status" gorm:"column:send_status;type:tinyint;not null;default:1;comment:发送状态 1:发送中 2:发送成功 3:发送失败 4:部分失败"`
	SuccessCount    int32      `json:"success_count" gorm:"column:success_count;type:int;not null;default:0;comment:发送成功数量"`
	FailureCount    int32      `json:"failure_count" gorm:"column:failure_count;type:int;not null;default:0;comment:发送失败数量"`
	ErrorMessage    string     `json:"error_message" gorm:"column:error_message;type:text;comment:错误信息"`
	SystemMsgData   string     `json:"system_msg_data" gorm:"column:system_msg_data;type:longtext;comment:系统消息完整数据(JSON格式)"`
	SendStartTime   time.Time  `json:"send_start_time" gorm:"column:send_start_time;type:datetime;comment:发送开始时间"`
	SendEndTime     *time.Time `json:"send_end_time" gorm:"column:send_end_time;type:datetime;comment:发送结束时间"`
	SendDuration    int64      `json:"send_duration" gorm:"column:send_duration;type:bigint;default:0;comment:发送耗时(毫秒)"`
	OperatorId      uint       `json:"operator_id" gorm:"column:operator_id;type:int unsigned;not null;comment:操作员ID"`
	OperatorName    string     `json:"operator_name" gorm:"column:operator_name;type:varchar(100);not null;comment:操作员姓名"`
}

// TableName 设置表名
func (SystemNotificationRecord) TableName() string {
	return "`vc_user`.`user_system_notification_records`"
}

// ReceiverTypeEnum 接收者类型枚举
const (
	ReceiverTypeAll      = 1 // 全部用户
	ReceiverTypeSpecific = 2 // 指定用户
)

// SendStatusEnum 发送状态枚举
const (
	SendStatusSending       = 1 // 发送中
	SendStatusSuccess       = 2 // 发送成功
	SendStatusFailed        = 3 // 发送失败
	SendStatusPartialFailed = 4 // 部分失败
)
