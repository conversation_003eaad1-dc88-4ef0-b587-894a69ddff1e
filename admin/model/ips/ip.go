// 自动生成模板Ips
package ips

// ips表 结构体  Ips
type Ips struct {
	Id          *int    `json:"id" form:"id" gorm:"primarykey;comment:IPID;column:id;"size:19;`                  //IPID
	Name        *string `json:"name" form:"name" gorm:"comment:IP名称;column:name;"size:50;`                       //IP名称
	Description *string `json:"description" form:"description" gorm:"comment:IP描述;column:description;"size:255;` //IP描述
	Logo        *string `json:"logo" form:"logo" gorm:"comment:IP logo;column:logo;"size:255;`                   //IP logo
	Hot         *int    `json:"hot" form:"hot" gorm:"comment:IP热度值;column:hot;"size:10;`                         //IP热度值
	Sort        *int    `json:"sort" form:"sort" gorm:"comment:排序值;column:sort;"size:10;`                        //排序值
	CreatedAt   *int64  `json:"created_at" form:"created_at" gorm:"comment:创建时间;column:created_at;"size:19;`     //创建时间
	UpdatedAt   *int64  `json:"updated_at" form:"updated_at" gorm:"comment:更新时间;column:updated_at;"size:19;`     //更新时间
}

// TableName ips表 Ips自定义表名 ips
func (Ips) TableName() string {
	return "`vc_script`.`ips`"
}
