// 自动生成模板Moments
package mymoments

// moments表 结构体  Moments
type Moments struct {
	Id             *int    `json:"id" form:"id" gorm:"primarykey;comment:朋友圈ID;column:id;"size:19;`                               //朋友圈ID
	UserId         *int    `json:"userId" form:"userId" gorm:"comment:用户ID;column:user_id;"size:19;`                              //用户ID
	CharacterId    *int    `json:"characterId" form:"characterId" gorm:"comment:角色ID;column:character_id;"size:19;`               //角色ID
	OriginAudioUrl *string `json:"originAudioUrl" form:"originAudioUrl" gorm:"comment:原声音频URL;column:origin_audio_url;"size:255;` //原声音频URL
	VoiceAudioUrl  *string `json:"voiceAudioUrl" form:"voiceAudioUrl" gorm:"comment:变声音频URL;column:voice_audio_url;"size:255;`    //变声音频URL
	Content        *string `json:"content" form:"content" gorm:"comment:文本内容;column:content;"`                                    //文本内容
	AsrDataUrl     *string `json:"asrDataUrl" form:"asrDataUrl" gorm:"comment:ASR数据地址;column:asr_data_url;"size:255;`             //ASR数据地址
	AsrStatus      *int    `json:"asrStatus" form:"asrStatus" gorm:"comment:asr识别状态 0:识别中 1:识别成功 2:识别失败;column:asr_status;"`      //asr识别状态 0:识别中 1:识别成功 2:识别失败
	Duration       *int    `json:"duration" form:"duration" gorm:"comment:语音时长(秒);column:duration;"size:10;`                      //语音时长(秒)
	CreatedAt      *int64  `json:"createdAt" form:"createdAt" gorm:"comment:创建时间;column:created_at;"size:19;`                     //创建时间
	UpdatedAt      *int64  `json:"updatedAt" form:"updatedAt" gorm:"comment:更新时间;column:updated_at;"size:19;`                     //更新时间
}

// TableName moments表 Moments自定义表名 moments
func (Moments) TableName() string {
	return "`vc_moment`.moments"
}
