// 自动生成模板Report
package scripts

// reports表 结构体  Report
type Report struct {
	Id         *int64  `json:"id" form:"id" gorm:"primarykey;comment:举报ID;column:id;"size:19;`                                 //举报ID
	ReportType *int    `json:"report_type" form:"report_type" gorm:"comment:举报类型（1:剧本、2:配音、3:评论）;column:report_type;"size:10;` //举报类型（1:剧本、2:配音、3:评论）
	TargetId   *int64  `json:"target_id" form:"target_id" gorm:"comment:目标ID;column:target_id;"size:19;`                       //目标ID
	UserId     *int64  `json:"user_id" form:"user_id" gorm:"comment:用户ID;column:user_id;"size:19;`                             //用户ID
	ReasonId   *int64  `json:"reason_id" form:"reason_id" gorm:"comment:原因ID;column:reason_id;"size:10;`                       //原因ID
	ReasonText *string `json:"reason_text" form:"reason_text" gorm:"comment:原因文本;column:reason_text;"size:255;`                //原因文本
	Content    *string `json:"content" form:"content" gorm:"comment:举报内容;column:content;"size:255;`                            //举报内容
	Evidence   *string `json:"evidence" form:"evidence" gorm:"comment:证据;column:evidence;"size:1000;`                          //证据
	Status     *int    `json:"status" form:"status" gorm:"comment:状态（0:未处理、1:不通过、2:通过）;column:status;"size:10;`                //状态（0:未处理、1:不通过、2:通过）
	Tag        *string `json:"tag" form:"tag" gorm:"comment:违规标签（低俗色情,违法有害,政治敏感,未成年有害）;column:tag;"size:255;`              //违规标签
	Reply      *string `json:"reply" form:"reply" gorm:"comment:处理回复;column:reply;"size:255;`                                  //处理回复
	CreatedAt  *int64  `json:"created_at" form:"created_at" gorm:"comment:创建时间;column:created_at;"size:19;`                    //创建时间
	UpdatedAt  *int64  `json:"updated_at" form:"updated_at" gorm:"comment:更新时间;column:updated_at;"size:19;`                    //更新时间
}

// TableName reports表 Report自定义表名 reports
func (Report) TableName() string {
	return "vc_script.`reports`"
}
