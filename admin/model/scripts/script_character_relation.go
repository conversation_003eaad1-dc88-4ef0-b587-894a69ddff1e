// 自动生成模板ScriptCharacterRelation
package scripts

// scriptCharacterRelation表 结构体  ScriptCharacterRelation
type ScriptCharacterRelation struct {
	Id               *int64 `json:"id" form:"id" gorm:"primarykey;comment:ID;column:id;"size:19;`                                          //ID
	ScriptId         *int64 `json:"script_id" form:"script_id" gorm:"comment:剧本ID;column:script_id;"size:19;`                              //剧本ID
	CharacterId      *int64 `json:"character_id" form:"character_id" gorm:"comment:角色ID;column:character_id;"size:19;`                     //角色ID
	CharacterAssetId *int   `json:"character_asset_id" form:"character_asset_id" gorm:"comment:角色资源id;column:character_asset_id;"size:19;` //角色资源id
	LineCount        *int   `json:"line_count" form:"line_count" gorm:"comment:剧本台词数;column:line_count;"size:10;`                          //剧本台词数
	Sort             *int   `json:"sort" form:"sort" gorm:"comment:排序值;column:sort;"size:10;`                                              //排序值
	Status           *int   `json:"status" form:"status" gorm:"comment:状态（1:正常、2:删除）;column:status;"size:10;`                              //状态（1:正常、2:删除）
	CreatedAt        *int64 `json:"created_at" form:"created_at" gorm:"comment:创建时间;column:created_at;"size:19;`                           //创建时间
	UpdatedAt        *int64 `json:"updated_at" form:"updated_at" gorm:"comment:更新时间;column:updated_at;"size:19;`                           //更新时间

	//业务字段
	CharacterName string `json:"character_name" form:"character_name" gorm:"-"` //角色名称
	AssetUrl      string `json:"asset_url" form:"asset_url" gorm:"-"`           //角色资源url
}

// TableName scriptCharacterRelation表 ScriptCharacterRelation自定义表名 script_character_relation
func (ScriptCharacterRelation) TableName() string {
	return "vc_script.`script_character_relation`"
}
