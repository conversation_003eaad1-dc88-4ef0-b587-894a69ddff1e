// 自动生成模板ScriptTopicRelation
package scripts

// scriptTopicRelation表 结构体  ScriptTopicRelation
type ScriptTopicRelation struct {
	Id        *int64 `json:"id" form:"id" gorm:"primarykey;comment:ID;column:id;"size:19;`                //ID
	ScriptId  *int64 `json:"script_id" form:"script_id" gorm:"comment:剧本ID;column:script_id;"size:19;`    //剧本ID
	TopicId   *int64 `json:"topic_id" form:"topic_id" gorm:"comment:话题ID;column:topic_id;"size:19;`       //话题ID
	CreatedAt *int64 `json:"created_at" form:"created_at" gorm:"comment:创建时间;column:created_at;"size:19;` //创建时间
	Status    *int   `json:"status" form:"status" gorm:"comment:状态（1:正常、2:删除）;column:status;"size:10;`    //状态（1:正常、2:删除）

	//业务字段
	TopicName string `json:"topic_name" form:"topic_name" gorm:"-"` //话题名称
}

// TableName scriptTopicRelation表 ScriptTopicRelation自定义表名 script_topic_relation
func (ScriptTopicRelation) TableName() string {
	return "vc_script.`script_topic_relation`"
}
