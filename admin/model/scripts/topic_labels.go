package scripts

// topics表 结构体  Topic
type TopicLabel struct {
	Id        *int    `json:"id" form:"id" gorm:"primarykey;comment:话题ID;column:id;"size:19;`              //话题ID
	Name      *string `json:"name" form:"name" gorm:"comment:话题名称;column:name;"size:50;`                   //话题名称
	Sort      *int    `json:"sort" form:"sort" gorm:"comment:排序值;column:sort;"size:10;`                    //排序值
	Color     *string `json:"color" form:"color" gorm:"comment:颜色;column:color;"size:50;`                  //颜色
	BgUrl     *string `json:"bg_url" form:"bg_url" gorm:"comment:背景图;column:bg_url;"size:50;`              //背景图
	CreatedAt *int64  `json:"created_at" form:"created_at" gorm:"comment:创建时间;column:created_at;"size:19;` //创建时间
	UpdatedAt *int64  `json:"updated_at" form:"updated_at" gorm:"comment:更新时间;column:updated_at;"size:19;` //更新时间
	Status    *int    `json:"status" form:"status" gorm:"comment:状态（1:正常、2:删除）;column:status;"size:10;`    //状态（1:正常、2:删除）
}

// TableName topics表 Topic自定义表名 topics
func (TopicLabel) TableName() string {
	return "vc_script.`topic_labels`"
}
