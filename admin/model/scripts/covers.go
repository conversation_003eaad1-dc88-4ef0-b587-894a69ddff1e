// 自动生成模板Cover
package scripts

// covers表 结构体  Cover
type Cover struct {
	Id          *int    `json:"id" form:"id" gorm:"primarykey;comment:封面ID;column:id;"size:19;`                  //封面ID
	Url         *string `json:"url" form:"url" gorm:"comment:封面地址;column:url;"size:255;`                         //封面地址
	Description *string `json:"description" form:"description" gorm:"comment:封面说明;column:description;"size:255;` //封面说明
	Status      *int    `json:"status" form:"status" gorm:"comment:封面状态 0-默认 1-正常 2-删除;column:status;"`          //封面状态
	Type        *int    `json:"type" form:"type" gorm:"comment:封面类型：1-静态，2-动态;column:type;"`                     //封面类型
	ThemeColor  *string `json:"theme_color" form:"theme_color" gorm:"comment:主题色;column:theme_color;"size:30;`   //主题色
	Sort        *int    `json:"sort" form:"sort" gorm:"comment:排序值;column:sort;"size:10;`                        //排序值
	CreatedAt   int64   `json:"created_at" form:"created_at" gorm:"comment:创建时间;column:created_at;"size:19;`     //创建时间
	UpdatedAt   int64   `json:"updated_at" form:"updated_at" gorm:"comment:更新时间;column:updated_at;"size:19;`     //更新时间
}

// TableName covers表 Cover自定义表名 covers
func (Cover) TableName() string {
	return "vc_script.`covers`"
}
