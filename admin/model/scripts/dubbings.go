// 自动生成模板Dubbing
package scripts

// dubbings表 结构体  Dubbing
type Dubbing struct {
	Id               *int64  `json:"id" form:"id" gorm:"primarykey;comment:配音ID;column:id;"size:19;`                                                         //配音ID
	ScriptId         *int64  `json:"script_id" form:"script_id" gorm:"comment:剧本ID;column:script_id;"size:19;`                                               //剧本ID
	LineId           *int64  `json:"line_id" form:"line_id" gorm:"comment:台词ID;column:line_id;"size:19;`                                                     //台词ID
	TopicId          *int64  `json:"topic_id" form:"topic_id" gorm:"comment:话题ID;column:topic_id;"size:19;`                                                  //话题ID
	UserId           *int64  `json:"user_id" form:"user_id" gorm:"comment:用户ID;column:user_id;"size:19;`                                                     //用户ID
	CharacterId      *int64  `json:"character_id" form:"character_id" gorm:"comment:角色ID;column:character_id;"size:19;`                                      //角色ID
	CharacterAssetId *int64  `json:"character_asset_id" form:"character_asset_id" gorm:"comment:角色资源id;column:character_asset_id;"size:19;`                  //角色资源id
	OriginalUrl      *string `json:"original_url" form:"original_url" gorm:"comment:原声地址;column:original_url;"size:255;`                                     //原声地址
	OriginalDuration *int    `json:"original_duration" form:"original_duration" gorm:"comment:原声时长;column:original_duration;"size:10;`                       //原声时长
	DubbedUrl        *string `json:"dubbed_url" form:"dubbed_url" gorm:"comment:配音地址;column:dubbed_url;"size:255;`                                           //配音地址
	DubbedDuration   *int    `json:"dubbed_duration" form:"dubbed_duration" gorm:"comment:原声时长;column:dubbed_duration;"size:10;`                             //原声时长
	IsAuthor         *bool   `json:"is_author" form:"is_author" gorm:"comment:是否作者配音;column:is_author;"`                                                     //是否作者配音
	IsTop            *bool   `json:"is_top" form:"is_top" gorm:"comment:是否置顶;column:is_top;"`                                                                //是否置顶
	Likes            *int    `json:"likes" form:"likes" gorm:"comment:点赞数;column:likes;"size:10;`                                                            //点赞数
	ReviewStatus     *int    `json:"review_status" form:"review_status" gorm:"comment:审核状态 1:待审核 2:机审通过 3:机审拒绝 4:人审通过 5:人审拒绝;column:review_status;"size:10;` //审核状态 1:待审核 2:机审通过 3:机审拒绝 4:人审通过 5:人审拒绝
	Status           *int    `json:"status" form:"status" gorm:"comment:状态（1:正常、2:删除）;column:status;"size:10;`                                               //状态（1:正常、2:删除）
	CreatedAt        *int64  `json:"created_at" form:"created_at" gorm:"comment:创建时间;column:created_at;"size:19;`                                            //创建时间
	UpdatedAt        *int64  `json:"updated_at" form:"updated_at" gorm:"comment:更新时间;column:updated_at;"size:19;`                                            //更新时间

	//业务字段
	ScriptTitle string `json:"script_title" form:"script_title" gorm:"-"` //剧本标题
}

// TableName dubbings表 Dubbing自定义表名 dubbings
func (Dubbing) TableName() string {
	return "vc_script.`dubbings`"
}
