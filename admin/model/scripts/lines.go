// 自动生成模板Line
package scripts

// lines表 结构体  Line
type Line struct {
	Id               *int64  `json:"id" form:"id" gorm:"primarykey;comment:台词ID;column:id;"size:19;`                                        //台词ID
	ScriptId         *int    `json:"script_id" form:"script_id" gorm:"comment:剧本ID;column:script_id;"size:19;`                              //剧本ID
	CharacterId      *int    `json:"character_id" form:"character_id" gorm:"comment:角色ID;column:character_id;"size:19;`                     //角色ID
	CharacterAssetId *int    `json:"character_asset_id" form:"character_asset_id" gorm:"comment:角色资源ID;column:character_asset_id;"size:19;` //角色资源ID
	Content          *string `json:"content" form:"content" gorm:"comment:台词内容;column:content;"`                                            //台词内容
	BackgroundUrl    *string `json:"background_url" form:"background_url" gorm:"comment:台词背景图;column:background_url;"size:255;`             //台词背景图
	BgThemeColor     *string `json:"bg_theme_color" form:"bg_theme_color" gorm:"comment:背景图主题色;column:bg_theme_color;"size:20;`            //背景图主题色
	DubbingDuration  *int    `json:"dubbing_duration" form:"dubbing_duration" gorm:"comment:配音时长;column:dubbing_duration;"size:10;`         //配音时长
	DubbingCount     *int    `json:"dubbing_count" form:"dubbing_count" gorm:"comment:配音数;column:dubbing_count;"size:10;`                   //配音数
	Sort             *int    `json:"sort" form:"sort" gorm:"comment:排序值;column:sort;"size:10;`                                              //排序值
	Status           *int    `json:"status" form:"status" gorm:"comment:状态 1:正常 2:删除;column:status;"`                                       //状态 1:正常 2:删除
	CreatedAt        *int64  `json:"created_at" form:"created_at" gorm:"comment:创建时间;column:created_at;"size:19;`                           //创建时间
	UpdatedAt        *int64  `json:"updated_at" form:"updated_at" gorm:"comment:更新时间;column:updated_at;"size:19;`                           //更新时间
}

// TableName lines表 Line自定义表名 lines
func (Line) TableName() string {
	return "vc_script.`lines`"
}

type ScriptLine struct {
	Line
	DubbedUrl      string `json:"dubbed_url"`
	DubbedDuration int32  `json:"dubbed_duration"`
}
