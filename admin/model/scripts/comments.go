// 自动生成模板Comment
package scripts

// comments表 结构体  Comment
type Comment struct {
	Id               *int64  `json:"id" form:"id" gorm:"primarykey;comment:评论ID;column:id;"size:19;`                                                         //评论ID
	CommentType      *int    `json:"comment_type" form:"comment_type" gorm:"comment:评论类型（1:剧本、2:配音）;column:comment_type;"size:10;`                           //评论类型（1:剧本、2:配音）
	ParentId         *int    `json:"parent_id" form:"parent_id" gorm:"comment:父级评论ID;column:parent_id;"size:19;`                                             //父级评论ID
	ScriptId         *int    `json:"script_id" form:"script_id" gorm:"comment:剧本ID;column:script_id;"size:19;`                                               //剧本ID
	DubbingId        *int    `json:"dubbing_id" form:"dubbing_id" gorm:"comment:配音ID;column:dubbing_id;"size:19;`                                            //配音ID
	UserId           *int64  `json:"user_id" form:"user_id" gorm:"comment:用户ID;column:user_id;"size:19;`                                                     //用户ID
	CharacterId      *int    `json:"character_id" form:"character_id" gorm:"comment:角色ID;column:character_id;"size:19;`                                      //角色ID
	CharacterAssetId *int    `json:"character_asset_id" form:"character_asset_id" gorm:"comment:角色资源ID;column:character_asset_id;"size:19;`                  //角色资源ID
	Content          *string `json:"content" form:"content" gorm:"comment:评论内容;column:content;"`                                                             //评论内容
	ContentType      *int    `json:"content_type" form:"content_type" gorm:"comment:内容类型（1:文本、2:语音）;column:content_type;"size:10;`                           //内容类型（1:文本、2:语音）
	VoiceUrl         *string `json:"voice_url" form:"voice_url" gorm:"comment:语音地址;column:voice_url;"size:255;`                                              //语音地址
	VoiceDuration    *int    `json:"voice_duration" form:"voice_duration" gorm:"comment:语音时长;column:voice_duration;"size:10;`                                //语音时长
	AsrText          *string `json:"asr_text" form:"asr_text" gorm:"comment:语音转写文本;column:asr_text;"`                                                        //语音转写文本
	AsrStatus        *int    `json:"asr_status" form:"asr_status" gorm:"comment:识别状态（0: 默认 1:识别成功、2:失败、3:超时）;column:asr_status;"size:10;`                    //识别状态（0: 默认 1:识别成功、2:失败、3:超时）
	IsTop            *bool   `json:"is_top" form:"is_top" gorm:"comment:是否置顶;column:is_top;"`                                                                //是否置顶
	IsHot            *bool   `json:"is_hot" form:"is_hot" gorm:"comment:是否热门;column:is_hot;"`                                                                //是否热门
	IsAuthor         *bool   `json:"is_author" form:"is_author" gorm:"comment:是否作者;column:is_author;"`                                                       //是否作者
	Likes            *int    `json:"likes" form:"likes" gorm:"comment:点赞数;column:likes;"size:10;`                                                            //点赞数
	Status           *int    `json:"status" form:"status" gorm:"comment:状态（1:正常、2:删除）;column:status;"size:10;`                                               //状态（1:正常、2:删除）
	ReviewStatus     *int    `json:"review_status" form:"review_status" gorm:"comment:审核状态 1:待审核 2:机审通过 3:机审拒绝 4:人审通过 5:人审拒绝;column:review_status;"size:10;` //审核状态 1:待审核 2:机审通过 3:机审拒绝 4:人审通过 5:人审拒绝
	CreatedAt        *int64  `json:"created_at" form:"created_at" gorm:"comment:创建时间;column:created_at;"size:19;`                                            //创建时间
	UpdatedAt        *int64  `json:"updated_at" form:"updated_at" gorm:"comment:更新时间;column:updated_at;"size:19;`                                            //更新时间

	//业务字段
	ScriptTitle string `json:"script_title" form:"script_title" gorm:"-"` //剧本标题
}

// TableName comments表 Comment自定义表名 comments
func (Comment) TableName() string {
	return "vc_script.`comments`"
}
