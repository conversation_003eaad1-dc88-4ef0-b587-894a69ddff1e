package request

import (
	"github.com/flipped-aurora/gin-vue-admin/server/model/common/request"
)

type DubbingSearch struct {
	ID               *int64  `json:"id" form:"id" `                   // 配音ID搜索
	ScriptId         *int64  `json:"script_id" form:"script_id" `
	LineId           *int64  `json:"line_id" form:"line_id" `
	TopicId          *int64  `json:"topic_id" form:"topic_id" `
	UserId           *int64  `json:"user_id" form:"user_id" `
	CharacterId      *int64  `json:"character_id" form:"character_id" `
	ReviewStatus     *int    `json:"review_status" form:"review_status" `
	Status           *int    `json:"status" form:"status" `
	ScriptTitle      *string `json:"script_title" form:"script_title" ` // 剧本标题搜索
	StartCreatedAt   *int64  `json:"startCreatedAt" form:"startCreatedAt"`
	EndCreatedAt     *int64  `json:"endCreatedAt" form:"endCreatedAt"`
	request.PageInfo
	Sort  string `json:"sort" form:"sort"`
	Order string `json:"order" form:"order"`
}

// ManualUploadDubbingReq 手动上传配音请求
type ManualUploadDubbingReq struct {
	LineId        int64  `json:"line_id" binding:"required"`        // 台词ID
	CharacterId   int64  `json:"character_id" binding:"required"`   // 角色ID
	DubbedUrl     string `json:"dubbed_url" binding:"required"`     // 配音地址（去除域名）
	DubbedDuration int32  `json:"dubbed_duration"`                  // 配音时长（毫秒）
}
