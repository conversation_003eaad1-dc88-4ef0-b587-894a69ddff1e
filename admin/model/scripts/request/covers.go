package request

import (
	"github.com/flipped-aurora/gin-vue-admin/server/model/common/request"
)

type CoverSearch struct {
	Description    *string `json:"description" form:"description" `
	Type           *int    `json:"type" form:"type" `
	StartCreatedAt *int64  `json:"startCreatedAt" form:"startCreatedAt"`
	EndCreatedAt   *int64  `json:"endCreatedAt" form:"endCreatedAt"`
	request.PageInfo
	Sort  string `json:"sort" form:"sort"`
	Order string `json:"order" form:"order"`
}
