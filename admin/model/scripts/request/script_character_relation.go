package request

import (
	"github.com/flipped-aurora/gin-vue-admin/server/model/common/request"
)

type ScriptCharacterRelationSearch struct {
	Id             *int64 `json:"id" form:"id" `
	ScriptId       *int64 `json:"script_id" form:"script_id" `
	CharacterId    *int64 `json:"character_id" form:"character_id" `
	StartCreatedAt *int64 `json:"startCreatedAt" form:"startCreatedAt"`
	EndCreatedAt   *int64 `json:"endCreatedAt" form:"endCreatedAt"`
	request.PageInfo
	Sort  string `json:"sort" form:"sort"`
	Order string `json:"order" form:"order"`
}
