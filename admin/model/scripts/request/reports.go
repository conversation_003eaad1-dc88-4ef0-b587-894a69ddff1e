package request

import (
	"github.com/flipped-aurora/gin-vue-admin/server/model/common/request"
)

type ReportSearch struct {
	Id             *int64  `json:"id" form:"id" `
	ReportType     *int64  `json:"report_type" form:"report_type" `
	TargetId       *int64  `json:"target_id" form:"target_id" `
	UserId         *int64  `json:"user_id" form:"user_id" `
	ReasonId       *int64  `json:"reason_id" form:"reason_id" `
	ReasonText     *string `json:"reason_text" form:"reason_text" `
	Content        *string `json:"content" form:"content" `
	Status         *int    `json:"status" form:"status" `
	Tag            *string `json:"tag" form:"tag" `
	Reply          *string `json:"reply" form:"reply" `
	StartCreatedAt *int64  `json:"startCreatedAt" form:"startCreatedAt"`
	EndCreatedAt   *int64  `json:"endCreatedAt" form:"endCreatedAt"`
	request.PageInfo
	Sort  string `json:"sort" form:"sort"`
	Order string `json:"order" form:"order"`
}

// ReportAuditReq 举报审核请求
type ReportAuditReq struct {
	Id     int64    `json:"id" binding:"required"`     // 举报ID
	Status int      `json:"status" binding:"required"` // 审核状态：1-不通过，2-通过
	Tags   []string `json:"tags"`                      // 违规标签（通过时必填）
	Reply  string   `json:"reply"`                     // 处理回复
}

// ReportContentDetailReq 举报内容详情请求
type ReportContentDetailReq struct {
	ReportType *int   `json:"report_type" form:"report_type" binding:"required"` // 举报类型
	TargetId   *int64 `json:"target_id" form:"target_id" binding:"required"`     // 目标ID
}
