package request

import (
	"github.com/flipped-aurora/gin-vue-admin/server/model/common/request"
)

type CommentSearch struct {
	ID             *int64  `json:"id" form:"id" `                   // 评论ID搜索
	CommentType    *int    `json:"comment_type" form:"comment_type" `
	ParentId       *int    `json:"parent_id" form:"parent_id" `
	ScriptId       *int    `json:"script_id" form:"script_id" `
	DubbingId      *int    `json:"dubbing_id" form:"dubbing_id" `
	UserId         *int    `json:"user_id" form:"user_id" `
	CharacterId    *int    `json:"character_id" form:"character_id" `
	ContentType    *int    `json:"content_type" form:"content_type" `
	AsrStatus      *int    `json:"asr_status" form:"asr_status" `
	Status         *int    `json:"status" form:"status" `
	ReviewStatus   *int    `json:"review_status" form:"review_status" `
	Content        *string `json:"content" form:"content" `        // 评论内容搜索
	ScriptTitle    *string `json:"script_title" form:"script_title" ` // 剧本标题搜索
	StartCreatedAt *int64  `json:"startCreatedAt" form:"startCreatedAt"`
	EndCreatedAt   *int64  `json:"endCreatedAt" form:"endCreatedAt"`
	request.PageInfo
	Sort  string `json:"sort" form:"sort"`
	Order string `json:"order" form:"order"`
}
