package request

import (
	"github.com/flipped-aurora/gin-vue-admin/server/model/common/request"
)

type ScriptSearch struct {
	Id             *int64  `json:"id" form:"id" `
	Title          *string `json:"title" form:"title" `
	AuthorId       *int64  `json:"author_id" form:"author_id" `
	ReviewStatus   *int    `json:"review_status" form:"review_status" `
	PublishStatus  *int    `json:"publish_status" form:"publish_status" `
	Status         *int    `json:"status" form:"status" `
	StartCreatedAt *int64  `json:"startCreatedAt" form:"startCreatedAt"`
	EndCreatedAt   *int64  `json:"endCreatedAt" form:"endCreatedAt"`
	request.PageInfo
	Sort  string `json:"sort" form:"sort"`
	Order string `json:"order" form:"order"`
}

// BatchCreateScriptReq 批量创建剧本请求
type BatchCreateScriptReq struct {
	Scripts []CreateScriptReq `json:"scripts" binding:"required,min=1"`
}

// CreateScriptReq 创建单个剧本请求，参考svcscript.CreateScriptReq
type CreateScriptReq struct {
	Title           string                 `json:"title" binding:"required"`     // 标题
	Cover           string                 `json:"cover" binding:"required"`     // 封面
	AuthorId        int64                  `json:"author_id" binding:"required"` // 作者ID
	CharacterAssets []ScriptCharacterAsset `json:"character_assets"`             // 角色ID和资源包ID列表
	TopicNames      []string               `json:"topic_names"`                  // 话题文本列表
	Lines           []CreateLineReq        `json:"lines"`                        // 台词列表
	BgmUrl          string                 `json:"bgm_url"`                      // 背景音乐地址
	BgmDuration     int32                  `json:"bgm_duration"`                 // 背景音乐时长（毫秒）
	ThemeColor      string                 `json:"theme_color"`                  // 主题色
}

// ScriptCharacterAsset 角色资源包关联信息
type ScriptCharacterAsset struct {
	CharacterId      int64 `json:"character_id"`       // 角色ID
	CharacterAssetId int64 `json:"character_asset_id"` // 角色资源包ID
}

// CreateLineReq 创建台词请求
type CreateLineReq struct {
	Content          string `json:"content" binding:"required"`      // 内容
	Sort             int32  `json:"sort"`                            // 排序值
	CharacterId      int64  `json:"character_id" binding:"required"` // 角色id
	CharacterAssetId int64  `json:"character_asset_id"`              // 角色资源包ID
}

// BatchCreateScriptResp 批量创建剧本响应
type BatchCreateScriptResp struct {
	SuccessCount int      `json:"success_count"` // 成功创建的剧本数量
	FailCount    int      `json:"fail_count"`    // 失败创建的剧本数量
	ScriptIds    []int64  `json:"script_ids"`    // 成功创建的剧本ID列表
	Errors       []string `json:"errors"`        // 失败的错误信息列表
}

// EditScriptDetailReq 编辑剧本详情请求
type EditScriptDetailReq struct {
	Id              int64                  `json:"id" binding:"required"`        // 剧本ID
	Title           string                 `json:"title" binding:"required"`     // 标题
	Cover           string                 `json:"cover" binding:"required"`     // 封面
	AuthorId        int64                  `json:"author_id" binding:"required"` // 作者ID
	CharacterAssets []ScriptCharacterAsset `json:"character_assets"`             // 角色ID和资源包ID列表
	TopicNames      []string               `json:"topic_names"`                  // 话题文本列表
	Lines           []CreateLineReq        `json:"lines"`                        // 台词列表
	BgmUrl          string                 `json:"bgm_url"`                      // 背景音乐地址
	BgmDuration     int32                  `json:"bgm_duration"`                 // 背景音乐时长（毫秒）
	ThemeColor      string                 `json:"theme_color"`                  // 主题色
}

// BatchCreateScriptResp 编辑剧本详情响应
type EditScriptDetailResp struct {
	SuccessCount int      `json:"success_count"` // 成功创建的剧本数量
	FailCount    int      `json:"fail_count"`    // 失败创建的剧本数量
	ScriptIds    []int64  `json:"script_ids"`    // 成功创建的剧本ID列表
	Errors       []string `json:"errors"`        // 失败的错误信息列表
}
