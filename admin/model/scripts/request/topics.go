package request

import (
	"github.com/flipped-aurora/gin-vue-admin/server/model/common/request"
)

type TopicSearch struct {
	Id             *int64  `json:"id" form:"id" `
	Name           *string `json:"name" form:"name" `
	LabelId        *int    `json:"label_id" form:"label_id" `
	IsRecommend    *bool   `json:"is_recommend" form:"is_recommend" `
	IsHot          *bool   `json:"is_hot" form:"is_hot"`
	StartCreatedAt *int64  `json:"startCreatedAt" form:"startCreatedAt"`
	EndCreatedAt   *int64  `json:"endCreatedAt" form:"endCreatedAt"`
	request.PageInfo
	Sort  string `json:"sort" form:"sort"`
	Order string `json:"order" form:"order"`
}
