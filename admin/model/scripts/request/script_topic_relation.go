package request

import (
	"github.com/flipped-aurora/gin-vue-admin/server/model/common/request"
)

type ScriptTopicRelationSearch struct {
	Id             *int64 `json:"id" form:"id" `
	ScriptId       *int64 `json:"script_id" form:"script_id" `
	TopicId        *int64 `json:"topic_id" form:"topic_id" `
	StartCreatedAt *int64 `json:"startCreatedAt" form:"startCreatedAt"`
	EndCreatedAt   *int64 `json:"endCreatedAt" form:"endCreatedAt"`
	request.PageInfo
}
