// 自动生成模板Script
package scripts

// scripts表 结构体  Script
type Script struct {
	Id            *int64  `json:"id" form:"id" gorm:"primarykey;comment:剧本ID;column:id;"size:19;`                                      //剧本ID
	Title         *string `json:"title" form:"title" gorm:"comment:标题;column:title;"size:255;`                                         //标题
	Cover         *string `json:"cover" form:"cover" gorm:"comment:封面地址;column:cover;"size:255;`                                       //封面地址
	AuthorId       *int64 `json:"author_id" form:"author_id" gorm:"comment:作者用户ID;column:author_id;"size:19;`                          //作者用户ID
	CreationUserId *uint  `json:"creation_user_id" form:"creation_user_id" gorm:"comment:创建该剧本的admin用户ID;column:creation_user_id;"size:10;` //创建该剧本的admin用户ID，用于数据权限控制
	BgmUrl         *string `json:"bgm_url" form:"bgm_url" gorm:"comment:背景音乐地址;column:bgm_url;"size:255;`                               //背景音乐地址
	BgmDuration   *int    `json:"bgm_duration" form:"bgm_duration" gorm:"comment:背景音乐时长;column:bgm_duration;"size:10;`                 //背景音乐时长
	ThemeColor    *string `json:"theme_color" form:"theme_color" gorm:"comment:主题色;column:theme_color;"size:20;`                       //主题色
	ReviewStatus  *int    `json:"review_status" form:"review_status" gorm:"comment:审核状态;column:review_status;"size:10;`                //审核状态
	Score         *int    `json:"score" form:"score" gorm:"comment:剧本质量分数;column:score;"size:10;`                                      //剧本质量分数
	Sort          *int    `json:"sort" form:"sort" gorm:"comment:排序值;column:sort;"size:10;`                                            //排序值
	DubbingCount  *int    `json:"dubbing_count" form:"dubbing_count" gorm:"comment:配音数;column:dubbing_count;"size:10;`                 //配音数
	CommentCount  *int    `json:"comment_count" form:"comment_count" gorm:"comment:评论数;column:comment_count;"size:10;`                 //评论数
	LikeCount     *int    `json:"like_count" form:"like_count" gorm:"comment:点赞数;column:like_count;"size:10;`                          //点赞数
	FollowCount   *int    `json:"follow_count" form:"follow_count" gorm:"comment:关注数;column:follow_count;"size:10;`                    //关注数
	ViewCount     *int    `json:"view_count" form:"view_count" gorm:"comment:浏览数;column:view_count;"size:10;`                          //浏览数
	ShareCount    *int    `json:"share_count" form:"share_count" gorm:"comment:分享数;column:share_count;"size:10;`                       //分享数
	PublishStatus *int    `json:"publish_status" form:"publish_status" gorm:"comment:发布状态（1:草稿、2:已发布）;column:publish_status;"size:10;` //发布状态（1:草稿、2:已发布）
	Status        *int    `json:"status" form:"status" gorm:"comment:剧本状态（1:正常、2:删除）;column:status;"size:10;`                          //剧本状态（1:正常、2:删除）
	CreatedAt     int64   `json:"created_at" form:"created_at" gorm:"comment:创建时间;column:created_at;"size:19;`                         //创建时间
	UpdatedAt     int64   `json:"updated_at" form:"updated_at" gorm:"comment:更新时间;column:updated_at;"size:19;`                         //更新时间
}

// TableName scripts表 Script自定义表名 scripts
func (Script) TableName() string {
	return "vc_script.`scripts`"
}
