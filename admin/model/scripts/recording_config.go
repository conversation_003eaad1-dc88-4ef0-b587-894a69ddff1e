package scripts

// GetRecordingConfigReq 获取录音配置请求
type GetRecordingConfigReq struct {
	CharacterID      int64  `json:"character_id" binding:"required"`      // 角色ID
	CharacterAssetID int64  `json:"character_asset_id" binding:"required"` // 角色资源包ID
	Scene            string `json:"scene" binding:"required"`             // 场景：comment, dubbing, signature
}

// GetRecordingConfigResp 获取录音配置响应
type GetRecordingConfigResp struct {
	SampleRate          int64 `json:"sample_rate"`           // 录音采样率
	Channels            int32 `json:"channels"`              // 录音声道数
	BitDepth            int32 `json:"bit_depth"`             // 录音位深
	OutputSampleRate    int64 `json:"output_sample_rate"`    // 输出采样率
	OutputBitDepth      int32 `json:"output_bit_depth"`      // 输出位深
	OutputChannels      int32 `json:"output_channels"`       // 输出声道数
	AudioMilliDuration  int32 `json:"audio_milli_duration"`  // 录音时长限制（毫秒）
	ChunkMs             int32 `json:"chunk_ms"`              // WebSocket上传块时长
	UseRvc              bool  `json:"use_rvc"`               // 是否使用RVC变声
	SeedName            string `json:"seed_name"`             // 种子名称
	RvcName             string `json:"rvc_name"`              // RVC模型名称
	ReferenceAudioUrl   string `json:"reference_audio_url"`   // 参考音频URL
}

// RecordingConfigData 录音配置数据（用于前端）
type RecordingConfigData struct {
	// Web Audio API 相关参数
	SampleRate   int64 `json:"sampleRate"`   // 录音采样率
	ChannelCount int32 `json:"channelCount"` // 录音声道数
	
	// 录音控制参数
	MaxDuration int32 `json:"maxDuration"` // 最大录音时长（秒）
	ChunkSize   int32 `json:"chunkSize"`   // 音频块大小（毫秒）
	
	// 变声相关参数
	UseRvc            bool   `json:"useRvc"`            // 是否支持变声
	SeedName          string `json:"seedName"`          // 种子名称
	RvcName           string `json:"rvcName"`           // RVC模型名称
	ReferenceAudioUrl string `json:"referenceAudioUrl"` // 参考音频URL
	
	// 输出参数
	OutputSampleRate int64 `json:"outputSampleRate"` // 输出采样率
	OutputChannels   int32 `json:"outputChannels"`   // 输出声道数
	OutputBitDepth   int32 `json:"outputBitDepth"`   // 输出位深
}

// ToRecordingConfigData 转换为前端使用的录音配置数据
func (r *GetRecordingConfigResp) ToRecordingConfigData() *RecordingConfigData {
	return &RecordingConfigData{
		SampleRate:        r.SampleRate,
		ChannelCount:      r.Channels,
		MaxDuration:       r.AudioMilliDuration / 1000, // 转换为秒
		ChunkSize:         r.ChunkMs,
		UseRvc:            r.UseRvc,
		SeedName:          r.SeedName,
		RvcName:           r.RvcName,
		ReferenceAudioUrl: r.ReferenceAudioUrl,
		OutputSampleRate:  r.OutputSampleRate,
		OutputChannels:    r.OutputChannels,
		OutputBitDepth:    r.OutputBitDepth,
	}
}
