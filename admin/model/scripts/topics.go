// 自动生成模板Topic
package scripts

// topics表 结构体  Topic
type Topic struct {
	Id            *int64  `json:"id" form:"id" gorm:"primarykey;comment:话题ID;column:id;"size:19;`                          //话题ID
	Name          *string `json:"name" form:"name" gorm:"comment:话题名称;column:name;"size:50;`                               //话题名称
	LabelId       *int    `json:"label_id" form:"label_id" gorm:"comment:标签id;column:label_id;"size:19;`                   //标签id
	Sort          *int    `json:"sort" form:"sort" gorm:"comment:排序值;column:sort;"size:10;`                                //排序值
	IsRecommend   *bool   `json:"is_recommend" form:"is_recommend" gorm:"comment:是否首页推荐;column:is_recommend;"`             //是否首页推荐
	RecommendSort *int    `json:"recommend_sort" form:"recommend_sort" gorm:"comment:推荐排序;column:recommend_sort;"size:10;` //推荐排序
	IsHot         *bool   `json:"is_hot" form:"is_hot" gorm:"comment:是否热门;column:is_hot;"`                                 //是否热门
	HotSort       *int    `json:"hot_sort" form:"hot_sort" gorm:"comment:热门排序;column:hot_sort;"size:10;`                   //热门排序
	ScriptCount   *int    `json:"script_count" form:"script_count" gorm:"comment:话题数;column:script_count;"size:10;`        //话题数
	DubbingCount  *int    `json:"dubbing_count" form:"dubbing_count" gorm:"comment:配音数;column:dubbing_count;"size:10;`     //配音数
	ViewCount     *int    `json:"view_count" form:"view_count" gorm:"comment:浏览数;column:view_count;"size:10;`              //浏览数
	CreatedAt     *int64  `json:"created_at" form:"created_at" gorm:"comment:创建时间;column:created_at;"size:19;`             //创建时间
	UpdatedAt     *int64  `json:"updated_at" form:"updated_at" gorm:"comment:更新时间;column:updated_at;"size:19;`             //更新时间
	Status        *int    `json:"status" form:"status" gorm:"comment:状态（1:正常、2:删除）;column:status;"size:10;`                //状态（1:正常、2:删除）
}

// TableName topics表 Topic自定义表名 topics
func (Topic) TableName() string {
	return "vc_script.`topics`"
}
