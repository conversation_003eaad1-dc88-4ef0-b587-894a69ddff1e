// 自动生成模板User
package users

// users表 结构体  User
type UserScore struct {
	UserId          int64 `json:"user_id" form:"user_id" gorm:"primarykey;comment:用户ID;column:user_id;"size:20;`                   //用户ID
	FreeBalance     int64 `json:"free_balance" form:"free_balance" gorm:"comment:免费比余额;column:free_balance;"size:20;`              //免费币余额
	ValuableBalance int64 `json:"valuable_balance" form:"valuable_balance" gorm:"comment:有价值币余额;column:valuable_balance;"size:20;` //有价值币余额
	TotalFree       int64 `json:"total_free" form:"total_free" gorm:"comment:免费总积分;column:total_free;"size:20;`                    //免费总积分
	TotalValuable   int64 `json:"total_valuable" form:"total_valuable" gorm:"comment:总积分;column:total_valuable;"size:20;`          //头像URL
	CreatedAt       int64 `json:"created_at" form:"created_at" gorm:"comment:创建时间;column:created_at;"size:20;`                     //创建时间
	UpdatedAt       int64 `json:"updated_at" form:"updated_at" gorm:"comment:更新时间;column:updated_at;"size:20;`                     //更新时间
}

// TableName users表 User自定义表名 users
func (UserScore) TableName() string {
	return "`vc_user`.`user_score`"
}
