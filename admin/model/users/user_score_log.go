// 自动生成模板UserScoreLog
package users

import (
	"fmt"

	"new-gitlab.xunlei.cn/vcproject/backends/baselib/server/env"
)

// userScoreLog表 结构体  UserScoreLog
type UserScoreLog struct {
	Id        *int64  `json:"id" form:"id" gorm:"primarykey;comment:主键;column:id;"size:19;`                //主键
	UserId    *int64  `json:"user_id" form:"user_id" gorm:"comment:用户ID;column:user_id;"size:20;`          //用户ID
	BizType   *int    `json:"biz_type" form:"biz_type" gorm:"comment:业务类型;column:biz_type;"size:20;`       //业务类型
	BizName   *string `json:"biz_name" form:"biz_name" gorm:"comment:业务名;column:biz_name;"size:32;`        //业务名
	Score     *int64  `json:"score" form:"score" gorm:"comment:积分;column:score;"size:19;`                  //积分
	FreeScore *int64  `json:"free_score" form:"free_score" gorm:"comment:免费积分;column:free_score;"size:19;` //免费积分
	OrderId   *string `json:"order_id" form:"order_id" gorm:"comment:外部订单ID;column:order_id;"size:64;`     //外部订单ID
	Remark    *string `json:"remark" form:"remark" gorm:"comment:备注;column:remark;"size:255;`              //备注
	CreatedAt *int64  `json:"created_at" form:"created_at" gorm:"comment:创建时间;column:created_at;"size:19;` //创建时间
	UpdatedAt *int64  `json:"updated_at" form:"updated_at" gorm:"comment:更新时间;column:updated_at;"size:19;` //更新时间
}

// TableName userScoreLog表 UserScoreLog自定义表名 user_score_log
func (UserScoreLog) TableName(userId int64) string {
	if env.IsProd() {
		return fmt.Sprintf("`vc_user`.`user_score_log_%d`", userId%10+1)
	}
	return "`vc_user`.`user_score_log`"
}
