package users

import (
	"context"
	"encoding/json"
	"errors"
	"github.com/flipped-aurora/gin-vue-admin/server/global"
	"gorm.io/gorm"
	"new-gitlab.xunlei.cn/vcproject/backends/baselib/errcode"
	"new-gitlab.xunlei.cn/vcproject/backends/baselib/util"
	"new-gitlab.xunlei.cn/vcproject/backends/proto/api/svcscript"
)

// UserAuditType 用户审核类型
type UserAuditType int32

const (
	UserAuditTypeUnknown    UserAuditType = 0 // 未知
	UserAuditTypeAvatar     UserAuditType = 1 // 头像
	UserAuditTypeBackground UserAuditType = 2 // 背景图
	UserAuditTypeNickname   UserAuditType = 3 // 昵称
	UserAuditTypeVoice      UserAuditType = 4 // 语音签名
)

// GetUserAuditTypeName 获取审核类型名称
func (t UserAuditType) GetName() string {
	switch t {
	case UserAuditTypeAvatar:
		return "头像"
	case UserAuditTypeBackground:
		return "背景图"
	case UserAuditTypeNickname:
		return "昵称"
	case UserAuditTypeVoice:
		return "语音签名"
	default:
		return "未知"
	}
}

// UserAuditContent 用户审核内容结构
type UserAuditContent struct {
	// 头像审核内容
	Avatar string `json:"avatar,omitempty"`

	// 背景图审核内容
	BackgroundUrl string `json:"background_url,omitempty"`

	// 昵称审核内容
	Nickname string `json:"nickname,omitempty"`

	// 语音签名审核内容
	VoiceSignatureUrl string `json:"voice_signature_url,omitempty"`
	VoiceDuration     int32  `json:"voice_duration,omitempty"`

	// 审核相关信息
	ReviewUrl string `json:"review_url,omitempty"` // 审核时使用的完整URL
	BtId      string `json:"bt_id,omitempty"`      // 数美审核ID（语音签名使用）
}

// UserAuditRecord 用户审核记录表
type UserAuditRecord struct {
	ID          int64                  `gorm:"primaryKey;autoIncrement;column:id" json:"id"`      // 自增ID
	UserId      int64                  `gorm:"column:user_id;index" json:"user_id"`               // 用户ID
	AuditType   UserAuditType          `gorm:"column:audit_type" json:"audit_type"`               // 审核类型：1-头像，2-背景图，3-昵称，4-语音签名
	AuditStatus svcscript.ReviewStatus `gorm:"column:audit_status;default:1" json:"audit_status"` // 审核状态：1-待审核，2-机审通过，3-机审拒绝，4-人审通过，5-人审拒绝
	Content     string                 `gorm:"column:content;type:text" json:"content"`           // 审核内容JSON
	CreatedAt   int64                  `gorm:"column:created_at" json:"created_at"`               // 创建时间
	UpdatedAt   int64                  `gorm:"column:updated_at" json:"updated_at"`               // 更新时间
	DeletedAt   int64                  `gorm:"column:deleted_at;default:0;index" json:"deleted_at"` // 软删除时间戳，0表示未删除
}

func (*UserAuditRecord) TableName() string {
	return "`vc_user`.`user_audit_records`"
}

// GetContentStruct 获取审核内容结构体
func (u *UserAuditRecord) GetContentStruct() (*UserAuditContent, error) {
	if u.Content == "" {
		return &UserAuditContent{}, nil
	}

	var content UserAuditContent
	err := json.Unmarshal([]byte(u.Content), &content)
	if err != nil {
		return nil, err
	}
	return &content, nil
}

// SetContentStruct 设置审核内容结构体
func (u *UserAuditRecord) SetContentStruct(content *UserAuditContent) error {
	if content == nil {
		u.Content = ""
		return nil
	}

	contentBytes, err := json.Marshal(content)
	if err != nil {
		return err
	}
	u.Content = string(contentBytes)
	return nil
}

// GetAuditTypeName 获取审核类型名称
func (u *UserAuditRecord) GetAuditTypeName() string {
	return u.AuditType.GetName()
}

// GetAuditStatusName 获取审核状态名称
func (u *UserAuditRecord) GetAuditStatusName() string {
	switch u.AuditStatus {
	case svcscript.ReviewStatus_REVIEW_STATUS_PENDING:
		return "待审核"
	case svcscript.ReviewStatus_REVIEW_STATUS_AUTO_APPROVED:
		return "机审通过"
	case svcscript.ReviewStatus_REVIEW_STATUS_AUTO_REJECTED:
		return "机审拒绝"
	case svcscript.ReviewStatus_REVIEW_STATUS_MANUAL_APPROVED:
		return "人审通过"
	case svcscript.ReviewStatus_REVIEW_STATUS_MANUAL_REJECTED:
		return "人审拒绝"
	default:
		return "未知"
	}
}

// UserAuditRecordService 用户审核记录服务
type UserAuditRecordService struct{}

// CreateUserAuditRecord 创建用户审核记录
func (s *UserAuditRecordService) CreateUserAuditRecord(ctx context.Context, record *UserAuditRecord) error {
	record.CreatedAt = util.NowTimeMillis()
	record.UpdatedAt = util.NowTimeMillis()
	err := global.GVA_DB.WithContext(ctx).Create(record).Error
	if err != nil {
		return errcode.ErrUserAuditRecordCreateFailed
	}
	return nil
}

// GetUserAuditRecordByID 根据ID获取用户审核记录
func (s *UserAuditRecordService) GetUserAuditRecordByID(ctx context.Context, id int64) (*UserAuditRecord, error) {
	var record UserAuditRecord
	err := global.GVA_DB.WithContext(ctx).Where("id = ? AND deleted_at = 0", id).First(&record).Error
	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, errcode.ErrUserAuditRecordNotFound
		}
		return nil, errcode.ErrUserAuditRecordQueryFailed
	}
	return &record, nil
}

// UpdateUserAuditRecord 更新用户审核记录
func (s *UserAuditRecordService) UpdateUserAuditRecord(ctx context.Context, id int64, updates map[string]any) error {
	updates["updated_at"] = util.NowTimeMillis()
	err := global.GVA_DB.WithContext(ctx).Model(&UserAuditRecord{}).Where("id = ? AND deleted_at = 0", id).Updates(updates).Error
	if err != nil {
		return errcode.ErrUserAuditRecordUpdateFailed
	}
	return nil
}

// DeleteUserAuditRecord 软删除用户审核记录
func (s *UserAuditRecordService) DeleteUserAuditRecord(ctx context.Context, id int64) error {
	updates := map[string]any{
		"deleted_at": util.NowTimeMillis(),
		"updated_at": util.NowTimeMillis(),
	}
	err := global.GVA_DB.WithContext(ctx).Model(&UserAuditRecord{}).Where("id = ? AND deleted_at = 0", id).Updates(updates).Error
	if err != nil {
		return errcode.ErrUserAuditRecordUpdateFailed
	}
	return nil
}

// GetUserAuditRecordsByUserId 根据用户ID获取审核记录列表
func (s *UserAuditRecordService) GetUserAuditRecordsByUserId(ctx context.Context, userId int64, limit, offset int) ([]*UserAuditRecord, int64, error) {
	var records []*UserAuditRecord
	var total int64

	query := global.GVA_DB.WithContext(ctx).Model(&UserAuditRecord{}).Where("user_id = ? AND deleted_at = 0", userId)

	err := query.Count(&total).Error
	if err != nil {
		return nil, 0, errcode.ErrUserAuditRecordQueryFailed
	}

	err = query.Order("created_at DESC").Limit(limit).Offset(offset).Find(&records).Error
	if err != nil {
		return nil, 0, errcode.ErrUserAuditRecordQueryFailed
	}

	return records, total, nil
}

// GetUserAuditRecordsByStatus 根据审核状态获取审核记录列表
func (s *UserAuditRecordService) GetUserAuditRecordsByStatus(ctx context.Context, status svcscript.ReviewStatus, limit, offset int) ([]*UserAuditRecord, int64, error) {
	var records []*UserAuditRecord
	var total int64

	query := global.GVA_DB.WithContext(ctx).Model(&UserAuditRecord{}).Where("audit_status = ? AND deleted_at = 0", status)

	err := query.Count(&total).Error
	if err != nil {
		return nil, 0, errcode.ErrUserAuditRecordQueryFailed
	}

	err = query.Order("created_at DESC").Limit(limit).Offset(offset).Find(&records).Error
	if err != nil {
		return nil, 0, errcode.ErrUserAuditRecordQueryFailed
	}

	return records, total, nil
}

// GetUserAuditRecordsByTypeAndStatus 根据审核类型和状态获取审核记录列表
func (s *UserAuditRecordService) GetUserAuditRecordsByTypeAndStatus(ctx context.Context, auditType UserAuditType, status svcscript.ReviewStatus, limit, offset int) ([]*UserAuditRecord, int64, error) {
	var records []*UserAuditRecord
	var total int64

	query := global.GVA_DB.WithContext(ctx).Model(&UserAuditRecord{}).Where("audit_type = ? AND audit_status = ? AND deleted_at = 0", auditType, status)

	err := query.Count(&total).Error
	if err != nil {
		return nil, 0, errcode.ErrUserAuditRecordQueryFailed
	}

	err = query.Order("created_at DESC").Limit(limit).Offset(offset).Find(&records).Error
	if err != nil {
		return nil, 0, errcode.ErrUserAuditRecordQueryFailed
	}

	return records, total, nil
}

// GetUserAuditRecordsList 获取用户审核记录列表（支持多条件筛选）
func (s *UserAuditRecordService) GetUserAuditRecordsList(ctx context.Context, userId int64, auditType UserAuditType, status svcscript.ReviewStatus, startTime, endTime int64, limit, offset int) ([]*UserAuditRecord, int64, error) {
	var records []*UserAuditRecord
	var total int64

	query := global.GVA_DB.WithContext(ctx).Model(&UserAuditRecord{}).Where("deleted_at = 0")

	if userId > 0 {
		query = query.Where("user_id = ?", userId)
	}
	if auditType > 0 {
		query = query.Where("audit_type = ?", auditType)
	}
	if status > 0 {
		query = query.Where("audit_status = ?", status)
	}
	if startTime > 0 {
		query = query.Where("created_at >= ?", startTime)
	}
	if endTime > 0 {
		query = query.Where("created_at <= ?", endTime)
	}

	err := query.Count(&total).Error
	if err != nil {
		return nil, 0, errcode.ErrUserAuditRecordQueryFailed
	}

	err = query.Order("created_at DESC").Limit(limit).Offset(offset).Find(&records).Error
	if err != nil {
		return nil, 0, errcode.ErrUserAuditRecordQueryFailed
	}

	return records, total, nil
}

// DeletePendingAuditRecords 软删除用户指定类型的待审核记录
func (s *UserAuditRecordService) DeletePendingAuditRecords(ctx context.Context, userId int64, auditType UserAuditType) error {
	updates := map[string]any{
		"deleted_at": util.NowTimeMillis(),
		"updated_at": util.NowTimeMillis(),
	}
	err := global.GVA_DB.WithContext(ctx).Model(&UserAuditRecord{}).Where("user_id = ? AND audit_type = ? AND audit_status = ? AND deleted_at = 0",
		userId, auditType, svcscript.ReviewStatus_REVIEW_STATUS_PENDING).Updates(updates).Error
	if err != nil {
		return errcode.ErrUserAuditRecordUpdateFailed
	}
	return nil
}

// CreateUserAuditRecordWithReplace 创建用户审核记录（先软删除待审核记录再创建新记录）
func (s *UserAuditRecordService) CreateUserAuditRecordWithReplace(ctx context.Context, record *UserAuditRecord) error {
	return global.GVA_DB.WithContext(ctx).Transaction(func(tx *gorm.DB) error {
		// 1. 先软删除该用户该类型的待审核记录
		updates := map[string]any{
			"deleted_at": util.NowTimeMillis(),
			"updated_at": util.NowTimeMillis(),
		}
		err := tx.Model(&UserAuditRecord{}).Where("user_id = ? AND audit_type = ? AND audit_status = ? AND deleted_at = 0",
			record.UserId, record.AuditType, svcscript.ReviewStatus_REVIEW_STATUS_PENDING).Updates(updates).Error
		if err != nil {
			return errcode.ErrUserAuditRecordUpdateFailed
		}

		// 2. 创建新的审核记录
		record.CreatedAt = util.NowTimeMillis()
		record.UpdatedAt = util.NowTimeMillis()
		record.DeletedAt = 0 // 确保新记录未被删除
		err = tx.Create(record).Error
		if err != nil {
			return errcode.ErrUserAuditRecordCreateFailed
		}

		return nil
	})
}
