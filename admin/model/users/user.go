// 自动生成模板User
package users

// users表 结构体  User
type User struct {
	UserId                  *int    `json:"user_id" form:"user_id" gorm:"primarykey;comment:用户ID;column:user_id;"size:20;`                                                 //用户ID
	Nickname                *string `json:"nickname" form:"nickname" gorm:"comment:用户昵称;column:nickname;"size:64;`                                                         //用户昵称
	NicknamePinyin          *string `json:"nickname_pinyin" form:"nickname_pinyin" gorm:"comment:名称拼音，用于排序;column:nickname_pinyin;"size:255;`                              //名称拼音，用于排序
	Phone                   *string `json:"phone" form:"phone" gorm:"comment:手机号;column:phone;"size:20;`                                                                   //手机号
	Avatar                  *string `json:"avatar" form:"avatar" gorm:"comment:头像URL;column:avatar;"size:255;`                                                             //头像URL
	Gender                  *int    `json:"gender" form:"gender" gorm:"comment:性别 0:未知 1:男 2:女;column:gender;"`                                                            //性别 0:未知 1:男 2:女
	Status                  *int    `json:"status" form:"status" gorm:"comment:状态 1:正常 2:禁用;column:status;"`                                                               //状态 1:正常 2:禁用
	BackgroundUrl           *string `json:"background_url" form:"background_url" gorm:"comment:背景图地址;column:background_url;"size:255;`                                     //背景图地址
	VoiceSignatureUrl       *string `json:"voice_signature_url" form:"voice_signature_url" gorm:"comment:语音签名地址;column:voice_signature_url;"size:255;`                     //语音签名地址
	VoiceDuration           *int    `json:"voice_duration" form:"voice_duration" gorm:"comment:语音时长;column:voice_duration;"size:10;`                                       //语音时长
	IsPremiumCreator        *bool   `json:"is_premium_creator" form:"is_premium_creator" gorm:"comment:是否优质创作者 0:否 1:是;column:is_premium_creator;"`                        //是否优质创作者 0:否 1:是
	CreationUserId          *uint   `json:"creation_user_id" form:"creation_user_id" gorm:"comment:关联的创作者admin用户ID;column:creation_user_id;default:0;index;"size:10;`         //关联的创作者admin用户ID
	DeletedAt               *int64  `json:"deleted_at" form:"deleted_at" gorm:"comment:软删除时间戳，0表示未删除;column:deleted_at;default:0;index;"size:19;`                           //软删除时间戳
	CreatedAt               *int64  `json:"created_at" form:"created_at" gorm:"comment:创建时间;column:created_at;"size:19;`                                                   //创建时间
	UpdatedAt               *int64  `json:"updated_at" form:"updated_at" gorm:"comment:更新时间;column:updated_at;"size:19;`                                                   //更新时间
}

// TableName users表 User自定义表名 users
func (User) TableName() string {
	return "`vc_user`.`users`"
}
