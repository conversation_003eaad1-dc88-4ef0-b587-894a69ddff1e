package request

import (
	"github.com/flipped-aurora/gin-vue-admin/server/model/common/request"
)

type UserSearch struct {
	request.PageInfo
	UserId   int64  `json:"user_id" form:"user_id"`
	NickName string `json:"nickname" form:"nickname"`
	Phone    string `json:"phone" form:"phone"`
	Sort     string `json:"sort" form:"sort"`         // 排序字段
	Order    string `json:"order" form:"order"`       // 排序方向 asc/desc
}

type UserScoreSearch struct {
	UserId int64 `json:"user_id" form:"user_id"`
}

// CreateUserReq 创建用户请求
type CreateUserReq struct {
	Phone               string `json:"phone" form:"phone" binding:"required"`                                 // 手机号
	Nickname            string `json:"nickname" form:"nickname"`                                              // 昵称
	Avatar              string `json:"avatar" form:"avatar"`                                                  // 头像URL
	Gender              int32  `json:"gender" form:"gender"`                                                  // 性别 0:未知 1:男 2:女
	Status              int32  `json:"status" form:"status"`                                                  // 状态 1:正常 3:禁用
	BackgroundUrl       string `json:"background_url" form:"background_url"`                                  // 背景图
	VoiceSignatureUrl   string `json:"voice_signature_url" form:"voice_signature_url"`                       // 语音签名
	VoiceDuration       int32  `json:"voice_duration" form:"voice_duration"`                                 // 语音时长
	IsPremiumCreator    bool   `json:"is_premium_creator" form:"is_premium_creator"`                          // 是否优质创作者
	CreationUserId      *uint  `json:"creation_user_id" form:"creation_user_id"`                              // 关联的创作者admin用户ID
}
