package request

import (
	"github.com/flipped-aurora/gin-vue-admin/server/model/common/request"
)

type UserScoreLogSearch struct {
	request.PageInfo
	UserId int64 `json:"user_id" form:"user_id"`
}

type RewardUserScoreReq struct {
	UserId     int64  `json:"user_id" form:"user_id"`
	RewardType int64  `json:"reward_type" form:"reward_type"`
	Coins      int64  `json:"coins" form:"coins"`
	RewardName string `json:"reward_name" form:"reward_name"`
}
