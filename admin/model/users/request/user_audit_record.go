package request

import (
	"github.com/flipped-aurora/gin-vue-admin/server/model/common/request"
	"github.com/flipped-aurora/gin-vue-admin/server/model/users"
	"new-gitlab.xunlei.cn/vcproject/backends/proto/api/svcscript"
)

// UserAuditRecordSearch 用户审核记录搜索结构体
type UserAuditRecordSearch struct {
	request.PageInfo
	UserId      int64                     `json:"user_id" form:"user_id"`           // 用户ID
	AuditType   users.UserAuditType       `json:"audit_type" form:"audit_type"`     // 审核类型
	AuditStatus svcscript.ReviewStatus    `json:"audit_status" form:"audit_status"` // 审核状态
	StartTime   int64                     `json:"start_time" form:"start_time"`     // 开始时间（毫秒时间戳）
	EndTime     int64                     `json:"end_time" form:"end_time"`         // 结束时间（毫秒时间戳）
}

// UserAuditRecordById 通过ID获取用户审核记录
type UserAuditRecordById struct {
	ID int64 `json:"id" form:"id" binding:"required"` // 审核记录ID
}

// UserAuditRecordApprove 用户审核通过请求
type UserAuditRecordApprove struct {
	ID int64 `json:"id" form:"id" binding:"required"` // 审核记录ID
}

// UserAuditRecordReject 用户审核拒绝请求
type UserAuditRecordReject struct {
	ID     int64  `json:"id" form:"id" binding:"required"`         // 审核记录ID
	Reason string `json:"reason" form:"reason" binding:"required"` // 拒绝原因
}

// UserAuditRecordBatchApprove 批量审核通过请求
type UserAuditRecordBatchApprove struct {
	IDs []int64 `json:"ids" form:"ids" binding:"required"` // 审核记录ID列表
}

// UserAuditRecordBatchReject 批量审核拒绝请求
type UserAuditRecordBatchReject struct {
	IDs    []int64 `json:"ids" form:"ids" binding:"required"`       // 审核记录ID列表
	Reason string  `json:"reason" form:"reason" binding:"required"` // 拒绝原因
}
